/****************************************************************************************************
 *
 * FILE NAME:  PnPrmCal.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.09.20
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _PN_PRM_CAL_H_
#define	_PN_PRM_CAL_H_

#include "Global.h"

PRM_RSLT pncal_RatFreq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_RatPow(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_RatVolt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_RatCur(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_RatTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_RatSpd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_MotKt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AbsEncOffset(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_ResDir(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_StpMode(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_Fault1StpMode(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_brkoffdelay(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_brkwait(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_brkspd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_brkdelay(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_LedWarn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_ExtRegenW(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_ExtRegenR(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_PowInputSel(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_UWVSeq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AcoffDischargeSw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_EncZeroOffset(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_posgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdloophz(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdlooptime(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_jrate(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdloophz2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdlooptime2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);;
PRM_RSLT pncal_posgn2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_curloophz(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdmodsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdmstrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdmsspd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdmsacc(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdmsperr(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_trqffcfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_trqfffil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_trqffgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdffcfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdfffil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdffgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_SvoOnSpdLim(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_TconSpdLim(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_spdMafilCfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_TrqFilTi(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_TrqFil2Freq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_TrqFil2Q(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_fwtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_rvtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_outfwtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_outrvtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_emgtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_srfil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_notchcfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_notchfilter1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_notchfilter2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_notchfilter3(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_notchfilter4(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_sfbfil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_curloopti(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_PosRefMaFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_SpdffMaFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_TrqffMaFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_PosRefHFRad(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_PosRefExpFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_pjogsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_pjogdist(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_pjogspd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_pjogacctm(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_pjogwaittm(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_pjognum(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_jogspd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_jogacct(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_jogdect(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_gnchgt1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_gnchgt2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_gnchgwait1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_gnchgwait2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_gnchgsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_TrqFilTi2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_GravTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_PosClombTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_NegClombTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_ViscTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_FricSpd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_PosDisturComp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_FunctionSw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_PulseSpdMode(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_AvibOpt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AvibFrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AvibGn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AvibDamp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AvibFil1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AvibFil2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AvibDamp2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AvibFrq2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_dobgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_dobgn2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_dtrqgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_dlpfil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_dobjgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_evobgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_evobjgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_advappsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_evib_opt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_evib_frq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_evib_fil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_tls_cfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcgncomp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcfwbias(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcrvbias(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcvibfreqA(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcvibfreqB(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcvff(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcgn2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_mfcgncomp2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_wfbkp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_wfbti(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_wfidset(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_wfswitch(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_vibSupfreq2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_vibSupfreq2Cmp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT FnClampingJawForLit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT FnClampingJawRevLit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_FuncSlt_DI1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_LogicSlt_DI1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_FuncSlt_DI2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_LogicSlt_DI2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_FuncSlt_DI3(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_LogicSlt_DI3(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_FuncSlt_DI4(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_LogicSlt_DI4(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_FuncSlt_DO1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_LogicSlt_DO1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_ovlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_nonregenovlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_uvlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_uvfil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_uvwrnlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_ovrspdlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_ovrperwrnlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_ovrperalmlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_svonovrperwrnlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_svonovrperalmlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_ovrruntrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AlmMask(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_VibDetSens(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_VibChkSpd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_JstVibChkSpd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_VibDetSw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_remdetw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_coinlvl(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_nearlvl(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_AlmCacheChConfig(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_AlmCacheTConfig(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_OverCur(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pnexe_clearalarm(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_SysPrmInit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_resetlog(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_MotIdent(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_fungenstrt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_FnSvoOff(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_PrgJogCmd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_JogCmd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_mpistrt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_miistrt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_miiolstrt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_TimeStamp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_enccmd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_follerrwin(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_follerrtime(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pnexe_enctype(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_singleturn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_abzenc(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_useenc2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pnexe_encconfig(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_encconfig2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pnexe_DevID(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_devidconfig(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pnexe_ClampingJawInit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
// add 2023-02-02 
PRM_RSLT pncal_phfind(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_MotIdenCur(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_gearatio(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_PosRefFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);


PRM_RSLT pncal_PosStiff(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_SpdStiff(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_CalHumanPosKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_CalHumanVelKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_CalHumanTrqKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_CalHumanTimeout(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_CalHumanCurKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_CalHumanVolKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_CalSftPosLimit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_HjVelLimit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_HjTrqLimit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_HjLimitKp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_HjLimitKv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);
PRM_RSLT pncal_HjIpdGainCal(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

PRM_RSLT pncal_EncAbnormalValue(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue);

#endif  // _PN_PRM_CAL_H_


