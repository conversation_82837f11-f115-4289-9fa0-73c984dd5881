/****************************************************************************************************
 *
 * FILE NAME:  TamagawaIF.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.09.08
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	08-09-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _TAMAGAWA_IF_H_
#define _TAMAGAWA_IF_H_

#include "BaseDef.h"

#define TMGW_DATA_ID_0 0x02
#define TMGW_DATA_ID_1 0x8A
#define TMGW_DATA_ID_2 0x92
#define TMGW_DATA_ID_3 0x1A          // Read ABS & ABM
#define TMGW_DATA_ID_6 0x32          // write eeprom
#define TMGW_DATA_ID_7 0xBA          // Clear erroro
#define TMGW_DATA_ID_8 0xC2
#define TMGW_DATA_ID_C 0x62          // Clear error and multi-turn
#define TMGW_DATA_ID_D 0xEA          // read eeprom




PUBLIC void TmgwSetCF(UINT16 AxisID, UINT16 Lenght, UINT8* TmgwCF);

PUBLIC UINT8 TmgwWriteEep(UINT16 AxisID,UINT8* Data, UINT8 Addr);
PUBLIC UINT8 TmgwReadEep(UINT16 AxisID,UINT8 *Data, UINT8 Addr);

#endif  //#ifndef _TAMAGAWA_IF_H_

