/*******************************************************************************
								Copyright 2022 IAR Systems. All rights reserved.
Function:RZ/N2L(Cortex-R52) Processor core  protect settings function

Note:

History:
 No  Rev  Date       Name			Note
---+-----+----------+-------------+--------------------------------------------
000 01.00 2022/07/29 S.Tonoshita	New Development
*******************************************************************************/
#ifndef __PROTECT_H__
#define __PROTECT_H__

//
// Register Write Protection Function
//
#define PRCRS_PRKEY			(0x0000A500U)				// PRC key code for safety Area Protect Register
#define PRCRS_PRC0			(0x00000001U)				// Protect 0
#define PRCRS_PRC1			(0x00000002U)				// Protect 1
#define PRCRS_PRC2			(0x00000004U)				// Protect 2
#define PRCRS_PRC3			(0x00000008U)				// Protect 3

#define PRCRN_PRKEY			(0x0000A500U)				// PRC key code for non safety Area Protect Register
#define PRCRN_PRC0			(0x00000001U)				// Protect 0
#define PRCRN_PRC1			(0x00000002U)				// Protect 1
#define PRCRN_PRC2			(0x00000004U)				// Protect 2
#define PRCRN_PRC3			(0x00000008U)				// Protect 3

#endif // __PROTECT_H__
