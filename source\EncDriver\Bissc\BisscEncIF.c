/****************************************************************************************************
 *
 * FILE NAME:  TamagawaIF.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.09.08
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	08-09-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "hal_data.h"
#include "BisscEncIF.h"
#include "bsp_spi.h"
#include "r_spi.h"
#include "HardApi.h"
#include "bsp_timer.h"
#include "Encoder.h"



PRIVATE UINT8 tableCRC6[64] = 
{
    0x00, 0x03, 0x06, 0x05, 0x0C, 0x0F, 0x0A, 0x09,
    0x18, 0x1B, 0x1E, 0x1D, 0x14, 0x17, 0x12, 0x11,
    0x30, 0x33, 0x36, 0x35, 0x3C, 0x3F, 0x3A, 0x39,
    0x28, 0x2B, 0x2E, 0x2D, 0x24, 0x27, 0x22, 0x21,
    0x23, 0x20, 0x25, 0x26, 0x2F, 0x2C, 0x29, 0x2A,
    0x3B, 0x38, 0x3D, 0x3E, 0x37, 0x34, 0x31, 0x32,
    0x13, 0x10, 0x15, 0x16, 0x1F, 0x1C, 0x19, 0x1A,
    0x0B, 0x08, 0x0D, 0x0E, 0x07, 0x04, 0x01, 0x02
};


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
/*64-bit input data, right alignment, Calculation over 42 bits (mult. of 6) */
#pragma optimize=none
PRIVATE UINT8 CRC_BiSS_43_42bit(uint64_t dw_InputData)
{
    UINT8 b_Index = 0;
    UINT8 b_CRC = 0;
    b_Index = (UINT8)((dw_InputData >> 36u) & (uint64_t)0x00000003Fu);
    b_CRC = (UINT8)((dw_InputData >> 30u) & (uint64_t)0x0000003Fu);
    b_Index = b_CRC ^ tableCRC6[b_Index];
    b_CRC = (UINT8)((dw_InputData >> 24u) & (uint64_t)0x0000003Fu);
    b_Index = b_CRC ^ tableCRC6[b_Index];
    b_CRC = (UINT8)((dw_InputData >> 18u) & (uint64_t)0x0000003Fu);
    b_Index = b_CRC ^ tableCRC6[b_Index];
    b_CRC = (UINT8)((dw_InputData >> 12u) & (uint64_t)0x0000003Fu);
    b_Index = b_CRC ^ tableCRC6[b_Index];
    b_CRC = (UINT8)((dw_InputData >> 6u) & (uint64_t)0x0000003Fu);
    b_Index = b_CRC ^ tableCRC6[b_Index];
    b_CRC = (UINT8)(dw_InputData & (uint64_t)0x0000003Fu);
    b_Index = b_CRC ^ tableCRC6[b_Index];
    b_CRC = tableCRC6[b_Index];
    
    return b_CRC;
}
    


PRIVATE UINT8 calcBISSCCRC(uint64_t longData, UINT16 dataBitLength)
{
  UINT16 topBitLength;
  UINT8 crc=0;
  UINT8 seg=0; 
  UINT16 i;
    
  longData ^=( ((1 << 6) - 1) << (64 - dataBitLength));
  topBitLength = dataBitLength % 6;  
  crc = tableCRC6[longData >> (64 - topBitLength)];
   
  i=6 + topBitLength;
   while(i<=dataBitLength)
   {
      seg = longData >> (64 - i) & 0x3F;
      crc = tableCRC6[crc ^ seg];
      i+=6;
   }
 
  return crc;
}



/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
void hApi_BisscCmd(uint8_t Cmd, uint16_t ch) 
{
  R_SPI0_Type *spireg = (SPI_CH1 == ch)?(R_SPI1):(R_SPI3); 
  if(BISSC_READ_POSCMD == Cmd)
  {
      /* Clear the status register. */
      spireg->SPSRC = (R_SPI0_SPSRC_SPDRFC_Msk) | (R_SPI0_SPSRC_OVRFC_Msk) | (R_SPI0_SPSRC_MODFC_Msk) |
                              (R_SPI0_SPSRC_PERFC_Msk) | (R_SPI0_SPSRC_UDRFC_Msk);

      /* Clear the FIFO status */
      spireg->SPFCR = R_SPI0_SPFCR_SPFRST_Msk;

     
      /* Disable the transmit end interrupt */
      spireg->SPCR_b.CENDIE = 0;
      
    /* Load data into the transmit buffer */
      spireg->SPDCR_b.SPRDTD = 0;         /* Set to transmit mode */      

      /* Enable the SPI Transfer. */
      spireg->SPCR_b.SPE = 1;

      spireg->SPCRRM_b.RMSTTG = 1;
  }
  else
  {
    // todo 
       /* Clear the status register. */
      spireg->SPSRC = (R_SPI0_SPSRC_SPDRFC_Msk) | (R_SPI0_SPSRC_OVRFC_Msk) | (R_SPI0_SPSRC_MODFC_Msk) |
                              (R_SPI0_SPSRC_PERFC_Msk) | (R_SPI0_SPSRC_UDRFC_Msk);

      /* Clear the FIFO status */
      spireg->SPFCR = R_SPI0_SPFCR_SPFRST_Msk;

      /* Disable the transmit end interrupt */
      spireg->SPCR_b.CENDIE = 0;

      /* Enable the SPI Transfer. */
      spireg->SPCR_b.SPE = 1;   
      
      spireg->SPCRRM_b.RMSTTG = 1;
  }
}

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
uint8_t hApi_IniBissC(uint16_t ch)
{
  uint8_t ret = 0;

  // spi device init
  bsp_spi3_init(SPI3_BISSC_MODE);

  // read one time position
  if(SPI_CH1 == ch)
  {
      hApi_BisscCmd(BISSC_READ_POSCMD,ch);
  }
  else
  {
      hApi_BisscCmd(BISSC_READ_POSCMD,ch);
  }
  return ret;
}
/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS: 0:成功 1:失败
 * 失败，接收到的数据长度小于预期，认为断线 
****************************************************************************************************/
uint8_t hApi_Bissc_GetPos(uint32_t *pdata,  uint16_t ch) 
{
    uint8_t result = 0;

    R_SPI0_Type *spireg = (SPI_CH1 == ch)?(R_SPI1):(R_SPI3);
    
    if(spireg->SPSR & SPI_ERROR_MASK)
    {
       /* Clear error flag */
       spireg->SPSRC = (R_SPI0_SPSRC_PERFC_Msk) | (R_SPI0_SPSRC_MODFC_Msk) | (R_SPI0_SPSRC_OVRFC_Msk) |
                            (R_SPI0_SPSRC_UDRFC_Msk);    
       
       uint16_t dummy = spireg->SPSRC;
      (void) ((dummy));
    }
    
    if(spireg->SPRFSR == 2)
    {
      while (spireg->SPRFSR != 0)
      {
        *pdata =  spireg->SPDR;
        pdata++;
      }
    }
    else
    {
      result = 1;
    }
    
    /* Clear the SPI Receive Data Ready Flag */    
    if (1 == spireg->SPSR_b.SPDRF)
    {
        spireg->SPSRC_b.SPDRFC = 1;
    }
    
    /* Clear the receive buffer full flag */
    spireg->SPSRC = R_SPI0_SPSRC_SPRFC_Msk;    
    
    if(0 == spireg->SPSR_b.IDLNF )
    {
      spireg->SPCR_b.SPE = 0;
    }
    
    hApi_BisscCmd(BISSC_READ_POSCMD,ch); 
    
    return result;
}


/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
uint8_t hApi_Bissc_PosDecoding(ENCODER *pEnc,ENCODER *pEnc2)
{
  uint8_t result = 0;

  uint32_t Bissc_data[2] = {0};
  uint64_t Bissc_data64 = 0;
  uint32_t fw,fw2,fw3;   
  uint16_t shiftleft =0;    
  uint64_t temp64 = 0;
  uint16_t CRC_Read = 0;
  uint16_t CRC_Cal = 0;
    

  Bissc_data[0]  = (*(UINT32 *)&pEnc->V.AbsRecvBuf[0]);
  Bissc_data[1]  = (*(UINT32 *)&pEnc->V.AbsRecvBuf[4]);
  // count length from ACK to DataFrame
  fw = (*(UINT32 *)&pEnc->V.AbsRecvBuf[0])  << 2;
  
  for(UINT8 i = 0; i < 32; i++)
  { 
      if(( fw & 0x80000000) ==  0x80000000)
      {
            break;
      }
      else
      {
          shiftleft++;
          fw = fw << 1;
      }            
  }
  
  // mutiTurn high bit 
  UINT16 highbitnum = 32 - (shiftleft + 4);
  
  UINT32 mask32 = (1 << highbitnum) -1;
  fw = (*(UINT32 *)&pEnc->V.AbsRecvBuf[0]) & mask32;
        
  UINT16 lowbitnum = (pEnc->P.EncBitM - highbitnum);
    
  fw2 = (pEnc->P.DataLen - pEnc->P.EncBitM + lowbitnum) + 8;
  
  temp64 = (*(UINT32 *)&pEnc->V.AbsRecvBuf[0]);
  
  temp64 = (temp64 << 32) + (*(UINT32 *)&pEnc->V.AbsRecvBuf[4]);
  
  temp64 = (temp64 << (32 - highbitnum)) >> (32 - highbitnum);  // 移除ack 和 start 和cmd
  
  temp64 = (temp64)>> (32-fw2);  // 移除低位  mult _sig _alm_warn _crc 

  CRC_Read = temp64 & 0x3F;
  
  temp64 = temp64 >> 6;
  
  CRC_Cal = (~CRC_BiSS_43_42bit(temp64))&0x3F;

  if(CRC_Read == CRC_Cal)
  {
      UINT64 mask = (1 << 2)-1;
      pEnc->V.AbsAlmCode = (temp64 & mask);
      
      temp64 = temp64 >> (2 + pEnc->P.AlignLen);

      mask = (1 << pEnc->P.EncBitS) - 1;
      pEnc->V.SingleTurn  = (temp64 &  mask); 
      
      temp64 = temp64 >> (pEnc->P.EncBitS);
      
      mask = (1 << pEnc->P.EncBitM) - 1;
      pEnc->V.MultiTurn = (temp64 & mask);    
      result = 1;   
  }
  else
  {
    result = 0;
  }

  return result;
}


/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
uint8_t hApi_2Bissc_PosDecoding(ENCODER *pEnc,ENCODER *pEnc2)
{
  uint8_t result = 0;
  uint16_t shiftleft =0;    
  uint64_t temp64 = 0;
  uint16_t CRC_Cal = 1;

    // count length from ACK to DataFrame
    UINT16 temp = (((UINT16)pEnc->V.AbsRecvBuf[3])<<8) + (((UINT16)pEnc->V.AbsRecvBuf[2]));							
    for(uint8_t s=0;s<16;s++)
    {
      if(( temp& 0x800) ==  0x800)
      {
              break;
      }
      else
      {
          shiftleft++;
          temp<<=1;
      }
    }
                
    temp64 = (*(UINT32 *)&pEnc->V.AbsRecvBuf[0]);         
    temp64 = (temp64 << 32) + (*(UINT32 *)&pEnc->V.AbsRecvBuf[4]);        
    temp64 = (temp64)<<(BISS_HEAD + shiftleft);
    
    CRC_Cal = (calcBISSCCRC(temp64,pEnc->P.DataLen + BISS_ALARMLEN + BISS_CRCLEN));  

    if(CRC_Cal == 0)
    {
      pEnc2->V.SingleTurn = (temp64>>(64-pEnc2->P.EncBitS));
      pEnc2->V.MultiTurn = 0;
      pEnc->V.SingleTurn = (temp64<<(pEnc2->P.EncBitS ))>>(64-pEnc->P.EncBitS);
      pEnc->V.MultiTurn = 0;
      pEnc->V.AbsAlmCode = ((temp64<<(pEnc->P.DataLen))>>(64-BISS_ALARMLEN))&0x3; 
      result = 1;
    }
    else
    {
      result = 0;
    }
  
  return result;
}