/****************************************************************************************************
 *
 * FILE NAME:  AlarmTable.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.28
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	28-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseDef.h"
#include "Alarm.h"


/****************************************************************************************************
 * DESCRIPTION:
 *
****************************************************************************************************/
const ALM_TBL_DEF SvoAlmTbl[] =
{

	{ 0x0000, 0x02, 0x0F },  // No Alarm 
	{ 0x5530, 0x08, 0x0F },  // Eeprom error 
	{ 0x5531, 0x08, 0x0F },  // Eeprom Version error     
	{ 0x8400, 0x00, 0x0F },  // over speed 
	{ 0x0FFF, 0x00, 0x0F },  // over run
	{ 0x3210, 0x00, 0x0F },  // over voltage	
	{ 0x2320, 0x00, 0x0F },  // over current
	{ 0x3220, 0x00, 0x01 },  // under voltage	
	{ 0x3321, 0x00, 0x0F },  // Main circuit wiring error   // todo
    { 0x7303, 0x00, 0x0F },  // Abnormal main circuit detector
	{ 0x6320, 0x00, 0x0F },  // Parameter setting error 
    { 0xFF03, 0x00, 0x0F },  // Power supply related input signal error     // todo
    { 0x3130, 0x00, 0x01 },  // Power failure phase	
    { 0x7111, 0x00, 0x0F },  // Regenerative abnormality
    { 0x7112, 0x00, 0x0F },  // Regenerative over load
    { 0x5210, 0x00, 0x0F },  // Abnormal AD sampling circuit
    { 0x0E12, 0x00, 0x01 },  // Ethercat initialization error
    { 0xFF82, 0x00, 0x0F },  // Scheduler error   
	{ 0x4230, 0x00, 0x0F },  // IPM Fault 
	{ 0x8611, 0x00, 0x01 },  // over position error
	{ 0x8612, 0x00, 0x0F },  // over position error at servo on
	{ 0x8613, 0x00, 0x01 },  // over position error at servo on when speed limit
	{ 0x0FFE, 0x00, 0x0F },  // Phase find alarm
	{ 0x7380, 0x00, 0x0F },  //  Encoder communication break
	{ 0x738A, 0x00, 0x0F },  // encoder CRC error
	{ 0xFF04, 0x01, 0x0F },  // encoder data error
	{ 0x7387, 0x00, 0x0F },  // encoder over speed
	{ 0x7385, 0x00, 0x0F },  // encoder counter error
	{ 0x7389, 0x00, 0x0F },  // encoder counter overflow
	{ 0x7384, 0x08, 0x01 },  // encoder over heat
	{ 0x7383, 0x00, 0x0F },  // encoder multi-turn error
	{ 0x7382, 0x00, 0x01 },  // encoder battery error
	{ 0xFF25, 0x00, 0x01 },  // Overload (instantaneous maximum load)
	{ 0x3230, 0x00, 0x01 },  // Overload (continuous maximum load)
	{ 0xFFFE, 0x00, 0x0F },  // motor parameter identification error
    { 0x4210, 0x00, 0x01 },  // Over San temperation error
    { 0xFF84, 0x00, 0x01 },  // ethercat synchronization error
    { 0xFF1D, 0x00, 0x0F },  // STO1 error
    { 0xFF1C, 0x00, 0x0F },  // STO2 error
    { 0xFF30, 0x00, 0x0F },  // fpga software version errror
	{ 0x7121, 0x00, 0x01 },  // Motor Stall alarm
	{ 0xF0FF, 0x00, 0x01 },  // Vibration alarm			
	{ 0xF0FE, 0x00, 0x0F },  // Auto tuning alarm
	{ 0xF0FD, 0x00, 0x0F },  // Parameter combination error
	{ 0xF0FC, 0x00, 0x0F },  // Phase information mismatch	
	{ 0xF0FB, 0x00, 0x0F },  // Magnetic pole detection failure						
	{ 0xF0FA, 0x00, 0x0F },  // Overtravel detection at magnetic pole detection	
	{ 0xF0F9, 0x00, 0x0F },  // Magnetic pole detection incomplete			
	{ 0xF0F8, 0x00, 0x0F },  // Excessive range of magnetic pole detection		
	{ 0xF0F7, 0x00, 0x0F },  // Magnetic pole detection failure 2
	{ 0xFFF1, 0x00, 0x0F },  // Device Global Alarm for Aix_1
	{ 0xFF90, 0x00, 0x01 },  // virtual serial communication Error
    { 0x5532, 0x00, 0x0F },  // Hardware Version error 
    { 0xFF91, 0x00, 0x01 },  // ethercat break error
    { 0xFF11, 0x00, 0x0F },  // Driver error
    { 0xFF12, 0x00, 0x0F },  // 24V error
    { 0xFF13, 0x00, 0x0F },  // PWM Close
    { 0xFF92, 0x00, 0x0F },  // relay error                 
	{ 0xFA01, 0x00, 0x0F },  // Parameter Err
	{ 0xFA02, 0x00, 0x0F },  // Motor Identification
	{ 0xFA03, 0x00, 0x0F },  // alarm reserved3
	{ 0xFA04, 0x00, 0x0F },  // alarm reserved4
	{ 0xFA05, 0x00, 0x0F },  // alarm reserved5
	{ 0xFA06, 0x00, 0x0F },  // alarm reserved6
	{ 0xFA07, 0x00, 0x01 },  // UID check on pass
	{ 0xFA08, 0x00, 0x01 },  // DI Duplicate allocation 
	{ 0xFA09, 0x00, 0x0F },  // alarm reserved9
	{ 0xFA10, 0x00, 0x01 },  // DI fast stop in vliad
	{ 0xFA11, 0x00, 0x0F },  // Phase Current initail failed 
	{ 0xFA12, 0x00, 0x0F },  // alarm reserved12	
	{ 0xFA13, 0x00, 0x0F },  // alarm reserved13
	{ 0xFA14, 0x00, 0x01 },  // IPM OTM
	{ 0xFA15, 0x00, 0x0F },  // ECAT_EEPROM write eeror
	{ 0xFA16, 0x00, 0x0F },  // ENC_EEPROM RW Error
	{ 0xFA17, 0x08, 0x0F },  // bus over current
	{ 0xFA18, 0x00, 0x01 },  // alarm reserved18
	{ 0xFA19, 0x00, 0x01 },  // alarm reserved19
	{ 0xFA20, 0x00, 0x01 },  // alarm reserved20
	{ 0xFA21, 0x00, 0x01 },  // alarm reserved21
	{ 0xFA22, 0x00, 0x01 },  // alarm reserved22
	{ 0xFA23, 0x00, 0x01 },  // alarm reserved23
	{ 0xFA24, 0x00, 0x01 },  // alarm reserved24
	{ 0xFA25, 0x00, 0x01 },  // alarm reserved25
	{ 0xFA26, 0x00, 0x01 },  // alarm reserved26
	{ 0xFA27, 0x00, 0x01 },  // alarm reserved27
	{ 0xFA28, 0x00, 0x01 },  // alarm reserved28
	{ 0xFA29, 0x00, 0x01 },  // alarm reserved29
	{ 0xFA30, 0x00, 0x01 },  // alarm reserved30
	{ 0xFA31, 0x00, 0x01 },  // alarm reserved31
	{ 0xFA32, 0x00, 0x01 },  // alarm reserved32
	{ 0xFA33, 0x00, 0x01 },  // alarm reserved33
	{ 0xFA34, 0x00, 0x01 },  // alarm reserved34
	{ 0xFA35, 0x00, 0x01 },  // alarm reserved35
	{ 0xFA36, 0x00, 0x01 },  // alarm reserved36
	{ 0xFA37, 0x00, 0x01 },  // alarm reserved37
	{ 0xFA38, 0x00, 0x01 },  // alarm reserved38
    
    //warning
	{ 0xFFFD, 0x01, 0x00 },  // over position error
	{ 0xFFFC, 0x01, 0x00 },  // over position error at servo on
	{ 0xFFFB, 0x01, 0x00 },  // need reboot (Parameter change)
	{ 0xFFFA, 0x01, 0x00 },  // Undervoltage
	{ 0xFFF9, 0x01, 0x00 },  // Regenerative warning
	{ 0xFFF8, 0x01, 0x00 },  // Regenerative over load warning
	{ 0x7381, 0x01, 0x00 },  // encoder battery alarm
	{ 0xFF31, 0x01, 0x00 },  // Overload
    { 0xFFF7, 0x01, 0x00 },  // motor parameter identification warning
	{ 0xF00F, 0x01, 0x00 },  // Vibration warning
    { 0x8600, 0x01, 0x00 },  // Following position warning
    { 0x8601, 0x01, 0x00 },  // position over limit warning
    { 0xFFF6, 0x01, 0x00 },  // FAN warning
	{ 0xFC01, 0x01, 0x00 },  // warning reserved1
	{ 0xFC02, 0x01, 0x00 },  // warning reserved2
	{ 0xFC03, 0x01, 0x00 },  // warning reserved3
	{ 0xFC04, 0x01, 0x00 },  // warning reserved4
	{ 0xFC05, 0x01, 0x00 },  // warning reserved5
	{ 0xFC06, 0x01, 0x00 },  // warning reserved6
	{ 0xFC07, 0x01, 0x00 },  // warning reserved7
	{ 0xFC08, 0x01, 0x00 },  // warning reserved8
	{ 0xFC09, 0x01, 0x00 },  // warning reserved9
	{ 0xFC10, 0x01, 0x00 },  // warning reserved10
	{ 0xFC11, 0x01, 0x00 },  // warning reserved11
	{ 0xFC12, 0x01, 0x00 },  // warning reserved12
	{ 0xFC13, 0x01, 0x00 },  // warning reserved13
	{ 0xFC14, 0x01, 0x00 },  // warning reserved14
	{ 0xFC15, 0x01, 0x00 },  // warning reserved15
	{ 0xFC16, 0x01, 0x00 },  // warning reserved16
	{ 0xFC17, 0x01, 0x00 },  // warning reserved17
	{ 0xFC18, 0x01, 0x00 },  // warning reserved18
	{ 0xFC19, 0x01, 0x00 },  // warning reserved19        
};

/****************************************************************************************************
 * DESCRIPTION:
 *
****************************************************************************************************/
const UINT32 AlmDefTblEntNum = sizeof(SvoAlmTbl)/sizeof(SvoAlmTbl[0]);


