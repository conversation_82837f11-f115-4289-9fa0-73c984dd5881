/****************************************************************************************************
 *
 * FILE NAME:  Round.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.11.11
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	11-11-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Global.h"
#include "EepromIF.h"
#include "HardApi.h"
#include "RegAccessIF.h"
//#include "ecat_eeprom.h"
#include "ktm59xxIF.h"
#include "bsp_ecat.h"
#include "Encoder.h"
#include "TamagawaIF.h"
#include "ecatslv.h"
#include "mm.h"

extern PUBLIC void Round_MotorPrmSaveToEep(AXIS_HANDLE *AxisB);
extern PUBLIC void Round_ClampingJawPrmSaveToEep(AXIS_HANDLE *AxisB);
extern  UINT32 Uid_Check_Result;
extern PUBLIC void Bus_Com_init( AXIS_HANDLE *AxisRscR );
extern char MemoryMalloc[2048];

#define UID_ALRAM_TIME_H   10*24     //  10 day hours
#define UID_ALRAM_TIME_S   UID_ALRAM_TIME_H*60*60  //continue 24 day
/****************************************************************************************************
* DESCRIPTION: 
*
* RETURNS:
*
****************************************************************************************************/
PRIVATE void PnPrmRunBackgrndProc( AXIS_HANDLE *Axis )
{
    PRM_RSLT	    rslt;
    AUX_PRM         *AuxPrm = &Axis->Prm->PnAuxPrm;
    ECAT_PRM        *EcatPrm = &Axis->Prm->PnEcatPrm;
    PHASE_FIND      *FhsFnd = &Axis->MotorIdentify->FhaseFind;
    REGIF_CONFIG_T  *RegCfg = Axis->RegManager->hPnReg;
    PRM_ATTR        *PrmAttr = NULL;
    TCiA402Axis     *Cia402Axis = &Axis->BaseCtrls->Cia402Axis;
    UINT16          FunctionSw = (Axis->Prm->PnCtrlCfgPrm.FunctionSw); 
    UINT16               HardwareVersion = 0;   
    UINT8           ret = 0;      
    
	 if(Axis->BaseLoops->Enc->V.ExCmd)
	 {
		 AuxPrm->EncCmd = Axis->BaseLoops->Enc->V.ExCmd;
	 }
         
	  
	
    if(AuxPrm->SysReboot)
    {

       hApi_SystemReset();
       
       
       AuxPrm->SysReboot = 0;
       return;
    }
    
    #if NO_ETHERCAT
    if((Axis->EcatDate->DevIdConfFlag == 1) && (FunctionSw &  FUNC_ATUOID_SET))
    {
        Axis->Prm->PnCfgPrm.DevID = Axis->EcatDate->EcatStationAddr;
        AuxPrm->DevIdConfig =  0x1100;
        Axis->EcatDate->DevIdConfFlag  = 2;
    }
    #endif
 
    if(AuxPrm->MotEncZero && !Axis->BaseCtrls->BaseEnable)
    {
      
        if(Axis->BaseLoops->UseEncoder2)
        {
          Axis->Prm->PnCfgPrm.EncZeroOffset = - Axis->BaseLoops->Enc2->V.MotPos;
           Axis->BaseLoops->Enc2->P.EncZeroOffset = Axis->Prm->PnCfgPrm.EncZeroOffset;
           Axis->BaseLoops->Enc->P.EncZeroOffset = 0;
           Axis->BaseLoops->Enc2->V.EncZeroOffFlag = 1;
        }
        else
        {
        Axis->Prm->PnCfgPrm.EncZeroOffset = - Axis->BaseLoops->Enc->V.MotPos;
          Axis->BaseLoops->Enc2->P.EncZeroOffset = 0;
          Axis->BaseLoops->Enc->P.EncZeroOffset = Axis->Prm->PnCfgPrm.EncZeroOffset;
          Axis->BaseLoops->Enc->V.EncZeroOffFlag = 0;
        }
        
       UINT32 CoeIndex = 0x20010039;
      PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
      rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);
      if(PRM_RSLT_SUCCESS != rslt)
      {
          ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
      }
        
        AuxPrm->MotEncZero = 0;
    }
    
    
    if(AuxPrm->AnalogZeroCal && !Axis->BaseCtrls->BaseEnable)
    {
        if(hApi_AnalogInputOffsetCheck(&Axis->Prm->PnCfgPrm.Analog1ZeroPoint,&Axis->Prm->PnCfgPrm.Analog2ZeroPoint))
        {          
            UINT32 CoeIndex = 0x2001003A;
            PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
            rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);
            if(PRM_RSLT_SUCCESS != rslt)
            {
                ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
            }
      
            CoeIndex = 0x2001003B;
            PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
            rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);
            if(PRM_RSLT_SUCCESS != rslt)
            {
                ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
            }
            
            AuxPrm->AnalogZeroCal = 0;          
        }

    }
    
    
    if(!Axis->BaseCtrls->BaseEnable)
    {
        if(Axis->BaseLoops->UseEncoder2)
        {
           Axis->BaseLoops->Enc->P.EncZeroOffset = 0;
           Axis->BaseLoops->Enc2->P.EncZeroOffset = Axis->Prm->PnCfgPrm.EncZeroOffset;
        }
        else
        {
      Axis->BaseLoops->Enc->P.EncZeroOffset = Axis->Prm->PnCfgPrm.EncZeroOffset;
          Axis->BaseLoops->Enc2->P.EncZeroOffset = 0;
        }
    }
    
    if(AuxPrm->SysPrmInit == 1)
    {
		Cia402Axis->LastSysPrmInit = AuxPrm->SysPrmInit;

        PrmLoadDefaultValues();

		PrmLoadSystemDefaultValues(Axis->RegManager->hPnReg);
        
        ReadDevInfoEeprom(&HardwareVersion);
        
        Axis->HardwareVer =  hApi_DevicenName();  

        PrmLoadDeviceDefaultValues(&Axis->Prm->PnCfgPrm, Axis->AxisCobID,Axis->HardwareVer);
                 
        rslt = PrmStoreAllValuesToEeprom(Axis->RegManager->hPnReg); 
        if(PRM_RSLT_SUCCESS != rslt)
        {
            ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
        }
		SysResetAxisParameters(Axis);
        AuxPrm->SysPrmInit = 0;
    }
    else if(AuxPrm->SysPrmInit == 2)
    {
        SysResetAxisParameters(Axis);
        AuxPrm->SysPrmInit = 0;
    }
	else if(AuxPrm->SysPrmInit == 0xA5 || AuxPrm->SysPrmInit == 0x5A)
    {
        Cia402Axis->LastSysPrmInit = AuxPrm->SysPrmInit;

        PrmLoadDefaultValues();
        
		PrmLoadSystemDefaultValues(Axis->RegManager->hPnReg);
        
        WriteDevInfoEeprom(&HardwareVersion);        

        Axis->HardwareVer =  hApi_DevicenName();  

        PrmLoadDeviceDefaultValues(&Axis->Prm->PnCfgPrm, Axis->AxisCobID,Axis->HardwareVer);
        
        rslt = PrmStoreAllValuesToEepromPro(Axis->RegManager->hPnReg); 
        
        if(PRM_RSLT_SUCCESS != rslt)
        {
            ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
        }
        
		SysResetAxisParameters(Axis);

        if(Axis->BaseLoops->Enc->P.EncType == ENC_TYPE_KTM)
        {
            Axis->BaseLoops->Enc->V.ExCmdEn = TRUE;
            hApi_KTM59_Factory_Init();
            Axis->BaseLoops->Enc->V.ExCmdEn = FALSE;
        }
        
        // Ecat_DeInit();

        // if(0 != Ecat_Eeprom_Write())
        // {
        //     ALMCheckGlobalAlarm(ALM_ECAT_EEPROM);  
        // }        
        

        AuxPrm->SysPrmInit = 0;
        hApi_SystemReset();
        return;           
    }
    else if(AuxPrm->LoadPrm)
    {
        rslt = PrmLoadEepromValues(Axis->RegManager->hPnReg, 0, FALSE);
        if(PRM_RSLT_SUCCESS != rslt)
        {
            if(rslt == PRM_RSLT_CONDITION_ERR)
            {
                PrmLoadSystemDefaultValues(Axis->RegManager->hPnReg);
                ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM_VER);                 
            }
            else if(rslt == PRM_RSLT_RWACC_ERR)
            {
////                PrmLoadSystemDefaultValues(Axis->RegManager->hPnReg);
                ALMSetServoAlarm(Axis->AlmMngr, ALM_HARDWARE_VER);						
            }
            else
            {
                ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);
            }                      
        }  
        AuxPrm->LoadPrm = 0;
    }
    else if(AuxPrm->SavePrm)
    {
        rslt = PrmStoreAllValuesToEeprom(Axis->RegManager->hPnReg);
        if(PRM_RSLT_SUCCESS != rslt)
        {
            ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
        }  
        AuxPrm->SavePrm = 0;      
    }
    else if(FhsFnd->V.MechOfstSave)
    {
        UINT32 CoeIndex = 0x20000015;
        
        Axis->Prm->PnMotPrm.AbsEncOffset = Axis->BaseLoops->Enc->P.MechAngleOffset;
        
        PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
        
        rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);
        if(PRM_RSLT_SUCCESS != rslt)
        {
            ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
        }  
        
        FhsFnd->V.MechOfstSave = FALSE;
    }
    else if(Axis->AlmMngr->AlmTmStampRst == TRUE)
    {
        UINT32  CoeIndex = 0x2006000C;    
                
        PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
        rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);
        
        if(PRM_RSLT_SUCCESS != rslt)
        {
            ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
        }
        
        Axis->AlmMngr->AlmTmStampRst = FALSE;        
    }
    else if(AuxPrm->DevIdConfig & 0xFF00)
    {
      UINT32 CoeIndex = 0x20010029;
      PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
      rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);
      if(PRM_RSLT_SUCCESS != rslt)
      {
          ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
      }
      
      AuxPrm->DevIdConfig = 0;
    }
    // else if(AuxPrm->EscWrite && !Axis->BaseCtrls->BaseEnable)
    // {
    //    if(1 == AuxPrm->EscWrite)
    //    {
    //       Ecat_DeInit();
        
    //       if(0 != Ecat_Eeprom_Write())
    //       {
    //       ALMCheckGlobalAlarm(ALM_ECAT_EEPROM);  
    //       }
       
    //    }
    //    else if((2 == AuxPrm->EscWrite) || (3 == AuxPrm->EscWrite))
    //    {
       
    //        UINT16 PhyAddr     = (EcatPrm->PhyAddr&0x03);
    //        UINT16 PhyRegType  = (EcatPrm->PhyAddr&0x10)>>4;
    //        UINT16 PhyRegAddr  = (EcatPrm->PhyRegAddr);
    //        if(2 == AuxPrm->EscWrite)
    //        {
    //          XMC_ECAT_ReadPhy(0,PhyRegType,PhyRegAddr,&(EcatPrm->Phy0RegValue));
    //          XMC_ECAT_ReadPhy(2,PhyRegType,PhyRegAddr,&(EcatPrm->Phy1RegValue));
    //        }
    //        else
    //        {
    //          XMC_ECAT_WritePhy(0,PhyRegType,PhyRegAddr,EcatPrm->Phy0RegValue);
    //          XMC_ECAT_WritePhy(2,PhyRegType,PhyRegAddr,EcatPrm->Phy1RegValue);
    //        }
           
    //    }
    //    else if((4 == AuxPrm->EscWrite))
    //    {
    //      /* memory pool initialization , for function MyMalloc */
    //       InitMm( (unsigned long)&MemoryMalloc[0], (unsigned long)&MemoryMalloc[2047]);     
    //       Bus_Com_init(Axis);
    //       bEscStateTrans = 0;
    //    }
    //    else if((5 == AuxPrm->EscWrite))
    //    {
    //       User_Phy_Reset(1);
                        
    //    }
            
    //    AuxPrm->EscWrite = 0; 
    // }
    
     Round_MotorPrmSaveToEep(Axis);
    //  Round_ClampingJawPrmSaveToEep(Axis);
    
//    if(AuxPrm->EncCmd && !Axis->BaseCtrls->BaseEnable)
     
    if(AuxPrm->EscWrite)
    {
        AuxPrm->EncCmd =  AuxPrm->EscWrite;
    }
    
    if(AuxPrm->EncCmd )
    {      
        RpiEncExCmdRound(Axis->BaseLoops, AuxPrm->EncCmd, Axis->AxisID);
    }
    AuxPrm->EncCmd = 0;
    AuxPrm->EscWrite = 0;
    Axis->BaseLoops->Enc->V.ExCmd = 0;

	
}



/****************************************************************************************************
* DESCRIPTION: 
*   
* RETURNS:
*
****************************************************************************************************/
#if NO_ADVFUNC
PRIVATE void Rpi_AAT_Run( AXIS_HANDLE *AxisRscR )
{
	INT32 CmdCode=0;
    
    if( KlibGetLongTimerMs(AxisRscR->AdatHndl->ExeTime ) < 300 )
        return;
    
	switch(AxisRscR->AdatHndl->Step)
	{
		case 0 :
			CmdCode = 0x1201;
			fnCalcOprationMode1(PRM_ACCCMD_WRITE, 0, AxisRscR,  &CmdCode);
			AxisRscR->AdatHndl->Step = 1;
		break;
		
		case 1 :
			CmdCode = 0x04;  // FCMD_SV
			fnCalcOprationCmd1(PRM_ACCCMD_WRITE, 0, AxisRscR, &CmdCode);

			AxisRscR->AdatHndl->AdatReg.AdatPnCmd = AxisRscR->Prm->PnAuxPrm.AATCmd;

			if(AxisRscR->Prm->PnAuxPrm.AATCmd & 0x01)
			{
				AxisRscR->AdatHndl->AdatReg.AdatMode = FALSE;
			}
			else
			{
				AxisRscR->AdatHndl->AdatReg.AdatMode = TRUE;
			}

			if(AxisRscR->AdatHndl->AdatReg.AdatLevel == ADAT_LEVEL_SPD)
			{
				AxisRscR->AdatHndl->AdatReg.AdatLevel = ADAT_LEVEL_NOR;
				AxisRscR->AdatHndl->AdatReg.AdatPnCmd  |= 0x04;
			}
			
			AxisRscR->AdatHndl->Step = 2;

		break;

		case 2 :
//			if( KlibGetLongTimerMs(AxisRscR->AdatHndl->ExeTime ) > 300 )
			{
				CmdCode = 0x02;  //FCMD_UP
				fnCalcOprationCmd1(PRM_ACCCMD_WRITE, 0, AxisRscR, &CmdCode);
//				KlibRstLongTimer( &AxisRscR->AdatHndl->ExeTime );
			}

			if(AxisRscR->AdatHndl->AdatReg.AdatState == ADAT_STS_DONE)
			{
//				AxisRscR->Prm->PnAuxPrm.SavePrm = 1;
				AxisRscR->AdatHndl->Step = 3;
			}

			if((AxisRscR->AdatHndl->AdatV.ErrState& 0x00ff) <= ADAT_NG 
							|| AxisRscR->AdatHndl->AdatReg.AdatState == ADAT_STS_ERR)
			{
				AxisRscR->AdatHndl->Step = 4;
			}

		break;
			
		case 3:
			CmdCode = 0x07;
			fnCalcOprationCmd1(PRM_ACCCMD_WRITE, 0, AxisRscR, &CmdCode);
			AxisRscR->AdatHndl->Step =4;
		break;

		case 4:
			CmdCode = 0x01;
			fnCalcOperationFinish(PRM_ACCCMD_WRITE, 0, AxisRscR, &CmdCode);
			AxisRscR->AdatHndl->Step = 5;
		break;

		case 5:
			if(AxisRscR->BaseCtrls->BaseEnable == FALSE)
			{
				AxisRscR->AdatHndl->Step = 0;
			}
		break;

		default:
            break;
		
	}

    KlibRstLongTimer( &AxisRscR->AdatHndl->ExeTime );

}
#endif

/****************************************************************************************************
* DESCRIPTION: 
*        Round Main Procedure
* RETURNS:
*
****************************************************************************************************/
static UINT32   SccntLast=0;
PUBLIC void RpiRoundMain( AXIS_HANDLE *AxisRscR )
{
	INT32			ax_no;
	INT32			timer;
	UINT32			timeStamp, rdTime;
	AXIS_HANDLE 	*AxisRsc;
	ALARM       	*AlmManager;
	ANOTCHSEQCTRL	ANotchCtrl;
	
	if(AxisRscR->TaskProcessTime->SCCount == SccntLast) 
		return;

//	/* Alarm Manager: EEPROM log writing */
	timer = KlibGetMainPowerOffTimer();
	if((timer <= 0) || (timer >= KLIB_EEPDISABLE_TIME))
	{/* Do not write for 100ms after Main Power off */
		for( ax_no = 0; ax_no < MAX_AXIS_NUM; ax_no++ )
		{
			AxisRsc = &AxisRscR[ax_no];
			ALMLogWriteService(AxisRsc->AlmMngr);
		}
	}
	else
	{
		AlmManager = AxisRscR->AlmMngr;
//		
//		/* Write Time Stamp with Main Power off */
		rdTime = timeStamp = hApi_GetTimeStamp1000ms();
		EepdevReadValues( EEP_NOSUMCMN_ADDRESS(EEP_TIMESTAMP_OFS),		 (UINT16*)&rdTime, 2 );
		if(timeStamp != rdTime)
		{
			EepdevWriteNoSumValues( EEP_NOSUMCMN_ADDRESS(EEP_TIMESTAMP_OFS),		(UINT16*)&timeStamp, 2 );
		}
	}
//
//
	for( ax_no = 0; ax_no < MAX_AXIS_NUM; ax_no++ )
	{
		AxisRsc = &AxisRscR[ax_no];
		PnPrmRunBackgrndProc(AxisRsc);

		FunRunTimeService(AxisRsc->FnManager, FUN_CH1);
		FunRunTimeService(AxisRsc->FnManager, FUN_CH2);

		if( AxisRsc->BaseLoops->Enc->V.PhaseReady == TRUE )		
		{
			if ( !(AxisRsc->BaseSeq->ServoReady) && AxisRsc->FnCmnCtrl->FnSvonReq==TRUE)
			{ 
//				AxisRsc->FnCmnCtrl->FnSvonReq = FALSE;
				if(AxisRsc->AlmMngr->Status.AlmFlag == FALSE)
				{
//					ALMSetServoAlarm( AxisRsc->AlmMngr, WRN_SRVNRD ); 
				}
				
			}
		}
        #if NO_ADVFUNC
		/* Automatic notch filter setting pointer setting for call */
		ANotchCtrl.ANotchSeq = AxisRsc->ANotchSeq;
		ANotchCtrl.ANotch = AxisRsc->ANotch;
		ANotchCtrl.FftAna = AxisRsc->FftAna;
		ANotchCtrl.DetVib = AxisRsc->DetVib;
		ANotchCtrl.Prm = AxisRsc->Prm;
		ANotchCtrl.RegCfg = AxisRsc->RegManager->hPnReg;
		/* Data setting processing for automatic notch filter setting */
		AutoNotchSetCondition( ANotchCtrl.ANotchSeq,
							   AxisRsc->BaseCtrls->TuneLessCtrl.var.TuningFuncRun,
							   AxisRsc->SeqCtrlOut->BaseEnableReq,
							   AxisRsc->BaseCtrls->TuneLessCtrl.var.TuneLessAct,
							   AxisRsc->SeqCtrlOut->OverTrvlSts );
		/* Automatic notch filter setting sequence execution processing */
		AutoNotchFilterSetting( &ANotchCtrl );
		#endif
	}


	for( ax_no = 0; ax_no < MAX_AXIS_NUM; ax_no++ )
	{
		AxisRsc = &AxisRscR[ax_no];
        #if NO_ADVFUNC
		if(AxisRsc->Prm->PnAuxPrm.AATCmd >0 && AxisRsc->AlmMngr->Status.AlmFlag == FALSE)
		{
			Rpi_AAT_Run(AxisRsc);

			if(AxisRsc->AdatHndl->Step == 0)
                    {
				AxisRsc->Prm->PnAuxPrm.AATCmd = 0;
                    }
                    else if((AxisRsc->AdatHndl->AdatReg.AdatState == ADAT_STS_JERR) || 
                            (AxisRsc->AdatHndl->AdatReg.AdatState == ADAT_STS_ERR))
                    {
                      AxisRsc->Prm->PnAuxPrm.AATCmd = 0;
                    }
		}
		else if(AxisRsc->AdatHndl->Step != 0)
		{
			if(AxisRsc->AdatHndl->Step<4)
			{
				AxisRsc->AdatHndl->Step = 4;
			}
              
			Rpi_AAT_Run(AxisRsc);
			AxisRsc->Prm->PnAuxPrm.AATCmd = 0;
            
                        if(AxisRsc->AlmMngr->Status.AlmFlag )
			{
				AxisRsc->AdatHndl->AdatReg.AdatState == ADAT_STS_ABORT;
			}
		}
        #endif

	}
    
    
    
   if(1 == Uid_Check_Result)
   {
       if(hApi_GetActive1000ms() > UID_ALRAM_TIME_S)
       {
         //alram
          ALMSetGlobalAlarm(ALM_RSVD38); 
       }
   }
      
	SccntLast = AxisRscR->TaskProcessTime->SCCount;

}
