﻿ 19.783: File generated Fri Jun 27 09:10:52 2025

  0.000: Pass 1 of 1
  0.000: Starting fragment-style flashloader pass.
  0.000: Device: E:\1.Work\1.code\1.Workspace\Renesas\rzn2l_1axis_lv\humanjoint\rzn2l-boot\FlashRSK_RZN2L_SerialFlash\FlashRSK_RZN2L_SerialFlash.flash
  0.000: Flash loader: E:\1.Work\1.code\1.Workspace\Renesas\rzn2l_1axis_lv\humanjoint\rzn2l-boot/FlashRSK_RZN2L_SerialFlash/Debug/Exe/FlashRSK_RZN2L_SerialFlash.out
  3.953: FlashInitEntry is at 0x1000'163c
  3.953: FlashWriteEntry is at 0x1000'164c
  3.953: FlashEraseWriteEntry is at 0x1000'1654
  3.953: FlashBreak is at 0x1000'1064
  3.953: FlashBufferStart is at 0x1000'1700
  3.953: FlashBufferEnd is at 0x1017'fdec
  3.953: theFlashParams is at 0x1017'fdec
  3.953: FlashPreInitEntry not found
  3.953: FlashChecksumEntry not found
  3.953: FlashSignoffEntry not found
  3.953: page size is 256 (0x100)
  3.953: filler is 0xff
  3.953: buffer size is 1566208 (0x17'e600) (0x17'e6ec before rounding)
  3.953: Relative offset is   3.953: 0 (0x0)
  3.953: Base of flash at 0x6800'0000
  3.953: SimpleCode records (after offset):
  3.953:   Record 0: @ 0x6800'0000 [76 (0x4c) bytes] 0x6800'0000 - 0x6800'004b [00 00 00 00 ... ]
  3.953:   Record 1: @ 0x6800'004c [3848 (0xf08) bytes] 0x6800'004c - 0x6800'0f53 [83 07 0b e3 ... ]
  3.953:   Record 2: @ 0x6800'8080 [24 (0x18) bytes] 0x6800'8080 - 0x6800'8097 [ff ff ff ff ... ]
  3.953:   Record 3: @ 0x6800'80c0 [31588 (0x7b64) bytes] 0x6800'80c0 - 0x6800'fc23 [80 b5 73 b6 ... ]
  3.953: ->init       : base @ 0x6800'0000, image size 0xfc24
  3.953: Store TargetParams to 0x1017'fdec
  3.955: Setting PC to 0x1000'163c (FlashInitEntry)
  4.014:   timing(init): 0.0312 (CPU) 0.0600 (elapsed)
  4.014: Load TargetParams from 0x1017'fdec
  4.017: Transaction list:
  4.017:   Transaction 0: @ 0x6800'0000 + 0x0 (0x1000=4096 bytes) 1 packet(s).
  4.017:     Will erase 1 block(s):
  4.017:       0: 0x6800'0000 (0x1000 bytes)
  4.017:   Transaction 1: @ 0x6800'8000 + 0x0 (0x7d00=32000 bytes) 8 packet(s).
  4.017:     Will erase 8 block(s):
  4.017:       0: 0x6800'8000 (0x1000 bytes)
  4.017:       1: 0x6800'9000 (0x1000 bytes)
  4.017:       2: 0x6800'a000 (0x1000 bytes)
  4.017:       3: 0x6800'b000 (0x1000 bytes)
  4.017:       4: 0x6800'c000 (0x1000 bytes)
  4.017:       5: 0x6800'd000 (0x1000 bytes)
  4.017:       6: 0x6800'e000 (0x1000 bytes)
  4.017:       7: 0x6800'f000 (0x1000 bytes)
  4.018: ->multi_erase: 1 blocks (0x8 bytes in buffer) [00 00 00 68 ... ]
  4.019: Store TargetParams to 0x1017'fdec
  4.021: Setting PC to 0x1000'1654 (FlashEraseWriteEntry)
  4.048:   timing(erase): 0.0000 (CPU) 0.0270 (elapsed)
  4.048: Load TargetParams from 0x1017'fdec
  4.050: ->write      : @ 0x6800'0000 (0x1000 bytes, offset 0x0 into block @ 0x6800'0000) [00 00 00 00 ... ]
  4.050: Writing 0x1000 bytes to FlashBuffer @ 0x1000'1700
  4.061: Store TargetParams to 0x1017'fdec
  4.064: Setting PC to 0x1000'164c (FlashWriteEntry)
  4.103:   timing(write): 0.0156 (CPU) 0.0390 (elapsed)
  4.103: Load TargetParams from 0x1017'fdec
  4.105: ->multi_erase: 8 blocks (0x40 bytes in buffer) [00 80 00 68 ... ]
  4.107: Store TargetParams to 0x1017'fdec
  4.108: Setting PC to 0x1000'1654 (FlashEraseWriteEntry)
  4.173:   timing(erase): 0.0156 (CPU) 0.0650 (elapsed)
  4.173: Load TargetParams from 0x1017'fdec
  4.176: ->write      : @ 0x6800'8000 (0x7d00 bytes, offset 0x0 into block @ 0x6800'8000) [ff ff ff ff ... ]
  4.176: Writing 0x7d00 bytes to FlashBuffer @ 0x1000'1700
  4.243: Store TargetParams to 0x1017'fdec
  4.245: Setting PC to 0x1000'164c (FlashWriteEntry)
  4.377:   timing(write): 0.0469 (CPU) 0.1330 (elapsed)
  4.377: Load TargetParams from 0x1017'fdec
  4.397: Duration:   0.33 (CPU)   4.40 (elapsed)
  4.397:   of which on target: 0.1094 (CPU) 0.3240 (elapsed)
  4.397: Flash loading pass finished
