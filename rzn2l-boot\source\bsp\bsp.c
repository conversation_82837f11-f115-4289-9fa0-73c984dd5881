/*******************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only
* intended for use with Renesas products. No other uses are authorized. This
* software is owned by Renesas Electronics Corporation and is protected under
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND RENESAS MAKES NO WARRANTIES REGARDING
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT
* LIMITED TO WAR<PERSON>NTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE
* AND NON-INFRINGEMENT. ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software
* and to discontinue the availability of this software. By using this software,
* you agree to the additional terms and conditions found by accessing the
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2021 Renesas Electronics Corporation. All rights reserved.
***********************************************************************************************************************/

/***********************************************************************************************************************
* System Name  : RZ/T2M Motor Solution Kit
* File Name    : r_cg_systeminit.c
* Version      : 1.10
* Device       : RZ/T2M
* Tool-Chain   : IAR Embedded Workbench for ARM
*                Renesas e2studio
* OS           : Not use
* Description  : Source file of system initializing.
***********************************************************************************************************************/

/***********************************************************************************************************************
Pragma directive
***********************************************************************************************************************/
/* Start user code for pragma. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
Includes
***********************************************************************************************************************/
#include "hal_data.h"
#include "bsp.h"

/***********************************************************************************************************************
Global variables and functions
***********************************************************************************************************************/
/* Start user code for global. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */

/***********************************************************************************************************************
* Function Name: user bsp_init
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void user_bsp_init(void)
{
    __disable_interrupt();
    __asm volatile ("isb");
    
 
    /* Enable Relay to connect for 0.5s */
    R_BSP_SoftwareDelay(100, BSP_DELAY_UNITS_MILLISECONDS);
    R_PORT_NSR->P_b[0x12].POUT_2 = 0U;  
    R_BSP_SoftwareDelay(500, BSP_DELAY_UNITS_MILLISECONDS);    
    
    /* Set peripheral settings */
    bsp_sci0_init_ss(115200,0);
    bsp_usb_init();

    
    bsp_timerCounter_init();
    

    __enable_interrupt();
    __asm volatile ("isb");
}



/* 仅仅用于fsp生成中断宏定义 */
void null(void)
{

}

/***********************************************************************************************************************
* Function Name: user xspi_qspi
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_qspi_init(void)
{
 //  R_XSPI_QSPI_Open(&g_qspi_flash_ctrl,&g_qspi_flash_cfg);
}


/***********************************************************************************************************************
* Function Name: user xspi_qspi
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
uint8_t bsp_detectusb(void)
{
    return R_USBF->SYSCFG0_b.CNEN;
}

/* Start user code for adding. Do not edit comment generated here */
/* End user code. Do not edit comment generated here */
