/*
 * bsp_ecat.h
 *
 *  Created on: Aug 1, 2023
 *      Author: xgj12
 */

#ifndef BSP_BSP_ECAT_H_
#define BSP_BSP_ECAT_H_
#include <stdint.h>


extern void bsp_ecat_init(void);
extern uint16_t ecat_startAutoNegotiate(void);
extern void ecat_read_eeprom(void);
uint16_t XMC_ECAT_ReadPhy(uint16_t phyAddr, uint16_t RegAType, uint16_t regAddr, uint16_t * regVal);
uint16_t XMC_ECAT_WritePhy(uint16_t phyAddr,uint16_t RegAType, uint16_t regAddr, uint16_t regVal);
uint16_t ecatForceLink(void);
uint16_t EcatWritePhyLedCtrlReg(void);
uint32_t EcatErrRegRead(uint8_t regtype, uint8_t port);

void User_Phy_Reset (uint8_t ForceReset);
#endif /* BSP_BSP_ECAT_H_ */
