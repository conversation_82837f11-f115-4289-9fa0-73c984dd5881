/*
 * bsp_eeprom.h
 *
 *  Created on: Oct 25, 2023
 *      Author: xgj12
 */

#ifndef BSP_TIMER_H_
#define BSP_TIMER_H_

#define COUNTER_PPR_US   (float)(50.0/8)
#define COUNTER_PPR_MS   (COUNTER_PPR_US*1000)


void bsp_timerCounter_init(void);
uint32_t bsp_gettimercnt(void);



void bsp_dc_mtu8_init(void);
uint32_t bsp_get_mtu8_cmpcnt(void);
uint32_t bsp_get_mtu8_cnt(void);
#endif /* BSP_TIMER_H_ */
