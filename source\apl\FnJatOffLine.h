/****************************************************************************************************
 *
 * FILE NAME:  FnJatOffLine.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.17
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	17-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FN_JAT_OFFLINE_H_
#define _FN_JAT_OFFLINE_H_
	
#include "Basedef.h"
#include "JatOffLine.h"
#include "RegAccessIf.h"

/****************************************************************************************************/
/*																									*/
/*	Constant definition										            							*/
/*																									*/
/****************************************************************************************************/
#define	JRATCALCYCUS	(TASKC_CYCLEUS)				// Inertia calculation cycle[us]
#define	JRATVERRFIL		1000						// Speed deviation filter frequency for identification error[0.1Hz]
#define	JATFIL			100000						// Inertia update filter time constant: 100000[us]
#define	JATVFFGN		100							// Inertia identification Medium speed FF gain: 100[%]

/****************************************************************************************************/
/*																									*/
/*		Function Prototype Definition																*/
/*																									*/
/****************************************************************************************************/
PUBLIC INT16 CpiInitJatOffLine( JATHNDL *JatHdl, PRMDATA *PnPrm, BPRMDAT *Bprm, REG_MANAGER_HANDLE *RegManager, 
					      GAIN_CHNG_HNDL GainChange, INT16 InitSw, INT32 distance, UINT8 CtrlMode, INT32 SvCycleNs);
PUBLIC void CpiEndJatOffLine( JATHNDL *JatHdl, REG_ACC_T *AccessPrm );
PUBLIC INT8 CpiGetJatExeErrStatus( JATHNDL	*JatHdl );	
PUBLIC INT8 CpiGetJatErrStatus( JATHNDL *JatHdl );		
PUBLIC INT8 CpiGetJatCompStatus( JATHNDL *JatHdl );		
PUBLIC void CpiSetEstimateJrat( JATHNDL *JatHdl, REG_ACC_T *AccessPrm );	
	
#endif

