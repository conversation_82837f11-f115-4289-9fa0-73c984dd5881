/****************************************************************************************************
 *
 * FILE NAME:  RegAccessIF.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.13
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	13-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _REG_ACCESS_IF_H_
#define	_REG_ACCESS_IF_H_

#include "BaseDef.h"
#include "EepromIF.h"
#include "Alarm.h"
#include "PnPrmStruct.h"

/*--------------------------------------------------------------------------------------------------*/
/*	  defines of Parameter Attribute and table list												    */
/*--------------------------------------------------------------------------------------------------*/
#define PRMDEF_BASENBL			0				 // nibble representation	
#define PRMDEF_BASEHEX			1			     // hexadecimal representation	
#define PRMDEF_BASEDEC			2			     // decimal representation
#define PRMDEF_BASEBIT			3		         // bit representation

#define ACCLVL_USER1			1	
#define ACCLVL_USER2			2			
#define ACCLVL_USER3			3									
#define ACCLVL_SYSTEM			4	

#define WRITE_OK_SET_VAL		0x0000				// Write permission setting value					
#define WRITE_NG_SET_VAL		0x0001				// Write protect setting value		
#define ACCLVL_USER1_SET_VAL	0x1002				// User 1 setting value				
#define ACCLVL_USER2_SET_VAL	0x1501				// User 2 setting value			
#define ACCLVL_USER3_SET_VAL	0x2001				// User 3 setting value			
#define ACCLVL_SYSTEM_SET_VAL	0x4907				// System level setting



/*--------------------------------------------------------------------------------------------------*/
/*	  Variant union define										                    	         	*/
/*--------------------------------------------------------------------------------------------------*/
typedef union {
	UINT32	ulVal;
	INT32	lVal;
	UINT16	usArray[2];
	INT16	sArray[2];
	UINT8	ucArray[4];
	INT8	cArray[4];

	UINT16	usVal;
	INT16	sVal;
	UINT8	ucVal;
	INT8	cVal;
} VARIANT;

/*--------------------------------------------------------------------------------------------------*/
/*	 eeprom device id defines						        									    */
/*--------------------------------------------------------------------------------------------------*/
enum EEP_DEVICE_ID {
	PRMDEF_EEPROM_IIC = 0,
	PRMDEF_NO_EEPROM,
};

/*--------------------------------------------------------------------------------------------------*/
/*	 Parameter result defines						        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef enum 
{
	PRM_RSLT_SUCCESS		= (INT32)0x00000000L,	/* End normal */
	PRM_RSLT_CONTINUE		= (INT32)0x00000001L,	/* Normal termination, continued (for Fn) */
	PRM_RSLT_NEEDREBOOT		= (INT32)0x00000003L,	/* Need reboot*/
	PRM_RSLT_RAMONLY		= (INT32)0x00000004L,	/* RAM only  */
	PRM_RSLT_NO_DIFF		= (INT32)0x00000005L,	/* EEPROM and ram match */
    PRM_RSLT_NEEDRESET	    = (INT32)0x00000006L,	/* Need reset */
	PRM_RSLT_PRMNO_ERR		= (INT32)0x80110002L,	/* Pn number error*/
	PRM_RSLT_LIMIT_ERR		= (INT32)0x80120006L,	/* Error range*/
	PRM_RSLT_SIZE_ERR		= (INT32)0x80150004L,	/* Error size*/
	PRM_RSLT_CALC_ERR		= (INT32)0x80130006L,	/* Calculation error*/
	PRM_RSLT_RWACC_ERR		= (INT32)0x80140005L,	/* Access error*/
	PRM_RSLT_CMD_ERR		= (INT32)0x80100001L,	/* CMD error*/
	PRM_RSLT_CONDITION_ERR	= (INT32)0x80300008L,	/* Condition error	*/
	PRM_RSLT_NOACCRIGHT_ERR	= (INT32)0x80300009L,	/* No access right error*/
} PRM_RSLT;

/*--------------------------------------------------------------------------------------------------*/
/*	 Parameter access command defines						        							    */
/*--------------------------------------------------------------------------------------------------*/
typedef enum 
{
	PRM_ACCCMD_READ = 0,
	PRM_ACCCMD_WRITE,
	PRM_ACCCMD_EEPWRITE,
	PRM_ACCCMD_RECALC,
} PRM_ACCCMD;

typedef PRM_RSLT (*PRMCAL_CALLBACK)(PRM_ACCCMD, UINT32, void*, INT32*);
typedef void *(*PRMPTR_CALLBACK)(UINT16, void*);

/*--------------------------------------------------------------------------------------------------*/
/*	 Parameter attribute struct						        						     	        */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	UINT16			Number;			    /* Parameter number */
	
	UINT16			AccessLevel		:3;	/* 1:User1, 2:User2, 3:System*/
	UINT16			AxisCommon		:1;	/* common parameter for all axises */
	UINT16			NeedReboot		:1;	/* new setting value is not activated until re-boot */
	UINT16			Sign			:1;	/* signed or unsigned */
	UINT16			ReadOnly		:1;	/* read only */
	UINT16			FormatType		:2;	/* HEX / DEC / NBL/ BIT */
	UINT16			EepromDevice	:3;	/* Eeprom device */
	UINT16			DataLength		:4;	/* data size in bytes */
	
/*------------------------------------------------------------------------------------------*/
	UINT16			NumberOfArray;		/* number of array(reserved) */
	UINT16			EepromAddress;		/* EEPROM address */

	INT32			LowerLimit;			/* lower limit */
	INT32			UpperLimit;			/* upper limit */
	INT32			DefaultValue;		/* System default value */

	PRMPTR_CALLBACK	GetRamPtrCallback;	/* pointer to the callback function to get the RAM address */
	PRMCAL_CALLBACK	PrmCalCallback;		/* pointer to the callback function for parameter caluculation  */
} PRM_ATTR;


/*--------------------------------------------------------------------------------------------------*/
/*	 Parameter table struct						        						     	            */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
    UINT32              CoeIndex;
	UINT16				Number;
	const PRM_ATTR      *Attribute;
} PRM_TBL;

/*--------------------------------------------------------------------------------------------------*/
/*	 Structure of Configurations for Parameter Manager	    					     	            */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	PRM_TBL				*PrmListTable;		 /* Pointer to the Parameter list table */
	UINT16				NumOfTableEntry;	 /* number of parameters */
	void				*GetRamPtrParam;	 /* Input parameter for the callback function "GetPtrCallback" */
	void				*PrmCalParam;	     /* Input parameter for the callback function "PrmCalCallback" */
	UINT16				EepromBlockAddress;	 /* EEPROM block address */
	UINT16				SysLevel;			 /* System level switch */
	void				*WriteAccessOwner;	 /* Parameter write access owner */
	BOOL				WriteAccessLock;	 /* Parameter write access lock */
	PRM_RSLT			RegWrResult;		 /* for Executing status of Ope register */
	ALARM  		        *AlmMngr;            /* alarm manager pointer*/
} REGIF_CONFIG_T;

typedef REGIF_CONFIG_T* PREGIF_CONFIG_T;

/*--------------------------------------------------------------------------------------------------*/
/*	 Structure of Parameter Access	    					     	                                */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT32			 MemAddr;	/* Memory Address */
	PREGIF_CONFIG_T  hRegCfg;	/* Pointer to Register Configuration structure  */
	PRM_ATTR    	 *PrmAttr;	/* Pointer to Parameter Attribute */
	UINT16			 ArrayIdx;	/* Array Number */
	BOOL			 EepWrite;	/* EEPROM Write info */
	void			 *IfHandle;	/* Interface handle ID */
} REG_ACC_T;

/*--------------------------------------------------------------------------------------------------*/
/*	  Structure of Register Manager Handle															*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	REGIF_CONFIG_T *hPnReg;			/* Paramter Register */
	REGIF_CONFIG_T *hFnReg;			/* Operation Register */
	REGIF_CONFIG_T *hUnReg;			/* Monitor Register */
	REGIF_CONFIG_T *hInfReg;		/* Information Register */
	REGIF_CONFIG_T *hTrcReg;		/* Trace buffer Register */
	REGIF_CONFIG_T *hNetworkIdReg;	/* Network ID Register */
} REG_MANAGER_HANDLE;

/*--------------------------------------------------------------------------------------------------*/
/*	Device Parameter  struct						        						     	            */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16	DevType;		
	UINT16	DevVolt;		
	UINT16  DevRatW;              
	UINT16  DevMaxW;              
	UINT16  DevRatCur;            
	UINT16  DevMaxCur;     
    REAL32  MaxSampleCur;
    UINT16  DeadTime;
} DEV_PRM_ATTR;

/*--------------------------------------------------------------------------------------------------*/
/*	 Device Parameter Table struct						        						     	            */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
    UINT16                  CoeIndex;
	UINT16				    Number;
	const DEV_PRM_ATTR      *Attribute;
} DEV_PRM_TBL;

/*--------------------------------------------------------------------------------------------------*/
/*	 Joint Parameter Table struct						        						     	            */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{	
	int32_t *addr;
    int32_t value;
}JointPrm;

typedef struct
{	
	JointPrm RatPow;
    JointPrm RatCur;
    JointPrm RatTrq;
    JointPrm RatSpd;
    JointPrm MaxCur;
    JointPrm MaxTrq;
    JointPrm MaxSpd;
    JointPrm MotPolePairs;
    JointPrm MotR;
    JointPrm Jmot;
    JointPrm MotEmf;
    JointPrm MotKt;
	JointPrm EncType;
	JointPrm AbsEncBitS;	
    JointPrm MotLd;
    JointPrm MotLq;
    JointPrm UseEncoder2;
    JointPrm PosLoopHz;
    JointPrm SpdLoopHz;
    JointPrm SpdLoopTi;
    JointPrm Jrat;
	JointPrm DevID;
	JointPrm OverVoltValue;
    JointPrm MotorRevolutions0x6091;
    JointPrm LoadShaftRevolutions0x6091;
}Joint_Prm_Attr;


PUBLIC void PrmLoadSystemDefaultValues(REGIF_CONFIG_T * PrmCfg);
PUBLIC void PrmLoadMotorDefaultValues(REGIF_CONFIG_T* RegCfg);
PUBLIC void PrmStoreSystemDefaultValues(REGIF_CONFIG_T  *PrmCfg, BOOL bAxCommon);	
PUBLIC PRM_RSLT PrmReLoadValueFromEeprom(REGIF_CONFIG_T  *RegCfg, 
															PRM_ATTR *PrmAttr, UINT16 ArrayIdx);
PUBLIC INT32 PrmLoadEepromValues(REGIF_CONFIG_T *PrmCfg, UINT16 devID, BOOL bAxCommon);
PUBLIC INT32 PrmLoadMotorEepromValues(REGIF_CONFIG_T *PrmCfg, UINT16 devID, BOOL bAxCommon);
PUBLIC PRM_RSLT PrmRecalcAllParameters(REGIF_CONFIG_T *PrmCfg, BOOL bAxCommon);
PUBLIC PRM_ATTR*   PrmFindTableIndex(REGIF_CONFIG_T *PrmCfg, UINT16 PrmNo, UINT16 *ArrayIdx);
PUBLIC PRM_RSLT PrmReadValue(REG_ACC_T *AccessPrm, INT32 *value, UINT16 bSize);
PUBLIC PRM_RSLT PrmWriteValue(REG_ACC_T *AccessPrm, INT32 *value, UINT16 bSize);
PUBLIC PRM_RSLT PrmWriteValueToEeprom(REGIF_CONFIG_T  *PrmCfg, 
													PRM_ATTR *PrmAttr, UINT16 ArrayIdx);
PUBLIC PRM_RSLT PrmStoreValueToEeprom(REGIF_CONFIG_T  *PrmCfg, 
													PRM_ATTR *PrmAttr, UINT16 ArrayIdx);
PUBLIC PRM_RSLT PrmStoreMotEcnPrmToEeprom(REGIF_CONFIG_T *RegCfg);
PUBLIC PRM_RSLT PrmStoreAllValuesToEeprom(REGIF_CONFIG_T *PrmCfg);
PUBLIC PRM_RSLT PrmStoreAllValuesToEepromPro(REGIF_CONFIG_T *PrmCfg);
PUBLIC PRM_ATTR*   PrmGetAttrByCoeIndex(REGIF_CONFIG_T *RegCfg, UINT32 Index);
PUBLIC PRM_ATTR *RegMngrGetAttrByMemAddr(REG_ACC_T *AccessPrm);
PUBLIC void RegMngrLockWriteOpeRegister(REG_MANAGER_HANDLE *hRegMngr);
PUBLIC void RegMngrFreeWriteOpeRegister(REG_MANAGER_HANDLE *hRegMngr);
PUBLIC PRM_RSLT RegMngrGetWriteOpeRegStatus(REG_MANAGER_HANDLE *hRegMngr);
PUBLIC void RegMngrSetWriteOpeRegStatus(REG_MANAGER_HANDLE *hRegMngr, PRM_RSLT sts);
PUBLIC void RegMngrLockWriteParameters(REG_MANAGER_HANDLE *hRegMngr);
PUBLIC void RegMngrFreeWriteParameters(REG_MANAGER_HANDLE *hRegMngr);
PUBLIC BOOL RegMngrGetWritePrmAccessLock(REG_MANAGER_HANDLE *hRegMngr);

PUBLIC void  PrmLoadDeviceDefaultValues(BASE_CFG_PRM *PnCfgPrm,UINT16 AxisCobID, UINT16 HardwareVer);
PUBLIC INT32 WriteDevInfoEeprom(UINT16 *Value);
PUBLIC INT32 ReadDevInfoEeprom(UINT16 *Value);

PUBLIC void PrmLoadDefaultValues(void);
#endif // _REG_ACCESS_IF_H_

