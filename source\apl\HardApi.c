/******************************************************************************
* Includes <System Includes> , "Project Includes"
******************************************************************************/
#include "BaseSetting.h"
#include "HardApi.h"
#include "Global.h"
#include "PrmTable.h"
#include "bsp.h"


uint16_t MCU_DEVICE_ID = 0;

PRIVATE INT32	StartUpTimer;
PRIVATE UINT32	Timer1000msActiveCnt;
PRIVATE UINT32	Timer1000msCounter;
PRIVATE UINT32	Timer1msCounter;
PRIVATE UINT32	SaveTimeCounter;
PRIVATE UINT32	PowerOnTimer;
PRIVATE INT32	EepDisableTimer;
PRIVATE UINT16  PwmEnCtrl=0;

#define VDC_GAIN    (0.021093f)   // 86.4/4096  clibration 
PRIVATE UINT16 Axis1_Iu_Offset = 32768U;
PRIVATE UINT16 Axis1_Iv_Offset = 32768U;

//PRIVATE UINT16 Vdc_Offset = 2048;
PRIVATE UINT16 Vdc_Offset = 0u;  //V

PWM_Control_WORD    gPWMControl[MAX_AXIS_NUM];
PWM_CTRL_SETTING    gPWMSetting[MAX_AXIS_NUM] = {0};
extern AXIS_HANDLE SysAxisHandle[MAX_AXIS_NUM]; // todo : for debug



PRIVATE UINT32 TempTable[29]=
{
    3850 ,3414 ,2998 ,2613 ,2265 ,1953 ,1678 ,1438 ,1231 ,1053 ,
    900 ,771 ,660 ,566 ,487 ,419 ,362 ,314 ,273 ,238 ,
    208 ,182 ,160 ,141 ,124 ,110 ,98 ,87 ,77,

};

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_SystemReset(void)
{
    volatile UINT32 dummy; 
  
    
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    
    R_BSP_SystemReset();

}
/****************************************************************************************************
 * DESCRIPTION: Initialize time stamp timer.
 *             Can be used to calculate the execution time of a piece of code
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_TimerCounterInit(void)
{
    AXIS_HANDLE           *AxisA;    
    AxisA = GetAxisHandle(0);
    
    bsp_timerCounter_init();

    Timer1000msCounter = AxisA[0].Prm->PnAlmCfgPrm.TimeStamp;
    PowerOnTimer = 0;
    StartUpTimer = 0;
    Timer1000msActiveCnt = 0;
    EepDisableTimer = KLIB_EEPDISABLE_TIME;
}

/****************************************************************************************************
 * DESCRIPTION: Read time stamp value
 *
 * RETURNS: Return current time stam value
 *
****************************************************************************************************/
PUBLIC UINT32 hApi_GetTimerCounter(void)
{
    return bsp_gettimercnt();
}
/****************************************************************************************************
 * DESCRIPTION: Count up the Time Stamp value(Called from cyclic scan taskC)
 *
 * RETURNS: 
 *
****************************************************************************************************/
PUBLIC void KlibCountTimeStamp(UINT32 tick_in_msec, BOOL PowerOn )
{

    AXIS_HANDLE     *AxisA;
    
    AxisA = GetAxisHandle(0);

	Timer1msCounter++;

	PowerOnTimer += tick_in_msec;
    if(PowerOnTimer >= 1000)
	{
        Timer1000msCounter++;
        Timer1000msActiveCnt++;
        SaveTimeCounter++;
        if((AxisA[0].Prm->PnAlmCfgPrm.TimeStamp >=  (Timer1000msCounter + 60))
            ||  (Timer1000msCounter >= (AxisA[0].Prm->PnAlmCfgPrm.TimeStamp + 60)))
        {
            Timer1000msCounter = AxisA[0].Prm->PnAlmCfgPrm.TimeStamp;
        }
        AxisA[0].Prm->PnAlmCfgPrm.TimeStamp = Timer1000msCounter;
        
        if(SaveTimeCounter >= 300)
        {
           AxisA[0].AlmMngr->AlmTmStampRst = TRUE;
            SaveTimeCounter = 0;
        }
        PowerOnTimer = 0;
    }
	if(PowerOn != FALSE)		/* Update only when Power On */
	{
		EepDisableTimer = 0;
	}
	else
	{
		if(EepDisableTimer < KLIB_EEPDISABLE_TIME)
		{	EepDisableTimer += tick_in_msec;}
	}

	/* Main timer (Timer from control power-on [ms], Max. 1193H / 49D)*/
	if( StartUpTimer < 0xFFFF0000 )	
	{
		StartUpTimer += tick_in_msec;
	}

}
/****************************************************************************************************
 * DESCRIPTION: Read time stamp value
 *
 * RETURNS: Return current time stam value
 *
****************************************************************************************************/
PUBLIC UINT32 hApi_GetTimeStamp1000ms(void)
{
    return Timer1000msCounter;
}


/****************************************************************************************************
 * DESCRIPTION: Read time stamp value
 *
 * RETURNS: Return current time stam value
 *
****************************************************************************************************/
PUBLIC UINT32 hApi_GetActive1000ms(void)
{
    return Timer1000msActiveCnt;
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS: 
 *
****************************************************************************************************/
PUBLIC void KlibRstLongTimer( UINT32 *timer )
{
	*timer = Timer1msCounter;
}
/*****************************************************************************************************/


/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS: 
 *
****************************************************************************************************/

PUBLIC UINT32 KlibGetLongTimerMs( UINT32 timer )
{
	UINT32	CuCount;	
	UINT32	TimeUsec;	

	CuCount = Timer1msCounter;

	TimeUsec = CuCount - timer;

	return TimeUsec;

}
/****************************************************************************************************
 * DESCRIPTION: Get a power on time after start up 
 *
 * RETURNS: 
 *
****************************************************************************************************/
PUBLIC INT32 KlibGetTimeAfterPowerOn( void )
{
	return StartUpTimer;
}
/****************************************************************************************************
 * DESCRIPTION: Get the power off time state 
 *
 * RETURNS: 
 *
****************************************************************************************************/
PUBLIC INT32 KlibGetMainPowerOffTimer( void )
{
	return EepDisableTimer;
}


/******************************************************************************
* Function Name : hApi_SetPwmCfg
* Description   : Set pwm function
* Arguments     : none
* Return Value  : none
******************************************************************************/
PUBLIC void hApi_SetPwmCfg(UINT16 AxisID, UINT16 Type, UINT16 Value)
{ 
  UINT16 wk = 0;

  switch(Type)
  {
    case PWM_MTU3_DEAD_CFG:
      
      gPWMSetting[AxisID].Mtu3PrdCnt = MTU3_PRD_CNT;
      gPWMSetting[AxisID].Mtu0CmpIntCnt = MTU0_CMP_INT_CNT;
      gPWMSetting[AxisID].Mtu0CmpCapCnt = MTU0_CMP_CAP_CNT;
      gPWMSetting[AxisID].Mtu0PrdCnt = MTU0_PRD_CNT;      
  
      
      wk = (Value*2)*MIN_TIME_FOR_CNT;
      if(wk > PWM_DEAD_TIME_MAX) 
      {
        wk = PWM_DEAD_TIME_MIN;
      }
      else if(wk < PWM_DEAD_TIME_MIN) 
      {
        wk = PWM_DEAD_TIME_MIN;
      }       
      gPWMSetting[AxisID].Mtu3DeadCnt = wk;
      break;
    case PWM_MTU3_PRD_CFG:
      gPWMSetting[AxisID].Mtu3PrdCnt = (1000.0f/(REAL32)Value*PWM_CLOCK)/2;   // Value[kHz]
      break;
    case PWM_MTU0_CMP_INT_CFG:
      gPWMSetting[AxisID].Mtu0CmpIntCnt = 1000.0f/(REAL32)Value*PWM_CLOCK;   // Value[kHz];
      break;
    case PWM_MTU0_CMP_CAP_CFG:
      gPWMSetting[AxisID].Mtu0CmpCapCnt = 1000.0f/(REAL32)Value*PWM_CLOCK;   // Value[kHz];
      break;
    case PWM_MTU0_PRD_CFG:
      gPWMSetting[AxisID].Mtu0PrdCnt = 1000.0f/(REAL32)Value*PWM_CLOCK;   // Value[kHz];
      break;
      break;
    case PWM_TEST_DUTY_CFG:
      gPWMSetting[AxisID].PwmTestDuty = (REAL32)Value/100.0*PWM_PERIOD_VALUE;
      break;
    case PWM_PHASE_CHECK_DUTY_CFG:
      gPWMSetting[AxisID].PhaCheckDuty = (REAL32)Value/100.0*PWM_PERIOD_VALUE;
      break;
    case PWM_CHARGE_PUMP_ON_DUTY_CFG:
      gPWMSetting[AxisID].ChargePumpOnDuty = (REAL32)Value/100.0*PWM_PERIOD_VALUE;
      break;
    case PWM_ADC_OFFSET_CHECK_DUTY_CFG:
      gPWMSetting[AxisID].AdcOffsetCheckDuty = (REAL32)Value/100.0*PWM_PERIOD_VALUE;
      break;
  }
}
/******************************************************************************
* Function Name : hApi_GetPwmCfg
* Description   : Get pwm function
* Arguments     : none
* Return Value  : none
******************************************************************************/
UINT16 hApi_GetPwmCfg(UINT16 AxisID, UINT16 Type)
{ 
  return gPWMSetting[AxisID].Data[Type];
}
/******************************************************************************
* Function Name : hApi_PwmInit
* Description   : Initialize pwm function
* Arguments     : none
* Return Value  : none
******************************************************************************/
PUBLIC void hApi_PwmInit(UINT16 AxisID)
{ 

  bsp_pwm_init(gPWMSetting[AxisID].Mtu0PrdCnt,
               gPWMSetting[AxisID].Mtu0CmpIntCnt,
               gPWMSetting[AxisID].Mtu0CmpCapCnt,
               gPWMSetting[AxisID].Mtu3PrdCnt,
               gPWMSetting[AxisID].Mtu3DeadCnt);
}


/******************************************************************************
* Function Name : hApi_PwmEnable
* Description   : enable pwm function
* Arguments     : none
* Return Value  : none
******************************************************************************/
PUBLIC void hApi_PwmEnable(UINT16 AxisID)
{
//  if(SysAxisHandle[0].HwIputSignals->bits.IpmA == 1 )
//  {
//    hApi_PwmDisable(0);
//  }
//  else
//  {
    if(AxisID == 0)
    {
      PwmEnCtrl |= 0x01;
      bsp_pwm_output_enable(AxisID, 0x3F);
    }else
    {
      PwmEnCtrl |= 0x10;
      bsp_pwm_output_enable(AxisID, 0x3F);
    }
    
    R_BSP_PinWrite(PORT_PWMEN1,PIN_LEVEL_HIGH);
//  }
}

/******************************************************************************
* Function Name : hApi_PwmEnable
* Description   : enable pwm function
* Arguments     : none
* Return Value  : none
******************************************************************************/
PUBLIC void LP_PwmUpdate(UINT16 AxisID)
{
    if(AxisID == 0)
    {
      PwmEnCtrl |= 0x01;
      bsp_pwm_output_enable(AxisID, 0x20);
    }else
    {
      PwmEnCtrl |= 0x10;
      bsp_pwm_output_enable(AxisID, 0x20);
    }
    
    R_BSP_PinWrite(PORT_PWMEN1,PIN_LEVEL_HIGH);
}
/******************************************************************************
* Function Name : hApi_PwmDisable
* Description   : disable pwm function
* Arguments     : none
* Return Value  : none
******************************************************************************/
PUBLIC void hApi_PwmDisable(UINT16 AxisID)
{
    if(AxisID == 0)
    {
       PwmEnCtrl &= 0xFFF0;
       bsp_pwm_output_disable(AxisID);
    }else
    {
      PwmEnCtrl &= 0xFF0F;
      bsp_pwm_output_disable(AxisID);
    }   
    if(PwmEnCtrl == 0)
        R_BSP_PinWrite(PORT_PWMEN1,PIN_LEVEL_LOW);
}
/******************************************************************************
* Function Name : hApi_PwmUpdate
* Description   : PWM duty setting
* Arguments     : float f4_u, float f4_v, float f4_w, float f4_vdc
* Return Value  : none
******************************************************************************/
PUBLIC void hApi_PwmUpdate(UINT16 AxisID, UINT16 Ta, UINT16 Tb, UINT16 Tc)
{ 
    UINT16 PWMTa=0,PWMTb=0,PWMTc=0;
    UINT16 HalfDeadTime = hApi_GetPwmCfg(AxisID, PWM_MTU3_DEAD_CFG)/2;
    
    if(gPWMControl[AxisID].bit.PhaCheckPumpOn == 1)
    {     
        if(AXIS_1 == AxisID)
        {
          R_MTU3->TGRD = Ta;
          R_MTU4->TGRC = Tb;
          R_MTU4->TGRD = PWM_PERIOD_VALUE;         
        }
        else if(AXIS_2 == AxisID)
        {
          R_MTU6->TGRD = Ta;
          R_MTU7->TGRC = Tb;
          R_MTU7->TGRD = PWM_PERIOD_VALUE;
        }	
        LP_PwmUpdate(AxisID);      
    }
    else if(gPWMControl[AxisID].bit.ChargePumpOn == 1)
    {			
        if(AXIS_1 == AxisID)
        {
          R_MTU3->TGRD = PWM_PERIOD_VALUE/2;
          R_MTU4->TGRC = PWM_PERIOD_VALUE/2;
          R_MTU4->TGRD = PWM_PERIOD_VALUE/2;
        }
        else if(AXIS_2 == AxisID)
        {
          R_MTU6->TGRD = PWM_PERIOD_VALUE/2;
          R_MTU7->TGRC = PWM_PERIOD_VALUE/2;
          R_MTU7->TGRD = PWM_PERIOD_VALUE/2;
        }	
        hApi_PwmEnable(AxisID);
    }
    else if(gPWMControl[AxisID].bit.BaseEnable == 1)
    {
        PWMTa =  Ta + HalfDeadTime;
        PWMTb =  Tb + HalfDeadTime;
        PWMTc =  Tc + HalfDeadTime;
        
        if(PWMTa > PWM_PERIOD_VALUE)
        {
          PWMTa = PWM_PERIOD_VALUE;
        }
        
        if(PWMTb > PWM_PERIOD_VALUE)
        {
          PWMTb = PWM_PERIOD_VALUE;
        }
        
        if(PWMTc > PWM_PERIOD_VALUE)
        {
          PWMTc = PWM_PERIOD_VALUE;
        }
      
      
        if(AXIS_1 == AxisID)
        {
          R_MTU3->TGRD = PWMTa;
          R_MTU4->TGRC = PWMTb;
          R_MTU4->TGRD = PWMTc;
        }
        else if(AXIS_2 == AxisID)
        {
          R_MTU6->TGRD = PWMTa;
          R_MTU7->TGRC = PWMTb;
          R_MTU7->TGRD = PWMTc;
        }	
        hApi_PwmEnable(AxisID);
    }
    else
    {
      
#if (PWM_TESTFLAG == 0)
		if(AXIS_1 == AxisID)
		{
            R_MTU3->TGRD = PWM_PERIOD_VALUE/4;
            R_MTU4->TGRC = PWM_PERIOD_VALUE/4;
            R_MTU4->TGRD = PWM_PERIOD_VALUE/4;
		}
		else if(AXIS_2 == AxisID)
		{
            R_MTU6->TGRD = PWM_PERIOD_VALUE/2;
            R_MTU7->TGRC = PWM_PERIOD_VALUE/2;
            R_MTU7->TGRD = PWM_PERIOD_VALUE/2;
		} 
         hApi_PwmEnable(AxisID);
#else
		if(AXIS_1 == AxisID)
		{
            R_MTU3->TGRD = PWM_PERIOD_VALUE/2;
            R_MTU4->TGRC = PWM_PERIOD_VALUE/2;
            R_MTU4->TGRD = PWM_PERIOD_VALUE/2;
		}
		else if(AXIS_2 == AxisID)
		{
            R_MTU6->TGRD = PWM_PERIOD_VALUE/2;
            R_MTU7->TGRC = PWM_PERIOD_VALUE/2;
            R_MTU7->TGRD = PWM_PERIOD_VALUE/2;
		} 
     hApi_PwmDisable(AxisID);    
#endif        
    }  
}


/******************************************************************************
* Function Name : hApi_PwmUpdate
* Description   : PWM duty setting
* Arguments     : float f4_u, float f4_v, float f4_w, float f4_vdc
* Return Value  : none
******************************************************************************/
PUBLIC void hApi_PwmUpdateTest(UINT16 AxisID, UINT16 Ta)
{ 
    if(AXIS_1 == AxisID)
    {
      R_MTU3->TGRD = Ta;
      R_MTU4->TGRC = Ta;
      R_MTU4->TGRD = Ta;  
    }
    else if(AXIS_2 == AxisID)
    {
      R_MTU6->TGRD = Ta;
      R_MTU7->TGRC = Ta;
      R_MTU7->TGRD = Ta;
    }	
    hApi_PwmEnable(AxisID);   
}


/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_SetTaskBInterrupt(void)
{
    R_ICU_NS->NS_SWINT_b.IC0 = 1;
    
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_SetTaskCInterrupt(void)
{
   R_ICU_NS->NS_SWINT_b.IC1 = 1;
}
/****************************************************************************************************
 * DESCRIPTION: hApi_AdcOffsetCheck(void)
 *	     
 * RETURNS:
 *
****************************************************************************************************/
extern PUBLIC volatile BOOL gSystemInitDone;
PRIVATE BOOL gAdcOffsetCheck = FALSE;
PRIVATE INT16 CurSmpInitErr[2] = {0};

PUBLIC BOOL hApi_AdcOffsetCheck(void)
{
    static UINT32 sum[6]={0};
    UINT16 pwmVal;
    static UINT16 CheckCnt=0;   
    
    if(gAdcOffsetCheck == FALSE)
    { 
        if(CheckCnt < 400)
        {
          hApi_DynamicBrakeOff(AXIS_1);
        }
        else if(CheckCnt < 1600)
        {
            hApi_BaseEnable(AXIS_1);
            pwmVal = PWM_PERIOD_VALUE*0.75;
            hApi_PwmUpdate(AXIS_1, pwmVal, pwmVal, pwmVal);	  
        }
        else if(CheckCnt < 1664)
        {
            sum[0] += bsp_dsmif_read(0,0);
            sum[1] += bsp_dsmif_read(0,1);
             
        }
        else if(CheckCnt < 1700)
        {
            hApi_BaseBlock(AXIS_1);
            pwmVal = PWM_PERIOD_VALUE/4;
            hApi_PwmUpdate(AXIS_1, pwmVal, pwmVal, pwmVal); 
            
            Axis1_Iu_Offset = sum[0]/64;
            Axis1_Iv_Offset = sum[1]/64;

            if(Axis1_Iu_Offset > 34768 || Axis1_Iu_Offset < 30768
            || Axis1_Iv_Offset > 34768 || Axis1_Iv_Offset < 30768)
            {
                CurSmpInitErr[0] = 1;
            }             
        }
        else
        {
            gAdcOffsetCheck = TRUE;
            gSystemInitDone = TRUE;   
            hApi_DynamicBrakeOn(AXIS_1);
        }
        
        CheckCnt++;
    }
   
    return gAdcOffsetCheck;    
}

/****************************************************************************************************
 * DESCRIPTION: 
 *	     
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT16 hApi_GetAdcInitState(UINT16 AxisID)
{

    return (CurSmpInitErr[AxisID]);

}
/******************************************************************************
 * DESCRIPTION:  phase current start
 *	     
 * RETURNS: 
 *
******************************************************************************/
PUBLIC UINT32 hApi_CurAdcStart(void)
{
//    R_S12AD0_Start(); 
  return 0;
}

/******************************************************************************
 * DESCRIPTION:  check phase current end
 *	     
 * RETURNS: 
 *
******************************************************************************/
PUBLIC UINT32 hApi_CheckAdcEnd(void)
{
//    if(S12ADC0.ADCSR.BIT.ADST == 0)
//      return 1;
//    else
      return 0;
}
/******************************************************************************
 * DESCRIPTION: Get phase current
 *	     
 * RETURNS: Return Ia, Ib
 *
******************************************************************************/
UINT16 gAdIu,gAdIv;
PUBLIC void hApi_GetPhaseCurrent(UINT16 AxisID, float *Ia, float *Ib, float *Ic, float CurSplKx)
{ 
    INT16 TmpA = 0,TmpB = 0; 
	
    if(AxisID == AXIS_1)
    {
      gAdIu = bsp_dsmif_read(AxisID,0);
      gAdIv = bsp_dsmif_read(AxisID,1);
      
      TmpA = gAdIu - Axis1_Iu_Offset;
      TmpB = gAdIv - Axis1_Iv_Offset;
      
      *Ia = (REAL32)TmpA * (REAL32)CurSplKx;
      *Ib = (REAL32)TmpB * (REAL32)CurSplKx;
//       *Ia = (REAL32)TmpA *IUVW_GAIN_750;
//       *Ib = (REAL32)TmpB *IUVW_GAIN_750;
    }

    *Ic = -*Ia - *Ib;
}

/****************************************************************************************************
 * DESCRIPTION: voltage adc start
 *	     
 * RETURNS: Return Vbus
 *
****************************************************************************************************/
PUBLIC INT32 hApi_VoltAdcStart(void)
{       
//    R_S12AD0_Start();
  return 0;
}

/****************************************************************************************************
 * DESCRIPTION: voltage adc stop
 *	     
 * RETURNS: Return Vbus
 *
****************************************************************************************************/
//PUBLIC INT32 hApi_VoltAdcStart(void)
//{       
//    R_S12AD0_Start();
//}

/****************************************************************************************************
 * DESCRIPTION: Get bus voltage
 *	     
 * RETURNS: Return Vbus
 *
****************************************************************************************************/

UINT16 gAdVdc;
PUBLIC INT32 hApi_GetDcVoltage(void)
{ 

    REAL32 Val;
    
    bsp_adc0_getvalue(ADCHANNEL2,&gAdVdc);
      
    Val = (REAL32)gAdVdc * (REAL32)VDC_GAIN;

    Val = Val - Vdc_Offset;
    
    if(Val < 0)  Val = 0;
    
    return ((INT32)Val);  
}


/****************************************************************************************************
 * DESCRIPTION: Get bus voltage
 *	     
 * RETURNS: Return Vbus
 *
****************************************************************************************************/
PUBLIC UINT32 hApi_GetVdcFilo(void)
{ 
    static REAL32  VdcSum = 0;       
    static REAL32  VdcFilo = 0;   
    static UINT32  VdcFilTime = 0;
    #define VDC_FILTIME  1000  // 1s
    
    VdcSum += hApi_GetDcVoltage();
    
    VdcFilTime ++;
    
    if(VdcFilTime >= VDC_FILTIME)
    {
      VdcFilo = VdcSum/VDC_FILTIME;
      VdcFilTime = 0;
      VdcSum = 0;
    } 
    return (VdcFilo);  
}
/****************************************************************************************************
 * DESCRIPTION: Get environment Temp adc start
 *	     
 * RETURNS: Return environment Temp
 *
****************************************************************************************************/
PUBLIC INT32 hApi_Adc121Start(void)
{       
    R_S12AD1_Start();

  return 0;
}
/****************************************************************************************************
 * DESCRIPTION: Get environment Temp
 *	     
 * RETURNS: Return environment Temp
 *
****************************************************************************************************/
PUBLIC INT16 hApi_GetEnvTemp(INT16 *IPMTemp)
{ 
    float Temp1 = 0;
    UINT16 i = 0;
    UINT16 AdcValue;
    float AdUnit = 0;
    
    
    bsp_adc1_getvalue(ADCHANNEL3,&AdcValue);

    #if(DEVICE_HARDWARE_VERSION == 0x0003 || DEVICE_HARDWARE_VERSION == 0x0004)
    AdUnit = (float)AdcValue/4096.0f;
    
    if(AdcValue <= 1422) 
    {
        Temp1 = 125;
    }
    else if(AdcValue <= 2500)
    {
        Temp1 = 75.399*AdUnit*AdUnit - 232.52*AdUnit + 196.41;
    }
    else if(AdcValue <= 3500)
    {
        Temp1 = -147.17*AdUnit*AdUnit + 44.267*AdUnit + 109.8;
    }
    else if(AdcValue <=3900)
    {
        Temp1 = -1356.9*AdUnit*AdUnit + 2131.5*AdUnit- 791.27;
    }
    else if(AdcValue <= 4000)
    {
        Temp1 = -8364.2*AdUnit*AdUnit +15447*AdUnit- 7117.5;
    }
    else if(AdcValue <= 4050)
    {
        Temp1 = -33206*AdUnit*AdUnit + 64003*AdUnit - 30845;
    }
    else
    {
        Temp1 = -25;
    }
    #else
    if(AdcValue <= TempTable[28])
    {
            Temp1 = 125;
    }
    else if(AdcValue >= TempTable[0])
    {
            Temp1 = -15;
    }
    else
    {
        for(i=0;i<=27;i++)
        {
            if(AdcValue>=TempTable[i+1] && AdcValue<TempTable[i])
            {
                Temp1 = (float)(i+1)-((float)((float)TempTable[i+1]- (float)AdcValue)/(float)((float)TempTable[i+1]-(float)TempTable[i]));
                Temp1 = (Temp1 - 4)*5.0f;
                break;
            }
                 
        }	
    }    
    #endif
    *IPMTemp = (INT16)Temp1; 

    return (INT16)Temp1;
}


/****************************************************************************************************
 * DESCRIPTION: Get motor Temp
 *	     
 * RETURNS: Return motor Temp
 *
****************************************************************************************************/
PUBLIC INT16 hApi_GetMotTemp(INT16 *MotTemp)
{ 
    float Temp1 = 0;
    UINT16 i = 0;
    UINT16 AdcValue;
    float AdUnit = 0;
    
    bsp_adc1_getvalue(ADCHANNEL4,&AdcValue);
    
    #if(DEVICE_HARDWARE_VERSION == 0x0003 || DEVICE_HARDWARE_VERSION == 0x0004)
    AdUnit = (float)AdcValue/4096.0;
    
    if(AdcValue <= 1422) 
    {
        Temp1 = 125;
    }
    else if(AdcValue <= 2500)
    {
        Temp1 = 75.399*AdUnit*AdUnit - 232.52*AdUnit + 196.41;
    }
    else if(AdcValue <= 3500)
    {
        Temp1 = -147.17*AdUnit*AdUnit + 44.267*AdUnit + 109.8;
    }
    else if(AdcValue <=3900)
    {
        Temp1 = -1356.9*AdUnit*AdUnit + 2131.5*AdUnit- 791.27;
    }
    else if(AdcValue <= 4000)
    {
        Temp1 = -8364.2*AdUnit*AdUnit +15447*AdUnit- 7117.5;
    }
    else if(AdcValue <= 4050)
    {
        Temp1 = -33206*AdUnit*AdUnit + 64003*AdUnit - 30845;
    }
    else
    {
        Temp1 = -25;
    }
    #else
    if(AdcValue <= TempTable[28])
    {
            Temp1 = 125;
    }
    else if(AdcValue >= TempTable[0])
    {
            Temp1 = -15;
    }
    else
    {
        for(i=0;i<=27;i++)
        {
            if(AdcValue>=TempTable[i+1] && AdcValue<TempTable[i])
            {
                Temp1 = (float)(i+1)-((float)((float)TempTable[i+1]- (float)AdcValue)/(float)((float)TempTable[i+1]-(float)TempTable[i]));
                Temp1 = (Temp1 - 4)*5.0f;
                break;
            }
                
        }	
    }
    #endif
    
    *MotTemp = (INT16)Temp1;  

    return (INT16)Temp1;
}
/****************************************************************************************************
 * DESCRIPTION: Get environment Temp
 *	     
 * RETURNS: Return environment Temp
 *
****************************************************************************************************/
PUBLIC void hApi_GetAnalogInput1(UINT16 *AnalogInput)
{
    // +-11.9V(0~4096)  user can use +-10V
    UINT16 AnalogTemp;

    bsp_adc1_getvalue(ADCHANNEL4,&AnalogTemp);

    
    *AnalogInput = AnalogTemp; 
   
}

PUBLIC void hApi_GetAnalogInput2(UINT16 *AnalogInput)
{
    // +-11.9V(0~4096)  user can use +-10V
    UINT16 AnalogTemp;

    bsp_adc1_getvalue(ADCHANNEL5,&AnalogTemp);
   
    *AnalogInput = AnalogTemp;   
}



PUBLIC BOOL hApi_AnalogInputOffsetCheck(UINT16 *AnalogOffset1,UINT16 *AnalogOffset2)
{
    static UINT32 Analogsum[2]={0};
    static UINT16 AnalogCheckCnt=0;   
    UINT16 Temp1,Temp2;
    

    if(AnalogCheckCnt < 64)
    {
        hApi_GetAnalogInput1(&Temp1);
        Analogsum[0]+=Temp1;
        hApi_GetAnalogInput2(&Temp2);
        Analogsum[1]+=Temp2;    
    }
    else 
    {       
        *AnalogOffset1 = Analogsum[0]/64;
        *AnalogOffset2 = Analogsum[1]/64;     
        
        AnalogCheckCnt = 0;
        Analogsum[0] = 0;
        Analogsum[1] = 0;
        
        return TRUE;    
    }

    
    AnalogCheckCnt++;
    
    return FALSE;   
}
/****************************************************************************************************
 * DESCRIPTION: Get OTM
 *	     
 * RETURNS: Return environment Temp
 *
****************************************************************************************************/
PUBLIC BOOL hApi_GetOTM(UINT16 AxisID)
{ 
    INT16 Temp1 = 0;
    UINT16 AdcValue;
    BOOL ret = 0; 
    
    if(AxisID == 0)
    { 
      bsp_adc0_getvalue(ADCHANNEL3,&AdcValue);
    }
    else if(AxisID == 1)
    {
      bsp_adc1_getvalue(ADCHANNEL2,&AdcValue);
    }
      
    if(AdcValue < 1024)
    {
      ret =  1;
    }    
    
    return ret;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_ChargePumpOn(UINT16 AxisID)
{
	gPWMControl[AxisID].bit.ChargePumpOn = 1;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_ChargePumpOff(UINT16 AxisID)
{
	gPWMControl[AxisID].bit.ChargePumpOn = 0;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_BaseEnable(UINT16 AxisID)
{
    gPWMControl[AxisID].bit.BaseEnable = 1;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_BaseBlock(UINT16 AxisID)
{
    gPWMControl[AxisID].bit.BaseEnable = 0;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_RelayControlOn(UINT16 AxisID)
{
  R_BSP_PinWrite(PORT_RELAY,PIN_LEVEL_LOW);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_RelayControlOff(UINT16 AxisID)
{
  R_BSP_PinWrite(PORT_RELAY,PIN_LEVEL_HIGH);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_DynamicBrakeOn(UINT16 AxisID)
{
//  if(AxisID == 0)
//  {
//    R_BSP_PinWrite(PORT_DB1,PIN_LEVEL_HIGH);
//  }
//  else if(AxisID == 1)
//  {
//    R_BSP_PinWrite(PORT_DB2,PIN_LEVEL_HIGH);
//  }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_DynamicBrakeOff(UINT16 AxisID)
{
//  if(AxisID == 0)
//  {
//    R_BSP_PinWrite(PORT_DB1,PIN_LEVEL_LOW);
//  }
//  else if(AxisID == 1)
//  {
//    R_BSP_PinWrite(PORT_DB2,PIN_LEVEL_LOW);
//  }
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_RegeneCtrlOn(void)
{
    R_BSP_PinWrite(PROT_BKCTRL,PIN_LEVEL_HIGH);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_RegeneCtrlOff(void)
{
    R_BSP_PinWrite(PROT_BKCTRL,PIN_LEVEL_LOW);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL hApi_GetRegeneCtrlStatue(void)
{
    UINT32 result = R_BSP_PinRead(PROT_BKCTRL);
    return (BOOL)result;
}

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/

PUBLIC void hApi_BrakeCtrlBrake(UINT16 AxisID)
{
    if(AxisID == AXIS_1)
    {
         //
    }
    else if(AxisID == AXIS_2)
    {
         //
    }        
}
/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_BrakeCtrlRelease(UINT16 AxisID)
{
    if(AxisID == AXIS_1)
    {
//        gDoOutReg.BrkOut = TRUE;
    }
    else if(AxisID == AXIS_2)
    {
//        gDoOutReg.BrkOut = TRUE;
    }
}

/****************************************************************************************************
 * DESCRIPTION:perid cycle 100ms    duty 0-100%     and call by in 1ms interrupt
 *        
 * RETURNS: 
 *
****************************************************************************************************/
#define FANCTRL_CYCLE 100
PUBLIC void hApi_FanCtrl(BOOL BaseEnable, UINT16 Duty)
{
    static UINT16 timecnt = 0;
    UINT16 OnTime = Duty*0.01*FANCTRL_CYCLE;
      
    if(BaseEnable == TRUE)
    {
      timecnt++;
      if(timecnt < OnTime)
      {
        R_BSP_PinWrite(PROT_FANCT,PIN_LEVEL_LOW);
      }
      else 
      {
        R_BSP_PinWrite(PROT_FANCT,PIN_LEVEL_HIGH);
        if(timecnt == FANCTRL_CYCLE)
        {
          timecnt = 0;
        }
      }
    }
    else
    {
      R_BSP_PinWrite(PROT_FANCT,PIN_LEVEL_HIGH);
    }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
#pragma optimize=none 
HW_STATUS_REG   gHwSingalMon ;
PUBLIC UINT16 hApi_GetHwSignal(void)   
{

    HW_STATUS_REG   HwSingal = {0};
    
    HwSingal.bits.IpmA = R_POE3->ICSR1_b.POE0F;
    
    HwSingal.bits.IpmB = R_POE3->ICSR2_b.POE4F;

    HwSingal.bits.STO1 = !R_BSP_PinRead(PORT_STO1);
    HwSingal.bits.STO2 = !R_BSP_PinRead(PORT_STO2);
    
    BOOL Acon = R_BSP_PinRead(PORT_ACLOSS);   // dc : low  acin : Plus  acoff: Hihg
    HwSingal.bits.Acl  = !(Acon);             // invert 
    
    HwSingal.bits.RegenFdb =  R_BSP_PinRead(PROT_BKFB);
    
      
    gHwSingalMon.all = HwSingal.all;

    return HwSingal.all;

}

/****************************************************************************************************
 * DESCRIPTION:
 *              
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT16 hApi_GetBusOcSignal(void)   
{
    return R_POE3->ICSR3_b.POE8F;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_delay_us(UINT32 nus)
{
  UINT32 temp =  hApi_GetTimerCounter();
  UINT32 nscnt = (UINT32)(nus*CMTW1_PPR_US);
  
  while((hApi_GetTimerCounter() - temp) < nscnt);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_delay_ms(UINT32 mus)
{
    hApi_delay_us(mus*1000);
}


/****************************************************************************************************
 * DESCRIPTION: 
 *	     
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_IncEncoderInit(UINT16 Mode, UINT16 AxisID)
{  

    bsp_mtu2_init(Mode,0x10000);
       
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT32 hApi_GetIncEncoderPulse(UINT16 AxisID)
{
    UINT32 res = 0;
    

      res = bsp_opmtu2_cnt(0);
    
    
    return res;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_ClearIncEncoderPulse(UINT16 AxisID)
{

      bsp_opmtu2_cnt(1);

}


/****************************************************************************************************
 * DESCRIPTION: 
 *	     
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_CtrlPulseInit(UINT16 Mode, UINT16 AxisID)
{  

    bsp_mtu1_init(Mode,0x10000);
       
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT32 hApi_GetCtrlPulse(void)
{
    UINT32 res = 0;
    

    res = bsp_opmtu1_cnt(0);

    
    return res;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_ClearCtrlPulse(void)
{
   bsp_opmtu1_cnt(1);

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT16 hApi_UpdataDI(UINT16 AxisID)   
{
    DI_STATUS_REG   DiSingal = {0};
    
    if(AxisID == 0)
    {
      DiSingal.bits.DI1 =  !(R_BSP_PinRead(PORT_DI1));
      DiSingal.bits.DI2 =  !(R_BSP_PinRead(PORT_DI2));
      DiSingal.bits.DI3 =  !(R_BSP_PinRead(PORT_DI3));
      DiSingal.bits.DI4 =  !(R_BSP_PinRead(PORT_DI4));
    }
    else
    {
      //
    }
    
    return DiSingal.all;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_DOSetHigh(UINT16 Index)   
{
    if(Index == 0)
    {
      R_BSP_PinWrite(PORT_DO1,PIN_LEVEL_HIGH);
    }else if(Index == 1)
    {
      R_BSP_PinWrite(PORT_DO2,PIN_LEVEL_HIGH);
    }else if(Index == 2)
    {
      R_BSP_PinWrite(PORT_DO3,PIN_LEVEL_HIGH);
    }else if(Index == 3)
    {
      R_BSP_PinWrite(PORT_DO4,PIN_LEVEL_HIGH);
    } 
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_DOSetLow(UINT16 Index)   
{
    if(Index == 0)
    {
      R_BSP_PinWrite(PORT_DO1,PIN_LEVEL_LOW);
    }else if(Index == 1)
    {
      R_BSP_PinWrite(PORT_DO2,PIN_LEVEL_LOW);
    }else if(Index == 2)
    {
      R_BSP_PinWrite(PORT_DO3,PIN_LEVEL_LOW);
    }else if(Index == 3)
    {
      R_BSP_PinWrite(PORT_DO4,PIN_LEVEL_LOW);
    } 
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT16 hApi_GetSync0(void)   
{
//    return SYNC0_PORT;
  return 0;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_UpdataDO (void)   
{
      
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT16  hApi_GetAxisCobID (UINT16 AxisID)   
{
   UINT16 AxisCobID = 0;
  
#if 0   
   if(0x00 ==  MCU_DEVICE_ID)
   {
     if(0 ==  AxisID)
     {
       AxisCobID = 0;
     }
     else
     {
       AxisCobID = 1;
     }
   }
   else if(0x01 ==  MCU_DEVICE_ID)
   {
     if(0 ==  AxisID)
     {
       AxisCobID = 2;
     }
     else
     {
       AxisCobID = 3;
     }     
   }
   else if(0x10 ==  MCU_DEVICE_ID)
   {
     if(0 ==  AxisID)
     {
       AxisCobID = 4;
     }
     else
     {
       AxisCobID = 5;
     }        
   }
#endif   
   return AxisCobID;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT8 hApi_GetCurSmpKx (REAL32 *Kx, UINT16 *DeadTime, UINT16 AxisCobID, UINT16 HeadwareVer)   
{
   UINT8 rc = 0;
   UINT16 Index = (HeadwareVer & 0x00FF) - (BASE_DEVICE_HARDWARE_VERSION & 0x00FF);
    
    if(Index > (DevPrmTblNum-1) ||  (AxisCobID > 1))
    {
      //alarm 
      rc = 1;
    }
    else
    {
      *Kx       = DevPrmTbl[Index].Attribute[AxisCobID].MaxSampleCur;
      *DeadTime = DevPrmTbl[Index].Attribute[AxisCobID].DeadTime;
    }
    return rc; 
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_DeadTimeReset(UINT16 DeadTime, UINT16 AxisID)
{
    if(AxisID == 0)
    {
      R_MTU->TDDRA = DeadTime;
      R_MTU3->TCNT = DeadTime;
      R_MTU3->TGRA = PWM_PERIOD_VALUE + DeadTime;
      R_MTU3->TGRC = PWM_PERIOD_VALUE + DeadTime;
    }
    else if(AxisID == 1)
    {
      R_MTU->TDDRB = DeadTime;
      R_MTU6->TCNT = DeadTime;
      R_MTU6->TGRA = PWM_PERIOD_VALUE + DeadTime;
      R_MTU6->TGRC = PWM_PERIOD_VALUE + DeadTime;
    }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_DisableInterrupt(void)
{
  R_BSP_IrqDisable(VECTOR_NUMBER_INTCPU0);
  R_BSP_IrqDisable(VECTOR_NUMBER_INTCPU1);
  
  R_BSP_IrqDisable(VECTOR_NUMBER_DMAC0_INT1);
  R_BSP_IrqDisable(VECTOR_NUMBER_DMAC0_INT1);
  
  R_BSP_IrqDisable(VECTOR_NUMBER_CMT0_CMI);  
  R_BSP_IrqDisable(VECTOR_NUMBER_CMT4_CMI);  
  R_BSP_IrqDisable(VECTOR_NUMBER_TGID0);
  R_BSP_IrqDisable(VECTOR_NUMBER_TGIA8);


  R_BSP_IrqDisable(VECTOR_NUMBER_ESC_SYNC0);
  R_BSP_IrqDisable(VECTOR_NUMBER_ESC_SYNC1);
  R_BSP_IrqDisable(VECTOR_NUMBER_ESC_CAT);
  
//  R_BSP_IrqDisable(VECTOR_NUMBER_USB_FI);  
//  R_BSP_IrqDisable(VECTOR_NUMBER_USB_FDMA0);  
//  R_BSP_IrqDisable(VECTOR_NUMBER_USB_FDMA1);  

  
  R_BSP_IrqDisable(VECTOR_NUMBER_SCI0_ERI);  
  R_BSP_IrqDisable(VECTOR_NUMBER_SCI0_RXI);  
  R_BSP_IrqDisable(VECTOR_NUMBER_SCI0_TEI);    

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL hApi_DetectUsb(void)
{ 
  return R_USBF->SYSCFG0_b.CNEN;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT32 hApi_ReadEcatErrReg(UINT8 RegType,UINT8 port)
{ 
  return EcatErrRegRead(RegType,port);
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT16 hApi_DevicenName(void)
{ 
    UINT16 AdcValue;
        
    bsp_adc1_getvalue(ADCHANNEL6,&AdcValue);
         
    
    if(AdcValue>259 && AdcValue<487)           //0.164
    {
        return 1;
    }
    else if(AdcValue>569 && AdcValue<796)      //0.3
    {
        return 2;
    }
    else if(AdcValue>1195 && AdcValue<1422)    //0.575
    {
        return 3;
    }
    else if(AdcValue>1934 && AdcValue<2162)    //0.9
    {
        return 4;
    }   
    else if(AdcValue>3311 && AdcValue<3538)    //1.505
    {
        return 5;
    }
    else
    {
      	ALMSetGlobalAlarm(ALM_POWIDERR );
        return 1;
    }
      
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_PhaCheckPumpOn(UINT16 AxisID)
{
	gPWMControl[AxisID].bit.PhaCheckPumpOn = 1;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void hApi_PhaCheckPumpOff(UINT16 AxisID)
{
	gPWMControl[AxisID].bit.PhaCheckPumpOn = 0;
}