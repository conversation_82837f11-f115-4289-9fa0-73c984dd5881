/****************************************************************************************************
 *
 * FILE NAME:  FnOnePrmTuning.h
 *
 * DESCRIPTION: One Parameter Tuning
 *
 * CREATED ON:  2021.02.18
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	18-02-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FN_ONE_PRM_TUNING_H_
#define _FN_ONE_PRM_TUNING_H_

#include "BaseDef.h"
#include "RegAccessIf.h"

/****************************************************************************************************/
/*																									*/
/*		Structure Definition																		*/
/*																									*/
/****************************************************************************************************/
typedef	struct ONEPRMV
{
	struct {
		UINT16	SpatMode;				/* 0x2050 : 1 parameter tuning mode					    	*/
		UINT16	SpatLevel;				/* 0x2051 : 1 parameter tuning level						*/
		UINT16	SpatFFLevel;			/* 0x2052 : 1 Para-tuning FF level							*/
		UINT16	SpatLvlChgState;		/* 0x2053 : 1 Para-tuning level change completed			*/
		UINT16	SpatANotchState;		/* 0x2054 : 1 Para-tuning automatic notch completed			*/
		UINT16	SpatAresState;			/* 0x2055 : 1 Para-tuning automatic type A vibration control completed*/
		UINT16	OnePrmFilType;			/* 0x2095 : 1 Para-tuning filter type (common with AAT) 	*/
		UINT16	Dummy;					/* for Alignment											*/
	} OpeReg;
/*--------------------------------------------------------------------------------------------------*/
	struct {
		BOOL	TunRun;					/* Tuning in progress										*/
		BOOL	TunSel;					/* FB / FF tuning selection									*/
		BOOL	FftWait;				/* Waiting for automatic notch execution					*/
		BOOL	NotchChk;				/* Response check after automatic notch setting				*/
		BOOL	FftReq;					/* Frequency analysis request								*/
		BOOL	ModeUnmatch;			/* Tuning mode unmatched									*/
/*--------------------------------------------------------------------------------------------------*/
		UINT32	FftWaitTimer;			/* Automatic notch execution timer							*/
		UINT32	FfWaitTimer;			/* FF setting waiting timer									*/
		UINT32	NotchChkTimer;			/* Notch check timer										*/
/*--------------------------------------------------------------------------------------------------*/
		UINT8	ModeMin;				/* Tuning mode setting minimum value						*/
		UINT8	ModeMax;				/* Tuning mode setting maximum value 						*/
/*--------------------------------------------------------------------------------------------------*/
		UINT8	DigMax;					/* Tuning level movement maximum digit						*/
		UINT8	Dummy1;					/* for Alignment											*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	FfSetting;				/* FF tuning setting										*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	Jrate;					/* Moment of inertia / weight ratio setting					*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	OrgKv;					/* Kv before change											*/
		UINT16	OrgMdlgn;				/* Kpm before change										*/
		UINT16	OrgNf1;					/* First notch filter frequency before change				*/
		UINT16	OrgNf2;					/* Second notch filter frequency before change				*/
		UINT16	OrgNfQ1;				/* First notch filter Q value before change					*/
		UINT16	OrgNfQ2;				/* Second notch filter Q value before change				*/
		UINT16	OrgAResf;				/* Type A damping frequency before change					*/
		UINT16	OrgAResDGn;				/* Type A damping damping gain before change				*/
/*--------------------------------------------------------------------------------------------------*/
		INT16	FftStat;				/* Frequency analysis status								*/
		INT16	Dummy2;					/* for Alignment											*/
/*--------------------------------------------------------------------------------------------------*/
		UINT8	ANotchSetStat;			/* Automatic notch setting result status					*/
/*--------------------------------------------------------------------------------------------------*/
		UINT8	ANotchStat;				/* Automatic notch setting execution status					*/
		UINT8	ANotch1Stat;			/* Automatic notch 1st stage notch setting result			*/
		UINT8	ANotch2Stat;			/* Automatic notch 2nd stage notch setting result			*/
		UINT8	ANotchPreStat;			/* Notch setting just before automatic notch				*/
/*--------------------------------------------------------------------------------------------------*/
		UINT8	AResSetStat;			/* Automatic A type setting result status					*/
/*--------------------------------------------------------------------------------------------------*/
		UINT8	AResStat;				/* Automatic A type setting execution status				*/
		UINT8	ARes1Stat;				/* Automatic A type setting result							*/
/*--------------------------------------------------------------------------------------------------*/
	} var;
} ONEPRMTUNE;


typedef	struct TUNEGAIN
{
	UINT16	loophz;					/* Pn203:Speed loop gain										*/
	UINT16	pitime;					/* Pn204:Speed loop integration time constant					*/
	UINT16	posgn;					/* Pn202:Position loop gain										*/
	UINT16	trqfil11;				/* Pn218:Torque filter time constant							*/
	UINT16	mdlgn;					/* Pn318:Model loop gain										*/
	UINT16	dobgn;					/* Pn309:Friction compensation gain								*/
	UINT16	dlpfil;					/* Pn30C:Friction compensation frequency correction				*/
	UINT16	ntchq1;					/* Pn22B:1st notch filter Q value								*/
	UINT16	ntchq2;					/* Pn22E:2nd notch filter Q value								*/
} TUNEGAIN;


typedef	struct TUNECALV
{
	UINT32		PIRate;
	UINT32		KpZeta;
	UINT32		Ti;
	TUNEGAIN	Gain;				/* Tuning gain													*/
} TUNECALV;


/****************************************************************************************************/
/*																									*/
/*		Constant Definition																			*/
/*																									*/
/****************************************************************************************************/
enum OPTUNE_SEQ_STS {
	OPTUNE_SEQ_INIT				= 0,		/* Initial processing									*/
	OPTUNE_SEQ_JSET,						/* Moment of inertia ratio setting process				*/
	OPTUNE_SEQ_MODESEL,						/* Mode setting process									*/
	OPTUNE_SEQ_FILSEL,						/* Filter type setting process							*/
	OPTUNE_SEQ_PREDISP,						/* Gain display processing before tuning				*/
	OPTUNE_SEQ_TUNING,						/* 1 Para adjustment process							*/
	OPTUNE_SEQ_VIBSEL,						/* Vibration suppression processing						*/
	OPTUNE_SEQ_POSTDISP,					/* Gain display processing after tuning					*/
	OPTUNE_SEQ_NOTCHERR,					/* Notch detection error handling						*/
	OPTUNE_SEQ_END,							/* End processing										*/
};
/*--------------------------------------------------------------------------------------------------*/
enum OPTUNE_FLAG_SEL {
	OPTUNE_FLAG_FBSEL			= 0,		/* Feedback gain selection								*/
	OPTUNE_FLAG_FFSEL,						/* Feedforward gain selection							*/
};
/*--------------------------------------------------------------------------------------------------*/
enum OPTUNE_ARES_DETVIB_STS {
	OPTUNE_STS_ARES_NOVIBDET	= 0,		/* Vibration not detected								*/
	OPTUNE_STS_ARES_DETECT,					/* Vibration is being detected							*/
	OPTUNE_STS_ARES_NOENABLE,				/* Cannot be set automatically							*/
	OPTUNE_STS_ARES_DET_ERR,				/* Detection error										*/
};
/*--------------------------------------------------------------------------------------------------*/
enum OPTUNE_ARES_SET_STS {
	OPTUNE_STS_ARES_INIT		= 0,		/* Initial setting of A type damping					*/
	OPTUNE_STS_ARES_OK,						/* Type A damping setting completed normally			*/
};
/*--------------------------------------------------------------------------------------------------*/
enum OPTUNE_ARES_COMP_STS {
	OPTUNE_STS_ARES_NODETECT	= 0,		/* Type A vibration control completion status: Not implemented*/
	OPTUNE_STS_ARES_COMP,					/* Type A damping completion status: Setting completed	*/
	OPTUNE_STS_ARES_NG,						/* Type A damping completion status: Setting failure	*/
};
/*--------------------------------------------------------------------------------------------------*/
enum OPTUNE_FF_STS {
	OPTUNE_STS_FFCOMP			= 0,		/* FF level change completed status: Change completed / initials  */
	OPTUNE_STS_FFWAIT,						/* FF level change completed status: Waiting for change  */
	OPTUNE_STS_FFTMOUT,						/* FF level change completed status: Change failed (timeout) */
};
/*--------------------------------------------------------------------------------------------------*/


#define		OPTUNE_JTUN_MAX		5			/* Moment of inertia / weight ratio adjustment maximum girder   */
#define		OPTUNE_MFC_ACTIVE	0x0001		/* Rigid system setting(Pn317)							*/ 
#define		OPTUNE_MODE_MIN		0			/* 1 Paramode lower limit								*/
#define		OPTUNE_MODE_MAX		3			/* 1 Paramode upper limit								*/
#define		TUNE_MAX_TI			3000		/* Ti upper limit										*/



/****************************************************************************************************/
/*																									*/
/*		Function Prototype Definition																*/
/*																									*/
/****************************************************************************************************/


/****************************************************************************************************/
/*		API's																						*/
/****************************************************************************************************/
void		OnePrmReSetTuningLevel( ONEPRMTUNE *OnePrmTune, UINT16 loophz, UINT16 posgn, BOOL IPSpdControl );
PRM_RSLT	RpiFunSetOnePrmTuningMode( ONEPRMTUNE *OnePrmTune, UINT16 Mode );
PRM_RSLT	RpiFunSetOnePrmTuningLevel( ONEPRMTUNE *OnePrmTune, UINT16 Lvl );
PRM_RSLT	RpiFunSetOnePrmTuningFfLevel( ONEPRMTUNE *OnePrmTune, UINT16 Lvl );
void		RpiRegSetOnePrmTuningLevel( ONEPRMTUNE *OnePrmTune, UINT16 Gain );
void		RpiRegSetOnePrmTuningFfLevel( ONEPRMTUNE *OnePrmTune, UINT16 Gain );




#endif

