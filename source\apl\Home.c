/****************************************************************************************************
 *
 * FILE NAME:  Home.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.09.29
 *
 * AUTHOR:      XUXIAO
 *
 * History:
 ****************************************************************************************************
	29-0-2019 Version 1.00 : Created by XUXIAO
****************************************************************************************************/

#include "Cia402Appl.h"
#include  "Home.h"
#include "Mlib.h"

#define IndexSignalZ R_BSP_PinRead(BSP_IO_PORT_00_PIN_3)
 /****************************************************************************************************
  * DESCRIPTION:
  *
  * RETURNS:
  *
 ****************************************************************************************************/
PRIVATE void HomeGo(HOME_MODE_CTRL *pCtrlHome)
{	
	 INT32 VelAcc,AccRem,VelQuot,VelRem;
	 VelAcc = pCtrlHome->Acc / (UINT32)TASKB_FRQ;
	 AccRem = pCtrlHome->Acc % (UINT32)TASKB_FRQ;

	 //Zero Point is in the positive direction
	 if(pCtrlHome->HomeOffset < 0)
	 {
	 	 //Slow down to Positive direction
		 pCtrlHome->RmdVel	 += AccRem;
	 
		 if(pCtrlHome->RmdVel  >= TASKB_FRQ)
		 {
			 pCtrlHome->RmdVel	 -= TASKB_FRQ;
			 VelAcc++;
		 }
 
		 if(pCtrlHome->OutputVel + VelAcc <= (INT32)pCtrlHome->SlowSpeed )
		 {
			 pCtrlHome->OutputVel += VelAcc;
		 }
		 else
		 {
			 pCtrlHome->OutputVel = (INT32)pCtrlHome->SlowSpeed;
		 }
		 
		 //Keep running in the positive direction until zero point
		 VelQuot = pCtrlHome->OutputVel /(INT32)TASKB_FRQ;
		 VelRem = pCtrlHome->OutputVel - VelQuot*(INT32)TASKB_FRQ;
			 
		 pCtrlHome->RmdPos += VelRem;
		 if(pCtrlHome->RmdPos >= TASKB_FRQ)
		 {
			 pCtrlHome->RmdPos -= TASKB_FRQ;
			 VelQuot++;
		 }
		 else if(pCtrlHome->RmdPos <= -(INT32)TASKB_FRQ)
		 {
			 pCtrlHome->RmdPos += TASKB_FRQ;
			 VelQuot--;
		 }
		 
		 if(pCtrlHome->OutputPos + VelQuot < 0)
		 {				 
			 pCtrlHome->IntegralPos += VelQuot;
			 pCtrlHome->OutputPos = (INT32)pCtrlHome->IntegralPos + pCtrlHome->InitPos;
		 }
		 else
		 {
			 pCtrlHome->OutputPos = 0;
		 }
	 }
	 else 
	 {
		 //Slow down to negative direction
		 pCtrlHome->RmdVel	 += AccRem;
	 
		 if(pCtrlHome->RmdVel  >= TASKB_FRQ)
		 {
			 pCtrlHome->RmdVel	 -= TASKB_FRQ;
			 VelAcc++;
		 }

		 if(pCtrlHome->OutputVel - VelAcc >= -(INT32)pCtrlHome->SlowSpeed )
		 {
			 pCtrlHome->OutputVel -= VelAcc;
		 }
		 else
		 {
			 pCtrlHome->OutputVel = -(INT32)pCtrlHome->SlowSpeed;
		 }

		 //Keep running in the negative direction until zero point
		 VelQuot = pCtrlHome->OutputVel /(INT32)TASKB_FRQ;
		 VelRem = pCtrlHome->OutputVel - VelQuot*(INT32)TASKB_FRQ;
			 
		 pCtrlHome->RmdPos += VelRem;
		 if(pCtrlHome->RmdPos >= TASKB_FRQ)
		 {
			 pCtrlHome->RmdPos -= TASKB_FRQ;
			 VelQuot++;
		 }
		 else if(pCtrlHome->RmdPos <= -(INT32)TASKB_FRQ)
		 {
			 pCtrlHome->RmdPos += TASKB_FRQ;
			 VelQuot--;
		 }
		 
		 if(pCtrlHome->OutputPos + VelQuot > 0)
		 {
			 pCtrlHome->IntegralPos += VelQuot;
			 pCtrlHome->OutputPos = (INT32)pCtrlHome->IntegralPos + pCtrlHome->InitPos;
		 }
		 else
		 {
			 pCtrlHome->OutputPos = 0;
		 }
	 }
}

PRIVATE void PositiveGoFast(HOME_MODE_CTRL *pCtrlHome)
{	 
    INT32 VelAcc,AccRem;
    VelAcc = pCtrlHome->Acc / (UINT32)TASKB_FRQ;
    AccRem = pCtrlHome->Acc % (UINT32)TASKB_FRQ;

    //Positive operation fast
    pCtrlHome->RmdVel	+= AccRem;

    if(pCtrlHome->RmdVel  >= TASKB_FRQ)
    {
        pCtrlHome->RmdVel	 -= TASKB_FRQ;
        VelAcc++;
    }

    if(pCtrlHome->OutputVel + VelAcc <= (INT32)pCtrlHome->FastSpeed )
    {
        pCtrlHome->OutputVel += VelAcc;
    }
    else
    {
        pCtrlHome->OutputVel =  (INT32)pCtrlHome->FastSpeed;
    }
	
}

PRIVATE void NegativeGoFast(HOME_MODE_CTRL *pCtrlHome)
{	 
    INT32 VelAcc,AccRem;
    VelAcc = pCtrlHome->Acc / (UINT32)TASKB_FRQ;
    AccRem = pCtrlHome->Acc % (UINT32)TASKB_FRQ;

    //Negative operation fast
    pCtrlHome->RmdVel	 += AccRem;

    if(pCtrlHome->RmdVel  >= TASKB_FRQ)
    {
        pCtrlHome->RmdVel	 -= TASKB_FRQ;
        VelAcc++;
    }

    if(pCtrlHome->OutputVel - VelAcc >= -(INT32)pCtrlHome->FastSpeed )
    {
        pCtrlHome->OutputVel -= VelAcc;
    }
    else
    {
        pCtrlHome->OutputVel = -(INT32)pCtrlHome->FastSpeed;
    }
	
 }

 
PRIVATE BOOL PositiveGoSlowly(TCiA402Axis *Cia402Axis,HOME_MODE_CTRL *pCtrlHome)
{	 
    INT32 VelAcc,AccRem;
    VelAcc = pCtrlHome->Acc / (UINT32)TASKB_FRQ;
    AccRem = pCtrlHome->Acc % (UINT32)TASKB_FRQ;

    //Positive operation slowly
    pCtrlHome->RmdVel	+= AccRem;

    if(pCtrlHome->RmdVel  >= TASKB_FRQ)
    {
        pCtrlHome->RmdVel -= TASKB_FRQ;
        VelAcc++;
    }

    if(pCtrlHome->OutputVel >= (INT32)pCtrlHome->SlowSpeed )
    {
        if(pCtrlHome->OutputVel - VelAcc >= (INT32)pCtrlHome->SlowSpeed )
        {
            pCtrlHome->OutputVel -= VelAcc;
        }
        else
        {
            pCtrlHome->OutputVel =  (INT32)pCtrlHome->SlowSpeed;
        }
    }
    else
    {
        if(pCtrlHome->OutputVel + VelAcc <= (INT32)pCtrlHome->SlowSpeed )
        {
            pCtrlHome->OutputVel += VelAcc;
        }
        else
        {
            pCtrlHome->OutputVel =  (INT32)pCtrlHome->SlowSpeed;
        }
    }

    //Reach Index
    if(IndexSignalZ)
    {
        //TODO:Add HomeOffset to the actual position
        pCtrlHome->TouchIndexMark = 1;
				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
    }
    else
    {
        if(Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->SwitchPos > pCtrlHome->IndexMaxDistance)
        {
            return FALSE;
        }
    }
    return TRUE;
}

 PRIVATE BOOL NegativeGoSlowly(TCiA402Axis *Cia402Axis,HOME_MODE_CTRL *pCtrlHome)
 {	 
	  INT32 VelAcc,AccRem;
	  VelAcc = pCtrlHome->Acc / (UINT32)TASKB_FRQ;
	  AccRem = pCtrlHome->Acc % (UINT32)TASKB_FRQ;
	  
	 //Negitive operation slowly
	 pCtrlHome->RmdVel	+= AccRem;
	 
	 if(pCtrlHome->RmdVel  >= TASKB_FRQ)
	 {
		 pCtrlHome->RmdVel	 -= TASKB_FRQ;
		 VelAcc++;
	 }

	if(pCtrlHome->OutputVel <= -(INT32)pCtrlHome->SlowSpeed)
	{
		 if(pCtrlHome->OutputVel + VelAcc <= -(INT32)pCtrlHome->SlowSpeed )
		 {
			 pCtrlHome->OutputVel += VelAcc;
		 }
		 else
		 {
			 pCtrlHome->OutputVel =  -(INT32)pCtrlHome->SlowSpeed;
		 }
	}
	else
	{
		 if(pCtrlHome->OutputVel - VelAcc >= -(INT32)pCtrlHome->SlowSpeed )
		 {
			 pCtrlHome->OutputVel -= VelAcc;
		 }
		 else
		 {
			 pCtrlHome->OutputVel =  -(INT32)pCtrlHome->SlowSpeed;
		 }
	}

	 //Reach Index
	 if(IndexSignalZ)
	 {
		 //TODO:Add HomeOffset to the actual position
		 pCtrlHome->TouchIndexMark = 1;
				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
	 }
	 else
	 {
		 if(Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->SwitchPos < -pCtrlHome->IndexMaxDistance)
		 {
			 return FALSE;
		 }
	 }

	 return TRUE;
 }

 PRIVATE void CalculatePosition(HOME_MODE_CTRL *pCtrlHome)
 {
 	 INT32 VelQuot,VelRem;
	 
 	//Calculate position command
	 VelQuot = pCtrlHome->OutputVel /(INT32)TASKB_FRQ;
	 VelRem = pCtrlHome->OutputVel - VelQuot*(INT32)TASKB_FRQ;
		 
	 pCtrlHome->RmdPos += VelRem;
	 if(pCtrlHome->RmdPos >= TASKB_FRQ)
	 {
		 pCtrlHome->RmdPos -= TASKB_FRQ;
		 VelQuot++;
	 }
	 else if(pCtrlHome->RmdPos <= -(INT32)TASKB_FRQ)
	 {
		 pCtrlHome->RmdPos += TASKB_FRQ;
		 VelQuot--;
	 }
	 
	 pCtrlHome->IntegralPos += VelQuot;
	 pCtrlHome->OutputPos = (INT32)pCtrlHome->IntegralPos + pCtrlHome->InitPos;
 }
  /****************************************************************************************************
   * DESCRIPTION:
   *
   * RETURNS:
   *
  ****************************************************************************************************/
 PRIVATE BOOL trapHOME_Ctrl(TCiA402Axis *Cia402Axis,HOME_MODE_CTRL *pCtrlHome)
 { 
	 //TODO:Assign the value of IO to variables(homeswitch,negativelimit,positivelimit)

	 switch(pCtrlHome->Method)
	 {		 
		 /* Negative limit switch + falling edge + Index signal*/
		 case NegativeLimit_FallEdge_Index: 
		 	 //Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //Not on the negative limit switch
				 if(!pCtrlHome->NegativeLimit)
				 {
					 //Have reached the negative limit switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //Positive operation slowly
						 if(PositiveGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					 }
					 else
					 {
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064 ;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
				 
				 //Calculate position command
 				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }

			 break;
 
		 /* Positive limit switch + falling edge + Index signal*/
		 case PositiveLimit_FallEdge_Index:		 	
			 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //Not on the Positive limit switch
				 if(!pCtrlHome->PositiveLimit)
				 {
					 //Have reached the Positive limit switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //Negitive operation slowly						
						 if(NegativeGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					 }
					 else
					 {
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Negitive operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }
			 
			 break;
 
		 /* HOME Switch in the positive direction  + falling edge + Index signal*/
		 case HomeInPositive_FallEdge_Index:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit || pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //Not on the Home switch
				 if(!pCtrlHome->HomeSwitch)
				 {
					 //Have reached the Home switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //Negitive operation slowly
						 if(NegativeGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					 }
					 else
					 {
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Negitive operation fast
					 NegativeGoFast(pCtrlHome);
 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }

			 break;
 
		 /* HOME Switch in the positive direction + rising edge + Index signal*/
		 case HomeInPositive_RisEdge_Index: 
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit || pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Have reached the Home switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //Positive operation slowly
						 if(PositiveGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					 }
					 else
					 {
						 //Negative operation fast
						NegativeGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }
 
			 break;

		/* HOME Switch in the negative direction  + falling edge + Index signal*/
		 case HomeInNegative_FallEdge_Index:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit || pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //Not on the Home switch
				 if(!pCtrlHome->HomeSwitch)
				 {
					 //Have reached the Home switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //Positive operation slowly
						 if(PositiveGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					 }
					 else
					 {
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }
 
			 break;

		 /* HOME Switch in the Negative direction + rising edge + Index signal*/
		 case HomeInNegative_RisEdge_Index: 
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit || pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Have reached the Home switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //Negative operation slowly
						 if(NegativeGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					 }
					 else
					 {
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
					 }
					 
				 }
				 else 
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }
 
			 break;

		/* HOME Switch negative Edge + PositiveLimit  + falling edge + Index signal*/
		 case HomeNegEdge_PositiveLimit_FallEdge_Index:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchSwitchMark = 1;
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
					 
				 }
				 else if(pCtrlHome->PositiveLimit)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if((pCtrlHome->TouchLimitMark == 0) &&(pCtrlHome->TouchSwitchMark == 0))
				 	{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else if(pCtrlHome->TouchSwitchMark)
					{
						 //Negative operation slowly
						 if(NegativeGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					}
					else
					{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
							
					}
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch negative Edge + PositiveLimit  + Rising edge + Index signal*/
		 case HomeNegEdge_PositiveLimit_RisEdge_Index:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
				 	if(pCtrlHome->TouchSwitchMark == 0)
				 	{
				 		 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
				 	}
					else
					{
						 //Positive operation slowly
						 if(PositiveGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					}
					pCtrlHome->TouchLimitMark = 0;
				 }
				 else if(pCtrlHome->PositiveLimit)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
					 pCtrlHome->TouchSwitchMark = 0;
				 }
				 else 
				 {
				 	if(pCtrlHome->TouchLimitMark == 0)
				 	{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);

						 pCtrlHome->TouchSwitchMark = 1;
						 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
				 	}
					else
					{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);							
					}								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Positive Edge + PositiveLimit  + Rising edge + Index signal*/
		 case HomePosiEdge_PositiveLimit_RisEdge_Index:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
				 	if(!pCtrlHome->TouchEdgeMark)
				 	{
				 		 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else
					{
						 //Negative operation slowly
						 if(NegativeGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					}

					pCtrlHome->TouchSwitchMark = 1;

				 }
				 else if(pCtrlHome->PositiveLimit)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if(pCtrlHome->TouchLimitMark == 0 && pCtrlHome->TouchSwitchMark == 0)
				 	{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else
					{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
						 
						 pCtrlHome->TouchEdgeMark = 1;							
					}

					pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Positive Edge + PositiveLimit  + falling edge + Index signal*/
		 case HomePosiEdge_PositiveLimit_FallEdge_Index:
		 	//Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchSwitchMark = 1;
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
					 
				 }
				 else if(pCtrlHome->PositiveLimit)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if((pCtrlHome->TouchLimitMark == 0) &&(pCtrlHome->TouchSwitchMark == 0))
				 	{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else if(pCtrlHome->TouchSwitchMark)
					{
						 //Positive operation slowly
						 if(PositiveGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					}
					else
					{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);							
					}
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Positive Edge + Negative Limit  + falling edge + Index signal*/
		 case HomePosiEdge_NegativeLimit_FallEdge_Index:
		 	//Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchSwitchMark = 1;
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
					 
				 }
				 else if(pCtrlHome->NegativeLimit)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if((pCtrlHome->TouchLimitMark == 0) &&(pCtrlHome->TouchSwitchMark == 0))
				 	{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
				 	}
					else if(pCtrlHome->TouchSwitchMark)
					{
						 //Positive operation slowly
						 if(PositiveGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					}
					else
					{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);							
					}
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Positive Edge + Negative Limit  + Rising edge + Index signal*/
		 case HomePosiEdge_NegativeLimit_RisEdge_Index:
		 	 //Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
				 	if(pCtrlHome->TouchSwitchMark == 0)
				 	{
				 		 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else
					{
						 //Negative operation slowly
						 if(NegativeGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					}
					pCtrlHome->TouchLimitMark = 0;
				 }
				 else if(pCtrlHome->NegativeLimit)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
					 pCtrlHome->TouchSwitchMark = 0;
				 }
				 else 
				 {
				 	if(pCtrlHome->TouchLimitMark == 0)
				 	{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
						 
						 pCtrlHome->TouchSwitchMark = 1;
						 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
				 	}
					else
					{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);							
					}								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Negative Edge + Negative Limit  + Rising edge + Index signal*/
		 case HomeNegEdge_NegativeLimit_RisEdge_Index:
		 	//Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
				 	if(!pCtrlHome->TouchEdgeMark)
				 	{
				 		 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
				 	}
					else
					{
						 //Positive operation slowly
						 if(PositiveGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					}
					pCtrlHome->TouchSwitchMark = 1;

				 }
				 else if(pCtrlHome->NegativeLimit)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if(pCtrlHome->TouchLimitMark == 0 && pCtrlHome->TouchSwitchMark == 0)
				 	{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
				 	}
					else
					{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
						 
						 pCtrlHome->TouchEdgeMark = 1;							
					}

					pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Negative Edge + Negative Limit  + falling edge + Index signal*/
		 case HomeNegEdge_NegativeLimit_FallEdge_Index:
		 	//Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchSwitchMark = 1;
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
					 
				 }
				 else if(pCtrlHome->NegativeLimit)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if((pCtrlHome->TouchLimitMark == 0) &&(pCtrlHome->TouchSwitchMark == 0))
				 	{						
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
				 	}
					else if(pCtrlHome->TouchSwitchMark)
					{
						 //Negative operation slowly
						 if(NegativeGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
						 {
						 	 return FALSE;
						 }
					}
					else
					{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);							
					}								 
				 }

				 //Calculate position command				 
				CalculatePosition(pCtrlHome);
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* Negative limit switch + falling edge*/
		 case NegativeLimit_FallEdge: 
		 	 //Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //Not on the negative limit switch
				 if(!pCtrlHome->NegativeLimit)
				 {
					 //Have reached the negative limit switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					 }
					 else
					 {
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064 ;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }
 
			 break;
 
		 /* Positive limit switch + falling edge*/
		 case PositiveLimit_FallEdge: 
		 	//Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //Not on the Positive limit switch
				 if(!pCtrlHome->PositiveLimit)
				 {
					 //Have reached the Positive limit switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					 }
					 else
					 {
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Negitive operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }
 
			 break;
 
		 /* HOME Switch in the positive direction  + falling edge*/
		 case HomeInPositive_FallEdge:
		 	//Over limit, failure
			 if(pCtrlHome->NegativeLimit || pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //Not on the Home switch
				 if(!pCtrlHome->HomeSwitch)
				 {
					 //Have reached the Home switch
					 if(pCtrlHome->TouchSwitchMark)
					 {						
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					 }
					 else
					 {
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Negitive operation fast
					 NegativeGoFast(pCtrlHome);
 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }
 
			 break;
 
		 /* HOME Switch in the positive direction + rising edge*/
		 case HomeInPositive_RisEdge: 
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit || pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Have reached the Home switch
					 if(pCtrlHome->TouchSwitchMark)
					 {						
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);					
					 }
					 else
					 {
						 //Negative operation fast
						NegativeGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				 HomeGo(pCtrlHome);
			 }
 
			 break;

		/* HOME Switch in the negative direction  + falling edge*/
		 case HomeInNegative_FallEdge:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit || pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //Not on the Home switch
				 if(!pCtrlHome->HomeSwitch)
				 {
					 //Have reached the Home switch
					 if(pCtrlHome->TouchSwitchMark)
					 {
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					 }
					 else
					 {
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 
									 
				 }
 
				 //Calculate position command
				CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				 HomeGo(pCtrlHome);
			 }
 
			 break;

		 /* HOME Switch in the Negative direction + rising edge*/
		 case HomeInNegative_RisEdge: 
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit || pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
			 //Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Have reached the Home switch
					 if(pCtrlHome->TouchSwitchMark)
					 {						 
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);					
					 }
					 else
					 {
						 //Positive operation fast
						PositiveGoFast(pCtrlHome);
					 }					 
				 }
				 else 
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
 
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
					 pCtrlHome->TouchSwitchMark = 1;	 									 
				 }
 
				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
			 }
			 else
			 {	 
				HomeGo(pCtrlHome);
			 }
 
			 break;

		/* HOME Switch negative Edge + PositiveLimit  + falling edge*/
		 case HomeNegEdge_PositiveLimit_FallEdge:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);

					 pCtrlHome->TouchSwitchMark = 1;
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;
				 }
				 else if(pCtrlHome->PositiveLimit)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if((pCtrlHome->TouchLimitMark == 0) &&(pCtrlHome->TouchSwitchMark == 0))
				 	{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else if(pCtrlHome->TouchSwitchMark)
					{						 
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					}
					else
					{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);							
					}
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch negative Edge + PositiveLimit  + Rising edge*/
		 case HomeNegEdge_PositiveLimit_RisEdge:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
				 	if(pCtrlHome->TouchSwitchMark == 0)
				 	{
				 		 //Negative operation fast
						NegativeGoFast(pCtrlHome);
				 	}
					else
					{
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					}
					pCtrlHome->TouchLimitMark = 0;
				 }
				 else if(pCtrlHome->PositiveLimit)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
					 pCtrlHome->TouchSwitchMark = 0;
				 }
				 else 
				 {
				 	if(pCtrlHome->TouchLimitMark == 0)
				 	{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);

						 pCtrlHome->TouchSwitchMark = 1;
						 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
				 	}
					else
					{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);							
					}								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Positive Edge + PositiveLimit  + Rising edge*/
		 case HomePosiEdge_PositiveLimit_RisEdge:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
				 	if(!pCtrlHome->TouchEdgeMark)
				 	{
				 		//Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else
					{						
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					}

					pCtrlHome->TouchSwitchMark = 1;

				 }
				 else if(pCtrlHome->PositiveLimit)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if(pCtrlHome->TouchLimitMark == 0 && pCtrlHome->TouchSwitchMark == 0)
				 	{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else
					{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);

						 pCtrlHome->TouchEdgeMark = 1;
							
					}

					pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Positive Edge + PositiveLimit  + falling edge*/
		 case HomePosiEdge_PositiveLimit_FallEdge:
		 	 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					  //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
	                 pCtrlHome->TouchSwitchMark = 1;
	                 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064;

				 }
				 else if(pCtrlHome->PositiveLimit)
				 {
					 //Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if((pCtrlHome->TouchLimitMark == 0) &&(pCtrlHome->TouchSwitchMark == 0))
				 	{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);
				 	}
					else if(pCtrlHome->TouchSwitchMark)
					{						
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					}
					else
					{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);							
					}							 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			 HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Positive Edge + Negative Limit  + falling edge*/
		 case HomePosiEdge_NegativeLimit_FallEdge:
		 	 //Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
			 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchSwitchMark = 1;
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
					 
				 }
				 else if(pCtrlHome->NegativeLimit)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if((pCtrlHome->TouchLimitMark == 0) &&(pCtrlHome->TouchSwitchMark == 0))
				 	{
						 //Negative operation fast
						NegativeGoFast(pCtrlHome);
				 	}
					else if(pCtrlHome->TouchSwitchMark)
					{						
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					}
					else
					{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);							
					}
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Positive Edge + Negative Limit  + Rising edge*/
		 case HomePosiEdge_NegativeLimit_RisEdge:
		 	 //Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
				 	if(pCtrlHome->TouchSwitchMark == 0)
				 	{
				 		 //Positive operation fast
						PositiveGoFast(pCtrlHome);
				 	}
					else
					{						
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
						
					}
					pCtrlHome->TouchLimitMark = 0;
				 }
				 else if(pCtrlHome->NegativeLimit)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
					 pCtrlHome->TouchSwitchMark = 0;
				 }
				 else 
				 {
				 	if(pCtrlHome->TouchLimitMark == 0)
				 	{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
						 
						 pCtrlHome->TouchSwitchMark = 1;
						 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
				 	}
					else
					{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);							
					}								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Negative Edge + Negative Limit  + Rising edge*/
		 case HomeNegEdge_NegativeLimit_RisEdge:
		 	//Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 // on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
				 	if(!pCtrlHome->TouchEdgeMark)
				 	{
				 		 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
						 
				 	}
					else
					{						
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					}

					pCtrlHome->TouchSwitchMark = 1;

				 }
				 else if(pCtrlHome->NegativeLimit)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if(pCtrlHome->TouchLimitMark == 0 && pCtrlHome->TouchSwitchMark == 0)
				 	{
						 //Negative operation fast
						 NegativeGoFast(pCtrlHome);
				 	}
					else
					{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);

						 pCtrlHome->TouchEdgeMark = 1;
							
					}

					pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			 HomeGo(pCtrlHome);
		 }
			 
		 break;

		 /* HOME Switch Negative Edge + Negative Limit  + falling edge*/
		 case HomeNegEdge_NegativeLimit_FallEdge:
		 	//Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
		 
		 	//Have not reached Index 
			 if(!pCtrlHome->TouchIndexMark)
			 {
				 //on the Home switch
				 if(pCtrlHome->HomeSwitch)
				 {
					//Negative operation fast
					 NegativeGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchSwitchMark = 1;
					 pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
					 
				 }
				 else if(pCtrlHome->NegativeLimit)
				 {
					 //Positive operation fast
					 PositiveGoFast(pCtrlHome);
					 
					 pCtrlHome->TouchLimitMark = 1;
				 }
				 else 
				 {
				 	if((pCtrlHome->TouchLimitMark == 0) &&(pCtrlHome->TouchSwitchMark == 0))
				 	{
						
						//Negative operation fast
						 NegativeGoFast(pCtrlHome);
				 	}
					else if(pCtrlHome->TouchSwitchMark)
					{						 
						 //TODO:Add HomeOffset to the actual position
						 pCtrlHome->TouchIndexMark = 1;
						 				pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
					}
					else
					{
						 //Positive operation fast
						 PositiveGoFast(pCtrlHome);							
					}								 
				 }

				 //Calculate position command
				 CalculatePosition(pCtrlHome);
				 
		 }
		 else
		 {	 
			HomeGo(pCtrlHome);
		 }
			 
		 break;

		/* Negative operation + Index */
		case Negative_Index:
			 //Over limit, failure
			 if(pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
			 
			if(!pCtrlHome->TouchIndexMark)
			{
				if(pCtrlHome->TouchSwitchMark == 0)
				{
					pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
					pCtrlHome->TouchSwitchMark = 1;
				}
				else
				{
					  //Negitive operation slowly
					 if(NegativeGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
					 {
					 	 return FALSE;
					 }
					 //Calculate position command
				 	 CalculatePosition(pCtrlHome);
				}
			}
			else
			{
				 HomeGo(pCtrlHome);
			}
		 
			break;
			
		/* Positive operation + Index */	
		case  Positive_Index:
			//Over limit, failure
			 if(pCtrlHome->PositiveLimit)
			 {
				 return FALSE;				 
			 }
		 
			if(!pCtrlHome->TouchIndexMark)
			{
				if(pCtrlHome->TouchSwitchMark == 0)
				{
					pCtrlHome->SwitchPos = Cia402Axis->Objects->PositionActualValue0x6064; 
					pCtrlHome->TouchSwitchMark = 1;
				}
				else
				{
					//Positive operation slowly
					 if(PositiveGoSlowly(Cia402Axis,pCtrlHome) == FALSE)
					 {
					 	 return FALSE;
					 }
					 //Calculate position command
					 CalculatePosition(pCtrlHome);
				}
			}
			else
			{
				 HomeGo(pCtrlHome);
			}
			
			break;
				
		case HomeNow:
			//Over limit, failure
			 if(pCtrlHome->PositiveLimit ||pCtrlHome->NegativeLimit)
			 {
				 return FALSE;				 
			 }
		 	
			if(!pCtrlHome->TouchIndexMark)
			{
				 //TODO:Add HomeOffset to the actual position
				 pCtrlHome->PosOffset = pCtrlHome->HomeOffset - (Cia402Axis->Objects->PositionActualValue0x6064 - pCtrlHome->PosOffset);
				 pCtrlHome->TouchIndexMark = 1;
			}
			else
			{
				 HomeGo(pCtrlHome);
			}

			break;
		
		default:
			 return FALSE;		
			break;
	 }

	 return TRUE; 
 }


 /****************************************************************************************************
  * DESCRIPTION:
  *
  * RETURNS:
  *
 ****************************************************************************************************/
 PUBLIC void Cia402_HOME_Mode(TCiA402Axis *Cia402Axis, AXIS_HANDLE *AxisB, BOOL BaseEnable )
 {
	 ENCODER                 *pEnc =  AxisB->BaseLoops->Enc;
	 CiA402_PRM 			 *Objects;
	 CIA402_STATUS_WORD 	 StatusWord = {0};
	 CIA402_CTRL_WORD		 ControlWord = {0};
	 HOME_MODE_CTRL			*pHOME_Ctrl = &Cia402Axis->HOME_Ctrl;

	 UINT16 HomeState;

	 Objects = Cia402Axis->Objects;
	 StatusWord.all = Cia402Axis->Objects->Statusword0x6041;
	 ControlWord.all = Cia402Axis->Objects->Controlword0x6040;

 
	 if(BaseEnable == FALSE)
	 {
           MlibResetLongMemory(pHOME_Ctrl, sizeof(*pHOME_Ctrl)/4);
           Objects->PositionDemandValue0x6062 = Objects->PositionActualValue0x6064;
           return;
	 }
	 
	 
        if(pHOME_Ctrl->FirstRun == 0 )
        {       
            pHOME_Ctrl->HomeMark = 0;		 
            StatusWord.bit.TargetReached = 1;
            StatusWord.bit.OperSpecific_b12 = 0;
            StatusWord.bit.OperSpecific_b13 = 0;
            Objects->PositionDemandValue0x6062 = Objects->PositionActualValue0x6064;
            pHOME_Ctrl->FirstRun = 1;  
        }
 
	 if(ControlWord.bit.OperSpecific_b4)
	 {
		 if(!pHOME_Ctrl->HomeMark)
		 {
			 pHOME_Ctrl->FastSpeed = Objects->FastHomingSpeed0x6099;
			 pHOME_Ctrl->SlowSpeed = Objects->SlowHomingSpeed0x6099;
			 
			 //accerlation limit
			 if(Objects->Max_acceleration0x60C5 <= Objects->Max_deceleration0x60C6)
			 {
				 pHOME_Ctrl->Acc = MlibLimitu32(Objects->HomingAcceleration0x609A, Objects->Max_acceleration0x60C5);
			 }
			 else
			 {
				 pHOME_Ctrl->Acc = MlibLimitu32(Objects->HomingAcceleration0x609A, Objects->Max_deceleration0x60C6);
			 }
				 
			 //Fast Speed limit  
			 if(pHOME_Ctrl->FastSpeed >Objects->Max_profile_velocity0x607F)
			 {
				 pHOME_Ctrl->FastSpeed = Objects->Max_profile_velocity0x607F;
			 }
			 
			 //Slow Speed limit  
			 if(pHOME_Ctrl->SlowSpeed >Objects->Max_profile_velocity0x607F)
			 {
				 pHOME_Ctrl->SlowSpeed = Objects->Max_profile_velocity0x607F;
			 }
			 
			 pHOME_Ctrl->HomeOffset = Objects->HomeOffset0x607C;
			 pHOME_Ctrl->Method = Objects->HomingMethod0x6098;
			 pHOME_Ctrl->InitPos = Objects->PositionDemandValue0x6062;
			 pHOME_Ctrl->OutputPos = pHOME_Ctrl->InitPos;
			 pHOME_Ctrl->OutputVel = 0;
			 pHOME_Ctrl->RmdPos = 0;
			 pHOME_Ctrl->RmdVel = 0;
			 pHOME_Ctrl->IntegralPos = 0;
			 pHOME_Ctrl->IndexMaxDistance = pEnc->P.EncPPR;
			 pHOME_Ctrl->SwitchPos = 0;
			 pHOME_Ctrl->TouchIndexMark = 0;
			 pHOME_Ctrl->LastTouchIndexMark = 0;
			 pHOME_Ctrl->TouchSwitchMark = 0;
			 pHOME_Ctrl->TouchLimitMark = 0;
			 pHOME_Ctrl->TouchEdgeMark = 0;
			 
			 pHOME_Ctrl->HomeMark = 1;
		 }
		 
		 
		 if(pHOME_Ctrl->PosChangFlag)
		 {
			 pHOME_Ctrl->InitPos = Objects->PositionActualValue0x6064;
			 pHOME_Ctrl->OutputPos = pHOME_Ctrl->InitPos;
			 pHOME_Ctrl->OutputVel = Objects->VelocityActualValue0x606C;
			 pHOME_Ctrl->RmdPos = 0;
			 pHOME_Ctrl->RmdVel = 0;
			 pHOME_Ctrl->IntegralPos = 0;
			 pHOME_Ctrl->IndexMaxDistance = pEnc->P.EncPPR;
		 }
 
		 HomeState = trapHOME_Ctrl(Cia402Axis,pHOME_Ctrl);
		 
		 Objects->PositionDemandValue0x6062 = (INT32)pHOME_Ctrl->OutputPos;
 
		 //Home fail
		 if(!HomeState)
		 {
			 if(pHOME_Ctrl->OutputVel == 0)
			 {					 
				 StatusWord.bit.TargetReached = 1;
				 StatusWord.bit.OperSpecific_b12 = 0;
				 StatusWord.bit.OperSpecific_b13 = 1;				 
			 }
			 else
			 {
				 StatusWord.bit.TargetReached = 0;
				 StatusWord.bit.OperSpecific_b12 = 0;
				 StatusWord.bit.OperSpecific_b13 = 1;
			 }
		 }
		 else
		 {
			 // Zero Reach
			 if((Objects->PositionActualValue0x6064 < (INT32)Objects->PositionWindow0x6067)
			   && (Objects->PositionActualValue0x6064 > - (INT32)Objects->PositionWindow0x6067)
			   && pHOME_Ctrl->TouchIndexMark)
			 {
				 if(pHOME_Ctrl->PosWindowCnt < Objects->PositionWindowTime0x6068 * 1000/TASKB_CYCLEUS)
				 {
					 pHOME_Ctrl->PosWindowCnt++;
					 
					 StatusWord.bit.TargetReached = 0;
					 StatusWord.bit.OperSpecific_b12 = 1;
					 StatusWord.bit.OperSpecific_b13 = 0;
				 }
				 else
				 {
					 StatusWord.bit.TargetReached = 1;
					 StatusWord.bit.OperSpecific_b12 = 1;
					 StatusWord.bit.OperSpecific_b13 = 0;
				 }
			 }
			 else if(pHOME_Ctrl->TouchSwitchMark)
			 {
				 pHOME_Ctrl->PosWindowCnt = 0;
				 
				 StatusWord.bit.TargetReached = 0;
				 StatusWord.bit.OperSpecific_b12 = 1;
				 StatusWord.bit.OperSpecific_b13 = 0;
			 }
			 else
			 {
				 pHOME_Ctrl->PosWindowCnt = 0;
				 
				 StatusWord.bit.TargetReached = 0;
				 StatusWord.bit.OperSpecific_b12 = 0;
				 StatusWord.bit.OperSpecific_b13 = 0;
			 }
			 
		 }
	 }
	 else
	 {
		 pHOME_Ctrl->HomeMark = 0;
		 
		 StatusWord.bit.TargetReached = 1;
		 StatusWord.bit.OperSpecific_b12 = 0;
		 StatusWord.bit.OperSpecific_b13 = 0;
	 }

	 Objects->Statusword0x6041 = StatusWord.all;
	 
 }



