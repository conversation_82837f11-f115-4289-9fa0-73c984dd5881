@echo off

pushd %0\..
cls

@echo --- Move Boot Binary ---

setlocal enabledelayedexpansion

for /f "tokens=2 delims==" %%G in ('wmic os get localdatetime /value') do (
    set datetime=%%G
	set yyyy=!datetime:~0,4!
    set mm=!datetime:~4,2!
    set dd=!datetime:~6,2!
)

set BINNAME=%1
set BINNAME2=%3

::set BINNAME="RossFinch-I.bin"

if not exist %BINNAME% goto end

::set DSTDIR=../Binary

set DSTDIR=%2

copy /Y %BINNAME% "%DSTDIR%\%BINNAME2%_%yyyy%%mm%%dd%.bin"

@echo --- Move  Ok ---

:end