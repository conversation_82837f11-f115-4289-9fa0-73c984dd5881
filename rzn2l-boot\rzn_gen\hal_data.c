/* generated HAL source file - do not edit */
#include "hal_data.h"
usb_instance_ctrl_t g_basic0_ctrl;

#if !defined(g_usb_descriptor)
extern usb_descriptor_t g_usb_descriptor;
#endif
#define FSP_NOT_DEFINED (1)
            const usb_cfg_t g_basic0_cfg =
            {
                .usb_mode  = USB_MODE_PERI,
                .usb_speed = USB_SPEED_HS,
                .module_number = 0,
                .type = USB_CLASS_PCDC,
#if defined(g_usb_descriptor)
                .p_usb_reg = g_usb_descriptor,
#else
                .p_usb_reg = &g_usb_descriptor,
#endif
                .usb_complience_cb = NULL,
#if defined(VECTOR_NUMBER_USB_FI)
                .irq       = VECTOR_NUMBER_USB_FI,
#elif defined(VECTOR_NUMBER_USB_HI)
                .irq       = VECTOR_NUMBER_USB_HI,
#else
                .irq       = FSP_INVALID_VECTOR,
#endif
#if defined(VECTOR_NUMBER_USBFS_RESUME)
                .irq_r     = VECTOR_NUMBER_USBFS_RESUME,
#else
                .irq_r     = FSP_INVALID_VECTOR,
#endif
#if defined(VECTOR_NUMBER_USB_FDMA0)
                .irq_d0    = VECTOR_NUMBER_USB_FDMA0,
#else
                .irq_d0    = FSP_INVALID_VECTOR,
#endif
#if defined(VECTOR_NUMBER_USB_FDMA1)
                .irq_d1    = VECTOR_NUMBER_USB_FDMA1,
#else
                .irq_d1    = FSP_INVALID_VECTOR,
#endif
                .hsirq     = FSP_INVALID_VECTOR,
                .hsirq_d0  = FSP_INVALID_VECTOR,
                .hsirq_d1  = FSP_INVALID_VECTOR,
                .ipl       = (12),
                .ipl_r     = (12),
                .ipl_d0    = (12),
                .ipl_d1    = (12),
                .hsipl     = (12),
                .hsipl_d0  = (12),
                .hsipl_d1  = (12),
#if (BSP_CFG_RTOS == 2)
                .p_usb_apl_callback = NULL,
#else
                .p_usb_apl_callback = NULL,
#endif
#if defined(NULL)
                .p_context = NULL,
#else
                .p_context = &NULL,
#endif
                .p_transfer_tx = NULL,
                .p_transfer_rx = NULL,
            };
#undef FSP_NOT_DEFINED

/* Instance structure to use this module. */
const usb_instance_t g_basic0 =
{
    .p_ctrl        = &g_basic0_ctrl,
    .p_cfg         = &g_basic0_cfg,
    .p_api         = &g_usb_on_usb,
};

xspi_qspi_instance_ctrl_t g_qspi1_ctrl;

static const spi_flash_erase_command_t g_qspi1_erase_command_list[] =
{
#if 4096 > 0
    {.command = 0x20,     .size = 4096 },
#endif
#if 32768 > 0
    {.command = 0x52, .size = 32768 },
#endif
#if 65536 > 0
    {.command = 0xD8,      .size = 65536 },
#endif
#if 0xC7 > 0
    {.command = 0xC7,       .size  = SPI_FLASH_ERASE_SIZE_CHIP_ERASE         },
#endif
};

static xspi_qspi_timing_setting_t g_qspi1_timing_settings =
{
    .command_to_command_interval = XSPI_QSPI_COMMAND_INTERVAL_CLOCKS_7,
    .cs_pullup_lag               = XSPI_QSPI_CS_PULLUP_CLOCKS_NO_EXTENSION,
    .cs_pulldown_lead            = XSPI_QSPI_CS_PULLDOWN_CLOCKS_NO_EXTENSION
};

static const xspi_qspi_extended_cfg_t g_qspi1_extended_cfg =
{
    .unit              = 1,
    .chip_select       = XSPI_QSPI_CHIP_SELECT_0,
    .memory_size       = XSPI_QSPI_MEMORY_SIZE_16MB,
    .p_timing_settings = &g_qspi1_timing_settings,
    .prefetch_en       = XSPI_QSPI_PREFETCH_FUNCTION_ENABLE,
};
const spi_flash_cfg_t g_qspi1_cfg =
{
    .spi_protocol        = SPI_FLASH_PROTOCOL_1S_1S_1S,
    .address_bytes       = SPI_FLASH_ADDRESS_BYTES_3,
    .dummy_clocks        = SPI_FLASH_DUMMY_CLOCKS_0,
    .read_command        = 0x03,
    .page_program_command = 0x02,
    .write_enable_command = 0x06,
    .status_command = 0x05,
    .write_status_bit    = 0,
    .xip_enter_command   = 0x20,
    .xip_exit_command    = 0xFF,
    .p_erase_command_list = &g_qspi1_erase_command_list[0],
    .erase_command_list_length = sizeof(g_qspi1_erase_command_list) / sizeof(g_qspi1_erase_command_list[0]),
    .p_extend            = &g_qspi1_extended_cfg,
};
/** This structure encompasses everything that is needed to use an instance of this interface. */
const spi_flash_instance_t g_qspi1 =
{
    .p_ctrl = &g_qspi1_ctrl,
    .p_cfg =  &g_qspi1_cfg,
    .p_api =  &g_spi_flash_on_xspi_qspi,
};
void g_hal_init(void) {
g_common_init();
}
