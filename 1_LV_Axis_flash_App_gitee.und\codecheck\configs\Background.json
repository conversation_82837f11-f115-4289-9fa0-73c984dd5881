{"automatic": true, "checks": ["CPP_CT_BUGPRONE_ASSERT_SIDE_EFFECT", "CPP_CT_BUGPRONE_BRANCH_CLONE", "CPP_CT_BUGPRONE_COPY_CONSTRUCTOR_INIT", "CPP_CT_BUGPRONE_INFINITE_LOOP", "CPP_CT_BUGPRONE_MACRO_REPEATED_SIDE_EFFECTS", "CPP_CT_BUGPRONE_NOT_NULL_TERMINATED_RESULT", "CPP_CT_BUGPRONE_REDUNDANT_BRANCH_CONDITION", "CPP_CT_READABILITY_DELETE_NULL_POINTER", "CPP_WARN_ADDRESS_OF_TEMPORARY", "CPP_WARN_ARRAY_BOUNDS", "CPP_WARN_AVAILABILITY", "CPP_WARN_BACKSLASH_NEWLINE_ESCAPE", "CPP_WARN_BITFIELD_CONSTANT_CONVERSION", "CPP_WARN_BITFIELD_WIDTH", "CPP_WARN_BOOL_CONVERSION", "CPP_WARN_BUILTIN_MACRO_REDEFINED", "CPP_WARN_BUILTIN_MEMCPY_CHK_SIZE", "CPP_WARN_CALL_TO_PURE_VIRTUAL_FROM_CTOR_DTOR", "CPP_WARN_CAST_QUAL_UNRELATED", "CPP_WARN_CLASS_CONVERSION", "CPP_WARN_COMPARE_DISTINCT_POINTER_TYPES", "CPP_WARN_COMPOUND_TOKEN_SPLIT_BY_MACRO", "CPP_WARN_CONDITIONAL_TYPE_MISMATCH", "CPP_WARN_CONSTANT_CONVERSION", "CPP_WARN_CONSTANT_LOGICAL_OPERAND", "CPP_WARN_CPP11_COMPAT_DEPRECATED_WRITABLE_STRINGS", "CPP_WARN_DANGLING", "CPP_WARN_DANGLING_FIELD", "CPP_WARN_DANGLING_GSL", "CPP_WARN_DANGLING_INITIALIZER_LIST", "CPP_WARN_DEFAULTED_FUNCTION_DELETED", "CPP_WARN_DELEGATING_CTOR_CYCLES", "CPP_WARN_DELETE_ABSTRACT_NON_VIRTUAL_DTOR", "CPP_WARN_DELETE_INCOMPLETE", "CPP_WARN_DEPRECATED_BUILTINS", "CPP_WARN_DEPRECATED_DECLARATIONS", "CPP_WARN_DEPRECATED_INCREMENT_BOOL", "CPP_WARN_DEPRECATED_NON_PROTOTYPE", "CPP_WARN_DEPRECATED_REGISTER", "CPP_WARN_DEPRECATED_VOLATILE", "CPP_WARN_DIVISION_BY_ZERO", "CPP_WARN_DYNAMIC_CLASS_MEMACCESS", "CPP_WARN_ENUM_COMPARE", "CPP_WARN_ENUM_COMPARE_SWITCH", "CPP_WARN_EXCEPTIONS", "CPP_WARN_EXTERN_C_COMPAT", "CPP_WARN_EXTERN_INITIALIZER", "CPP_WARN_EXTRA_QUALIFICATION", "CPP_WARN_FIXED_POINT_OVERFLOW", "CPP_WARN_FLAG_ENUM", "CPP_WARN_FORMAT", "CPP_WARN_FORMAT_EXTRA_ARGS", "CPP_WARN_FORMAT_INSUFFICIENT_ARGS", "CPP_WARN_FORMAT_INVALID_SPECIFIER", "CPP_WARN_FORMAT_SECURITY", "CPP_WARN_FORMAT_ZERO_LENGTH", "CPP_WARN_FORTIFY_SOURCE", "CPP_WARN_FREE_NONHEAP_OBJECT", "CPP_WARN_HEADER_GUARD", "CPP_WARN_IGNORED_ATTRIBUTES", "CPP_WARN_IGNORED_REFERENCE_QUALIFIERS", "CPP_WARN_IMPLICITLY_UNSIGNED_LITERAL", "CPP_WARN_IMPLICIT_CONST_INT_FLOAT_CONVERSION", "CPP_WARN_IMPLICIT_CONVERSION_FLOATING_POINT_TO_BOOL", "CPP_WARN_IMPLICIT_EXCEPTION_SPEC_MISMATCH", "CPP_WARN_IMPLICIT_FIXED_POINT_CONVERSION", "CPP_WARN_INACCESSIBLE_BASE", "CPP_WARN_INCOMPATIBLE_EXCEPTION_SPEC", "CPP_WARN_INCOMPATIBLE_LIBRARY_REDECLARATION", "CPP_WARN_INCOMPATIBLE_POINTER_TYPES", "CPP_WARN_INCOMPATIBLE_POINTER_TYPES_DISCARDS_QUALIFIERS", "CPP_WARN_INCONSISTENT_MISSING_OVERRIDE", "CPP_WARN_INCREMENT_BOOL", "CPP_WARN_INJECTED_CLASS_NAME", "CPP_WARN_INLINE_NAMESPACE_REOPENED_NONINLINE", "CPP_WARN_INSTANTIATION_AFTER_SPECIALIZATION", "CPP_WARN_INTEGER_OVERFLOW", "CPP_WARN_INT_TO_POINTER_CAST", "CPP_WARN_INT_TO_VOID_POINTER_CAST", "CPP_WARN_INVALID_NORETURN", "CPP_WARN_INVALID_NO_BUILTIN_NAMES", "CPP_WARN_INVALID_OFFSETOF", "CPP_WARN_INVALID_PP_TOKEN", "CPP_WARN_LARGE_BY_VALUE_COPY", "CPP_WARN_LITERAL_CONVERSION", "CPP_WARN_LITERAL_RANGE", "CPP_WARN_LOGICAL_NOT_PARENTHESES", "CPP_WARN_MACRO_REDEFINED", "CPP_WARN_MAIN_RETURN_TYPE", "CPP_WARN_MANY_BRACES_AROUND_SCALAR_INIT", "CPP_WARN_MAX_UNSIGNED_ZERO", "CPP_WARN_MEMSET_TRANSPOSED_ARGS", "CPP_WARN_MEMSIZE_COMPARISON", "CPP_WARN_MISMATCHED_NEW_DELETE", "CPP_WARN_MISSING_DECLARATIONS", "CPP_WARN_MISSING_EXCEPTION_SPEC", "CPP_WARN_MULTICHAR", "CPP_WARN_MULTIPLE_MOVE_VBASE", "CPP_WARN_NEW_RETURNS_NULL", "CPP_WARN_NODEREF", "CPP_WARN_NONNULL", "CPP_WARN_NONPORTABLE_INCLUDE_PATH", "CPP_WARN_NONTRIVIAL_MEMACCESS", "CPP_WARN_NON_C_TYPEDEF_FOR_LINKAGE", "CPP_WARN_NON_LITERAL_NULL_CONVERSION", "CPP_WARN_NON_POD_VARARGS", "CPP_WARN_NON_POWER_OF_TWO_ALIGNMENT", "CPP_WARN_NULL_ARITHMETIC", "CPP_WARN_NULL_CHARACTER", "CPP_WARN_NULL_CONVERSION", "CPP_WARN_NULL_DEREFERENCE", "CPP_WARN_ODR", "CPP_WARN_ORDERED_COMPARE_FUNCTION_POINTERS", "CPP_WARN_OUT_OF_SCOPE_FUNCTION", "CPP_WARN_OVERLOADED_SHIFT_OP_PARENTHESES", "CPP_WARN_PARENTHESES_EQUALITY", "CPP_WARN_POINTER_ARITH", "CPP_WARN_POINTER_BOOL_CONVERSION", "CPP_WARN_POINTER_COMPARE", "CPP_WARN_POINTER_INTEGER_COMPARE", "CPP_WARN_POINTER_SIGN", "CPP_WARN_POINTER_TO_ENUM_CAST", "CPP_WARN_POINTER_TO_INT_CAST", "CPP_WARN_POINTER_TYPE_MISMATCH", "CPP_WARN_POTENTIALLY_EVALUATED_EXPRESSION", "CPP_WARN_PRAGMA_ONCE_OUTSIDE_HEADER", "CPP_WARN_QUALIFIED_VOID_RETURN_TYPE", "CPP_WARN_REDECLARED_CLASS_MEMBER", "CPP_WARN_REDUNDANT_CONSTEVAL_IF", "CPP_WARN_REINTERPRET_BASE_CLASS", "CPP_WARN_REORDER_INIT_LIST", "CPP_WARN_RETURN_STACK_ADDRESS", "CPP_WARN_RETURN_TYPE", "CPP_WARN_RETURN_TYPE_C_LINKAGE", "CPP_WARN_SELF_ASSIGN_FIELD", "CPP_WARN_SHIFT_COUNT_NEGATIVE", "CPP_WARN_SHIFT_COUNT_OVERFLOW", "CPP_WARN_SHIFT_NEGATIVE_VALUE", "CPP_WARN_SHIFT_OP_PARENTHESES", "CPP_WARN_SHIFT_OVERFLOW", "CPP_WARN_SINGLE_BIT_BITFIELD_CONSTANT_CONVERSION", "CPP_WARN_SIZEOF_ARRAY_ARGUMENT", "CPP_WARN_SIZEOF_ARRAY_DECAY", "CPP_WARN_SIZEOF_ARRAY_DIV", "CPP_WARN_SIZEOF_POINTER_DIV", "CPP_WARN_SIZEOF_POINTER_MEMACCESS", "CPP_WARN_STATIC_INLINE_EXPLICIT_INSTANTIATION", "CPP_WARN_STATIC_LOCAL_IN_INLINE", "CPP_WARN_STATIC_SELF_INIT", "CPP_WARN_STRING_COMPARE", "CPP_WARN_STRING_PLUS_CHAR", "CPP_WARN_STRING_PLUS_INT", "CPP_WARN_STRLCPY_STRLCAT_SIZE", "CPP_WARN_STRNCAT_SIZE", "CPP_WARN_SWITCH", "CPP_WARN_SWITCH_BOOL", "CPP_WARN_TAUTOLOGICAL_CONSTANT_COMPARE", "CPP_WARN_TAUTOLOGICAL_CONSTANT_OUT_OF_RANGE_COMPARE", "CPP_WARN_TAUTOLOGICAL_POINTER_COMPARE", "CPP_WARN_TAUTOLOGICAL_UNDEFINED_COMPARE", "CPP_WARN_TENTATIVE_DEFINITION_INCOMPLETE_TYPE", "CPP_WARN_TRIGRAPHS", "CPP_WARN_TYPEDEF_REDEFINITION", "CPP_WARN_TYPENAME_MISSING", "CPP_WARN_TYPE_SAFETY", "CPP_WARN_UNDEFINED_BOOL_CONVERSION", "CPP_WARN_UNDEFINED_INLINE", "CPP_WARN_UNDEFINED_INTERNAL", "CPP_WARN_UNDEFINED_VAR_TEMPLATE", "CPP_WARN_UNEVALUATED_EXPRESSION", "CPP_WARN_UNGUARDED_AVAILABILITY_NEW", "CPP_WARN_UNICODE", "CPP_WARN_UNICODE_HOMOGLYPH", "CPP_WARN_UNICODE_WHITESPACE", "CPP_WARN_UNICODE_ZERO_WIDTH", "CPP_WARN_UNKNOWN_DIRECTIVES", "CPP_WARN_UNKNOWN_ESCAPE_SEQUENCE", "CPP_WARN_UNQUALIFIED_STD_CAST_CALL", "CPP_WARN_UNSEQUENCED", "CPP_WARN_UNSUPPORTED_AVAILABILITY_GUARD", "CPP_WARN_UNSUPPORTED_FRIEND", "CPP_WARN_UNUSED_COMPARISON", "CPP_WARN_UNUSED_RESULT", "CPP_WARN_UNUSED_VALUE", "CPP_WARN_UNUSED_VOLATILE_LVALUE", "CPP_WARN_USER_DEFINED_LITERALS", "CPP_WARN_VARARGS", "CPP_WARN_VEXING_PARSE", "CPP_WARN_VISIBILITY", "CPP_WARN_VOID_POINTER_TO_ENUM_CAST", "CPP_WARN_VOID_POINTER_TO_INT_CAST", "CPP_WARN_VOID_PTR_DEREFERENCE", "CPP_WARN_WRITABLE_STRINGS", "CPP_WARN_XOR_USED_AS_POW", "STI_FRIENDS", "STI_SPECIAL_MEMBER_FUNCTIONS", "STI_UNUSED"], "excludes": [], "name": "Background", "parents": [], "tags": []}