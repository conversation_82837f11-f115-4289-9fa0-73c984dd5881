/****************************************************************************************************
 *
 * FILE NAME:  RegAccessIF.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.13
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	13-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Global.h"
#include "RegAccessIF.h"
#include "Mlib.h"
#include "PrmTable.h"

extern UINT16   gEepromVersion;
/****************************************************************************************************
 * DESCRIPTION:
 *            Get a long value from any type
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 prmGetLongValue(void *pSrc, UINT16 bSize, BOOL sign)
{
	if(sign != FALSE)
	{
		if(bSize == 4){ return *((INT32*)pSrc);}
		if(bSize == 2){ return (INT32)*((INT16*)pSrc);}
		if(bSize == 1){ return (INT32)*((INT8*)pSrc);}
	}
	else
	{
		if(bSize == 4){ return *((INT32*)pSrc);}
		if(bSize == 2){ return (INT32)*((UINT16*)pSrc);}
		if(bSize == 1){ return (INT32)*((UINT8*)pSrc);}
	}
	return 0;
}


/****************************************************************************************************
 * DESCRIPTION:
 *            Set a long value to any type
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void prmSetLongValue(void *pDst, INT32 src, UINT16 bSize)
{
	if(bSize == 4){ *((INT32*)pDst) = src;}
	else if(bSize == 2){ *((UINT16*)pDst) = (UINT16)src;}
	else if(bSize == 1){ *((UINT8*)pDst) = (UINT8)src;}
	else {	;}
}


/****************************************************************************************************
 * DESCRIPTION:
 *            Check the data range
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE PRM_RSLT prmCheckDataLimits(PRM_ATTR *PrmAttr, INT32 *value)
{
	PRM_RSLT	rslt;
	UINT16		i, sz;
	VARIANT 	min, max, val, cval;
	UINT32		mask;

	rslt = PRM_RSLT_SUCCESS;

	if(PRMDEF_BASENBL == PrmAttr->FormatType)
	{
		val.lVal = 0;
		sz = PrmAttr->DataLength*8;
		for(i = 0; i < sz; i += 4)
		{
			mask = 0xF << i;
			min.lVal = PrmAttr->LowerLimit & mask;
			max.lVal = PrmAttr->UpperLimit & mask;
			cval.lVal = *value & mask;
			if(cval.ulVal < min.ulVal)
			{
				cval.ulVal = min.ulVal;
				rslt = PRM_RSLT_LIMIT_ERR;
			}
			else if(cval.ulVal > max.ulVal)
			{
				cval.ulVal = max.ulVal;
				rslt = PRM_RSLT_LIMIT_ERR;
			}
			val.lVal |= cval.lVal;
		}
	}
	else
	{
		val.lVal = *value;
		min.lVal = PrmAttr->LowerLimit;
		max.lVal = PrmAttr->UpperLimit;
		mask = (4 - PrmAttr->DataLength) * 8;

		if(PrmAttr->Sign)	/* signed value */
		{
			val.lVal = (val.lVal << mask) >> mask;
			if(val.lVal < min.lVal)
			{
				val.lVal = min.lVal;
				rslt = PRM_RSLT_LIMIT_ERR;
			}
			else if(val.lVal > max.lVal)
			{
				val.lVal = max.lVal;
				rslt = PRM_RSLT_LIMIT_ERR;
			}
		}
		else				/* unsigned value */
		{
			val.ulVal = (val.ulVal << mask) >> mask;
			if(val.ulVal < min.ulVal)
			{
				val.ulVal = min.ulVal;
				rslt = PRM_RSLT_LIMIT_ERR;
			}
			else if(val.ulVal > max.ulVal)
			{
				val.ulVal = max.ulVal;
				rslt = PRM_RSLT_LIMIT_ERR;
			}
		}
	}

	*value = val.lVal;
	return rslt;
}


/****************************************************************************************************
 * DESCRIPTION:
 *             Find a same attribute from the table top to the table end
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE BOOL prmCheckSameAttribute(PRM_TBL *TableTop, PRM_TBL *TableEnd)
{
	PRM_TBL	*pTbl;

	for(pTbl = TableTop; pTbl != TableEnd; pTbl++)
	{
		if(pTbl->Attribute == TableEnd->Attribute)
		{
			return TRUE;
		}
	}
	return FALSE;
}


/****************************************************************************************************
 * DESCRIPTION:
 *           The parameters need to be checked after power reboot
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE UINT32	prmBootWarnChkPdef(			PRM_ATTR   *PrmAttr,UINT32	NewData,
										                 UINT32	OldData, ALARM	*AlmMngr )		
{
	UINT32	err = FALSE;
	UINT32	ChkReboot = 0xFFFFFFFF;

	if ( PrmAttr->NeedReboot != TRUE )
	{
		ChkReboot = 0;
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Set check reboot mask 																		*/
/*--------------------------------------------------------------------------------------------------*/
#if 0
	else if ( (&pndef_gnmode) == PrmAttr 	/* Pn10B */ )
	{
		ChkReboot = 0xFFF0; 
	}
	else if ( (&pndef_DatLevel) == PrmAttr 	/* Pn170 */ )
	{
		ChkReboot = 0x00FF;
	}
	else if ( (&pndef_tfuncsw) == PrmAttr 	/* Pn408 */ )
	{
		ChkReboot = 0x00F0;
	}
	else if ( (&pndef_b_prmD) == PrmAttr 	/* Pn00D */ )
	{
		ChkReboot = 0x0FFF;
	}
	else if ( (&pndef_syssw3) == PrmAttr 	/* PnE0D */ )
	{
		ChkReboot = ~(0x0022);
	}
#endif
/*--------------------------------------------------------------------------------------------------*/
	err = (NewData ^ OldData) & ChkReboot;

	if( err != 0 )
	{
		ALMSetServoAlarm( AlmMngr, WRN_BOOT );
	}
/*--------------------------------------------------------------------------------------------------*/
	return (err);
}

/****************************************************************************************************
 * DESCRIPTION:
 *            Load system default value 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PrmLoadDeviceDefaultValues(BASE_CFG_PRM *PnCfgPrm,UINT16 AxisCobID, UINT16 HardwareVer)
{
    UINT16 Index = (HardwareVer & 0x00FF)- (BASE_DEVICE_HARDWARE_VERSION & 0x00FF);
    
    if(Index > (DevPrmTblNum-1))
    {
      return;
    }
    
    if(AxisCobID > 1)
    {
      return;
    }
    
    PnCfgPrm->DevType = DevPrmTbl[Index].Attribute[AxisCobID].DevType;
    PnCfgPrm->DevVolt = DevPrmTbl[Index].Attribute[AxisCobID].DevVolt;
    PnCfgPrm->DevRatW = DevPrmTbl[Index].Attribute[AxisCobID].DevRatW;
    PnCfgPrm->DevMaxW = DevPrmTbl[Index].Attribute[AxisCobID].DevMaxW;
    PnCfgPrm->DevRatCur = DevPrmTbl[Index].Attribute[AxisCobID].DevRatCur;
    PnCfgPrm->DevMaxCur = DevPrmTbl[Index].Attribute[AxisCobID].DevMaxCur; 
}




/****************************************************************************************************
 * DESCRIPTION:
 *            Load joint default value 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PrmLoadDefaultValues(void)
{
    UINT16 Index = (DEVICE_HARDWARE_VERSION -BASE_DEVICE_HARDWARE_VERSION);
    
    if(Index > (JointPrmTblNum))
    {
      Index = 0;
    }
    
	JointPrm  *pJointPrm = &JointPrmTbl[Index].RatPow;

    for(UINT16 i = 0; i< JointPrmAttrNum; i++)
	{
		*pJointPrm->addr =  pJointPrm->value;
		pJointPrm++;
	}
}
/****************************************************************************************************
 * DESCRIPTION:
 *            Load system default value 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PrmLoadSystemDefaultValues(REGIF_CONFIG_T* RegCfg)
{
	void		*pDstData;
	PRM_TBL		*pTbl;
	UINT16		usSize, i, ArraySize;

	for(pTbl = RegCfg->PrmListTable; pTbl->Number != 0xFFFF; pTbl++)
	{
		usSize = pTbl->Attribute->DataLength;
		if( (usSize != 0) && (NULL != pTbl->Attribute->GetRamPtrCallback) )
		{
			ArraySize = (pTbl->Attribute->NumberOfArray != 0) ? 
						pTbl->Attribute->NumberOfArray : 1;
			for(i = 0; i < ArraySize; i++)
			{
				/* Get Parameter ram address */
				pDstData = pTbl->Attribute->GetRamPtrCallback(i, RegCfg->GetRamPtrParam);
				/* Copy to Parameter ram */
				prmSetLongValue(pDstData, pTbl->Attribute->DefaultValue, usSize);
			}
		}
	}
}


/****************************************************************************************************
 * DESCRIPTION:
 *            Load system default value 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PrmLoadMotorDefaultValues(REGIF_CONFIG_T* RegCfg)
{
	void		*pDstData;
	PRM_TBL		*pTbl;
	UINT16		usSize, i, ArraySize;

	for(pTbl = RegCfg->PrmListTable; pTbl->Number <= 0xFFFF; pTbl++)
	{
		usSize = pTbl->Attribute->DataLength;
		if( (usSize != 0) && (NULL != pTbl->Attribute->GetRamPtrCallback) )
		{
			ArraySize = (pTbl->Attribute->NumberOfArray != 0) ? 
						pTbl->Attribute->NumberOfArray : 1;
			for(i = 0; i < ArraySize; i++)
			{
				/* Get Parameter ram address */
				pDstData = pTbl->Attribute->GetRamPtrCallback(i, RegCfg->GetRamPtrParam);
				/* Copy to Parameter ram */
				prmSetLongValue(pDstData, pTbl->Attribute->DefaultValue, usSize);
			}
		}
	}
}

/****************************************************************************************************
 * DESCRIPTION:
 *           Store system default value 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PrmStoreSystemDefaultValues(REGIF_CONFIG_T* RegCfg,  BOOL bAxCommon)
{
	PRM_TBL 	*pTbl;
	void		*pRamV;
	UINT16		EepAddress, offset;
	UINT16		checksum;
	UINT16		wdSize, i, ArraySize;
	VARIANT  	eepVal, rdVal;
	BOOL		skip;
	EEP_HEADER_INFO  eep_header;

	checksum = 0;
	offset = (bAxCommon != FALSE) ?
			EEP_CMNPRM_ADDRESS : RegCfg->EepromBlockAddress;

	for(pTbl = RegCfg->PrmListTable; pTbl->Number != 0xFFFF; pTbl++)
	{
		wdSize = (pTbl->Attribute->DataLength+1)/2;
		skip = prmCheckSameAttribute(RegCfg->PrmListTable, pTbl);
		if( (skip == FALSE)
			&& (bAxCommon == pTbl->Attribute->AxisCommon) )
		{
			EepAddress = offset + pTbl->Attribute->EepromAddress;
			ArraySize = (pTbl->Attribute->NumberOfArray != 0) ?
						pTbl->Attribute->NumberOfArray : 1;
			for(i = 0; i < ArraySize; i++)
			{
				/* Read the current value */
				rdVal.lVal = 0;
				EepdevReadValues(EepAddress, rdVal.usArray, wdSize);

				if(ACCLVL_SYSTEM > pTbl->Attribute->AccessLevel)
				{
					/* Read the default value */
					eepVal.lVal = 0;
					EepdevReadValues(EepAddress+(ArraySize*wdSize*2), eepVal.usArray, wdSize);

					/* Write the default value */
					if(rdVal.lVal != eepVal.lVal)
					{
						/* write to EEPROM	*/
						EepdevWriteNoSumValues(EepAddress, eepVal.usArray, wdSize);
						/* write to RAM */
						if(NULL != pTbl->Attribute->GetRamPtrCallback)
						{
							pRamV = pTbl->Attribute->GetRamPtrCallback(
												0, RegCfg->GetRamPtrParam);
							prmSetLongValue(pRamV, eepVal.lVal, wdSize*2);
						}
					}
				}
				else
				{	/* Skip a parameter writing. */
					eepVal.lVal = rdVal.lVal;
				}

				checksum = (USHORT)(checksum
						 + eepVal.usArray[0] + eepVal.usArray[1]);
				EepAddress += (USHORT)(wdSize*2);
			}
		}
	}

	/* Write values */
	EepdevReadValues((offset + sizeof(eep_header.Checksum)), eepVal.usArray, 1);
	checksum = (UINT16)(-(checksum + eepVal.usArray[0]));
	EepdevWriteNoSumValues(offset, (UINT16*)&checksum, 1);

	ALMSetServoAlarm( RegCfg->AlmMngr, WRN_BOOT );		
}


/****************************************************************************************************
 * DESCRIPTION:
 *			 Load stored parameters in EEPROM
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 PrmLoadEepromValues(REGIF_CONFIG_T *RegCfg, UINT16 devID, BOOL bAxCommon)
{
	void		*pDstData;
	VARIANT 	eepVal;
	UINT16		i, ArraySize;
	PRM_TBL 	*pTbl;
	INT32		eRslt;
	UINT16		usEepAdr, usSize;
	UINT16		checksum, sizeB, eep_adr,hardware_adr,EepromVersion,hardwareVersion;
	UINT16		offset;
	BOOL		skip;
	EEP_HEADER_INFO   eep_header;

	offset = (bAxCommon != FALSE) ? 
			EEP_CMNPRM_ADDRESS : RegCfg->EepromBlockAddress;
    
	eep_adr = offset + EEP_MAP_HEADER_INFO_LENGTH + 2;
	eRslt = EepdevReadValues(eep_adr, &EepromVersion, 1);
	if(0 != eRslt)
	{
		return eRslt;
	}
    if(EepromVersion != gEepromVersion)
    {
        return PRM_RSLT_CONDITION_ERR;
    }
    

	eep_adr = offset + EEP_MAP_HEADER_INFO_LENGTH ;
	eRslt = EepdevReadValues(eep_adr, &checksum, 1);
	if(0 != eRslt)
	{
		return eRslt;
	}

	eep_adr += sizeof(eep_header.Checksum);
	eRslt = EepdevReadValues(eep_adr, &sizeB, 1);
	if(0 != eRslt)
	{
		return eRslt;
	}
	checksum += sizeB;

	pTbl = RegCfg->PrmListTable;
	while(pTbl->Number != 0xFFFF)
	{
		usEepAdr = pTbl->Attribute->EepromAddress + offset;
		usSize = (pTbl->Attribute->DataLength+1)/2;
		skip = prmCheckSameAttribute(RegCfg->PrmListTable, pTbl);

		if( (skip == FALSE)
			&& (bAxCommon == pTbl->Attribute->AxisCommon)
			&& (pTbl->Attribute->EepromDevice == devID) )
		{

			ArraySize = (pTbl->Attribute->NumberOfArray != 0) ? 
						pTbl->Attribute->NumberOfArray : 1;
			for(i = 0; i < ArraySize; i++)
			{
				eepVal.lVal = 0;
				/* Read stored value from EEPROM */
				EepdevReadValues(usEepAdr, eepVal.usArray, usSize);
				
				if(NULL != pTbl->Attribute->GetRamPtrCallback)
				{
					/* Get Parameter ram address */
					pDstData = pTbl->Attribute->GetRamPtrCallback(i, 
															RegCfg->GetRamPtrParam);

					/* EEPROM -> RAM */
					MlibCopyByteMemory(pDstData, &eepVal, pTbl->Attribute->DataLength);
				}

				/* Add check sum value */
				checksum = (USHORT)(checksum + eepVal.usArray[0] + eepVal.usArray[1]);
				
				usEepAdr += (usSize*4);
			}
		}
		pTbl++; 
	}

	return (INT32)eRslt;
}


/****************************************************************************************************
 * DESCRIPTION:
 *			 Load stored parameters in EEPROM
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 PrmLoadMotorEepromValues(REGIF_CONFIG_T *RegCfg, UINT16 devID, BOOL bAxCommon)
{
	void		*pDstData;
	VARIANT 	eepVal;
	UINT16		i, ArraySize;
	PRM_TBL 	*pTbl;
	INT32		eRslt;
	UINT16		usEepAdr, usSize;
	UINT16		checksum, sizeB, eep_adr,hardware_adr,EepromVersion,hardwareVersion;
	UINT16		offset;
	BOOL		skip;
	EEP_HEADER_INFO   eep_header;

	offset = (bAxCommon != FALSE) ? 
			EEP_CMNPRM_ADDRESS : RegCfg->EepromBlockAddress;
    
	eep_adr = offset + EEP_MAP_HEADER_INFO_LENGTH + 2;
	eRslt = EepdevReadValues(eep_adr, &EepromVersion, 1);
	if(0 != eRslt)
	{
		return eRslt;
	}
    if(EepromVersion != gEepromVersion)
    {
        return PRM_RSLT_CONDITION_ERR;
    }
    

	eep_adr = offset + EEP_MAP_HEADER_INFO_LENGTH ;
	eRslt = EepdevReadValues(eep_adr, &checksum, 1);
	if(0 != eRslt)
	{
		return eRslt;
	}

	eep_adr += sizeof(eep_header.Checksum);
	eRslt = EepdevReadValues(eep_adr, &sizeB, 1);
	if(0 != eRslt)
	{
		return eRslt;
	}
	checksum += sizeB;

	pTbl = RegCfg->PrmListTable;
	while(pTbl->Number <= 0x0028)
	{
		usEepAdr = pTbl->Attribute->EepromAddress + offset;
		usSize = (pTbl->Attribute->DataLength+1)/2;
		skip = prmCheckSameAttribute(RegCfg->PrmListTable, pTbl);

		if( (skip == FALSE)
			&& (bAxCommon == pTbl->Attribute->AxisCommon)
			&& (pTbl->Attribute->EepromDevice == devID) )
		{

			ArraySize = (pTbl->Attribute->NumberOfArray != 0) ? 
						pTbl->Attribute->NumberOfArray : 1;
			for(i = 0; i < ArraySize; i++)
			{
				eepVal.lVal = 0;
				/* Read stored value from EEPROM */
				EepdevReadValues(usEepAdr, eepVal.usArray, usSize);
				
				if(NULL != pTbl->Attribute->GetRamPtrCallback)
				{
					/* Get Parameter ram address */
					pDstData = pTbl->Attribute->GetRamPtrCallback(i, 
															RegCfg->GetRamPtrParam);

					/* EEPROM -> RAM */
					MlibCopyByteMemory(pDstData, &eepVal, pTbl->Attribute->DataLength);
				}

				/* Add check sum value */
				checksum = (USHORT)(checksum + eepVal.usArray[0] + eepVal.usArray[1]);
				
				usEepAdr += (usSize*4);
			}
		}
		pTbl++; 
	}

	return (INT32)eRslt;
}
/****************************************************************************************************
 * DESCRIPTION:
 *            Calculate all parameters
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PrmRecalcAllParameters(REGIF_CONFIG_T *RegCfg, BOOL bAxCommon)
{
	UINT16		sub, ArraySize;
	PRM_TBL 	*pTbl;
	PRM_RSLT	eRslt, err;
	INT32		value;
	void		*pRamV;
    

	eRslt = PRM_RSLT_SUCCESS;
    
	for(pTbl = RegCfg->PrmListTable; pTbl->Number != 0xFFFF; pTbl++)
	{
//      TestNum = pTbl->Number;
        if((pTbl->Number & 0xFF00) == 0x0700)  // pass 0x07XX parameter   todo:denny :2020.02.18
            continue;
        
		if( (pTbl->Attribute->AxisCommon == bAxCommon) 
			&& (pTbl->Attribute->ReadOnly == FALSE) 
			&& (pTbl->Attribute->PrmCalCallback != NULL) )
		{
			ArraySize = (pTbl->Attribute->NumberOfArray != 0) ? 
						pTbl->Attribute->NumberOfArray : 1;
			for(sub = 0;  sub < ArraySize; sub++)
			{
				/* Read the current parameter value */
				value = pTbl->Attribute->DefaultValue;
				if(NULL != pTbl->Attribute->GetRamPtrCallback)
				{
					pRamV = pTbl->Attribute->GetRamPtrCallback(
												sub, RegCfg->GetRamPtrParam);
					value = prmGetLongValue(pRamV, 
											pTbl->Attribute->DataLength,
											pTbl->Attribute->Sign);
				}

				/* Calculate the internal parameters  */
				err = pTbl->Attribute->PrmCalCallback(PRM_ACCCMD_RECALC, 
											sub, RegCfg->PrmCalParam, &value);
				if(PRM_RSLT_SUCCESS != err)
				{
					eRslt = err;
				}
			}
		}
	}

	return eRslt;
}


/****************************************************************************************************
 * DESCRIPTION:
 *			 Find a table index by parameter number
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_ATTR*   PrmFindTableIndex(REGIF_CONFIG_T *RegCfg, UINT16 PrmNo, UINT16 *ArrayIdx)
{
	PRM_ATTR   	    *pPrmAttr;
	PRM_ATTR 	    *FindAttr;
	PRM_TBL		    *PrmListTable;
	INT32 			low, high, mid;
	INT32 			prm_sz,idx;
	UINT32			end_no;

	low = 0;
	high = RegCfg->NumOfTableEntry - 2;
	FindAttr = NULL;

	while(low <= high)
	{
		mid = (INT32)((UINT32)(low + high) >> 1);
		PrmListTable = &RegCfg->PrmListTable[mid];
		pPrmAttr = (PRM_ATTR*)PrmListTable->Attribute;

		prm_sz = (pPrmAttr->NumberOfArray != 0) ? (pPrmAttr->NumberOfArray-1) : 0;
		end_no = ((prm_sz*pPrmAttr->DataLength) / 2) + PrmListTable->Number;

		if(PrmNo < PrmListTable->Number)
		{
			high = mid - 1;
		}
		else if(PrmNo > end_no)
		{
			low = mid + 1;
		}
		else 
		{
			FindAttr = pPrmAttr;
			idx = (PrmNo - PrmListTable->Number) * 2;
			*ArrayIdx = idx / pPrmAttr->DataLength;
			if((idx % pPrmAttr->DataLength) != 0)
			{
				FindAttr = NULL;
			}
			break;
		}
	}

	return FindAttr;
}


/****************************************************************************************************
 * DESCRIPTION:
 *            Read parameter values
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PrmReadValue(REG_ACC_T *AccessPrm, INT32 *value, UINT16 bSize)
{
	void			*pRamV;
	REGIF_CONFIG_T	*RegCfg;
	PRM_ATTR     	*PrmAttr;
	UINT32			data_sz; 

	RegCfg = AccessPrm->hRegCfg; 
	PrmAttr = AccessPrm->PrmAttr;			

	/* parameter number ? */
	if(NULL == PrmAttr)
	{	return PRM_RSLT_PRMNO_ERR;}

	if( (PrmAttr->NumberOfArray != 0) && 
		(AccessPrm->ArrayIdx >= PrmAttr->NumberOfArray) )
	{	return PRM_RSLT_PRMNO_ERR;}

	/* data size ? */
	data_sz = ((PrmAttr->DataLength == 1) && (bSize > 4))
			? PrmAttr->NumberOfArray : PrmAttr->DataLength;
	if(bSize != data_sz)
	{	return PRM_RSLT_SIZE_ERR;}
	
	/* Read a Parameter value */
	if(NULL != PrmAttr->GetRamPtrCallback)
	{
		pRamV = PrmAttr->GetRamPtrCallback(
						AccessPrm->ArrayIdx, RegCfg->GetRamPtrParam);
		if(data_sz <= 4)
		{
			*value = prmGetLongValue(pRamV, PrmAttr->DataLength, PrmAttr->Sign);
		}
		else
		{
			MlibCopyByteMemory(value, pRamV, data_sz);
		}
	}
	else
	{
		*value = PrmAttr->DefaultValue;
		if(NULL != PrmAttr->PrmCalCallback)
		{	
			PrmAttr->PrmCalCallback(PRM_ACCCMD_READ, 
						AccessPrm->ArrayIdx, RegCfg->PrmCalParam, value);
		}
	}

	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************
 * DESCRIPTION:
 *            Write parameter values
 * RETURNS:
 *
****************************************************************************************************/
#pragma optimize=none 
PUBLIC PRM_RSLT PrmWriteValue(REG_ACC_T *AccessPrm, INT32 *value, UINT16 bSize)
{
	PRM_RSLT		rslt;
	INT32			cval, data_sz; 
	void			*pRamV;
	PRM_ACCCMD		PrmCmd;
	REGIF_CONFIG_T	*RegCfg;
	PRM_ATTR     	*PrmAttr;

	RegCfg = AccessPrm->hRegCfg; 
	PrmAttr = AccessPrm->PrmAttr;	

	/* parameter number ? */
	if(NULL == PrmAttr)
	{	return PRM_RSLT_PRMNO_ERR;}

	if( (PrmAttr->NumberOfArray != 0) && 
		(AccessPrm->ArrayIdx >= PrmAttr->NumberOfArray) )
	{	return PRM_RSLT_PRMNO_ERR;}

	/* data size ? */
	data_sz = ((PrmAttr->DataLength == 1) && (bSize > 4))
			? PrmAttr->NumberOfArray : PrmAttr->DataLength;
	if(bSize != data_sz)
	{	return PRM_RSLT_SIZE_ERR;}

	if(NULL != AccessPrm->IfHandle)
	{
		/* check the read-only flag */
		if(FALSE != PrmAttr->ReadOnly)
		{	return PRM_RSLT_RWACC_ERR;}

		/* check the access level */
		if(RegCfg->SysLevel < PrmAttr->AccessLevel)
		{	return PRM_RSLT_RWACC_ERR;}

		/* check the access right */
		if(RegCfg->WriteAccessLock != FALSE)
		{
			if(RegCfg->WriteAccessOwner != AccessPrm->IfHandle)
			{	return PRM_RSLT_NOACCRIGHT_ERR;}
		}
		else
		{
			RegCfg->WriteAccessOwner = AccessPrm->IfHandle;
		}
	}

	/* check parameter range */
	cval = *value;
	rslt = prmCheckDataLimits(PrmAttr, &cval);
	if(rslt != PRM_RSLT_SUCCESS)
	{	return rslt;}
	
	/* Write a new value to RAM */
	if(NULL != PrmAttr->GetRamPtrCallback)
	{
		pRamV = PrmAttr->GetRamPtrCallback(
							AccessPrm->ArrayIdx, RegCfg->GetRamPtrParam);

		if(bSize > 4)
		{
			MlibCopyByteMemory(pRamV, value, bSize);
		}
		else
		{
			cval = prmGetLongValue(pRamV, bSize, PrmAttr->Sign);
			prmSetLongValue(pRamV, *value, bSize);
		}
	}

	/* Calls back to the registered function of parameter calc.*/
	if(NULL != PrmAttr->PrmCalCallback)
	{
		PrmCmd = (AccessPrm->EepWrite != FALSE) ? 
					PRM_ACCCMD_EEPWRITE : PRM_ACCCMD_WRITE;

		rslt = PrmAttr->PrmCalCallback(PrmCmd, AccessPrm->ArrayIdx, 
											RegCfg->PrmCalParam, value);

        if(PRM_RSLT_NEEDRESET == rslt)
        {
            if(NULL != PrmAttr->GetRamPtrCallback)
			{	prmSetLongValue(pRamV, 0, bSize);}
            
            rslt = PRM_RSLT_SUCCESS;
        }
		else if((rslt < 0) && (bSize <= 4))
		{
			/* if error, restore to previous parameter */
			if(NULL != PrmAttr->GetRamPtrCallback)
			{	prmSetLongValue(pRamV, cval, bSize);}

			PrmAttr->PrmCalCallback(PrmCmd, AccessPrm->ArrayIdx, 
									RegCfg->PrmCalParam, &cval);
		}
	}

	/* Store to eeprom. */
	if((rslt >= 0) && (AccessPrm->EepWrite != FALSE))
	{
		PrmStoreValueToEeprom(RegCfg, PrmAttr, AccessPrm->ArrayIdx);         //delete by jrh 2021/11/9
	}

	RegCfg->RegWrResult = rslt;
	return rslt;
}


/****************************************************************************************************
 * DESCRIPTION:
 *			  Write parameter values to Eeprom
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PrmWriteValueToEeprom(REGIF_CONFIG_T  *RegCfg, 
													PRM_ATTR *PrmAttr, UINT16 ArrayIdx)
{
	PRM_RSLT	rslt;
	UINT16		wrSize;
	void		*pRamV;
	VARIANT 	eepVal;
	UINT16		offset;
	VARIANT 	oldVal; 
	
	if(NULL == PrmAttr)
	{	return PRM_RSLT_PRMNO_ERR;}

	rslt = PRM_RSLT_SUCCESS;

	if( NULL == PrmAttr->GetRamPtrCallback )
	{ /* If callback function is undefined */
		rslt = PRM_RSLT_PRMNO_ERR;
	}
	else if( PRMDEF_NO_EEPROM == PrmAttr->EepromDevice )
	{ /* RAM Only Parameter */
		rslt = PRM_RSLT_RAMONLY;
	}
	else
	{
		pRamV = PrmAttr->GetRamPtrCallback(ArrayIdx, 
											RegCfg->GetRamPtrParam);
		
		wrSize = (USHORT)((PrmAttr->DataLength + 1) / 2);
		offset = (PrmAttr->AxisCommon != FALSE) ? 
					EEP_CMNPRM_ADDRESS : RegCfg->EepromBlockAddress;
		offset += (ArrayIdx * wrSize * 4);
		
		eepVal.lVal = 0;
		MlibCopyByteMemory(&eepVal, pRamV, PrmAttr->DataLength);

		/* Read the current value */
		oldVal.lVal = 0;
		if ( 0 == EepdevReadValues( PrmAttr->EepromAddress + offset, oldVal.usArray, wrSize ) )
		{
			/* Check Need Reboot Warning (A.941) */
			prmBootWarnChkPdef( PrmAttr, 
								eepVal.ulVal,
								oldVal.ulVal,
								RegCfg->AlmMngr );
		}

		switch ( EepdevWriteParameters(PrmAttr->EepromAddress + offset, eepVal.usArray, wrSize ) )
		{
		case 0: /* Parameter change successful */
			rslt = PRM_RSLT_SUCCESS;
			break;

		case 1: /* Parameter is not different */
			rslt = PRM_RSLT_NO_DIFF;
			break;

		default: /* Parameter change error */
			rslt = PRM_RSLT_RWACC_ERR;
			break;
		}

		/* Calls back to the registered function of parameter calc.*/
		if(NULL != PrmAttr->PrmCalCallback)
		{
			eepVal.lVal = prmGetLongValue(pRamV, PrmAttr->DataLength, PrmAttr->Sign);
			PrmAttr->PrmCalCallback(PRM_ACCCMD_EEPWRITE, ArrayIdx, 
											RegCfg->PrmCalParam, &eepVal.lVal);
		}
	}

	return rslt;
}


/****************************************************************************************************
 * DESCRIPTION:
 *			  Write parameter value to Eeprom
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PrmStoreValueToEeprom(REGIF_CONFIG_T  *RegCfg, 
													PRM_ATTR *PrmAttr, UINT16 ArrayIdx)
{
	PRM_RSLT	rslt;
	UINT16		wrSize;
	void		*pRamV;
	VARIANT 	eepVal;
	UINT16		offset;
	VARIANT 	oldVal; 
	
	if(NULL == PrmAttr)
	{	return PRM_RSLT_PRMNO_ERR;}

	rslt = PRM_RSLT_SUCCESS;

	if((NULL != PrmAttr->GetRamPtrCallback) && 
		(PRMDEF_NO_EEPROM != PrmAttr->EepromDevice))
	{
		
		pRamV = PrmAttr->GetRamPtrCallback(ArrayIdx, 
											RegCfg->GetRamPtrParam);
		
		wrSize = (USHORT)((PrmAttr->DataLength + 1) / 2);
		offset = (PrmAttr->AxisCommon != FALSE) ? 
					EEP_CMNPRM_ADDRESS : RegCfg->EepromBlockAddress;
		offset += (ArrayIdx * wrSize * 4);
		
		eepVal.lVal = 0;
		MlibCopyByteMemory(&eepVal, pRamV, PrmAttr->DataLength);

		/* Read the current value */
		oldVal.lVal = 0;
		if ( 0 == EepdevReadValues(PrmAttr->EepromAddress + offset, oldVal.usArray, wrSize ) )
		{
			/* Check Need Reboot Warning */
			prmBootWarnChkPdef( PrmAttr,  eepVal.ulVal, oldVal.ulVal, RegCfg->AlmMngr );
		}

		if( 0 > EepdevWriteParameters(PrmAttr->EepromAddress + offset, eepVal.usArray, wrSize) )
		{
			rslt = PRM_RSLT_RWACC_ERR;
		}
	}

	return rslt;
}

/****************************************************************************************************
 * DESCRIPTION:
 *			  Write motor parameters value to Eeprom
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PrmStoreMotEcnPrmToEeprom(REGIF_CONFIG_T *RegCfg)
{
	PRM_RSLT	rslt;
	PRM_TBL 	*pTbl;
	UINT16		i, ArraySize;
	UINT16		offset, eep_adr,hardward_adr;
	UINT16		EepromVersion;
	UINT16		HardWareVersion;
	INT32		err;
	
	rslt = PRM_RSLT_SUCCESS;
	
	offset = RegCfg->EepromBlockAddress;
	eep_adr = offset + EEP_MAP_HEADER_INFO_LENGTH + 2;
	EepromVersion = gEepromVersion;
	EepdevWriteParameters(eep_adr, &EepromVersion, 1);
	
	hardward_adr = EEP_AXPRM_TOP_ADDRESS + EEP_MAP_HEADER_INFO_LENGTH + 6;
	err = EepdevReadValues(hardward_adr, &HardWareVersion, 1);
	if(err == 0 && HardWareVersion== 0xFFFF)
	{
		HardWareVersion = DEVICE_HARDWARE_VERSION;
	}
	
	for(pTbl = RegCfg->PrmListTable; pTbl->Number <= 0x0100; pTbl++)
	{
		ArraySize = (pTbl->Attribute->NumberOfArray != 0) ?
					pTbl->Attribute->NumberOfArray : 1;
		for(i = 0; i < ArraySize; i++)
		{
			rslt |= PrmStoreValueToEeprom(RegCfg, (PRM_ATTR*)pTbl->Attribute, i);
		}
	}
  
	return rslt;
}
/****************************************************************************************************
 * DESCRIPTION:
 *			  Write all of parameters value to Eeprom
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PrmStoreAllValuesToEeprom(REGIF_CONFIG_T *RegCfg)
{
	PRM_RSLT	rslt;
	PRM_TBL 	*pTbl;
	UINT16		i, ArraySize;
    UINT16		offset, eep_adr,hardward_adr;
	UINT16		EepromVersion;
    UINT16		HardWareVersion;
    INT32       err;
    
	rslt = PRM_RSLT_SUCCESS;
    
    offset = RegCfg->EepromBlockAddress;
	eep_adr = offset + EEP_MAP_HEADER_INFO_LENGTH + 2;
//    EepromVersion = EEP_PRM_VERSION;
    EepromVersion = gEepromVersion;
    EepdevWriteParameters(eep_adr, &EepromVersion, 1);
    
    hardward_adr = EEP_AXPRM_TOP_ADDRESS + EEP_MAP_HEADER_INFO_LENGTH + 6;
    err = EepdevReadValues(hardward_adr, &HardWareVersion, 1);
    if(err == 0 && HardWareVersion== 0xFFFF)
    {
        HardWareVersion = DEVICE_HARDWARE_VERSION;
//        EepdevWriteParameters(hardward_adr, &HardWareVersion, 1);
    }
    
	for(pTbl = RegCfg->PrmListTable; pTbl->Number != 0xFFFF; pTbl++)
	{
		ArraySize = (pTbl->Attribute->NumberOfArray != 0) ?
					pTbl->Attribute->NumberOfArray : 1;
		for(i = 0; i < ArraySize; i++)
		{
            rslt |= PrmStoreValueToEeprom(RegCfg, (PRM_ATTR*)pTbl->Attribute, i);
		}
	}
  
	return rslt;
}

/****************************************************************************************************
 * DESCRIPTION:
 *			  Write all of parameters value to Eeprom
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PrmStoreAllValuesToEepromPro(REGIF_CONFIG_T *RegCfg)
{
	PRM_RSLT	rslt;
	PRM_TBL 	*pTbl;
	UINT16		i, ArraySize;
    UINT16		offset, eep_adr,hardward_adr;
	UINT16		EepromVersion;
    UINT16		HardWareVersion;
    INT32       err;
    
	rslt = PRM_RSLT_SUCCESS;
    
    offset = RegCfg->EepromBlockAddress;
	eep_adr = offset + EEP_MAP_HEADER_INFO_LENGTH + 2;
//    EepromVersion = EEP_PRM_VERSION;
    EepromVersion = gEepromVersion;
    EepdevWriteParameters(eep_adr, &EepromVersion, 1);
    
//    hardward_adr = EEP_AXPRM_TOP_ADDRESS + EEP_MAP_HEADER_INFO_LENGTH + 6;
//    err = EepdevReadValues(hardward_adr, &HardWareVersion, 1);
//    if(err == 0 )
//    {
//        HardWareVersion = DEVICE_HARDWARE_VERSION;
//        EepdevWriteParameters(hardward_adr, &HardWareVersion, 1);
//    }
    
	for(pTbl = RegCfg->PrmListTable; pTbl->Number != 0xFFFF; pTbl++)
	{
		ArraySize = (pTbl->Attribute->NumberOfArray != 0) ?
					pTbl->Attribute->NumberOfArray : 1;
		for(i = 0; i < ArraySize; i++)
		{
            rslt |= PrmStoreValueToEeprom(RegCfg, (PRM_ATTR*)pTbl->Attribute, i);
		}
	}
  
	return rslt;
}

/****************************************************************************************************
 * DESCRIPTION:
 *			 Write  device info in EEPROM
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 WriteDevInfoEeprom(UINT16 *Value)
{

	INT32		eRslt;
	UINT16		hardware_adr, hardwareVersion;

    hardware_adr = EEP_AXPRM_TOP_ADDRESS + EEP_MAP_HEADER_INFO_LENGTH + 6;
    eRslt = EepdevReadValues(hardware_adr, &hardwareVersion, 1);
    if(eRslt == 0 )
    {
        hardwareVersion = DEVICE_HARDWARE_VERSION;
        EepdevWriteParameters(hardware_adr, &hardwareVersion, 1);
    }    
    
	eRslt = EepdevReadValues(hardware_adr, &hardwareVersion, 1);
	if(0 != eRslt)
	{
		return eRslt;
	}
    
    if(hardwareVersion != DEVICE_HARDWARE_VERSION)
    {
        return PRM_RSLT_RWACC_ERR;
    }    

    *Value = hardwareVersion;
    
	return (INT32)eRslt;
}

/****************************************************************************************************
 * DESCRIPTION:
 *			 Read  device info in EEPROM
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 ReadDevInfoEeprom(UINT16 *Value)
{

	INT32		eRslt = 0;
	UINT16		hardware_adr, hardwareVersion;

    hardware_adr = EEP_AXPRM_TOP_ADDRESS + EEP_MAP_HEADER_INFO_LENGTH + 6;
    
    EepdevReadValues(hardware_adr, &hardwareVersion, 1);
    
    *Value = hardwareVersion;
    
    if(hardwareVersion != DEVICE_HARDWARE_VERSION)
    {
        return PRM_RSLT_RWACC_ERR;
    }    

	return (INT32)eRslt;
}
/****************************************************************************************************
 * DESCRIPTION:
 *			Reload a Parameter value from Eeprom
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PrmReLoadValueFromEeprom(REGIF_CONFIG_T  *RegCfg, 
															PRM_ATTR *PrmAttr, UINT16 ArrayIdx)
{
	PRM_RSLT	rslt;
	UINT16		wrSize;
	INT32		cval;
	void		*pRamV;
	VARIANT 	eepVal;
	UINT16		offset;
	
	if(NULL == PrmAttr)
	{	return PRM_RSLT_PRMNO_ERR;}

	rslt = PRM_RSLT_SUCCESS;
	cval = 0;

	if(PRMDEF_NO_EEPROM != PrmAttr->EepromDevice)
	{		
		wrSize = (USHORT)((PrmAttr->DataLength + 1) / 2);
		offset = (PrmAttr->AxisCommon != FALSE) ? 
					EEP_CMNPRM_ADDRESS : RegCfg->EepromBlockAddress;
		offset += (ArrayIdx * wrSize * 4);
		
		eepVal.lVal = 0;

		if(0 != EepdevReadValues(PrmAttr->EepromAddress + offset, eepVal.usArray, wrSize))
		{
			return PRM_RSLT_RWACC_ERR;
		}

		/* Write a new value to RAM */
		if(NULL != PrmAttr->GetRamPtrCallback)
		{
			pRamV = PrmAttr->GetRamPtrCallback(ArrayIdx, 
										RegCfg->GetRamPtrParam);

			MlibCopyByteMemory(pRamV, &eepVal, PrmAttr->DataLength);
			cval = prmGetLongValue(pRamV, wrSize*2, PrmAttr->Sign);
		}

		/* Calls back to the registered function of parameter calc.*/
		if(NULL != PrmAttr->PrmCalCallback)
		{
			rslt = PrmAttr->PrmCalCallback(PRM_ACCCMD_WRITE, ArrayIdx,
										        RegCfg->PrmCalParam, &cval);
		}
	}

	return rslt;
}



/****************************************************************************************************
* DESCRIPTION:
*			 Load a Parameter value from Eeprom (To simple)
* RETURNS:
*
****************************************************************************************************/											
PUBLIC PRM_RSLT PrmLoadValueFromEeprom(REGIF_CONFIG_T  *RegCfg, PRM_ATTR *PrmAttr, 
															             UINT16 ArrayIdx, INT32* pVal)
{
	PRM_RSLT	rslt;
	UINT16		wrSize;
	VARIANT 	eepVal;
	UINT16		offset;
	
	if(NULL == PrmAttr)
	{	return PRM_RSLT_PRMNO_ERR;}

	rslt = PRM_RSLT_SUCCESS;

	if(PRMDEF_NO_EEPROM != PrmAttr->EepromDevice)
	{		
		wrSize = (USHORT)((PrmAttr->DataLength + 1) / 2);
		offset = (PrmAttr->AxisCommon != FALSE) ? 
					EEP_CMNPRM_ADDRESS : RegCfg->EepromBlockAddress;
		offset += (ArrayIdx * wrSize * 4);
		
		eepVal.lVal = 0;

		if(0 != EepdevReadValues(PrmAttr->EepromAddress + offset, eepVal.usArray, wrSize))
		{
			return PRM_RSLT_RWACC_ERR;
		}
		
		MlibCopyByteMemory(pVal, &eepVal, PrmAttr->DataLength);
	}

	return rslt;
}


/****************************************************************************************************
 * DESCRIPTION:
 *			 Get a parameter attribute by parameter CoeIndex
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_ATTR*   PrmGetAttrByCoeIndex(REGIF_CONFIG_T *RegCfg, UINT32 Index)
{
	PRM_ATTR   	    *pPrmAttr;
	PRM_ATTR 	    *FindAttr;
	PRM_TBL		    *PrmListTable;
	UINT32 			low, high, mid;
	UINT32 			idx;

	low = 0;
	high = RegCfg->NumOfTableEntry - 2;
	FindAttr = NULL;

	while(low <= high)
	{
		mid = (low + high) >> 1;
		PrmListTable = &RegCfg->PrmListTable[mid];
		pPrmAttr = (PRM_ATTR*)PrmListTable->Attribute;

		if(Index < PrmListTable->CoeIndex)
		{
			high = mid - 1;
		}
		else if(Index > PrmListTable->CoeIndex)
		{
			low = mid + 1;
		}
		else 
		{
			FindAttr = pPrmAttr;
			break;
		}
	}

	return FindAttr;
}

/****************************************************************************************************
 * DESCRIPTION: Get a register attribute by Virtual Memory Address
 *           
 * RETURNS:
 *
****************************************************************************************************/
#pragma optimize=none 
PUBLIC PRM_ATTR *RegMngrGetAttrByMemAddr(REG_ACC_T *AccessPrm)
{
	 UINT32			 ax_no;
	 UINT32			 CoeIndex;
	 AXIS_HANDLE     *Axis;

	Axis = GetAxisHandle(0);
	AccessPrm->hRegCfg = NULL;
	AccessPrm->PrmAttr = NULL;

	ax_no = (AccessPrm->MemAddr & 0x08000000)?1:0;
	CoeIndex = AccessPrm->MemAddr&0xF7FFFFFF;

	if((AccessPrm->MemAddr & 0xF0000000) == 0xF0000000)
	{
		AccessPrm->hRegCfg = Axis[ax_no].RegManager->hFnReg;
		AccessPrm->EepWrite = FALSE;	
	}
	else
	{
		AccessPrm->hRegCfg = Axis[ax_no].RegManager->hPnReg;
		AccessPrm->EepWrite = FALSE;	
	}

	if(NULL != AccessPrm->hRegCfg)
	{
		AccessPrm->PrmAttr = PrmGetAttrByCoeIndex(AccessPrm->hRegCfg, CoeIndex);

	}

	return AccessPrm->PrmAttr;
}


/****************************************************************************************************
 * DESCRIPTION: 
 * 	  Lock to Write Access to Operation registers
 * RETURNS:
 *
 ****************************************************************************************************/
 PUBLIC void RegMngrLockWriteOpeRegister(REG_MANAGER_HANDLE *hRegMngr)
 {
	 if(hRegMngr->hFnReg != NULL)
	 {
		 hRegMngr->hFnReg->WriteAccessLock = TRUE;
	 }
 }

/****************************************************************************************************
 * DESCRIPTION: 
 * 		  Free to Write Access to Operation registers
 * RETURNS:
 *
 ****************************************************************************************************/
 PUBLIC void RegMngrFreeWriteOpeRegister(REG_MANAGER_HANDLE *hRegMngr)
 {
	 if(hRegMngr->hFnReg != NULL)
	 {
		 hRegMngr->hFnReg->WriteAccessLock = FALSE;
		 hRegMngr->hFnReg->WriteAccessOwner = NULL;
	 }
 }

 /****************************************************************************************************
 * DESCRIPTION: 
 * 		  
 * RETURNS:
 *
 ****************************************************************************************************/
 PUBLIC PRM_RSLT RegMngrGetWriteOpeRegStatus(REG_MANAGER_HANDLE *hRegMngr)
 {
	 if(hRegMngr->hFnReg == NULL)
	 {
		 return PRM_RSLT_SUCCESS;
	 }
	 return hRegMngr->hFnReg->RegWrResult;
 }

/****************************************************************************************************
 * DESCRIPTION: 
 * 		  
 * RETURNS:
 *
 ****************************************************************************************************/
 PUBLIC void RegMngrSetWriteOpeRegStatus(REG_MANAGER_HANDLE *hRegMngr, PRM_RSLT sts)
 {
	 if(hRegMngr->hFnReg != NULL)
	 {
		 hRegMngr->hFnReg->RegWrResult = sts;
	 }
 }


/****************************************************************************************************
 * DESCRIPTION: 
 * 		  Lock to Write Access to Parameters
 * RETURNS:
 *
 ****************************************************************************************************/
 PUBLIC void RegMngrLockWriteParameters(REG_MANAGER_HANDLE *hRegMngr)
 {
	 if(hRegMngr->hPnReg != NULL)
	 {
		 hRegMngr->hPnReg->WriteAccessLock = TRUE;
	 }
 }
 
/****************************************************************************************************
* DESCRIPTION: 
* 	    Free to Write Access to Parameters
* RETURNS:
*
****************************************************************************************************/
 PUBLIC void RegMngrFreeWriteParameters(REG_MANAGER_HANDLE *hRegMngr)
 {
	 if(hRegMngr->hPnReg != NULL)
	 {
		 hRegMngr->hPnReg->WriteAccessLock = FALSE;
		 hRegMngr->hPnReg->WriteAccessOwner = NULL;
	 }
 }

/****************************************************************************************************
* DESCRIPTION: 
* 	  
* RETURNS:
*
****************************************************************************************************/
 PUBLIC BOOL RegMngrGetWritePrmAccessLock(REG_MANAGER_HANDLE *hRegMngr)
 {
	 if(hRegMngr->hPnReg != NULL)
	 {
		 return hRegMngr->hPnReg->WriteAccessLock;
	 }
	 return FALSE;
 }





