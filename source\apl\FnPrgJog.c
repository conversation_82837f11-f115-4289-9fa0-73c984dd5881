/****************************************************************************************************
 *
 * FILE NAME:  FnPrgJog.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Global.h"
#include "FnPrgJog.h"
#include "HardApi.h"
#include "Mlib.h"


/****************************************************************************************************
 * DESCRIPTION:
 *		Program Jog mode Command
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT PnCmdPrgJog( void *AxisHdl )
{
  
	AXIS_HANDLE *Axis;


	Axis = (AXIS_HANDLE*)AxisHdl;
	FUN_CMN_CONTROL 	*FnCmnCtrl;
	PJOGV *PJogV;

        
	FnCmnCtrl = Axis->FnCmnCtrl;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;

	PJogV->Var.PnPrgJogCmd = Axis->Prm->PnAuxPrm.PrgJogCmd;
 
	if(FnCmnCtrl->FnSvonReq == TRUE )
	{
		if(FnCmnCtrl->FnCtrlMcmd != CTRL_MODE_PJOG)
		{
			return( PRM_RSLT_CONDITION_ERR );	
		}
		else if((Axis->Prm->PnAuxPrm.PrgJogCmd & 0x10) == 0)
		{
			FnCmnCtrl->FnSvonReq = FALSE;
//			FnCmnCtrl->FnSvControl = FALSE;
		}
		return PRM_RSLT_SUCCESS;
	}

	if(Axis->Prm->PnAuxPrm.PrgJogCmd & 0x10)
	{
		PrgJogReset( &Axis->BaseCtrls->PJogHdl );
		PJogV->Var.State = PJOG_START;
		PJogV->Var.Step = 0;
		PcalPjogSw(       Axis );		
		PcalPjogDistance( Axis );		
		PcalPjogRotspd(   Axis );		
		PcalPjogAcctime(  Axis );		
		PcalPjogWaitTime( Axis );	
		PcalPjogNum(      Axis );	
		IprmcalPrgJog( &Axis->BaseCtrls->PJogHdl, Axis->BaseLoops->Bprm );
	
		FnCmnCtrl->FnSvControl = TRUE;
		FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_PJOG;
		FnCmnCtrl->FnSensOnReq = TRUE;
		FnCmnCtrl->FnSvonReq = TRUE;
		FnCmnCtrl->JogSpeed = 0;
        PJogV->Var.PnPrgJogFlg = TRUE;
	}

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************
 * DESCRIPTION:
 *		 Program Jog mode Execution
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC  PRM_RSLT CpiPrgJogLoopExec(void *AxisHdl)
{
	AXIS_HANDLE *Axis;
	Axis = (AXIS_HANDLE*)AxisHdl;  
  
	PRM_RSLT rc;
	UINT32 WaitTime;

	PJOGV *PJogV;
	FUN_CMN_CONTROL *FnCmnCtrl;
	SEQ_CTRL_OUT *SeqCtrlOut;

	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;
	FnCmnCtrl = Axis->FnCmnCtrl;
	SeqCtrlOut= Axis->SeqCtrlOut;

	rc = PRM_RSLT_SUCCESS;

	if(FnCmnCtrl->FnSvonReq == FALSE )
	{
		if(Axis->BaseCtrls->BaseEnable == FALSE )
		{
			PJogV->Var.Step = 0;
			FnCmnCtrl->FnSvControl = FALSE;
			FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_NOCMD;
			Axis->Prm->PnAuxPrm.PrgJogCmd = 0;
            PJogV->Var.PnPrgJogFlg = FALSE;
		}
		return PRM_RSLT_SUCCESS;
	}

	switch( PJogV->Var.Step )
	{
		case 0:	
			FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_PJOG;
			PJogV->Var.RoutCmd = PJOGCMD_INIT;
			PrgJogReset( &Axis->BaseCtrls->PJogHdl );
			PJogV->Var.Step = 1;
			break;

		case 1:	/* Servo off	*/
			if( (PJogV->Var.RoutCmd == PJOGCMD_INIT && PJogV->Var.State == PJOG_INIT) ||
				 PJogV->Var.State == PJOG_ABORT || PJogV->Var.State == PJOG_ERROR )
			{
				PJogV->Var.RoutCmd = PJOGCMD_NONE;
			}

			/* Processing state transition processing by servo-on */
			if( Axis->SeqCtrlOut->BaseEnableReq == TRUE )
			{
				PJogV->Var.RoutCmd = PJOGCMD_INIT;
				PJogV->Var.Step = 2;
			}
			break;

		case 2:	/* Servo on (stopped)*/
			if( (PJogV->Var.RoutCmd == PJOGCMD_INIT) && (PJogV->Var.State == PJOG_INIT) )
			{
				PJogV->Var.RoutCmd = PJOGCMD_NONE;
			}
			
			switch( PJogV->Var.PnPrgJogCmd )
			{
				case 0x10:
					
					break;
					
				case 0x11:		/* Program JOG operation start (normal rotation)*/
//					if( !PJogV->Cnst.RevDir && Axis->SeqCtrlOut->BaseEnableReq )
					{
						PJogV->Var.RoutCmd = PJOGCMD_START;
						PJogV->Var.Step = 3;
					}
					break;
					
				case 0x12:		/* Program JOG operation start (reverse rotation)*/
//					if( PJogV->Cnst.RevDir && Axis->SeqCtrlOut->BaseEnableReq )
					{
						PJogV->Var.RoutCmd = PJOGCMD_START;
						PJogV->Var.Step = 3;
					}
					break;

				default:
					break;
			}
			/* Processing transition when the servo is off */
			if( !Axis->SeqCtrlOut->BaseEnableReq )
			{
				PJogV->Var.Step = 1;
			}
			break;

		case 3:	/* Moving */
			if( (PJogV->Var.RoutCmd == PJOGCMD_START) && (PJogV->Var.State == PJOG_START) )
			{
				PJogV->Var.RoutCmd = PJOGCMD_NONE;
			}


			switch( PJogV->Var.PnPrgJogCmd )
			{

			case 0x10:
				PJogV->Var.RoutCmd = PJOGCMD_ABORT;
				break;
            
			case 0x11:		/* Forward rotation	*/
//				if( !PJogV->Cnst.RevDir && Axis->SeqCtrlOut->BaseEnableReq )
				{

				}
				break;
                
			case 0x12:		/* Reverse rotation*/
//				if( PJogV->Cnst.RevDir && Axis->SeqCtrlOut->BaseEnableReq )
				{

				}
				break;
                
			case 0x00:		/* Servo off	*/
				FnCmnCtrl->FnSvonReq = FALSE;
				break;
            
			default:
				break;
			}

			/* Processing transition when the servo is off*/
			if(!Axis->SeqCtrlOut->BaseEnableReq)
			{
				PJogV->Var.Step = 1;
			}

			/* TaskC execution confirmation */
			if( PJogV->Var.TaskCRunFlg == TRUE )
			{
				if( (PJogV->Var.State == PJOG_ABORT) 
				 || (PJogV->Var.State == PJOG_END) 
				 ||	(PJogV->Var.State == PJOG_ERROR) )
				{
					PJogV->Var.RoutCmd = PJOGCMD_NONE;
					PJogV->Var.PnPrgJogCmd &= ~0x0F;
					PJogV->Var.Step = 4;
				}
			}
    
			break;

		case 4:	/* End of operation (post-processing)*/
			if(FnCmnCtrl->SafeStopTime == 65535)
			{
				WaitTime = 10 * 1000; /* 10s Wait	*/
			}
			else
			{
				WaitTime = FnCmnCtrl->SafeStopTime * 1000;	/* Safe stop time Wait */
			}
//			if( KlibGetLongTimerMs( FunExe->LongTimer ) > WaitTime)
			{
				PJogV->Var.Step = 2;
			}
			break;

		default:
			break;
	}

	/* Flag that turns on with TaskC and turns off with Round*/
	PJogV->Var.TaskCRunFlg = FALSE;

	return(rc);
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Program Jog mode Begin
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC PRM_RSLT FnEnterPrgJog( FUNEXE *FunExe, AXIS_HANDLE *Axis )
{
	FUN_CMN_CONTROL *FnCmnCtrl;
	PJOGV *PJogV;

	FnCmnCtrl = Axis->FnCmnCtrl;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;

	if(FnCmnCtrl->State.FnSvCtrlDisable != FALSE)
	{
		return PRM_RSLT_CONDITION_ERR;
	}


	if(FALSE != ALMCheckEachState( Axis->AlmMngr, ALM_PRMUNMATCH ))
	{
		return PRM_RSLT_CONDITION_ERR;
	}

	PrgJogReset( &Axis->BaseCtrls->PJogHdl );
	PJogV->Var.State = PJOG_START;
	PcalPjogSw(       Axis );		
	PcalPjogDistance( Axis );		
	PcalPjogRotspd(   Axis );		
	PcalPjogAcctime(  Axis );		
	PcalPjogWaitTime( Axis );	
	PcalPjogNum(      Axis );	
	IprmcalPrgJog( &Axis->BaseCtrls->PJogHdl, Axis->BaseLoops->Bprm );

	FnCmnCtrl->FnSvControl = TRUE;
	FnCmnCtrl->FnCtrlMcmd  = CTRL_MODE_PJOG;	
	FnCmnCtrl->FnSensOnReq = TRUE;
	FnCmnCtrl->JogSpeed = 0;

	if ( ( PJogV->Cnst.PrmUnMatch == TRUE ) || ( PJogV->Cnst.PrmUnMatch2 == TRUE ) )
	{
		ALMSetServoAlarm( Axis->AlmMngr, ALM_PRMUNMATCH );
	}

	FunExe->HoldFnMode = TRUE;

	return(PRM_RSLT_SUCCESS);
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Program Jog mode End
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void FnLeavePrgJog(FUNEXE *FunExe, AXIS_HANDLE *Axis)
{
	FUN_CMN_CONTROL *FnCmnCtrl;
	PJOGV *PJogV;

	FnCmnCtrl = Axis->FnCmnCtrl;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;

	PJogV->Var.RoutCmd = PJOGCMD_INIT;


	FnCmnCtrl->JogSpeed    = 0;
	FnCmnCtrl->FnSvonReq   = FALSE;
	FnCmnCtrl->FnCtrlMcmd  = CTRL_MODE_NOCMD;
	FnCmnCtrl->FnSensOnReq = FALSE;
	FnCmnCtrl->FnSvControl = FALSE;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Program Jog mode Execution
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC  INT32 FnExecutePrgJog(FUNEXE *FunExe, AXIS_HANDLE *Axis)
{
	PRM_RSLT rc;
	UINT32 WaitTime;

	PJOGV *PJogV;
	FUN_CMN_CONTROL *FnCmnCtrl;
	SEQ_CTRL_OUT *SeqCtrlOut;

	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;
	FnCmnCtrl = Axis->FnCmnCtrl;
	SeqCtrlOut= Axis->SeqCtrlOut;

	rc = PRM_RSLT_SUCCESS;

	switch( FunExe->State )
	{
		case 0:	
			FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_PJOG;
			PJogV->Var.RoutCmd = PJOGCMD_INIT;
			PrgJogReset( &Axis->BaseCtrls->PJogHdl );
			FnCmnCtrl->FnSensOnReq = TRUE;
			FnCmnCtrl->FnSvonReq = FALSE;

			FunExe->State = 1;
			break;

		case 1:	/* Servo off	*/
			if( (PJogV->Var.RoutCmd == PJOGCMD_INIT && PJogV->Var.State == PJOG_INIT) ||
				 PJogV->Var.State == PJOG_ABORT || PJogV->Var.State == PJOG_ERROR )
			{
				PJogV->Var.RoutCmd = PJOGCMD_NONE;
			}

			switch( FunExe->CmdCode )
			{
				/* Servo-on processing */
				case FCMD_SV:

					if( ((Axis->AlmMngr)->Status.AlmFlag != FALSE) || ( SeqCtrlOut->HwbbSts == TRUE) ||
					  (SeqCtrlOut->MainPowerOn == FALSE) || (SeqCtrlOut->OverTrvlSts == TRUE) )
					{
						rc = PRM_RSLT_CONDITION_ERR;
					}
					else
					{
						FnCmnCtrl->FnSvonReq = (~FnCmnCtrl->FnSvonReq) & 0x01;
					}
					break;
				case FCMD_EXEC: case FCMD_CH1: case FCMD_CH2:
				case FCMD_EWRT: case FCMD_MTUP: case FCMD_MTDOWN:
					rc = PRM_RSLT_CONDITION_ERR;
					break;
				default:
					break;
			}
			/* Processing state transition processing by servo-on */
			if( Axis->SeqCtrlOut->BaseEnableReq == TRUE )
			{
				PJogV->Var.RoutCmd = PJOGCMD_INIT;
				FunExe->State = 2;
			}
			break;

		case 2:	/* Servo on (stopped)*/
			if( (PJogV->Var.RoutCmd == PJOGCMD_INIT) && (PJogV->Var.State == PJOG_INIT) )
			{
				PJogV->Var.RoutCmd = PJOGCMD_NONE;
			}
			switch( FunExe->CmdCode )
			{
				case FCMD_NONE:
				case FCMD_STOP:
					break;
				case FCMD_UP:		/* Program JOG operation start (normal rotation)*/
					if( !PJogV->Cnst.RevDir && Axis->SeqCtrlOut->BaseEnableReq )
					{
						PJogV->Var.RoutCmd = PJOGCMD_START;
						KlibRstLongTimer( &FunExe->LongTimer );
						FunExe->State = 3;
					}
					break;
				case FCMD_DOWN:		/* Program JOG operation start (reverse rotation)*/
					if( PJogV->Cnst.RevDir && Axis->SeqCtrlOut->BaseEnableReq )
					{
						PJogV->Var.RoutCmd = PJOGCMD_START;
						KlibRstLongTimer( &FunExe->LongTimer );
						FunExe->State = 3;
					}
					break;
				case FCMD_SV:		/* Servo off */
					FnCmnCtrl->FnSvonReq = FALSE;
					break;
				case FCMD_EXEC: case FCMD_CH1:  case FCMD_CH2:
				case FCMD_EWRT: case FCMD_MTUP: case FCMD_MTDOWN:
					rc = PRM_RSLT_CONDITION_ERR;
					break;
				default:
					break;
			}
			/* Processing transition when the servo is off */
			if( !Axis->SeqCtrlOut->BaseEnableReq )
			{
				FunExe->State = 1;
			}
			break;

		case 3:	/* Moving */
			if( (PJogV->Var.RoutCmd == PJOGCMD_START) && (PJogV->Var.State == PJOG_START) )
			{
				PJogV->Var.RoutCmd = PJOGCMD_NONE;
			}


			switch( FunExe->CmdCode )
			{

			case FCMD_STOP:
				PJogV->Var.RoutCmd = PJOGCMD_ABORT;
				break;
            
			case FCMD_UP:		/* Forward rotation	*/
				if( !PJogV->Cnst.RevDir && Axis->SeqCtrlOut->BaseEnableReq )
				{
					KlibRstLongTimer( &FunExe->LongTimer );
				}
				break;
                
			case FCMD_DOWN:		/* Reverse rotation*/
				if( PJogV->Cnst.RevDir && Axis->SeqCtrlOut->BaseEnableReq )
				{
					KlibRstLongTimer( &FunExe->LongTimer );
				}
				break;
                
			case FCMD_SV:		/* Servo off	*/
				FnCmnCtrl->FnSvonReq = FALSE;
				break;
            
			case FCMD_EXEC: case FCMD_CH1: case FCMD_CH2:
			case FCMD_EWRT: case FCMD_MTUP: case FCMD_MTDOWN:
				rc = PRM_RSLT_CONDITION_ERR;
				break;
            
			case FCMD_NONE:	
			default:

				if(FnCmnCtrl->SafeStopTime == 65535)
				{
					WaitTime = 10 * 1000; /* 10s Wait	*/
				}
				else
				{
					WaitTime = FnCmnCtrl->SafeStopTime * 1000;	/* Safe stop time Wait */
				}
				if( KlibGetLongTimerMs( FunExe->LongTimer ) > WaitTime)
				{
					PJogV->Var.RoutCmd = PJOGCMD_ABORT;
				}
				break;
			}

			/* Processing transition when the servo is off	*/
			if(!Axis->SeqCtrlOut->BaseEnableReq)
			{
				FunExe->State = 1;
			}

			/* TaskC execution confirmation */
			if( PJogV->Var.TaskCRunFlg == TRUE )
			{
				if( (PJogV->Var.State == PJOG_ABORT) 
				 || (PJogV->Var.State == PJOG_END) 
				 ||	(PJogV->Var.State == PJOG_ERROR) )
				{
					PJogV->Var.RoutCmd = PJOGCMD_NONE;
					KlibRstLongTimer( &FunExe->LongTimer );
					FunExe->State = 4;
				}
			}
    
			break;

		case 4:	/* End of operation (post-processing)*/
			if(FnCmnCtrl->SafeStopTime == 65535)
			{
				WaitTime = 10 * 1000; /* 10s Wait	*/
			}
			else
			{
				WaitTime = FnCmnCtrl->SafeStopTime * 1000;	/* Safe stop time Wait */
			}
			if( KlibGetLongTimerMs( FunExe->LongTimer ) > WaitTime)
			{
				FunExe->State = 2;
			}
			break;

		default:
			break;
	}

	/* Flag that turns on with TaskC and turns off with Round*/
	PJogV->Var.TaskCRunFlg = FALSE;

	return(rc);
}




/****************************************************************************************************
 * DESCRIPTION:
 *		 Program JOG operation related switches
 * RETURNS:
 *
****************************************************************************************************/
void PcalPjogSw( void *AxisHdl )
{
	AXIS_HANDLE *Axis;
	PRMDATA *Prm;
	PJOGV *PJogV;

	Axis = (AXIS_HANDLE*)AxisHdl;
	Prm = Axis->Prm;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;

	if( PJogV->Var.State == PJOG_START )
	{
		PJogV->Var.RoutCmd = PJOGCMD_ABORT;
	}
	PJogV->Cnst.Pattern = (INT8) ( Prm->PnCtrlCfgPrm.PjogSW & 0x000F );
	PJogV->Cnst.RevDir = ( PJogV->Cnst.Pattern & 0x01 )? TRUE : FALSE;
	PJogV->Cnst.MTimes = ( PJogV->Cnst.Pattern < 2 )? Prm->PnCtrlCfgPrm.PjogNum : ( Prm->PnCtrlCfgPrm.PjogNum << 1 );
}



/****************************************************************************************************
 * DESCRIPTION:
 *		 Program JOG movement distance [command unit]
 * RETURNS:
 *
****************************************************************************************************/
void PcalPjogDistance( void *AxisHdl )
{
	AXIS_HANDLE *Axis;
	PRMDATA *Prm;
	PJOGV *PJogV;

	Axis = (AXIS_HANDLE*)AxisHdl;
	Prm = Axis->Prm;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;

	if( PJogV->Var.State == PJOG_START )
	{
		PJogV->Var.RoutCmd = PJOGCMD_ABORT;
	}
	PJogV->Cnst.Distance = Prm->PnCtrlCfgPrm.PjogDist;
}



/****************************************************************************************************
 * DESCRIPTION:
 *		 Program JOG movement speed[min-1]
 * RETURNS:
 *
****************************************************************************************************/
void PcalPjogRotspd( void *AxisHdl )
{
	INT32	PjogSpdP1;
	INT32	PjogSpdP2;
	INT32	kx1, sx1;
	INT32	kx2, sx2;
	INT32	Egear_a,Egear_b;

	AXIS_HANDLE *Axis;
	PRMDATA *Prm;
	BPRMDAT *Bprm;
	PJOGV	*PJogV;
	PCMKPV  *PJogPcmk;

	Axis = (AXIS_HANDLE*)AxisHdl;
	Bprm = Axis->BaseLoops->Bprm;
	Prm  = Axis->Prm;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;
	PJogPcmk = &Axis->BaseCtrls->PJogHdl.PJogPcmk;
	Egear_a = 1;
	Egear_b = 1;

	if( (Prm->PnMotPrm.MotType&0xFF) == MOTTYPE_ROTARY )
	{
		PjogSpdP1 = (10 * Prm->PnCtrlCfgPrm.PjogSpd);
		if( PJogV->Var.State == PJOG_START )
		{
			PJogV->Var.RoutCmd = PJOGCMD_ABORT;
		}

		PJogV->Cnst.MaxSpd = Prm->PnCtrlCfgPrm.PjogSpd * Bprm->Kspdrpm;
		PJogV->Cnst.AccTime = ( Prm->PnCtrlCfgPrm.PjogAccT == 0 )? 2000 : ( Prm->PnCtrlCfgPrm.PjogAccT * 1000 );
		MlibPcalaPcmdMaker( PJogV->Cnst.MaxSpd, PJogV->Cnst.AccTime,
									PJogV->Cnst.AccTime, PS_CYCLEUS, &PJogPcmk->P );
		kx1 = MlibScalKxgain( Egear_a, 256, Egear_b, &sx1, -1 );
		kx2 = MlibScalKskxkx( Bprm->KMotSpdConv, PjogSpdP1, 1, &sx2, -1 );
		PjogSpdP2 = MlibScalKskskx( kx1, kx2, 256, NULL, 24 );
		PjogSpdP2 = MlibGAINRD( PjogSpdP2 );
		if( PjogSpdP2 < LMTLOWSPEED )
		{
			PJogV->Cnst.PrmUnMatch = TRUE;
		}
		else
		{
			PJogV->Cnst.PrmUnMatch = FALSE;
		}
	}
	else
	{
		PjogSpdP1 = (10 * Prm->PnCtrlCfgPrm.PjogSpd);
		if( PJogV->Var.State == PJOG_START )
		{
			PJogV->Var.RoutCmd = PJOGCMD_ABORT;
		}

		PJogV->Cnst.MaxSpd = Prm->PnCtrlCfgPrm.PjogSpd * Bprm->Kspdrpm;
		PJogV->Cnst.AccTime = ( Prm->PnCtrlCfgPrm.PjogAccT == 0 )? 2000 : ( Prm->PnCtrlCfgPrm.PjogAccT * 1000 );
		MlibPcalaPcmdMaker( PJogV->Cnst.MaxSpd, PJogV->Cnst.AccTime,
									PJogV->Cnst.AccTime, PS_CYCLEUS, &PJogPcmk->P );
		kx1 = MlibScalKxgain( Egear_a, 256, Egear_b, &sx1, -1 );
		kx2 = MlibScalKskxkx( Bprm->KMotSpdConv, PjogSpdP1, 1, &sx2, -1 );
		PjogSpdP2 = MlibScalKskskx( kx1, kx2, 256, NULL, 24 );
		PjogSpdP2 = MlibGAINRD( PjogSpdP2 );
		if( PjogSpdP2 < LMTLOWSPEED )
		{
			PJogV->Cnst.PrmUnMatch = TRUE;
		}
		else
		{
			PJogV->Cnst.PrmUnMatch = FALSE;
		}
	}
        
    
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Program JOG acceleration/deceleration time[ms]
 * RETURNS:
 *
****************************************************************************************************/
void PcalPjogAcctime( void *AxisHdl )
{
	AXIS_HANDLE *Axis;
	PRMDATA *Prm;
	BPRMDAT *Bprm;
	PJOGV *PJogV;
	PCMKPV *PJogPcmk;

	Axis = (AXIS_HANDLE*)AxisHdl;
	Bprm = Axis->BaseLoops->Bprm;
	Prm  = Axis->Prm;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;
	PJogPcmk = &Axis->BaseCtrls->PJogHdl.PJogPcmk;

	if( PJogV->Var.State == PJOG_START )
	{
		PJogV->Var.RoutCmd = PJOGCMD_ABORT;
	}
	PJogV->Cnst.AccTime = ( Prm->PnCtrlCfgPrm.PjogAccT == 0 )? 2000 : ( Prm->PnCtrlCfgPrm.PjogAccT * 1000 );

	MlibPcalaPcmdMaker( PJogV->Cnst.MaxSpd, PJogV->Cnst.AccTime,
								PJogV->Cnst.AccTime, PS_CYCLEUS, &PJogPcmk->P );
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Program JOG wait time[ms]
 * RETURNS:
 *
****************************************************************************************************/
void PcalPjogWaitTime( void *AxisHdl )
{
	AXIS_HANDLE *Axis;
	PRMDATA *Prm;
	PJOGV	*PJogV;

	Axis = (AXIS_HANDLE*)AxisHdl;
	Prm = Axis->Prm;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;

	if( PJogV->Var.State == PJOG_START )
	{
		PJogV->Var.RoutCmd = PJOGCMD_ABORT;
	}
	if( Prm->PnCtrlCfgPrm.PjogWaitT < 2 )
	{
		PJogV->Cnst.WaitTime = 0;
	}
	else
	{
		PJogV->Cnst.WaitTime = Prm->PnCtrlCfgPrm.PjogWaitT - 2;
	}
}

/****************************************************************************************************
 * DESCRIPTION:
 *		 Program JOG move count
 * RETURNS:
 *
****************************************************************************************************/
void PcalPjogNum( void *AxisHdl )
{
	AXIS_HANDLE *Axis;
	PRMDATA *Prm;
	PJOGV	*PJogV;

	Axis = (AXIS_HANDLE*)AxisHdl;
	Prm   = Axis->Prm;
	PJogV = &Axis->BaseCtrls->PJogHdl.PJogV;

	if( PJogV->Var.State == PJOG_START )
	{
		PJogV->Var.RoutCmd = PJOGCMD_ABORT;
	}
	PJogV->Cnst.MTimes = ( PJogV->Cnst.Pattern < 2 )? Prm->PnCtrlCfgPrm.PjogNum : ( Prm->PnCtrlCfgPrm.PjogNum << 1 );
}



