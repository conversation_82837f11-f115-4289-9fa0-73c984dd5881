#include "hal_data.h"

#define CAN_EXAMPLE_ID    (0x20)
can_frame_t g_can_tx0_frame;
can_frame_t g_can_rx0_frame;

volatile canfd_error_t g_err_status = (canfd_error_t) 0;

const canfd_afl_entry_t p_canfd0_afl[CANFD_CFG_AFL_CH0_RULE_NUM] =
{
    {
        .id =
        {
            .id         = 0x0F0,
            .frame_type = CAN_FRAME_TYPE_DATA,
            .id_mode    = CAN_ID_MODE_STANDARD
        },
        .mask = 
        {
            .mask_id    = 0x7FF,
            .mask_frame_type = 0,
            .mask_id_mode = 0,          
        },
        .destination =
        {
            .minimum_dlc       = CANFD_MINIMUM_DLC_4,
            .rx_buffer         = CANFD_RX_MB_0,
            .fifo_select_flags = CANFD_RX_FIFO_0
        }
    },
    {
      .id =
      {
          .id         = 0x1F0,
          .frame_type = CAN_FRAME_TYPE_DATA,
          .id_mode    = CAN_ID_MODE_STANDARD
      },
      .mask = 
      {
          .mask_id    = 0x7FF,
          .mask_frame_type = 0,
          .mask_id_mode = 0,          
      },
      .destination =
      {
          .minimum_dlc       = CANFD_MINIMUM_DLC_4,
          .rx_buffer         = CANFD_RX_MB_0,
          .fifo_select_flags = CANFD_RX_FIFO_0
      }
  },   
  {
    .id =
    {
        .id         = 0x0E0,
        .frame_type = CAN_FRAME_TYPE_DATA,
        .id_mode    = CAN_ID_MODE_STANDARD
    },
    .mask = 
    {
        .mask_id    = 0x7FF,
        .mask_frame_type = 0,
        .mask_id_mode = 0,          
    },
    .destination =
    {
        .minimum_dlc       = CANFD_MINIMUM_DLC_4,
        .rx_buffer         = CANFD_RX_MB_0,
        .fifo_select_flags = CANFD_RX_FIFO_0
    }
}, 

};

/*******************************************************************************
* Function Name: Can_BaudRate_Select
* Description  : Initialize static variables.
* Arguments    : none
* Return Value : none
*******************************************************************************/
static void Can_BaudRate_Select(uint16_t BaudRate)
{
  uint32_t tmp;
  
  uint8_t NominalBaudrate =  (BaudRate)&0xFF;
  uint8_t DataBaudrate    =  (BaudRate>>8)&0xFF;
  
  canfd_extended_cfg_t * p_extend = (canfd_extended_cfg_t *) g_canfd0_cfg.p_extend;
  
  if(NominalBaudrate == 0)
  {
    //BAUD_RATE_500kBPS
    g_canfd0_cfg.p_bit_timing->baud_rate_prescaler = 1;
    g_canfd0_cfg.p_bit_timing->time_segment_1 = 59;
    g_canfd0_cfg.p_bit_timing->time_segment_2 = 20;
    g_canfd0_cfg.p_bit_timing->synchronization_jump_width = 20;
  }
  else if(NominalBaudrate == 1)
  {
    //BAUD_RATE_800kBPS
    g_canfd0_cfg.p_bit_timing->baud_rate_prescaler = 1;
    g_canfd0_cfg.p_bit_timing->time_segment_1 = 37;
    g_canfd0_cfg.p_bit_timing->time_segment_2 = 12; 
    g_canfd0_cfg.p_bit_timing->synchronization_jump_width = 12;
  }   
  else if(NominalBaudrate == 2)
  {
    //BAUD_RATE_1MBPS
    g_canfd0_cfg.p_bit_timing->baud_rate_prescaler = 1;
    g_canfd0_cfg.p_bit_timing->time_segment_1 = 29;
    g_canfd0_cfg.p_bit_timing->time_segment_2 = 10;
    g_canfd0_cfg.p_bit_timing->synchronization_jump_width = 10;
  }
   else if(NominalBaudrate == 3)
  {
    //BAUD_RATE_2MBPS
    g_canfd0_cfg.p_bit_timing->baud_rate_prescaler = 1;
    g_canfd0_cfg.p_bit_timing->time_segment_1 = 14;
    g_canfd0_cfg.p_bit_timing->time_segment_2 = 5;
    g_canfd0_cfg.p_bit_timing->synchronization_jump_width = 5;
  }
  else if(NominalBaudrate == 4)
  {
    //BAUD_RATE_4MBPS
    g_canfd0_cfg.p_bit_timing->baud_rate_prescaler = 1;
    g_canfd0_cfg.p_bit_timing->time_segment_1 = 7;
    g_canfd0_cfg.p_bit_timing->time_segment_2 = 2;
    g_canfd0_cfg.p_bit_timing->synchronization_jump_width = 2;
  }  
  else if(NominalBaudrate == 5)
  {
    //BAUD_RATE_5MBPS
    g_canfd0_cfg.p_bit_timing->baud_rate_prescaler = 1;
    g_canfd0_cfg.p_bit_timing->time_segment_1 = 5;
    g_canfd0_cfg.p_bit_timing->time_segment_2 = 2;
    g_canfd0_cfg.p_bit_timing->synchronization_jump_width = 2;
  }
 
  
  if(DataBaudrate == 0)
  {
    //BAUD_RATE_500kBPS
    p_extend->p_data_timing->baud_rate_prescaler = 1;
    p_extend->p_data_timing->time_segment_1 = 59;
    p_extend->p_data_timing->time_segment_2 = 20;
    p_extend->p_data_timing->synchronization_jump_width = 20;
  }
  else if(DataBaudrate == 1)
  {
    //BAUD_RATE_800kBPS
    p_extend->p_data_timing->baud_rate_prescaler = 1;
    p_extend->p_data_timing->time_segment_1 = 37;
    p_extend->p_data_timing->time_segment_2 = 12;
    p_extend->p_data_timing->synchronization_jump_width = 12;
  }   
  else if(DataBaudrate == 2)
  {
    //BAUD_RATE_1MBPS
    p_extend->p_data_timing->baud_rate_prescaler = 1;
    p_extend->p_data_timing->time_segment_1 = 29;
    p_extend->p_data_timing->time_segment_2 = 10;
   p_extend->p_data_timing->synchronization_jump_width = 10;
  }
   else if(DataBaudrate == 3)
  {
    //BAUD_RATE_2MBPS ,Actual sample point: 75 %.
    p_extend->p_data_timing->baud_rate_prescaler = 1;
    p_extend->p_data_timing->time_segment_1 = 14;
    p_extend->p_data_timing->time_segment_2 = 5;
    p_extend->p_data_timing->synchronization_jump_width = 5;
  }
  else if(DataBaudrate == 4)
  {
    //BAUD_RATE_4MBPS,Actual sample point: 80 %.
    p_extend->p_data_timing->baud_rate_prescaler = 1;
    p_extend->p_data_timing->time_segment_1 = 7;
    p_extend->p_data_timing->time_segment_2 = 2;
    p_extend->p_data_timing->synchronization_jump_width = 2;
  }  
  else if(DataBaudrate == 5)
  {
    //BAUD_RATE_5MBPS,Actual sample point: 75 %.
    p_extend->p_data_timing->baud_rate_prescaler = 1;
    p_extend->p_data_timing->time_segment_1 = 5;
    p_extend->p_data_timing->time_segment_2 = 2;
    p_extend->p_data_timing->synchronization_jump_width = 2;
  }
  
          
}


/*******************************************************************************************************************//**
 * Transmit ISR.
 *
 * Saves context if RTOS is used, clears interrupts, calls common transmit function
 * and restores context if RTOS is used.
 **********************************************************************************************************************/
uint32_t Canfd_GetCh0SR (void)
{
    return R_CANFD->CFDC[0].STS;
}

uint32_t Canfd_GetCh0ER (void)
{
    return R_CANFD->CFDC[0].ERFL;
}

uint32_t Canfd_GetCh0Gl0SR (void)
{
    return R_CANFD->CFDGSTS;
}

uint32_t Canfd_GetCh0Gl0ER (void)
{
    return R_CANFD->CFDGERFL;
}

uint32_t Canfd_GetCh0TQ0SR (void)
{
    return R_CANFD->CFDTXQSTS0[0];
}

uint32_t Canfd_GetCh0CFSR (void)
{
    return R_CANFD->CFDC2[0].FDSTS;
}

/*******************************************************************************************************************//**
 * main() is generated by the FSP Configuration editor and is used to generate threads if an RTOS is used.  This function
 * is called by main() when no RTOS is used.
 **********************************************************************************************************************/
void CanInit(uint16_t BaudRate,uint16_t SlaveId)
{
    __asm volatile ("cpsie i");

    Can_BaudRate_Select(BaudRate);
    /* Initialize the CAN module */
    fsp_err_t err = R_CANFD_Open(&g_canfd0_ctrl, &g_canfd0_cfg);
}

/*******************************************************************************************************************//**
 *  SET AFL TABLE
 * 
 **********************************************************************************************************************/
void CanSetAfl(uint16_t SlaveId)
{

    uint32_t afl_entry = 0;
    uint32_t afl_max   = 2;
    /* Unlock AFL */
    R_CANFD->CFDGAFLECTR = R_CANFD_CFDGAFLECTR_AFLDAE_Msk;

    /* AFL register access is performed through a page window comprised of 16 entries. See Section "Entering
      * Entries in the AFL" in the RZ microprocessor User's Manual for more details. */

    /* Set AFL page */
    R_CANFD->CFDGAFLECTR = (0 >> 4) | R_CANFD_CFDGAFLECTR_AFLDAE_Msk;

    /* Get pointer to current AFL rule and set it to the rule pointed to by p_afl */
    volatile R_CANFD_CFDGAFL_Type * cfdgafl = &R_CANFD->CFDGAFL[0 & 0xF];

    
    R_CANFD->CFDGAFL[0 & 0xF].ID_b.GAFLID = 0x0E0 | (SlaveId & 0x1F);
    R_CANFD->CFDGAFL[1 & 0xF].ID_b.GAFLID = 0x1E0 | (SlaveId & 0x1F);

    /* Lock AFL */
    R_CANFD->CFDGAFLECTR = 0;
}




