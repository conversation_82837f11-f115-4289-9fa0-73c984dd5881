/****************************************************************************************************
 *
 * FILE NAME:  BaseLoops.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.13
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	13-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseLoops.h"
#include "Mlib.h"
#include "Encoder.h"
#include "ExControls.h"

PUBLIC void CurrentLoopInit(BASE_LOOP *BaseLoops);
PRIVATE REAL32 LpxSpdRefPhaseComp(SPD_LOOP *SpdCtrl, REAL32 SpdRefx, 
	                                                      REAL32 SpdRefPhFil, REAL32 SpdRefPhFilGn);
PRIVATE REAL32 LpxSpdFFPhaseComp(SPD_LOOP *SpdCtrl, REAL32 SpdFFC, REAL32 P_SpdFFC, 
                                                            REAL32 SpdFFPhFil, REAL32 SpdFFPhFilGn);

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BaseLoopsInit(BASE_LOOP *BaseLoops)
{
    BaseLoops->GseGains = &BaseLoops->BaseCtrls->GainChange.GselGains[0];
    BaseLoops->SpdLoop.P.KiFil = 0.1f;	
	CurrentLoopInit(BaseLoops);
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void ModeSwitch( MODE_SW *ModeSwData, REAL32 trq_ref, REAL32 spd_ref, REAL32 pos_err )
{
	INT32	ModeSWFlag;
	INT32	wk;

	ModeSWFlag = FALSE;
	wk = 0;

	switch( ModeSwData->conf.ModeSWSel )
	{
	case MODESW_TRQREF: 
		wk = trq_ref;
		if( wk < 0 )
		{
			wk = -wk;
		}
		if( wk >= ModeSwData->conf.MSWTrqLevel )
		{
			ModeSWFlag = TRUE;
		}
		break;

	case MODESW_SPDREF: 
		wk = spd_ref;
		if( wk < 0 )
		{
			wk = -wk;
		}
		if( wk >= ModeSwData->conf.MSWSpdLevel )
		{
			ModeSWFlag = TRUE;
		}
		break;

	case MODESW_MOTACC: 
		wk = ModeSwData->var.Acceleration;
		if( wk < 0 )
		{
			wk = -wk;
		}
		if( wk >= ModeSwData->conf.MSWAccLevel )
		{
			ModeSWFlag = TRUE;
		}
		break;

	case MODESW_POSERR: 
		wk = pos_err;
		if( wk < 0 )
		{
			wk = -wk;
		}
		if( wk >= ModeSwData->conf.MSWErrLevel )
		{
			ModeSWFlag = TRUE;
		}
		break;

	case MODESW_NOTUSE: 
	default:
		break;
	}
	
	ModeSwData->var.ModeSWFlag = ModeSWFlag;
	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 SpeedLoopExec(BASE_LOOP *BaseLoops , REAL32 SpdFFC, 
                                         REAL32 SpdFBC, BOOL BaseEnable)
{
	REAL32   SpdRefx;
	REAL32   ErrP;
	REAL32	 d_SpdRefx, d_SpdFFC;
	BOOL	 integral_calc_on;

	SPD_LOOP *pSpd = &BaseLoops->SpdLoop;

	if( BaseEnable == FALSE )
	{
        BaseLoops->SpdRefSum = 0;
        MlibResetLongMemory( &(pSpd->V), sizeof(pSpd->V)/4 );
		return (0);
	}

	if( BaseLoops->SpdLoop.V.SvonSpdLmtFlag )			
	{
		SpdFFC = 0;
	}

	if( BaseLoops->ZCtrlMode == BASE_MODE_ZSTOP )	
	{
		SpdRefx = 0;
		SpdFFC = 0;
		BaseLoops->TrqFFC = 0;
	}
	else if( BaseLoops->ZCtrlMode == BASE_MODE_LDSTOP )	
	{
		SpdRefx = BaseLoops->BaseCtrls->CtrlCmdMngr.LdStpSpdRef;
		SpdFFC = 0;
	}
	else if( BaseLoops->ZCtrlMode == BASE_MODE_ZCLMP )
	{
//		SpdRefx = ZeroPositionControl( BaseLoops, BaseLoops->dMotPos );
		SpdRefx = 0;
		SpdFFC = 0;
		BaseLoops->TrqFFC = 0;
	}
	else
	{
		SpdRefx = BaseLoops->SpdRef;
	}

#if 1
	/*	Phase advance filtering */
	d_SpdRefx = LpxSpdRefPhaseComp( &BaseLoops->SpdLoop, SpdRefx,
									BaseLoops->BaseCtrls->CtrlCmdPrm.TLPrm.SpdRefPhFil,
									BaseLoops->BaseCtrls->CtrlCmdPrm.TLPrm.SpdRefPhFilGn );
	
	if( BaseLoops->CtrlMode == BASE_MODE_SPD )
	{
		d_SpdFFC = LpxSpdFFPhaseComp( &BaseLoops->SpdLoop, SpdFFC, 0,
									  BaseLoops->BaseCtrls->CtrlCmdPrm.TLPrm.SpdRefPhFil,
									  BaseLoops->BaseCtrls->CtrlCmdPrm.TLPrm.SpdRefPhFilGn );
	}
	else
	{
		d_SpdFFC = LpxSpdFFPhaseComp( &BaseLoops->SpdLoop, SpdFFC, SpdFFC,
									  BaseLoops->BaseCtrls->CtrlCmdPrm.TLPrm.SpdRefPhFil,
									  BaseLoops->BaseCtrls->CtrlCmdPrm.TLPrm.SpdRefPhFilGn );
	}

	if(TuneLessGetTuneLessSts(&(BaseLoops->BaseCtrls->TuneLessCtrl)) )
	{	
//		BaseLoops->DebugVar3 = d_SpdRefx;
		SpdRefx = d_SpdRefx;
		SpdFFC = d_SpdFFC;
	}
#endif 

	pSpd->V.SpdRef = SpdRefx;

	ModeSwitch(	&BaseLoops->BaseCtrls->ModeSw,
				BaseLoops->TrqRefo,
				(SpdRefx + SpdFFC),
				BaseLoops->PosLoop.PosErr );

    BaseLoops->SpdRefSum = FlibLimitul( (SpdRefx + SpdFFC), NOR_MAXVAL_FLOAT, -NOR_MAXVAL_FLOAT );
    SpdRefx = BaseLoops->SpdRefSum - SpdFFC; 
    
	if( ( (SpdRefx - BaseLoops->SpdObsFdb >= 0) && (pSpd->V.TrqRef < 0) )
			|| ( (SpdRefx - BaseLoops->SpdObsFdb < 0) && (pSpd->V.TrqRef > 0) ) )
	{ 
		integral_calc_on = TRUE;
	}
	else
	{ 
		integral_calc_on = FALSE;
	}

	//Speed feedback low pass filter
	pSpd->V.SpdFdbFilo = FlibLpfilter1(BaseLoops->SpdObsFdb + SpdFBC, 
								   BaseLoops->BaseCtrls->CtrlCmdPrm.KVfdbfil, 
								   pSpd->V.SpdFdbFilo );

	if( (BaseLoops->BaseCtrls->CtrlCmdBit.bits.SpdPCtrl)
			|| ((BaseLoops->BaseCtrls->ModeSw.var.ModeSWFlag)
				&& (!BaseLoops->BaseCtrls->CtrlCmdPrm.IPSpdControl)) )
	{ // P Control
		BaseLoops->SpdPctrlFlag = TRUE;
		
		pSpd->V.SpdErr = SpdRefx *BaseLoops->BaseCtrls->CtrlCmdPrm.PI_rate - pSpd->V.SpdFdbFilo + SpdFFC;
		pSpd->V.SpdKpPart = ( pSpd->V.SpdErr * BaseLoops->GseGains->Kv );

		if( !BaseLoops->BaseCtrls->CtrlCmdPrm.SpdIctrlKeep )
		{ 
			pSpd->V.SpdKiPart = FlibLpfilter1( 0.0f, pSpd->P.KiFil, pSpd->V.SpdKiPart );
			pSpd->V.Ivar = pSpd->V.SpdKiPart;
		}
	}
	else
	{ // PI Control
		BaseLoops->SpdPctrlFlag = FALSE;
		
		//PDFF Calculation
		ErrP = SpdRefx *BaseLoops->BaseCtrls->CtrlCmdPrm.PI_rate - pSpd->V.SpdFdbFilo + SpdFFC;
		pSpd->V.SpdKpPart = ( ErrP * BaseLoops->GseGains->Kv );
//		BaseLoops->DebugVar2 =  BaseLoops->GseGains->Kv;

		pSpd->V.SpdErr = SpdRefx - pSpd->V.SpdFdbFilo + SpdFFC;
		if( (BaseLoops->TrqLitFlag == FALSE) || (integral_calc_on == TRUE) )
		{
			pSpd->V.SpdKiPart = FlibIntegral( pSpd->V.SpdErr, BaseLoops->GseGains->Kvi, &pSpd->V.Ivar );
		}
	}

	pSpd->V.TrqRef = pSpd->V.SpdKpPart + pSpd->V.SpdKiPart;

    return ( pSpd->V.TrqRef);
}
										 

#define NRFMAX	16014615
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 PositionLoopExec(BASE_LOOP *BaseLoops, BOOL PerrClrReq)
{
	REAL32    SpdRefx;
	POS_LOOP *pPos = &BaseLoops->PosLoop;

	if( PerrClrReq )
	{
		pPos->Per64[0] = 0;
		pPos->Per64[1] = 0;

		pPos->Per64a[0] = 0;
		pPos->Per64a[1] = 0;
		pPos->PosErra = 0;
		pPos->PosErr = 0;
		return( 0 );
	}

	pPos->PosErra = MlibPerrcalx(BaseLoops->dPcmda, BaseLoops->dPosFdb, pPos->Per64a);

	pPos->PosErr = MlibPerrcalx(BaseLoops->dPosRef, BaseLoops->dPosFdb, pPos->Per64);
//	PosErr = (INT64)BaseLoops->PosRef - (INT64)BaseLoops->PosFdb;

	SpdRefx = (REAL32)pPos->PosErr * BaseLoops->GseGains->Kp;

	if( (BaseLoops->BaseCtrls->BaseEnable) == FALSE )
	{
		SpdRefx = 0;
	}

	pPos->SpdRef = FlibLimitul( SpdRefx, NRFMAX, -NRFMAX );

	return (pPos->SpdRef);
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 PositionErrorExec(BASE_LOOP *BaseLoops, BOOL PerrClrReq)
{
	REAL32    SpdRefx;
	POS_LOOP *pPos = &BaseLoops->PosLoop;

	if( PerrClrReq )
	{
		pPos->Per64[0] = 0;
		pPos->Per64[1] = 0;

		pPos->Per64a[0] = 0;
		pPos->Per64a[1] = 0;
		pPos->PosErra = 0;
		pPos->PosErr = 0;
		return( 0 );
	}

	pPos->PosErra = MlibPerrcalx(BaseLoops->dPcmda, BaseLoops->dPosFdb, pPos->Per64a);

	pPos->PosErr = MlibPerrcalx(BaseLoops->dPosRef, BaseLoops->dPosFdb, pPos->Per64);
//	PosErr = (INT64)BaseLoops->PosRef - (INT64)BaseLoops->PosFdb;

	return (pPos->PosErr);
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 SvonSpeedLimit( BASE_LOOP *BaseLoops,  BOOL BaseEnable, REAL32 SpdRefi )
{
	REAL32	AbsSpdRefi;
	REAL32	SpdRefx;

	SPD_LOOP *pSpd = &BaseLoops->SpdLoop;

	SpdRefx = SpdRefi;

	if(((BaseEnable == TRUE) && (pSpd->V.LastSvonFlag == FALSE))  || pSpd->V.SvonSpdLmtFlag )
	{
		AbsSpdRefi = SpdRefi;
		if( AbsSpdRefi < 0 )
		{
			AbsSpdRefi = -AbsSpdRefi;
		}

		if( AbsSpdRefi > BaseLoops->BaseCtrls->CtrlCmdPrm.SvonSpdLmtLevel )
		{ 
			pSpd->V.SvonSpdLmtFlag = TRUE;
			if( SpdRefi >= 0 )
			{
				SpdRefx = BaseLoops->BaseCtrls->CtrlCmdPrm.SvonSpdLmtLevel;
			}
			else
			{
				SpdRefx = -BaseLoops->BaseCtrls->CtrlCmdPrm.SvonSpdLmtLevel;
			}
		}
		else
		{ 
			pSpd->V.SvonSpdLmtFlag = FALSE;
		}
	}
	
	pSpd->V.LastSvonFlag = BaseEnable;
	
	return( SpdRefx );
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 TorqueFilterForPSCtrl(BASE_LOOP *BaseLoops, REAL32 TrqFFC, REAL32 TrqFBC)
{


	TRQFIL *TrqFil;
	REAL32	TrqRefx = 0;

	TrqFil = &BaseLoops->TrqFil;

	if( BaseLoops->BaseCtrls->BaseEnable == FALSE )
	{
		TrqFil->V.TrqFilClrCmd = FALSE;			
		TrqFil->V.FilOut = 0;
//		TrqFil->V.FilOut3 = 0;
		TrqFil->V.LpFil2[0] = 0;
		TrqFil->V.LpFil2[1] = 0;
		TrqFil->V.LpFil2[2] = 0;
		TrqFil->V.LpFil2[3] = 0;
		return( 0 );
	}

	TrqRefx = BaseLoops->TrqRef;

	if(TrqFil->V.TrqFilClrCmd == TRUE)		
	{
		TrqFil->V.FilOut = TrqRefx;				
		TrqFil->V.TrqFilClrCmd = FALSE;		
	}

	TrqFil->V.FilOut = FlibLpfilter1( TrqRefx, BaseLoops->GseGains->Klpf, TrqFil->V.FilOut );

	TrqRefx = FlibLimitul( (TrqFil->V.FilOut + TrqFFC - TrqFBC), (REAL32)0x01000000, (REAL32)-0x01000000 );
#if 0	
	/* First-order low-pass filter processing (after torque compensation) */
	if(BaseLoops->BaseCtrls->CtrlCmdPrm.LpassFil3 == TRUE)
	{ 
		if(BaseLoops->BaseCtrls->TuneLessCtrl.conf.TuningLessUse )
		{ 
			TrqFil->V.FilOut3 = FlibLpfilter1( TrqRefx,
										(BaseLoops->BaseCtrls)->CtrlCmdPrm.TLPrm.Klpf3,
										TrqFil->V.FilOut3 );
			TrqRefx = TrqFil->V.FilOut3;
		}
		else
		{
			TrqFil->V.FilOut3 = 0;
		}
	}
	else
	{
		TrqFil->V.FilOut3 = 0;
	}
#endif

	if(FALSE == BaseLoops->BaseCtrls->CtrlCmdPrm.LpassFil2)
	{
		TrqFil->V.LpFil2[0] = 0;
		TrqFil->V.LpFil2[1] = 0;
		TrqFil->V.LpFil2[2] = 0;
		TrqFil->V.LpFil2[3] = 0;
	}
	else
	{
		TrqRefx = FlibLowPassfilter2( TrqRefx, TrqFil->P.Klpf2, TrqFil->V.LpFil2 );
	}

	return( FlibLimitul( TrqRefx, (REAL32)0x01000000, (REAL32)-0x01000000 ) );

	 
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 TorqueFilterForTrqCtrl(BASE_LOOP *BaseLoops)
{

// 	BaseLoops->TrqRef = 0.0f;
 	REAL32   TrqRefx = 0.0f;

	if( BaseLoops->BaseCtrls->BaseEnable == FALSE )
	{
		BaseLoops->TrqRefo = 0;
		BaseLoops->TrqFil.V.FilOut = 0;
		BaseLoops->TrqFil.V.FilOut3 = 0;
		BaseLoops->TrqFil.V.LpFil2[0] = 0;
		BaseLoops->TrqFil.V.LpFil2[1] = 0;
		BaseLoops->TrqFil.V.LpFil2[2] = 0;
		BaseLoops->TrqFil.V.LpFil2[3] = 0;
		return( 0 );
	}

	if(BaseLoops->TrqFil.V.TrqFilClrCmd == TRUE)		
	{
		BaseLoops->TrqFil.V.FilOut = TrqRefx;				
		BaseLoops->TrqFil.V.TrqFilClrCmd = FALSE;		
	}
	
    // speed limit for torque control
	TrqRefx = BaseLoops->TrqRef - BaseLoops->BaseCtrls->CtrlCmdMngr.OvrTrq;
        
	BaseLoops->TrqFil.V.FilOut = FlibLpfilter1( TrqRefx, BaseLoops->TrqFil.A.Klpf, BaseLoops->TrqFil.V.FilOut );

	TrqRefx = FlibLimitul(BaseLoops->TrqFil.V.FilOut, (REAL32)0x01000000, (REAL32)-0x01000000 );

	if(FALSE == BaseLoops->BaseCtrls->CtrlCmdPrm.LpassFil2)
	{
		BaseLoops->TrqFil.V.LpFil2[0] = 0;
		BaseLoops->TrqFil.V.LpFil2[1] = 0;
		BaseLoops->TrqFil.V.LpFil2[2] = 0;
		BaseLoops->TrqFil.V.LpFil2[3] = 0;
	}
	else
	{
		TrqRefx = FlibLowPassfilter2( TrqRefx, BaseLoops->TrqFil.P.Klpf2, BaseLoops->TrqFil.V.LpFil2 );
	}

	BaseLoops->TrqRefo = FlibLimitul(TrqRefx, (REAL32)0x01000000, (REAL32)-0x01000000 );

	return TrqRefx;
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void TorqueLoopExec(BASE_LOOP *BaseLoops)
{

 
 
	 
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 LpxSpdDetMaFilter( MOTSPDMAFIL *SpdMaFil, INT32 MotSpd )
{
        INT32	    lwk0;
	INT32  	output;
	INT32  	idx_max;
	INT32  	MotSpdTemp;
	MotSpdTemp = MotSpd;
	idx_max = 1 << SpdMaFil->manumBit;

	/* get data from buffer */
	lwk0 = SpdMaFil->ksub * SpdMaFil->mabuf[SpdMaFil->idx];
	/* new data storage */
	SpdMaFil->mabuf[SpdMaFil->idx] = MotSpdTemp;

	SpdMaFil->idx++;
	if( SpdMaFil->idx >= idx_max )
	{
		/* reset index */
		SpdMaFil->idx = 0;
		SpdMaFil->ksub = 1;
	}
	/* Buffer sum value update */
	SpdMaFil->mabufSumx = SpdMaFil->mabufSumx - lwk0 + MotSpdTemp;

	output = ( SpdMaFil->mabufSumx >> SpdMaFil->manumBit );

	return( output );
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PosSpdFdbCal( BASE_LOOP *BaseLoops)
{
	INT32  		MotSpd,MotSpd1ms;
	EHVOBS_CTRL		*EhVobsCtrl;
    CIA402_STATUS_WORD    StatusWord = {0};
    INT32 Positon;

	EhVobsCtrl = &(BaseLoops->BaseCtrls->EhVobsCtrl);
	if(BaseLoops->Enc->V.EncConnectLast && BaseLoops->Enc->V.EncConnect)
	{
		if(BaseLoops->UseEncoder2)
		{
			Positon = BaseLoops->Bprm->DirSign * BaseLoops->Enc->V.MotPos;
		}
		else
		{
			Positon = BaseLoops->Bprm->DirSign *(BaseLoops->Enc->V.MotPos + BaseLoops->Enc->P.EncZeroOffset)  + BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.PosOffset;
		}
		BaseLoops->dMotPos = Positon - BaseLoops->Enc1MotPos;               
	}
	else
	{
		BaseLoops->dMotPos = 0;
	}
	
	BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.PosChangFlag = 0;
	
	if((BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.TouchIndexMark ==1) 
			&&(BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.LastTouchIndexMark == 0))
	{
		BaseLoops->dMotPos = 0;
		BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.PosChangFlag = 1;
	}
	
	BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.LastTouchIndexMark = BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.TouchIndexMark;
	BaseLoops->Enc->V.EncConnectLast = BaseLoops->Enc->V.EncConnect;
	
	if(BaseLoops->UseEncoder2)
	{
		INT32 Positon =  BaseLoops->Bprm->DirSign * (BaseLoops->Enc2->V.MotPos )  + BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.PosOffset;
		BaseLoops->dPosFdb = Positon - BaseLoops->MotPos;
	}
	else
	{
		BaseLoops->dPosFdb = BaseLoops->dMotPos;
	}
        

        
	if(BaseLoops->UseEncoder2)
	{
		BaseLoops->MotPos = BaseLoops->Bprm->DirSign * (BaseLoops->Enc2->V.MotPos)  + BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.PosOffset;
		BaseLoops->Enc1MotPos = BaseLoops->Bprm->DirSign * BaseLoops->Enc->V.MotPos;
	}
	else
	{
		BaseLoops->MotPos = BaseLoops->Bprm->DirSign * (BaseLoops->Enc->V.MotPos + BaseLoops->Enc->P.EncZeroOffset)  + BaseLoops->BaseCtrls->Cia402Axis.HOME_Ctrl.PosOffset;
		BaseLoops->Enc1MotPos = BaseLoops->MotPos;
	}
        
    BaseLoops->SigleTurn = BaseLoops->Enc->V.SingleTurn;

	MotSpd = BaseLoops->dMotPos *BaseLoops->Bprm->KmotspdF;
	

	BaseLoops->MotSpd1msCnt++;
	BaseLoops->dMotPos1msSum +=  BaseLoops->dPosFdb;
	if(BaseLoops->MotSpd1msCnt >= 8)
	{ 
		BaseLoops->MotSpd1ms = BaseLoops->dMotPos1msSum * BaseLoops->Bprm->KmotspdC;
		BaseLoops->dMotPos1msSum = 0;
		BaseLoops->MotSpd1msCnt = 0;
	}        
        
	if( BaseLoops->MotSpdMaFil->manumBit == 0 )
	{ 
		BaseLoops->MotSpd = MotSpd;
	}
	else
	{ 
		BaseLoops->MotSpd = LpxSpdDetMaFilter( BaseLoops->MotSpdMaFil, MotSpd );
	}
        
	if( BaseLoops->MotSpdMaFil1ms[0].manumBit == 0 )
	{ 
                BaseLoops->MotSpd1msFil = BaseLoops->MotSpd1ms;
	}
	else
	{ 
                MotSpd1ms = LpxSpdDetMaFilter( &BaseLoops->MotSpdMaFil1ms[0], BaseLoops->MotSpd1ms );
		MotSpd1ms = LpxSpdDetMaFilter( &BaseLoops->MotSpdMaFil1ms[1], MotSpd1ms );
                MotSpd1ms = LpxSpdDetMaFilter( &BaseLoops->MotSpdMaFil1ms[2], MotSpd1ms );
		BaseLoops->MotSpd1msFil = LpxSpdDetMaFilter( &BaseLoops->MotSpdMaFil1ms[3], MotSpd1ms );
	}        
	
	BaseLoops->PosFdb = BaseLoops->MotPos;
	BaseLoops->SpdFdb = BaseLoops->MotSpd;
    
//    if((StatusWord.bit.OperSpecific_b15 == 1) &&  (CTRL_MODE_HOME != BaseLoops->BaseCtrls->CtrlModeSet.CtrlMode.b.cm))
    if(StatusWord.bit.OperSpecific_b15 == 1)
    {
      BaseLoops->PosFdb =  BaseLoops->PosFdb  -  BaseLoops->BaseCtrls->Cia402Axis.ZorePostion;
    }

	/* Phase compensation speed observer calculation */
	// if( TRUE == EhVobsGetObserverUse(EhVobsCtrl) )
	// {
	// 	BaseLoops->SpdObsFdb = EhSpeedObserver( EhVobsCtrl, BaseLoops->SpdFdb, BaseLoops->TrqRef );
	// }
	// else
	{
		BaseLoops->SpdObsFdb = BaseLoops->SpdFdb;
	}

	MotSpd = BaseLoops->SpdObsFdb /BaseLoops->Bprm->Kmotspd;
	BaseLoops->IpdSpdFdb = Cia402_GearRatioInvCal(&BaseLoops->BaseCtrls->Cia402Axis, MotSpd);

	REAL32 SpdObsFdbRads = BaseLoops->SpdObsFdb /BaseLoops->Bprm->KmotVel;
	BaseLoops->IpdSpdFdbRads = GearRatioInvCal(BaseLoops, SpdObsFdbRads);

	if(BaseLoops->UseEncoder2)
	{
		/* code */
		BaseLoops->IpdPosFdbRad = BaseLoops->MotPos*BaseLoops->Bprm->Kmotpls1;
		BaseLoops->IpdPosFdb = BaseLoops->MotPos;
	}
	else
	{
		REAL32 PosFdbRad = BaseLoops->MotPos*BaseLoops->Bprm->Kmotpls;
		BaseLoops->IpdPosFdbRad =  GearRatioInvCal(BaseLoops, PosFdbRad);
		BaseLoops->IpdPosFdb =  Cia402_GearRatioInvCal(&BaseLoops->BaseCtrls->Cia402Axis, BaseLoops->MotPos);
	}
    
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void OutputTrqRef(BASE_LOOP *BaseLoops, REAL32 TrqRef, REAL32 WeakRef)
{
	REAL32  IqRefLmtP;
	TRQ_LMT_DATA	  *TrqLmt =  &BaseLoops->BaseCtrls->TrqLimitData;

	BaseLoops->IqRef = (TrqRef/NOR_MAXVAL_FLOAT)*BaseLoops->Bprm->MaxCur;
	BaseLoops->IdRef = (WeakRef/NOR_MAXVAL_FLOAT)*BaseLoops->Bprm->MaxCur;

	BaseLoops->CurLoop.V.IqRefLmtP = (TrqLmt->V.PosTrqLmtOut/NOR_MAXVAL_FLOAT)*BaseLoops->Bprm->MaxCur;
	BaseLoops->CurLoop.V.IqRefLmtN = (TrqLmt->V.NegTrqLmtOut/NOR_MAXVAL_FLOAT)*BaseLoops->Bprm->MaxCur;

	if(BaseLoops->Bprm->RvsDir)
	{
		BaseLoops->IqRef = -BaseLoops->IqRef;
		BaseLoops->IdRef = -BaseLoops->IdRef;

		IqRefLmtP = BaseLoops->CurLoop.V.IqRefLmtP;
		BaseLoops->CurLoop.V.IqRefLmtP = -BaseLoops->CurLoop.V.IqRefLmtN;
		BaseLoops->CurLoop.V.IqRefLmtN = -IqRefLmtP;
	}

/*--------------------------------------------------------------------------------------------------*/
/*		Variable field control related variable output to current loop								*/
/*--------------------------------------------------------------------------------------------------*/
	/* Voltage FB proportional gain*/
	BaseLoops->WeakFV.WfKp = BaseLoops->BaseCtrls->WeakenField.var.Kpv;
	/* Voltage FB integrated gain */
	BaseLoops->WeakFV.WfKi = BaseLoops->BaseCtrls->WeakenField.var.Kiv;
	/* Voltage command limit value */
	BaseLoops->WeakFV.WfV1Max= BaseLoops->BaseCtrls->WeakenField.conf.V1Max;
	/* d-axis current command limit */
	BaseLoops->WeakFV.WfIdRefLim = BaseLoops->BaseCtrls->WeakenField.var.IdrefLim;
	/* q-axis current command limit */
	BaseLoops->WeakFV.WfIqRefLim = BaseLoops->BaseCtrls->WeakenField.var.IqrefLim;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void TaskAInputCtrlCmd(BASE_LOOP *BaseLoops, BASE_CTRL *BaseCtrls)
{
	float			PosTrqLmt;
	float			NegTrqLmt;

	BaseLoops->LastCtrlMode = BaseLoops->CtrlMode;
	BaseLoops->CtrlMode = BaseCtrls->CtrlCmdMngr.CtrlMode;
	BaseLoops->ZCtrlMode = (UINT16)BaseCtrls->CtrlModeSet.ZctrlMode.zm;
	
	/*Torque Reference after Torque Limit for Observer */			
	PosTrqLmt = (BaseLoops->BaseCtrls)->TrqLimitData.V.PosTrqLmtOut;
	NegTrqLmt = (BaseLoops->BaseCtrls)->TrqLimitData.V.NegTrqLmtOut;
	if( BaseLoops->TrqRefo > PosTrqLmt )
	{
		BaseLoops->TrqRefoLmt = PosTrqLmt;
	}
	else if( BaseLoops->TrqRefo < NegTrqLmt )
	{
		BaseLoops->TrqRefoLmt = NegTrqLmt;
	}
	else
	{
		BaseLoops->TrqRefoLmt = BaseLoops->TrqRefo;
	}

	if ( BaseLoops->LastCtrlMode == BASE_MODE_TRQ )    
	{
		BaseLoops->SpdLoop.V.SpdRefFilClrCmd = TRUE;    
		BaseLoops->SpdLoop.V.SpdFFFilClrCmd = TRUE;     
	}

	BaseLoops->SpdFFC = BaseLoops->BaseCtrls->CtrlCmdMngr.SpdFFCFilo;
	BaseLoops->TrqFFC = BaseLoops->BaseCtrls->CtrlCmdMngr.TrqFFCFilo;
	BaseLoops->SpdFBC = BaseLoops->BaseCtrls->CtrlCmdMngr.SpdFBC;
	BaseLoops->TrqFBC = BaseLoops->BaseCtrls->CtrlCmdMngr.TrqFBC;
	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/

PUBLIC void BaseLoopsExec(BASE_LOOP *BaseLoops)
{
	BOOL            BaseEnable,CmdEnable;
	TUNELESS_CTRL	*TuneLessCtrl;
	DOBS_CTRL		*DobsCtrl;
        REAL32           Tmp32;

#if 0
	static UINT32 LockCnt = 0;

	if(LockCnt++ > 16000*3600*2)   //
	{
		ALMSetGlobalAlarm( ALM_ISR_TIME );
		return;
	}
#endif    
    
    
	TuneLessCtrl  = &(BaseLoops->BaseCtrls->TuneLessCtrl);
	BaseEnable = BaseLoops->BaseCtrls->BaseEnable;
    CmdEnable  = BaseLoops->BaseCtrls->CmdEnable;
	DobsCtrl  = &(BaseLoops->BaseCtrls->DobsCtrl);

//	PosSpdFdbCal(BaseLoops);

	TaskAInputCtrlCmd(BaseLoops, BaseLoops->BaseCtrls);
    
#if 1
	#if NO_ADVFUNC
	if( TRUE == TuneLessGetTuneLessUse(TuneLessCtrl) )
	{ 
		/* Tuneless control --> SpdObsFdb, TrqFBC */
		TuningLessCtrl( TuneLessCtrl, BaseLoops->SpdFdb, BaseLoops->TrqRef, BaseLoops->TrqRefoLmt );

		/* Tuneless control calculation result acquisition */
		if( TRUE == TuneLessGetTuneLessSts(TuneLessCtrl) )
		{ 
			BaseLoops->SpdObsFdb = TuneLessGetSpdObsFbk( TuneLessCtrl );
			BaseLoops->TrqFBC += TuneLessGetDisTrqCmp( TuneLessCtrl );


		}
		else
		{ 
			BaseLoops->TrqFil.V.TrqFilClrCmd = TRUE;

		}

		/* Torque command filter integral initialization request */
		if( TRUE == TuneLessGetTFilClrReq(TuneLessCtrl) )
		{
			BaseLoops->TrqFil.V.TrqFilClrCmd = TRUE;
		}

	}
	else
	{
		DisturbObserver( DobsCtrl, BaseLoops->SpdObsFdb, BaseLoops->TrqRefoLmt );  

//	    BaseLoops->DebugVar1 = DobsGetDisTrqCmp( DobsCtrl );
	    
		if( TRUE == DobsGetObserverSts(DobsCtrl) )
		{ 
			BaseLoops->TrqFBC += DobsGetDisTrqCmp( DobsCtrl );
	        
		}

//        switch ( DobsGetExchangeSts(DobsCtrl) )
//        {

//        case DOBSCHNG_INV2ACT:	// Invalid => Valid

//            fwk = ((REAL32)DobsCtrl->V.DisTrqCmp * BaseLoops->GseGains->Kv2Inv);

//            BaseLoops->SpdLoop.V.Ivar += (fwk * 2.0f);

//            BaseLoops->TrqFil.V.TrqFilClrCmd = TRUE;
//            break;

//        case DOBSCHNG_ACT2INV:	// Valid => Invalid
//            fwk = ((float)DobsCtrl->V.DisTrqCmp * BaseLoops->GseGains->Kv2Inv);

//            BaseLoops->SpdLoop.V.Ivar -= (fwk * 2.0f);

//            BaseLoops->TrqFil.V.TrqFilClrCmd = TRUE;
//            break;

//        case DOBS_NOCHANGE:		
//        default:
//            break;
//        }

	}
	#endif
#endif

	BaseLoops->WeakRef = 0.0f;

	switch(BaseLoops->CtrlMode)
	{
	case BASE_MODE_POS:
		BaseLoops->dPcmda = BaseLoops->BaseCtrls->PosManager.var.dPcmda;
		BaseLoops->dPosRef = BaseLoops->BaseCtrls->CtrlCmdMngr.dPosRefo;
                BaseLoops->PosRef = BaseLoops->BaseCtrls->CtrlCmdMngr.PosRefFilo;
		BaseLoops->TrqPosComp = BaseLoops->BaseCtrls->CtrlCmdMngr.TrqPosComp;
          	
		BaseLoops->SpdRefP =  PositionLoopExec(BaseLoops, BaseLoops->BaseCtrls->CtrlCmdMngr.PerrClrReq);

		Tmp32 = SvonSpeedLimit(BaseLoops, CmdEnable, BaseLoops->SpdRefP);
                if(BaseLoops->UseEncoder2)
                {
                  BaseLoops->SpdRef = GearRatioCal(BaseLoops, Tmp32 );
                }
                else
                {
                   BaseLoops->SpdRef = Tmp32;
                }
	
	    if(BaseLoops->BaseCtrls->CtrlModeSet.ZctrlMode.zm != BASE_MODE_ZNONE)
        {
                BaseLoops->SpdFFC = BaseLoops->SpdFFCLatch;
                BaseLoops->TrqFFC = BaseLoops->TrqFFCLatch;
                if(!BaseEnable)
                {
                    BaseLoops->SpdFFC = 0;
                    BaseLoops->TrqFFC = 0;
                }
        }
        else
        {
                BaseLoops->SpdFFCLatch = BaseLoops->SpdFFC;
                BaseLoops->TrqFFCLatch = BaseLoops->TrqFFC;         
        }        

		BaseLoops->TrqRef = SpeedLoopExec(BaseLoops, BaseLoops->SpdFFC, 
			                                           BaseLoops->SpdFBC, BaseEnable);

		BaseLoops->TrqRef = BaseLoops->TrqRef + BaseLoops->TrqPosComp;	
				
		BaseLoops->TrqRefo = TorqueFilterForPSCtrl(BaseLoops, BaseLoops->TrqFFC, BaseLoops->TrqFBC);

		break;

	case BASE_MODE_SPD:
		BaseLoops->SpdRef = BaseLoops->BaseCtrls->CtrlCmdMngr.SpdRef;
    
        BaseLoops->TrqFFC = BaseLoops->BaseCtrls->CtrlCmdMngr.TrqFFCFilo;

        if(BaseLoops->BaseCtrls->CtrlModeSet.ZctrlMode.zm != BASE_MODE_ZNONE)
        {
                BaseLoops->TrqFFC = BaseLoops->TrqFFCLatch;
                if(!BaseEnable)
                {
                    BaseLoops->TrqFFC = 0;
                }
        }
        else
        {
                BaseLoops->TrqFFCLatch = BaseLoops->TrqFFC;         
        }     

		BaseLoops->TrqRef = SpeedLoopExec(BaseLoops, BaseLoops->SpdFFC, 
			                                           BaseLoops->SpdFBC, BaseEnable);
		
		BaseLoops->TrqRefo = TorqueFilterForPSCtrl(BaseLoops, BaseLoops->TrqFFC, BaseLoops->TrqFBC);
		
		break;

	case BASE_MODE_TRQ:
		BaseLoops->WeakRef = BaseLoops->BaseCtrls->CtrlCmdMngr.WeakRef;
		BaseLoops->TrqRef = BaseLoops->BaseCtrls->CtrlCmdMngr.TrqRef;
		BaseLoops->TrqRefo = TorqueFilterForTrqCtrl(BaseLoops);
		break;

	case BASE_MODE_VF:
	
//		BaseLoops->TrqRefo = TorqueFilterForTrqCtrl(BaseLoops);
		break;

	default:
		break;
	}

//	BaseLoops->SpdFBC = 0;	 // todo
    BaseLoops->TrqFBC = 0;   // todo
    BaseLoops->TrqRefoComp = BaseLoops->TrqRefo;

	// BaseLoops->BaseCtrls->TrqMotFricComp = LpxMotorFricComp( BaseLoops->BaseCtrls->FrictionM.MotFricEnable, BaseLoops );
    BaseLoops->TrqRefoComp += BaseLoops->BaseCtrls->TrqMotFricComp;

	OutputTrqRef(BaseLoops, BaseLoops->TrqRefoComp, BaseLoops->WeakRef);

	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
	

	
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void SysOutputTaskAProcessData( BASE_LOOP *BaseLoops, CTRL_LOOP_OUT *CtrlLoopOut )
{
//	POS_MNG_HNDL *PosMngV;
//	PosMngV = &BaseLoops->BaseCtrls->PosManager;

	CtrlLoopOut->MotPosErr = BaseLoops->PosLoop.PosErr;	
	CtrlLoopOut->SpdRefo = BaseLoops->SpdRef;		
						
	CtrlLoopOut->MotSpd_a = BaseLoops->MotSpd;
	CtrlLoopOut->TrqRefo = BaseLoops->TrqRefo;		
	CtrlLoopOut->TrqRefo_a = BaseLoops->TrqRefo;	

	if(BaseLoops->Bprm->RvsDir)
	{
		CtrlLoopOut->IdRefMon = -BaseLoops->CurLoop.V.IdRef;
		CtrlLoopOut->IqRefMon = -BaseLoops->CurLoop.V.IqRef;
	}
	else
	{
		CtrlLoopOut->IdRefMon = BaseLoops->CurLoop.V.IdRef;
		CtrlLoopOut->IqRefMon = BaseLoops->CurLoop.V.IqRef;
	}

	CtrlLoopOut->TrqRefMon = CtrlLoopOut->IqRefMon*NOR_MAXVAL_FLOAT/BaseLoops->Bprm->MaxCur;
	CtrlLoopOut->TrqRefMon_a = 	CtrlLoopOut->TrqRefMon;					

	CtrlLoopOut->IdFbMon = BaseLoops->CurLoop.V.IdFdb;
	CtrlLoopOut->IqFbMon = BaseLoops->CurLoop.V.IqFdb;

	CtrlLoopOut->SvonSpdLmtFlag = BaseLoops->SpdLoop.V.SvonSpdLmtFlag;

}


/****************************************************************************************************
 * DESCRIPTION:
 *        Speed command phase lead compensation calculation when tuneless control is enabled
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 LpxSpdRefPhaseComp(SPD_LOOP *SpdCtrl, REAL32 SpdRefx, 
                                                           REAL32 SpdRefPhFil, REAL32 SpdRefPhFilGn)
{
	REAL32	SpdRefComp;

	if(SpdCtrl->V.SpdRefFilClrCmd == TRUE)
	{
		SpdCtrl->V.SpdRefPhLpfo = SpdRefx;
		SpdCtrl->V.SpdRefPhHpfTmp[0] = SpdRefx;
        SpdCtrl->V.SpdRefPhHpfTmp[1] = SpdRefx;
        
		SpdCtrl->V.SpdRefFilClrCmd = FALSE;
	}

	SpdCtrl->V.SpdRefPhLpfo = FlibLpfilter1(SpdRefx, SpdRefPhFil, SpdCtrl->V.SpdRefPhLpfo);
	SpdCtrl->V.SpdRefPhHpfo = FlibHpfilter1(SpdRefx, SpdRefPhFil, SpdCtrl->V.SpdRefPhHpfTmp);

	SpdRefComp = SpdCtrl->V.SpdRefPhLpfo + ((INT32)SpdCtrl->V.SpdRefPhHpfo * SpdRefPhFilGn);

	return(SpdRefComp);
}

/****************************************************************************************************
 * DESCRIPTION:
 *        Speed FF phase lead compensation calculation when tuneless control is enabled
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 LpxSpdFFPhaseComp(SPD_LOOP *SpdCtrl, REAL32 SpdFFC, REAL32 P_SpdFFC, 
                                                            REAL32 SpdFFPhFil, REAL32 SpdFFPhFilGn)
{
	REAL32	SpdFFCComp;

	if(SpdCtrl->V.SpdFFFilClrCmd == TRUE)
	{
		SpdCtrl->V.SpdFFFilClrCmd = FALSE;

		SpdCtrl->V.SpdFFPhLpfo = P_SpdFFC;
		SpdCtrl->V.SpdFFPhHpfTmp[0] = P_SpdFFC;
        SpdCtrl->V.SpdFFPhHpfTmp[1] = SpdFFC;
	}

	SpdCtrl->V.SpdFFPhLpfo = FlibLpfilter1(SpdFFC, SpdFFPhFil, SpdCtrl->V.SpdFFPhLpfo);
	SpdCtrl->V.SpdFFPhHpfo = FlibHpfilter1(SpdFFC, SpdFFPhFil, SpdCtrl->V.SpdFFPhHpfTmp);

	SpdFFCComp = SpdCtrl->V.SpdFFPhLpfo + (SpdCtrl->V.SpdFFPhHpfo * SpdFFPhFilGn);

	return(SpdFFCComp);
}


