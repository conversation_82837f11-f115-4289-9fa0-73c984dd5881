/****************************************************************************************************
 *
 * FILE NAME:  Encoder.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.14
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	14-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _ENCODER_H_
#define _ENCODER_H_

#include "BaseDef.h"
#include "BaseLoops.h"
#include "PnPrmStruct.h"
#include "alarm.h"



#define  ENC_COM_ERR_LMT            0x9

#define  ENC_TYPE_INCREMENT         0x0001
#define  ENC_TYPE_TAMAGAWA          0x0002
#define  ENC_TYPE_NIKON_4M          0x0003
#define  ENC_TYPE_NIKON_2M5         0x0004
#define  ENC_TYPE_ROTARY         	0x0005
#define  ENC_TYPE_BISSC            0x0007
#define  ENC_TYPE_INCREMENT_HALL    0x0009
#define  ENC_TYPE_SSIC             0x000B
#define  ENC_TYPE_2BISSC            0x000D

#define  ENC_TYPE_USER_DEFINE       0x0010
#define  ENC_TYPE_KTM               0x0011
#define  ENC_TYPE_MA600            0x00012
#define  ENC_TYPE_CAL               0x00AA


#define  ENC_STDATA_MASK            0x0007FFFF   //      
#define  ENC_MTDATA_MASK            0x0000FFFF   

#define TAMA_CMD_DATAID0   0x02
#define TAMA_CMD_DATAID3   0x1A


#define ENC_CLR_MULTITURNANDERR         0x01
#define ENC_ENCSTATE                    0x02
#define ENC_CLR_MULTITURN               0x03
#define ENC_CLR_ERR                     0x04
#define ENC_READTEMP                    0x05
#define ENC_WRITEMOTORPRM               0x06
#define ENC_READMOTORPRM                0x07
#define ENC_FACTROY_INIT                0x08


#define ENC_COM_ERR         0x01
#define ENC_CRC_ERR         0x02
#define ENC_NEEPROM_ERR     0x03
#define ENC_WEEPROM_ERR     0x04
#define ENC_REEPROM_ERR     0x05


#define MOTOR_PRM_INDEX   0x2000



typedef struct 
{
    UINT8   InitFlag;
	UINT8	SubIndex;				// motor type
    UINT8   Length;
    UINT8   Value;
    UINT16  Address;
}MOTORPRM_INDEX;


typedef struct 
{
    UINT8   Value;
    UINT16  Address;
}MOTORPRODUCT;

PUBLIC void EncoderInit(UINT16 AxisID,ENCODER *pEnc,UINT16 PluseInMode);
PUBLIC void GetEncoderPulse(ENCODER *pEnc, ALARM *AlmMngr, UINT16 AxisID, BOOL ServoOn);
PUBLIC void EncoderCalExec(ENCODER *pEnc,ENCODER *pEnc2, ALARM *AlmMngr, UINT16 AxisID, BOOL ServoOn, BOOL UWVSeq,UINT16 MotorType);
PUBLIC void MechAngleCalibration(ENCODER *pEnc, REAL32 angle);


PUBLIC UINT8 EncReadMotPrm(UINT16 AxisID);
PUBLIC UINT8 EncWriteMotPrm(UINT16 AxisID);
PUBLIC void RpiEncExCmdRound(BASE_LOOP *BaseLoops, UINT16 ExCmd, UINT16 AxisID);

PUBLIC void GetEncoderPulse2(ENCODER *pEnc, ALARM *AlmMngr, UINT16 AxisID, BOOL ServoOn);
#endif /* _ENCODER_H_ */
