/****************************************************************************************************
 *
 * FILE NAME:  MotorPoleFind.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-08-2019 Version 1.00 : Created by Denny
****************************************************************************************************/

#include "MotorPoleFind.h"
#include "MLib.h"

PRIVATE ALMID_T	MpoleFindDetectAlarm( MPFIND *MpFind, MPFEXEINFO *MpfExeInfo );
PRIVATE ALMID_T	MpoleFindCheckMovingRange( MPFIND *MpFind, INT32 dMotPos, INT32 Kmovpos );
PRIVATE	void	MpoleFindDrive( MPFIND *MpFind, INT32 SpdFbki, INT32 TrqRefo );
PRIVATE	void	CheckMpFindFail( MPFIND *MpFind, INT32 SpdFbki );
PRIVATE	void	MpFindLauSpdRef( MPFIND *MpFind, INT32 SpdFbki );
PRIVATE	void	MpFindCalPosAbout( MPFIND *MpFind );
PRIVATE	void	MpFindCalPos( INT32 *DegCalc, INT32 *Counter, INT32 *Degree, BOOL *NoGood_flag );
PRIVATE	void	MpFindGetMaxTrqSpd( MPFIND *MpFind, INT32 SpdFbki, INT32 TrqRefo );
PRIVATE	void	JudgeMpFindErrSign( MPFIND *MpFind );
PRIVATE	void	UpdatePhaseCalculation( MPFIND *MpFind );
PRIVATE	void	StopMpoleFind( MPFIND *MpFind, BOOL MotStopSts );
PRIVATE	BOOL	ConfirmMpoleFind( MPFIND *MpFind, INT32 Kphasepos, INT32 dMotPos, INT32 SpdFbki );
PRIVATE	INT32	MpFindLauTrqRef( MPFIND *MpFind, BOOL *WaitFinish );
/****************************************************************************************************/


/****************************************************************************************************
 * DESCRIPTION:
 *		 Speed control calculation for magnetic pole detection
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MpoleFindSpeedControl( MPFIND *MpFind, MPFEXEINFO *MpfExeInfo, BOOL BeComplete )
{
	INT32			PacOut;
	INT32			IacOut;
	INT32			TrqRefx;
	INT32			work_integless;
	INT32			work;

/*--------------------------------------------------------------------------------------------------*/
/*	Processing when requesting torque control for confirming phase correction value					*/
/*--------------------------------------------------------------------------------------------------*/
	/* Check Control Mode Change Flag */
	if( MpFind->var.PhaseConfFlg == TRUE )
	{
		/* Direct Torque Reference Input */
		MpFind->var.Ivar64[1] = MpFind->var.Ivar64[0] = 0;
		return( MpFind->var.TrqRefPhaConf );
	}

	MpFind->var.SpdFbFilo = MlibLpfilter1( MpfExeInfo->SpdFbMonitor,
										   MpfExeInfo->KVfbFil,
										   MpFind->var.SpdFbFilo );

	MpFind->var.SpdErr = MpFind->MpFindSpdRef - MpFind->var.SpdFbFilo;

	if( (BeComplete == FALSE) || (MpFind->var.ZeroTorque == TRUE) )
	{
		MpFind->TrqFilOut = 0;
		MpFind->var.Ivar64[1] = MpFind->var.Ivar64[0] = 0;
		return( 0 );
	}

	PacOut = MpFind->var.SpdErr;

	if( MpFind->var.ClrIntegral )
	{
		IacOut = 0;
		work_integless = 0;
		MpFind->var.Ivar64[0] = 0;
		MpFind->var.Ivar64[1] = 0;
		MpFind->var.ClrIntegral = FALSE;
	}
	else
	{
		if( !MpFind->var.InteglessON )
		{
			work_integless = 0;
		}
		else
		{
			work_integless = MlibMulgainNolim( (MpFind->var.Ivar64[1] >> 1), MpFind->conf.InteglessGain );
		}
	}
	work = MpFind->var.SpdErr - work_integless;
	IacOut = MlibIntegral( work, MpFind->conf.Ki, MpFind->var.Ivar64 );

	work = PacOut + IacOut;

	/* Automatic gain adjustment */
	switch( MpFind->var.KvGain )
	{
		case 0 :		/* gain * 1 */
			break;

		case 1 :		/* gain * 1.5 */
			work = work + (work >> 1);
			break;

		case 2 :		/* gain * 2 */
			work = (work << 1);
			break;

		case -1 :		/* gain * 0.75 */
			work = (work >> 1) + (work >> 2);
			break;

		case -2 :		/* gain * 0.5 */
			work = (work >> 1);
			break;

		default :
			break;
	}

	TrqRefx = MlibMulgain( work, MpFind->conf.Kv );


	MpFind->TrqFilOut = MlibLpfilter1( TrqRefx, MpFind->conf.Klpf, MpFind->TrqFilOut );
	return( MlibLimitul(MpFind->TrqFilOut, 0x01000000, -0x01000000) );

}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Magnetic pole error detection Alarm detection
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE ALMID_T	MpoleFindDetectAlarm( MPFIND *MpFind, MPFEXEINFO *MpfExeInfo )
{
	ALMID_T		AlarmCode;

	if(MpfExeInfo->HaltCondition != FALSE)
	{
		MpFind->MPJudgeMonitor[0] = 0;	
		MpFind->MPJudgeMonitor[1] = 0;	
		MpFind->MPJudgeMonitor[2] = 0;	
		AlarmCode = ALM_PFINDSTOP;	
	}
	else if(MpFind->var.FailTimes >= TRYCOUNT)
	{
		AlarmCode = ALM_POLE;		
	}
	else
	{
		AlarmCode = MpoleFindCheckMovingRange(MpFind,
		                                      MpfExeInfo->dMotPosition,
		                                      MpfExeInfo->Kmovpos);
	}

	return	AlarmCode;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Magnetic pole detection movable range confirmation processing
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE ALMID_T	MpoleFindCheckMovingRange( MPFIND *MpFind, INT32 dMotPos, INT32 Kmovpos )
{
	INT32		lwk;
	ALMID_T		AlarmCode;
	AlarmCode = NO_ALARM;

	MpFind->UnMpFindingTime++;

	MpFind->var.MovPos += dMotPos;

	if( MpFind->var.MovPos > MpFind->UnMpfMovePosP )
	{
		MpFind->UnMpfMovePosP = MpFind->var.MovPos;
	}
	else if( MpFind->var.MovPos < MpFind->UnMpfMovePosN )
	{
		MpFind->UnMpfMovePosN = MpFind->var.MovPos;
	}

	if( MpFind->UnMpfMovePosP >= -MpFind->UnMpfMovePosN )
	{
		lwk = MpFind->UnMpfMovePosP;
	}
	else
	{
		lwk = -MpFind->UnMpfMovePosN;
	}

	if( MpFind->conf.DetectRange < MlibMulgain( lwk, Kmovpos ) )
	{
		AlarmCode = ALM_PDET_MOVEOVER;
	}

	return	AlarmCode;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Magnetic pole detection processing sequence
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC ALMID_T MpoleFindSequence( MPFIND *MpFind, MPFEXEINFO *MpfExeInfo, BOOL PdetReq, BOOL BaseEnable )
{
	ALMID_T		AlarmCode;
	AlarmCode = NO_ALARM;

	if( PdetReq == FALSE )
	{
		MpFind->var.Step = MPF_INIT;
	}

/*--------------------------------------------------------------------------------------------------*/
/*		Magnetic pole detection processing sequence													*/
/*--------------------------------------------------------------------------------------------------*/
	switch( MpFind->var.Step )
	{
	/*----------------------------------------------------------------------------------------------*/
	case MPF_INIT:			/* Initial Step															*/
	/*----------------------------------------------------------------------------------------------*/
		if( (PdetReq & BaseEnable) == TRUE )
		{ /* Magnetic pole detection sequence start request */
			MlibResetLongMemory( &MpFind->var, sizeof( MpFind->var ) >> 2 );

			MpFind->UnMpFindingTime = 0;
			MpFind->UnMpfMovePosP = 0;
			MpFind->UnMpfMovePosN = 0;

			MpFind->var.Step = MPF_INIT2;
		}
		break;
	/*----------------------------------------------------------------------------------------------*/
		case MPF_INIT2:		/* Initial Step2														*/
	/*----------------------------------------------------------------------------------------------*/
			MpFind->var.Ivar64[0] = 0;
			MpFind->var.Ivar64[1] = 0;
			MpFind->var.OSFail = FALSE;
			MpFind->var.dAxisMaxTrqPlus = 0;
			MpFind->var.dAxisMaxTrqMinus = 0;
			MpFind->var.qAxisMaxTrqPlus = 0;
			MpFind->var.qAxisMaxTrqMinus = 0;

			MpFind->var.dAxisMaxSpdPlus = 0;
			MpFind->var.dAxisMaxSpdMinus = 0;
			MpFind->var.qAxisMaxSpdPlus = 0;
			MpFind->var.qAxisMaxSpdMinus = 0;

			MpFind->var.DegRev = 0;
			MpFind->var.DegFinal = 0;
			MpFind->var.AreaCheckStep = 0;
			MpFind->var.ReverseCtr = 0;
			MpFind->var.NoGood = 0;

			/* Offset speed (speed at speed command start)*/
			MpFind->var.SpdOffset = MpfExeInfo->SpdFbMonitor;

			MpFind->var.Step = MPF_DRIVE;
			break;

	case MPF_DRIVE:			
		MpoleFindDrive( MpFind, MpfExeInfo->SpdFbMonitor, MpfExeInfo->TrefMonitor );
		AlarmCode = MpoleFindDetectAlarm(MpFind, MpfExeInfo);
		break;

	case MPF_CAL:			
		AlarmCode = MpoleFindDetectAlarm(MpFind, MpfExeInfo);
		UpdatePhaseCalculation( MpFind );
		break;

	case MPF_STOP:			
		AlarmCode = MpoleFindDetectAlarm(MpFind, MpfExeInfo);
		StopMpoleFind( MpFind, MpfExeInfo->MotStopSts );
		break;

	case MPF_CONFIRM:		
		AlarmCode = MpoleFindDetectAlarm(MpFind, MpfExeInfo);
		MpFind->var.PhaseConfFlg = TRUE;	/* Flag for torque control to confirm the phase correction value*/
		if( FALSE != ConfirmMpoleFind( MpFind,
									   MpfExeInfo->Kphasepos,
									   MpfExeInfo->dMotPosition,
									   MpfExeInfo->SpdFbMonitor ) )
		{
			AlarmCode = ALM_PDET_NG;
		}
		break;

	case MPF_END:			
		MpFind->var.PhaseConfFlg = FALSE;
		MpFind->var.Ivar64[0] = 0;
		MpFind->var.Ivar64[1] = 0;
		break;

	default:			
		MpFind->var.Step = MPF_INIT;
		AlarmCode = ALM_POLE;
		break;
	}

	return	AlarmCode;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Magnetic pole detection operation processing
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void MpoleFindDrive( MPFIND *MpFind, INT32 SpdFbki, INT32 TrqRefo )
{
/*--------------------------------------------------------------------------------------------------*/
/*		Operation sequence status check																*/
/*--------------------------------------------------------------------------------------------------*/
	if( !MpFind->var.Fail )
	{
		MpFind->var.PastTime++;
	}

/*--------------------------------------------------------------------------------------------------*/
/*		Reverse running & speed over check processing												*/
/*--------------------------------------------------------------------------------------------------*/
	CheckMpFindFail( MpFind, SpdFbki );

/*--------------------------------------------------------------------------------------------------*/
/*		Speed command creation process																*/
/*--------------------------------------------------------------------------------------------------*/
	MpFindLauSpdRef( MpFind, SpdFbki );

	if( !MpFind->var.RangeFix )
	{
/*--------------------------------------------------------------------------------------------------*/
/*		Check all areas divided into 8																*/
/*--------------------------------------------------------------------------------------------------*/
		switch( MpFind->var.RangeFixStep )
		{
		case PAREACHECK_000:
			if ( MpFind->var.PhaseChange )
			{
				/* Processing when determining reverse run */
				MpFind->MpfPhaseOffset += PHASE_DEG180;
				MpFind->var.PhaseChange = FALSE;
				MpFind->var.RangeFixStep = PAREACHECK_001;
			}
			else if ( MpFind->var.PastTime == 0 )
			{
				if ( MpFind->var.AreaCheckStep == 7 )
				{
					/* Calculate rough position from maximum speed and reverse running position */
					MpFindCalPosAbout( MpFind );

					/* Redo check */
					switch ( MpFind->var.NoGood )
					{
					case 0:
						MpFind->MpfPhaseOffset += MpFind->var.DegFinal + PHASE_DEG090;
						MpFind->var.RangeFix = TRUE;
						MpFind->var.Direction = FALSE;
						MpFind->var.AxisChange = FALSE;
						break;

					case 1:
						/* Increase gain */
						MpFind->var.KvGain++;
						MpFind->MpfPhaseOffset = 0;
						MpFind->var.RangeFixStep = PAREACHECK_000;
						MpFind->var.FailTimes++;						/* Increase the number of failures by 1. */
						MpFind->var.Step = MPF_INIT2;					/* Start over from the initialization process */
						break;
					}
				}
				else
				{
					MpFind->MpfPhaseOffset += PHASE_DEG045;
					MpFind->var.RangeFixStep = PAREACHECK_000;
					MpFind->var.AreaCheckStep++;
				}
			}
			break;

		case PAREACHECK_001:
			if ( MpFind->var.PastTime == 0 )
			{
				/* Get reverse position */
				if ( MpFind->var.ReverseCtr <= 3 )
				{
					MpFind->var.DegReverse[MpFind->var.ReverseCtr] = PHASE_DEG045 * MpFind->var.AreaCheckStep;
				}
				MpFind->var.ReverseCtr++;

				if ( MpFind->var.AreaCheckStep == 7 )
				{
					/* Calculate rough position from maximum speed and reverse running position */
					MpFindCalPosAbout( MpFind );

					/* Redo check */
					switch ( MpFind->var.NoGood )
					{
					case 0:
						MpFind->MpfPhaseOffset += MpFind->var.DegFinal - PHASE_DEG090;
						MpFind->var.RangeFix = TRUE;
						MpFind->var.Direction = FALSE;
						MpFind->var.AxisChange = FALSE;
						break;

					case 1:
						/* Increase gain */
						MpFind->var.KvGain++;
						MpFind->MpfPhaseOffset = 0;
						MpFind->var.RangeFixStep = PAREACHECK_000;
						MpFind->var.FailTimes++;						/* Increase the number of failures by 1. */
						MpFind->var.Step = MPF_INIT2;					/* Start over from the initialization process */
						break;
					}
				}
				else
				{
					MpFind->MpfPhaseOffset -= PHASE_DEG135;
					MpFind->var.RangeFixStep = PAREACHECK_000;
					MpFind->var.AreaCheckStep++;
				}
			}
			break;

		case PAREACHECK_002:
			if ( MpFind->var.PastTime == 0 )
			{
				MpFind->MpfPhaseOffset = 0;
				MpFind->var.KvGain--;									/* Lower the gain */
				MpFind->var.RangeFixStep = PAREACHECK_000;
				MpFind->MPJudgeMonitor[MpFind->var.FailTimes] = 5;		/* Judgment monitor set */
				MpFind->var.FailTimes++;								/* Increase the number of failures by 1. */
				MpFind->var.Step = MPF_INIT2;							/* Start over from the initialization process */
			}
			break;

		case PAREACHECK_003:
			MpFind->MpfPhaseOffset = 0;
			MpFind->var.KvGain--;										/* Lower the gain */
			MpFind->var.RangeFixStep = PAREACHECK_000;
			MpFind->MPJudgeMonitor[MpFind->var.FailTimes] = 4;		
			MpFind->var.FailTimes++;									
			MpFind->var.Step = MPF_INIT2;							
			break;

		default :
			MpFind->var.RangeFixStep = PAREACHECK_000;
			MpFind->MpfPhaseOffset = 0;
			break;
		}
	}
	else
	{
/*--------------------------------------------------------------------------------------------------*/
/*		Estimated angle narrowing down based on dq axis shift										*/
/*--------------------------------------------------------------------------------------------------*/
		if ( MpFind->var.PhaseChange )
		{
			MpFind->MpfPhaseOffset += PHASE_DEG180;
			MpFind->var.Times = 0;
			MpFind->var.RangeFixStep = PAREACHECK_002;
			MpFind->var.RangeFix = FALSE;
			MpFind->var.PhaseChange = FALSE;
		}
		else
		{
			MpFindGetMaxTrqSpd( MpFind, SpdFbki, TrqRefo );

			if ( MpFind->var.PastTime == 0 )
			{
				if ( MpFind->var.AxisChange )
				{
					MpFind->MpfPhaseOffset -= PHASE_DEG090;
				}
				else
				{
					MpFind->var.Times++;
					JudgeMpFindErrSign( MpFind );

					if ( MpFind->var.Step != MPF_CAL )
					{
						switch ( MpFind->var.AxisErrSign )
						{
							case AXISERR_EQUAL :
							case AXISERR_MINUS :
								MpFind->MpfPhaseOffset += PHASE_DEG090 + (PHASE_DEG030 >> MpFind->var.Times);
								break;

							case AXISERR_PLUS :
								MpFind->MpfPhaseOffset += PHASE_DEG090 - (PHASE_DEG030 >> MpFind->var.Times);
								break;

							default :
								break;
						}
					}
				}
			}
		}
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Error detection: Reverse run and overspeed judgment.
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void CheckMpFindFail( MPFIND *MpFind, INT32 SpdFbki )
{
	INT32	work;
	INT32	OverSpeed;

	/* Reverse run judgment level setting */
	if ( MpFind->var.ReverseLevel )
	{
		/* During waiting time */
		work = MpFind->conf.ReverseSpd2;
	}
	else
	{
		/* During speed command */
		work = MpFind->conf.ReverseSpd;
	}

	/* Set runaway judgment level */
	if ( MpFind->var.OSFail )
	{
		/* Level after taking overspeed */
		OverSpeed = MpFind->conf.MaxSpd;
	}
	else
	{
		/* Normal level */
		OverSpeed = MpFind->conf.OverSpd;
	}

	/* Always overspeed judgment */
	if ( MlibABS( SpdFbki ) >= OverSpeed )
	{
		MpFind->var.PastTime = MpFind->conf.WaitTime;
		MpFind->var.Fail = TRUE;
		MpFind->var.OSFail = TRUE;
		MpFind->var.ZeroTorque = TRUE;
	}

	/* Reverse running is not being executed & reverse running is detected at step 1. */
	if ( (!MpFind->var.Fail) && (!MpFind->var.RangeFix) )
	{
		if ( !MpFind->var.Direction )
		{
			/* Forward speed command */
			if ( (SpdFbki - MpFind->var.SpdOffset) < ( -work ) )
			{
				MpFind->var.Fail = TRUE;
				MpFind->var.PhaseChange = TRUE;
				MpFind->var.PastTime = MpFind->conf.WaitTime;
				MpFind->var.ClrIntegral = TRUE;
			}
		}
		else
		{
			/* Negative speed command */
			if ( (SpdFbki - MpFind->var.SpdOffset) > work )
			{
				MpFind->var.Fail = TRUE;
				MpFind->var.PhaseChange = TRUE;
				MpFind->var.PastTime = MpFind->conf.WaitTime;
				MpFind->var.ClrIntegral = TRUE;
			}
		}
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Speed command creation: Create the operation command when the magnetic pole is detected.
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void MpFindLauSpdRef( MPFIND *MpFind, INT32 SpdFbki )
{
	INT32	AbsSpdRef;

/*--------------------------------------------------------------------------------------------------*/
/*		Processing when an error is detected														*/
/*--------------------------------------------------------------------------------------------------*/
	if( MpFind->var.Fail )
	{
		MpFind->MpFindSpdRef = 0;
		MpFind->var.InteglessON = FALSE;

		if( --MpFind->var.PastTime <= 0 )
		{
			MpFind->var.RefOutTime = 0;
			MpFind->var.Fail = FALSE;
			MpFind->var.RefOutStep = MPFLAU_ACC;
			MpFind->var.Direction = FALSE;
			MpFind->var.AxisChange = FALSE;
			MpFind->var.ZeroTorque = FALSE;
			MpFind->var.ReverseLevel = FALSE;
			MpFind->var.SpdOffset = SpdFbki;

			if ( MpFind->var.OSFail )
			{
				MpFind->var.RangeFixStep = PAREACHECK_003;
			}
		}
		return;
	}

	AbsSpdRef = MlibABS( MpFind->MpFindSpdRef );

/*--------------------------------------------------------------------------------------------------*/
/*		Speed command creation sequence																*/
/*--------------------------------------------------------------------------------------------------*/
	switch( MpFind->var.RefOutStep )
	{
	/*----------------------------------------------------------------------------------------------*/
	case MPFLAU_INIT:		/* Wait time has passed													*/
	/*----------------------------------------------------------------------------------------------*/
		MpFind->var.RefOutStep = MPFLAU_ACC;
		MpFind->var.RefOutTime = 0;
		MpFind->var.SpdOffset = SpdFbki;
		break;

	/*----------------------------------------------------------------------------------------------*/
	case MPFLAU_ACC:		/* Acceleration command creation															*/
	/*----------------------------------------------------------------------------------------------*/
		AbsSpdRef = MlibLaufilter( MpFind->conf.MaxSpd, AbsSpdRef, MpFind->conf.dSpd );
		MpFind->var.RefOutTime++;
		if( MpFind->var.RefOutTime >= MpFind->conf.AccTime )
		{
			MpFind->var.RefOutStep = MPFLAU_CONST;
			MpFind->var.RefOutTime = 0;
		}
		break;

	/*----------------------------------------------------------------------------------------------*/
	case MPFLAU_CONST:		/* Create steady speed command											*/
	/*----------------------------------------------------------------------------------------------*/
		AbsSpdRef = MpFind->conf.MaxSpd;
		MpFind->var.RefOutTime++;
		if( MpFind->var.RefOutTime >= MpFind->conf.ClmpTime )
		{
			MpFind->var.RefOutStep = MPFLAU_DEC;
			MpFind->var.RefOutTime = 0;
		}
		break;

	/*----------------------------------------------------------------------------------------------*/
	case MPFLAU_DEC:		/* Create deceleration command											*/
	/*----------------------------------------------------------------------------------------------*/
		AbsSpdRef = MlibLaufilter( 0, AbsSpdRef, MpFind->conf.dSpd );
		MpFind->var.RefOutTime++;
		if( MpFind->var.RefOutTime >= MpFind->conf.AccTime )
		{
			MpFind->var.RefOutStep = MPFLAU_WAIT;
			MpFind->var.RefOutTime = 0;
		}
		break;

	/*----------------------------------------------------------------------------------------------*/
	case MPFLAU_WAIT:		/* Wait time has passed													*/
	/*----------------------------------------------------------------------------------------------*/
		AbsSpdRef = 0;
		MpFind->var.RefOutTime++;
		MpFind->var.ReverseLevel = TRUE;
		if ( MpFind->var.RefOutTime >= MpFind->conf.InteglessTime )
		{
			MpFind->var.InteglessON = TRUE;
		}

		if( MpFind->var.RefOutTime >= MpFind->conf.WaitTime )
		{
			if( MpFind->var.Direction )
			{
				MpFind->var.PastTime = 0;
				if( MpFind->var.AxisChange )
				{
					MpFind->var.AxisChange = FALSE;
				}
				else
				{
					MpFind->var.AxisChange = TRUE;
				}
			}
			if( MpFind->var.Direction )
			{
				MpFind->var.Direction = FALSE;
			}
			else
			{
				MpFind->var.Direction = TRUE;
			}
			MpFind->var.RefOutStep = MPFLAU_ACC;
			MpFind->var.RefOutTime = 0;
			MpFind->var.InteglessON = FALSE;
			MpFind->var.ReverseLevel = FALSE;
			MpFind->var.SpdOffset = SpdFbki;
		}
		break;

	/*----------------------------------------------------------------------------------------------*/
	default:
	/*----------------------------------------------------------------------------------------------*/
		AbsSpdRef = 0;
		break;
	}

/*--------------------------------------------------------------------------------------------------*/
/*		Speed command output																		*/
/*--------------------------------------------------------------------------------------------------*/
	if( MpFind->var.Direction )
	{
		MpFind->MpFindSpdRef = -AbsSpdRef;
	}
	else
	{
		MpFind->MpFindSpdRef = AbsSpdRef;
	}

	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Rough position calculation function: Calculates rough position from reverse running position
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void MpFindCalPosAbout( MPFIND *MpFind )
{
	BOOL	NG_flag;

	/* Check the number of reverse runs */
	if( MpFind->var.ReverseCtr >= 5 )
	{
		/* Increase the gain and try again */
		MpFind->var.NoGood = TRUE;
		MpFind->MPJudgeMonitor[MpFind->var.FailTimes] = 1;			/* Judgment monitor set */
		return;
	}

	/* Check the number of reverse runs */
	if( MpFind->var.ReverseCtr <= 1 )
	{
		/* Increase the gain and try again */
		MpFind->var.NoGood = TRUE;
		MpFind->MPJudgeMonitor[MpFind->var.FailTimes] = 2;			/* Judgment monitor set */
		return;
	}

	/* Calculate reverse position */
	NG_flag = FALSE;
	MpFindCalPos( &MpFind->var.DegRev, &MpFind->var.ReverseCtr,
													&MpFind->var.DegReverse[0], &NG_flag );
	/* Redo judgment */
	if( NG_flag )
	{
		/* Increase the gain and try again */
		MpFind->var.NoGood = TRUE;
		MpFind->MPJudgeMonitor[MpFind->var.FailTimes] = 3;			/* Judgment monitor set */
		return;
	}
	MpFind->var.DegRev += PHASE_DEG180;

	MpFind->var.DegFinal = MpFind->var.DegRev;

	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Position calculation function: Calculate position (used in MpFindCalPosAbout() function)
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void MpFindCalPos( INT32 *DegCalc, INT32 *Counter, INT32 *Degree, BOOL *NoGood_flag )
{
	UINT32	lwork1, lwork2;
	UINT16	work[3];
	UINT16	i;

	*NoGood_flag = FALSE;
	lwork1 = lwork2 = 0;

	if( *Counter == 1 )
	{
		lwork1 = *Degree;
	}
	else if( *Counter == 2 )
	{
		lwork1 = (UINT32)*Degree + (UINT32)*(Degree + 1);
		work[0] = (UINT16)( *(Degree + 1) - *Degree );

		if( work[0] > (UINT16)PHASE_DEG180 )
		{
			lwork1 = (lwork1 - (UINT32)PHASE_DEG360) >> 1;
		}
		else if( work[0] < (UINT16)PHASE_DEG180 )
		{
			lwork1 = lwork1 >> 1;
		}
		else
		{
			/* Set the NoGood_flag because the reverse phase was 180 degrees out of phase */
			*NoGood_flag = TRUE;
		}
	}
	else if( *Counter == 3 )
	{
		lwork1 = (UINT32)*Degree + (UINT32)*(Degree + 1) + (UINT32)*(Degree + 2);
		work[0] = (UINT16)( *(Degree + 2) - *Degree );
		work[1] = (UINT16)( *(Degree + 1) - *Degree );
		work[2] = (UINT16)( *(Degree + 2) - *(Degree + 1) );

		if( work[0] <= (UINT16)PHASE_DEG135 )
		{
			lwork1 = lwork1 / 3;
		}
		else if( work[1] >= (UINT16)PHASE_DEG225 )
		{
			lwork1 = (lwork1 + (UINT32)PHASE_DEG360) / 3;
		}
		else if( work[2] >= (UINT16)PHASE_DEG225 )
		{
			lwork1 = (lwork1 + (UINT32)PHASE_DEG360 + (UINT32)PHASE_DEG360) / 3;
		}
		else
		{
			/* NoGood_flag is set because the reverse phase was outside the 135deg range. */
			*NoGood_flag = TRUE;
		}
	}
	else if( *Counter == 4 )
	{
		lwork1 = (UINT32)*Degree + (UINT32)*(Degree + 1) + (UINT32)*(Degree + 2) + (UINT32)*(Degree + 3);
		work[0] = (UINT16)( *(Degree + 1) - *Degree );
		work[1] = (UINT16)( *(Degree + 2) - *(Degree + 1) );
		work[2] = (UINT16)( *(Degree + 3) - *(Degree + 2) );

		lwork2 = 0;
		for( i = 0; i <= 2; i++ )
		{
			if( (work[i] != (UINT16)PHASE_DEG045) && (work[i] != (UINT16)PHASE_DEG225) )
			{
				/* NoGood_flag is set because the reverse phase was outside the 135deg range. */
				*NoGood_flag = TRUE;
				break;
			}
			else if( work[i] == (UINT16)PHASE_DEG225 )
			{
				lwork2 = ((UINT32)PHASE_DEG360 * (i+1));
			}
		}

		if( *NoGood_flag == FALSE )
		{
			lwork1 = (lwork1 + lwork2) >> 2;
		}
	}
	*DegCalc = lwork1;
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Maximum torque & maximum speed acquisition: 
 *       Get maximum torque and maximum speed of each d-q axis during operation
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void MpFindGetMaxTrqSpd( MPFIND *MpFind, INT32 SpdFbki, INT32 TrqRefo )
{
	if( !MpFind->var.AxisChange )
	{
		/* q axis */
		if( !MpFind->var.Direction )
		{
			/* Forward speed command */
			if( MpFind->var.qAxisMaxTrqPlus < TrqRefo )
			{
				MpFind->var.qAxisMaxTrqPlus = TrqRefo;
			}
			if( MpFind->var.qAxisMaxSpdPlus < SpdFbki )
			{
				MpFind->var.qAxisMaxSpdPlus = SpdFbki;
			}
		}
		else
		{
			/* Negative speed command */
			if( MpFind->var.qAxisMaxTrqMinus > TrqRefo )
			{
				MpFind->var.qAxisMaxTrqMinus = TrqRefo;
			}
			if( MpFind->var.qAxisMaxSpdMinus > SpdFbki )
			{
				MpFind->var.qAxisMaxSpdMinus = SpdFbki;
			}
		}
	}
	else
	{
		/* d axis */
		if( !MpFind->var.Direction )
		{
			/* Forward speed command */
			if( MpFind->var.dAxisMaxTrqPlus < TrqRefo )
			{
				MpFind->var.dAxisMaxTrqPlus = TrqRefo;
			}
			if( MpFind->var.dAxisMaxSpdPlus < SpdFbki )
			{
				MpFind->var.dAxisMaxSpdPlus = SpdFbki;
			}
		}
		else
		{
			/* Negative speed command */
			if( MpFind->var.dAxisMaxTrqMinus > TrqRefo )
			{
				MpFind->var.dAxisMaxTrqMinus = TrqRefo;
			}
			if( MpFind->var.dAxisMaxSpdMinus > SpdFbki )
			{
				MpFind->var.dAxisMaxSpdMinus = SpdFbki;
			}
		}
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Calculate d-q axis deviation angle:
 *       Calculate deviation angle from the relationship of maximum torque of each d-q axis
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void JudgeMpFindErrSign( MPFIND *MpFind )
{
	INT32	lwork1, lwork2;
	INT32	lwork3, lwork4;
	INT32	kx;

	lwork1 = MlibABS( MpFind->var.dAxisMaxTrqPlus - MpFind->var.qAxisMaxTrqPlus );
	lwork2 = MlibABS( MpFind->var.dAxisMaxTrqMinus - MpFind->var.qAxisMaxTrqMinus );
	lwork3 = MlibABS( MpFind->var.dAxisMaxSpdPlus - MpFind->var.qAxisMaxSpdPlus );
	lwork4 = MlibABS( MpFind->var.dAxisMaxSpdMinus - MpFind->var.qAxisMaxSpdMinus );

	if( lwork1 == lwork2 )
	{
		if( lwork3 < lwork4 )
		{
			/* Negative direction adoption */
			lwork1 = MlibABS( MpFind->var.dAxisMaxTrqMinus );
			lwork2 = MlibABS( MpFind->var.qAxisMaxTrqMinus );
			lwork3 = MlibABS( MpFind->var.dAxisMaxSpdMinus );
			lwork4 = MlibABS( MpFind->var.qAxisMaxSpdMinus );
		}
		else
		{
			/* Positive direction adoption */
			lwork1 = MpFind->var.dAxisMaxTrqPlus;
			lwork2 = MpFind->var.qAxisMaxTrqPlus;
			lwork3 = MpFind->var.dAxisMaxSpdPlus;
			lwork4 = MpFind->var.qAxisMaxSpdPlus;
		}
	}
	else if( lwork1 < lwork2 )
	{
		/* Negative direction adoption */
		lwork1 = MlibABS( MpFind->var.dAxisMaxTrqMinus );
		lwork2 = MlibABS( MpFind->var.qAxisMaxTrqMinus );
		lwork3 = MlibABS( MpFind->var.dAxisMaxSpdMinus );
		lwork4 = MlibABS( MpFind->var.qAxisMaxSpdMinus );
	}
	else
	{
		/* Positive direction adoption */
		lwork1 = MpFind->var.dAxisMaxTrqPlus;
		lwork2 = MpFind->var.qAxisMaxTrqPlus;
		lwork3 = MpFind->var.dAxisMaxSpdPlus;
		lwork4 = MpFind->var.qAxisMaxSpdPlus;
	}

	if( lwork1 == lwork2 )
	{
		lwork2 = 16384;
		lwork1 = AXISERR_EQUAL;
	}
	else if( lwork1 > lwork2 )
	{
		kx = MlibScalKxgain( lwork2, 1, lwork1, NULL, 24 );

		lwork2 = MlibMulgain( 16384, kx );

		lwork1 = AXISERR_MINUS;
	}
	else
	{
		kx = MlibScalKxgain( lwork1, 1, lwork2, NULL, 24 );

		lwork2 = MlibMulgain( 16384, kx );

		lwork1 = AXISERR_PLUS;
	}

	if( lwork3 == lwork4 )
	{
		lwork4 = 16384;
		lwork3 = AXISERR_EQUAL;
	}
	else if( lwork3 > lwork4 )
	{
		kx = MlibScalKxgain( lwork4, 1, lwork3, NULL, 24 );
		lwork4 = MlibMulgain( 16384, kx );
		lwork3 = AXISERR_PLUS;
	}
	else
	{
		kx = MlibScalKxgain( lwork3, 1, lwork4, NULL, 24 );
		lwork4 = MlibMulgain( 16384, kx );
		lwork3 = AXISERR_MINUS;
	}

	if( MpFind->var.Times >= MpFind->conf.RepeatNum )
	{
		MpFind->MpFindSpdRef = 0;
		MpFind->var.AdjDegree = SINPHASE_045;
		MpFind->var.AdjSwpDeg = SINPHASE_045;
		MpFind->var.Step = MPF_CAL;
	}
	else
	{
		MpFind->var.dAxisMaxTrqPlus = 0;
		MpFind->var.qAxisMaxTrqPlus = 0;
		MpFind->var.dAxisMaxTrqMinus = 0;
		MpFind->var.qAxisMaxTrqMinus = 0;
		MpFind->var.dAxisMaxSpdPlus = 0;
		MpFind->var.qAxisMaxSpdPlus = 0;
		MpFind->var.dAxisMaxSpdMinus = 0;
		MpFind->var.qAxisMaxSpdMinus = 0;
	}

	if( lwork1 == lwork3 )
	{
		MpFind->var.AxisErrSign = (UCHAR)lwork1;
		MpFind->var.AxisErr = lwork2;
	}
	else if( lwork2 <= lwork4 )
	{
		MpFind->var.AxisErrSign = (UCHAR)lwork1;
		MpFind->var.AxisErr = lwork2;
	}
	else
	{
		MpFind->var.AxisErrSign = (UCHAR)lwork3;
		MpFind->var.AxisErr = lwork4;
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Phase calculation: 
 *       The phase is calculated from the data obtained in the operation process and the phase is updated.
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void UpdatePhaseCalculation( MPFIND *MpFind )
{
	INT32	lwk;

	MpFind->var.ClrIntegral = TRUE;
	MpFind->var.AdjSwpDeg >>= 1;

	if( MpFind->var.AxisErrSign != AXISERR_EQUAL )
	{
		lwk = ( MlibSins16( MpFind->var.AdjDegree, 0x11 ) << 14 ) /
				MlibSins16( MpFind->var.AdjDegree + SINPHASE_090, 0x11 );

		if( MpFind->var.AxisErr > MlibABS( lwk ) )
		{
			MpFind->var.AdjDegree += MpFind->var.AdjSwpDeg;
		}
		else
		{
			MpFind->var.AdjDegree -= MpFind->var.AdjSwpDeg;
		}
	}

	if( (MpFind->var.AdjSwpDeg == 1) || (MpFind->var.AxisErrSign == AXISERR_EQUAL) )
	{
		lwk = MpFind->var.AdjDegree << 2;

		switch( MpFind->var.AxisErrSign )
		{
		case AXISERR_EQUAL :
			MpFind->MpfPhaseOffset += PHASE_DEG045;
			break;

		case AXISERR_MINUS :
			MpFind->MpfPhaseOffset += PHASE_DEG090 - (INT16)lwk;
			break;

		case AXISERR_PLUS :
			MpFind->MpfPhaseOffset += (INT16)lwk;
			break;

		default :
			break;
		}
		MpFind->var.Step = MPF_STOP;
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Stop confirmation processing: Stop confirmation processing for magnetic pole detection is performed.
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void StopMpoleFind( MPFIND *MpFind, BOOL MotStopSts )
{
	if( MotStopSts == TRUE )
	{
		MlibResetLongMemory( &MpFind->var, sizeof( MpFind->var ) >> 2 );

		MpFind->var.RangeFix = TRUE;
		MpFind->var.Step = MPF_CONFIRM;
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Operation confirmation processing (current lock method): 
 *       Performs operation confirmation processing for magnetic pole detection.
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE BOOL ConfirmMpoleFind( MPFIND *MpFind, INT32 Kphasepos, INT32 dMotPos, INT32 SpdFbki )
{
	BOOL pdet_err;
	BOOL WaitFinish;
	pdet_err = FALSE;
	WaitFinish = FALSE;

	/* Thrust command creation */
	MpFind->var.TrqRefPhaConf = MpFindLauTrqRef( MpFind, &WaitFinish );

	switch( MpFind->var.ConfirmStep )
	{
		case MPFCSTEP_WAIT:
			/* If ConfirmStep is other than PHS CHANGE or ADJUST, check overspeed. */
			if( MlibABS( SpdFbki ) >= MpFind->conf.OverSpd )
			{
				pdet_err = TRUE;
			}
			/* Confirmation of thrust command creation processing start */
			if( MpFind->var.RefOutStep == MPFTRQ_ACC )
			{
				MpFind->var.ConfirmStep = MPFCSTEP_PHSCHANGE;
			}
			break;


		case MPFCSTEP_PHSCHANGE:
			/* If Confirm Step is PHS CHANGE or ADJUST, check the travel distance. */
			/* Create position for operation check */
			MpFind->var.MovPosPhaConf += dMotPos;
			if( MlibABS(MpFind->var.MovPosPhaConf) >= MpFind->conf.ErrOKLevel )
			{
				pdet_err = TRUE;
			}
			/* Temporary save of detection results */
			MpFind->var.PhsOffsetCalRslt = MpFind->MpfPhaseOffset;
			/* Phase change (current is sent to the d-axis) */
			MpFind->MpfPhaseOffset -= PHASE_DEG090;
			MpFind->var.ConfirmStep = MPFCSTEP_ADJUST;
			break;


		case MPFCSTEP_ADJUST:
			/* If Confirm Step is PHS CHANGE or ADJUST, check the travel distance. */
			/* Create position for operation check */
			MpFind->var.MovPosPhaConf += dMotPos;
			if( MlibABS(MpFind->var.MovPosPhaConf) >= MpFind->conf.ErrOKLevel )
			{
				pdet_err = TRUE;
			}
			
			/* To fix the phase, adjust the offset by the amount of movement. */
			MpFind->MpfPhaseOffset -= MlibMulgainNolim( dMotPos, Kphasepos );

			/* Confirm WAIT end timing */
			if( WaitFinish == TRUE )
			{
				if( MpFind->var.RefOutStep == MPFTRQ_DEC )
				{ /* End of constant speed operation */
					MpFind->var.ConfirmStep = MPFCSTEP_PHSSTORE;
				}
				else if( MpFind->var.RefOutStep == MPFTRQ_END )
				{ /* End of deceleration operation */
					MpFind->var.ConfirmStep = MPFCSTEP_PHSREVERT;
				}
				else
				{ /* End of acceleration operation */
					; /* No processing at the end of acceleration operation */
				}
			}
			break;


		case MPFCSTEP_PHSSTORE:
			/*If ConfirmStep is other than PHS CHANGE or ADJUST, check overspeed. */
			if( MlibABS( SpdFbki ) >= MpFind->conf.OverSpd )
			{
				pdet_err = TRUE;
			}
			/* Save the final phase. */
			MpFind->var.PhsOffsetComp = MpFind->MpfPhaseOffset;
			/* To fix the phase, adjust the offset by the amount of movement. */
			MpFind->MpfPhaseOffset -= MlibMulgainNolim( dMotPos, Kphasepos );

			MpFind->var.ConfirmStep = MPFCSTEP_ADJUST;
			break;


		case MPFCSTEP_PHSREVERT:
			/* If ConfirmStep is other than PHS CHANGE or ADJUST, check overspeed. */
			if( MlibABS( SpdFbki ) >= MpFind->conf.OverSpd )
			{
				pdet_err = TRUE;
			}
			/* Return the phase to its original value (apply current to the q-axis)*/
			MpFind->MpfPhaseOffset = MpFind->var.PhsOffsetComp + PHASE_DEG090;
			MpFind->var.ConfirmStep = MPFCSTEP_WAIT;
			MpFind->var.Step = MPF_END;
			break;

		default:
			break;
	}

	/* Calculation of correction amount of detection result(for Monitor) */
	MpFind->var.PhaseOffsetCalComp = MpFind->MpfPhaseOffset - MpFind->var.PhsOffsetCalRslt;

	return pdet_err;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Create thrust command: Create a thrust command when magnetic pole detection is confirmed.
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	INT32 MpFindLauTrqRef( MPFIND *MpFind, BOOL *WaitFinish )
{
	INT32	TrqRef;

	/* Average thrust */
	TrqRef = MpFind->var.TrqRefPhaConf;
/*--------------------------------------------------------------------------------------------------*/
/*		Thrust command creation sequence															*/
/*--------------------------------------------------------------------------------------------------*/
	switch( MpFind->var.RefOutStep )
	{
	/*----------------------------------------------------------------------------------------------*/
	case MPFTRQ_INIT:	/* Wait time has passed														*/
	/*----------------------------------------------------------------------------------------------*/
		TrqRef = 0;
		MpFind->var.ClrIntegral = TRUE;
		MpFind->var.RefOutStep = MPFTRQ_ACC;
		MpFind->var.RefOutTime = 0;
		break;

	/*----------------------------------------------------------------------------------------------*/
	case MPFTRQ_ACC:	/* Thrust command increase													*/
	/*----------------------------------------------------------------------------------------------*/
		TrqRef = MlibLaufilter( MpFind->conf.MaxTrq, TrqRef, MpFind->conf.dTrq );
		MpFind->var.RefOutTime++;
		if( MpFind->var.RefOutTime >= MpFind->conf.IncTime )
		{
			MpFind->var.RefOutStep = MPFTRQ_CONST;
			MpFind->var.RefOutTime = 0;
			*WaitFinish = TRUE;
		}
		break;

	/*----------------------------------------------------------------------------------------------*/
	case MPFTRQ_CONST:	/* 	Constant thrust command													*/
	/*----------------------------------------------------------------------------------------------*/
		TrqRef = MpFind->conf.MaxTrq;
		MpFind->var.RefOutTime++;
		if( MpFind->var.RefOutTime >= MpFind->conf.ConstTime )
		{
			MpFind->var.RefOutStep = MPFTRQ_DEC;
			MpFind->var.RefOutTime = 0;
			*WaitFinish = TRUE;
		}
		break;

	/*----------------------------------------------------------------------------------------------*/
	case MPFTRQ_DEC:	/* Thrust command decrease													*/
	/*----------------------------------------------------------------------------------------------*/
		TrqRef = MlibLaufilter( 0, TrqRef, MpFind->conf.dTrq );
		MpFind->var.RefOutTime++;
		if( MpFind->var.RefOutTime >= MpFind->conf.IncTime )
		{
			MpFind->var.RefOutStep = MPFTRQ_END;
			MpFind->var.RefOutTime = 0;
			*WaitFinish = TRUE;
		}
		break;

	/*----------------------------------------------------------------------------------------------*/
	case MPFTRQ_END:	/* Wait time has passed														*/
	/*----------------------------------------------------------------------------------------------*/
		MpFind->var.ClrIntegral = TRUE;
		TrqRef = 0;
		MpFind->var.RefOutStep = MPFTRQ_ACC;
		MpFind->var.RefOutTime = 0;
		break;

	/*----------------------------------------------------------------------------------------------*/
	default :
	/*----------------------------------------------------------------------------------------------*/
		break;
	}

	return( MlibLimitul(TrqRef, MpFind->conf.MaxTrq, -MpFind->conf.MaxTrq) );
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Command speed parameter calculation for magnetic pole detection
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MpoleFind_CalcSpeedCommandPrm( MPFIND *MpFind, PRMDATA *PnPrm, 
															BPRMDAT *Bprm, MPFIND_UPRM *PdetPrm )

{
	INT32		work;

	if((PnPrm->PnMotPrm.MotType&0xFF) == MOTTYPE_ROTARY )
	{
		MpFind->conf.MaxSpd = Bprm->Kspdrpm * PdetPrm->pdetmaxspd_r;

	}
	else if( (PnPrm->PnMotPrm.MotType&0xFF) == MOTTYPE_LINEAR )
	{ 
		MpFind->conf.MaxSpd = Bprm->Kspdrpm * PdetPrm->pdetmaxspd;
	}

	MpFind->conf.ReverseSpd		= MpFind->conf.MaxSpd * PdetPrm->pdetrevlvspdref / 100;
	MpFind->conf.ReverseSpd2	= MpFind->conf.MaxSpd * PdetPrm->pdetrevlvwait / 100;
	MpFind->conf.OverSpd		= MpFind->conf.MaxSpd * PdetPrm->pdetoslevel / 100;

	MpFind->conf.dSpd			= MpFind->conf.MaxSpd * PS_CYCLEUS / 1000 / PdetPrm->pdetmaxt;
	MpFind->conf.AccTime		= PdetPrm->pdetmaxt * 1000 / PS_CYCLEUS;
	MpFind->conf.ClmpTime		= PdetPrm->pdetclmpt * 1000 / PS_CYCLEUS;
	MpFind->conf.WaitTime		= PdetPrm->pdetwait * 1000 / PS_CYCLEUS;
	MpFind->conf.InteglessTime	= MpFind->conf.WaitTime * PdetPrm->pdetinteglesst / 100;

	MpFind->conf.MaxTrq		= Bprm->Kmottrq * PdetPrm->pdetmaxtrq *10;
	MpFind->conf.dTrq		= MpFind->conf.MaxTrq * PS_CYCLEUS / 1000 / PdetPrm->pdetinctime;
	MpFind->conf.IncTime	= PdetPrm->pdetinctime * 1000 / PS_CYCLEUS;
	MpFind->conf.ConstTime	= PdetPrm->pdetconsttime * 1000 /PS_CYCLEUS;

}


/****************************************************************************************************
* DESCRIPTION:
*		Calculation of speed control parameters for magnetic pole detection
* RETURNS:
*
****************************************************************************************************/
PUBLIC void MpoleFind_CalcSpeedControlPrm( MPFIND *MpFind,UINT16 trqfil11, 
															 BPRMDAT *Bprm, MPFIND_UPRM *PdetPrm  )

{
	INT32		s;
	INT32		wk;

	MpFind->conf.Kv = MlibScalKskxkx( Bprm->Kvx, (100 + PdetPrm->pdetjrate) * PdetPrm->pdetloophz, 1000, NULL, 24 );
	MpFind->conf.Ki = MlibScalKxgain( 1, PS_CYCLEUS, 10*PdetPrm->pdetpitime, NULL, 24 );

	wk = MlibScalKxgain( PdetPrm->pdetintegless, 10*PdetPrm->pdetpitime, 10000, &s, 0 );
	MpFind->conf.InteglessGain = MlibPcalKxgain( wk, 1, PS_CYCLEUS, &s, 24 );

	MpFind->conf.Klpf = MlibPcalKf1gain( 10 * trqfil11, PS_CYCLEUS, 0 );

}


/****************************************************************************************************
* DESCRIPTION:
*		Magnetic pole detection error level calculation
* RETURNS:
*
****************************************************************************************************/
PUBLIC void MpoleFind_CalcErrorLevel( MPFIND *MpFind, PRMDATA *PnPrm, 
															 BPRMDAT *Bprm, MPFIND_UPRM *PdetPrm  )

{
	INT32		kx, sx;
	INT32		work;

	if( (PnPrm->PnMotPrm.MotType&0xFF) == MOTTYPE_LINEAR )
	{ 
		; // todo todo	
	}
	else
	{ 
		MpFind->conf.DetectRange = PdetPrm->pdetdistok_r;
		
		/* Convert electrical angle to pulse number. */
		MpFind->conf.ErrOKLevel =MlibScalKxgain( PdetPrm->pdetdegreeok, Bprm->FbPulse, 
		         								 (180 * PnPrm->PnMotPrm.MotPolePairs*2 ), NULL, -30 );
	}
}


