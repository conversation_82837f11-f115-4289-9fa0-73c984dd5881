/* generated HAL header file - do not edit */
#ifndef HAL_DATA_H_
#define HAL_DATA_H_
#include <stdint.h>
#include "bsp_api.h"
#include "common_data.h"
#include "r_spi.h"
#include "r_cmt.h"
#include "r_timer_api.h"
#include "r_dmac.h"
#include "r_transfer_api.h"
#include "r_dsmif.h"
#include "r_adc_api.h"
#include "r_xspi_qspi.h"
#include "r_spi_flash_api.h"
#include "r_spi.h"
#include "r_canfd.h"
#include "r_can_api.h"
#include "VersionDef.h"

FSP_HEADER
/* Transfer on DMAC Instance. */
extern const transfer_instance_t g_transfer0;

/** Access the DMAC instance using these structures when calling API functions directly (::p_api is not used). */
extern dmac_instance_ctrl_t g_transfer0_ctrl;
extern const transfer_cfg_t g_transfer0_cfg;

#ifndef NULL
void NULL(dmac_callback_args_t * p_args);
#endif

#ifndef spi_rxi_dmac_isr
extern void spi_rxi_dmac_isr(IRQn_Type irq);
#endif
/** SPI on SPI Instance. */
extern const spi_instance_t g_spi1;

/** Access the SPI instance using these structures when calling API functions directly (::p_api is not used). */
extern spi_instance_ctrl_t g_spi1_ctrl;
extern const spi_cfg_t g_spi1_cfg;

/** Callback used by SPI Instance. */
#ifndef spi_callback
void spi_callback(spi_callback_args_t * p_args);
#endif
/** SPI on SPI Instance. */
extern const spi_instance_t g_spi3;

/** Access the SPI instance using these structures when calling API functions directly (::p_api is not used). */
extern spi_instance_ctrl_t g_spi3_ctrl;
extern const spi_cfg_t g_spi3_cfg[3];

/** Callback used by SPI Instance. */
#ifndef spi_callback
void spi_callback(spi_callback_args_t * p_args);
#endif
/** Timer on CMT Instance. */
extern const timer_instance_t g_timer_free_run;

/** Access the CMT instance using these structures when calling API functions directly (::p_api is not used). */
extern cmt_instance_ctrl_t g_timer_free_run_ctrl;
extern const timer_cfg_t g_timer_free_run_cfg;

#ifndef NULL
void NULL(timer_callback_args_t * p_args);
#endif
/* Transfer on DMAC Instance. */
extern const transfer_instance_t g_sci_zx_tx_dma;

/** Access the DMAC instance using these structures when calling API functions directly (::p_api is not used). */
extern dmac_instance_ctrl_t g_sci_zx_tx_dma_ctrl;
extern const transfer_cfg_t g_sci_zx_tx_dma_cfg;

#ifndef sci_zx_tx_dma_transfer_end_isr
void sci_zx_tx_dma_transfer_end_isr(dmac_callback_args_t * p_args);
#endif

#ifndef NULL
extern void NULL(IRQn_Type irq);
#endif
/* Transfer on DMAC Instance. */
extern const transfer_instance_t g_sci_ss_tx_dma;

/** Access the DMAC instance using these structures when calling API functions directly (::p_api is not used). */
extern dmac_instance_ctrl_t g_sci_ss_tx_dma_ctrl;
extern const transfer_cfg_t g_sci_ss_tx_dma_cfg;

#ifndef dmac_callback_sci0_tx_end
void dmac_callback_sci0_tx_end(dmac_callback_args_t * p_args);
#endif

#ifndef dmac_callback_sci4_tx_end
void dmac_callback_sci4_tx_end(dmac_callback_args_t * p_args);
#endif

#ifndef NULL
extern void NULL(IRQn_Type irq);
#endif
/* Transfer on DMAC Instance. */
extern const transfer_instance_t g_iic_led_tx_dma;

/** Access the DMAC instance using these structures when calling API functions directly (::p_api is not used). */
extern dmac_instance_ctrl_t g_iic_led_tx_dma_ctrl;
extern const transfer_cfg_t g_iic_led_tx_dma_cfg;

#ifndef NULL
void NULL(dmac_callback_args_t * p_args);
#endif

#ifndef NULL
extern void NULL(IRQn_Type irq);
#endif
/** ADC on DSMIF Instance. */
            extern const adc_instance_t g_dsmif1;
            extern dsmif_instance_ctrl_t g_dsmif1_ctrl;
            extern const adc_cfg_t g_dsmif1_cfg;
            #ifndef NULL
            void NULL(adc_callback_args_t * p_args);
            #endif
/** ADC on DSMIF Instance. */
            extern const adc_instance_t g_dsmif0;
            extern dsmif_instance_ctrl_t g_dsmif0_ctrl;
            extern const adc_cfg_t g_dsmif0_cfg;
            #ifndef NULL
            void NULL(adc_callback_args_t * p_args);
            #endif
extern const spi_flash_instance_t g_qspi_flash;
            extern xspi_qspi_instance_ctrl_t g_qspi_flash_ctrl;
            extern const spi_flash_cfg_t g_qspi_flash_cfg;

/** CANFD on CANFD Instance. */
     /* Vector table allocations */
        #define VECTOR_NUMBER_CAN_RXF ((IRQn_Type) 316) /* CAN_RXF (CANFD RX FIFO interrupt) */
        #define VECTOR_NUMBER_CAN_GLERR ((IRQn_Type) 317) /* CAN_GLERR (CANFD Global error interrupt) */
        #define VECTOR_NUMBER_CAN0_TX ((IRQn_Type) 318) /* CAN0_TX (CANFD0 Channel TX interrupt) */
        #define VECTOR_NUMBER_CAN0_CHERR ((IRQn_Type) 319) /* CAN0_CHERR (CANFD0 Channel CAN error inter*/

extern const can_instance_t g_canfd0;
/** Access the CANFD instance using these structures when calling API functions directly (::p_api is not used). */
extern canfd_instance_ctrl_t g_canfd0_ctrl;
extern const can_cfg_t g_canfd0_cfg;
extern const canfd_extended_cfg_t g_canfd0_cfg_extend;

#ifndef canfd0_callback
void canfd0_callback(can_callback_args_t * p_args);
#endif

/* Global configuration (referenced by all instances) */
extern canfd_global_cfg_t g_canfd_global_cfg;
#ifndef NULL
void NULL(usb_event_info_t *, usb_hdl_t, usb_onoff_t);
#endif
/** CDC Driver on USB Instance. */
void hal_entry(void);
void g_hal_init(void);
FSP_FOOTER
#endif /* HAL_DATA_H_ */
