
/* File generated by gen_cfile.py. Should not be modified. */

#include "canobject.h"
#include "BaseDef.h"
#include "ecat_def.h"

#define TXPDO_TRIG 

/**************************************************************************/
/* Declaration of mapped variables                                        */
/**************************************************************************/

/**************************************************************************/
/* Declaration of value range types                                       */
/**************************************************************************/

#define valueRange_EMC 0x9F /* Type for index 0x1003 subindex 0x00 (only set of value 0 is possible) */
UNS32 ad_valueRangeTest (UNS8 typeValue, void * value)
{
  switch (typeValue) {
    case valueRange_EMC:
      if (*(UNS8*)value != (UNS8)0) return OD_VALUE_RANGE_EXCEEDED;
      break;
  }
  return 0;
}

/**************************************************************************/
/* The node id                                                            */
/**************************************************************************/
/* node_id default value.*/
UNS8 ad_bDeviceNodeId = 0x00;

/**************************************************************************/
/* Array of message processing information */

const UNS8 ad_iam_a_slave = 1;

TIMER_HANDLE ad_heartBeatTimers[5] = {TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE};

/*
$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$

                               OBJECT DICTIONARY

$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
*/

/* index 0x1000 :   Device Type. */
                    UNS32 ad_obj1000 = 0x00020192;	/* 0 */
                    subindex ad_Index1000[] = 
                     {
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1000 }
                     };

/* index 0x1001 :   Error Register. */
                    UNS8 ad_obj1001 = 0x0;	/* 0 */
                    subindex ad_Index1001[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_obj1001 }
                     };

/* index 0x1003 :   Pre-defined Error Field. */
                    UNS8 ad_highestSubIndex_obj1003 = 0; /* number of subindex - 1*/
                    UNS32 ad_obj1003[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    ODCallback_t ad_Index1003_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex ad_Index1003[] = 
                     {
                       { RW, valueRange_EMC, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1003 },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1003[0] },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1003[1] },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1003[2] },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1003[3] }
                     };

/* index 0x1005 :   SYNC COB ID. */
                    UNS32 ad_obj1005 = 0x80;	/* 0 */
                    ODCallback_t ad_Index1005_callbacks[] = 
                     {
                       NULL,
                     };
                    subindex ad_Index1005[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1005 }
                     };

/* index 0x1006 :   Communication / Cycle Period. */
                    UNS32 ad_obj1006 = 0x0;	/* 0 */
                    ODCallback_t ad_Index1006_callbacks[] = 
                     {
                       NULL,
                     };
                    subindex ad_Index1006[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1006 }
                     };

/* index 0x1008 :   Manufacturer Device Name. */
//                    UNS8 ad_obj1008[10] = (UNS8*)"";
                    UNS8 ad_obj1008[] =  DEVICE_NAME;
                    subindex ad_Index1008[] = 
                     {
                       { RO, visible_string, DEVICE_NAME_LEN, (void*)&ad_obj1008 }
                     };

/* index 0x1009 :   Manufacturer Hardware Version. */
//                    UNS8 ad_obj1009[10] = (UNS8*)"";
                    UNS8 ad_obj1009[] = DEVICE_HARDWARE_VERSION_CHAR;
                    subindex ad_Index1009[] = 
                     {
                       { RO, visible_string, DEVICE_SOFTWARE_VERSION_LEN , (void*)&ad_obj1009 }
                     };

/* index 0x100A :   Manufacturer Software Version. */
//                    UNS8 ad_obj100A[10] = (UNS8*)"";
                    UNS8 ad_obj100A[] = MCU_SOFTWARE_VERSION;
                    subindex ad_Index100A[] = 
                     {
                       { RO, visible_string, MCU_SOFTWARE_VERSION_LEN, (void*)&ad_obj100A }
                     };

/* index 0x100C :   Guard Time. */
                    UNS16 ad_obj100C = 0x1000;	/* 0 */
                    subindex ad_Index100C[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj100C }
                     };

/* index 0x100D :   Life Time Factor. */
                    UNS8 ad_obj100D = 0x3;	/* 0 */
                    subindex ad_Index100D[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj100D }
                     };

/* index 0x1010 :   Store parameters. */
                    UNS8 ad_highestSubIndex_obj1010 = 3; /* number of subindex - 1*/
                    UNS32 ad_obj1010_Save_All_Parameters = 0x1;	/* 0 */
                    UNS32 ad_obj1010_Save_Communication_Parameters = 0x1;	/* 0 */
                    UNS32 ad_obj1010_Save_Application_Parameters = 0x1;	/* 0 */
                    subindex ad_Index1010[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1010 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1010_Save_All_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1010_Save_Communication_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1010_Save_Application_Parameters }
                     };

/* index 0x1011 :   Restore Default Parameters. */
                    UNS8 ad_highestSubIndex_obj1011 = 3; /* number of subindex - 1*/
                    UNS32 ad_obj1011_Restore_All_Default_Parameters = 0x1;	/* 0 */
                    UNS32 ad_obj1011_Restore_Communication_Default_Parameters = 0x1;	/* 0 */
                    UNS32 ad_obj1011_Restore_Application_Default_Parameters = 0x1;	/* 0 */
                    subindex ad_Index1011[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1011 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1011_Restore_All_Default_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1011_Restore_Communication_Default_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1011_Restore_Application_Default_Parameters }
                     };          
                    
/* index 0x1014 :   Emergency COB ID. */
                    UNS32 ad_obj1014 = 0x81;	/* 128 */
                    subindex ad_Index1014[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1014 }
                     };

/* index 0x1016 :   Consumer Heartbeat Time. */
                    UNS8 ad_highestSubIndex_obj1016 = 5; /* number of subindex - 1*/
                    UNS32 ad_obj1016[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                    };
                    subindex ad_Index1016[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1016 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1016[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1016[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1016[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1016[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1016[4] },
                     };

/* index 0x1017 :   Producer Heartbeat Time. */
                    UNS16 ad_obj1017 = 0x0;	/* 0 */
                    ODCallback_t ad_Index1017_callbacks[] = 
                     {
                       NULL,
                     };
                    subindex ad_Index1017[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1017 }
                     };

/* index 0x1018 :   Identity. */
                    UNS8 ad_highestSubIndex_obj1018 = 3; /* number of subindex - 1*/
                    UNS32 ad_obj1018_Vendor_ID = 0x00000A88;	/* 0 */
                    UNS32 ad_obj1018_Product_Code = 0x00000080;	/* 0 */
                    UNS32 ad_obj1018_Revision_Number = 0x00000300;	/* 0 */
                    subindex ad_Index1018[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1018 },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1018_Vendor_ID },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1018_Product_Code },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1018_Revision_Number }, 
                     };
                                      
/* index 0x1200 :   Server SDO Parameter. */
                    UNS8 ad_highestSubIndex_obj1200 = 2; /* number of subindex - 1*/
                    UNS32 ad_obj1200_COB_ID_Client_to_Server_Receive_SDO = 0x600;	/* 1536 */
                    UNS32 ad_obj1200_COB_ID_Server_to_Client_Transmit_SDO = 0x580;	/* 1408 */
                    subindex ad_Index1200[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1200 },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1200_COB_ID_Client_to_Server_Receive_SDO },
                       { RO, uint32, sizeof (UNS32), (void*)&ad_obj1200_COB_ID_Server_to_Client_Transmit_SDO }
                     };

/* index 0x1400 :   Receive PDO 1 Parameter. */
                    UNS8 ad_highestSubIndex_obj1400 = 2; /* number of subindex - 1*/
                    UNS32 ad_obj1400_COB_ID_used_by_PDO = 0x200;	/* 512 */
                    UNS8 ad_obj1400_Transmission_Type = 0xFF;	/* 0 */
                    UNS16 ad_obj1400_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 ad_obj1400_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 ad_obj1400_Event_Timer = 0x0;	/* 0 */
                    subindex ad_Index1400[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1400 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1400_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1400_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1400_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1400_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1400_Event_Timer }
                     };

/* index 0x1401 :   Receive PDO 2 Parameter. */
                    UNS8 ad_highestSubIndex_obj1401 = 2; /* number of subindex - 1*/
                    UNS32 ad_obj1401_COB_ID_used_by_PDO = 0x80000300;	/* 768 */
                    UNS8 ad_obj1401_Transmission_Type = 0xFF;	/* 0 */
                    UNS16 ad_obj1401_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 ad_obj1401_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 ad_obj1401_Event_Timer = 0x0;	/* 0 */
                    subindex ad_Index1401[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1401 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1401_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1401_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1401_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1401_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1401_Event_Timer }
                     };

/* index 0x1402 :   Receive PDO 3 Parameter. */
                    UNS8 ad_highestSubIndex_obj1402 = 2; /* number of subindex - 1*/
                    UNS32 ad_obj1402_COB_ID_used_by_PDO = 0x80000400;	/* 1024 */
                    UNS8 ad_obj1402_Transmission_Type = 0xFF;	/* 0 */
                    UNS16 ad_obj1402_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 ad_obj1402_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 ad_obj1402_Event_Timer = 0x0;	/* 0 */
                    subindex ad_Index1402[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1402 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1402_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1402_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1402_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1402_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1402_Event_Timer }
                     };

/* index 0x1403 :   Receive PDO 4 Parameter. */
                    UNS8 ad_highestSubIndex_obj1403 = 2; /* number of subindex - 1*/
                    UNS32 ad_obj1403_COB_ID_used_by_PDO = 0x80000500;	/* 1280 */
                    UNS8 ad_obj1403_Transmission_Type = 0xFF;	/* 0 */
                    UNS16 ad_obj1403_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 ad_obj1403_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 ad_obj1403_Event_Timer = 0x0;	/* 0 */
                    subindex ad_Index1403[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1403 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1403_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1403_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1403_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1403_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1403_Event_Timer }
                     };

/* index 0x1600 :   Receive PDO 1 Mapping. */
                    UNS8 ad_highestSubIndex_obj1600 = 3; /* number of subindex - 1*/
                    UNS32 ad_obj1600[] = 
                    {
                      0x60400010,	/* 1614807056 */
                      0x60600008,	/* 1616969736 */
                      0x607A0020,	/* 1618608160 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex ad_Index1600[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1600 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1600[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1600[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1600[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1600[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1600[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1600[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1600[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1600[7] }
                     };

/* index 0x1601 :   Receive PDO 2 Mapping. */
                    UNS8 ad_highestSubIndex_obj1601 = 2; /* number of subindex - 1*/
                    UNS32 ad_obj1601[] = 
                    {
                      0x60810020,	/* 1614807056 */
                      0x60830020,	/* 1616904200 */
                      0x0,	/* 1627324448 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex ad_Index1601[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1601 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1601[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1601[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1601[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1601[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1601[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1601[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1601[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1601[7] }
                     };

/* index 0x1602 :   Receive PDO 3 Mapping. */
                    UNS8 ad_highestSubIndex_obj1602 = 2; /* number of subindex - 1*/
                    UNS32 ad_obj1602[] = 
                    {
                      0x60840020,	/* 1614807056 */
                      0x60FF0020,	/* 1616904200 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex ad_Index1602[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1602 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1602[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1602[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1602[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1602[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1602[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1602[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1602[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1602[7] }
                     };

/* index 0x1603 :   Receive PDO 4 Mapping. */
                    UNS8 ad_highestSubIndex_obj1603 = 1; /* number of subindex - 1*/
                    UNS32 ad_obj1603[] = 
                    {
                      0x607C0020,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex ad_Index1603[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1603 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1603[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1603[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1603[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1603[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1603[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1603[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1603[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1603[7] }
                     };

/* index 0x1800 :   Transmit PDO 1 Parameter. */
                    UNS8 ad_highestSubIndex_obj1800 = 5; /* number of subindex - 1*/
                    UNS32 ad_obj1800_COB_ID_used_by_PDO = 0x80000180;	/* 384 */								 
                    UNS8 ad_obj1800_Transmission_Type = 0xFF;	/* 0 */
                    UNS16 ad_obj1800_Inhibit_Time = 0x50;	/* 0 */
                    UNS8 ad_obj1800_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 ad_obj1800_Event_Timer = 0x0;	/* 0 */									 
                    ODCallback_t ad_Index1800_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex ad_Index1800[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1800 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1800_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1800_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1800_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1800_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1800_Event_Timer }
                     };

/* index 0x1801 :   Transmit PDO 2 Parameter. */
                    UNS8 ad_highestSubIndex_obj1801 = 5; /* number of subindex - 1*/
                    UNS32 ad_obj1801_COB_ID_used_by_PDO = 0x80000280;	/* 640 */								 
                    UNS8 ad_obj1801_Transmission_Type = 0xFF;	/* 0 */
                    UNS16 ad_obj1801_Inhibit_Time = 0x50;	/* 0 */
                    UNS8 ad_obj1801_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 ad_obj1801_Event_Timer = 0x00;	/* 0 */							 
                    ODCallback_t ad_Index1801_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex ad_Index1801[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1801 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1801_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1801_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1801_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1801_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1801_Event_Timer }
                     };

/* index 0x1802 :   Transmit PDO 3 Parameter. */
                    UNS8 ad_highestSubIndex_obj1802 = 5; /* number of subindex - 1*/
                    UNS32 ad_obj1802_COB_ID_used_by_PDO = 0x80000380;	/* 896 */
                    UNS8 ad_obj1802_Transmission_Type = 0xFF;	/* 0 */
                    UNS16 ad_obj1802_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 ad_obj1802_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 ad_obj1802_Event_Timer = 0x0;	/* 0 */		                    									 
                    ODCallback_t ad_Index1802_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex ad_Index1802[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1802 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1802_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1802_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1802_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1802_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1802_Event_Timer }
                     };

/* index 0x1803 :   Transmit PDO 4 Parameter. */
                    UNS8 ad_highestSubIndex_obj1803 = 5; /* number of subindex - 1*/
                    UNS32 ad_obj1803_COB_ID_used_by_PDO = 0x80000480;	/* 1152 */
                    UNS8 ad_obj1803_Transmission_Type = 0xFF;	/* 0 */
                    UNS16 ad_obj1803_Inhibit_Time = 0;	/* 0 */
                    UNS8 ad_obj1803_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 ad_obj1803_Event_Timer = 0x0;	/* 0 */
                    ODCallback_t ad_Index1803_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex ad_Index1803[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1803 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1803_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1803_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1803_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&ad_obj1803_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&ad_obj1803_Event_Timer }
                     };

/* index 0x1A00 :   Transmit PDO 1 Mapping. */
                    UNS8 ad_highestSubIndex_obj1A00 = 3; /* number of subindex - 1*/
                    UNS32 ad_obj1A00[] = 
                    {
                      0x60410010,	/* 1614872592 */
                      0x603F0010,	/* 1616969736 */
                      0x60610008,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex ad_Index1A00[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1A00 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A00[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A00[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A00[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A00[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A00[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A00[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A00[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A00[7] }
                     };

/* index 0x1A01 :   Transmit PDO 2 Mapping. */
                    UNS8 ad_highestSubIndex_obj1A01 = 2; /* number of subindex - 1*/
                    UNS32 ad_obj1A01[] = 
                    {
                      0x60640020,	/* 1614872592 */
                      0x606C0020,	/* 1616969736 */
		              0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex ad_Index1A01[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1A01 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A01[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A01[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A01[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A01[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A01[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A01[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A01[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A01[7] }
                     };

/* index 0x1A02 :   Transmit PDO 3 Mapping. */
                    UNS8 ad_highestSubIndex_obj1A02 = 0; /* number of subindex - 1*/
                    UNS32 ad_obj1A02[] = 
                    {
                      0x0,	/* 1614872592 */
                      0x0,	/* 1616969736 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex ad_Index1A02[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1A02 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A02[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A02[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A02[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A02[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A02[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A02[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A02[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A02[7] }
                     };

/* index 0x1A03 :   Transmit PDO 4 Mapping. */
                    UNS8 ad_highestSubIndex_obj1A03 = 0; /* number of subindex - 1*/
                    UNS32 ad_obj1A03[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex ad_Index1A03[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&ad_highestSubIndex_obj1A03 },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A03[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A03[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A03[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A03[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A03[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A03[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A03[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&ad_obj1A03[7] }
                     };							 
/**************************************************************************/
/* Declaration of pointed variables                                       */
/**************************************************************************/

const indextable ad_objdict[] = 
{
  { (subindex*)ad_Index1000,sizeof(ad_Index1000)/sizeof(ad_Index1000[0]), 0x1000},
  { (subindex*)ad_Index1001,sizeof(ad_Index1001)/sizeof(ad_Index1001[0]), 0x1001},
  { (subindex*)ad_Index1003,sizeof(ad_Index1003)/sizeof(ad_Index1003[0]), 0x1003},
  { (subindex*)ad_Index1005,sizeof(ad_Index1005)/sizeof(ad_Index1005[0]), 0x1005},
  { (subindex*)ad_Index1006,sizeof(ad_Index1006)/sizeof(ad_Index1006[0]), 0x1006},
  { (subindex*)ad_Index1008,sizeof(ad_Index1008)/sizeof(ad_Index1008[0]), 0x1008},
  { (subindex*)ad_Index1009,sizeof(ad_Index1009)/sizeof(ad_Index1009[0]), 0x1009},
  { (subindex*)ad_Index100A,sizeof(ad_Index100A)/sizeof(ad_Index100A[0]), 0x100A},
  { (subindex*)ad_Index100C,sizeof(ad_Index100C)/sizeof(ad_Index100C[0]), 0x100C},
  { (subindex*)ad_Index100D,sizeof(ad_Index100D)/sizeof(ad_Index100D[0]), 0x100D},
  { (subindex*)ad_Index1010,sizeof(ad_Index1010)/sizeof(ad_Index1010[0]), 0x1010},
  { (subindex*)ad_Index1011,sizeof(ad_Index1011)/sizeof(ad_Index1011[0]), 0x1011},
  { (subindex*)ad_Index1014,sizeof(ad_Index1014)/sizeof(ad_Index1014[0]), 0x1014},
  { (subindex*)ad_Index1016,sizeof(ad_Index1016)/sizeof(ad_Index1016[0]), 0x1016},
  { (subindex*)ad_Index1017,sizeof(ad_Index1017)/sizeof(ad_Index1017[0]), 0x1017},
  { (subindex*)ad_Index1018,sizeof(ad_Index1018)/sizeof(ad_Index1018[0]), 0x1018},
  { (subindex*)ad_Index1200,sizeof(ad_Index1200)/sizeof(ad_Index1200[0]), 0x1200},
  { (subindex*)ad_Index1400,sizeof(ad_Index1400)/sizeof(ad_Index1400[0]), 0x1400},
  { (subindex*)ad_Index1401,sizeof(ad_Index1401)/sizeof(ad_Index1401[0]), 0x1401},
  { (subindex*)ad_Index1402,sizeof(ad_Index1402)/sizeof(ad_Index1402[0]), 0x1402},
  { (subindex*)ad_Index1403,sizeof(ad_Index1403)/sizeof(ad_Index1403[0]), 0x1403},
  { (subindex*)ad_Index1600,sizeof(ad_Index1600)/sizeof(ad_Index1600[0]), 0x1600},
  { (subindex*)ad_Index1601,sizeof(ad_Index1601)/sizeof(ad_Index1601[0]), 0x1601},
  { (subindex*)ad_Index1602,sizeof(ad_Index1602)/sizeof(ad_Index1602[0]), 0x1602},
  { (subindex*)ad_Index1603,sizeof(ad_Index1603)/sizeof(ad_Index1603[0]), 0x1603},
  { (subindex*)ad_Index1800,sizeof(ad_Index1800)/sizeof(ad_Index1800[0]), 0x1800},
  { (subindex*)ad_Index1801,sizeof(ad_Index1801)/sizeof(ad_Index1801[0]), 0x1801},
  { (subindex*)ad_Index1802,sizeof(ad_Index1802)/sizeof(ad_Index1802[0]), 0x1802},
  { (subindex*)ad_Index1803,sizeof(ad_Index1803)/sizeof(ad_Index1803[0]), 0x1803},
  { (subindex*)ad_Index1A00,sizeof(ad_Index1A00)/sizeof(ad_Index1A00[0]), 0x1A00},
  { (subindex*)ad_Index1A01,sizeof(ad_Index1A01)/sizeof(ad_Index1A01[0]), 0x1A01},
  { (subindex*)ad_Index1A02,sizeof(ad_Index1A02)/sizeof(ad_Index1A02[0]), 0x1A02},
  { (subindex*)ad_Index1A03,sizeof(ad_Index1A03)/sizeof(ad_Index1A03[0]), 0x1A03},
};

const indextable * ad_scanIndexOD (UNS16 wIndex, UNS32 * errorCode, ODCallback_t **callbacks)
{
	int i;
	*callbacks = NULL;
	switch(wIndex){
		case 0x1000: i = 0;break;
		case 0x1001: i = 1;break;
		case 0x1003: i = 2;*callbacks = ad_Index1003_callbacks; break;
		case 0x1005: i = 3;*callbacks = ad_Index1005_callbacks; break;
		case 0x1006: i = 4;*callbacks = ad_Index1006_callbacks; break;
		case 0x1008: i = 5;break;
		case 0x1009: i = 6;break;
		case 0x100A: i = 7;break;
		case 0x100C: i = 8;break;
		case 0x100D: i = 9;break;
		case 0x1010: i = 10;break;
		case 0x1011: i = 11;break;
		case 0x1014: i = 12;break;
		case 0x1016: i = 13;break;
		case 0x1017: i = 14;*callbacks = ad_Index1017_callbacks; break;
		case 0x1018: i = 15;break;
		case 0x1200: i = 16;break;
		case 0x1400: i = 17;break;
		case 0x1401: i = 18;break;
		case 0x1402: i = 19;break;
		case 0x1403: i = 20;break;
		case 0x1600: i = 21;break;
		case 0x1601: i = 22;break;
		case 0x1602: i = 23;break;
		case 0x1603: i = 24;break;
		case 0x1800: i = 25;*callbacks = ad_Index1800_callbacks; break;
		case 0x1801: i = 26;*callbacks = ad_Index1801_callbacks; break;
		case 0x1802: i = 27;*callbacks = ad_Index1802_callbacks; break;
		case 0x1803: i = 28;*callbacks = ad_Index1803_callbacks; break;
		case 0x1A00: i = 29;break;
		case 0x1A01: i = 30;break;
		case 0x1A02: i = 31;break;
		case 0x1A03: i = 32;break;
		default:
			*errorCode = OD_NO_SUCH_OBJECT;
			return NULL;
	}
	*errorCode = OD_SUCCESSFUL;
	return &ad_objdict[i];
}

/* 
 * To count at which received SYNC a PDO must be sent.
 * Even if no pdoTransmit are defined, at least one entry is computed
 * for compilations issues.
 */
s_PDO_status ad_PDO_status[4] = {s_PDO_status_Initializer,s_PDO_status_Initializer,s_PDO_status_Initializer,s_PDO_status_Initializer};

const quick_index ad_firstIndex = {
  16, /* SDO_SVR */
  0, /* SDO_CLT */
  17, /* PDO_RCV */
  21, /* PDO_RCV_MAP */
  25, /* PDO_TRS */
  29 /* PDO_TRS_MAP */
};

const quick_index ad_lastIndex = {
  16, /* SDO_SVR */
  0, /* SDO_CLT */
  20, /* PDO_RCV */
  24, /* PDO_RCV_MAP */
  28, /* PDO_TRS */
  32 /* PDO_TRS_MAP */
};

const UNS16 ad_ObjdictSize = sizeof(ad_objdict)/sizeof(ad_objdict[0]); 

CO_Data Object_Data = CANOPEN_NODE_DATA_INITIALIZER(ad);