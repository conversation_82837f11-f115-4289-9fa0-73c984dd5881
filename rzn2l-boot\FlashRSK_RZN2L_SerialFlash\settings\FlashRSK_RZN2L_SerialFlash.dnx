<?xml version="1.0"?>
<settings>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <DebugChecksum>
        <Checksum>3723887126</Checksum>
    </DebugChecksum>
    <Exceptions>
        <StopOnUncaught>_ 0</StopOnUncaught>
        <StopOnThrow>_ 0</StopOnThrow>
    </Exceptions>
    <Disassembly>
        <MixedMode>1</MixedMode>
        <InstrCount>0</InstrCount>
    </Disassembly>
    <DriverProfiling>
        <Enabled>0</Enabled>
        <Mode>1</Mode>
        <Graph>0</Graph>
        <Symbiont>0</Symbiont>
    </DriverProfiling>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <InterruptLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
        <SumSortOrder>0</SumSortOrder>
    </InterruptLog>
    <DataLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <SumEnabled>0</SumEnabled>
        <ShowTimeSum>1</ShowTimeSum>
    </DataLog>
    <DisassembleMode>
        <mode>0</mode>
    </DisassembleMode>
    <Breakpoints2>
        <Count>0</Count>
    </Breakpoints2>
    <Interrupts>
        <Enabled>1</Enabled>
    </Interrupts>
    <MemConfig>
        <Base>1</Base>
        <Manual>0</Manual>
        <Ddf>1</Ddf>
        <TypeViol>0</TypeViol>
        <Stop>1</Stop>
    </MemConfig>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
    <Simulator>
        <Freq>10000000</Freq>
        <FreqHi>0</FreqHi>
        <MultiCoreRunAll>1</MultiCoreRunAll>
    </Simulator>
</settings>
