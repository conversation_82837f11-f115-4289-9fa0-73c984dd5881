include "memory_regions.icf";

/* The memory information for each device is done in memory regions file.
 * The starting address and length of memory not defined in memory regions file are defined as 0. */

if (isdefinedsymbol(ATCM_START))
{
    define symbol ATCM_PRV_START = ATCM_START;
}
else
{
    define symbol ATCM_PRV_START = 0;
}

if (isdefinedsymbol(ATCM_LENGTH))
{
    define symbol ATCM_PRV_LENGTH = ATCM_LENGTH;
}
else
{
    define symbol ATCM_PRV_LENGTH = 0;
}

if (isdefinedsymbol(BTCM_START))
{
    define symbol BTCM_PRV_START = BTCM_START;
}
else
{
    define symbol BTCM_PRV_START = 0;
}

if (isdefinedsymbol(BTCM_LENGTH))
{
    define symbol BTCM_PRV_LENGTH = BTCM_LENGTH;
}
else
{
    define symbol BTCM_PRV_LENGTH = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_START))
{
    define symbol SYSTEM_RAM_PRV_START = SYSTEM_RAM_START;
}
else
{
    define symbol SYSTEM_RAM_PRV_START = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_LENGTH))
{
    define symbol SYSTEM_RAM_PRV_LENGTH = SYSTEM_RAM_LENGTH;
}
else
{
    define symbol SYSTEM_RAM_PRV_LENGTH = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_MIRROR_START))
{
    define symbol SYSTEM_RAM_MIRROR_PRV_START = SYSTEM_RAM_MIRROR_START;
}
else
{
    define symbol SYSTEM_RAM_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_MIRROR_LENGTH))
{
    define symbol SYSTEM_RAM_MIRROR_PRV_LENGTH = SYSTEM_RAM_MIRROR_LENGTH;
}
else
{
    define symbol SYSTEM_RAM_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_MIRROR_START))
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_START = xSPI0_CS0_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH = xSPI0_CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_MIRROR_START))
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_START = xSPI0_CS1_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH = xSPI0_CS1_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_MIRROR_START))
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_START = xSPI1_CS0_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH = xSPI1_CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_MIRROR_START))
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_START = xSPI1_CS1_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH = xSPI1_CS1_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS0_SPACE_MIRROR_START))
{
    define symbol CS0_SPACE_MIRROR_PRV_START = CS0_SPACE_MIRROR_START;
}
else
{
    define symbol CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS0_SPACE_MIRROR_LENGTH))
{
    define symbol CS0_SPACE_MIRROR_PRV_LENGTH = CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS2_SPACE_MIRROR_START))
{
    define symbol CS2_SPACE_MIRROR_PRV_START = CS2_SPACE_MIRROR_START;
}
else
{
    define symbol CS2_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS2_SPACE_MIRROR_LENGTH))
{
    define symbol CS2_SPACE_MIRROR_PRV_LENGTH = CS2_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS2_SPACE_MIRROR_PRV_LENGTH = 0;
}


if (isdefinedsymbol(CS3_SPACE_MIRROR_START))
{
    define symbol CS3_SPACE_MIRROR_PRV_START = CS3_SPACE_MIRROR_START;
}
else
{
    define symbol CS3_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS3_SPACE_MIRROR_LENGTH))
{
    define symbol CS3_SPACE_MIRROR_PRV_LENGTH = CS3_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS3_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS5_SPACE_MIRROR_START))
{
    define symbol CS5_SPACE_MIRROR_PRV_START = CS5_SPACE_MIRROR_START;
}
else
{
    define symbol CS5_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS5_SPACE_MIRROR_LENGTH))
{
    define symbol CS5_SPACE_MIRROR_PRV_LENGTH = CS5_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS5_SPACE_MIRROR_PRV_LENGTH = 0;
}


if (isdefinedsymbol(xSPI0_CS0_SPACE_START))
{
    define symbol xSPI0_CS0_SPACE_PRV_START = xSPI0_CS0_SPACE_START;
}
else
{
    define symbol xSPI0_CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_LENGTH))
{
    define symbol xSPI0_CS0_SPACE_PRV_LENGTH = xSPI0_CS0_SPACE_LENGTH;
}
else
{
    define symbol xSPI0_CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_START))
{
    define symbol xSPI0_CS1_SPACE_PRV_START = xSPI0_CS1_SPACE_START;
}
else
{
    define symbol xSPI0_CS1_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_LENGTH))
{
    define symbol xSPI0_CS1_SPACE_PRV_LENGTH = xSPI0_CS1_SPACE_LENGTH;
}
else
{
    define symbol xSPI0_CS1_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_START))
{
    define symbol xSPI1_CS0_SPACE_PRV_START = xSPI1_CS0_SPACE_START;
}
else
{
    define symbol xSPI1_CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_LENGTH))
{
    define symbol xSPI1_CS0_SPACE_PRV_LENGTH = xSPI1_CS0_SPACE_LENGTH;
}
else
{
    define symbol xSPI1_CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_START))
{
    define symbol xSPI1_CS1_SPACE_PRV_START = xSPI1_CS1_SPACE_START;
}
else
{
    define symbol xSPI1_CS1_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_LENGTH))
{
    define symbol xSPI1_CS1_SPACE_PRV_LENGTH = xSPI1_CS1_SPACE_LENGTH;
}
else
{
    define symbol xSPI1_CS1_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS0_SPACE_START))
{
    define symbol CS0_SPACE_PRV_START = CS0_SPACE_START;
}
else
{
    define symbol CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS0_SPACE_LENGTH))
{
    define symbol CS0_SPACE_PRV_LENGTH = CS0_SPACE_LENGTH;
}
else
{
    define symbol CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS2_SPACE_START))
{
    define symbol CS2_SPACE_PRV_START = CS2_SPACE_START;
}
else
{
    define symbol CS2_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS2_SPACE_LENGTH))
{
    define symbol CS2_SPACE_PRV_LENGTH = CS2_SPACE_LENGTH;
}
else
{
    define symbol CS2_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS3_SPACE_START))
{
    define symbol CS3_SPACE_PRV_START = CS3_SPACE_START;
}
else
{
    define symbol CS3_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS3_SPACE_LENGTH))
{
    define symbol CS3_SPACE_PRV_LENGTH = CS3_SPACE_LENGTH;
}
else
{
    define symbol CS3_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS5_SPACE_START))
{
    define symbol CS5_SPACE_PRV_START = CS5_SPACE_START;
}
else
{
    define symbol CS5_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS5_SPACE_LENGTH))
{
    define symbol CS5_SPACE_PRV_LENGTH = CS5_SPACE_LENGTH;
}
else
{
    define symbol CS5_SPACE_PRV_LENGTH = 0;
}

/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\a_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = ATCM_PRV_START;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_ROM_start__ = xSPI1_CS0_SPACE_PRV_START + 0x00020100;
define symbol __ICFEDIT_region_ROM_end__   = xSPI1_CS0_SPACE_PRV_START + 0x0006FFFF;
define symbol __ICFEDIT_region_RAM_start__ = ATCM_PRV_START + 0x00000100;
define symbol __ICFEDIT_region_RAM_end__   = ATCM_PRV_START + 0x0001FFFF;
/**** End of ICF editor section. ###ICF###*/

define memory mem with size = 4G;
define region ROM_region   = mem:[from __ICFEDIT_region_ROM_start__   to __ICFEDIT_region_ROM_end__];
define region RAM_region   = mem:[from __ICFEDIT_region_RAM_start__   to __ICFEDIT_region_RAM_end__];

define symbol __region_D_LDR_DATA_start__    = BTCM_PRV_START;
define symbol __region_D_LDR_DATA_end__      = BTCM_PRV_START + 0x00001FFF;
define symbol __region_D_LDR_PRG_start__     = BTCM_PRV_START + 0x00002000;
define symbol __region_D_LDR_PRG_end__       = BTCM_PRV_START + 0x0001FFFF;

define symbol __region_DMAC_LINK_MODE_start__         = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00044000;
define symbol __region_DMAC_LINK_MODE_end__           = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00040000 - 1;
define symbol __region_SHARED_NONCACHE_BUFFER_start__ = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00040000;
define symbol __region_SHARED_NONCACHE_BUFFER_end__   = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00020000 - 1;
define symbol __region_NONCACHE_BUFFER_start__        = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00020000;
define symbol __region_NONCACHE_BUFFER_end__          = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 1;

define symbol __region_ATCM_start__              = ATCM_PRV_START;
define symbol __region_ATCM_end__                = ATCM_PRV_START + ATCM_PRV_LENGTH - 1;
define symbol __region_BTCM_start__              = BTCM_PRV_START;
define symbol __region_BTCM_end__                = BTCM_PRV_START + BTCM_PRV_LENGTH - 1;
define symbol __region_SYSTEM_RAM_start__        = SYSTEM_RAM_PRV_START;
define symbol __region_SYSTEM_RAM_end__          = SYSTEM_RAM_PRV_START + SYSTEM_RAM_PRV_LENGTH - 1;
define symbol __region_SYSTEM_RAM_MIRROR_start__ = SYSTEM_RAM_MIRROR_PRV_START;
define symbol __region_SYSTEM_RAM_MIRROR_end__   = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 1;

define symbol __region_XSPI0_CS0_MIRROR_start__ = xSPI0_CS0_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI0_CS0_MIRROR_end__   = xSPI0_CS0_SPACE_MIRROR_PRV_START + xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS1_MIRROR_start__ = xSPI0_CS1_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI0_CS1_MIRROR_end__   = xSPI0_CS1_SPACE_MIRROR_PRV_START + xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS0_MIRROR_start__ = xSPI1_CS0_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI1_CS0_MIRROR_end__   = xSPI1_CS0_SPACE_MIRROR_PRV_START + xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS1_MIRROR_start__ = xSPI1_CS1_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI1_CS1_MIRROR_end__   = xSPI1_CS1_SPACE_MIRROR_PRV_START + xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS0_MIRROR_start__       = CS0_SPACE_MIRROR_PRV_START;
define symbol __region_CS0_MIRROR_end__         = CS0_SPACE_MIRROR_PRV_START + CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS2_MIRROR_start__       = CS2_SPACE_MIRROR_PRV_START;
define symbol __region_CS2_MIRROR_end__         = CS2_SPACE_MIRROR_PRV_START + CS2_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS3_MIRROR_start__       = CS3_SPACE_MIRROR_PRV_START;
define symbol __region_CS3_MIRROR_end__         = CS3_SPACE_MIRROR_PRV_START + CS3_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS5_MIRROR_start__       = CS5_SPACE_MIRROR_PRV_START;
define symbol __region_CS5_MIRROR_end__         = CS5_SPACE_MIRROR_PRV_START + CS5_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS0_start__        = xSPI0_CS0_SPACE_PRV_START;
define symbol __region_XSPI0_CS0_end__          = xSPI0_CS0_SPACE_PRV_START + xSPI0_CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS1_start__        = xSPI0_CS1_SPACE_PRV_START;
define symbol __region_XSPI0_CS1_end__          = xSPI0_CS1_SPACE_PRV_START + xSPI0_CS1_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS0_start__        = xSPI1_CS0_SPACE_PRV_START;
define symbol __region_XSPI1_CS0_end__          = xSPI1_CS0_SPACE_PRV_START + xSPI1_CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS1_start__        = xSPI1_CS1_SPACE_PRV_START;
define symbol __region_XSPI1_CS1_end__          = xSPI1_CS1_SPACE_PRV_START + xSPI1_CS1_SPACE_PRV_LENGTH - 1;
define symbol __region_CS0_start__              = CS0_SPACE_PRV_START;
define symbol __region_CS0_end__                = CS0_SPACE_PRV_START + CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_CS2_start__              = CS2_SPACE_PRV_START;
define symbol __region_CS2_end__                = CS2_SPACE_PRV_START + CS2_SPACE_PRV_LENGTH - 1;
define symbol __region_CS3_start__              = CS3_SPACE_PRV_START;
define symbol __region_CS3_end__                = CS3_SPACE_PRV_START + CS3_SPACE_PRV_LENGTH - 1;
define symbol __region_CS5_start__              = CS5_SPACE_PRV_START;
define symbol __region_CS5_end__                = CS5_SPACE_PRV_START + CS5_SPACE_PRV_LENGTH - 1;

/************** SPI boot mode setting **************/
define symbol __region_LDR_PARAM_start__     = xSPI1_CS0_SPACE_PRV_START;
define symbol __region_LDR_PARAM_end__       = xSPI1_CS0_SPACE_PRV_START + 0x0000004B;
define symbol __region_S_LDR_PRG_start__     = xSPI1_CS0_SPACE_PRV_START + 0x0000004C;
define symbol __region_S_LDR_PRG_end__       = xSPI1_CS0_SPACE_PRV_START + 0x0000604B;
define symbol __region_S_LDR_DATA_start__    = xSPI1_CS0_SPACE_PRV_START + 0x0000604C;
define symbol __region_S_LDR_DATA_end__      = xSPI1_CS0_SPACE_PRV_START + 0x0000804B;

define symbol __region_S_intvec_start__      = xSPI1_CS0_SPACE_PRV_START + 0x00020000;
define symbol __region_S_intvec_end__        = xSPI1_CS0_SPACE_PRV_START + 0x000200FF;
define symbol __region_S_RAM_start__         = xSPI1_CS0_SPACE_PRV_START + 0x00070000;
define symbol __region_S_RAM_end__           = xSPI1_CS0_SPACE_PRV_START + 0x0009FFFF;
/****************************************************/

define region D_LDR_DATA_region = mem:[from __region_D_LDR_DATA_start__   to __region_D_LDR_DATA_end__];
define region D_LDR_PRG_region  = mem:[from __region_D_LDR_PRG_start__   to __region_D_LDR_PRG_end__];

define region LDR_PARAM_region  = mem:[from __region_LDR_PARAM_start__   to __region_LDR_PARAM_end__];
define region S_LDR_PRG_region  = mem:[from __region_S_LDR_PRG_start__   to __region_S_LDR_PRG_end__];
define region S_LDR_DATA_region = mem:[from __region_S_LDR_DATA_start__   to __region_S_LDR_DATA_end__];

define region S_intvec_region = mem:[from __region_S_intvec_start__  to __region_S_intvec_end__];
define region S_RAM_region    = mem:[from __region_S_RAM_start__  to __region_S_RAM_end__];

define region DMAC_LINK_MODE_region          = mem:[from __region_DMAC_LINK_MODE_start__   to __region_DMAC_LINK_MODE_end__];
define region SHARED_NONCACHE_BUFFER_region  = mem:[from __region_SHARED_NONCACHE_BUFFER_start__   to __region_SHARED_NONCACHE_BUFFER_end__];
define region NONCACHE_BUFFER_region         = mem:[from __region_NONCACHE_BUFFER_start__   to __region_NONCACHE_BUFFER_end__];

define region ATCM_region              = mem:[from __region_ATCM_start__  to __region_ATCM_end__ ];
define region BTCM_region              = mem:[from __region_BTCM_start__  to __region_BTCM_end__ ];
define region SYSTEM_RAM_region        = mem:[from __region_SYSTEM_RAM_start__  to __region_SYSTEM_RAM_end__ ];
define region SYSTEM_RAM_MIRROR_region = mem:[from __region_SYSTEM_RAM_MIRROR_start__  to __region_SYSTEM_RAM_MIRROR_end__ ];
define region XSPI0_CS0_MIRROR_region  = mem:[from __region_XSPI0_CS0_MIRROR_start__  to __region_XSPI0_CS0_MIRROR_end__ ];
define region XSPI0_CS1_MIRROR_region  = mem:[from __region_XSPI0_CS1_MIRROR_start__  to __region_XSPI0_CS1_MIRROR_end__ ];
define region XSPI1_CS0_MIRROR_region  = mem:[from __region_XSPI1_CS0_MIRROR_start__  to __region_XSPI1_CS0_MIRROR_end__ ];
define region XSPI1_CS1_MIRROR_region  = mem:[from __region_XSPI1_CS1_MIRROR_start__  to __region_XSPI1_CS1_MIRROR_end__ ];
define region CS0_MIRROR_region        = mem:[from __region_CS0_MIRROR_start__  to __region_CS0_MIRROR_end__ ];
define region CS2_MIRROR_region        = mem:[from __region_CS2_MIRROR_start__  to __region_CS2_MIRROR_end__ ];
define region CS3_MIRROR_region        = mem:[from __region_CS3_MIRROR_start__  to __region_CS3_MIRROR_end__ ];
define region CS5_MIRROR_region        = mem:[from __region_CS5_MIRROR_start__  to __region_CS5_MIRROR_end__ ];
define region XSPI0_CS0_region         = mem:[from __region_XSPI0_CS0_start__  to __region_XSPI0_CS0_end__ ];
define region XSPI0_CS1_region         = mem:[from __region_XSPI0_CS1_start__  to __region_XSPI0_CS1_end__ ];
define region XSPI1_CS0_region         = mem:[from __region_XSPI1_CS0_start__  to __region_XSPI1_CS0_end__ ];
define region XSPI1_CS1_region         = mem:[from __region_XSPI1_CS1_start__  to __region_XSPI1_CS1_end__ ];
define region CS0_region               = mem:[from __region_CS0_start__  to __region_CS0_end__ ];
define region CS2_region               = mem:[from __region_CS2_start__  to __region_CS2_end__ ];
define region CS3_region               = mem:[from __region_CS3_start__  to __region_CS3_end__ ];
define region CS5_region               = mem:[from __region_CS5_start__  to __region_CS5_end__ ];

define block LDR_PRG_RBLOCK with fixed order, alignment = 4
                             { ro code section .loader_text_init object startup.o,
                               ro code object startup.o,
                               ro code object system.o,
                               ro code object bsp_clocks.o,
                               ro code object bsp_irq.o,
                               ro code object bsp_register_protection.o,
                               ro code object r_ioport.o,
                               ro code section .warm_start_init }
                               except { ro code section .intvec_init};
define block LDR_PRG_WBLOCK with fixed order, alignment = 4
                             { rw code section .loader_text object startup.o,
                               rw code object startup.o,
                               rw code object system.o,
                               rw code object bsp_clocks.o,
                               rw code object bsp_irq.o,
                               rw code object bsp_register_protection.o,
                               rw code object r_ioport.o,
                               rw code section .warm_start }
                               except { rw code section .intvec};
define block LDR_DATA_ZBLOCK with alignment = 4
                             { section .bss object startup.o,
                               section .bss object system.o,
                               section .bss object bsp_clocks.o,
                               section .bss object bsp_irq.o,
                               section .bss object bsp_register_protection.o,
                               section .bss object r_ioport.o,
                               section .bss object bsp_io.o };
define block LDR_DATA_RBLOCK with fixed order, alignment = 4
                             { section .data_init object startup.o,
                               section .data_init object system.o,
                               section .data_init object bsp_clocks.o,
                               section .data_init object bsp_irq.o,
                               section .data_init object bsp_register_protection.o,
                               section .data_init object r_ioport.o };
define block LDR_DATA_WBLOCK with fixed order, alignment = 4
                             { section .data object startup.o,
                               section .data object system.o,
                               section .data object bsp_clocks.o,
                               section .data object bsp_irq.o,
                               section .data object bsp_register_protection.o,
                               section .data object r_ioport.o };
                               
define block USER_CUSTOM_PRG_RBLOCK with fixed order, alignment = 4
                             { 
                               ro code object bsp_usb.o,
                               ro code object bsp_uart.o,
                               ro code object bsp_led.o,
                               ro code object bsp_eeprom.o,
                               ro code object hal_data.o,
                               ro code object pin_data.o,
                               ro code object cmd_flash.o,                              
                               ro code object flash.o,
                               ro code object common.o,
                               ro code object flash_if.o,
                               ro code object menu.o,
                               ro code object ymodem.o,
                               ro code object hal_entry.o,
                               ro code object Global.o,
                               ro code object ComUart.o,
                               ro code object Round.o,
                               ro code object SequenceIO.o, 
                               ro code object MotorIdentification.o,
                               ro code object PnPrmCal.o,
                               ro code object PrmTable.o,
                               ro code object Home.o,
                               ro code object AdvancedAutoTuning.o,
                               ro code object ExCtrlPrmCal.o,
                               
                               
                               ro code object ecatslv.o,
                               ro code object MLib.o,        
                               ro code object objdef.o,  
                               ro code object CheckAlarm.o,  
                               ro code object FnOnePrmTuning.o,  
                               ro code object FnPrgJog.o,
                               ro code object I2C_LED.o,
                               
                               
                               ro code object r_usb_basic.o, 
                               ro code object r_usb_cdataio.o,
                               ro code object r_usb_clibusbip.o,
                               ro code object r_usb_creg_abs.o,        
                               ro code object r_usb_creg_access.o,   
                               ro code object r_usb_mcu.o,        
                               ro code object r_usb_pcdc_driver.o,  
                               ro code object r_usb_pcontrolrw.o, 
                               ro code object r_usb_pdriver.o,
                               ro code object r_usb_pinthandler_usbip0.o,
                               ro code object r_usb_plibusbip.o,        
                               ro code object r_usb_preg_abs.o,   
                               ro code object r_usb_preg_access.o,        
                               ro code object r_usb_psignal.o,   
                               ro code object r_usb_pstdfunction.o, 
                               ro code object r_usb_pstdrequest.o,   
                               
                               ro code object modbus.o,   
                               
                               
                               }except { ro code section .warm_start_init};
define block USER_CUSTOM_PRG_WBLOCK with fixed order, alignment = 4
                             { 
                               rw code object bsp_usb.o,
                               rw code object bsp_uart.o,
                               rw code object bsp_led.o,
                               rw code object bsp_eeprom.o,
                               rw code object hal_data.o,
                               rw code object pin_data.o,
                               rw code object cmd_flash.o, 
                               rw code object flash.o,
                               rw code object common.o,
                               rw code object flash_if.o,
                               rw code object menu.o,
                               rw code object ymodem.o,
                               rw code object hal_entry.o,
                               rw code object Global.o,
                               rw code object ComUart.o,
                               rw code object Round.o,
                               rw code object SequenceIO.o, 
                               rw code object MotorIdentification.o,
                               rw code object PnPrmCal.o,
                               rw code object PrmTable.o,
                               rw code object Home.o,
                               rw code object AdvancedAutoTuning.o,
                               rw code object ExCtrlPrmCal.o,
                               
                               
                               rw code object ecatslv.o,                  
                               rw code object MLib.o,        
                               rw code object objdef.o,  
                               rw code object CheckAlarm.o,  
                               rw code object FnOnePrmTuning.o,                  
                               rw code object FnPrgJog.o,                       
                               rw code object I2C_LED.o, 

                               rw code object r_usb_basic.o, 
                               rw code object r_usb_cdataio.o,
                               rw code object r_usb_clibusbip.o,
                               rw code object r_usb_creg_abs.o,        
                               rw code object r_usb_creg_access.o,   
                               rw code object r_usb_mcu.o,        
                               rw code object r_usb_pcdc_driver.o,  
                               rw code object r_usb_pcontrolrw.o,                         
                               rw code object r_usb_pdriver.o,
                               rw code object r_usb_pinthandler_usbip0.o,
                               rw code object r_usb_plibusbip.o,        
                               rw code object r_usb_preg_abs.o,   
                               rw code object r_usb_preg_access.o,        
                               rw code object r_usb_psignal.o,   
                               rw code object r_usb_pstdfunction.o, 
                               rw code object r_usb_pstdrequest.o,          
                               rw code object modbus.o, 

                               }except { rw code section .warm_start,};  
                               
define block USER_CUSTOM_DATA_ZBLOCK with alignment = 4
                             { 
                               section .bss object bsp_usb.o,
                               section .bss object bsp_uart.o,
                               section .bss object bsp_led.o,
                               section .bss object bsp_eeprom.o,
                               section .bss object hal_data.o,
                               section .bss object pin_data.o,
                               section .bss object cmd_flash.o, 
                               section .bss object flash.o,
                               section .bss object common.o,
                               section .bss object flash_if.o,
                               section .bss object menu.o,
                               section .bss object ymodem.o,
                               section .bss object hal_entry.o,
                               section .bss object Global.o,
                               section .bss object ComUart.o,
                               section .bss object Round.o,
                               section .bss object SequenceIO.o,
                               section .bss object MotorIdentification.o,
                               section .bss object PnPrmCal.o,
                               section .bss object PrmTable.o,
                               section .bss object DataCache.o,
                               section .bss object Home.o,
                               section .bss object AdvancedAutoTuning.o,
                               section .bss object ExCtrlPrmCal.o,  

                               
                               section .bss object ecatslv.o,         
                               section .bss object MLib.o,        
                               section .bss object objdef.o,  
                               section .bss object CheckAlarm.o,  
                               section .bss object FnOnePrmTuning.o,  
                               section .bss object FnPrgJog.o,        
                               section .bss object I2C_LED.o, 

                               section .bss object r_usb_basic.o, 
                               section .bss object r_usb_cdataio.o,
                               section .bss object r_usb_clibusbip.o,
                               section .bss object r_usb_creg_abs.o,        
                               section .bss object r_usb_creg_access.o,   
                               section .bss object r_usb_mcu.o,        
                               section .bss object r_usb_pcdc_driver.o,  
                               section .bss object r_usb_pcontrolrw.o,        
                               section .bss object r_usb_pdriver.o,
                               section .bss object r_usb_pinthandler_usbip0.o,
                               section .bss object r_usb_plibusbip.o,        
                               section .bss object r_usb_preg_abs.o,   
                               section .bss object r_usb_preg_access.o,       
                               section .bss object r_usb_psignal.o,   
                               section .bss object r_usb_pstdfunction.o, 
                               section .bss object r_usb_pstdrequest.o,
                               
                               
                               section .bss object modbus.o,  
                               
                              
                               };
define block USER_CUSTOM_DATA_RBLOCK with fixed order, alignment = 4
                             { 
                               section .data_init object bsp_usb.o,
                               section .data_init object bsp_uart.o,
                               section .data_init object bsp_led.o,
                               section .data_init object bsp_eeprom.o,
                               section .data_init object hal_data.o,
                               section .data_init object pin_data.o,
                               section .data_init object cmd_flash.o, 
                               section .data_init object flash.o,
                               section .data_init object common.o,
                               section .data_init object flash_if.o,
                               section .data_init object menu.o,
                               section .data_init object ymodem.o,
                               section .data_init object hal_entry.o,
                               section .data_init object Global.o,
                               section .data_init object ComUart.o,
                               section .data_init object Round.o,
                               section .data_init object SequenceIO.o, 
                               section .data_init object MotorIdentification.o,
                               section .data_init object PnPrmCal.o,
                               section .data_init object PrmTable.o,                               
                               section .data_init object DataCache.o,
                               section .data_init object Home.o,
                               section .data_init object AdvancedAutoTuning.o,
                               section .data_init object ExCtrlPrmCal.o,   
                               
                               section .data_init object ecatslv.o,        
                               section .data_init object MLib.o,        
                               section .data_init object objdef.o,  
                               section .data_init object CheckAlarm.o,    
                               section .data_init object FnOnePrmTuning.o, 
                               section .data_init object FnPrgJog.o,     
                               section .data_init object I2C_LED.o,  
                               
                               section .data_init object r_usb_basic.o, 
                               section .data_init object r_usb_cdataio.o,
                               section .data_init object r_usb_clibusbip.o,
                               section .data_init object r_usb_creg_abs.o,        
                               section .data_init object r_usb_creg_access.o,   
                               section .data_init object r_usb_mcu.o,        
                               section .data_init object r_usb_pcdc_driver.o,  
                               section .data_init object r_usb_pcontrolrw.o,                                   
                               section .data_init object r_usb_pdriver.o,
                               section .data_init object r_usb_pinthandler_usbip0.o,
                               section .data_init object r_usb_plibusbip.o,        
                               section .data_init object r_usb_preg_abs.o,   
                               section .data_init object r_usb_preg_access.o,       
                               section .data_init object r_usb_psignal.o,   
                               section .data_init object r_usb_pstdfunction.o, 
                               section .data_init object r_usb_pstdrequest.o,                                  
                               section .data_init object usb_pcdc_descriptor.o,   
                               
                               section .data_init object modbus.o,   
                               

                               
                               
                               section .rodata_init object  PrmTable.o,
                               section .rodata_init object  pin_data.o,
                               section .rodata_init object  I2C_LED.o,
                               };
                               
define block USER_CUSTOM_DATA_WBLOCK with fixed order, alignment = 4
                             { 
                               section .data object   bsp_usb.o,
                               section .data object   bsp_uart.o,
                               section .data object   bsp_led.o,
                               section .data object   bsp_eeprom.o,,
                               section .data object   hal_data.o,
                               section .data object   pin_data.o,
                               section .data object   cmd_flash.o, 
                               section .data object   flash.o,
                               section .data object   common.o,
                               section .data object   flash_if.o,
                               section .data object   menu.o,           
                               section .data object   ymodem.o,
                               section .data object   hal_entry.o,
                               section .data object   Global.o,
                               section .data object   ComUart.o,                         
                               section .data object   Round.o,
                               section .data object   SequenceIO.o,   
                               section .data object   MotorIdentification.o,
                               section .data object   PnPrmCal.o,
                               section .data object   PrmTable.o,                              
                               section .data object   DataCache.o,
                               section .data object   Home.o,
                               section .data object   AdvancedAutoTuning.o,
                               section .data object   ExCtrlPrmCal.o,   

                               
                               section .data object   ecatslv.o,        
                               section .data object   MLib.o,        
                               section .data object   objdef.o,  
                               section .data object   CheckAlarm.o,    
                               section .data object   FnOnePrmTuning.o, 
                               section .data object   FnPrgJog.o,       
                               section .data object   I2C_LED.o, 

                               section .data object   r_usb_basic.o, 
                               section .data object   r_usb_cdataio.o,
                               section .data object   r_usb_clibusbip.o,
                               section .data object   r_usb_creg_abs.o,        
                               section .data object   r_usb_creg_access.o,   
                               section .data object   r_usb_mcu.o,        
                               section .data object   r_usb_pcdc_driver.o,  
                               section .data object   r_usb_pcontrolrw.o,        
                               section .data object   r_usb_pdriver.o,
                               section .data object   r_usb_pinthandler_usbip0.o,
                               section .data object   r_usb_plibusbip.o,        
                               section .data object   r_usb_preg_abs.o,   
                               section .data object   r_usb_preg_access.o,       
                               section .data object   r_usb_psignal.o,   
                               section .data object   r_usb_pstdfunction.o, 
                               section .data object   r_usb_pstdrequest.o,   
                               section .data object   usb_pcdc_descriptor.o,  
                               
                               section .data object   modbus.o,                                
                                
                               
                               section .rodata object   PrmTable.o,
                               section .rodata object   pin_data.o,
                               section .rodata object   I2C_LED.o,
                               };                               
                               
                               

define block VECTOR_RBLOCK with alignment = 32 { ro code section .intvec_init};
define block VECTOR_WBLOCK with alignment = 32 { rw code section .intvec};
define block USER_PRG_RBLOCK with alignment = 4 { ro code };
define block USER_PRG_WBLOCK with alignment = 4 { rw code };
define block USER_DATA_ZBLOCK with alignment = 4 { section .bss };
define block USER_DATA_RBLOCK with fixed order, alignment = 4
                              { section .data_init,
                                section __DLIB_PERTHREAD_init,
                                section .rodata_init,
                                section .version_init };
define block USER_DATA_WBLOCK with fixed order, alignment = 4
                              { section .data,
                                section __DLIB_PERTHREAD,
                                section .rodata,
                                section .version };
                                
define block DMAC_LINK_MODE_ZBLOCK with alignment = 4 { section .dmac_link_mode* };
define block SHARED_NONCACHE_BUFFER_ZBLOCK with alignment = 32 { section .shared_noncache_buffer* };
define block NONCACHE_BUFFER_ZBLOCK with alignment = 32 { section .noncache_buffer* };


initialize manually  { ro code object startup.o,
                       ro code object system.o,
                       ro code object bsp_clocks.o,
                       ro code object bsp_irq.o,
                       ro code object bsp_register_protection.o,
                       ro code object r_ioport.o,
                       ro code section .intvec,
                       ro code section .warm_start,
                       ro code object bsp_usb.o,
                       ro code object bsp_uart.o,
                       ro code object bsp_led.o,
                       ro code object bsp_eeprom.o,
                       ro code object hal_data.o,
                       ro code object pin_data.o,
                       ro code object cmd_flash.o,           
                       ro code object flash.o,
                       ro code object common.o,
                       ro code object flash_if.o,
                       ro code object menu.o,
                       ro code object ymodem.o,
                       ro code object hal_entry.o,
                       ro code object Global.o,
                       ro code object ComUart.o,
                       ro code object Round.o,
                       ro code object SequenceIO.o, 
                       
                       ro code object MotorIdentification.o,
                       ro code object PnPrmCal.o,
                       ro code object PrmTable.o,
                       ro code object PowerManager.o,
                       ro code object Home.o,
                       ro code object AdvancedAutoTuning.o,
                       ro code object ExCtrlPrmCal.o,

                       
                       ro code object ecatslv.o,
                       ro code object MLib.o,        
                       ro code object objdef.o,  
                       ro code object CheckAlarm.o,  
                       ro code object FnOnePrmTuning.o,  
                       ro code object FnPrgJog.o,
                       ro code object I2C_LED.o,
                       
                       ro code object r_usb_basic.o, 
                       ro code object r_usb_cdataio.o,
                       ro code object r_usb_clibusbip.o,
                       ro code object r_usb_creg_abs.o,        
                       ro code object r_usb_creg_access.o,   
                       ro code object r_usb_mcu.o,        
                       ro code object r_usb_pcdc_driver.o,  
                       ro code object r_usb_pcontrolrw.o, 
                       ro code object r_usb_pdriver.o,
                       ro code object r_usb_pinthandler_usbip0.o,
                       ro code object r_usb_plibusbip.o,        
                       ro code object r_usb_preg_abs.o,   
                       ro code object r_usb_preg_access.o,        
                       ro code object r_usb_psignal.o,   
                       ro code object r_usb_pstdfunction.o, 
                       ro code object r_usb_pstdrequest.o,   
                       
                       ro code object modbus.o,


                       
                       ro code,
                       section .data,
                       section __DLIB_PERTHREAD,
                       section .rodata,
                       section .version 
                       };

do not initialize   { section .noinit,
                      section .bss,
                      section .dmac_link_mode*,
                      section .shared_noncache_buffer*,
                      section .noncache_buffer*,
                      rw section HEAP,
                      rw section .stack*,
                      rw section .sys_stack,
                      rw section .svc_stack,
                      rw section .irq_stack,
                      rw section .fiq_stack,
                      rw section .und_stack,
                      rw section .abt_stack };
if (0)
{
place at address mem: __ICFEDIT_intvec_start__ { block VECTOR_WBLOCK };
place in SYSTEM_RAM_region        { block USER_PRG_WBLOCK };
place in SYSTEM_RAM_region        { readwrite };
place in SYSTEM_RAM_region        { block USER_DATA_WBLOCK,
                             block USER_DATA_ZBLOCK };
place in SYSTEM_RAM_region        { rw section HEAP,
                             rw section .stack* };
place in D_LDR_DATA_region { section LDR_DATA_WBLOCK, block LDR_DATA_WBLOCK,
                             section LDR_DATA_ZBLOCK, block LDR_DATA_ZBLOCK };
place at start of D_LDR_PRG_region { block LDR_PRG_WBLOCK };
place in D_LDR_PRG_region  { rw section .sys_stack,
                             rw section .svc_stack,
                             rw section .irq_stack,
                             rw section .fiq_stack,
                             rw section .und_stack,
                             rw section .abt_stack };
place in LDR_PARAM_region { readonly section .loader_param };
place in S_LDR_PRG_region { block LDR_PRG_RBLOCK };
place in S_LDR_DATA_region { section LDR_DATA_RBLOCK, block LDR_DATA_RBLOCK };
place in S_intvec_region { block VECTOR_RBLOCK };
place in ROM_region   { block USER_PRG_RBLOCK, readonly };
place in S_RAM_region { block USER_DATA_RBLOCK };
} else
{


define block CUSTOM_QUICK_DATA_ZBLOCK  with alignment = 4 { section .customdata.* };

do not initialize   { section .customdata.*};

define symbol __region_D_LDR_start__   = SYSTEM_RAM_PRV_START + SYSTEM_RAM_PRV_LENGTH - 0x004E000;
define symbol __region_D_LDR_end__     = SYSTEM_RAM_PRV_START + SYSTEM_RAM_PRV_LENGTH - 0x0044000 - 1;


define symbol __region_S_LTBL_start__   = xSPI1_CS0_SPACE_PRV_START  + 0x00100000;
define symbol __region_S_LTBL_end__     = xSPI1_CS0_SPACE_PRV_START  + 0x00101000 - 1;

define symbol __region_S_LDR_start__    = xSPI1_CS0_SPACE_PRV_START + 0x00101000;
define symbol __region_S_LDR_end__      = xSPI1_CS0_SPACE_PRV_START + 0x0010B000  - 1;


//define symbol __region_S_VECTOR_start__    = xSPI1_CS0_SPACE_PRV_START + 0x0010B000;
//define symbol __region_S_VECTOR_end__      = xSPI1_CS0_SPACE_PRV_START + 0x0010B100 - 1;
define symbol __region_S_APP_start__       = xSPI1_CS0_SPACE_PRV_START + 0x0010B000;
define symbol __region_S_APP_end__         = xSPI1_CS0_SPACE_PRV_START + 0x01000000 - 1;


define region D_LDR_region          =  mem: [from __region_D_LDR_start__ to __region_D_LDR_end__ ];
               
               
define region S_LDR_region          =  mem: [from __region_S_LDR_start__  to __region_S_LDR_end__ ];
define region S_LOADER_TBL_region   =  mem: [from __region_S_LTBL_start__ to __region_S_LTBL_end__ ];
//define region S_VECTOR_region       =  mem: [from __region_S_VECTOR_start__  to __region_S_VECTOR_end__ ];
define region S_APP_region          =  mem: [from __region_S_APP_start__  to __region_S_APP_end__ ];

place at address mem: ATCM_PRV_START { block VECTOR_WBLOCK };                          
                          
place in ATCM_region        {  
                                block LDR_PRG_WBLOCK,
                                block USER_PRG_WBLOCK 
                             };
place in BTCM_region         {   section LDR_DATA_WBLOCK, block LDR_DATA_WBLOCK,
                                 block USER_DATA_WBLOCK,
                                 section LDR_DATA_ZBLOCK, block LDR_DATA_ZBLOCK,
                                 readwrite,
                                 rw section .sys_stack,
                                 rw section .svc_stack,
                                 rw section .irq_stack,
                                 rw section .fiq_stack,
                                 rw section .und_stack,
                                 rw section .abt_stack,
                                  block USER_DATA_ZBLOCK,
                                 block CUSTOM_QUICK_DATA_ZBLOCK,
                              };
                     
place in SYSTEM_RAM_region   { block USER_CUSTOM_PRG_WBLOCK,
                               section USER_CUSTOM_DATA_WBLOCK, block USER_CUSTOM_DATA_WBLOCK,
                               section USER_CUSTOM_DATA_ZBLOCK, block USER_CUSTOM_DATA_ZBLOCK,
                               rw section HEAP,
                               rw section .stack* };                          


                          
place in S_LDR_region { block LDR_PRG_RBLOCK,
                        section LDR_DATA_RBLOCK,
                        block LDR_DATA_RBLOCK ,
                        };
                        
                        
place at start  of S_LOADER_TBL_region {readonly section user_loader_tbl};                  

place at start of S_APP_region {block VECTOR_RBLOCK};

place in S_APP_region  { block USER_PRG_RBLOCK,
                         block USER_DATA_RBLOCK, 
                         block USER_CUSTOM_PRG_RBLOCK,
                         block USER_CUSTOM_DATA_RBLOCK,
                         readonly,
                         };
}


place in DMAC_LINK_MODE_region { block DMAC_LINK_MODE_ZBLOCK };
place in SHARED_NONCACHE_BUFFER_region { block SHARED_NONCACHE_BUFFER_ZBLOCK };
place in NONCACHE_BUFFER_region { block NONCACHE_BUFFER_ZBLOCK };
place in ATCM_region { };
place in BTCM_region { };
place in SYSTEM_RAM_region { };
place in SYSTEM_RAM_MIRROR_region { };
place in XSPI0_CS0_MIRROR_region { };
place in XSPI0_CS1_MIRROR_region { };
place in XSPI1_CS0_MIRROR_region { };
place in XSPI1_CS1_MIRROR_region { };
place in CS0_MIRROR_region { };
place in CS2_MIRROR_region { };
place in CS3_MIRROR_region { };
place in CS5_MIRROR_region { };
place in XSPI0_CS0_region { };
place in XSPI0_CS1_region { };
place in XSPI1_CS0_region { };
place in XSPI1_CS1_region { };
place in CS0_region { };
place in CS2_region { };
place in CS3_region { };
place in CS5_region { };