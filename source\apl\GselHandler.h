 /****************************************************************************************************
 *
 * FILE NAME:  GselHandler.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.24
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	24-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _GSEL_HANDLER_H
#define _GSEL_HANDLER_H

#include "BaseDef.h"
/*--------------------------------------------------------------------------------------------------*/
/*		Gain Select Struct 			    			        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	REAL32  	Kp;
	REAL32  	Kv;
	REAL32		Kv2;					// Speed loop gain 2					
	REAL32		Kv2Inv;					// Speed loop gain 2 reciprocal			
	REAL32  	Kvi;
	REAL32      Klpf;                   // 1st delay torque filter gain
	REAL32		KpInv;					// Position loop gain reciprocal SvCycle unit
	REAL32		VirtualPosKp;			// Gain for virtual position deviation calculation
	REAL32		ZcKp;					// Position clamp gain for zero clamp
}GSELGAINS;

typedef	struct 
{					
	REAL32		Ks;						// Disturbance observer gain					
	REAL32		Kj;						// Disturbance observer bias gain
	REAL32		Ksj;					// Disturbance observer torque gain
	REAL32		Kd;						// Disturbance torque coefficient		
	REAL32		Klpf;					// Disturbance observer low pass filter gain
} GSELDOBS;


typedef	struct 
{							
	struct	
	{								
		INT32		Wait;							// Gain switching wait time			
		INT32		AmondGain;						// Analog monitor output gain		
		INT32		SwTime;							// Gain switching time[ms]						
		INT32		SvCycleUs;						// Control cycle[us]				
		REAL32		GselTrqFilMin;					
		GSELGAINS	dGain;						
		GSELDOBS	dGainDobs;						
	} conf;

	struct	
	{							
		INT32		Timer;							// Gain switching wait timer			
		GSELGAINS	*prevGain;						// Previous gain					
		GSELGAINS	*nextGain;						// Next time gain	
		GSELDOBS	*prevGainDobs;					// Previous gain (disturbance observer)
		GSELDOBS	*nextGainDobs;					// Next gain (disturbance observer)
	} var;
} ATGSEL;


typedef	struct 
{
	ATGSEL			AtGselA;			// Variable for automatic gain switching(TrgA)		
	ATGSEL			AtGselB;			// Variable for automatic gain switching(TrgB)

	GSELGAINS		GselGains[6];	
	GSELDOBS		GselDobs[3];	
	BOOL			AtGsel;				// Automatic gain switching selection setting
	INT32			AtGselTrgA;			// Automatic gain switching condition A selection setting								*/
	INT32			AmonActGain;		// AnaMon : Effective gain									
	INT32			ActGain;			// Un     : Effective gain						
} GAIN_CHNG_HNDL;



#endif



