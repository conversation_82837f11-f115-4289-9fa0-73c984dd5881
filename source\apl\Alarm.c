/****************************************************************************************************
 *
 * FILE NAME:  Alarm.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Alarm.h"
#include "Mlib.h"
#include "HardApi.h"

/* globals in this file */
PRIVATE ALARM    *AlmMngrGblHandle[MAX_AXIS_NUM];
PRIVATE INT32    AlmNumberOfAxes;
		
#define	TIME_STAMP_SEC			36000	  // [sec] Check Count for TimeStamp:36000*0.1s=1h	

/****************************************************************************************************
 * DESCRIPTION: Initialization of Global Handler
 *			   
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ALMInitGlobalHandle(ALARM **AxisTopHandle, INT32 AxisNum)
{
	INT16 ax;
	for( ax = 0; ax < AxisNum; ax++ )
	{
		AlmMngrGblHandle[ax] = AxisTopHandle[ax];
	}
	AlmNumberOfAxes = AxisNum;
}


/****************************************************************************************************
 * DESCRIPTION: Set an Alarm to all axises
 *             alm_id: Index number of the Alarm list table
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ALMSetGlobalAlarm(ALMID_T alm_id)
{
	INT16 ax;
	for( ax = 0; ax < AlmNumberOfAxes; ax++ )
	{ 
		ALMSetServoAlarm( AlmMngrGblHandle[ax], alm_id );
	}
}


/****************************************************************************************************
 * DESCRIPTION: Clear a detected alarm to all axises
 *			   alm_id: Index number of the Alarm list table
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ALMClearGlobalAlarm(ALMID_T alm_id)
{
	INT16 ax;
	for( ax = 0; ax < AlmNumberOfAxes; ax++ )
	{ 
		ALMClearWarning( AlmMngrGblHandle[ax], alm_id );
	}
}

/****************************************************************************************************
 * DESCRIPTION: Check whether specified alarm already detected.
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL ALMCheckGlobalAlarm(ALMID_T alm_id)
{
	return ALMCheckEachState( AlmMngrGblHandle[0], alm_id );
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void almUpdateStatusFlags(ALARM *AlmMngr, ALM_TBL_DEF *alm_def_tbl)
{
	UINT32    attr;
	UINT16    stop_mode = 0;

	/*----------------------------------------------------------------------*/
	/* Update Alarm status */
	/*----------------------------------------------------------------------*/
	attr = alm_def_tbl->AlmAttr;

	if(FALSE == (attr & ALMDEF_ATTR_WARNING))
	{// alarm
		AlmMngr->Status.DevAlmFlag = TRUE;
		if(AlmMngr->Status.AlmFlag == FALSE)
		{	
			AlmMngr->Status.AlmCode = alm_def_tbl->AlmCode;
		}
		AlmMngr->Status.AlmFlag = TRUE;
		
		if (TRUE == (attr & ALMDEF_ATTR_NOTRESET))
		{
			AlmMngr->Status.AlmResetInfo = 1; 
		}
	}
	else
	{//warning
		AlmMngr->Status.DevWrnFlag = TRUE;
		if(AlmMngr->Status.AlmCode == 0)
		{	
			AlmMngr->Status.AlmCode = alm_def_tbl->AlmCode;
		}
		AlmMngr->Status.WrnFlag = TRUE;
	}
 
	if( stop_mode == 0x0F )
	{	
		stop_mode = AlmMngr->StopModeParam;
	}    
    
	stop_mode = alm_def_tbl->StopMode & 0xF;
	if( stop_mode > AlmMngr->Status.StopMode )
	{
		AlmMngr->Status.StopMode = stop_mode;
	}

}


/****************************************************************************************************
 * DESCRIPTION:
 * 			Initialize the Alarm log table
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void almInitAlmLogTable(ALARM *AlmMngr)
{
	UINT16		  i;
	ALM_LOG		  *Log;

	Log = &AlmMngr->Log;

	/* Alarm log header information check */
	if( (Log->AlmLogHdr.b.Cnt > ALM_LOG_NUM)
		|| (Log->AlmLogHdr.b.Idx > (ALM_LOG_NUM - 1)) )
	{
		Log->AlmLogHdr.w = 0x0000;
	}

	/* Alarm log initial configuration pointer */
	i = (Log->AlmLogHdr.b.Idx == 0) ?
			(ALM_LOG_NUM - 1) : (Log->AlmLogHdr.b.Idx - 1);

	Log->AlmLogSp = &Log->AlmLog[i];

	/* note:
	  The same alarm within 1h is not registered when the alarm log is registered
	  To avoid such a condition, clear AlmCode beforehand.
	*/
	if(Log->AlmLogHdr.b.Cnt == 0)
	{
		Log->AlmLogSp->AlmCode = 0x0000;
	}

}


/****************************************************************************************************
 * DESCRIPTION:
 * 			Alarm registration table reset
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void almResetAlarmTable(ALARM *AlmMngr)
{
	UINT16			i, j, cnt;
	UCHAR			Attrx;
	ALM_TBL			AlmTbl[ALM_TBL_MAX];
	ALM_TBL			*AlmTblSp;	
    UINT32          AlmRstMask;

	i = j = 0;

	*((UINT32*)&AlmMngr->Status) = 0;
	cnt = AlmMngr->Log.AlmCnt;
	while( i < cnt)
	{
		Attrx = AlmMngr->Log.AlmTbl[i].AlmDef->AlmAttr;
		if(0 != (Attrx & ALMDEF_ATTR_NOTRESET))
		{
			AlmTbl[j] = AlmMngr->Log.AlmTbl[i];
			j++;	// Undelete alarm count up

		}
		else
		{
			UINT16 idx = AlmMngr->Log.AlmTbl[i].AlmId >> 5;
            UINT16 BitId = AlmMngr->Log.AlmTbl[i].AlmId - (idx << 5);
            AlmRstMask = ~(1 << BitId);
			AlmMngr->AlmSet[idx] &= AlmRstMask;
		}
		i++;
	}

	AlmMngr->Log.AlmCnt = j;

	for(i = 0; i < j; i++)
	{
		AlmMngr->Log.AlmTbl[i] = AlmTbl[i];
		almUpdateStatusFlags(AlmMngr, AlmTbl[i].AlmDef);

	}

	AlmMngr->Log.LstAlmCnt = j;	
}


/****************************************************************************************************
 * DESCRIPTION:
 * 			warning registration table reset
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void almResetWarningTable(ALARM *AlmMngr)
{
	UINT16			i, j, cnt;
	UINT8			Attrx;
	ALM_TBL			AlmTbl[ALM_TBL_MAX];
    UINT32          AlmRstMask;

	i = j = 0;

	*((UINT32*)&AlmMngr->Status) = 0;
	cnt = AlmMngr->Log.AlmCnt;
	while( i < cnt)
	{
		Attrx = AlmMngr->Log.AlmTbl[i].AlmDef->AlmAttr;
		if(0 == (Attrx & ALMDEF_ATTR_WARNING))
		{
			AlmTbl[j] = AlmMngr->Log.AlmTbl[i];
			j++;	
		}
		else
		{
			UINT16 idx = AlmMngr->Log.AlmTbl[i].AlmId >> 5;
			UINT16 BitId = AlmMngr->Log.AlmTbl[i].AlmId - (idx << 5);
            AlmRstMask = ~(1 << BitId);
			AlmMngr->AlmSet[idx] &= AlmRstMask;
		}
		i++;
	}

	AlmMngr->Log.AlmCnt = j;

	for(i = 0; i < j; i++)
	{
		AlmMngr->Log.AlmTbl[i] = AlmTbl[i];
		almUpdateStatusFlags(AlmMngr, AlmTbl[i].AlmDef);
	}


	AlmMngr->Log.LstAlmCnt = j;
}


/****************************************************************************************************
 * DESCRIPTION:
 *			Set Alarm Log Information
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void almSetDetecetedAlarmInfo(ALARM *AlmMngr )
{
	UINT32		i, cnt;
	UINT32		attr, ofst;
	volatile UINT32 idx;
	ALM_LOG	    *Log;

	if(( AlmMngr->AlmEepRstBusy != FALSE )||( AlmMngr->Status.AlmFlag == FALSE ))	
	{
		return;
	}

	Log = &AlmMngr->Log;
	idx = Log->AlmLogHdr.b.Idx;
	cnt = Log->AlmCnt;

	/* Alarm registration table check process */
	while(Log->LstAlmCnt != cnt)
	{
		if( Log->LstAlmCnt > cnt )
		{
			Log->LstAlmCnt = cnt;
		}
		else
		{
			i = Log->LstAlmCnt;
			attr = Log->AlmTbl[i].AlmDef->AlmAttr;
	
			/* Registration processing in the alarm log table */
			if((attr & ALMDEF_ATTR_NOEEPROM) == 0)
			{
				if( (Log->AlmTbl[i].AlmDef->AlmCode != Log->AlmLogSp->AlmCode)
					|| ((Log->AlmTbl[i].TmStamp - Log->AlmLogSp->TmStamp) >= TIME_STAMP_SEC) )
				{
					Log->AlmLog[idx].AlmCode = Log->AlmTbl[i].AlmDef->AlmCode;
					Log->AlmLog[idx].TmStamp = Log->AlmTbl[i].TmStamp;
					Log->AlmLogSp = &Log->AlmLog[idx];

					/* Write Alarm Code to EEPROM */
                                        //commit by_lly 2021/11/15
					ofst = EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, EEP_ALMCODE_OFS) + (idx*2);

					EepdevWriteNoSumValues(  ofst, (UINT16*)&(Log->AlmLog[idx].AlmCode), 1);
					
					/* Write Time stamp to EEPROM */
					ofst = EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, EEP_ALMTIME_OFS) + (idx*4);

					EepdevWriteNoSumValues( ofst, (UINT16*)&(Log->AlmLog[idx].TmStamp), 2);	


					idx = (idx >= (ALM_LOG_NUM - 1)) ? 0 : idx + 1;
					if( Log->AlmLogHdr.b.Cnt < ALM_LOG_NUM )
					{	Log->AlmLogHdr.b.Cnt++;}
				}

			}
			Log->LstAlmCnt = i + 1;
		}
	}

	if(Log->AlmLogHdr.b.Idx != idx)
	{
		Log->AlmLogHdr.b.Idx = idx;
		
		/* Write Alarm Info. to EEPROM */
                //commit by_lly 2021/11/15
		ofst = EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, EEP_ALMINFO_OFS);

		EepdevWriteNoSumValues( ofst, (UINT16*)&(Log->AlmLogHdr.w), 1);	
	}
}


/****************************************************************************************************
* DESCRIPTION:  Start up procedure after power on reset sequence
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC UINT32 ALMStartupProcedure(ALARM *AlmMngr, UINT8 StopModeParam)
{
	UINT16				i;
	EEP_NOCHKSUM_VAL_T	eepVal;
	UINT32				AlmOLInfo;

	AlmMngr->StopModeParam = StopModeParam;
	AlmOLInfo = 0;

	/* Read EEPROM Alarm Trace Data	*/
        //commit by_lly 2021/11/15
	if(0 == EepdevReadValues(EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, 0),
							 (UINT16*)&eepVal, EEP_ENGINFO_OFS/2) )			
	{
		AlmMngr->Log.AlmLogHdr.w = eepVal.AlarmInfo;
		AlmMngr->Status.AlmOLInfo = eepVal.OLInfo;						
		AlmOLInfo = AlmMngr->Status.AlmOLInfo;							
		for(i = 0; i < ALM_LOG_NUM; i++)
		{
			AlmMngr->Log.AlmLog[i].AlmCode = eepVal.AlarmCode[i];
			AlmMngr->Log.AlmLog[i].TmStamp = eepVal.AlarmTime[i];
		}
	}

	/* Init. Alarm Log Table */
	almInitAlmLogTable( AlmMngr );

	/* Set the deteceted Alarm information */
	almSetDetecetedAlarmInfo(AlmMngr);

	/* Reset OL informations */
	if( AlmMngr->Status.AlmOLInfo != 0x0000 )		
	{
		/* Reset overload information other than regenerative overload */
		AlmMngr->Status.AlmOLInfo &= ALMDEF_OLINFO_RGOL;			
		AlmMngr->Log.EepAlmOLInfo = AlmMngr->Status.AlmOLInfo;	

		/* Write to EEPROM Alarm OverLoad Info. */
		eepVal.OLInfo = (UINT16)AlmMngr->Status.AlmOLInfo;
                 // commit by_lly 2021-11-15
		EepdevWriteNoSumValues( EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, EEP_OLINFO_OFS),
								  &eepVal.OLInfo, 1 );
	}

	return AlmOLInfo;
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ALMInitAlarm(ALARM *AlmMngr, ALM_TBL_DEF *AlmDefTbl, UINT16 AlmDefCnt, UINT32 AxisNumber)
{
	UINT16	i;

	/* Initialize the member data in this module */
	MlibResetLongMemory(AlmMngr,  sizeof(ALARM)/4);

	AlmMngr->DefTable = AlmDefTbl;	
	AlmMngr->TableEntryNum = AlmDefCnt;
	AlmMngr->myAsicNo = AxisNumber;

        //commit by_lly 2021/11/15
	EepdevCreateQueue(&AlmMngr->EepQue, AlmMngr->EepQueBuf,
						sizeof(AlmMngr->EepQueBuf)/sizeof(EEP_QUE_BUFFER));

	/* Set a pointer of the last Log buffer */
	AlmMngr->Log.AlmLogSp = &(AlmMngr->Log.AlmLog[0]);

	/* Set the mask value at reseting alarm */
	for(i = 0; i < AlmDefCnt; i++)
	{
		if(0 != (AlmMngr->DefTable[i].AlmAttr & ALMDEF_ATTR_NOTRESET))
		{
			AlmMngr->AlmRstMsk[i>>5] |= 1 << (i & 0x1F);
		}

	}
	
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC ALM_TBL_DEF* ALMSetServoAlarm(ALARM *AlmMngr, ALMID_T alm_id)
{
	ALM_TBL			*alm_tbl_ptr;
	ALM_TBL_DEF  	*alm_def_tbl;
	UINT32			crt_idx, idx;
	UINT32			ArrayIdx, bitValue;	

	ArrayIdx = alm_id >> 5;
	bitValue = 1 << (alm_id&0x1F);
	alm_def_tbl = &AlmMngr->DefTable[alm_id];

	if((AlmMngr->AlmSetMsk[ArrayIdx] & bitValue) != 0 )
	{
		return NULL;
	}
	if((AlmMngr->AlmSet[ArrayIdx] & bitValue) != 0)
	{
		return alm_def_tbl;
	}

	crt_idx = AlmMngr->Log.AlmCnt;
	idx = (crt_idx < (ALM_TBL_MAX-1)) ? (crt_idx + 1) : crt_idx;

	AlmMngr->AlmSet[ArrayIdx] |= bitValue;

	almUpdateStatusFlags(AlmMngr, alm_def_tbl);   // todo

	AlmMngr->Log.AlmCnt = idx;

	if((idx < (ALM_TBL_MAX-1))	
		|| (0 != (alm_def_tbl->AlmAttr & ALMDEF_ATTR_NOTRESET)) )
	{
		alm_tbl_ptr = &AlmMngr->Log.AlmTbl[crt_idx];
		alm_tbl_ptr->AlmId   = alm_id;
		alm_tbl_ptr->AlmDef  = alm_def_tbl;
		alm_tbl_ptr->TmStamp = hApi_GetTimeStamp1000ms();  
	}
        
       return alm_def_tbl;    
        
        

}


/****************************************************************************************************
 * DESCRIPTION:
 *           This function must be called from a lowest priority task
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ALMLogWriteService(ALARM *AlmMngr)
{
	UINT16	i;
	USHORT	ofst;
	USHORT	rstBuffer[20];

	/* Clear EEPROM Alarm log */
	if(AlmMngr->AlmEepRst != FALSE)
	{
		MlibResetLongMemory(&rstBuffer, sizeof(rstBuffer)/4);
		AlmMngr->AlmEepRstBusy = TRUE;

		/* Clear EEPROM Alarm Log Data */
                //commit by_lly 2021/11/15
		EepdevWriteNoSumValues( EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, EEP_ALMINFO_OFS),
								  rstBuffer,
								  1 );
		EepdevWriteNoSumValues( EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, EEP_ALMCODE_OFS),
								  rstBuffer,
								  10 );
		EepdevWriteNoSumValues( EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, EEP_ALMTIME_OFS),
								  rstBuffer,
								  20 );

		/* Clear Alarm Log Data */
		AlmMngr->Log.AlmLogHdr.w = 0;
		for(i = 0; i < ALM_LOG_NUM; i++)
		{
			AlmMngr->Log.AlmLog[i].AlmCode = 0;
			AlmMngr->Log.AlmLog[i].TmStamp = 0;
		}
		AlmMngr->Log.AlmCnt = 0;
		AlmMngr->Log.LstAlmCnt = 0;

		AlmMngr->AlmEepRstBusy = FALSE;
		AlmMngr->AlmEepRst = FALSE;
	}

	/* Set the deteceted Alarm information to the Log buffer */
	almSetDetecetedAlarmInfo(AlmMngr);

	/* Write EEPROM Alarm Log */
	EepdevWriteQuedValues(&AlmMngr->EepQue, 8);

	/* EEPROM write request processing of alarm OL information */
	if(AlmMngr->Status.AlmOLInfo != AlmMngr->Log.EepAlmOLInfo)
	{
		AlmMngr->Log.EepAlmOLInfo = AlmMngr->Status.AlmOLInfo;
		
		/* Write OL Info. to EEPROM */
		ofst = EEP_NOSUM_ADDRESS(AlmMngr->myAsicNo, EEP_OLINFO_OFS);
		
		EepdevWriteNoSumValues( ofst, (UINT16*)&(AlmMngr->Log.EepAlmOLInfo), 1);	
	}

	/* Write EEPROM Alarm Latch Data */
	if( AlmMngr->LtAlmReq != 0 )
	{
		if( AlmMngr->LtAlmReq == 1 )
		{
			if( AlmMngr->Log.AlmCnt == AlmMngr->Log.LstAlmCnt )
			{
				*( AlmMngr->AlmCdAdr ) = ALMGetAlarmLog( AlmMngr, 0, AlmMngr->TmStpAdr );
				AlmMngr->LtAlmReq++;
			}
		}
		if( AlmMngr->LtAlmReq == 2 )
		{
			if( AlmMngr->LtDtWtCt != 0 )
			{
				EepdevWriteNoSumValues(  AlmMngr->LtRomAdr, AlmMngr->LtMemAdr, 1 );
				AlmMngr->LtMemAdr += 1;
				AlmMngr->LtRomAdr += 2;
				AlmMngr->LtDtWtCt--;
			}
			if( AlmMngr->LtDtWtCt == 0 )
			{
				AlmMngr->LtAlmReq = 3;		
			}
		}
		if( AlmMngr->LtAlmReq == 3 )
		{
			if ( AlmMngr->Status.AlmFlag == FALSE )
			{
				AlmMngr->LtAlmReq = 4;
			}
		}

	}

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ALMSetMask(ALARM *AlmMngr, ALMID_T alm_id, BOOL MaskSet)
{
	if(MaskSet != FALSE)
	{ /* Set mask */
		AlmMngr->AlmSetMsk[alm_id>>5] |= 1<<(alm_id&0x1F);
	}
	else
	{ /* Clear mask */
		AlmMngr->AlmSetMsk[alm_id>>5] &= ~(1<<(alm_id&0x1F));
	}
}

/****************************************************************************************************
 * DESCRIPTION: Check an Alarm state
 *
 * RETURNS:
 *    FALSE = no alarm, TRUE = alarm
****************************************************************************************************/
PUBLIC BOOL ALMCheckEachState(ALARM *AlmMngr, ALMID_T alm_id)
{
	BOOL alm_state;
	alm_state = (AlmMngr->AlmSet[alm_id >> 5] >> (alm_id & 0x1F)) & 1;
	return alm_state;
}


/****************************************************************************************************
 * DESCRIPTION: Get the other alarm state except "alm_id"
 *
 * RETURNS:
 *	  FALSE: no alarm, TRUE: alarm issued
****************************************************************************************************/
PUBLIC BOOL ALMGetOtherAlarmChk(ALARM *AlmMngr, ALMID_T alm_id)
{
	UINT16  i;
	
	if( AlmMngr->Log.AlmCnt == 0 ) 
		return( TRUE );		
	
	for(i = 0; i < AlmMngr->Log.AlmCnt; i++)
	{
		if( (AlmMngr->Log.AlmTbl[i].AlmId != alm_id)
			&& (0 == (AlmMngr->Log.AlmTbl[i].AlmDef->AlmAttr & ALMDEF_ATTR_WARNING)) )
		{
			return( TRUE );
		}
	}
	return( FALSE );
}


/****************************************************************************************************
 * DESCRIPTION: Check the Specified Alarm Set Mask Status
 *
 * RETURNS:
 *	  FALSE = not masked, TRUE = masked
****************************************************************************************************/
PUBLIC BOOL ALMCheckSetMask(ALARM *AlmMngr, ALMID_T alm_id)
{
	BOOL alm_state;
	alm_state = (AlmMngr->AlmSetMsk[alm_id >> 5] >> (alm_id & 0x1F)) & 1;
	return alm_state;
}

/****************************************************************************************************
* DESCRIPTION:  Set the Parameter error
*
* RETURNS:
*	  
****************************************************************************************************/
PUBLIC void ALMSetPramError(ALARM *AlmMngr, UINT16 ErrPrmNo)
{
	ALMSetServoAlarm(AlmMngr, ALM_PRM);		// Parameter setting error 
	if(AlmMngr->PrmErrInfo.ErrFlag == FALSE)
	{
		AlmMngr->PrmErrInfo.ErrPrmNo = ErrPrmNo;
		AlmMngr->PrmErrInfo.ErrFlag = TRUE;
	}
}


/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *	  
****************************************************************************************************/
PUBLIC void ALMClearWarning(ALARM *AlmMngr, ALMID_T alm_id)
{
#if	(ALM_WARNING_AUTO_CLEAR == TRUE)	
	UINT32			i, j, cnt;
	UINT32			wrn_cnt;	// Warning counter
	UINT32			clr_cnt;	// Clear counter
	UCHAR			Attrx;
	ALM_TBL			AlmTbl[ALM_TBL_MAX];
	UINT32     AlmRstMask;

	i = j = 0;
	wrn_cnt = clr_cnt = 0;	

	*((UINT32*)&AlmMngr->Status) = 0;
	cnt = AlmMngr->Log.AlmCnt;
	while( i < cnt)
	{
		Attrx = AlmMngr->Log.AlmTbl[i].AlmDef->AlmAttr;
		if(0 == (Attrx & ALMDEF_ATTR_WARNING))
		{	
			{
				AlmTbl[j] = AlmMngr->Log.AlmTbl[i];
			}
			j++;	
		}
		else
		{
			wrn_cnt++;	
			if(alm_id != AlmMngr->Log.AlmTbl[i].AlmId)
			{
				{
					AlmTbl[j] = AlmMngr->Log.AlmTbl[i];
				}
				j++;	
			}
			else
			{	
				UINT16 idx = AlmMngr->Log.AlmTbl[i].AlmId >> 5;
				UINT16 Bitid = AlmMngr->Log.AlmTbl[i].AlmId - (idx << 5);
//				AlmMngr->AlmSet[idx] &= AlmMngr->AlmRstMsk[idx];
			  AlmRstMask = ~(1 << Bitid);
				
				AlmMngr->AlmSet[idx] &= AlmRstMask;
				wrn_cnt--;	
				clr_cnt++;	
				AlmMngr->Log.AlmCnt--;	

			}
		}
		i++;
	}

	AlmMngr->Log.AlmCnt = j;

	if(wrn_cnt == 0)
	{	
		AlmMngr->Status.WrnFlag = FALSE;	
	}

	for(i = 0; i < j; i++)
	{
		AlmMngr->Log.AlmTbl[i] = AlmTbl[i];
		almUpdateStatusFlags(AlmMngr, AlmTbl[i].AlmDef);
	}
	if(clr_cnt > 0)
	{	
		AlmMngr->Log.AlmTbl[j].AlmId = NO_ALARM;	
		AlmMngr->Log.AlmTbl[j].AlmDef = 0x00000000;	
		AlmMngr->Log.AlmTbl[j].TmStamp = 0x00000000;
	}
#else//#if (ALM_WARNING_AUTO_CLEAR == TRUE)	
	UINT32			i, cnt;
	UCHAR			Attrx;

	i = 0;
	cnt = AlmMngr->Log.AlmCnt;
	while( i < cnt)
	{
		Attrx = AlmMngr->Log.AlmTbl[i].AlmDef->AlmAttr;
		if(1 == (Attrx & ALMDEF_ATTR_WARNING))
		{	
			if(alm_id == AlmMngr->Log.AlmTbl[i].AlmId)
			{	
				UINT16 idx = AlmMngr->Log.AlmTbl[i].AlmId >> 5;
                UINT16 Bitid = AlmMngr->Log.AlmTbl[i].AlmId - (idx << 5);
//              AlmMngr->AlmSet[idx] &= AlmMngr->AlmRstMsk[idx];
                AlmRstMask = ~(1 << Bitid);
                
                AlmMngr->AlmSet[idx] &= AlmRstMask;

			}
		}
		i++;
	}
#endif//#if (ALM_WARNING_AUTO_CLEAR == TRUE)	

}


/****************************************************************************************************
 * DESCRIPTION: Get the alarm status
 *
 * RETURNS:
 *	  
****************************************************************************************************/
PUBLIC UINT32 ALMGetStatus(ALARM *AlmMngr, BOOL *AlmFlag, BOOL *WrnFlag)
{
	*AlmFlag = AlmMngr->Status.AlmFlag;	
	*WrnFlag = AlmMngr->Status.WrnFlag;	
	return AlmMngr->Status.AlmCode;		
}


/****************************************************************************************************
 * DESCRIPTION:  Get the Alarm Log with alarm index
 *
 * RETURNS:
 *	  
****************************************************************************************************/
PUBLIC UINT16 ALMGetAlarmLog(ALARM *AlmMngr, UINT16 Index, UINT32 *TimeStamp)
{
	INT32	   i;
	UINT16	   almCode;
	ALM_LOG    *AlmLog;

	AlmLog = &AlmMngr->Log;

	if(Index >= (UINT16)AlmLog->AlmLogHdr.b.Cnt)
	{
		almCode = 0;
		*TimeStamp = 0;
	}
	else
	{
		i = AlmLog->AlmLogHdr.b.Idx - 1 - Index;
		if(i < 0)
		{
			i += ALM_LOG_NUM;
		}
		almCode = AlmLog->AlmLog[i].AlmCode;
		*TimeStamp = AlmLog->AlmLog[i].TmStamp;
	}

	return almCode;
}


/****************************************************************************************************
 * DESCRIPTION:  Get the Alarm Log 
 *
 * RETURNS:
 *	  
****************************************************************************************************/
PUBLIC INT32 ALMGetAlarmLogAll(ALARM *AlmMngr, UINT16 *AlmCode, UINT32 *TimeStamp, UINT16 MaxInfo)
{
	INT32	i, j;
	INT32	AlmCnt;
	ALM_LOG *AlmLog;

	AlmLog = &AlmMngr->Log;

	j = AlmLog->AlmLogHdr.b.Idx - 1;
	if(j < 0)
	{
		j += ALM_LOG_NUM;
	}

	if((INT32)AlmLog->AlmLogHdr.b.Cnt < MaxInfo)
	{
		AlmCnt = AlmLog->AlmLogHdr.b.Cnt;
	}
	else
	{
		AlmCnt = MaxInfo;
	}
	
	for(i = 0; i < AlmCnt; i++)
	{
		AlmCode[i] = AlmLog->AlmLog[j].AlmCode;
		TimeStamp[i] = AlmLog->AlmLog[j].TmStamp;
		--j;
		if(j < 0)
		{
			j += ALM_LOG_NUM;
		}
	}

	for( ; i < MaxInfo; i++)
	{
		AlmCode[i] = 0x00;
	}

	return AlmCnt;
}


/****************************************************************************************************
 * DESCRIPTION:  Alarm Log Reset Request service
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ALMSetAlarmHistResetRequest(ALARM *AlmMngr)
{
	AlmMngr->AlmEepRst = TRUE;
}



/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL AlmClearReq( ALARM *AlmMngr, ALMID_T AlmId )
{
	if( AlmMngr->AlmClear == FALSE )
	{
		AlmMngr->ClrAlmId = AlmId;
		AlmMngr->AlmClear = TRUE;
		return( TRUE );
	}
	return( FALSE );
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ALMRuntimeProcess(ALARM *AlmMngr, UINT8 StopModeParam )
{
	UINT32			i, j, cnt, idx,BitIdx;
	ALMID_T 		AlmId;
	ALM_TBL 		AlmTbl[ALM_TBL_MAX];
    UINT32          AlmRstMask;

	AlmMngr->StopModeParam = StopModeParam;
    
	if(AlmMngr->AlmRst != FALSE)
	{
		almResetAlarmTable(AlmMngr);

		AlmMngr->AlmRst = FALSE;
		AlmMngr->WrnRst = FALSE;
	}
	else if(AlmMngr->WrnRst != FALSE)
	{
		almResetWarningTable(AlmMngr);

		AlmMngr->WrnRst = FALSE;
	}
	else
	{
		;
	}

	AlmMngr->AlmReady = (AlmMngr->Status.AlmFlag == FALSE) ? TRUE : FALSE;

	if( AlmMngr->AlmClear == FALSE )
	{
		return;
	}
	
	AlmId = AlmMngr->ClrAlmId;
	
	i = j = 0;

	*((UINT32*)&AlmMngr->Status) = 0;
	cnt = AlmMngr->Log.AlmCnt;
	while( i < cnt)
	{
		if( AlmId != AlmMngr->Log.AlmTbl[i].AlmId )
		{
			AlmTbl[j] = AlmMngr->Log.AlmTbl[i];
			j++;

		}
		else
		{
			idx = AlmMngr->Log.AlmTbl[i].AlmId >> 5;
            BitIdx = AlmMngr->Log.AlmTbl[i].AlmId - (1 << idx);
            AlmRstMask = ~(1<<BitIdx);
			AlmMngr->AlmSet[idx] &= AlmRstMask;
		}
		i++;
	}

	AlmMngr->Log.AlmCnt = j;

	for(i = 0; i < j; i++)
	{
		AlmMngr->Log.AlmTbl[i] = AlmTbl[i];
		almUpdateStatusFlags(AlmMngr, AlmTbl[i].AlmDef);
	}
	
	AlmMngr->Log.LstAlmCnt = j;
	AlmMngr->AlmClear = FALSE;

}

