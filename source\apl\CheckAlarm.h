/****************************************************************************************************
 *
 * FILE NAME:  CheckAlarm.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _CHECK_ALARM_H_
#define	_CHECK_ALARM_H_
		
#include "BaseDef.h"


#define MOTSPD_OS_10	1677721		// over speed *�i1/10�j	
//#define STMOTSPD  		10			// Stall motor speed [10 rpm]

typedef	struct 
{
	INT32	OvrChkCnt;										
	INT32	OvrAlmCnt;						
	INT32	OvrTrqRef;						
	INT32	OvrAbsMotSpd;			
	INT32	OvrPeakMotSpd;					
	INT32	OvrTrqLevel;					
	INT32	OvrSpdLevel;	
        INT32   OvrTrqInTrqMode;
} CHECK_OVRRUN;


/*--------------------------------------------------------------------------------------------------*/
/*		Amplifier & motor overload check data definition											*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{				
	INT32	Tmid;							// Intermediate current OL time (including derating) [0.1s]
	INT32	Tmax;							// Maximum current OL time (including derating) [0.1s]
	REAL32	Ibse;							// Base current (including derating) [A]
	REAL32	Imid;							// Intermediate OL current (including derating) [A]
	REAL32	Imax;							// Maximum OL current (including derating) [A]
} OLPRM;

/*--------------------------------------------------------------------------------------------------*/
/*		Structure definition for overload check														*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct
{				
	REAL32	Ibsqrmid;					// Square of continuous overload base current							
	REAL32	Ibsqrmax;					// Instantaneous overload base current squared							
	REAL32	AlmLvlmid;					// Continuous overload alarm level								
	REAL32	WrnLvlmid;					// Continuous overload warning level							
	REAL32	AlmLvlmax;					// Instantaneous overload alarm level								
	REAL32	WrnLvlmax;					// Instantaneous overload warning level						
	REAL32	OlLvlmidGain;				// Continuous overload detection level gain [low-speed OL support]
	REAL32	OlLvlmaxGain;				// Instantaneous overload detection level gain [low-speed OL support]
	REAL32	CoolRateGain;				// Self-propelled air cooling coefficient 

} CHECK_OLP;


typedef	struct 
{
	struct	
	{						
		CHECK_OLP	NormalArea;			// Normal section overload check parameter					
		CHECK_OLP	LowSpdArea;			// Low-speed section overload check parameter					
		CHECK_OLP	LockArea;			// Lock section overload check parameter					
	} conf;

	struct	
	{						
		INT32	WrnInfo;				// Warning information										
		REAL32	SumImid;				// Squared current integrated value for continuous overload		
		REAL32	SumImax;				// Squared current integrated value for instantaneous overload		
	} var;
} CHECK_OL;


/*--------------------------------------------------------------------------------------------------*/
/*		Structure definition for DB overload check													*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct CHECK_DBOL {
	struct	
	{						
		BOOL	DbOHeatCheck;				// Selection of DB overload detection process due to temperature rise
		INT32	Kdbpower;					// DB overload: Power calculation gain					
		INT32	DbolPowMax; 				// DB overload: Maximum power					
		INT32	DbolPowBase; 				// DB overload: Power base						
		INT32	DbolSpdLevel;				// DB overload: speed level					
		INT32	DbolAlmLevel;				// DB overload: alarm level					
		INT32	Kundbpower;					// DB resistance power consumption operation gain	
		INT32	KundbpowerCycle;			// DB resistance power consumption operation gain				
		INT32	DbOHeatAlmLvl;				// DB overload (temperature rise): Alarm level					
		INT32	DbOHeatWrnLvl;				// DB overload (temperature rise): Warning level
	} conf;

	struct	
	{						
		BOOL	DbOHeatWarn;				// DB overload (temperature rise) warning detection flag
		INT32	DbolPowSum;					// DB overload: integrated power value
		INT32	DbPowerSum;					// DB average value: Power integrated value
		INT32	DbMeanCycle;				// DB average value: Number of power integration
		INT32	DbHeat;						// Integrated value for DB overload (temperature rise) detection
		INT32	UnDbPower;					// DB power consumption value [%]
		INT32	UnMeanDbPower;				// Average DB power consumption [%]
	} var;
} CHECK_DBOL;


/************************************************************************************************/
/*		Variable definition for amplifier low speed overload check								*/
/************************************************************************************************/
typedef	struct 
{							
	OLPRM	AmpLock;						// Basic parameters for lock section		
	OLPRM	AmpLow;							// Basic parameters for low speed section
} LOWOLPRM;

typedef	struct 
{						
	struct 
	{
		INT32	LowSpdOLchg;					// Low speed OL inflection point speed [low speed section] [2 ^ 24 / OvrSpd]
		INT32	LockSpdOLchg;					// Low-speed OL inflection point speed [lock section] [2 ^ 24 / OvrSpd]
		REAL32	LowSpdOffsetImid;				// Low-speed OL inflection point OL current [low-speed section] [A]
		INT32	LowSpdOffsetTmax;				// Low-speed OL inflection point OL time [low-speed section] [0.1s]
		INT32	TransOLImid;					// Low-speed OL intermediate OL current conversion coefficient 
		INT32	TransOLTmax;					// Low-speed OL maximum OL time conversion coefficient 		
		UINT8	LowSpdOLChkEnable;				// Low speed overload check execution flag
		INT8	Dummy1;							// for alignment					
		INT16	Dummy2;							// for alignment 							
	} conf;
} LOWOLCHKP;

typedef	struct 
{							
	LOWOLPRM	LowOLPrm;								// Basic parameters for low-speed overload check
	LOWOLCHKP	LowOLChkP;								// Low-speed overload check parameters

	struct
	{
		BOOL	BaseCurrentDerateEnable;				// Overload detection base current derating function enabled
	}conf;

} CHECK_LOWOL;


typedef	struct 
{
	INT32	PerWrnLevel;				// Over position error warning level [Pulse unit]
	INT32	PerAlmLevel;				// Over position error alarm level [Pulse unit]
	INT32	SvonPerWrnLevel;			// Over position error warning level when servo on [Pulse unit]
	INT32	SvonPerAlmLevel;			// Over position error alarm level when servo on [Pulse unit]

} CHECK_OVRPOSERR;

typedef	struct 
{
    INT32	FollowAlmLevel;			    // Follow int position error alarm level [Pulse unit]
    INT32   FollowAlmTime;              // Follow int position error alarm time [number]
    INT32	FollowAlmCnt;		
} CHECK_FOLLPOSERR;
typedef	struct 
{
	UINT32	OsAlmLvl;		   // over speed alarm level
	UINT32	OSCountMax; 	   // OS state continuous counter upper limit
	UINT32	OSCnt;			   // over speed counter

} CHECK_OVRSPD;

typedef struct
{
	UINT16	SwOcCnt;
    UINT16  SwOcTime;
	REAL32	SwOcCur;
}CHECK_OC;



typedef struct
{
	REAL32	StallCur;
	UINT32	StallCountMax;
	INT32   StallCnt;
	UINT32  StallSpdL;
}CHECK_STALL;
	
typedef struct
{
	UINT32   CheckCnt;
}CHECK_UID;


typedef struct
{
	UINT32   CheckCnt;
}CHECK_CAN;

typedef struct
{ 
  
	struct	
	{	
      #define PHASELACK_BASE_CURRENT 0.5
      REAL32 PhaLackCur;
      REAL32 TrqPhaLackCur;
				  REAL32 PLCheckTime;   // 62.5us every time 
	} conf;

 	struct	
	{						
      UINT32   CheckCnt;
      REAL32   IaFdbSum;
      REAL32   IbFdbSum;
      REAL32   IcFdbSum;    
      REAL32   IaRefSum;
      REAL32   IbRefSum;   
      REAL32   IcRefSum;      
	} var;   
  
}CHECK_PHALACK;


typedef struct
{	
	struct	
	{	
      #define MAX_MOTOR_TEMP  120
      #define MAX_IPM_TEMP    95
      REAL32 MaxMotTemp;
      REAL32 MaxIPMTemp;
	  REAL32 MotVtTime;    
	  REAL32 IPMVtTime;
	} conf;	

	struct	
	{	
      UINT32   MotVtCnt;
      UINT32   IPMVtCnt;
	  INT32    MotTemp;
	  INT32    IPMTemp;
	} var;	
  
}CHECK_OVTEMP;



typedef struct
{	
	struct	
	{	
	  REAL32 IPDPosNLmit;
	  REAL32 IPDPosPLmit;
	  #define IPD_POS_LMT_TIME 100   // 100ms 
	  UINT32 IPDPosLmtTime;
	} conf;	

	struct	
	{	
      REAL32   IPDPosFdbRad;
	  UINT32   IPDPosLmtCnt;
	} var;	
  
}CHECK_POS_LMT;

typedef	struct 
{
	CHECK_OL			 MotOL;				
	CHECK_OL			 AmpOL;			
	CHECK_OVRRUN         OvrRun;
	CHECK_DBOL			 DbOL;
	CHECK_OVRPOSERR 	 OvrPosErr;	
	CHECK_LOWOL			 AmpLowOL;	
	CHECK_OVRSPD         OvrSpd;
    CHECK_OC             SwOc;
	CHECK_STALL			 MotStall;
    CHECK_FOLLPOSERR     PosFollowErr;
    CHECK_UID			 UidCheck;
    CHECK_CAN			 CanStop;
    CHECK_PHALACK        PhaseChk;
	CHECK_OVTEMP		 OverTemp;
	CHECK_POS_LMT		 OverLimit;
} CHECK_ALARM;


PUBLIC void AdetCheckOverSpeed( ALARM *AlmMngr, CHECK_OVRSPD *pChkOs, INT32 MotSpd, BPRMDAT *Bprm );
PUBLIC void AdetCheckMotorOverrun( ALARM *AlmMngr, CHECK_OVRRUN *OvrRun,INT32 MotSpd,
																  INT32 TrqRef, INT32 BaseEnableSts,UINT16 CtrlMode);
PUBLIC BOOL AdetCheckOverLoadAmpMot( ALARM *AlmMngr, CHECK_ALARM *CheckAlarm,
												REAL32 IdRefMon, REAL32 IqRefMon, REAL32 AbsMotSpd, BOOL EncConnect );	

PUBLIC void AdetCheckSvonPerrOverFlow( ALARM *AlmMngr, CHECK_OVRPOSERR *OvrPosErr, INT32 PositionError );
PUBLIC void AdetCheckPerrOverFlow( ALARM *AlmMngr, CHECK_OVRPOSERR *OvrPosErr,
										BOOL PosCtrlMode, INT32 PositionError, BOOL SvonSpdLmt );
PUBLIC void AdetCheckPosFollow( ALARM *AlmMngr, CHECK_FOLLPOSERR *PosFollowErr,
										BOOL PosCtrlMode, INT32 PositionError,CiA402_PRM *Objects);
PUBLIC void AdetCheckAdOffset( ALARM *AlmMngr, UINT16 AxisID );
PUBLIC void AdetCheckSwOc(  ALARM *AlmMngr, CHECK_ALARM *CheckAlarm, BASE_LOOP *BaseLoops);
PUBLIC void AdetCheckMotStall(ALARM *AlmMngr, CHECK_STALL *MotStall, REAL32 Iq, INT32 MotSpd );
PUBLIC void AdetCheckUid(ALARM *AlmMngr, CHECK_UID *Uid_Alm, UINT32  Uid_Check_Result);
PUBLIC void AdetCheckBusOC(BOOL BaseEnable);
PUBLIC void AdetCheckPhaseLack(ALARM *AlmMngr, CHECK_PHALACK *PhaseChk, BASE_LOOP *BaseLoops);

PUBLIC void LosaPhaseCheckALM( ALARM *AlmMngr, UINT16 AxisID );


PUBLIC void AdetCheckOverTemp(ALARM *AlmMngr, CHECK_OVTEMP *OverTemp);
PUBLIC void AdetCheckIPDPosLimit( ALARM *AlmMngr, CHECK_POS_LMT *OverLimit, REAL32 IPDPosFdbRad, BOOL RefDir);




#endif //_CHECK_ALARM_H_

