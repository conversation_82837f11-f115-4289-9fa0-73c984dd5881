/****************************************************************************************************
 *
 * FILE NAME:  Encoder.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.14
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	14-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Encoder.h"
#include "HardApi.h"
#include "bsp_enc.h"
#include "TamagawaIF.h"
#include "BisscEncIF.h" 
#include "RegMngr.h"
#include "RegAccessIF.h"
#include "fundefine.h"
#include "ktm59xxIF.h"
#include "Ma600IF.h"



/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void EncoderConbineInit(ENCODER *pEnc)
{   
    
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void EncoderInit(UINT16 AxisID,ENCODER *pEnc,UINT16 PluseInMode)
{   
    INT32 EncType = pEnc->P.EncType;
    UINT8 tmp = 0;
    UINT8 InitState = 0;
    UINT8 mode = (PluseInMode & 0x00FF);
    
    if(EncType == ENC_TYPE_BISSC)
    {
         hApi_IniBissC(AxisID);
         hApi_IniBissC(1);    
    }
    else if(EncType == ENC_TYPE_2BISSC)
    {
          hApi_IniBissC(AxisID);   
          hApi_IniBissC(1);       
    } 
    else if(EncType == ENC_TYPE_KTM)
    {
        InitState =  hApi_IniKtm59();         
        
        if(InitState)
        {
            // todo alarm   
            ALMSetGlobalAlarm(ALM_ENCDATA);
        }  
    }
    else if(EncType == ENC_TYPE_MA600)
    {
          hApi_MA600_Init();
    } 

    #if NO_ENCINCREMENT
    if(EncType == ENC_TYPE_INCREMENT)
    {
        hApi_IncEncoderInit(0,AxisID);
	    pEnc->V.MechAngle = 0;
        pEnc->V.MotPos = 0;
        pEnc->V.AbzPulse = 0;
        pEnc->V.LastAbzPulse = 0;
    }
	    else
    {
       hApi_IncEncoderInit(mode, AxisID);
    }
    #endif
    

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void GetEncoderPulse(ENCODER *pEnc, ALARM *AlmMngr, UINT16 AxisID, BOOL ServoOn)
{
    UINT8  tmp,tmp1;
    UINT8  state = 0;
    
    #if NO_ENCINCREMENT
    if(pEnc->P.EncType == ENC_TYPE_INCREMENT)
    {
        pEnc->V.AbzPulse = hApi_GetIncEncoderPulse(AxisID);
        pEnc->V.EncConnect = TRUE;
    }
    else 
    #endif
    if(!pEnc->V.ExCmdEn)
    {
        if(ENC_TYPE_BISSC == pEnc->P.EncType || ENC_TYPE_2BISSC == pEnc->P.EncType || ENC_TYPE_KTM == pEnc->P.EncType)
        {
            if(ENC_TYPE_KTM == pEnc->P.EncType)
            {
                state = hApi_KTM59_GetPos((UINT32 *)&pEnc->V.AbsRecvBuf[0]);
            }
            else if(ENC_TYPE_BISSC == pEnc->P.EncType || ENC_TYPE_2BISSC == pEnc->P.EncType)
            {
                state = hApi_Bissc_GetPos((UINT32 *)&pEnc->V.AbsRecvBuf[0],AxisID);
            }
            else if(ENC_TYPE_MA600 == pEnc->P.EncType)
            {
                state = hApi_MA600_readMagAlphaAngleWithParityBitCheck((UINT32 *)&pEnc->V.AbsRecvBuf[0]);
            }

            if(state == TRUE)
            {
                pEnc->V.LostLink++;
                if(pEnc->V.ComErrCnt >= ENC_COM_ERR_LMT)
                {
                    pEnc->V.bDataLost = FALSE;
                    pEnc->V.ComErrCnt = ENC_COM_ERR_LMT;
                    ALMSetServoAlarm( AlmMngr, ALM_ENCBRK );	
                }
                else if(ServoOn)
                {
                    pEnc->V.bDataLost = TRUE;
                    pEnc->V.ComErrCnt += 3;
                }
                else
                {
                    pEnc->V.bDataLost = TRUE;
                    pEnc->V.ComErrCnt ++;
                }
                
                pEnc->V.EncConnect = FALSE;              
            }
            else
            {
                pEnc->V.EncConnect = TRUE;
                pEnc->V.bDataLost = FALSE;
            }
        }
    }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void GetEncoderPulse2(ENCODER *pEnc, ALARM *AlmMngr, UINT16 AxisID, BOOL ServoOn)
{
 UINT8  tmp,tmp1;
    UINT8  state = 0;
//    
    if(!pEnc->V.ExCmdEn)
    {
        if(ENC_TYPE_BISSC == pEnc->P.EncType)
        {
          state = hApi_Bissc_GetPos((UINT32 *)&pEnc->V.AbsRecvBuf[0],1);
//    
          if(state == TRUE)
          {
            if(pEnc->V.ComErrCnt >= ENC_COM_ERR_LMT)
            {
                pEnc->V.bDataLost = FALSE;
                pEnc->V.ComErrCnt = ENC_COM_ERR_LMT;
                ALMSetServoAlarm( AlmMngr, ALM_ENCBRK );	
            }
            else if(ServoOn)
            {
                pEnc->V.bDataLost = TRUE;
                pEnc->V.ComErrCnt += 3;
            }
            else
            {
                pEnc->V.bDataLost = TRUE;
                pEnc->V.ComErrCnt ++;
            }
//    
             pEnc->V.EncConnect = FALSE;              
          }
          else
          {
            pEnc->V.EncConnect = TRUE;
            pEnc->V.bDataLost = FALSE;
          }
        }
        else
        {
            pEnc->V.EncConnect = TRUE;
            pEnc->V.bDataLost = FALSE;          
        }
    }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void ClearEncoderPulse2(ENCODER *pEnc, UINT16 AxisID)
{
//    UINT16 EncoderId = 0;
//    
//    if(pEnc->P.EncType == ENC_TYPE_INCREMENT)
//    {
//      return ;
//    }
//    
//    hApi_ClearIncEncoderPulse(EncoderId);
//    
//    pEnc->V1.AbzPulse = 0;
//    pEnc->V1.MotPos   = 0;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void EncoderMotPosCal(ENCODER *pEnc,ENCODER *pEnc2)
{
    BOOL   AbsPosReCal = FALSE; 
    INT32  Tmp;
    UINT16 CRC_Read = 0;
    INT32  DeltaSingleTurn = 0;    
    INT32   DeltaAccTurn = 0;

    if( (pEnc->V.EncConnect == TRUE) && (pEnc->V.AbsPosEncConnect == FALSE))
    {
        AbsPosReCal = TRUE;
        pEnc->V.AbsPosEncConnect = pEnc->V.EncConnect;   

        // add by lly 20131122 add Abnormal position increment detect 
        Tmp = pEnc->V.SingleTurn << pEnc->P.EncSftBitNum;
        DeltaSingleTurn = (Tmp - pEnc->V.LastSingleTurn) >> pEnc->P.EncSftBitNum;
        DeltaAccTurn = DeltaSingleTurn - pEnc->V.DeltaSingleTurn;
        
        if(MlibABS(DeltaAccTurn) > pEnc->P.EncMaxPluseInc)  //  Abnormal position increment
        {
            pEnc->V.EncIncAbnormFlag = TRUE;
        }
        else
        {
            pEnc->V.EncIncAbnormFlag = FALSE;
        }
    }


    if(pEnc->P.EncIacEnable == FALSE)
    {
        pEnc->V.EncIncAbnormFlag = FALSE;
    }
  
    if(FALSE == pEnc->V.bDataLost && pEnc->V.EncIncAbnormFlag == FALSE)
    {
        Tmp = (pEnc->V.SfSingleTurn << pEnc->P.EncSftBitNum);
        pEnc->V.DeltaSingleTurn = (Tmp - pEnc->V.LastSingleTurn) >> pEnc->P.EncSftBitNum;
        pEnc->V.LastSingleTurn = Tmp;	
    }
    else
    {
        Tmp = (pEnc->V.SfSingleTurn << pEnc->P.EncSftBitNum) + (pEnc->V.DeltaSingleTurn << pEnc->P.EncSftBitNum);
        pEnc->V.SfSingleTurn = (Tmp) >> pEnc->P.EncSftBitNum;
        pEnc->V.DeltaSingleTurn = pEnc->V.DeltaSingleTurn;
        pEnc->V.LastSingleTurn = pEnc->V.SfSingleTurn << pEnc->P.EncSftBitNum;	                          
    }
    
    
    if(pEnc->V.EncReset)
    {
        pEnc->V.MultiTurn = 0;
        AbsPosReCal = TRUE;
        pEnc->V.EncReset = FALSE; 
    }  

    
    if( AbsPosReCal)
    {
        pEnc->V.MotPos = (pEnc->V.MultiTurn << (32 - pEnc->P.EncSftBitNum)) + pEnc->V.SingleTurn;
    }
    else
    {
        pEnc->V.MotPos += pEnc->V.DeltaSingleTurn; // Increment motor position
    }  
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void EncoderMotPosCal2(ENCODER *pEnc,ENCODER *pEnc2)
{
    BOOL   AbsPosReCal = FALSE; 
    INT32  Tmp = 0;
    INT32  DeltaSingleTurn = 0;
    INT32   DeltaAccTurn = 0;
    static INT32 waitcnt = 0;
    if( (pEnc->V.EncConnect == TRUE) && (pEnc->V.AbsPosEncConnect == FALSE))
    {
        AbsPosReCal = TRUE;
        pEnc->V.AbsPosEncConnect = pEnc->V.EncConnect;  
    }
    else
    {
        AbsPosReCal = FALSE;
    }
    
    if(pEnc->V.EncConnect == TRUE) 
    {
      if(waitcnt<10)
      {
          waitcnt++;
          pEnc->V.EncIncAbnormFlag = FALSE;
      }
      else
      {
                 // add by lly 20131122 add Abnormal position increment detect 
          Tmp = pEnc->V.SfSingleTurn << pEnc->P.EncSftBitNum;
      //    DeltaSingleTurn = (Tmp - pEnc->V.LastSingleTurnReal) >> pEnc->P.EncSftBitNum;
          DeltaSingleTurn = (Tmp - pEnc->V.LastSingleTurn) >> pEnc->P.EncSftBitNum;
          pEnc->V.DeltaAccTurn = DeltaSingleTurn - pEnc->V.DeltaSingleTurn;
          
          if(MlibABS(pEnc->V.DeltaAccTurn) > pEnc->P.EncMaxPluseInc)  //  Abnormal position increment
          {
              pEnc->V.EncIncAbnormFlag = TRUE;
          }
          else
          {
              pEnc->V.EncIncAbnormFlag = FALSE;
          }
          
          // add by lly 20131122 add Abnormal position increment detect 
          Tmp = pEnc2->V.SfSingleTurn << pEnc2->P.EncSftBitNum;
          DeltaSingleTurn = (Tmp - pEnc2->V.LastSingleTurn) >> pEnc2->P.EncSftBitNum;
          pEnc2->V.DeltaAccTurn = DeltaSingleTurn - pEnc2->V.DeltaSingleTurn;
          
          if(MlibABS(DeltaAccTurn) > pEnc->P.EncMaxPluseInc)  //  Abnormal position increment
          {
              pEnc2->V.EncIncAbnormFlag = TRUE;
          }
          else
          {
              pEnc2->V.EncIncAbnormFlag = FALSE;
          }   
      }
      
  
    }

    if(pEnc->P.EncIacEnable == FALSE)
    {
        pEnc->V.EncIncAbnormFlag = FALSE;        
    }
    pEnc2->V.EncIncAbnormFlag = FALSE;


    if(FALSE == pEnc->V.bDataLost)
    {
        if(pEnc2->V.EncIncAbnormFlag == FALSE)
        {
            Tmp = pEnc2->V.SfSingleTurn << pEnc2->P.EncSftBitNum;
            pEnc2->V.DeltaSingleTurn = (Tmp - pEnc2->V.LastSingleTurn) >> pEnc2->P.EncSftBitNum;
            pEnc2->V.LastSingleTurn = Tmp;
        }
        else
        {
            Tmp = (pEnc2->V.SfSingleTurn << pEnc2->P.EncSftBitNum) + (pEnc2->V.DeltaSingleTurn << pEnc2->P.EncSftBitNum);
            pEnc2->V.SfSingleTurn = (Tmp)>>pEnc2->P.EncSftBitNum;
            pEnc2->V.DeltaSingleTurn = pEnc2->V.DeltaSingleTurn;
            pEnc2->V.LastSingleTurn = pEnc2->V.SfSingleTurn << pEnc2->P.EncSftBitNum;
        }

        if(pEnc->V.EncIncAbnormFlag == FALSE)
        {
            Tmp = pEnc->V.SfSingleTurn << pEnc->P.EncSftBitNum;
            pEnc->V.DeltaSingleTurn = (Tmp - pEnc->V.LastSingleTurn) >> pEnc->P.EncSftBitNum;
            pEnc->V.LastSingleTurn = Tmp;	
        }
        else
        {
            Tmp =  pEnc->V.LastSingleTurn + (pEnc->V.DeltaSingleTurn << pEnc->P.EncSftBitNum);
            pEnc->V.SfSingleTurn = (Tmp)>>pEnc->P.EncSftBitNum;
            pEnc->V.DeltaSingleTurn = pEnc->V.DeltaSingleTurn;
            pEnc->V.LastSingleTurn = pEnc->V.SfSingleTurn << pEnc->P.EncSftBitNum;	
        }
        pEnc->V.LastSingleTurnReal= (pEnc->V.SingleTurn >> pEnc->P.EncUserBitSwNum) << pEnc->P.EncSftBitNum;
    }
    else
    {
        Tmp = (pEnc2->V.SfSingleTurn << pEnc2->P.EncSftBitNum) + (pEnc2->V.DeltaSingleTurn << pEnc2->P.EncSftBitNum);
        
        pEnc2->V.SfSingleTurn = (Tmp)>>pEnc2->P.EncSftBitNum;
        pEnc2->V.DeltaSingleTurn = pEnc2->V.DeltaSingleTurn;
        pEnc2->V.LastSingleTurn = pEnc2->V.SfSingleTurn << pEnc2->P.EncSftBitNum;
        
        
        Tmp = (pEnc->V.SfSingleTurn << pEnc->P.EncSftBitNum) + (pEnc->V.DeltaSingleTurn << pEnc->P.EncSftBitNum);
        pEnc->V.SfSingleTurn = (Tmp)>>pEnc->P.EncSftBitNum;
        pEnc->V.DeltaSingleTurn = pEnc->V.DeltaSingleTurn;
        pEnc->V.LastSingleTurn = pEnc->V.SfSingleTurn << pEnc->P.EncSftBitNum;	                
    }


        
    if( AbsPosReCal || pEnc2->V.EncZeroOffFlag)
    {
        pEnc2->V.MotPos = pEnc2->V.SfSingleTurn + pEnc2->P.EncZeroOffset;
        if(pEnc2->V.MotPos < -pEnc2->P.EncPPR/2)
        {
            pEnc2->V.MotPos = pEnc2->V.MotPos + pEnc2->P.EncPPR;
        }
        else if(pEnc2->V.MotPos > pEnc2->P.EncPPR/2)
        {
            pEnc2->V.MotPos = pEnc2->V.MotPos - pEnc2->P.EncPPR;
        }
        pEnc->V.MotPos =  pEnc->V.SfSingleTurn; 
        pEnc2->V.EncZeroOffFlag  = 0;         
    }
    else
    {
        pEnc2->V.MotPos += pEnc2->V.DeltaSingleTurn;
        pEnc->V.MotPos += pEnc->V.DeltaSingleTurn;
    }  

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
UINT32 Bissc_data[2] = {0};
UINT64 Bissc_data64 = 0;
#pragma optimize=none
PUBLIC void EncoderCalExec(ENCODER *pEnc,ENCODER *pEnc2, ALARM *AlmMngr, UINT16 AxisID, BOOL ServoOn, BOOL UWVSeq,UINT16 MotorType)
{
    INT32  Tmp, Tmp2, wk;
    INT32  PulseInc;
    INT32  MechAngleOffset;


    UINT32 fw,fw2,fw3;   
	UINT16 shiftleft =0;    
    UINT64 temp64 = 0;
    UINT16 CRC_Read = 0;
    UINT16 CRC_Cal = 0;
    
    #if NO_ENCINCREMENT
    if(pEnc->P.EncType == ENC_TYPE_INCREMENT)
    {
        Tmp = pEnc->V.AbzPulse << pEnc->P.EncSftBitNum;
        PulseInc = (Tmp - pEnc->V.LastAbzPulse)>>pEnc->P.EncSftBitNum;       
        pEnc->V.LastAbzPulse = Tmp;

        Tmp2 = pEnc->V.MechAngle;
        Tmp2 += PulseInc;

        Tmp2 %= pEnc->P.EncPPR;
        if(Tmp2 < 0)
        {
            Tmp2 += pEnc->P.EncPPR;
        }
           
        pEnc->V.MechAngle = (UINT32)Tmp2;
        pEnc->V.MotPos += PulseInc;
    }
    else 
    #endif
    if(pEnc->P.EncType == ENC_TYPE_BISSC)
    {  
      if(pEnc->V.EncConnect)
      {       
          CRC_Read = hApi_Bissc_PosDecoding(pEnc,pEnc2);

          if(CRC_Read == 1)
          {
              pEnc->V.bDataLost = FALSE;        
              if(pEnc->V.ComErrCnt > 0)
					pEnc->V.ComErrCnt--;    
              pEnc->V.SfSingleTurn = (pEnc->V.SingleTurn >> pEnc->P.EncUserBitSwNum);
              if(pEnc->V.AbsAlmCode & 0x01)
              {
    //					ALMSetServoAlarm( AlmMngr, ALM_ENCOS );	
              }
              
              if(pEnc->V.AbsAlmCode & 0x02)
              {
    //					ALMSetServoAlarm( AlmMngr, ALM_ENCCE );	
              }             
          }
          else
          {
              if(pEnc->V.ComErrCnt >= ENC_COM_ERR_LMT)
              {
                  pEnc->V.bDataLost = FALSE;
                  pEnc->V.ComErrCnt = ENC_COM_ERR_LMT;
                  ALMSetServoAlarm( AlmMngr, ALM_ENCCRC );	
              }
              else if(ServoOn)
              {
                  pEnc->V.bDataLost = TRUE;
                  pEnc->V.ComErrCnt += 3;
              }
              else
              {
                  pEnc->V.bDataLost = TRUE;
                  pEnc->V.ComErrCnt ++;
              }            
          }  
      }      
      EncoderMotPosCal(pEnc,pEnc2);

        if(pEnc->V.EncIncAbnormCnt >= ENC_COM_ERR_LMT)
        {
            pEnc->V.EncIncAbnormCnt = ENC_COM_ERR_LMT;
            ALMSetServoAlarm( AlmMngr, ALM_ENCDATA);
        }

        if(pEnc->V.EncIncAbnormFlag == TRUE)
        {
            pEnc->V.EncIncAbnormCnt++;
        }
        else if(pEnc->V.EncIncAbnormCnt > 0)
        {
            pEnc->V.EncIncAbnormCnt --;
        }
    }
    else if(pEnc->P.EncType == ENC_TYPE_2BISSC)
    {  
        if(pEnc->V.EncConnect)
        {                                             

          CRC_Read = hApi_2Bissc_PosDecoding(pEnc,pEnc2);

          pEnc->V.SfSingleTurn = (pEnc->V.SingleTurn >> pEnc->P.EncUserBitSwNum);
          pEnc2->V.SfSingleTurn = (pEnc2->V.SingleTurn >> pEnc2->P.EncUserBitSwNum);

          if(CRC_Read == 1)
          {
              pEnc->V.bDataLost = FALSE;
              
              if(pEnc->V.ComErrCnt > 0)
                      pEnc->V.ComErrCnt--;  
                                       
              if(pEnc->V.AbsAlmCode & 0x01)
              {
    //					ALMSetServoAlarm( AlmMngr, ALM_ENCOS );	
              }
              
              if(pEnc->V.AbsAlmCode & 0x02)
              {
    //					ALMSetServoAlarm( AlmMngr, ALM_ENCCE );	
              }             
          }
          else
          {
              if(pEnc->V.ComErrCnt >= ENC_COM_ERR_LMT)
              {
                  pEnc->V.bDataLost = FALSE;
                  pEnc->V.ComErrCnt = ENC_COM_ERR_LMT;
                  ALMSetServoAlarm( AlmMngr, ALM_ENCCRC );	
              }
              else if(ServoOn)
              {
                  pEnc->V.bDataLost = TRUE;
                  pEnc->V.ComErrCnt += 3;
              }
              else
              {
                  pEnc->V.bDataLost = TRUE;
                  pEnc->V.ComErrCnt ++;
              }               
          }    
      }
      else
      {
          if(pEnc->V.ComErrCnt >= ENC_COM_ERR_LMT)
          {
              pEnc->V.bDataLost = FALSE;
              pEnc->V.ComErrCnt = ENC_COM_ERR_LMT;
              ALMSetServoAlarm( AlmMngr, ALM_ENCDATA );	
          }
          else if(ServoOn)
          {
              pEnc->V.bDataLost = TRUE;
              pEnc->V.ComErrCnt += 3;
          }
          else
          {
              pEnc->V.bDataLost = TRUE;
              pEnc->V.ComErrCnt ++;
          }               
      }

      EncoderMotPosCal2(pEnc,pEnc2);    


        if(pEnc->V.EncIncAbnormCnt >= ENC_COM_ERR_LMT)
        {
            pEnc->V.EncIncAbnormCnt = ENC_COM_ERR_LMT;
            ALMSetServoAlarm( AlmMngr, ALM_ENCDATA);
        }

        if(pEnc->V.EncIncAbnormFlag == TRUE || pEnc2->V.EncIncAbnormFlag == TRUE)
        {

            pEnc->V.EncIncAbnormCnt ++;
        } 
        else if(pEnc->V.EncIncAbnormCnt > 0)
        {
            pEnc->V.EncIncAbnormCnt --;
        }
    }
    else if(pEnc->P.EncType == ENC_TYPE_KTM)
    {
        if(pEnc->V.EncConnect)
        {       

          CRC_Read = hApi_KTM59_PosDecoding(pEnc,pEnc2);  
          
          pEnc->V.MultiTurn = 0;
  
          if(CRC_Read == 1)
          {
              pEnc->V.bDataLost = FALSE;
              
              pEnc->V.SfSingleTurn = (pEnc->V.SingleTurn >> pEnc->P.EncUserBitSwNum);
              
              if(pEnc->V.ComErrCnt > 0)
                  pEnc->V.ComErrCnt--;    
              if(pEnc->V.AbsAlmCode & 0x01)
              {
                  ALMSetServoAlarm( AlmMngr, ALM_ENCOS );	
              }
  
              if(pEnc->V.AbsAlmCode & 0x02)
              {
                  ALMSetServoAlarm( AlmMngr, ALM_ENCCE );	
              }    
          }
          else
          {
              pEnc->V.LostLink++;
              if(pEnc->V.ComErrCnt >= ENC_COM_ERR_LMT)
              {
                  pEnc->V.bDataLost = FALSE;
                  pEnc->V.ComErrCnt = ENC_COM_ERR_LMT;
                  ALMSetServoAlarm( AlmMngr, ALM_ENCCRC );	
              }
              else if(ServoOn)
              {
                  pEnc->V.bDataLost = TRUE;
                  pEnc->V.ComErrCnt += 3;
              }
              else
              {
                  pEnc->V.bDataLost = TRUE;
                  pEnc->V.ComErrCnt ++;
              }   
          }
        }
        EncoderMotPosCal(pEnc,pEnc2);
    }
    else if(pEnc->P.EncType == ENC_TYPE_MA600)
    {
        if(pEnc->V.EncConnect)
        {       
         CRC_Read =  hApi_MA600_PosDecoding(pEnc,pEnc2);
          
          if(CRC_Read)
          {
              pEnc->V.bDataLost = FALSE;
              
              pEnc->V.SfSingleTurn = (pEnc->V.SingleTurn >> pEnc->P.EncUserBitSwNum);
              
              if(pEnc->V.ComErrCnt > 0)
                  pEnc->V.ComErrCnt--;    
              if(pEnc->V.AbsAlmCode & 0x01)
              {
                  ALMSetServoAlarm( AlmMngr, ALM_ENCOS );	
              }
  
              if(pEnc->V.AbsAlmCode & 0x02)
              {
                  ALMSetServoAlarm( AlmMngr, ALM_ENCCE );	
              }    
          }
          else
          {
              pEnc->V.LostLink++;
              if(pEnc->V.ComErrCnt >= ENC_COM_ERR_LMT)
              {
                  pEnc->V.bDataLost = FALSE;
                  pEnc->V.ComErrCnt = ENC_COM_ERR_LMT;
                  ALMSetServoAlarm( AlmMngr, ALM_ENCCRC );	
              }
              else if(ServoOn)
              {
                  pEnc->V.bDataLost = TRUE;
                  pEnc->V.ComErrCnt += 3;
              }
              else
              {
                  pEnc->V.bDataLost = TRUE;
                  pEnc->V.ComErrCnt ++;
              }   
          }
        }
        EncoderMotPosCal(pEnc,pEnc2);
    }

    #if NO_ENCINCREMENT
    if(pEnc->P.EncType == ENC_TYPE_INCREMENT)
    {
        Tmp = (pEnc->V.MechAngle * pEnc->P.PolePairs) % pEnc->P.EncPPR;
        pEnc->V.ElecAngle = (((Tmp << 8) / pEnc->P.EncPPR)<<7) << 1;     
        pEnc->V.SingleTurn  =  pEnc->V.AbzPulse;
    }
    else
    #endif
    {    
        {        
//            pEnc->V.MechAngle = (((UINT32)Tmp )>>16);   
            Tmp = pEnc->V.SingleTurn << pEnc->P.EncSftBitNum;
            pEnc->V.MechAngle = (((UINT32)Tmp)>>16);       
            pEnc->P.PhaseOffset = pEnc->P.MechAngleOffset;
            Tmp = pEnc->P.PolePairs * pEnc->V.MechAngle;
            pEnc->V.ElecAngle = (UINT16)Tmp - pEnc->P.PhaseOffset;
            
        }                
    }
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MechAngleCalibration(ENCODER *pEnc, REAL32 angle)
{
    REAL32   tmp;

//	tmp = (REAL32)pEnc->P.EncPPR/(REAL32)pEnc->P.PolePairs;
	tmp = 65536 * angle/360;

	if(pEnc->P.EncType == ENC_TYPE_INCREMENT)
	{
		pEnc->V.MechAngle = tmp/(REAL32)pEnc->P.PolePairs;
		pEnc->P.PhaseOffset= 0;
	}
	else
	{
		pEnc->P.PhaseOffset = tmp;	
	}
}


/****************************************************************************************************
 * DESCRIPTION:
 *        Encoder module Round Main Procedure
 * RETURNS:
 *
****************************************************************************************************/
uint8_t EncCode[4] = {0,0,0,0};

PUBLIC void RpiEncExCmdRound(BASE_LOOP *BaseLoops, UINT16 ExCmd, UINT16 AxisID)
{
    UINT16 i = 0;
    UINT8 temp = 0;
    ENCODER *pEnc = BaseLoops->Enc;
    pEnc->V.ExCmdEn = TRUE;
    UINT8  ret = 0;
   if(pEnc->P.EncType == ENC_TYPE_KTM)  
   {
        if(ExCmd ==  1)
        {
            hApi_KTM59_Factory_Init();
        }     
        else if(ExCmd == 2)
        {
            ret = hApi_KTM59_Factory_AutoCalib();
            if(ret == 1)
            {
              ALMSetServoAlarm( BaseLoops->AlmMngr, ALM_ENCOF);	
            }
            else if(ret == 2)
            {
              ALMSetServoAlarm( BaseLoops->AlmMngr, ALM_RSVD22);	
            }   
            else if(ret == 3)
            {
              ALMSetServoAlarm( BaseLoops->AlmMngr, ALM_RSVD23);	
            }
        }
        else if(ExCmd == 3)
        {
            ret = hApi_KTM59_Factory_ManualCalib();
            if(ret == 1)
            {
              ALMSetServoAlarm( BaseLoops->AlmMngr, ALM_ENCOF);	
            }  
        }
        else if(ExCmd == 4)
        {
            hApi_ReIniKtm59();            
        }
        else if(ExCmd == 5)
        {
            EncCode[0] =  KTM59_ReadReg(0x129);
            EncCode[1] =  KTM59_ReadReg(0x12A);        
        }
   }
    pEnc->V.ExCmdEn = FALSE;
}


