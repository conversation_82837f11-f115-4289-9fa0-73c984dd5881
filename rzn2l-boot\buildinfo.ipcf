<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<iarProjectConnection version="1.8" name="Flex Software">
    <device>
        <name>R9A07G084M04</name>
    </device>
    <includePath>
        <path>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</path>
        <path>$PROJ_DIR$/rzn/fsp/inc</path>
        <path>$PROJ_DIR$/rzn/fsp/inc/api</path>
        <path>$PROJ_DIR$/rzn/fsp/inc/instances</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</path>
        <path>$PROJ_DIR$/rzn_cfg/driver</path>
        <path>$PROJ_DIR$/rzn_cfg/fsp_cfg</path>
        <path>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</path>
        <path>$PROJ_DIR$/rzn_gen</path>
        <path>$PROJ_DIR$/src</path>
    </includePath>
    <defines>
        <define>_RZN_CORE=CR52_0</define>
    </defines>
    <asmIncludePath>
        <path>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</path>
        <path>$PROJ_DIR$/rzn/fsp/inc</path>
        <path>$PROJ_DIR$/rzn/fsp/inc/api</path>
        <path>$PROJ_DIR$/rzn/fsp/inc/instances</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</path>
        <path>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</path>
        <path>$PROJ_DIR$/rzn_cfg/driver</path>
        <path>$PROJ_DIR$/rzn_cfg/fsp_cfg</path>
        <path>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</path>
        <path>$PROJ_DIR$/rzn_gen</path>
        <path>$PROJ_DIR$/src</path>
    </asmIncludePath>
    <asmDefines>
        <define>_RZN_CORE=CR52_0</define>
    </asmDefines>
    <linkerFile>
        <override>true</override>
        <path>$PROJ_DIR$/script/fsp_xspi1_boot.icf</path>
    </linkerFile>
    <linkerExtraOptions>
        <arg>--config_search "$PROJ_DIR$"</arg>
    </linkerExtraOptions>
    <customArgVars>
        <group name="RA Smart Configurator">
            <argVar>
                <name>RASC_EXE_PATH</name>
                <value>C:\Renesas\rzn\sc_v2023-07_fsp_v1.3.0\eclipse\rasc.exe</value>
            </argVar>
        </group>
    </customArgVars>
    <files>
        <group name="Components">
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_compiler.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_cp15.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_gcc.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_iccarm.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_version.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/core_cr52.h</path>
            <path>rzn/arm/CMSIS_5/LICENSE.txt</path>
            <path>rzn/board/custom/board.h</path>
            <path>rzn/fsp/inc/api/bsp_api.h</path>
            <path>rzn/fsp/inc/api/r_ioport_api.h</path>
            <path>rzn/fsp/inc/api/r_spi_flash_api.h</path>
            <path>rzn/fsp/inc/api/r_transfer_api.h</path>
            <path>rzn/fsp/inc/api/r_usb_basic_api.h</path>
            <path>rzn/fsp/inc/api/r_usb_pcdc_api.h</path>
            <path>rzn/fsp/inc/fsp_common_api.h</path>
            <path>rzn/fsp/inc/fsp_features.h</path>
            <path>rzn/fsp/inc/fsp_version.h</path>
            <path>rzn/fsp/inc/instances/r_ioport.h</path>
            <path>rzn/fsp/inc/instances/r_usb_basic.h</path>
            <path>rzn/fsp/inc/instances/r_xspi_qspi.h</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Include/R9A07G084.h</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Include/renesas.h</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Include/system.h</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/SVD/RA.svd</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_clocks.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_clocks.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_common.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_common.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_compiler_support.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_delay.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_delay.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_io.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_io.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_irq.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_irq.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_mcu_api.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_module_stop.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_register_protection.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_register_protection.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_reset.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_reset.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_sbrk.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_tfu.h</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_cache.c</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_cache.h</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_elc.h</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_feature.h</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_loader_param.c</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_mcu_info.h</path>
            <path>rzn/fsp/src/r_ioport/r_ioport.c</path>
            <path>rzn/fsp/src/r_usb_basic/r_usb_basic.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_basic_config.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_basic_define.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_basic_if.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_basic_local.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_cstd_rtos.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_extern.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_hehci_def_usr.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_hehci_extern.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_hehci_typedef.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_hhci.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_hhci_local.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_hohci_def_usr.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_hohci_extern.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_hohci_typedef.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_rtos_config.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/inc/r_usb_typedef.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_cdataio.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_clibusbip.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_cstd_rtos.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hbc.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hcontrolrw.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hdriver.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hdriver_api_rz.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hdriver_rz.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hehci_main.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hehci_memory.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hehci_transfer.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hhci.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hhubsys.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hintfifo.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hinthandler_rz.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hinthandler_usbip0.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hinthandler_usbip1.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hlibusbip.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hmanager.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hmanager_rz.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hohci_main.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hohci_memory.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hohci_transfer.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hscheduler.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hscheduler_rz.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hsignal.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_hstdfunction.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_pbc.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_pcontrolrw.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_pdriver.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_pintfifo.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_pinthandler_usbip0.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_plibusbip.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_psignal.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_pstdfunction.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/driver/r_usb_pstdrequest.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/inc/r_usb_bitdefine.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/inc/r_usb_dmac.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/inc/r_usb_dmaca_rz_config.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/inc/r_usb_dmaca_rz_config_reference.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/inc/r_usb_dmaca_rz_if.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/inc/r_usb_dmaca_rz_private.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/inc/r_usb_dmaca_rz_target.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/inc/r_usb_reg_access.h</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_creg_abs.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_creg_access.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_dma.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_dmaca_rz.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_dmaca_rz_target.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_dma_rz.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_hostelectrical.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_hreg_abs.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_hreg_access.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_mcu.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_preg_abs.c</path>
            <path>rzn/fsp/src/r_usb_basic/src/hw/r_usb_preg_access.c</path>
            <path>rzn/fsp/src/r_usb_pcdc/r_usb_pcdc_descriptor.c.template</path>
            <path>rzn/fsp/src/r_usb_pcdc/src/inc/r_usb_pcdc.h</path>
            <path>rzn/fsp/src/r_usb_pcdc/src/r_usb_pcdc_driver.c</path>
            <path>rzn/fsp/src/r_xspi_qspi/r_xspi_qspi.c</path>
        </group>
        <group name="Build Configuration">
            <path>rzn_cfg/driver/r_xspi_qspi_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/board_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_mcu_device_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_mcu_device_pn_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_mcu_family_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_pin_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_ioport_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_usb_basic_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_usb_class_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_usb_pcdc_cfg.h</path>
        </group>
        <group name="Generated Data">
            <path>rzn_gen/bsp_clock_cfg.h</path>
            <path>rzn_gen/common_data.c</path>
            <path>rzn_gen/common_data.h</path>
            <path>rzn_gen/hal_data.c</path>
            <path>rzn_gen/hal_data.h</path>
            <path>rzn_gen/main.c</path>
            <path>rzn_gen/pin_data.c</path>
            <path>rzn_gen/vector_data.c</path>
            <path>rzn_gen/vector_data.h</path>
        </group>
        <group name="Program Entry">
            <path>src/hal_entry.c</path>
        </group>
    </files>
</iarProjectConnection>
