/****************************************************************************************************
 *
 * FILE NAME:  BaseSetting.h
 *
 * DESCRIPTION:  
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/

#ifndef _BASE_SETTING_H
#define	_BASE_SETTING_H


/*****************************************************************************************************
   peripherals input clocks frequence[Mhz]
****************************************************************************************************/
#define  SYSTEM_CPU_CLK    800
#define  PWM_MTU3_PCLKH    200   // mtu3 gpt tfu dsmif poe3 poeg adc0 xsip  Srma
#define  CMT_CMTW_PLCKL     50   // adc1 iic doc cmt cmtw wdt rtc tsu 
#define  SCI_SPI_PLCKM     100   // crc sci gpt_ns canfd ethsw ship opt esc usb gpio clam



#define  USE_DB      FALSE

#define  MAX_AXIS_NUM                1

#define  AXIS_1                      0
#define  AXIS_2                      1

#define  UartCOMCtrl                 0
#define  BUSTpyeCtrl                 1

#define  PWM_CLOCK                   (200.0f)                          // 200Mhz 
#define  DELTA_SIGMA_CLOCK           (20.0f)                           // 20 Mhz
#define  MTU3_CLOCK                  (PWM_CLOCK)                       // 200Mhz  
#define  MIN_TIME_FOR_CNT            (1.0f)                            // 5ns:1,200Mhz      


#define  PWM_PERIOD_VALUE            (6250U)                           // PWM Period Value:125us ,12500U,200Mhz,  PWM Period Value:62.5us，6250U，200Mhz 
#define  DEAD_TIME_FOR_TIM           (300U)                            // 1.5us:300U,200Mhz    0.5us:100U,200Mhz
#define  HALF_DEAD_TIME_FOR_TIM      (DEAD_TIME_FOR_TIM >> 1)          // half dead time  

#define  PWM_DUTY_MAX                (100)
#define  PWM_DEAD_TIME_MAX           (1000)  //  5us:1000U,200Mhz
#define  PWM_DEAD_TIME_MIN           (100)   //  0.5us:100U,200Mhz



#define  MTU0_PRD_CNT                PWM_PERIOD_VALUE               

//#define  MTU0_CMP_CAP_CNT            (960+13)  // 3*64/DELTA_SIGMA_CLOCK = 9.6us 9.6/2 *MTU3_CLOCK = 960  ideal trigger time
#define  MTU0_CMP_CAP_CNT            (1000)    // 1000/200 = 5us
#define  MTU0_CMP_INT_CNT            (PWM_PERIOD_VALUE-960)

                                    
#define  MTU3_PRD_CNT                PWM_PERIOD_VALUE
#define  MTU3_DEAD_CNT               DEAD_TIME_FOR_TIM


#define  HALF_ADC_VALUE              (25600.0f)                          // linear  

#define  MAX_SMP_CUR_13A             (12.5f)                             // max sample current 13A(20 mohm) PEAK 
#define  MAX_SMP_CUR_25A             (25.0f)                             // max sample current 25A(10 mohm) PEAK
#define  MAX_SMP_CUR_50A             (50.0f)                             // max sample current 50A(5 mohm)  PEAK
#define  MAX_SMP_CUR_100A            (100.0f)                            // max sample current 100A(2.5 mohm) PEAK
#define  MAX_SMP_CUR_250A            (250.0f)                            // max sample current 250A(1 mohm) PEAK
#define  IUVW_GAIN_13A	             (MAX_SMP_CUR_13A/HALF_ADC_VALUE)    // (+/-)25.0A/25600.0f
#define  IUVW_GAIN_25A	             (MAX_SMP_CUR_25A/HALF_ADC_VALUE)    // (+/-)25.0A/25600.0f
#define  IUVW_GAIN_50A		         (MAX_SMP_CUR_50A/HALF_ADC_VALUE)    // (+/-)50.0A/25600.0f
#define  IUVW_GAIN_100A		         (MAX_SMP_CUR_100A/HALF_ADC_VALUE)    // (+/-)50.0A/25600.0f
#define  IUVW_GAIN_250A		         (MAX_SMP_CUR_250A/HALF_ADC_VALUE)    // (+/-)250.0A/25600.0f

#define  RATED_CUR_3A                (2.5f)                             // device rated current 2.9A(20 mohm)  RSM 3 times overload 
#define  RATED_CUR_5A               (5.0f)                             // device rated current 5.9A(10 mohm)  RSM 3 times overload
#define  RATED_CUR_10A               (10.0f)                            // device rated current 14.0A(5 mohm)  RSM 2.5 times overload
#define  RATED_CUR_20A              (20.0f)                            // device rated current 28.0A(2.5 mohm) RSM 2.5 times overload
#define  RATED_CUR_50A              (50.0f)                            // device rated current 70.0A(1 mohm)   RSM 2.5 times overload

#define  PEAK_CUR_13A               (MAX_SMP_CUR_13A/1.414)            // device rated current 2.9A(20 mohm)  RSM 3 times overload 
#define  PEAK_CUR_25A               (MAX_SMP_CUR_25A/1.414)            // device rated current 5.9A(10 mohm)  RSM 3 times overload
#define  PEAK_CUR_50A               (MAX_SMP_CUR_50A/1.414)            // device rated current 14.0A(5 mohm)  RSM 2.5 times overload
#define  PEAK_CUR_100A              (MAX_SMP_CUR_100A/1.414)           // device rated current 28.0A(2.5 mohm) RSM 2.5 times overload
#define  PEAK_CUR_250A              (MAX_SMP_CUR_250A/1.414)           // device rated current 70.0A(1 mohm)   RSM 2.5 times overload
                                   
#define  DEV_POWER_150W             (15.0f)                             // max sample current 13A(20 mohm)
#define  DEV_POWER_300W             (30.0f)                             // max sample current 25A(10 mohm)
#define  DEV_POWER_750W             (50.0f)                             // max sample current 50A(5 mohm)
#define  DEV_POWER_1500W            (100.0f)                            // max sample current 100A(2.5 mohm)
#define  DEV_POWER_3000W            (300.0f)                            // max sample current 250A(1 mohm)


#define  TASKA_MAX_TIME              (273)                               // 62.5us  * 70%    TIMEER: 50/8 Mhz
#define  TASKB_MAX_TIME              (1172)                              // 250.00us * 75%   TIMEER: 50/8 Mhz
#define  TASKC_MAX_TIME              (3125)                              // 50%   TIMEER: 50/8 Mhz

#define  TASKB_CYCLENS               (125000)
#define  PS_CYCLENS                  (125000)                            // pos & spd loop cycle[ns]
#define  CUR_CYCLENS             	 (32500)                             // pwm & cur loop cycle[ns]
#define  TASKA_CYCLENS             	 (32500)

#define  KPI_TACOUNTMS             	 (1000000/TASKA_CYCLENS)		     // taskA Cycle Count per 1ms 
#define  KPI_TBCOUNTMS             	 (1000000/TASKB_CYCLENS)			 // taskB Cycle Count per 1ms 

#define  TASKB_CYCLEUS           	 125
#define  PS_CYCLEUS              	 125                                 // pos & spd loop cycle[us]
#define  CUR_CYCLEUS             	 32                                  // pwm & cur loop cycle[us]
#define  CUR_FRQ                	 (32000)                             // pwm & cur loop frequency[Hz]


#define  TASKC_CYCLEMS			     1
#define  TASKC_CYCLEUS           	 1000

#define  ENC_FRQ                     16000
#define  TASKB_FRQ               	 8000                                // taskB frequency[Hz]

//#define  UDC_LEVEL               	 48.0f                             // Udc level  todo todo todo
//#define  Uab_LEVEL               	 (UDC_LEVEL/C_FSqrt3)

#define  UDC_LEVEL               	 48.0f                             // Udc level  todo todo todo
#define  Uab_LEVEL               	 (UDC_LEVEL/C_FSqrt3)

#define  HIGH_VOLTAGE                0

#define  MaxAnalogVdc                12.6f
#define  AnalogVdc                   10.0f



#define  HW_WVU                      FALSE                               // Hardware WVU sequence

#define  REGENE_USE                  1



/*--------------------------------------------------------------------------------------------------*/
/*		Amp and Mot Type Definition						        								    */
/*--------------------------------------------------------------------------------------------------*/

#define  MOTTYPE_ROTARY				0x00			 // Rotary motor type
#define  MOTTYPE_LINEAR				0x01			 // Linear motor type
#define  MOTTYPE_VOICECOIL		        0x02			 // VoiceCoil motor type


//#define  PWM_FRQ               (SYSTEM_FRQ/PWM_PERIOD_VALUE/2)
//#define  CURRENT_FRQ           PWM_FRQ
//#define  SPEED_FRQ             CURRENT_FRQ/5
//#define  POSITION_FRQ          SPEED_FRQ


//#define  CURRENT_PRD           (1.0f/(float)CURRENT_FRQ)
//#define  SPEED_PRD             (5.0f*CURRENT_PRD)
//#define  POSITION_PRD          SPEED_PRD
#define   WARN_UPDATE_USE        FALSE

#define ECAT_INFECNT   0
#define ECAT_RXFECNT   1
#define ECAT_FWDRFECNT 2
#define ECAT_PROCECNT  3
#define ECAT_PIDECNT   4
#define ECAT_LSOTECNT  5
#define ECAT_DLCONTROL 6
#define ECAT_DLSTATUS  7

#endif  /* _BASE_SETTING_H */


