/****************************************************************************************************
 *
 * FILE NAME:  MpfHandler.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-08-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef MPF_HANDLER_H
#define MPF_HANDLER_H

#include "BaseDef.h"

/****************************************************************************************************/
/*		STRUCT DEFINITION																			*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		State management variable definition for magnetic pole detection							*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{ 			
	BOOL	HaltCondition;					/* Over Travel or not Servo Ready State 				*/
	BOOL	MotStopSts; 					/* Motor stopped state									*/
	INT32	dMotPosition;					/* Position FB increment value			[pulse/TaskB]	*/
	INT32	TrefMonitor;					/* Torque command output (average value) [2^24/MaxTrq]	*/
	INT32	SpdFbMonitor;					/* Speed FB input					 [2^24/OvrSpd]		*/
	INT32	KVfbFil;						/* Speed FB filter gain									*/
	INT32	Kphasepos;						/* Gain for Motor Phase Position Cal.					*/
	INT32	Kmovpos;						/* Gain for Linear Motor Move Position Cal. 			*/
} MPFEXEINFO;


/*--------------------------------------------------------------------------------------------------*/
/*		Variable definition for magnetic pole detection 											*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{ 				
	struct	
	{						
		INT32	Kv; 							/* Velocity loop gain 			[0.1Hz] 			*/
		INT32	Ki; 							/* Velocity loop integral gain	[0.01ms]			*/
		INT32	InteglessGain;					/* Integral less gain			[%] 				*/
		INT32	Klpf;							/* First-order lag filter gain						*/
		INT32	MaxSpd; 						/* Speed command peak value 		[2^24/OvrSpd]	*/
		INT32	dSpd;							/* Speed command difference value	[2^24/OvrSpd/Cycle] */
		INT32	ReverseSpd; 					/* Reverse run level (at speed command) [2^24/OvrSpd] */
		INT32	ReverseSpd2;					/* Reverse run level (during waiting time) [2^24/OvrSpd] */
		INT32	OverSpd;						/* Overspeed detection value		[2^24/OvrSpd]	*/
		INT32	DetectRange;					/* Range of movement 				[0.01mm]		*/
		INT32	MaxTrq; 						/* Thrust command peak value 		[2^24/MaxTrq]	*/
		INT32	dTrq;							/* Thrust command difference value	[2^24/MaxTrq/Cycle] */
		INT32	ErrOKLevel; 					/* Detection error OK level			[Pluse] 		*/
		INT32	AccTime;						/* Acceleration and deceleration time[Times/Cycle]	*/
		INT32	ClmpTime;						/* Constant speed time				[Times/Cycle]	*/
		INT32	WaitTime;						/* Waiting time 					[Times/Cycle]	*/
		INT32	InteglessTime;					/* Start time without integration 	[Times/Cycle]	*/
		INT32	IncTime;						/* Thrust command adjustment time 	[Times/Cycle]	*/
		INT32	ConstTime;						/* Thrust finger constant time		[Times/Cycle]	*/
		INT32	RepeatNum;						/* Number of magnetic pole detection repetitions	*/
	} conf;

	struct	
	{							
		BOOL	Fail;							/* Error occurred (speed deviation or torque limit)	*/
		BOOL	Direction;						/* Driving command direction						*/
		BOOL	AxisChange; 					/* q-axis torque application flag					*/
		BOOL	ClrIntegral;					/* Clear integration buffer							*/
		BOOL	PhaseChange;					/* Phase change 									*/
		BOOL	ZeroTorque; 					/* Zero torque command output flag 					*/
		BOOL	RangeFix;						/* Detection area confirmed							*/
		BOOL	InteglessON;					/* 0: Disable integration-less, 1: Execute integration-less */
		BOOL	ReverseLevel;					/* Reverse running judgment level (0: speed command, 1: waiting time)*/
		BOOL	OSFail; 						/* Overspeed									*/
		BOOL	PhaseConfFlg;					/* Torque control request flag for magnetic pole detection result confirmation*/
		BOOL	NoGood; 						/* Angle calculation error information (0: OK, 1: increase gain)*/

		INT32	SpdErr; 						/* Speed deviation 									*/
		INT32	Ivar64[2];						/* Velocity loop integration buffer					*/
		INT32	dAxisMaxTrqPlus;				/* Forward direction d-axis maximum torque 			*/
		INT32	qAxisMaxTrqPlus;				/* Forward direction q-axis maximum torque 			*/
		INT32	dAxisMaxTrqMinus;				/* Negative direction d-axis maximum torque 		*/
		INT32	qAxisMaxTrqMinus;				/* Negative direction q-axis maximum torque 		*/
		INT32	dAxisMaxSpdPlus;				/* Forward direction d-axis maximum speed			*/
		INT32	qAxisMaxSpdPlus;				/* Forward direction q-axis maximum speed			*/
		INT32	dAxisMaxSpdMinus;				/* Negative direction d-axis maximum speed			*/
		INT32	qAxisMaxSpdMinus;				/* Negative direction q-axis maximum speed			*/
		INT32	AxisErr;						/* q-axis deviation									*/
		INT32	MovPos; 						/* Magnetic pole detection movement amount			*/
		INT32	TrqRefPhaConf;					/* Thrust command for magnetic pole detection confirmation*/
		INT32	MovPosPhaConf;					/* Position for magnetic pole detection confirmation */
		INT32	PhsOffsetComp;					/* Magnetic pole detection position correction amount */
		INT32	SpdOffset;						/* Speed before operation starts 					*/

		INT32	AdjSwpDeg;						/* Adjustment phase change step 					*/
		INT32	AdjDegree;						/* Adjustment phase 								*/
		INT32	PastTime;						/* Elapsed time after starting operation			*/
		INT32	RefOutTime; 					/* Time work buffer (only within speed command creation function)*/
		INT32	PhsOffsetCalRslt;				/* Magnetic pole detection position (theoretical value calculation result)*/
		INT32	PhaseOffsetCalComp; 			/* Correction amount after magnetic pole detection operation confirmation (for monitor)*/
		INT32	DegReverse[4];					/* Reverse running angle							*/
		INT32	DegRev; 						/* Composite angle during reverse running 			*/
		INT32	DegFinal;						/* Final composite angle							*/

		INT32	KvGain; 						/* Automatic gain adjustment parameter 				*/
		INT32	Step;							/* Magnetic pole detection sequence step			*/
		INT32	RefOutStep; 					/* Command output sequence step						*/
		INT32	RangeFixStep;					/* Magnetic pole area detection sequence step		*/
		INT32	Times;							/* Number of repetitions							*/
		INT32	FailTimes;						/* Number of failures 								*/
		INT32	AxisErrSign;					/* d-q Axis deviation direction (torque)			*/
		INT32	AreaCheckStep;					/* 8-division area check step						*/
		INT32	ReverseCtr; 					/* Number of reverse runs 							*/
		INT32	ConfirmStep;					/* Magnetic pole detection result confirmation step  */
		INT32	SpdFbFilo;						/* Speed FB filter output							*/
	} var;
	
	BOOL	MpfSeqStartReq; 					/* Magnetic pole detection sequence start request	*/
	INT32	MpFindSpdRef;						/* Magnetic pole detection speed command output		*/
	INT32	TrqFilOut;							/* Torque filter output								*/
	INT32	MpfPhaseOffset; 					/* Magnetic pole detection phase offset[65536/360deg] */
	INT32	MPJudgeMonitor[3];					/* Magnetic pole detection judgment monitor			*/
	INT32	UnMpFindingTime;					/* Magnetic pole detection time required[0.1s]		*/
	INT32	UnMpfMovePosP;						/* Maximum magnetic pole detection movement in the positive direction[0.01mm]*/
	INT32	UnMpfMovePosN;						/* Magnetic pole detection Reverse maximum movement amount[0.01mm]*/
} MPFIND;

	

#endif   /* MPF_HANDLER_H */



