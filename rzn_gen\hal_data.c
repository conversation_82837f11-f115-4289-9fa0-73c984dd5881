/* generated HAL source file - do not edit */
#include "hal_data.h"


dmac_instance_ctrl_t g_transfer0_ctrl;

dmac_register_set_setting_t g_transfer0_next1_register_setting =
{
    .p_dest = NULL,
    .p_src  = NULL,
    .length = 1
};

dmac_extended_info_t g_transfer0_extend_info =
{
    .src_size            = DMAC_TRANSFER_SIZE_1_BYTE,
    .dest_size           = DMAC_TRANSFER_SIZE_1_BYTE,
    .p_next1_register_setting = &g_transfer0_next1_register_setting,
};

transfer_info_t g_transfer0_info =
{
    .dest_addr_mode      = TRANSFER_ADDR_MODE_INCREMENTED,
    .repeat_area         = (transfer_repeat_area_t) 0, // Unused
    .irq                 = (transfer_irq_t) 0, // Unused
    .chain_mode          = (transfer_chain_mode_t) 0, // Unused
    .src_addr_mode       = TRANSFER_ADDR_MODE_FIXED,
    .size                = (transfer_size_t) 0, // Unused
    .mode                = TRANSFER_MODE_NORMAL,
    .p_dest              = (void *) NULL,
    .p_src               = (void const *) NULL,
    .num_blocks          = 0, // Unused
    .length              = 0,
    .p_extend            = &g_transfer0_extend_info,
};

const dmac_extended_cfg_t g_transfer0_extend =
{
    .unit                = 0,
    .channel             = 3,
#if defined(VECTOR_NUMBER_DMAC0_INT3)
    .dmac_int_irq         = VECTOR_NUMBER_DMAC0_INT3,
#else
    .dmac_int_irq         = FSP_INVALID_VECTOR,
#endif
    .dmac_int_ipl         = (BSP_IRQ_DISABLED),
    .dmac_int_irq_detect_type = (0),

    .activation_source   = ELC_EVENT_SPI1_SPRI,

    .ack_mode               = DMAC_ACK_MODE_BUS_CYCLE_MODE,
    .detection_mode         = (dmac_detection_t) ((0) << 2 | (1) << 1 | (0) << 0),
    .activation_request_source_select = DMAC_REQUEST_DIRECTION_SOURCE_MODULE,

    .next_register_operation = DMAC_REGISTER_SELECT_REVERSE_DISABLE,

    .transfer_interval      = 0,
    .channel_scheduling     = DMAC_CHANNEL_SCHEDULING_FIXED,

    .p_callback          = NULL,
    .p_context           = NULL,

    .p_peripheral_module_handler = spi_rxi_dmac_isr,
};
const transfer_cfg_t g_transfer0_cfg =
{
    .p_info              = &g_transfer0_info,
    .p_extend            = &g_transfer0_extend,
};
/* Instance structure to use this module. */
const transfer_instance_t g_transfer0 =
{
    .p_ctrl        = &g_transfer0_ctrl,
    .p_cfg         = &g_transfer0_cfg,
    .p_api         = &g_transfer_on_dmac
};
spi_instance_ctrl_t g_spi1_ctrl;

/** SPI extended configuration for SPI HAL driver */
const spi_extended_cfg_t g_spi1_ext_cfg =
{
    .spi_clksyn              = SPI_SSL_MODE_SPI,
    .spi_comm                = SPI_COMMUNICATION_FULL_DUPLEX,
    .ssl_polarity            = SPI_SSLP_LOW,
    .ssl_select              = SPI_SSL_SELECT_SSL0,
    .mosi_idle               = SPI_MOSI_IDLE_VALUE_FIXING_DISABLE,
    .parity                  = SPI_PARITY_MODE_DISABLE,
    .byte_swap               = SPI_BYTE_SWAP_DISABLE,
    .spck_div                = {
        /* Actual calculated bitrate: 4000000. */ .spbr = 11, .brdv = 0
    },
    .spck_delay              = SPI_DELAY_COUNT_1,
    .ssl_negation_delay      = SPI_DELAY_COUNT_1,
    .next_access_delay       = SPI_DELAY_COUNT_1,
    .transmit_fifo_threshold = 0,
    .receive_fifo_threshold  = 2,
    .receive_data_ready_detect_adjustment  = 0,
    .sync_bypass             = SPI_SYNCHRONIZER_NOT_BYPASS
 };

/** SPI configuration for SPI HAL driver */
const spi_cfg_t g_spi1_cfg =
{
    .channel             = 1,

#if defined(VECTOR_NUMBER_SPI1_SPRI)
    .rxi_irq             = VECTOR_NUMBER_SPI1_SPRI,
#else
    .rxi_irq             = FSP_INVALID_VECTOR,
#endif
#if defined(VECTOR_NUMBER_SPI1_SPTI)
    .txi_irq             = VECTOR_NUMBER_SPI1_SPTI,
#else
    .txi_irq             = FSP_INVALID_VECTOR,
#endif
#if defined(VECTOR_NUMBER_SPI1_SPCEND)
    .tei_irq             = VECTOR_NUMBER_SPI1_SPCEND,
#else
    .tei_irq             = FSP_INVALID_VECTOR,
#endif
#if defined(VECTOR_NUMBER_SPI1_SPEI)
    .eri_irq             = VECTOR_NUMBER_SPI1_SPEI,
#else
    .eri_irq             = FSP_INVALID_VECTOR,
#endif

    .rxi_ipl             = (12),
    .txi_ipl             = (12),
    .tei_ipl             = (12),
    .eri_ipl             = (12),

    .operating_mode      = SPI_MODE_MASTER,

    .clk_phase           = SPI_CLK_PHASE_EDGE_EVEN,
    .clk_polarity        = SPI_CLK_POLARITY_HIGH,

    .mode_fault          = SPI_MODE_FAULT_ERROR_DISABLE,
    .bit_order           = SPI_BIT_ORDER_MSB_FIRST,
#define FSP_NOT_DEFINED (1)
#if (FSP_NOT_DEFINED == FSP_NOT_DEFINED)
    .p_transfer_tx       = NULL,
#else
    .p_transfer_tx       = &FSP_NOT_DEFINED,
#endif
#if (FSP_NOT_DEFINED == g_transfer0)
    .p_transfer_rx       = NULL,
#else
    .p_transfer_rx       = &g_transfer0,
#endif
#undef FSP_NOT_DEFINED
    .p_callback          = spi_callback,

    .p_context           = NULL,
    .p_extend            = (void *)&g_spi1_ext_cfg,
};

/* Instance structure to use this module. */
const spi_instance_t g_spi1 =
{
    .p_ctrl        = &g_spi1_ctrl,
    .p_cfg         = &g_spi1_cfg,
    .p_api         = &g_spi_on_spi
};
spi_instance_ctrl_t g_spi3_ctrl;


/** SPI extended configuration for SPI HAL driver */
const spi_extended_cfg_t g_spi3_ext_cfg[3]=
{
    // bissc spi mode
    {
        .spi_clksyn              = SPI_SSL_MODE_SPI,
        .spi_comm                = SPI_COMMUNICATION_RECEIVE_ONLY,
        .ssl_polarity            = SPI_SSLP_LOW,
        .ssl_select              = SPI_SSL_SELECT_SSL0,
        .mosi_idle               = SPI_MOSI_IDLE_VALUE_FIXING_DISABLE,
        .parity                  = SPI_PARITY_MODE_DISABLE,
        .byte_swap               = SPI_BYTE_SWAP_DISABLE,
        .spck_div                = {
            /* Actual calculated bitrate: 2000000. */ .spbr = 5, .brdv = 2     
        },
        .spck_delay              = SPI_DELAY_COUNT_1,
        .ssl_negation_delay      = SPI_DELAY_COUNT_1,
        .next_access_delay       = SPI_DELAY_COUNT_1,
        .transmit_fifo_threshold = 0,
        .receive_fifo_threshold  = 1,
        .receive_data_ready_detect_adjustment  = 0,
        .sync_bypass             = SPI_SYNCHRONIZER_NOT_BYPASS
    },

    {
        .spi_clksyn              = SPI_SSL_MODE_SPI,
        .spi_comm                = SPI_COMMUNICATION_FULL_DUPLEX,
        .ssl_polarity            = SPI_SSLP_LOW,
        .ssl_select              = SPI_SSL_SELECT_SSL0,
        .mosi_idle               = SPI_MOSI_IDLE_VALUE_FIXING_LOW,
        .parity                  = SPI_PARITY_MODE_DISABLE,
        .byte_swap               = SPI_BYTE_SWAP_DISABLE,
        .spck_div                = {
    //        /* Actual calculated bitrate: 2000000. */ .spbr = 5, .brdv = 2
            /* Actual calculated bitrate: 4000000. */ .spbr = 5, .brdv = 1      
    //        /* Actual calculated bitrate: 8000000. */ .spbr = 5, .brdv = 0
    //        /* Actual calculated bitrate: 12000000. */ .spbr = 3, .brdv = 0          
        },
        .spck_delay              = SPI_DELAY_COUNT_1,
        .ssl_negation_delay      = SPI_DELAY_COUNT_1,
        .next_access_delay       = SPI_DELAY_COUNT_1,
        .transmit_fifo_threshold = 2,
        .receive_fifo_threshold  = 1,
        .receive_data_ready_detect_adjustment  = 0,
        .sync_bypass             = SPI_SYNCHRONIZER_NOT_BYPASS
    },
    // AM600 spi mode
    {
        .spi_clksyn              = SPI_SSL_MODE_SPI,
        .spi_comm                = SPI_COMMUNICATION_FULL_DUPLEX,
        .ssl_polarity            = SPI_SSLP_LOW,
        .ssl_select              = SPI_SSL_SELECT_SSL0,
        .mosi_idle               = SPI_MOSI_IDLE_VALUE_FIXING_LOW,
        .parity                  = SPI_PARITY_MODE_DISABLE,
        .byte_swap               = SPI_BYTE_SWAP_DISABLE,
        .spck_div                = {
    //        /* Actual calculated bitrate: 2000000. */ .spbr = 5, .brdv = 2
            /* Actual calculated bitrate: 4000000. */ .spbr = 5, .brdv = 1      
    //        /* Actual calculated bitrate: 8000000. */ .spbr = 5, .brdv = 0
    //        /* Actual calculated bitrate: 12000000. */ .spbr = 3, .brdv = 0          
        },
        .spck_delay              = SPI_DELAY_COUNT_1,
        .ssl_negation_delay      = SPI_DELAY_COUNT_1,
        .next_access_delay       = SPI_DELAY_COUNT_1,
        .transmit_fifo_threshold = 2,
        .receive_fifo_threshold  = 1,
        .receive_data_ready_detect_adjustment  = 0,
        .sync_bypass             = SPI_SYNCHRONIZER_NOT_BYPASS
    },
 };
/** SPI configuration for SPI HAL driver */
const spi_cfg_t g_spi3_cfg[3] =
{
    // bissc spi mode
    {
        .channel             = 3,

    #if defined(VECTOR_NUMBER_SPI3_SPRI)
        .rxi_irq             = VECTOR_NUMBER_SPI3_SPRI,
    #else
        .rxi_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPTI)
        .txi_irq             = VECTOR_NUMBER_SPI3_SPTI,
    #else
        .txi_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPCEND)
        .tei_irq             = VECTOR_NUMBER_SPI3_SPCEND,
    #else
        .tei_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPEI)
        .eri_irq             = VECTOR_NUMBER_SPI3_SPEI,
    #else
        .eri_irq             = FSP_INVALID_VECTOR,
    #endif

        .rxi_ipl             = (12),
        .txi_ipl             = (12),
        .tei_ipl             = (12),
        .eri_ipl             = (12),

        .operating_mode      = SPI_MODE_MASTER,
        .clk_phase           = SPI_CLK_PHASE_EDGE_EVEN,
        .clk_polarity        = SPI_CLK_POLARITY_LOW,
        .mode_fault          = SPI_MODE_FAULT_ERROR_DISABLE,
        .bit_order           = SPI_BIT_ORDER_MSB_FIRST,
    #define FSP_NOT_DEFINED (1)
    #if (FSP_NOT_DEFINED == FSP_NOT_DEFINED)
        .p_transfer_tx       = NULL,
    #else
        .p_transfer_tx       = &FSP_NOT_DEFINED,
    #endif
    #if (FSP_NOT_DEFINED == FSP_NOT_DEFINED)
        .p_transfer_rx       = NULL,
    #else
        .p_transfer_rx       = &FSP_NOT_DEFINED,
    #endif
    #undef FSP_NOT_DEFINED
        .p_callback          = spi_callback,

        .p_context           = NULL,
        .p_extend            = (void *)&g_spi3_ext_cfg[0], 
    },
    // ktm59xx spi mode
    {
        .channel             = 3,

    #if defined(VECTOR_NUMBER_SPI3_SPRI)
        .rxi_irq             = VECTOR_NUMBER_SPI3_SPRI,
    #else
        .rxi_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPTI)
        .txi_irq             = VECTOR_NUMBER_SPI3_SPTI,
    #else
        .txi_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPCEND)
        .tei_irq             = VECTOR_NUMBER_SPI3_SPCEND,
    #else
        .tei_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPEI)
        .eri_irq             = VECTOR_NUMBER_SPI3_SPEI,
    #else
        .eri_irq             = FSP_INVALID_VECTOR,
    #endif
        .rxi_ipl             = (12),
        .txi_ipl             = (12),
        .tei_ipl             = (12),
        .eri_ipl             = (12),
        .operating_mode      = SPI_MODE_MASTER,
        .clk_phase           = SPI_CLK_PHASE_EDGE_ODD,
        .clk_polarity        = SPI_CLK_POLARITY_LOW,
        .mode_fault          = SPI_MODE_FAULT_ERROR_DISABLE,
        .bit_order           = SPI_BIT_ORDER_MSB_FIRST,
    #define FSP_NOT_DEFINED (1)
    #if (FSP_NOT_DEFINED == FSP_NOT_DEFINED)
        .p_transfer_tx       = NULL,
    #else
        .p_transfer_tx       = &FSP_NOT_DEFINED,
    #endif
    #if (FSP_NOT_DEFINED == FSP_NOT_DEFINED)
        .p_transfer_rx       = NULL,
    #else
        .p_transfer_rx       = &FSP_NOT_DEFINED,
    #endif
    #undef FSP_NOT_DEFINED
        .p_callback          = spi_callback,

        .p_context           = NULL,
        .p_extend            = (void *)&g_spi3_ext_cfg[1], 
    },
    // AM600 spi mode
    {
        .channel             = 3,

    #if defined(VECTOR_NUMBER_SPI3_SPRI)
        .rxi_irq             = VECTOR_NUMBER_SPI3_SPRI,
    #else
        .rxi_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPTI)
        .txi_irq             = VECTOR_NUMBER_SPI3_SPTI,
    #else
        .txi_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPCEND)
        .tei_irq             = VECTOR_NUMBER_SPI3_SPCEND,
    #else
        .tei_irq             = FSP_INVALID_VECTOR,
    #endif
    #if defined(VECTOR_NUMBER_SPI3_SPEI)
        .eri_irq             = VECTOR_NUMBER_SPI3_SPEI,
    #else
        .eri_irq             = FSP_INVALID_VECTOR,
    #endif

        .rxi_ipl             = (12),
        .txi_ipl             = (12),
        .tei_ipl             = (12),
        .eri_ipl             = (12),

        .operating_mode      = SPI_MODE_MASTER,
        .clk_phase           = SPI_CLK_PHASE_EDGE_EVEN,
        .clk_polarity        = SPI_CLK_POLARITY_HIGH,
        .mode_fault          = SPI_MODE_FAULT_ERROR_DISABLE,
        .bit_order           = SPI_BIT_ORDER_MSB_FIRST,
    #define FSP_NOT_DEFINED (1)
    #if (FSP_NOT_DEFINED == FSP_NOT_DEFINED)
        .p_transfer_tx       = NULL,
    #else
        .p_transfer_tx       = &FSP_NOT_DEFINED,
    #endif
    #if (FSP_NOT_DEFINED == FSP_NOT_DEFINED)
        .p_transfer_rx       = NULL,
    #else
        .p_transfer_rx       = &FSP_NOT_DEFINED,
    #endif
    #undef FSP_NOT_DEFINED
        .p_callback          = spi_callback,

        .p_context           = NULL,
    .p_extend            = (void *)&g_spi3_ext_cfg[2], 
    },
};

/* Instance structure to use this module. */
const spi_instance_t g_spi3 =
{
    .p_ctrl        = &g_spi3_ctrl,
    .p_cfg         = &g_spi3_cfg[0],
    .p_api         = &g_spi_on_spi
};
cmt_instance_ctrl_t g_timer_free_run_ctrl;
const timer_cfg_t g_timer_free_run_cfg =
{
    .mode                = TIMER_MODE_PERIODIC,
    /* Actual period: 0.01048576 seconds. */ .period_counts = (uint32_t) 0x10000, .source_div = (timer_source_div_t)3,
    .channel             = 2,
    .p_callback          = NULL,
    .p_context           = NULL,
    .p_extend            = NULL,
    .cycle_end_ipl       = (BSP_IRQ_DISABLED),
#if defined(VECTOR_NUMBER_CMT2_CMI)
    .cycle_end_irq       = VECTOR_NUMBER_CMT2_CMI,
#else
    .cycle_end_irq = FSP_INVALID_VECTOR,
#endif
};
/* Instance structure to use this module. */
const timer_instance_t g_timer_free_run =
{
    .p_ctrl        = &g_timer_free_run_ctrl,
    .p_cfg         = &g_timer_free_run_cfg,
    .p_api         = &g_timer_on_cmt
};
dmac_instance_ctrl_t g_sci_zx_tx_dma_ctrl;

dmac_register_set_setting_t g_sci_zx_tx_dma_next1_register_setting =
{
    .p_dest = NULL,
    .p_src  = NULL,
    .length = 1
};

dmac_extended_info_t g_sci_zx_tx_dma_extend_info =
{
    .src_size            = DMAC_TRANSFER_SIZE_1_BYTE,
    .dest_size           = DMAC_TRANSFER_SIZE_1_BYTE,
    .p_next1_register_setting = &g_sci_zx_tx_dma_next1_register_setting,
};

transfer_info_t g_sci_zx_tx_dma_info =
{
    .dest_addr_mode      = TRANSFER_ADDR_MODE_FIXED,
    .repeat_area         = (transfer_repeat_area_t) 0, // Unused
    .irq                 = (transfer_irq_t) 0, // Unused
    .chain_mode          = (transfer_chain_mode_t) 0, // Unused
    .src_addr_mode       = TRANSFER_ADDR_MODE_INCREMENTED,
    .size                = (transfer_size_t) 0, // Unused
    .mode                = TRANSFER_MODE_NORMAL,
    .p_dest              = (void *) NULL,
    .p_src               = (void const *) NULL,
    .num_blocks          = 0, // Unused
    .length              = 1,
    .p_extend            = &g_sci_zx_tx_dma_extend_info,
};

const dmac_extended_cfg_t g_sci_zx_tx_dma_extend =
{
    .unit                = 0,
    .channel             = 1,
#if defined(VECTOR_NUMBER_DMAC0_INT1)
    .dmac_int_irq         = VECTOR_NUMBER_DMAC0_INT1,
#else
    .dmac_int_irq         = FSP_INVALID_VECTOR,
#endif
    .dmac_int_ipl         = (12),
    .dmac_int_irq_detect_type = (0),

    .activation_source   = ELC_EVENT_SCI4_TXI,

    .ack_mode               = DMAC_ACK_MODE_LEVEL_MODE,
    .detection_mode         = (dmac_detection_t) ((0) << 2 | (1) << 1 | (0) << 0),
    .activation_request_source_select = DMAC_REQUEST_DIRECTION_DESTINATION_MODULE,

    .next_register_operation = DMAC_REGISTER_SELECT_REVERSE_DISABLE,

    .transfer_interval      = 0,
    .channel_scheduling     = DMAC_CHANNEL_SCHEDULING_FIXED,

    .p_callback          = dmac_callback_sci4_tx_end,
    .p_context           = NULL,

    .p_peripheral_module_handler = NULL,
};
const transfer_cfg_t g_sci_zx_tx_dma_cfg =
{
    .p_info              = &g_sci_zx_tx_dma_info,
    .p_extend            = &g_sci_zx_tx_dma_extend,
};
/* Instance structure to use this module. */
const transfer_instance_t g_sci_zx_tx_dma =
{
    .p_ctrl        = &g_sci_zx_tx_dma_ctrl,
    .p_cfg         = &g_sci_zx_tx_dma_cfg,
    .p_api         = &g_transfer_on_dmac
};
dmac_instance_ctrl_t g_sci_ss_tx_dma_ctrl;

dmac_register_set_setting_t g_sci_ss_tx_dma_next1_register_setting =
{
    .p_dest = NULL,
    .p_src  = NULL,
    .length = 1
};

dmac_extended_info_t g_sci_ss_tx_dma_extend_info =
{
    .src_size            = DMAC_TRANSFER_SIZE_1_BYTE,
    .dest_size           = DMAC_TRANSFER_SIZE_1_BYTE,
    .p_next1_register_setting = &g_sci_ss_tx_dma_next1_register_setting,
};

transfer_info_t g_sci_ss_tx_dma_info =
{
    .dest_addr_mode      = TRANSFER_ADDR_MODE_FIXED,
    .repeat_area         = (transfer_repeat_area_t) 0, // Unused
    .irq                 = (transfer_irq_t) 0, // Unused
    .chain_mode          = (transfer_chain_mode_t) 0, // Unused
    .src_addr_mode       = TRANSFER_ADDR_MODE_INCREMENTED,
    .size                = (transfer_size_t) 0, // Unused
    .mode                = TRANSFER_MODE_NORMAL,
    .p_dest              = (void *) NULL,
    .p_src               = (void const *) NULL,
    .num_blocks          = 0, // Unused
    .length              = 1,
    .p_extend            = &g_sci_ss_tx_dma_extend_info,
};

const dmac_extended_cfg_t g_sci_ss_tx_dma_extend =
{
    .unit                = 0,
    .channel             = 0,
#if defined(VECTOR_NUMBER_DMAC0_INT0)
    .dmac_int_irq         = VECTOR_NUMBER_DMAC0_INT0,
#else
    .dmac_int_irq         = FSP_INVALID_VECTOR,
#endif
    .dmac_int_ipl         = (12),
    .dmac_int_irq_detect_type = (0),

    .activation_source   = ELC_EVENT_SCI0_TXI,

    .ack_mode               = DMAC_ACK_MODE_LEVEL_MODE,
    .detection_mode         = (dmac_detection_t) ((0) << 2 | (1) << 1 | (0) << 0),
    .activation_request_source_select = DMAC_REQUEST_DIRECTION_DESTINATION_MODULE,

    .next_register_operation = DMAC_REGISTER_SELECT_REVERSE_DISABLE,

    .transfer_interval      = 0,
    .channel_scheduling     = DMAC_CHANNEL_SCHEDULING_FIXED,

    .p_callback          = dmac_callback_sci0_tx_end,
    .p_context           = NULL,

    .p_peripheral_module_handler = NULL,
};
const transfer_cfg_t g_sci_ss_tx_dma_cfg =
{
    .p_info              = &g_sci_ss_tx_dma_info,
    .p_extend            = &g_sci_ss_tx_dma_extend,
};
/* Instance structure to use this module. */
const transfer_instance_t g_sci_ss_tx_dma =
{
    .p_ctrl        = &g_sci_ss_tx_dma_ctrl,
    .p_cfg         = &g_sci_ss_tx_dma_cfg,
    .p_api         = &g_transfer_on_dmac
};
dmac_instance_ctrl_t g_iic_led_tx_dma_ctrl;

dmac_register_set_setting_t g_iic_led_tx_dma_next1_register_setting =
{
    .p_dest = NULL,
    .p_src  = NULL,
    .length = 1
};

dmac_extended_info_t g_iic_led_tx_dma_extend_info =
{
    .src_size            = DMAC_TRANSFER_SIZE_1_BYTE,
    .dest_size           = DMAC_TRANSFER_SIZE_1_BYTE,
    .p_next1_register_setting = &g_iic_led_tx_dma_next1_register_setting,
};

transfer_info_t g_iic_led_tx_dma_info =
{
    .dest_addr_mode      = TRANSFER_ADDR_MODE_FIXED,
    .repeat_area         = (transfer_repeat_area_t) 0, // Unused
    .irq                 = (transfer_irq_t) 0, // Unused
    .chain_mode          = (transfer_chain_mode_t) 0, // Unused
    .src_addr_mode       = TRANSFER_ADDR_MODE_INCREMENTED,
    .size                = (transfer_size_t) 0, // Unused
    .mode                = TRANSFER_MODE_NORMAL,
    .p_dest              = (void *) NULL,
    .p_src               = (void const *) NULL,
    .num_blocks          = 0, // Unused
    .length              = 1,
    .p_extend            = &g_iic_led_tx_dma_extend_info,
};

const dmac_extended_cfg_t g_iic_led_tx_dma_extend =
{
    .unit                = 1,
    .channel             = 2,
#if defined(VECTOR_NUMBER_DMAC1_INT2)
    .dmac_int_irq         = VECTOR_NUMBER_DMAC1_INT2,
#else
    .dmac_int_irq         = FSP_INVALID_VECTOR,
#endif
    .dmac_int_ipl         = (BSP_IRQ_DISABLED),
    .dmac_int_irq_detect_type = (0),

    .activation_source   = ELC_EVENT_IIC1_TXI,

    .ack_mode               = DMAC_ACK_MODE_LEVEL_MODE,
    .detection_mode         = (dmac_detection_t) ((0) << 2 | (1) << 1 | (0) << 0),
    .activation_request_source_select = DMAC_REQUEST_DIRECTION_DESTINATION_MODULE,

    .next_register_operation = DMAC_REGISTER_SELECT_REVERSE_DISABLE,

    .transfer_interval      = 0,
    .channel_scheduling     = DMAC_CHANNEL_SCHEDULING_FIXED,

    .p_callback          = sci_zx_tx_dma_transfer_end_isr,
    .p_context           = NULL,

    .p_peripheral_module_handler = NULL,
};
const transfer_cfg_t g_iic_led_tx_dma_cfg =
{
    .p_info              = &g_iic_led_tx_dma_info,
    .p_extend            = &g_iic_led_tx_dma_extend,
};
/* Instance structure to use this module. */
const transfer_instance_t g_iic_led_tx_dma =
{
    .p_ctrl        = &g_iic_led_tx_dma_ctrl,
    .p_cfg         = &g_iic_led_tx_dma_cfg,
    .p_api         = &g_transfer_on_dmac
};
dsmif_channel_cfg_t g_dsmif1_channel_cfg2 =
{
    .ioel                = 0,
    .ioeh                = 0,
    .ise                 = 0,
    .iue                 = 0,
    .ckdir               = DSMIF_CLOCK_CTRL_MASTER,
    .sedge               = DSMIF_CLOCK_EDGE_POSITIVE,
    .ckdiv               = DSMIF_MASTER_CLOCK_20MHZ_PCLKH200,
    .cmsinc              = DSMIF_FILTER_ORDER_3RD,
    .cmdec               = 63,
    .sde                 = 0,
    .ocsinc              = DSMIF_FILTER_ORDER_3RD,
    .ocdec               = 3,
    .ocmptbl             = 0x0,
    .ocmptbh             = 0x0,
    .scntl               = 0x0,
    .scnth               = 0x0,
    .odel                = 0,
    .odeh                = 0,
    .cmsh                = (dsimf_data_shift_t)6,
    .ocsh                = (dsimf_data_shift_t)18
};
#define DSMIF_CHANNEL_12
dsmif_channel_cfg_t g_dsmif1_channel_cfg1 =
{
    .ioel                = 0,
    .ioeh                = 0,
    .ise                 = 0,
    .iue                 = 0,
    .ckdir               = DSMIF_CLOCK_CTRL_MASTER,
    .sedge               = DSMIF_CLOCK_EDGE_POSITIVE,
    .ckdiv               = DSMIF_MASTER_CLOCK_20MHZ_PCLKH200,
    .cmsinc              = DSMIF_FILTER_ORDER_3RD,
    .cmdec               = 63,
    .sde                 = 0,
    .ocsinc              = DSMIF_FILTER_ORDER_3RD,
    .ocdec               = 3,
    .ocmptbl             = 0x0,
    .ocmptbh             = 0x0,
    .scntl               = 0x0,
    .scnth               = 0x0,
    .odel                = 0,
    .odeh                = 0,
    .cmsh                = (dsimf_data_shift_t)6,
    .ocsh                = (dsimf_data_shift_t)18
};
#define DSMIF_CHANNEL_11
dsmif_instance_ctrl_t g_dsmif1_ctrl;
#define DSMIF_MASK_UNIT_10  (DSMIF_UNIT0_MASK_10 | DSMIF_UNIT0_MASK_11 | DSMIF_UNIT0_MASK_12)
#define DSMIF_MASK_UNIT_11  (DSMIF_UNIT1_MASK_10 | DSMIF_UNIT1_MASK_11 | DSMIF_UNIT1_MASK_12)
/** DSMIF configuration extension. This extension is required and must be provided in adc_cfg_t::p_extend. */
const dsmif_extended_cfg_t g_dsmif1_cfg_extend =
{
    .isel                = 0,
    .iseh                = 0,
    .sedm                = DSMIF_SUM_ERR_DETECT_CHANNEL_0_2,
    .scmptbl             = 2048,
    .scmptbh             = 63487,
    .seel                = 0,
    .seeh                = 0,
    .cap_trig_a          = DSMIF_CAPTURE_TRIGGER_0,
    .cap_trig_b          = DSMIF_CAPTURE_TRIGGER_NOT,
    .cnt_init_trig       = DSMIF_COUNTER_INIT_TRIGGER_0,
    .edge                = DSMIF_CLOCK_EDGE_POSITIVE,
#ifndef DSMIF_CHANNEL_10
#define DSMIF_UNIT0_MASK_10   (0)
#define DSMIF_UNIT1_MASK_10   (0)
    .p_channel_cfgs[0]   = NULL,
#else
#define DSMIF_UNIT0_MASK_10   (DSMIF_CHANNEL_MASK_0)
#define DSMIF_UNIT1_MASK_10   (DSMIF_CHANNEL_MASK_3)
    .p_channel_cfgs[0]   = &g_dsmif1_channel_cfg0,
#endif
#ifndef DSMIF_CHANNEL_11
#define DSMIF_UNIT0_MASK_11   (0)
#define DSMIF_UNIT1_MASK_11   (0)
    .p_channel_cfgs[1]   = NULL,
#else
#define DSMIF_UNIT0_MASK_11   (DSMIF_CHANNEL_MASK_1)
#define DSMIF_UNIT1_MASK_11   (DSMIF_CHANNEL_MASK_4)
    .p_channel_cfgs[1]   = &g_dsmif1_channel_cfg1,
#endif
#ifndef DSMIF_CHANNEL_12
#define DSMIF_UNIT0_MASK_12   (0)
#define DSMIF_UNIT1_MASK_12   (0)
    .p_channel_cfgs[2]   = NULL,
#else
#define DSMIF_UNIT0_MASK_12   (DSMIF_CHANNEL_MASK_2)
#define DSMIF_UNIT1_MASK_12   (DSMIF_CHANNEL_MASK_5)
    .p_channel_cfgs[2]   = &g_dsmif1_channel_cfg2,
#endif
    .channel_mask        = (dsmif_channel_mask_t)DSMIF_MASK_UNIT_11,
};
const adc_cfg_t g_dsmif1_cfg =
{
    .unit                = 1,
    .mode                = ADC_MODE_SYNCHRONIZE_SCAN,
#if defined(VECTOR_NUMBER_DSMIF1_CDRUI)
    .scan_end_irq        = VECTOR_NUMBER_DSMIF1_CDRUI,
#else
    .scan_end_irq        = FSP_INVALID_VECTOR,
#endif
    .scan_end_ipl        = (BSP_IRQ_DISABLED),
    .p_callback          = NULL,
    .p_context           = NULL,
    .p_extend            = &g_dsmif1_cfg_extend,
};
/* Instance structure to use this module. */
const adc_instance_t g_dsmif1 =
{
    .p_ctrl        = &g_dsmif1_ctrl,
    .p_cfg         = &g_dsmif1_cfg,
    .p_api         = &g_adc_on_dsmif
};
dsmif_channel_cfg_t g_dsmif0_channel_cfg1 =
{
    .ioel                = 0,
    .ioeh                = 0,
    .ise                 = 0,
    .iue                 = 0,
    .ckdir               = DSMIF_CLOCK_CTRL_MASTER,
    .sedge               = DSMIF_CLOCK_EDGE_POSITIVE,
    .ckdiv               = DSMIF_MASTER_CLOCK_20MHZ_PCLKH200,
    .cmsinc              = DSMIF_FILTER_ORDER_3RD,
    .cmdec               = 63,
    .sde                 = 0,
    .ocsinc              = DSMIF_FILTER_ORDER_3RD,
    .ocdec               = 3,
    .ocmptbl             = 0x0,
    .ocmptbh             = 0x0,
    .scntl               = 0x0,
    .scnth               = 0x0,
    .odel                = 0,
    .odeh                = 0,
    .cmsh                = (dsimf_data_shift_t)6,
    .ocsh                = (dsimf_data_shift_t)18
};
#define DSMIF_CHANNEL_01
dsmif_channel_cfg_t g_dsmif0_channel_cfg0 =
{
    .ioel                = 0,
    .ioeh                = 0,
    .ise                 = 0,
    .iue                 = 0,
    .ckdir               = DSMIF_CLOCK_CTRL_MASTER,
    .sedge               = DSMIF_CLOCK_EDGE_POSITIVE,
    .ckdiv               = DSMIF_MASTER_CLOCK_20MHZ_PCLKH200,
    .cmsinc              = DSMIF_FILTER_ORDER_3RD,
    .cmdec               = 63,
    .sde                 = 0,
    .ocsinc              = DSMIF_FILTER_ORDER_3RD,
    .ocdec               = 63,
    .ocmptbl             = 2048,
    .ocmptbh             = 63487,
    .scntl               = 1000,
    .scnth               = 1000,
    .odel                = 0,
    .odeh                = 0,
    .cmsh                = (dsimf_data_shift_t)6,
    .ocsh                = (dsimf_data_shift_t)6
};
#define DSMIF_CHANNEL_00
dsmif_instance_ctrl_t g_dsmif0_ctrl;
#define DSMIF_MASK_UNIT_00  (DSMIF_UNIT0_MASK_00 | DSMIF_UNIT0_MASK_01 | DSMIF_UNIT0_MASK_02)
#define DSMIF_MASK_UNIT_01  (DSMIF_UNIT1_MASK_00 | DSMIF_UNIT1_MASK_01 | DSMIF_UNIT1_MASK_02)
/** DSMIF configuration extension. This extension is required and must be provided in adc_cfg_t::p_extend. */
const dsmif_extended_cfg_t g_dsmif0_cfg_extend =
{
    .isel                = 0,
    .iseh                = 0,
    .sedm                = DSMIF_SUM_ERR_DETECT_CHANNEL_0_2,
    .scmptbl             = 2048,
    .scmptbh             = 63487,
    .seel                = 0,
    .seeh                = 0,
    .cap_trig_a          = DSMIF_CAPTURE_TRIGGER_0,
    .cap_trig_b          = DSMIF_CAPTURE_TRIGGER_NOT,
    .cnt_init_trig       = DSMIF_COUNTER_INIT_TRIGGER_0,
    .edge                = DSMIF_CLOCK_EDGE_POSITIVE,
#ifndef DSMIF_CHANNEL_00
#define DSMIF_UNIT0_MASK_00   (0)
#define DSMIF_UNIT1_MASK_00   (0)
    .p_channel_cfgs[0]   = NULL,
#else
#define DSMIF_UNIT0_MASK_00   (DSMIF_CHANNEL_MASK_0)
#define DSMIF_UNIT1_MASK_00   (DSMIF_CHANNEL_MASK_3)
    .p_channel_cfgs[0]   = &g_dsmif0_channel_cfg0,
#endif
#ifndef DSMIF_CHANNEL_01
#define DSMIF_UNIT0_MASK_01   (0)
#define DSMIF_UNIT1_MASK_01   (0)
    .p_channel_cfgs[1]   = NULL,
#else
#define DSMIF_UNIT0_MASK_01   (DSMIF_CHANNEL_MASK_1)
#define DSMIF_UNIT1_MASK_01   (DSMIF_CHANNEL_MASK_4)
    .p_channel_cfgs[1]   = &g_dsmif0_channel_cfg1,
#endif
#ifndef DSMIF_CHANNEL_02
#define DSMIF_UNIT0_MASK_02   (0)
#define DSMIF_UNIT1_MASK_02   (0)
    .p_channel_cfgs[2]   = NULL,
#else
#define DSMIF_UNIT0_MASK_02   (DSMIF_CHANNEL_MASK_2)
#define DSMIF_UNIT1_MASK_02   (DSMIF_CHANNEL_MASK_5)
    .p_channel_cfgs[2]   = &g_dsmif0_channel_cfg2,
#endif
    .channel_mask        = (dsmif_channel_mask_t)DSMIF_MASK_UNIT_00,
};
const adc_cfg_t g_dsmif0_cfg =
{
    .unit                = 0,
    .mode                = ADC_MODE_SYNCHRONIZE_SCAN,
#if defined(VECTOR_NUMBER_DSMIF0_CDRUI)
    .scan_end_irq        = VECTOR_NUMBER_DSMIF0_CDRUI,
#else
    .scan_end_irq        = FSP_INVALID_VECTOR,
#endif
    .scan_end_ipl        = (3),
    .p_callback          = NULL,
    .p_context           = NULL,
    .p_extend            = &g_dsmif0_cfg_extend,
};
/* Instance structure to use this module. */
const adc_instance_t g_dsmif0 =
{
    .p_ctrl        = &g_dsmif0_ctrl,
    .p_cfg         = &g_dsmif0_cfg,
    .p_api         = &g_adc_on_dsmif
};
xspi_qspi_instance_ctrl_t g_qspi_flash_ctrl;

static const spi_flash_erase_command_t g_qspi_flash_erase_command_list[] =
{
#if 4096 > 0
    {.command = 0x20,     .size = 4096 },
#endif
#if 32768 > 0
    {.command = 0x52, .size = 32768 },
#endif
#if 65536 > 0
    {.command = 0xD8,      .size = 65536 },
#endif
#if 0xC7 > 0
    {.command = 0xC7,       .size  = SPI_FLASH_ERASE_SIZE_CHIP_ERASE         },
#endif
};

static xspi_qspi_timing_setting_t g_qspi_flash_timing_settings =
{
    .command_to_command_interval = XSPI_QSPI_COMMAND_INTERVAL_CLOCKS_7,
    .cs_pullup_lag               = XSPI_QSPI_CS_PULLUP_CLOCKS_NO_EXTENSION,
    .cs_pulldown_lead            = XSPI_QSPI_CS_PULLDOWN_CLOCKS_NO_EXTENSION
};

static const xspi_qspi_extended_cfg_t g_qspi_flash_extended_cfg =
{
    .unit              = 1,
    .chip_select       = XSPI_QSPI_CHIP_SELECT_0,
    .memory_size       = XSPI_QSPI_MEMORY_SIZE_16MB,
    .p_timing_settings = &g_qspi_flash_timing_settings,
    .prefetch_en       = XSPI_QSPI_PREFETCH_FUNCTION_ENABLE,
};
const spi_flash_cfg_t g_qspi_flash_cfg =
{
    .spi_protocol        = SPI_FLASH_PROTOCOL_1S_1S_1S,
    .address_bytes       = SPI_FLASH_ADDRESS_BYTES_3,
    .dummy_clocks        = SPI_FLASH_DUMMY_CLOCKS_0,
    .read_command        = 0x03,
    .page_program_command = 0x02,
    .write_enable_command = 0x06,
    .status_command = 0x05,
    .write_status_bit    = 0,
    .xip_enter_command   = 0x20,
    .xip_exit_command    = 0xFF,
    .p_erase_command_list = &g_qspi_flash_erase_command_list[0],
    .erase_command_list_length = sizeof(g_qspi_flash_erase_command_list) / sizeof(g_qspi_flash_erase_command_list[0]),
    .p_extend            = &g_qspi_flash_extended_cfg,
};
/** This structure encompasses everything that is needed to use an instance of this interface. */
const spi_flash_instance_t g_qspi_flash =
{
    .p_ctrl = &g_qspi_flash_ctrl,
    .p_cfg =  &g_qspi_flash_cfg,
    .p_api =  &g_spi_flash_on_xspi_qspi,
};

/* Nominal and Data bit timing configuration */

can_bit_timing_cfg_t g_canfd0_bit_timing_cfg =
{
    /* Actual bitrate: 1000000 Hz. Actual sample point: 75 %. */
    .baud_rate_prescaler = 1,
    .time_segment_1 = 29,
    .time_segment_2 = 10,
    .synchronization_jump_width = 4
};

can_bit_timing_cfg_t g_canfd0_data_timing_cfg =
{
    /* Actual bitrate: 2000000 Hz. Actual sample point: 75 %. */
    .baud_rate_prescaler = 1,
    .time_segment_1 = 14,
    .time_segment_2 = 5,
    .synchronization_jump_width = 1
};


extern const canfd_afl_entry_t p_canfd0_afl[CANFD_CFG_AFL_CH0_RULE_NUM];

#ifndef CANFD_PRV_GLOBAL_CFG
#define CANFD_PRV_GLOBAL_CFG
canfd_global_cfg_t g_canfd_global_cfg =
{
    .global_interrupts = CANFD_CFG_GLOBAL_ERR_SOURCES,
    .global_config     = (CANFD_CFG_TX_PRIORITY | CANFD_CFG_DLC_CHECK | CANFD_CFD_CLOCK_SOURCE | CANFD_CFG_FD_OVERFLOW | (uint32_t) (CANFD_CFG_TIMER_PRESCALER << R_CANFD_CFDGCFG_ITRCP_Pos)),
    .rx_mb_config      = (CANFD_CFG_RXMB_NUMBER | (CANFD_CFG_RXMB_SIZE << R_CANFD_CFDRMNB_RMPLS_Pos)),
    .global_err_ipl = CANFD_CFG_GLOBAL_ERR_IPL,
    .rx_fifo_ipl    = CANFD_CFG_RX_FIFO_IPL,
    .rx_fifo_config    =
    {
        ((CANFD_CFG_RXFIFO0_INT_THRESHOLD << R_CANFD_CFDRFCC_RFIGCV_Pos) | (CANFD_CFG_RXFIFO0_DEPTH << R_CANFD_CFDRFCC_RFDC_Pos) | (CANFD_CFG_RXFIFO0_PAYLOAD << R_CANFD_CFDRFCC_RFPLS_Pos) | (CANFD_CFG_RXFIFO0_INT_MODE) | (CANFD_CFG_RXFIFO0_ENABLE)),
        ((CANFD_CFG_RXFIFO1_INT_THRESHOLD << R_CANFD_CFDRFCC_RFIGCV_Pos) | (CANFD_CFG_RXFIFO1_DEPTH << R_CANFD_CFDRFCC_RFDC_Pos) | (CANFD_CFG_RXFIFO1_PAYLOAD << R_CANFD_CFDRFCC_RFPLS_Pos) | (CANFD_CFG_RXFIFO1_INT_MODE) | (CANFD_CFG_RXFIFO1_ENABLE)),
        ((CANFD_CFG_RXFIFO2_INT_THRESHOLD << R_CANFD_CFDRFCC_RFIGCV_Pos) | (CANFD_CFG_RXFIFO2_DEPTH << R_CANFD_CFDRFCC_RFDC_Pos) | (CANFD_CFG_RXFIFO2_PAYLOAD << R_CANFD_CFDRFCC_RFPLS_Pos) | (CANFD_CFG_RXFIFO2_INT_MODE) | (CANFD_CFG_RXFIFO2_ENABLE)),
        ((CANFD_CFG_RXFIFO3_INT_THRESHOLD << R_CANFD_CFDRFCC_RFIGCV_Pos) | (CANFD_CFG_RXFIFO3_DEPTH << R_CANFD_CFDRFCC_RFDC_Pos) | (CANFD_CFG_RXFIFO3_PAYLOAD << R_CANFD_CFDRFCC_RFPLS_Pos) | (CANFD_CFG_RXFIFO3_INT_MODE) | (CANFD_CFG_RXFIFO3_ENABLE)),
        ((CANFD_CFG_RXFIFO4_INT_THRESHOLD << R_CANFD_CFDRFCC_RFIGCV_Pos) | (CANFD_CFG_RXFIFO4_DEPTH << R_CANFD_CFDRFCC_RFDC_Pos) | (CANFD_CFG_RXFIFO4_PAYLOAD << R_CANFD_CFDRFCC_RFPLS_Pos) | (CANFD_CFG_RXFIFO4_INT_MODE) | (CANFD_CFG_RXFIFO4_ENABLE)),
        ((CANFD_CFG_RXFIFO5_INT_THRESHOLD << R_CANFD_CFDRFCC_RFIGCV_Pos) | (CANFD_CFG_RXFIFO5_DEPTH << R_CANFD_CFDRFCC_RFDC_Pos) | (CANFD_CFG_RXFIFO5_PAYLOAD << R_CANFD_CFDRFCC_RFPLS_Pos) | (CANFD_CFG_RXFIFO5_INT_MODE) | (CANFD_CFG_RXFIFO5_ENABLE)),
        ((CANFD_CFG_RXFIFO6_INT_THRESHOLD << R_CANFD_CFDRFCC_RFIGCV_Pos) | (CANFD_CFG_RXFIFO6_DEPTH << R_CANFD_CFDRFCC_RFDC_Pos) | (CANFD_CFG_RXFIFO6_PAYLOAD << R_CANFD_CFDRFCC_RFPLS_Pos) | (CANFD_CFG_RXFIFO6_INT_MODE) | (CANFD_CFG_RXFIFO6_ENABLE)),
        ((CANFD_CFG_RXFIFO7_INT_THRESHOLD << R_CANFD_CFDRFCC_RFIGCV_Pos) | (CANFD_CFG_RXFIFO7_DEPTH << R_CANFD_CFDRFCC_RFDC_Pos) | (CANFD_CFG_RXFIFO7_PAYLOAD << R_CANFD_CFDRFCC_RFPLS_Pos) | (CANFD_CFG_RXFIFO7_INT_MODE) | (CANFD_CFG_RXFIFO7_ENABLE)),
    },
    .common_fifo_config =
    {
        CANFD_CFG_COMMONFIFO0,
        CANFD_CFG_COMMONFIFO1,
        CANFD_CFG_COMMONFIFO2,
        CANFD_CFG_COMMONFIFO3,
        CANFD_CFG_COMMONFIFO4,
        CANFD_CFG_COMMONFIFO5,
    }
};
#endif

canfd_extended_cfg_t g_canfd0_extended_cfg =
{
    .p_afl              = p_canfd0_afl,
    .txmb_txi_enable    = ((1ULL << 0) |  0ULL),
    .error_interrupts   = (R_CANFD_CFDC_CTR_EPIE_Msk | R_CANFD_CFDC_CTR_BOEIE_Msk | R_CANFD_CFDC_CTR_BORIE_Msk | R_CANFD_CFDC_CTR_OLIE_Msk |  0U),
    .p_data_timing      = &g_canfd0_data_timing_cfg,
    .delay_compensation = (1),
    .p_global_cfg       = &g_canfd_global_cfg,
};

canfd_instance_ctrl_t g_canfd0_ctrl;
const can_cfg_t g_canfd0_cfg =
{
    .channel                = 0,
    .p_bit_timing           = &g_canfd0_bit_timing_cfg,
    .p_callback             = canfd0_callback,
    .p_extend               = &g_canfd0_extended_cfg,
    .p_context              = NULL,
    .ipl                    = (12),
#if defined(VECTOR_NUMBER_CAN0_COMFRX)
    .rx_irq             = VECTOR_NUMBER_CAN0_COMFRX,
#else
    .rx_irq             = FSP_INVALID_VECTOR,
#endif
#if defined(VECTOR_NUMBER_CAN0_TX)
    .tx_irq             = VECTOR_NUMBER_CAN0_TX,
#else
    .tx_irq             = FSP_INVALID_VECTOR,
#endif
#if defined(VECTOR_NUMBER_CAN0_CHERR)
    .error_irq             = VECTOR_NUMBER_CAN0_CHERR,
#else
    .error_irq             = FSP_INVALID_VECTOR,
#endif
};
/* Instance structure to use this module. */
const can_instance_t g_canfd0 =
{
    .p_ctrl        = &g_canfd0_ctrl,
    .p_cfg         = &g_canfd0_cfg,
    .p_api         = &g_canfd_on_canfd
};
void g_hal_init(void) {
g_common_init();
}
