
/* File generated by gen_cfile.py. Should not be modified. */

#ifndef CANOBJECT_H
#define CANOBJECT_H

#include "data.h"

/* Prototypes of function provided by object dictionnary */
UNS32 ad_valueRangeTest (UNS8 typeValue, void * value);
const indextable * ad_scanIndexOD (UNS16 wIndex, UNS32 * errorCode, ODCallback_t **callbacks);


/* Master node data struct */
extern CO_Data od_Data;
#endif // CANOBJECT_H
