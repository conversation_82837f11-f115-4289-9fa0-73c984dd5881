/****************************************************************************************************
 *
 * FILE NAME:  IntTaskB.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.24
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	24-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Global.h"
#include "MotorIdentification.h"
#include "HardApi.h"
#include "BaseControls.h"
#include "PowerManager.h"
#include "MicroPrmCal.h"
#include "ExControls.h"
#include "BaseLoops.h"
#include "fundefine.h"
#include "canfd_protocol.h"
#include "PosManager.h"

extern PUBLIC void PosSpdFdbCal( BASE_LOOP *BaseLoops);
extern PUBLIC void BaseServoControl(AXIS_HANDLE *AxisB);
extern PUBLIC void BpiTorqueLimitControl( BASE_CTRL *pBaseCtrl,BOOL OtTrqLmtReq, ALARM *AlmMngr,BASE_LOOP *BaseLoops);
extern PUBLIC void Cia402_UpdateFeedback(BASE_LOOP *BaseLoops,UINT32 DiStatus);
extern PUBLIC void SysOutputTaskAProcessData( BASE_LOOP *BaseLoops, CTRL_LOOP_OUT *CtrlLoopOut );

PRIVATE void SysBasePowerControl( AXIS_HANDLE *AxisRscB, UINT32 SBCnt );
PRIVATE void SysBaseMakeTaskCMonitor( AXIS_HANDLE *AxisRscB );	
PRIVATE void    SysBaseDIDOFast( AXIS_HANDLE *AxisRscC );
PRIVATE void SysBaseUpdateDoInfo(AXIS_HANDLE *AxisRscB);
PRIVATE void  SysBaseGetEcatDo( AXIS_HANDLE *AxisRscC );

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
UINT32 gLastTimeB=0, ilaspTimeB=0;
UINT16 TASKB_delay = 0;
PUBLIC void IntTaskBExec(void)
{
    INT16                 ax_id;
    UINT16                slot=0;
    AXIS_HANDLE           *AxisB;
    TASK_MEASURE_TIME	  *TaskProcessTime;	

    volatile UINT32	    SAinB_exeTime;
    POWER_MNG		   *PowerManager = NULL;
    FUN_CMN_CONTROL	   *FnCmnCtrl;
	
    /* Read time stamp at the entrance of interrupt: for calculate the elapsed time of interrupt*/
    UINT32 IsrStartTime = hApi_GetTimerCounter();
    
    AxisB = GetAxisHandle(0);
    TaskProcessTime = AxisB->TaskProcessTime;
    if(TaskProcessTime->SA_maxTime == 0) return;
    
    TaskProcessTime->SB_exeflg = TRUE;
    
    if(TaskProcessTime->SBCount == 0 && TaskProcessTime->SB_maxTime == 0)
    {
    	TaskProcessTime->SACount = 1;
    }    

    AxisB->HwIputSignals->all = hApi_GetHwSignal();

    
//    PowerManager = AxisB->PowMngr;
   
    
    Canfd_ServoCtrl(0);
    Canfd_State_Monitor();  
    Canfd_GetServoAlarm();

    if(Canfd_GetTimeoutFlag() == 1)
    {
        ALMSetGlobalAlarm(ALM_ECAT_Break);
        Canfd_ResetTimeoutFlag();
    }     
    
    hApi_VoltAdcStart();
    
    
    #if NO_DIDO
    // Mabey should located in taskA
    for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
    {     
        AxisB[ax_id].SequenceIO->AllDiPinSts.all = hApi_UpdataDI(ax_id);
        
        SysBaseUpdateDoInfo(&AxisB[ax_id]);    
        
        SysBaseGetEcatDo(&AxisB[ax_id]);
        
        SequenceIO_Loop(AxisB[ax_id].SequenceIO); 
        
        SysBaseDIDOFast(&AxisB[ax_id]);  
    }    
    #endif
    
    for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
    {    
    	PosSpdFdbCal(AxisB[ax_id].BaseLoops);

        // AxisB[ax_id].BaseCtrls->DcSyncSig = edsGetDcSyncFlag();
	
        BpiInputMotorPosition( AxisB[ax_id].BaseLoops, AxisB[ax_id].Prm, AxisB[ax_id].CtrlLoopOut);

		Cia402_UpdateFeedback( AxisB[ax_id].BaseLoops, (UINT32)0);
		
		/* Output Monitor status */
        BpiOutputNetStatus(AxisB[ax_id].BaseCtrls,
                           AxisB[ax_id].CtrlLoopOut,
                           AxisB[ax_id].SeqCtrlOut,
                           AxisB[ax_id].BaseLoops->Bprm);
        
        if(hApi_GetOTM(ax_id) == 1)
        {
 
          //ALMSetServoAlarm( AxisB[ax_id].AlmMngr, ALM_IPM_OTM );
        }
    }

//	edsClrDcSyncFlag();

    
    
      
    SysBasePowerControl( AxisB, TaskProcessTime->SBCount );

    for( ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++ )
    {
//        CiA402_PRM      *Prm402 = AxisB[ax_id].BaseCtrls->Cia402Axis.Objects;	

        FnCmnCtrl		= AxisB[ax_id].FnCmnCtrl;          //add by jrh 2021/11/26
       
        TaskBInputCtrlCmd(&AxisB[ax_id]);

		
        if(AxisB[ax_id].MotorIdentify->FhaseFind.V.FFStartFlg == TRUE)
        {
            AxisB[ax_id].BaseLoops->VdRefVF = AxisB[ax_id].MotorIdentify->FhaseFind.V.FFVqRef;
            AxisB[ax_id].BaseLoops->VqRefVF = 0.0f;
            AxisB[ax_id].BaseLoops->ElecAngleVF = (AxisB[ax_id].MotorIdentify->FhaseFind.V.FFElecAngleRef<<1);
            AxisB[ax_id].BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_VF;
                
        }
        else
        {
#if 1
            #if NO_ADVFUNC  
            /* Vibration detection speed observer calculation */
            DetVibSpeedObserver( AxisB[ax_id].DetVib, AxisB[ax_id].BaseCtrls->MotSts.MotSpd, AxisB[ax_id].CtrlLoopOut->TrqRefMon_a );


            /* Maximum/minimum speed deviation setting process for vibration detection */
            if( (FnCmnCtrl->State.TuningPctrlRun) || (FnCmnCtrl->State.TuningRun) )
            {
                    DetVibSetSpeedError( AxisB[ax_id].DetVib, AxisB[ax_id].BaseCtrls->TuneLessCtrl.conf.TuningLessUse, TRUE );
            }
            else
            { 
                    DetVibSetSpeedError( AxisB[ax_id].DetVib, AxisB[ax_id].BaseCtrls->TuneLessCtrl.conf.TuningLessUse, FALSE );
            }
            
            /* Velocity deviation trace by vibration detection observer */
            DetVibTraceRuntimeService( AxisB[ax_id].DetVib );

            if( AxisB[ax_id].BaseCtrls->ResVib.AresVibSetting == TRUE )
            { 
                /* Type A damping control calculation */
                AxisB[ax_id].BaseCtrls->CtrlCmdMngr.SpdFBC -=
                        ResVibATypeResVibCtrl( &(AxisB[ax_id].BaseCtrls->ResVib),
                                             AxisB[ax_id].CtrlLoopOut->MotSpd_a,
                                             AxisB[ax_id].CtrlLoopOut->TrqRefo_a );
            }	
            #endif		
#endif		

            BaseServoControl(&AxisB[ax_id]);
//	    AxisB[ax_id].BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_VF;

            /* Servo response position management */
            PosMngResponseManager( AxisB[ax_id].BaseCtrls );

            InertiaIdentOLCal(&AxisB[ax_id].MotorIdentify->InertiaIdentOL, AxisB[ax_id].BaseLoops);
            if(AxisB[ax_id].MotorIdentify->InertiaIdentOL.V.StartFlag)
            {
                AxisB[ax_id].Prm->PnCtrlCfgPrm.Jrat = AxisB[ax_id].MotorIdentify->InertiaIdentOL.V.LoadInertiaRatio;
            }

            BpiTorqueLimitControl( AxisB[ax_id].BaseCtrls,
                                   AxisB[ax_id].SeqCtrlOut->TrqLimitReq,
                                   AxisB[ax_id].AlmMngr,
                                   AxisB[ax_id].BaseLoops);	

            /* Settling time related processing */
            StlgCalculateSettlingTime( &AxisB[ax_id].BaseCtrls->SettlingTime,
                                       AxisB[ax_id].BaseCtrls->PosManager.var.dPcmda,
                                       AxisB[ax_id].BaseCtrls->PosCtrlSts.CoinSignal );

            /* Create maximum overshoot */
            StlgMakeMaxOverShoot( &AxisB[ax_id].BaseCtrls->SettlingTime,
                                   &(AxisB[ax_id].BaseCtrls->PosManager),
                                   (BASE_MODE_POS == AxisB[ax_id].BaseCtrls->CtrlCmdMngr.CtrlMode),
                                   AxisB[ax_id].BaseLoops->PosLoop.PosErra );


            /* Positioning completion failure cumulative time creation */
            StlgMakeCoinOffTime( &AxisB[ax_id].BaseCtrls->SettlingTime,
                                 AxisB[ax_id].BaseCtrls->PosManager.var.dPcmda,
                                 AxisB[ax_id].BaseCtrls->PosCtrlSts.CoinSignal,
                                 (BASE_MODE_POS == AxisB[ax_id].BaseCtrls->CtrlCmdMngr.CtrlMode));


          }

          AxisB[ax_id].BaseCtrls->FrictionM.MotFricEnable = AxisB[ax_id].SeqCtrlOut->BaseEnableReq;

        
          if((AxisB[ax_id].SeqCtrlOut->BaseEnableReq == TRUE) && AxisB[ax_id].BaseCtrls->FrictionM.InitEnable)
          {
			// InitParamFriction( AxisB[ax_id].BaseLoops );
          }
		
          if(AxisB[ax_id].SeqCtrlOut->BaseEnableReq == FALSE)
          {
              AxisB[ax_id].BaseCtrls->FrictionM.InitEnable = TRUE; //in order to initialize one time when servo on
          }
    }

    hApi_Adc121Start();

    for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
    {
        if(AxisB[ax_id].Prm->PnAuxPrm.PTPStart && AxisB[ax_id].BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep >=2)
        {
          AxisB[ax_id].Prm->PnCia402Prm.ModesOfOperation0x6060 = 1;
          AxisB[ax_id].Prm->PnCia402Prm.TargetPosition0x607A = AxisB[ax_id].BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos;
          AxisB[ax_id].BaseCtrls->Cia402Axis.PTP_Ctrl.StopTime = AxisB[ax_id].Prm->PnAdvPrm.PTPStpTime *4;
        }
        if(AxisB[ax_id].FnCmnCtrl->FnSvControl != FALSE 
            && AxisB[ax_id].FnCmnCtrl->FnSvonReq == TRUE )
        {
            AxisB[ax_id].BaseLoops->FnCmnCtrlSvon = 1;
        }
        else
        {
            AxisB[ax_id].BaseLoops->FnCmnCtrlSvon = 0;
        }
        BaseLoopsExec(AxisB[ax_id].BaseLoops);
        SysOutputTaskAProcessData( AxisB[ax_id].BaseLoops, AxisB[ax_id].CtrlLoopOut );
    }

    slot = TaskProcessTime->SBCount % 0x08;

    if(slot == 0)
    {
        SysBaseMakeTaskCMonitor(AxisB);

        if(TaskProcessTime->SC_exeflg )
        {
            for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
            {
                hApi_PwmDisable(ax_id);
            }
            ALMSetGlobalAlarm( ALM_ISR_TIME );
        }
        else
        {
			hApi_SetTaskCInterrupt();
        }
    }
	
#if(1 == REGENE_USE)      // to modify 20240322
    if( CHK_REGENE_NO_OPERATION != PcmCheckRegeneAlarm( &(PowerManager->RegeneCtrl),
                                                          PowerManager->PowerSts.PowerOn,
                                                          AxisB[0].HwIputSignals->bits.RegenFdb))
    {
//        if( (PowerManager->RegeneCtrl.P.RegSyn == PNEXREG)		
//        && (PowerManager->RegeneCtrl.P.RegRxIn == FALSE) ) 	 
//        {
//            if( PowerManager->MainPowChecker.V.WrnExRegInhibit == FALSE )
//            { 
//                ALMSetGlobalAlarm( WRN_EXREG );
//                PowerManager->MainPowChecker.V.WrnExRegInhibit = TRUE;
//            }
//        }
//        else
        { 
            ALMSetGlobalAlarm( ALM_RG );
        }
    }    // end if( CHK_REGENE_NO_OPERATION !=..		    
#endif    
    
    /* Task Process time calculation */
    TaskProcessTime->SBCount++;
    SAinB_exeTime = TaskProcessTime->SAinB_exeTime;
//	TaskProcessTime->SB_crtTime = hApi_GetTimerCounter() - IsrStartTime  - SAinB_exeTime;
    TaskProcessTime->SB_crtTime = hApi_GetTimerCounter() - IsrStartTime;

    TaskProcessTime->SB_sumTime += TaskProcessTime->SB_crtTime;				
    if( TaskProcessTime->SB_crtTime > TaskProcessTime->SB_maxTime)
    {
        TaskProcessTime->SB_maxTime = TaskProcessTime->SB_crtTime;
    }

    if( TaskProcessTime->SC_exeflg == TRUE )
    {
        TaskProcessTime->SBinC_exeTime += TaskProcessTime->SB_crtTime;
    }
    else
    {
        TaskProcessTime->SBinC_exeTime = 0;
    }

    TaskProcessTime->SB_exeflg = FALSE;
    TaskProcessTime->SAinB_exeTime = 0;

    if(TaskProcessTime->SB_maxTime > (UINT32)TASKB_MAX_TIME)
    {
        ALMSetGlobalAlarm( ALM_ISR_TIME );
    }
    

}

/****************************************************************************************************
* DESCRIPTION:  Converter Control
*
* RETURNS:
*
****************************************************************************************************/
PRIVATE void SysBasePowerControl( AXIS_HANDLE *AxisRscB, UINT32 SBCnt )
{
	INT32			ax_no;
//	INT32			DcVolt; 
	POWER_MNG		*PowerManager;
//	BASE_CTRL		*BaseControls;
	BOOL			AllMotStop;
	CTRL_LOOP_OUT	*CtrlLoopOut;
//	BASE_LOOP		*BaseLoops;

	PowerManager = AxisRscB->PowMngr;

	/* Converter Controls */
	if( (SBCnt&1) == 0 ) 
	{
       
		/* P-N voltage detection processing */
		PcmDetectDcVolt(&PowerManager->DetDcVolt,
						&PowerManager->PowerSts);
        
		/* OV detection processing */
		PcmCheckOverVoltage(&PowerManager->MainPowChecker,
							&PowerManager->DetDcVolt,
							&PowerManager->PowerSts,
							&PowerManager->RegeneCtrl );					 

		/* Confirmation of motor stop for all axes */
		AllMotStop = TRUE;
		for( ax_no = 0; ax_no < MAX_AXIS_NUM; ax_no++ )
		{
			if( AxisRscB[ax_no].SeqCtrlOut->MotStop == FALSE )
			{ 
				AllMotStop = FALSE;
				break;
			}
		}

#if(1 == REGENE_USE) 
		if( ( (PowerManager->MainPowChecker.P.DcbusWay == FALSE)			/* AC input mode 	*/
		   || ( (PowerManager->MainPowChecker.P.DcbusWay == TRUE)			/* DC power input 	*/
			 && (PowerManager->RegeneCtrl.P.RegSyn == PNREGSYN) ) )			/* Regenerative synchronization used */
			&& (PowerManager->DetDcVolt.P.VdetPrmOk == TRUE) )   			/* Normal voltage detection setting */
		{ 
			PcmRegeneControl( &PowerManager->RegeneCtrl,
							   &PowerManager->PowerSts,
							  AllMotStop );
		}
#endif			

        
	}

	for( ax_no = 0; ax_no < MAX_AXIS_NUM; ax_no++ )
	{

		CtrlLoopOut = AxisRscB[ax_no].CtrlLoopOut;

		/* PN voltage averaging for field weakening parameter calculation */
		PcmWFPrmCalc( &AxisRscB[ax_no].BaseCtrls->WeakenField,
							  CtrlLoopOut->MotSpd_a,			
							  PowerManager->PowerSts.DcVolt);
        
	}

        

}


/****************************************************************************************************
* DESCRIPTION:  
*
* RETURNS:
*
****************************************************************************************************/
PRIVATE void	SysBaseMakeTaskCMonitor( AXIS_HANDLE *AxisRscB )
{
	BASE_CTRL			*BaseControls;
//	BASE_LOOP			*BaseLoops;		
	CTRL_LOOP_OUT		*CtrlLoopOut;
	BASE_CTRL_OUT		*BaseCtrlOut;

	INT16				ax_no;
	AXIS_HANDLE			*AxisRsc;

	AxisRsc	= AxisRscB;
	for( ax_no = 0; ax_no < MAX_AXIS_NUM; ax_no++ )
	{
//		BaseLoops = AxisRsc->BaseLoops;			
		BaseControls= AxisRsc->BaseCtrls;
		CtrlLoopOut	= AxisRsc->CtrlLoopOut;
		BaseCtrlOut	= AxisRsc->BaseCtrlOut;

		/* Update monitor data for ScanB-> ScanC */
		BaseCtrlOut->MotPos			= BaseControls->Cia402Axis.Objects->PositionActualInternalvalue0x6063;
		
		BaseCtrlOut->PcmdCntr			= BaseControls->CtrlCmdMngr.PosRef;
		BaseCtrlOut->MotSpdSum			= BaseControls->MotSts.MotSpdSumB ;
		BaseCtrlOut->MotSpdCnt			= BaseControls->MotSts.MotSpdCntB ;
		BaseCtrlOut->OutputSpdRef		= (INT32)BaseControls->CtrlCmdMngr.SpdRef;
		BaseCtrlOut->PositionError		= AxisRsc->BaseLoops->PosLoop.PosErra;  // todo
		BaseCtrlOut->SvonSpdLmtReq		= AxisRsc->CtrlLoopOut->SvonSpdLmtFlag;

		BaseCtrlOut->IdRefMon			= CtrlLoopOut->IdRefMon;
		BaseCtrlOut->IqRefMon			= CtrlLoopOut->IqRefMon;
		BaseCtrlOut->TrqRefMon 			= CtrlLoopOut->TrqRefMon;

		/* Command data output */
		BaseCtrlOut->TrqCtrlRef = (INT32)BaseControls->CtrlCmdMngr.TrqRef;
		BaseCtrlOut->SpdCtrlRef = (INT32)AxisRsc->BaseLoops->SpdRefSum;
		BaseCtrlOut->CtrlModeOut.dw = BaseControls->CtrlCmdMngr.CtrlMode;;

		BaseControls->MotSts.MotSpdSumB = 0;
		BaseControls->MotSts.MotSpdCntB	= 0 ;
                
                BaseCtrlOut->PosTragetErr = BaseControls->CtrlCmdMngr.PosTragetErr;
                BaseCtrlOut->SpdTragetErr = BaseControls->CtrlCmdMngr.SpdTragetErr;
                BaseCtrlOut->TrqTragetErr = BaseControls->CtrlCmdMngr.TrqTragetErr;
//                BaseCtrlOut->PosTragetErr =   BaseControls->Cia402Axis.Objects->TargetPosition0x607A
//                                            - BaseControls->Cia402Axis.Objects->PositionActualValue0x6064;
		AxisRsc++;
	}


}

#if NO_DIDO
/****************************************************************************************************
* DESCRIPTION:  Be used in taskB
*
* RETURNS:
*
****************************************************************************************************/
PRIVATE void  SysBaseDIDOFast( AXIS_HANDLE *AxisRscB )
{  
    BASE_CTRL           *BaseCtrls = NULL;
    TCiA402Axis         *Cia402Axis;
    HOME_MODE_CTRL      *CtrlHome = NULL;    


    BaseCtrls  = AxisRscB->BaseCtrls;
    SequenceIO = AxisRscB->SequenceIO;
    Cia402Axis = &(BaseCtrls->Cia402Axis);
    CtrlHome   = &(Cia402Axis->HOME_Ctrl);
    
    if((SequenceIO->CalDiFunSet >> DI_POT_LMT) & 0x1)
    {
      CtrlHome->PositiveLimit = SequenceIO->V.bits.PosLmt; 
      if(Cia402Axis->Objects->ModesOfOperationDisplay0x6061 != CTRL_MODE_HOME)
      {
        if(CtrlHome->PositiveLimit == 1)
        {
//          ALMSetServoAlarm( AxisRscB->AlmMngr, ALM_HPOSLMT);	
        }
      }
      
    }
    
    if((SequenceIO->CalDiFunSet >> DI_NEG_LMT) & 0x1)
    {
      CtrlHome->NegativeLimit = SequenceIO->V.bits.NegLmt; 
      if(Cia402Axis->Objects->ModesOfOperationDisplay0x6061 != CTRL_MODE_HOME)
      {
        if(CtrlHome->NegativeLimit == 1)
        {
//          ALMSetServoAlarm( AxisRscB->AlmMngr, ALM_HNEGLMT);	
        }
      }
      
    }

    
    if((SequenceIO->CalDiFunSet >> DI_HOME_SW) & 0x1)
    {
      CtrlHome->HomeSwitch = SequenceIO->V.bits.HomeSw; 
    } 
    
    if((SequenceIO->CalDiFunSet >> DI_FAULT_CLR) & 0x1)
    {
      if(SequenceIO->V.bits.FaultClr)
      {
        Cia402Axis->AlmRstCmd = SequenceIO->V.bits.FaultClr; 
      }
    } 
    
    if((SequenceIO->CalDiFunSet >> DI_SVR_ON) & 0x1)
    {
       Cia402Axis->DiServoOnMask = TRUE;
//       if (!(AxisRscB->BaseSeq->ServoReady) && SequenceIO->V.bits.ServoOn==TRUE)
//       {
//          Cia402Axis->DiServoOn = FALSE; 
//          
//          if(AxisRscB->AlmMngr->Status.AlmFlag == FALSE)
//          {
//              ALMSetServoAlarm( AxisRscB->AlmMngr, ALM_PUV ); 
//          }          
//       }
//       else
       {
         Cia402Axis->DiServoOn = SequenceIO->V.bits.ServoOn; 
       }
    }
    else 
    {
       Cia402Axis->DiServoOn = SequenceIO->V.bits.ServoOn; 
       Cia402Axis->DiServoOnMask = FALSE;
    }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void SysBaseUpdateDoInfo(AXIS_HANDLE *AxisRscB)
{
    BASE_CTRL           *BaseCtrls = NULL;
    TCiA402Axis         *Cia402Axis;
	SEQ_IO_HNDL			*SequenceIO = NULL;
 

    BaseCtrls  = AxisRscB->BaseCtrls;
    SequenceIO = AxisRscB->SequenceIO;
    Cia402Axis = &(BaseCtrls->Cia402Axis);
    
    
    SequenceIO->DoOutsts.BrkOut    = !(AxisRscB->BaseSeq->BkSeqData.V.Brake);  // Brake == 0 relase brake and Motor shaft can move
    SequenceIO->DoOutsts.FaultOut  = AxisRscB->AlmMngr->Status.AlmFlag;
    SequenceIO->DoOutsts.TgtRchOut = (Cia402Axis->Objects->Statusword0x6041 >> 10) &0x1;
}

/****************************************************************************************************
* DESCRIPTION:   Get Ecat DO  Control Info
*
* RETURNS:
*
****************************************************************************************************/
PRIVATE void  SysBaseGetEcatDo( AXIS_HANDLE *AxisRscC )
{  
    TCiA402Axis         *Cia402Axis;
	SEQ_IO_HNDL			*SequenceIO = NULL;
  
    SequenceIO = AxisRscC->SequenceIO;
    Cia402Axis = &(AxisRscC->BaseCtrls->Cia402Axis);    
    
    SequenceIO->EcatDoCtrl = Cia402Axis->Objects->DigitalOutputsControl0x60FE;
    SequenceIO->EcatDoMask = Cia402Axis->Objects->DigitalOutputsMask0x60FE;
} 
#endif
