/****************************************************************************************************
 *
 * FILE NAME:  BaseControls.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.27
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	27-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _BASE_CONTROLS_H_
#define _BASE_CONTROLS_H_

#include  "Global.h"

#define FEED_FORWARD_NONE        0
#define FEED_FORWARD_INNER       1
#define FEED_FORWARD_OUT         2

PUBLIC void BaseControlInit(AXIS_HANDLE *AxisB);
PUBLIC void TaskBInputCtrlCmd(AXIS_HANDLE *AxisB);
PUBLIC void BpiInputMotorPosition( BASE_LOOP *BaseLoops,
                                                         PRMDATA *Prm, CTRL_LOOP_OUT *CtrlLoopOut);
void BpiOutputNetStatus(BASE_CTRL *BaseControls, CTRL_LOOP_OUT *CtrlLoopOut, 
														 SEQ_CTRL_OUT *SeqCtrlOut, BPRMDAT *Bprm);


#endif 

