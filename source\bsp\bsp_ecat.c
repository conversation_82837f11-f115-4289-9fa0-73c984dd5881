/*
 * bsp_ecat.c
 *
 *  Created on: Aug 1, 2023
 *      Author: xgj12
 */
#include "hal_data.h"
#include "bsp_ecat.h"
#include "ecat_def.h"
#include "ecat_eeprom.h"

#ifndef USER_ETHER_PHY_ERROR_RETURN

/*LDRA_INSPECTED 77 S This macro does not work when surrounded by parentheses. */
 #define USER_ETHER_PHY_ERROR_RETURN(a, err)    FSP_ERROR_RETURN((a), (err))
#endif

#define USER_ETHERC_REG_SIZE                               (0x400UL)

/** "RPHY" in ASCII.  Used to determine if the control block is open. */
#define USER_ETHER_PHY_OPEN                                (0x52504859U)

/* Media Independent Interface */
#define USER_ETHER_PHY_MII_ST                              (1)
#define USER_ETHER_PHY_MII_READ                            (2)
#define USER_ETHER_PHY_MII_WRITE                           (1)

/* Standard PHY Registers */
#define USER_ETHER_PHY_REG_CONTROL                         (0)
#define USER_ETHER_PHY_REG_STATUS                          (1)
#define USER_ETHER_PHY_REG_IDENTIFIER1                     (2)
#define USER_ETHER_PHY_REG_IDENTIFIER2                     (3)
#define USER_ETHER_PHY_REG_AN_ADVERTISEMENT                (4)
#define USER_ETHER_PHY_REG_AN_LINK_PARTNER                 (5)
#define USER_ETHER_PHY_REG_AN_EXPANSION                    (6)
#define USER_ETHER_PHY_REG_1000BT_CONTROL                  (9)
#define USER_ETHER_PHY_REG_1000BT_STATUS                   (10)

/* Basic Mode Control Register Bit Definitions */
#define USER_ETHER_PHY_CONTROL_RESET                       (1 << 15)
#define USER_ETHER_PHY_CONTROL_LOOPBACK                    (1 << 14)
#define USER_ETHER_PHY_CONTROL_100_MBPS                    (1 << 13)
#define USER_ETHER_PHY_CONTROL_AN_ENABLE                   (1 << 12)
#define USER_ETHER_PHY_CONTROL_POWER_DOWN                  (1 << 11)
#define USER_ETHER_PHY_CONTROL_ISOLATE                     (1 << 10)
#define USER_ETHER_PHY_CONTROL_AN_RESTART                  (1 << 9)
#define USER_ETHER_PHY_CONTROL_FULL_DUPLEX                 (1 << 8)
#define USER_ETHER_PHY_CONTROL_COLLISION                   (1 << 7)
#define USER_ETHER_PHY_CONTROL_SPEED_SELCT                 (1 << 6)

/* Basic Mode Status Register Bit Definitions */
#define USER_ETHER_PHY_STATUS_100_T4                       (1 << 15)
#define USER_ETHER_PHY_STATUS_100F                         (1 << 14)
#define USER_ETHER_PHY_STATUS_100H                         (1 << 13)
#define USER_ETHER_PHY_STATUS_10F                          (1 << 12)
#define USER_ETHER_PHY_STATUS_10H                          (1 << 11)
#define USER_ETHER_PHY_STATUS_EX_STATUS                    (1 << 8)
#define USER_ETHER_PHY_STATUS_AN_COMPLETE                  (1 << 5)
#define USER_ETHER_PHY_STATUS_RM_FAULT                     (1 << 4)
#define USER_ETHER_PHY_STATUS_AN_ABILITY                   (1 << 3)
#define USER_ETHER_PHY_STATUS_LINK_UP                      (1 << 2)
#define USER_ETHER_PHY_STATUS_JABBER                       (1 << 1)
#define USER_ETHER_PHY_STATUS_EX_CAPABILITY                (1 << 0)

/* Auto Negotiation Advertisement Bit Definitions */
#define USER_ETHER_PHY_AN_ADVERTISEMENT_NEXT_PAGE          (1 << 15)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_RM_FAULT           (1 << 13)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_ASM_DIR            (1 << 11)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_PAUSE              (1 << 10)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_100_T4             (1 << 9)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_100F               (1 << 8)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_100H               (1 << 7)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_10F                (1 << 6)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_10H                (1 << 5)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_SELECTOR           (1 << 0)
#define USER_ETHER_PHY_AN_ADVERTISEMENT_SELECTOR_MASK      (1 << 0)

/* Auto Negotiate Link Partner Ability Bit Definitions */
#define USER_ETHER_PHY_AN_LINK_PARTNER_NEXT_PAGE           (1 << 15)
#define USER_ETHER_PHY_AN_LINK_PARTNER_ACK                 (1 << 14)
#define USER_ETHER_PHY_AN_LINK_PARTNER_RM_FAULT            (1 << 13)
#define USER_ETHER_PHY_AN_LINK_PARTNER_ASM_DIR             (1 << 11)
#define USER_ETHER_PHY_AN_LINK_PARTNER_PAUSE               (1 << 10)
#define USER_ETHER_PHY_AN_LINK_PARTNER_100_T4              (1 << 9)
#define USER_ETHER_PHY_AN_LINK_PARTNER_100F                (1 << 8)
#define USER_ETHER_PHY_AN_LINK_PARTNER_100H                (1 << 7)
#define USER_ETHER_PHY_AN_LINK_PARTNER_10F                 (1 << 6)
#define USER_ETHER_PHY_AN_LINK_PARTNER_10H                 (1 << 5)
#define USER_ETHER_PHY_AN_LINK_PARTNER_SELECTOR            (1 << 0)

/* 1000BASE-T Control */
#define USER_ETHER_PHY_1000BT_CONTROL_1000F                (1 << 9)
#define USER_ETHER_PHY_1000BT_CONTROL_1000H                (1 << 8)

/* 1000BASE-T Status */
#define USER_ETHER_PHY_1000BT_STATUS_PARTNER_1000F         (1 << 11)
#define USER_ETHER_PHY_1000BT_STATUS_PARTNER_1000H         (1 << 10)

#define USER_ETHER_PHY_PIR_MDI_MASK                        (1 << 3)
#define USER_ETHER_PHY_PIR_MDO_HIGH                        (0x04)
#define USER_ETHER_PHY_PIR_MDO_LOW                         (0x00)
#define USER_ETHER_PHY_PIR_MMD_WRITE                       (0x02)
#define USER_ETHER_PHY_PIR_MMD_READ                        (0x00)
#define USER_ETHER_PHY_PIR_MDC_HIGH                        (0x01)
#define USER_ETHER_PHY_PIR_MDC_LOW                         (0x00)

#define USER_ETHER_PHY_PREAMBLE_LENGTH                     (32U)
#define USER_ETHER_PHY_WRITE_DATA_BIT_MASK                 (0x8000)

/* Define for MDIO_CFG_STATUS of MDIO_ETHSW */
#define USER_ETHER_PHY_ETHSW_MDIO_CFG_STATUS_BUSY          (1 << 0)  /**< busy bit mask */
#define USER_ETHER_PHY_ETHSW_MDIO_CFG_STATUS_READERR       (1 << 1)  /**< read error mask */

/* Define for MDIO_COMMAND of MDIO_ETHSW */
#define USER_ETHER_PHY_ETHSW_MDIO_COMMAND_REGADDR_MASK     (0x1f)    /**< REG address mask */
#define USER_ETHER_PHY_ETHSW_MDIO_COMMAND_PHYADDR_SHIFT    (5)       /**< PHY address shift */
                                                                /**< PHY address mask */
#define USER_ETHER_PHY_ETHSW_MDIO_COMMAND_PHYADDR_MASK     (0x1f << USER_ETHER_PHY_ETHSW_MDIO_COMMAND_PHYADDR_SHIFT)
#define USER_ETHER_PHY_ETHSW_MDIO_COMMAND_TRANINIT_READ    (1 << 15) /**< transaction init read */

#define USER_ETHER_PHY_16BIT_DATA_MASK                     (0xffff)

/* Define for delay time */
#define USER_ETHER_PHY_DELAY_2US                           (2) /* 2us */

/* Initial value of down counter for timeout detection */
#define USER_ETHER_PHY_TIMEOUT_COUNT                       (1000000000)

/* Illegal PHY register read value  */
#define USER_ETHER_PHY_REGISTER_READ_ERROR                 (0xffff)

/* Bit definen of GMII_Address Register */
#define USER_ETHER_PHY_GMII_ADDRESS_PA_SHIFT               (11)     /**< Physical Layer Address */
#define USER_ETHER_PHY_GMII_ADDRESS_PA_MASK                (0x1f << USER_ETHER_PHY_GMII_ADDRESS_PA_SHIFT)
#define USER_ETHER_PHY_GMII_ADDRESS_GR_SHIFT               (6)      /**< GMII Register */
#define USER_ETHER_PHY_GMII_ADDRESS_GR_MASK                (0x1f << USER_ETHER_PHY_GMII_ADDRESS_GR_SHIFT)
#define USER_ETHER_PHY_GMII_ADDRESS_CR                     (4 << 2) /**< CSR Clock Range */
#define USER_ETHER_PHY_GMII_ADDRESS_GW                     (1 << 1) /**< GMII Write */
#define USER_ETHER_PHY_GMII_ADDRESS_GB                     (1 << 0) /**< GMII Busy */


static uint32_t user_ether_phy_read (uint32_t port_num, uint32_t reg_addr);
uint32_t user_ether_phy_read_gmac (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr);
uint32_t user_ether_phy_read_ethsw (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr);
uint32_t user_ether_phy_read_esc (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr);

void user_ether_phy_write (uint32_t port_num, uint32_t reg_addr, uint32_t data);
void user_ether_phy_write_gmac (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr, uint32_t data);
void user_ether_phy_write_ethsw (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr, uint32_t data);
void user_ether_phy_write_esc (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr, uint32_t data);

uint8_t ecatInitStatus = 0;

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
void bsp_ecat_init(void)
{
    
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
uint16_t ecat_startAutoNegotiate(void)
{

   return EcatWritePhyLedCtrlReg();
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
void ecat_read_eeprom(void)
{
    
  
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
static uint32_t user_ether_phy_read (uint32_t port_num, uint32_t reg_addr)
{
    ether_phy_extend_cfg_t * p_extend;
    ether_phy_instance_ctrl_t * p_ether_pyh_instance_ctrl;

    if (port_num == 1)
    {
        p_ether_pyh_instance_ctrl = (ether_phy_instance_ctrl_t *)&g_ether_phy1_ctrl;
        p_extend = (ether_phy_extend_cfg_t *) g_ether_phy1_ctrl.p_ether_phy_cfg->p_extend;
    }
    else
    {
        p_ether_pyh_instance_ctrl = (ether_phy_instance_ctrl_t *)&g_ether_phy0_ctrl;
        p_extend = (ether_phy_extend_cfg_t *) g_ether_phy0_ctrl.p_ether_phy_cfg->p_extend;
    }

    switch (p_extend->mdio_type)
    {
        case ETHER_PHY_MDIO_GMAC:
        {
            return user_ether_phy_read_gmac(p_ether_pyh_instance_ctrl, reg_addr);
        }

        case ETHER_PHY_MDIO_ETHSW:
        {
            return user_ether_phy_read_ethsw(p_ether_pyh_instance_ctrl, reg_addr);
        }

        case ETHER_PHY_MDIO_ESC:
        {
            return user_ether_phy_read_esc(p_ether_pyh_instance_ctrl, reg_addr);
        }

        default:

            return (uint32_t) -1;
    }
}
/*******************************************************************************************************************//**
 * Reads a PHY register by GMAC control
 *
 * @param[in]   p_instance_ctrl     Pointer to the control block for the channel
 * @param[in]   reg_addr            Address of the PHY register
 *
 * @retval      read value
 **********************************************************************************************************************/
uint32_t user_ether_phy_read_gmac (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr)
{
    volatile R_GMAC_Type * p_reg_etherc = (R_GMAC_Type *) p_instance_ctrl->p_reg_etherc;
    uint32_t               timeout;
    uint32_t               val_tmp;

    val_tmp =
        ((p_instance_ctrl->p_ether_phy_cfg->phy_lsi_address << USER_ETHER_PHY_GMII_ADDRESS_PA_SHIFT) &
                USER_ETHER_PHY_GMII_ADDRESS_PA_MASK) |
        ((reg_addr << USER_ETHER_PHY_GMII_ADDRESS_GR_SHIFT) & USER_ETHER_PHY_GMII_ADDRESS_GR_MASK) | ///< Phy reg address
        USER_ETHER_PHY_GMII_ADDRESS_CR |                                                            ///< Speed
        USER_ETHER_PHY_GMII_ADDRESS_GB;                                                             ///< Busy

    p_reg_etherc->GMII_Address = val_tmp;

    /* calculate timeout based on current timestamp */
    timeout = USER_ETHER_PHY_TIMEOUT_COUNT;

    while (1)
    {
        /* Wait read complete */
        if (!(p_reg_etherc->GMII_Address_b.GB))
        {

            /* Return phy value */
            return p_reg_etherc->GMII_Data & USER_ETHER_PHY_16BIT_DATA_MASK;
        }

        if (0 == timeout)
        {
            /* Timeout */
            break;
        }

        timeout--;
    }

    return (uint32_t) -1;
}                                      /* End of function ether_phy_read_gmac() */

/*******************************************************************************************************************//**
 * Reads a PHY register by ETHSW control
 *
 * @param[in]   p_instance_ctrl     Pointer to the control block for the channel
 * @param[in]   reg_addr            Address of the PHY register
 *
 * @retval      read value
 **********************************************************************************************************************/
uint32_t user_ether_phy_read_ethsw (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr)
{
    return 0;
}                                      /* End of function ether_phy_read_ethsw() */

/*******************************************************************************************************************//**
 * Reads a PHY register by ESC control
 *
 * @param[in]   p_instance_ctrl     Pointer to the control block for the channel
 * @param[in]   reg_addr            Address of the PHY register
 *
 * @retval      read value
 **********************************************************************************************************************/
uint32_t user_ether_phy_read_esc (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr)
{
    return 0;
}
/*******************************************************************************************************************//**
 * Writes to a PHY register
 *
 * @param[in]   p_instance_ctrl     Pointer to the control block for the channel
 * @param[in]   reg_addr            Address of the PHY register
 * @param[in]   data                Value
 *
 * @retval      None
 **********************************************************************************************************************/
void user_ether_phy_write (uint32_t port_num, uint32_t reg_addr, uint32_t data)
{
    ether_phy_extend_cfg_t * p_extend;
    ether_phy_instance_ctrl_t * p_ether_pyh_instance_ctrl;

    if (port_num == 1)
    {
        p_ether_pyh_instance_ctrl = (ether_phy_instance_ctrl_t *)&g_ether_phy1_ctrl;
        p_extend = (ether_phy_extend_cfg_t *) g_ether_phy1_ctrl.p_ether_phy_cfg->p_extend;
    }
    else
    {
        p_ether_pyh_instance_ctrl = (ether_phy_instance_ctrl_t *)&g_ether_phy0_ctrl;
        p_extend = (ether_phy_extend_cfg_t *) g_ether_phy0_ctrl.p_ether_phy_cfg->p_extend;
    }

    switch (p_extend->mdio_type)
    {
        case ETHER_PHY_MDIO_GMAC:
        {
            user_ether_phy_write_gmac(p_ether_pyh_instance_ctrl, reg_addr, data);
            break;
        }

        case ETHER_PHY_MDIO_ETHSW:
        {
            user_ether_phy_write_ethsw(p_ether_pyh_instance_ctrl, reg_addr, data);
            break;
        }

        case ETHER_PHY_MDIO_ESC:
        {
            user_ether_phy_write_esc(p_ether_pyh_instance_ctrl, reg_addr, data);
            break;
        }

        default:
        {
            break;
        }
    }
}                                      /* End of function ether_phy_write() */

/***********************************************************************************************************************
 * Writes to a PHY register by GMAC control
 *
 * @param[in]   p_instance_ctrl     Pointer to the control block for the channel
 * @param[in]   reg_addr            Address of the PHY register
 * @param[in]   data                value
 *
 * @retval      none
 **********************************************************************************************************************/
void user_ether_phy_write_gmac (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr, uint32_t data)
{
    volatile R_GMAC_Type * p_reg_etherc = (R_GMAC_Type *) p_instance_ctrl->p_reg_etherc;
    uint32_t               timeout;
    uint32_t               val_tmp;

    val_tmp =
        ((p_instance_ctrl->p_ether_phy_cfg->phy_lsi_address << USER_ETHER_PHY_GMII_ADDRESS_PA_SHIFT) &
                USER_ETHER_PHY_GMII_ADDRESS_PA_MASK) |
        ((reg_addr << USER_ETHER_PHY_GMII_ADDRESS_GR_SHIFT) & USER_ETHER_PHY_GMII_ADDRESS_GR_MASK) | ///< Phy reg address
        USER_ETHER_PHY_GMII_ADDRESS_CR |                                                        ///< Speed
        USER_ETHER_PHY_GMII_ADDRESS_GW |                                                        ///< Write
        USER_ETHER_PHY_GMII_ADDRESS_GB;                                                         ///< Busy

    p_reg_etherc->GMII_Data    = data;                                                     ///< Set write value
    p_reg_etherc->GMII_Address = val_tmp;                                                  ///< Write phy register

    /* calculate timeout based on current timestamp */
    timeout = USER_ETHER_PHY_TIMEOUT_COUNT;

    while (1)
    {
        /* wait write complete */
        if (!(p_reg_etherc->GMII_Address_b.GB))
        {
            /* Sucess */
            break;
        }

        if (0 == timeout)
        {
            /* Timeout */
            break;
        }

        timeout--;
    }
}   

/*******************************************************************************************************************//**
 * Writes to a PHY register by ETHSW control
 *
 * @param[in]   p_instance_ctrl     Pointer to the control block for the channel
 * @param[in]   reg_addr            Address of the PHY register
 * @param[in]   data                value
 *
 * @retval      none
 **********************************************************************************************************************/
void user_ether_phy_write_ethsw (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr, uint32_t data)
{

}                                      /* End of function ether_phy_write_ethsw() */

/*******************************************************************************************************************//**
 * Writes to a PHY register by ESC control
 *
 * @param[in]   p_instance_ctrl     Pointer to the control block for the channel
 * @param[in]   reg_addr            Address of the PHY register
 * @param[in]   data                value
 *
 * @retval      none
 **********************************************************************************************************************/
void user_ether_phy_write_esc (ether_phy_instance_ctrl_t * p_instance_ctrl, uint32_t reg_addr, uint32_t data)
{

}
/*******************************************************************************************************************//**
 * Writes to a PHY register by ESC control
 *
 * @param[in]   p_instance_ctrl     Pointer to the control block for the channel
 * @param[in]   reg_addr            Address of the PHY register
 * @param[in]   data                value
 *
 * @retval      none
 **********************************************************************************************************************/
uint16_t XMC_ECAT_ReadPhy(uint16_t phyAddr, uint16_t RegAType, uint16_t regAddr, uint16_t * regVal)
{
    int32_t res = 0;

    if (RegAType == 1)
    {
        res = (int32_t)user_ether_phy_read( ((phyAddr == 2) ? 1 : 0),  regAddr);
    }
    else
    {
        user_ether_phy_write( ((phyAddr == 2) ? 1 : 0),  0x1E, regAddr);
        __NOP();
        __NOP();
        __NOP();
        __NOP();
        __NOP();
        res = (int32_t)user_ether_phy_read( ((phyAddr == 2) ? 1 : 0),  0x1F);
    }

    if (res < 0)
    {
        return 1;
    }

    *regVal = (uint16_t)res;

    return 0;
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
uint16_t XMC_ECAT_WritePhy( uint16_t phyAddr,uint16_t RegAType, uint16_t regAddr, uint16_t regVal)
{
    if (RegAType == 1)
    {
        user_ether_phy_write( ((phyAddr == 2) ? 1 : 0),  regAddr, regVal);
    }
    else
    {
        user_ether_phy_write( ((phyAddr == 2) ? 1 : 0),  0x1E, regAddr);
        __NOP();
        __NOP();
        __NOP();
        __NOP();
        __NOP();
        user_ether_phy_write( ((phyAddr == 2) ? 1 : 0),  0x1F, regVal);
    }

    return 0;
}


/*******************************************************************************************************************//**
 * Reset phy 
 *  This function is called when Link lost before master link
 *
 * 
 **********************************************************************************************************************/
/* ESC_RESETOUT# PFC setting */
#define ETHERCAT_SSC_PORT_PFC_ESC_RESETOUT         (0x01U << 4U)
#define REG13_BIT11MASK  (0x01<< 11u)
UINT16 Phy1_0x1Value = 0;
UINT16 Phy2_0x1Value = 0;
UINT16 Phy1_0x13Value = 0;
UINT16 Phy2_0x13Value = 0;  
UINT32 PhyResetCnt = 0;  
void User_Phy_Reset (BOOL ForceReset)
{
    static UINT16  PhyResetStep = 0;
    static UINT32  StartTime = 0;
    UINT32 Endtime;
    
    
    XMC_ECAT_ReadPhy(2,1,0x01,&Phy1_0x1Value);
    XMC_ECAT_ReadPhy(1,1,0x01,&Phy2_0x1Value);  
    

      if(ForceReset == 1)
      {
         /* Re-assign P13_4 to GPIO# */
        R_IOPORT_PinCfg(g_ioport.p_ctrl, ETHERCAT_SSC_PORT_CFG_ESC_RESET_PORT,
                    ((uint32_t)IOPORT_CFG_DRIVE_UHIGH | (uint32_t) IOPORT_CFG_PORT_DIRECTION_OUTPUT | 
                    (uint32_t) IOPORT_CFG_SLEW_RATE_FAST| (uint32_t) IOPORT_CFG_PORT_OUTPUT_HIGH));  

        /* Write Low-output to ESC_RESETOUT# as PHY reset */
        R_BSP_PinAccessEnable();
        R_BSP_PinWrite((bsp_io_port_pin_t) ETHERCAT_SSC_PORT_CFG_ESC_RESET_PORT, BSP_IO_LEVEL_LOW);  
           
        /* Reset hold time */
        R_BSP_SoftwareDelay(100, BSP_DELAY_UNITS_MILLISECONDS);
        R_BSP_PinWrite((bsp_io_port_pin_t) ETHERCAT_SSC_PORT_CFG_ESC_RESET_PORT, BSP_IO_LEVEL_HIGH);  

        R_BSP_PinAccessDisable();

        /* Re-assign P20_7 to ESC_RESETOUT# */
        R_IOPORT_PinCfg(g_ioport.p_ctrl, ETHERCAT_SSC_PORT_CFG_ESC_RESET_PORT,
                        ((uint32_t) IOPORT_CFG_PORT_PERI | (uint32_t) ETHERCAT_SSC_PORT_PFC_ESC_RESETOUT));   
     }    
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
uint16_t ecatForceLink(void)
{
    uint16_t regVal;

//    if (XMC_ECAT_ReadPhy(0x01, 0x1F, &regVal))
//    {
//        return 1;
//    }
//
//    regVal |= 0x0800;
//    if (XMC_ECAT_WritePhy(0x01, 0x1F, regVal))
//    {
//        return 1;
//    }
//
//    if (XMC_ECAT_ReadPhy(0x02, 0x1F, &regVal))
//    {
//        return 1;
//    }
//
//    regVal |= 0x0800;
//    if (XMC_ECAT_WritePhy(0x02, 0x1F, regVal))
//    {
//        return 1;
//    }

    return 0;
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
uint16_t EcatWritePhyLedCtrlReg(void)
{
    #define PHY_EXTADRR_TYPE                0x0
    #define PHY_MIIADRR_TYPE                0x1
    #define PHY_MII_PHY_SPECIFIC_CONTROL    0x10   


    uint16_t time = 20;
    uint16_t timeCnt = 0;
    uint16_t regVal = 0;
    uint16_t regVal2 = 0;
    uint16_t port = 0;
    uint16_t delay_time = 5;
    
 
        for (timeCnt = 0; timeCnt < time; timeCnt++)
        {
            R_BSP_SoftwareDelay(15, BSP_DELAY_UNITS_MILLISECONDS);
            
            if (XMC_ECAT_ReadPhy(port, PHY_MIIADRR_TYPE,PHY_MII_PHY_SPECIFIC_CONTROL, &regVal) != 0 )
            {
                continue;
            }
            R_BSP_SoftwareDelay(delay_time, BSP_DELAY_UNITS_MILLISECONDS);

            regVal &= ~(1<<5);  
            regVal &= ~(1<<6);  

            XMC_ECAT_WritePhy(port, PHY_MIIADRR_TYPE,PHY_MII_PHY_SPECIFIC_CONTROL, regVal);
            R_BSP_SoftwareDelay(delay_time, BSP_DELAY_UNITS_MILLISECONDS);
            

            if (XMC_ECAT_ReadPhy(port, PHY_MIIADRR_TYPE,PHY_MII_PHY_SPECIFIC_CONTROL, &regVal2) != 0 )
            {
                continue;
            }
            else
            {
                if (regVal != regVal2)
                {
                    continue;
                }
                else
                {
                    break;
                }
            }
        }
        
        if (timeCnt >= time)
        {
            return 1;
        }
           
        
        port = 1;
        
        for (timeCnt = 0; timeCnt < time; timeCnt++)
        {
            R_BSP_SoftwareDelay(15, BSP_DELAY_UNITS_MILLISECONDS);
            
            if (XMC_ECAT_ReadPhy(port, PHY_MIIADRR_TYPE,PHY_MII_PHY_SPECIFIC_CONTROL, &regVal) != 0 )
            {
                continue;
            }
            R_BSP_SoftwareDelay(delay_time, BSP_DELAY_UNITS_MILLISECONDS);

            regVal |= (1<<5);  
            regVal &= ~(1<<6);  

            XMC_ECAT_WritePhy(port, PHY_MIIADRR_TYPE,PHY_MII_PHY_SPECIFIC_CONTROL, regVal);
            R_BSP_SoftwareDelay(delay_time, BSP_DELAY_UNITS_MILLISECONDS);
            

            if (XMC_ECAT_ReadPhy(port, PHY_MIIADRR_TYPE,PHY_MII_PHY_SPECIFIC_CONTROL, &regVal2) != 0 )
            {
                continue;
            }
            else
            {
                if (regVal != regVal2)
                {
                    continue;
                }
                else
                {
                    break;
                }
            }
        }

        if (timeCnt >= time)
        {
            return 1;
        }
    

    
        return 0;
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * RETURNS:
 *
****************************************************************************************************/
uint32_t EcatErrRegRead(uint8_t regtype, uint8_t port)
{
   uint32_t result; 
    switch(regtype)
    {
      case ECAT_INFECNT:
        result = R_ESC->RX_ERR_COUNT_b[port].INVFRMCNT;
        break;
      case ECAT_RXFECNT:
        result = R_ESC->RX_ERR_COUNT_b[port].RXERRCNT;
        break;    
      case ECAT_FWDRFECNT:
        result = R_ESC->FWD_RX_ERR_COUNT_b[port].FWDERRCNT;
        break;    
      case ECAT_PROCECNT:
        result = R_ESC->ECAT_PROC_ERR_COUNT_b.EPUERRCNT;
        break;   
      case ECAT_PIDECNT:
        result = R_ESC->PDI_ERR_COUNT_b.PDIERRCNT;
        break;       
      case ECAT_LSOTECNT:
        result = R_ESC->LOST_LINK_COUNT_b[port].LOSTLINKCNT;
        break;  
      case ECAT_DLCONTROL:
        result = R_ESC->ESC_DL_CONTROL;
        break;  
      case ECAT_DLSTATUS:
        result = R_ESC->ESC_DL_STATUS;
        break;  
    }  
    return result;
       
}
