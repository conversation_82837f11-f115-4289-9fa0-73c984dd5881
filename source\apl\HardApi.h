/****************************************************************************************************
 *
 * FILE NAME:  HardApi.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _HARDAPI_H_
#define _HARDAPI_H_
#include "BaseDef.h"
#include "hal_data.h"

/* 通用DIDO 引脚宏定义 */
#define   PORT_DI1  BSP_IO_PORT_03_PIN_0
#define   PORT_DI2  BSP_IO_PORT_03_PIN_6
#define   PORT_DI3  BSP_IO_PORT_03_PIN_7
#define   PORT_DI4  BSP_IO_PORT_18_PIN_1

#define   PORT_DO1  BSP_IO_PORT_01_PIN_5
#define   PORT_DO2  BSP_IO_PORT_05_PIN_1
#define   PORT_DO3  BSP_IO_PORT_22_PIN_3
#define   PORT_DO4  BSP_IO_PORT_21_PIN_3

/* STO1和STO2 引脚宏定义 */
#define   PORT_STO1   BSP_IO_PORT_22_PIN_2
#define   PORT_STO2   BSP_IO_PORT_03_PIN_5

/* Acloss 引脚宏定义 */
#define   PORT_ACLOSS BSP_IO_PORT_05_PIN_0

/* 动态制动 引脚宏定义 */
#define   PORT_DB1   BSP_IO_PORT_12_PIN_4    /* active lvl: low_level */
#define   PORT_DB2   BSP_IO_PORT_15_PIN_2    /* active lvl: low_level */

/* 泄放控制 引脚宏定义 */
#define   PROT_BKFB     BSP_IO_PORT_14_PIN_2
#define   PROT_BKCTRL   BSP_IO_PORT_14_PIN_3  /* active lvl: low_level */

/* PWM使能和软启继电器 引脚宏定义 */
#define   PORT_PWMEN1    BSP_IO_PORT_05_PIN_2
#define   PORT_RELAY     BSP_IO_PORT_15_PIN_5  /* active lvl: low_level */

/* 485总线、上位机485、编码器1/2的485 控制引脚宏定义 */
#define   PORT_485SZ_DE  BSP_IO_PORT_15_PIN_6
#define   PORT_485SS_DE  BSP_IO_PORT_15_PIN_7
#define   PROT_ENC1DE  BSP_IO_PORT_04_PIN_5
#define   PROT_ENC2DE  BSP_IO_PORT_00_PIN_1

/* 芯片间 引脚宏定义 */
#define   PROT_RESIO1  BSP_IO_PORT_14_PIN_6
#define   PROT_RESIO2  BSP_IO_PORT_15_PIN_1
#define   PROT_RESIO3  BSP_IO_PORT_17_PIN_5
#define   PROT_RESIO4  BSP_IO_PORT_17_PIN_6
#define   PROT_RESIO5  BSP_IO_PORT_02_PIN_0

#define  PORT_LED1_GR  BSP_IO_PORT_14_PIN_7       //Axia1-Led green
#define  PORT_LED1_RD  BSP_IO_PORT_04_PIN_4       //Axia1-Led red
/* 风扇控制 引脚宏定义 */
#define   PROT_FANCT  BSP_IO_PORT_02_PIN_3  /* active lvl: low_level */


#define   PIN_LEVEL_HIGH  BSP_IO_LEVEL_HIGH
#define   PIN_LEVEL_LOW   BSP_IO_LEVEL_LOW


extern UINT16 MCU_DEVICE_ID;


#define MCU_DEVICE_ID_0  0x00
#define MCU_DEVICE_ID_1  0x01
#define MCU_DEVICE_ID_2  0x10

#define KLIB_EEPDISABLE_TIME		100		/* 100ms */


#define PWM_MTU0_PRD_CFG              0
#define PWM_MTU0_CMP_INT_CFG          1
#define PWM_MTU0_CMP_CAP_CFG          2
#define PWM_MTU3_PRD_CFG              3
#define PWM_MTU3_DEAD_CFG             4
#define PWM_TEST_DUTY_CFG             5
#define PWM_PHASE_CHECK_DUTY_CFG      6
#define PWM_CHARGE_PUMP_ON_DUTY_CFG   7
#define PWM_ADC_OFFSET_CHECK_DUTY_CFG 8 


/* PWM Control WORD*/
typedef struct
{	
	UINT16  ChargePumpOn:1;		
	UINT16  BaseEnable:1;
    UINT16  PhaCheckPumpOn:1;
	UINT16  Resv:13;				

}PWM_Control_BIT;

typedef union
{
	UINT16           all;
	PWM_Control_BIT  bit;
}PWM_Control_WORD;



typedef union
{	
    struct
    {
        UINT16 Mtu0PrdCnt;      // MTU0_PRD_CNT
        UINT16 Mtu0CmpIntCnt;   // MTU0_CMP_INT_CNT
        UINT16 Mtu0CmpCapCnt;   // MTU0_CMP_CAP_CNT
        UINT16 Mtu3PrdCnt;      // MTU3_PRD_CNT
        UINT16 Mtu3DeadCnt;     // MTU3_DEAD_CNT
        UINT16 PwmTestDuty;    // PwmTestDuty
        UINT16 PhaCheckDuty;   // PhaCheckDuty
        UINT16 ChargePumpOnDuty; // ChargePumpOnDuty
        UINT16 AdcOffsetCheckDuty; // AdcOffsetCheckDuty
    };  
    UINT16 Data[9];

}PWM_CTRL_SETTING;

/*--------------------------------------------------------------------------------------------------*/
/*    hardware status register bit union    			        				                    */
/*--------------------------------------------------------------------------------------------------*/
typedef union
{
    UINT16 all;
    struct
    {
		UINT16 IpmA:1;                         // IPM Fault(axis A)
		UINT16 IpmB:1;                         // IPM Fault(axis B)
		UINT16 STO1:1;                         // STO1
		UINT16 STO2:1;                         // STO2
		UINT16 DI1:1;                          // DI1
		UINT16 PWM_STS:1;                      // PWM Status
		UINT16 Acl:1;                          // Ac signal loss
		UINT16 RegenFdb:1;                     // Regen control feedback
		UINT16 RegenErr:1;                     // Regen control error	
        UINT16 BusOC:1;                        // Bus over current     
		UINT16 Resv:6;
    } bits;
}HW_STATUS_REG;


typedef union
{
    UINT16 all;
    struct
    {
        UINT16 DI1:1;                          // DI0
        UINT16 DI2:1;                          // DI1     
        UINT16 DI3:1;                          // DI2
        UINT16 DI4:1;                          // DI3        
		UINT16 Resv:12;
    } bits;
}DI_STATUS_REG;


typedef union
{
    UINT16 all;
    struct
    {
        UINT16 DO1:1;                          // DO0
        UINT16 DO2:1;                          // DO1     
        UINT16 DO3:1;                          // DO2
        UINT16 DO4:1;                          // DO3        
		UINT16 Resv:12;
    } bits;
}Send_STATUS_REG;




/* Time stamp module */
extern PUBLIC void hApi_SystemReset(void);
extern PUBLIC void hApi_TimerCounterInit(void);
extern PUBLIC UINT32 hApi_GetTimerCounter(void);
extern PUBLIC void KlibCountTimeStamp(UINT32 tick_in_msec, BOOL PowerOn );
extern PUBLIC UINT32 hApi_GetTimeStamp1000ms(void);
extern PUBLIC UINT32 hApi_GetActive1000ms(void);
extern PUBLIC void KlibRstLongTimer( UINT32 *timer );
extern PUBLIC UINT32 KlibGetLongTimerMs( UINT32 timer );
extern PUBLIC INT32 KlibGetTimeAfterPowerOn( void );
extern PUBLIC INT32 KlibGetMainPowerOffTimer( void );

/* PWM module */
extern PUBLIC void hApi_PwmInit(UINT16 AxisID);
extern PUBLIC UINT16 hApi_GetPwmCfg(UINT16 AxisID, UINT16 Type);
extern PUBLIC void hApi_SetPwmCfg(UINT16 AxisID, UINT16 Type, UINT16 Value);
extern PUBLIC void hApi_PwmEnable(UINT16 AxisID);
extern PUBLIC void hApi_PwmDisable(UINT16 AxisID);
extern PUBLIC void hApi_PwmUpdate(UINT16 AxisID, UINT16 Ta, UINT16 Tb, UINT16 Tc);
extern PUBLIC void hApi_PwmUpdateTest(UINT16 AxisID, UINT16 Ta);

extern PUBLIC void hApi_SetTaskBInterrupt(void);
extern PUBLIC void hApi_SetTaskCInterrupt(void);

/* ADC module */
extern PUBLIC BOOL hApi_AdcOffsetCheck(void);
extern PUBLIC UINT16 hApi_GetAdcInitState(UINT16 AxisID);
extern PUBLIC void hApi_GetPhaseCurrent(UINT16 AxisID, float *Ia, float *Ib, float *Ic, float kx);
extern PUBLIC INT32 hApi_VoltAdcStart(void);
extern PUBLIC INT32 hApi_GetDcVoltage(void);
extern PUBLIC UINT32 hApi_GetVdcFilo(void);
extern PUBLIC INT32 hApi_Adc121Start(void);
extern PUBLIC INT16 hApi_GetEnvTemp(INT16 *IPMTemp); 
extern PUBLIC INT16 hApi_GetMotTemp(INT16 *MotTemp);
PUBLIC BOOL hApi_GetOTM(UINT16 AxisID);
extern PUBLIC void hApi_GetAnalogInput1(UINT16 *AnalogInput);
extern PUBLIC void hApi_GetAnalogInput2(UINT16 *AnalogInput);
extern PUBLIC BOOL hApi_AnalogInputOffsetCheck(UINT16 *AnalogOffset1,UINT16 *AnalogOffset2);

extern PUBLIC UINT32 hApi_CurAdcStart(void);
extern PUBLIC UINT32 hApi_CheckAdcEnd(void);

extern PUBLIC void hApi_ChargePumpOn(UINT16 AxisID);
extern PUBLIC void hApi_ChargePumpOff(UINT16 AxisID);
extern PUBLIC void hApi_BaseEnable(UINT16 AxisID);
extern PUBLIC void hApi_BaseBlock(UINT16 AxisID);

extern PUBLIC void hApi_RelayControlOn(UINT16 AxisID);
extern PUBLIC void hApi_RelayControlOff(UINT16 AxisID);
extern PUBLIC void hApi_DynamicBrakeOn(UINT16 AxisID);
extern PUBLIC void hApi_DynamicBrakeOff(UINT16 AxisID);
extern PUBLIC void hApi_RegeneCtrlOn(void);
extern PUBLIC void hApi_RegeneCtrlOff(void);
extern PUBLIC BOOL hApi_GetRegeneCtrlStatue(void);

extern PUBLIC void hApi_BrakeCtrlBrake(UINT16 AxisID);
extern PUBLIC void hApi_BrakeCtrlRelease(UINT16 AxisID);

extern PUBLIC void hApi_FanCtrl(BOOL BaseEnable, UINT16 Duty);


extern PUBLIC UINT16 hApi_GetHwSignal(void);  
extern PUBLIC UINT16 hApi_GetBusOcSignal(void);  
extern PUBLIC void hApi_delay_us(UINT32 nus);
extern PUBLIC void hApi_delay_ms(UINT32 mus);

extern PUBLIC UINT16 hApi_UpdataDI(UINT16 AxisID);   
extern PUBLIC void hApi_DOSetHigh(UINT16 Index);
extern PUBLIC void hApi_DOSetLow(UINT16 Index);

extern PUBLIC UINT16 hApi_GetAxisCobID(UINT16 AxisID);  
extern PUBLIC UINT8 hApi_GetCurSmpKx (REAL32 *Kx, UINT16 *DeadTime, UINT16 AxisCobID, UINT16 HeadwareVer);   
extern PUBLIC void hApi_DeadTimeReset(UINT16 DeadTime, UINT16 AxisID);

/* Encoder module */
extern PUBLIC void hApi_IncEncoderInit(UINT16 Mode, UINT16 AxisID);
extern PUBLIC UINT32 hApi_GetIncEncoderPulse(UINT16 AxisID);
extern PUBLIC UINT32 hApi_GetCtrlPulse(void);
extern PUBLIC void hApi_ClearCtrlPulse(void);
extern PUBLIC void hApi_ClearIncEncoderPulse(UINT16 AxisID);
extern PUBLIC void hApi_CtrlPulseInit(UINT16 Mode, UINT16 AxisID);
extern PUBLIC INT32 hApi_GetPluseInCnt(UINT16 AxisID);
extern PUBLIC UINT16 hApi_GetSync0(void)   ;

extern  PUBLIC void hApi_DisableInterrupt(void);

extern  PUBLIC BOOL hApi_DetectUsb(void);
extern   PUBLIC UINT32 hApi_ReadEcatErrReg(UINT8 RegType,UINT8 port);
extern  PUBLIC UINT16 hApi_DevicenName(void);


extern PUBLIC void hApi_PhaCheckPumpOn(UINT16 AxisID);
extern PUBLIC void hApi_PhaCheckPumpOff(UINT16 AxisID);

#endif  // _HARDAPI_H_