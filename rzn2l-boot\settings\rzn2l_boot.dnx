<?xml version="1.0"?>
<settings>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <PlDriver>
        <FirstRun>0</FirstRun>
        <MemConfigValue>C:\Program Files\IAR Systems\arm\config\debugger\Renesas\R9A07G084M04.ddf</MemConfigValue>
    </PlDriver>
    <Jet>
        <PrevWtdReset>Hardware</PrevWtdReset>
        <OnlineReset>Hardware</OnlineReset>
        <JetConnSerialNo>14978</JetConnSerialNo>
        <JetConnFoundProbes />
        <DisableInterrupts>0</DisableInterrupts>
        <LeaveRunning>0</LeaveRunning>
        <MultiCoreRunAll>0</MultiCoreRunAll>
        <CpuHaltOnBreakpointSet>0</CpuHaltOnBreakpointSet>
    </Jet>
    <ArmDriver>
        <EnforceMemoryConfiguration>1</EnforceMemoryConfiguration>
        <EnableCache>1</EnableCache>
    </ArmDriver>
    <JLinkDriver>
        <WatchCond>_ 0</WatchCond>
        <Watch0>_ 0 "" 0 "" 0 "" 0 "" 0 0 0 0</Watch0>
        <Watch1>_ 0 "" 0 "" 0 "" 0 "" 0 0 0 0</Watch1>
        <jlinkResetStyle>12</jlinkResetStyle>
        <jlinkResetStrategy>0</jlinkResetStrategy>
        <LeaveTargetRunning>_ 0</LeaveTargetRunning>
        <CStepIntDis>_ 0</CStepIntDis>
    </JLinkDriver>
    <Exceptions>
        <StopOnUncaught>_ 0</StopOnUncaught>
        <StopOnThrow>_ 0</StopOnThrow>
    </Exceptions>
    <DebugChecksum>
        <Checksum>1627180031</Checksum>
    </DebugChecksum>
    <Disassembly>
        <MixedMode>1</MixedMode>
        <InstrCount>0</InstrCount>
    </Disassembly>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <SWOManager>
        <SamplingDivider>8192</SamplingDivider>
        <OverrideClock>0</OverrideClock>
        <CpuClock>8102938293808082481</CpuClock>
        <SwoClock>7308890649680765793</SwoClock>
        <DataLogMode>0</DataLogMode>
        <ItmPortsEnabled>63</ItmPortsEnabled>
        <ItmTermIOPorts>1</ItmTermIOPorts>
        <ItmLogPorts>0</ItmLogPorts>
        <ItmLogFile>$PROJ_DIR$\ITM.log</ItmLogFile>
        <PowerForcePC>1</PowerForcePC>
        <PowerConnectPC>1</PowerConnectPC>
    </SWOManager>
    <SfrWindow>
        <Show>1 1</Show>
        <Sort>4 0</Sort>
    </SfrWindow>
    <PowerLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <Title_0>ITrgPwr</Title_0>
        <Symbol_0>0 4 0</Symbol_0>
        <LiveEnabled>0</LiveEnabled>
        <LiveFile />
    </PowerLog>
    <PowerProbe>
        <Frequency>10000</Frequency>
        <Probe0>ITrgPwr</Probe0>
        <ProbeSetup0>2 1 1 2 0 0</ProbeSetup0>
    </PowerProbe>
    <TerminalIO>
        <InputSource>1</InputSource>
        <InputMode2>10</InputMode2>
        <Filename>$PROJ_DIR$\TermIOInput.txt</Filename>
        <InputEcho>1</InputEcho>
        <ShowReset>0</ShowReset>
        <InputEncodingICU>0</InputEncodingICU>
        <OutputEncodingICU>0</OutputEncodingICU>
    </TerminalIO>
    <struct_types>
        <Fmt0>loader_table-datadst	4	0</Fmt0>
        <Fmt1>loader_table-datasrc	4	0</Fmt1>
        <Fmt2>loader_table-prgdst	4	0</Fmt2>
        <Fmt3>loader_table-prgsize	4	0</Fmt3>
        <Fmt4>loader_table-prgsrc	4	0</Fmt4>
    </struct_types>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <ETMTraceWindow>
        <PortWidth>4</PortWidth>
        <PortMode>0</PortMode>
        <CaptureDataValues>0</CaptureDataValues>
        <CaptureDataAddresses>0</CaptureDataAddresses>
        <CaptureDataRange>0</CaptureDataRange>
        <DataFirst>0</DataFirst>
        <DataLast>4294967295</DataLast>
        <StopWhen>0</StopWhen>
        <StallCPU>0</StallCPU>
        <NoPCCapture>0</NoPCCapture>
    </ETMTraceWindow>
    <Trace2>
        <Enabled>0</Enabled>
        <ShowSource>0</ShowSource>
    </Trace2>
    <SWOTraceWindow>
        <ForcedPcSampling>0</ForcedPcSampling>
        <ForcedInterruptLogs>0</ForcedInterruptLogs>
        <ForcedItmLogs>0</ForcedItmLogs>
        <EventCPI>0</EventCPI>
        <EventEXC>0</EventEXC>
        <EventFOLD>0</EventFOLD>
        <EventLSU>0</EventLSU>
        <EventSLEEP>0</EventSLEEP>
    </SWOTraceWindow>
    <DriverProfiling>
        <Enabled>0</Enabled>
        <Mode>2</Mode>
        <Graph>0</Graph>
        <Symbiont>0</Symbiont>
        <Exclusions />
    </DriverProfiling>
    <CallStackLog>
        <Enabled>0</Enabled>
    </CallStackLog>
    <CallStackStripe>
        <ShowTiming>1</ShowTiming>
    </CallStackStripe>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <DisassembleMode>
        <mode>0</mode>
    </DisassembleMode>
    <Breakpoints2>
        <Bp0>_ 0 "EMUL_CODE" "{$PROJ_DIR$\source\bsp\bsp_usb.c}.337.13" 0 0 1 "" 0 "" 0</Bp0>
        <Bp1>_ 1 "EMUL_CODE" "{$PROJ_DIR$\src\hal_entry.c}.89.5" 0 0 1 "" 0 "" 0</Bp1>
        <Count>2</Count>
    </Breakpoints2>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
</settings>
