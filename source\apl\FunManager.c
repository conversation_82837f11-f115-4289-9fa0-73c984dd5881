/****************************************************************************************************
 *
 * FILE NAME:  FunManager.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/

#include "BaseDef.h"
#include "FunManager.h"
#include "Mlib.h"

/****************************************************************************************************
 * DESCRIPTION:
 *		Initialization of this module
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void FunInitialize(FUN_MANAGER *FunMngr, void *RegMngr,
				   FUN_AXCOMMON *FunAxCommon, CPFUNTBL FunTable, UINT32 TableNum)
{

	MlibResetByteMemory(FunMngr, sizeof(FUN_MANAGER));
	FunMngr->FunTable = FunTable;
	FunMngr->NumOfFunTable = TableNum;
	FunMngr->AxCommon = FunAxCommon;
	FunMngr->RegManager = RegMngr;

	RegMngrFreeWriteOpeRegister(RegMngr);

}

/****************************************************************************************************
* DESCRIPTION:
*	   Set the Operation mode.
* RETURNS:
*
****************************************************************************************************/
PRM_RSLT FunSetOpeModeRegister(FUN_MANAGER *FunMngr, FUN_EXECH ch, UINT32 mode, void* ClbkParam)
{
	CPFUNTBL	cFunTable, FindFnTbl;
	PRM_RSLT	err;
	UINT32		i;

	/* Check the mode value */
	if(mode == 0)
	{
		cFunTable = FunMngr->cFunMode[ch];
		if( (cFunTable != NULL)
			&& (NULL != cFunTable->FunDefAttr->LeaveFunc) )
		{	/* Clear Opration mode. */
			cFunTable->FunDefAttr->LeaveFunc(&FunMngr->FunExeState[ch], ClbkParam);
		}

		FunMngr->cFunMode[ch] = NULL;
		FunMngr->AxCommon->modeLock = FALSE;

		/* Unlock the register write access */
		if( (FunMngr->AxCommon->TraceMode == 0)
			&& (FunMngr->cFunMode[0] == NULL) && (FunMngr->cFunMode[1] == NULL) )
		{
			RegMngrFreeWriteOpeRegister((REG_MANAGER_HANDLE*)FunMngr->RegManager);
		}
		return PRM_RSLT_SUCCESS;
	}
	else
	{
		if(FunMngr->cFunMode[ch] != NULL)
		{	/* mode setting error */
			err = (FunMngr->cFunMode[ch]->FnMode != (UINT16)mode) ?
					PRM_RSLT_CONDITION_ERR : PRM_RSLT_SUCCESS;
			return err;
		}
	}

	/* Search function mode from the Function list table */
	FindFnTbl = NULL;
	err = PRM_RSLT_LIMIT_ERR;
	for(i = 0; i < FunMngr->NumOfFunTable; i++)
	{
		cFunTable = &FunMngr->FunTable[i];
		if( (cFunTable->FnMode == (UINT16)mode)
			&& (FunMngr->AxCommon->AccessLevel >= cFunTable->FunDefAttr->AccessLevel) )
		{
			FindFnTbl = cFunTable;
			break;
		}
	}

	/* Sets an operation mode */
	if(FindFnTbl != NULL)
	{
		/* Axis-dependent check */
		if(FunMngr->AxCommon->modeLock != FALSE)
		{
			return PRM_RSLT_CONDITION_ERR;
		}
		/* Operation mode simultaneous execution check */
		cFunTable = FunMngr->cFunMode[((INT32)ch+1)&1];
		if(cFunTable != NULL)
		{
			if(3 != (cFunTable->FunDefAttr->RunLevel + FindFnTbl->FunDefAttr->RunLevel))
			{	/*Only the combination of level 1 and 2 is possible*/
				return PRM_RSLT_CONDITION_ERR;
			}
		}
		/* Trace operation mode simultaneous execution check */
		if( (FunMngr->AxCommon->TraceMode != 0)
			&& (FALSE == FindFnTbl->FunDefAttr->ParaTrace) )
		{
			return PRM_RSLT_CONDITION_ERR;
		}

		/* Parameter write protection status check */
		if((FindFnTbl->FunDefAttr->ExePrmProhibit == FALSE)
			&& (FALSE != RegMngrGetWritePrmAccessLock((REG_MANAGER_HANDLE*)FunMngr->RegManager)) )
		{
			return PRM_RSLT_RWACC_ERR;
		}

		/* Operation register write access lock */
		RegMngrLockWriteOpeRegister((REG_MANAGER_HANDLE*)FunMngr->RegManager);

		/* Initialize the Executing state */
		MlibResetByteMemory(&FunMngr->FunExeState[ch], sizeof(FUNEXE));
		FunMngr->FunExeState[ch].CmdCode = FCMD_NONE;

		/* Call the registerd callback function */
		err = PRM_RSLT_SUCCESS;
		if(NULL != FindFnTbl->FunDefAttr->EnterFunc)
		{
			err = FindFnTbl->FunDefAttr->EnterFunc(
								&FunMngr->FunExeState[ch], ClbkParam);
		}

		if(err == PRM_RSLT_SUCCESS)
		{	/* Set a executing mode */
			FunMngr->AxCommon->modeLock = FindFnTbl->FunDefAttr->AxCommon;
			FunMngr->CallbackParam = ClbkParam;
			FunMngr->cFunMode[ch] = FindFnTbl;
		}
	}

	return err;
}


/****************************************************************************************************
* DESCRIPTION:
*	    Set the Trace mode
* RETURNS:
*
****************************************************************************************************/
PRM_RSLT FunSetTraceModeRegister(FUN_MANAGER *FunMngr, UINT32 mode)
{
	CPFUNTBL	cFunTbl1, cFunTbl2;
	PRM_RSLT	err;

	err = PRM_RSLT_SUCCESS;
	if(mode == 0)
	{	/* Clear opration mode. */
		FunMngr->AxCommon->TraceMode = 0;
		/* Unlock the register write access */
		if((FunMngr->cFunMode[0] == NULL) && (FunMngr->cFunMode[1] == NULL))
		{
			RegMngrFreeWriteOpeRegister((REG_MANAGER_HANDLE*)FunMngr->RegManager);
		}
	}
	else
	{
		if(FunMngr->AxCommon->TraceMode != 0)
		{	/* already in executing */
			err = PRM_RSLT_CONDITION_ERR;
		}
		else
		{
			/* Operation mode simultaneous execution check */
			cFunTbl1 = FunMngr->cFunMode[0];
			cFunTbl2 = FunMngr->cFunMode[1];
			if( ((cFunTbl1 != NULL) && (cFunTbl1->FunDefAttr->ParaTrace == FALSE))
				|| ((cFunTbl2 != NULL) && (cFunTbl2->FunDefAttr->ParaTrace == FALSE)) )
			{
				err = PRM_RSLT_CONDITION_ERR;
			}
			else
			{
				FunMngr->AxCommon->TraceMode = mode;
				/* Operation register write access lock */
				RegMngrLockWriteOpeRegister((REG_MANAGER_HANDLE*)FunMngr->RegManager);
			}
		}
	}

	return err;
}


/****************************************************************************************************
* DESCRIPTION:
*	    Set the operation command value
* RETURNS:
*
****************************************************************************************************/
PRM_RSLT FunSetOpeCommandRegister(FUN_MANAGER *FunMngr, FUN_EXECH ch, UINT32 cmd)
{
	PRM_RSLT err;

	/* Check that Function mode was already set. */
	if(FunMngr->cFunMode[ch] == NULL)
	{
		err = PRM_RSLT_CONDITION_ERR;
	}
	else
	{
		FunMngr->FunExeState[ch].CmdCode = (FCMD_CODE)cmd;
		FunRunTimeService(FunMngr, ch);
		err = FunMngr->FunExeState[ch].ErrorResult;
	}
	return err;
}

/****************************************************************************************************
* DESCRIPTION:
*	    Execute Utility Function
* RETURNS:
*
****************************************************************************************************/
PUBLIC void FunRunTimeService(FUN_MANAGER *FunMngr, FUN_EXECH ch)
{
	CPFUNTBL	cFunTable;
	PRM_RSLT	err;

	cFunTable = FunMngr->cFunMode[ch];
	if(cFunTable != NULL)
	{
		if(NULL != cFunTable->FunDefAttr->ExecuteFunc)
		{
			err = cFunTable->FunDefAttr->ExecuteFunc(&FunMngr->FunExeState[ch],
												 FunMngr->CallbackParam);

			FunMngr->FunExeState[ch].ErrorResult = err;
			FunMngr->FunExeState[ch].CmdCode = FCMD_NONE;
			RegMngrSetWriteOpeRegStatus((REG_MANAGER_HANDLE*)FunMngr->RegManager, err);

			if((err != PRM_RSLT_CONTINUE)
				&& (FunMngr->FunExeState[ch].HoldFnMode == FALSE))
			{
				/* Finish the function */
				FunMngr->cFunMode[ch] = NULL;
				FunMngr->FunExeState[ch].CmdCode = FCMD_NONE;
				FunMngr->FunExeState[ch].State = 0;
				FunMngr->AxCommon->modeLock = FALSE;

				/* Unlock the register write access */
				if( (FunMngr->AxCommon->TraceMode == 0)
					&& (FunMngr->cFunMode[0] == NULL)
					&& (FunMngr->cFunMode[1] == NULL) )
				{
					RegMngrFreeWriteOpeRegister((REG_MANAGER_HANDLE*)FunMngr->RegManager);
				}
			}
		}
	}
}


/****************************************************************************************************
* DESCRIPTION:
*	    Get the operation mode value
* RETURNS:
*
****************************************************************************************************/
PUBLIC UINT16 FunGetOpeModeRegister(FUN_MANAGER *FunMngr, FUN_EXECH ch)
{
	UINT16 mode;
	mode = 0;
	if(FunMngr->cFunMode[ch] != NULL)
	{
		mode = FunMngr->cFunMode[ch]->FnMode;
	}
	return mode;
}



