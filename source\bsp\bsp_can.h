#ifndef __BSP_CAN_H__
#define __BSP_CAN_H__

#include "r_can_api.h"


uint32_t Canfd_GetCh0SR (void);
uint32_t Canfd_GetCh0ER (void);
uint32_t Canfd_GetCh0Gl0SR (void);
uint32_t Canfd_GetCh0Gl0ER (void);
uint32_t Canfd_GetCh0TQ0SR (void);
uint32_t Canfd_GetCh0CFSR (void);



typedef struct {
  uint16_t cob_id;	/**< message's ID */
  uint8_t  rtr;		/**< remote transmission request. (0 if not rtr message, 1 if rtr message) */
  uint8_t  len;		/**< message's length (0 to 8) */
  uint8_t  data[8]; /**< message's datas */
} Message_Can;


void CanInit(uint16_t BaudRate);
void CanSetAfl(uint16_t SlaveId);

#endif

