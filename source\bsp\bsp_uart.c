#include "hal_data.h"
#include "bsp_uart.h"
#include "ComUart.h"
#include "bsp_timer.h"

void dmac_callback(dmac_callback_args_t *p_args);

volatile uint8_t *       gp_sci0_rx_address;   /* SCIFA0 receive buffer address */
uint16_t        g_sci0_rx_count;      /* SCIFA0 receive data number */
uint16_t        g_sci0_rx_length;     /* SCIFA0 receive data length */

volatile uint8_t *       gp_sci0_tx_address;   /* SCIFA0 receive buffer address */
uint16_t        g_sci0_tx_count;      /* SCIFA0 receive data number */
uint16_t        g_sci0_tx_length;     /* SCIFA0 receive data length */

volatile uint8_t *       gp_sci4_rx_address;   /* SCIFA0 receive buffer address */
uint16_t        g_sci4_rx_count;      /* SCIFA0 receive data number */
uint16_t        g_sci4_rx_length;     /* SCIFA0 receive data length */

volatile uint8_t *       gp_sci4_tx_address;   /* SCIFA0 receive buffer address */
uint16_t        g_sci4_tx_count;      /* SCIFA0 receive data number */
uint16_t        g_sci4_tx_length;     /* SCIFA0 receive data length */

extern ComUartStruct  CommandRecv;
extern ComUartStruct  CommandSend;

extern UINT32 gDevComId;

static uint32_t  sci_buadtrate_table[7] = {4800, 9600, 19200, 38400 ,57600, 115200, 2500000};

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 调试用SCI0初始化，程序开启了GIC级的TEI，TXI中断(外设级需要再发送数据的时候开启), 开启了RXI中断(GIC级中断被禁止，触发DMA传输时需要外设中断标志)
 * @param buadrate  - 波特率，目前支持(4800/9600/19200/38400/57600/115200/2500000)
 * @param format    - 数据格式，0-8N1,1-8N2，2-8E1，3-8E2，4-8O1,5-8O2
 * RETURNS:
 *
****************************************************************************************************/ 
static uint32_t bsp_sci_buadtrate_select(uint16_t buadratelvl)
{
  uint32_t buadrate;
 
  if(buadratelvl < 7)
  {
    buadrate = sci_buadtrate_table[buadratelvl];
  }
  else
  {
    buadrate = 115200;
  }
  return  buadrate;
}
 
 
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 调试用SCI0初始化，程序开启了GIC级的TEI，TXI中断(外设级需要再发送数据的时候开启), 开启了RXI中断(GIC级中断被禁止，触发DMA传输时需要外设中断标志)
 * @param buadrate  - 波特率，目前支持(4800/9600/19200/38400/57600/115200/2500000)
 * @param format    - 数据格式，0-8N1,1-8N2，2-8E1，3-8E2，4-8O1,5-8O2
 * RETURNS:
 *
****************************************************************************************************/ 
 void bsp_sci_buadtrate(R_SCI0_Type * regBase, uint32_t buadrate)
{
    /* 复位默认值 */
    regBase->CCR2 = 0xFF00FF04;

    /* 位速率调制功能/波特率小数分频功能使能，1-使能 */
    regBase->CCR2_b.BRME = 1;
    /* 0-禁止外部时钟 */
    regBase->CCR2_b.ABCSE = 0;

    /* 波特率寄存器设置, 输入时钟为96MHz
     * PCLKSCIx = 96MHz
     * BBR = ( (PCLKSCIx * 10000000) / (64 * 2^(2*n-1) * B) ) - 1
     * BBR  : 寄存器设定值
     * B    : 波特率值
     * n    : 时钟源分频设定值
     * 计算公式基于一个bit采样时钟16个，单速率模式下
     */
    switch (buadrate)
    {
    case 4800UL:
        /* 波特率生成双速率模式使能，1-使能 */
        regBase->CCR2_b.BGDM = 1;
        /* 0-16个时钟发送一个bit，1-8个时钟发送一个bit */
        regBase->CCR2_b.ABCS = 0;
        /* 波特率计数器 */
        regBase->CCR2_b.BRR = 64;
        /* 输入时钟源分频选择，0-部分品，1-4分频，2-16分频，3-64分频 */
        regBase->CCR2_b.CKS = 2;
        /* 调制占空比设置 */
        regBase->CCR2_b.MDDR = 213;
        break;

    case 9600UL:
        /* 波特率生成双速率模式使能，1-使能 */
        regBase->CCR2_b.BGDM = 0;
        /* 0-16个时钟发送一个bit，1-8个时钟发送一个bit */
        regBase->CCR2_b.ABCS = 0;
        /* 波特率计数器 */
        regBase->CCR2_b.BRR = 176;
        /* 输入时钟源分频选择，0-部分品，1-4分频，2-16分频，3-64分频 */
        regBase->CCR2_b.CKS = 0;
        /* 调制占空比设置 */
        regBase->CCR2_b.MDDR = 145;
        break;

    case 19200UL:
        /* 波特率生成双速率模式使能，1-使能 */
        regBase->CCR2_b.BGDM = 1;
        /* 0-16个时钟发送一个bit，1-8个时钟发送一个bit */
        regBase->CCR2_b.ABCS = 0;
        /* 波特率计数器 */
        regBase->CCR2_b.BRR = 176;
        /* 输入时钟源分频选择，0-部分品，1-4分频，2-16分频，3-64分频 */
        regBase->CCR2_b.CKS = 0;
        /* 调制占空比设置 */
        regBase->CCR2_b.MDDR = 145;
        break;

    case 38400UL:
        /* 波特率生成双速率模式使能，1-使能 */
        regBase->CCR2_b.BGDM = 1;
        /* 0-16个时钟发送一个bit，1-8个时钟发送一个bit */
        regBase->CCR2_b.ABCS = 1;
        /* 波特率计数器 */
        regBase->CCR2_b.BRR = 176;
        /* 输入时钟源分频选择，0-部分品，1-4分频，2-16分频，3-64分频 */
        regBase->CCR2_b.CKS = 0;
        /* 调制占空比设置 */
        regBase->CCR2_b.MDDR = 145;
        break;

    case 57600UL:
        /* 波特率生成双速率模式使能，1-使能 */
        regBase->CCR2_b.BGDM = 1;
        /* 0-16个时钟发送一个bit，1-8个时钟发送一个bit */
        regBase->CCR2_b.ABCS = 0;
        /* 波特率计数器 */
        regBase->CCR2_b.BRR = 58;
        /* 输入时钟源分频选择，0-部分品，1-4分频，2-16分频，3-64分频 */
        regBase->CCR2_b.CKS = 0;
        /* 调制占空比设置 */
        regBase->CCR2_b.MDDR = 145;
        break;

    case 2500000UL:
        /* 波特率生成双速率模式使能，1-使能 */
        regBase->CCR2_b.BGDM = 1;
        /* 0-16个时钟发送一个bit，1-8个时钟发送一个bit */
        regBase->CCR2_b.ABCS = 1;
        /* 波特率计数器 */
        regBase->CCR2_b.BRR = 2;
        /* 输入时钟源分频选择，0-部分品，1-4分频，2-16分频，3-64分频 */
        regBase->CCR2_b.CKS = 0;
        /* 调制占空比设置 */
        regBase->CCR2_b.MDDR = 160;
        break;

    case 115200UL:
    default:
        /* 波特率生成双速率模式使能，1-使能 */
        regBase->CCR2_b.BGDM = 1;
        /* 0-16个时钟发送一个bit，1-8个时钟发送一个bit */
        regBase->CCR2_b.ABCS = 1;
        /* 波特率计数器 */
        regBase->CCR2_b.BRR = 58;
        /* 输入时钟源分频选择，0-部分品，1-4分频，2-16分频，3-64分频 */
        regBase->CCR2_b.CKS = 0;
        /* 调制占空比设置 */
        regBase->CCR2_b.MDDR = 145;
        break;
    }
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 调试用SCI0初始化，程序开启了GIC级的TEI，TXI中断(外设级需要再发送数据的时候开启), 开启了RXI中断(GIC级中断被禁止，触发DMA传输时需要外设中断标志)
 * @param buadrate  - 波特率，目前支持(4800/9600/19200/38400/57600/115200/2500000)
 * @param format    - 数据格式，0-8N1,1-8N2，2-8E1，3-8E2，4-8O1,5-8O2
 * RETURNS:
 *
****************************************************************************************************/
 void bsp_sci_format(R_SCI0_Type * regBase, uint8_t format)
{
    switch (format)
    {
    case 1:
        /* 奇偶校验使能，1-使能*/
        regBase->CCR1_b.PE = 0;
        /* 0-偶校验(enen)，1-奇校验(odd) */
        regBase->CCR1_b.PM = 0;
        /* 0-1位停止位，1-2位停止位 */
        regBase->CCR3_b.STP = 1;
        break;
    case 2:
        /* 奇偶校验使能，1-使能*/
        regBase->CCR1_b.PE = 1;
        /* 0-偶校验(enen)，1-奇校验(odd) */
        regBase->CCR1_b.PM = 0;
        /* 0-1位停止位，1-2位停止位 */
        regBase->CCR3_b.STP = 0;
        break;
    case 3:
        /* 奇偶校验使能，1-使能*/
        regBase->CCR1_b.PE = 1;
        /* 0-偶校验(enen)，1-奇校验(odd) */
        regBase->CCR1_b.PM = 0;
        /* 0-1位停止位，1-2位停止位 */
        regBase->CCR3_b.STP = 1;
        break;
    case 4:
        /* 奇偶校验使能，1-使能*/
        regBase->CCR1_b.PE = 1;
        /* 0-偶校验(enen)，1-奇校验(odd) */
        regBase->CCR1_b.PM = 1;
        /* 0-1位停止位，1-2位停止位 */
        regBase->CCR3_b.STP = 0;
        break;
    case 5:
        /* 奇偶校验使能，1-使能*/
        regBase->CCR1_b.PE = 1;
        /* 0-偶校验(enen)，1-奇校验(odd) */
        regBase->CCR1_b.PM = 1;
        /* 0-1位停止位，1-2位停止位 */
        regBase->CCR3_b.STP = 1;
        break;
    case 0:
    default:
        /* 奇偶校验使能，1-使能*/
        regBase->CCR1_b.PE = 0;
        /* 0-偶校验(enen)，1-奇校验(odd) */
        regBase->CCR1_b.PM = 0;
        /* 0-1位停止位，1-2位停止位 */
        regBase->CCR3_b.STP = 0;
        break;
    }
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口dma初始化
 * RETURNS:
 *
****************************************************************************************************/
static void sci_sci0_dma_init(void)
{
    g_sci_ss_tx_dma.p_api->open(g_sci_ss_tx_dma.p_ctrl, g_sci_ss_tx_dma.p_cfg);
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 调试用SCI0初始化，程序开启了GIC级的TEI，TXI中断(外设级需要再发送数据的时候开启), 开启了RXI中断(GIC级中断被禁止，触发DMA传输时需要外设中断标志)
 * @param buadrate  - 波特率，目前支持(4800/9600/19200/38400/57600/115200/2500000)
 * @param format    - 数据格式，0-8N1,1-8N2，2-8E1，3-8E2，4-8O1,5-8O2
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci0_init_ss(uint32_t buadrate, uint8_t format)
{
    /* SCIx外设基地址 */
    R_SCI0_Type * regBase = ((R_SCI0_Type *) R_SCI0_BASE);

    /* SCIx通道 */
    uint32_t channel = 0UL;

    /* Cancel SCI0 module stop state */
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_SCI, channel);
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);

    /* 禁止GIC级中断 */
    R_BSP_IrqDisable(VECTOR_NUMBER_SCI0_ERI);
    R_BSP_IrqDisable(VECTOR_NUMBER_SCI0_RXI);
//    R_BSP_IrqDisable(VECTOR_NUMBER_SCI0_TXI);
    R_BSP_IrqDisable(VECTOR_NUMBER_SCI0_TEI);

    /* Clear transmit/receive enable bits */
    regBase->CCR0_b.TE = 0U;
    regBase->CCR0_b.RE = 0U;

    /* Reset transmit/receive FIFO data register operation */
    regBase->FCR_b.TFRST = 1U;
    regBase->FCR_b.RFRST = 1U;

    /* Read and clear status flags */
    regBase->CFCLR = 0UL;
    regBase->FFCLR = 0UL;

    /* Set transmission/reception format */

    regBase->CCR1 = 0x00000000;
    /* TXD引脚在TE为0时的电平，0-低电平，1-高电平(CCR1.bit12是取反意思) */
    regBase->CCR1_b.SPB2DT = 1;
    /* TXD引脚在TE为0时的输出是否受SPB2DT控制，1-使能控制 */
    regBase->CCR1_b.SPB2IO = 1;
    /* TXD输出引脚电平取反 */
    regBase->CCR1_b.TINV = 0;
    /* RXD输入引脚电平取反 */
    regBase->CCR1_b.RINV = 0;
    /* RXD输入数字滤波时钟选择 */
    regBase->CCR1_b.NFCS = 0;
    /* RXD输入数字滤波使能，1-使能 */
    regBase->CCR1_b.NFEN = 0;

    /* 波特率配置 */
    bsp_sci_buadtrate(regBase, buadrate);

    /* 复位默认值 */
    regBase->CCR3 = 0x00001203;
    /* 字符长度，0/1-9bit，2-8bit，3-7bit */
    regBase->CCR3_b.CHR = 2;
    /* 0-MSB先发送，1-LSB先发送 */
    regBase->CCR3_b.LSBF = 1;
    /* 1-接收/发送数据反相使能 */
    regBase->CCR3_b.SINV = 0;
    /* 0-低电平作为起始位检测，1-下降沿作为起始位检测 */
    regBase->CCR3_b.RXDESEL = 1;
    /* 0-串口通讯模式 */
    regBase->CCR3_b.MOD = 0;
    /* 1-FIFO功能使能 */
    regBase->CCR3_b.FM = 1;
    /* 1-RS485控制引脚驱动使能 */
    regBase->CCR3_b.DEN = 0;

    /* 数据格式配置 */
    bsp_sci_format(regBase, format);

    /* 复位默认值 */
    regBase->CCR4 = 0x00000000;

    /* Wait for at least 1-bit interval */
    R_BSP_SoftwareDelay(10, BSP_DELAY_UNITS_MICROSECONDS);

    /* Set FIFO trigger conditions */

    /* FIFO控制寄存器复位默认值 */
    regBase->FCR = 0x00000000;
    /* FIFO发送缓存触发中断设定值(使用DMA进行接收时，需要设置为0) */
    regBase->FCR_b.TTRG = 0xF;
    /* FIFO接收缓存触发中断设定值(使用DMA进行接收时，需要设置为0) */
    regBase->FCR_b.RTRG = 0;

    /* Disable transmit/receive FIFO data register reset operation */
    regBase->FCR_b.TFRST = 1U;
    regBase->FCR_b.RFRST = 1U;
#if 0
    /* 配置SCI中断优先级 */
    R_BSP_IrqCfg(VECTOR_NUMBER_SCI0_ERI, 12, NULL);
    R_BSP_IrqCfg(VECTOR_NUMBER_SCI0_RXI, 12, NULL);
//    R_BSP_IrqCfg(VECTOR_NUMBER_SCI0_TXI, 12, NULL);
    R_BSP_IrqCfg(VECTOR_NUMBER_SCI0_TEI, 12, NULL);

    /* 使能GIC级中断 */
    R_BSP_IrqEnable(VECTOR_NUMBER_SCI0_ERI);
    R_BSP_IrqEnable(VECTOR_NUMBER_SCI0_RXI);
//    R_BSP_IrqEnable(VECTOR_NUMBER_SCI0_TXI);
    R_BSP_IrqEnable(VECTOR_NUMBER_SCI0_TEI);


    /* 外设级中断使能：(只有使能了外设级别的接收中断才可以触发DMA进行数据传输) */
    regBase->CCR0_b.RIE = 1U;    /* 接收中断 */
    //regBase->CCR0_b.TIE = 1U;    /* 发送空中断 */
    //regBase->CCR0_b.TEIE = 1U;    /* 发送结束中断 */

    /* 使能接收及使能发送  */
    regBase->CCR0_b.TE = 0U;
    regBase->CCR0_b.RE = 0U;
#endif
//    while(1U != regBase->CCR0_b.TE)
//    {
//        ;
//    }
     /* 初始化 DMA发送 */
    sci_sci0_dma_init();  
    
    R_BSP_PinWrite(PORT_485SS_DE,PIN_LEVEL_LOW);
    bsp_sci0_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);    
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口 发送函数
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci0_Send(uint8_t * tx_buf, uint16_t tx_num)
{
    R_SCI0->CCR0_b.TE  = 0U;
    R_SCI0->CCR0_b.RE  = 0U;
    g_sci0_tx_count =  tx_num;
    
//    g_sci0_tx_length = tx_num;
    gp_sci0_tx_address = tx_buf;
    
//    R_SCI0->FCR_b.RTRG = 8;
    
    R_SCI0->CCR0_b.TE  = 1U;
    R_SCI0->CCR0_b.TEIE = 1U;    /* 接收中断 */
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口 接收函数
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci0_receive(uint8_t * rx_buf, uint16_t rx_num)
{
    R_SCI0->CCR0_b.TE  = 0U;
    R_SCI0->CCR0_b.RE  = 0U;
    g_sci0_rx_count =  0U;
    
    g_sci0_rx_length = rx_num;
    gp_sci0_rx_address = rx_buf;
    
    R_SCI0->FCR_b.RTRG = 8;
    
    R_SCI0->CCR0_b.RE  = 1U;
    R_SCI0->CCR0_b.RIE = 1U;    /* 接收中断 */
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口dma数据发送
 * RETURNS:
 *
****************************************************************************************************/
void sci_sci0_dma_tx(uint8_t *txBuf, uint16_t length)
{   
  /* SCIx外设基地址 */
    R_SCI0_Type * regBase = ((R_SCI0_Type *) R_SCI0_BASE);
    
    R_SCI0->CCR0 &= (uint32_t) ~(0x00100000 | 0x00200000);
    
    if(1 != R_SCI0->CSR_b.TEND)
    {
      return;
    }
 
    
    /* 禁能发送*/
    if(length > 1)
    {
      /* 目的地址： SCI串口的发送寄存器 */
      g_sci_ss_tx_dma.p_cfg->p_info->p_dest = (void *)(&(R_SCI0->TDR));

      /* 源地址： 数据寄存器(由于需要手动发送一个字节启动DMA传输，因此源起始地址需要注意) */
      g_sci_ss_tx_dma.p_cfg->p_info->p_src = (void *)(&txBuf[1]);

      /* 发送数据字节长度(DMA传输的字节长度是总字节长度减1，第1个字节靠手动发送) */
      g_sci_ss_tx_dma.p_cfg->p_info->length = (length - 1);

      /* Disable the corresponding IRQ when transferring using DMAC. */
      R_BSP_IrqDisable(VECTOR_NUMBER_SCI0_TXI);
      /* 重新配置后*/
      g_sci_ss_tx_dma.p_api->reconfigure(g_sci_ss_tx_dma.p_ctrl, g_sci_ss_tx_dma.p_cfg->p_info);
    }

     /* 发送一个字节用来启动DMA传输(DMA请求为边沿有效) */
    R_SCI0->CCR0 |= 0x00100000;
    
    R_SCI0->TDR_b.TDAT = txBuf[0];
    
    R_SCI0->CFCLR_b.TDREC = 1;
    
    R_SCI0->CCR0_b.TE  = 1U;
    R_SCI0->CCR0_b.TEIE = 1U;    /* 接收中断 */
}


/*******************************************************************************
* Function Name: userdef_scifa2_uart_receive
* Description  : Read data from the UART function of the SCIFA channel specified by
*              : the argument channel.
* Arguments    : uint8_t * data   : data pointer to read
* Return Value : SCIFA_UART_SUCCESS : Success
*              : SCIFA_UART_ERR     : Error
*******************************************************************************/
int32_t userdef_scifa0_uart_receive (uint8_t *rx_buf,uint16_t rx_num, uint32_t Timeout)
{   
    uint16_t Fsr_word;
    uint32_t tickstart;
    uint32_t TimeoutCnt = (uint32_t)(1000*Timeout*CMTW1_PPR_US);
      
    R_BSP_PinWrite(PORT_485SS_DE,PIN_LEVEL_LOW);
    /* Confirming receive error(ER,BRK,FER,PER,ORER) */
    //SCIFA0.FSR.WORD 使用这个会报错误，后面这个问题可能需要查一下
    Fsr_word = (R_SCI0->CSR_b.ORER || R_SCI0->CSR_b.MFF || R_SCI0->CSR_b.PER || R_SCI0->CSR_b.FER);
      

    if ((Fsr_word != 0))      
    {
        /* ---- Detect receive error ---- */
        /* Disable reception             */
        /* Reset receiving FIFO          */
        /* Clearing FIFO reception reset */
        /* Error bit clear               */
        /* Enable reception              */
        R_SCI0->CCR0_b.RE    = 0;
        R_SCI0->FCR_b.RFRST = 1;
        R_SCI0->FCR_b.RFRST = 0;
             
        R_SCI0->CFCLR |= (uint32_t)SCI_UART_RCVR_ERRCLR_MASK;

        g_sci0_rx_length   = rx_num;
        gp_sci0_rx_address = rx_buf;
    
        R_SCI0->FCR_b.RTRG = 0;    
        R_SCI0->CCR0_b.RE    = 1U;
     
        return SCIFA_UART_ERR;
    }

    if (rx_num < 1U)
    {
        return SCIFA_UART_ERR;  
    }
    else
    {
        g_sci0_rx_length  = rx_num;
        gp_sci0_rx_address = rx_buf;

        R_SCI0->FCR_b.RTRG = 0;
        R_SCI0->CCR0_b.RE    = 1U;
    }
    
    tickstart = bsp_gettimercnt();

    /* Is there receive FIFO data? */
    while (g_sci0_rx_length > 0) 
    {
        while(R_SCI0->CSR_b.RDRF == 0)
        {         
            if(bsp_gettimercnt() - tickstart > TimeoutCnt)
            {
              return SCIFA_UART_ERR;
            }                  
        }        
        
        *(gp_sci0_rx_address) = R_SCI0->RDR_b.RDAT;
        gp_sci0_rx_address++;           
        g_sci0_rx_length--;          
        /* Clear RDF */
        R_SCI0->CFCLR_b.RDRFC = 1;        
    }
    
    R_SCI0->CCR0_b.RE    = 0U;
    
    return SCIFA_UART_SUCCESS;        
}
/*******************************************************************************
* Function Name: userdef_scifa2_uart_send
* Description  : Write data to the UART function of the SCIFA channel specified by
*              : the argument channel.
* Arguments    : uint8_t data    : data to write
* Return Value : none
*******************************************************************************/
int32_t userdef_scifa0_uart_send (uint8_t *tx_buf,uint16_t tx_num, uint32_t Timeout)
{
    uint32_t tickstart;
    uint32_t TimeoutCnt = (uint32_t)(1000*Timeout*CMTW1_PPR_US);
    
   /* Check if it is possible to transmit (TDFE flag) */
    R_BSP_PinWrite(PORT_485SS_DE,PIN_LEVEL_HIGH);
    if (tx_num < 1U)
    {
         return SCIFA_UART_ERR;
    }
    else
    {
        gp_sci0_tx_address = tx_buf;
        g_sci0_tx_count    = tx_num;
        R_SCI0->FCR_b.TTRG = 0xF;
    }
    
    tickstart = bsp_gettimercnt();
    while (g_sci0_tx_count > 0)
    {
        while (0 == R_SCI0->CSR_b.TDRE)
        {
            if(bsp_gettimercnt() - tickstart > TimeoutCnt)
            {
              return SCIFA_UART_ERR;
            }       
        }

        /* Write the receiving data in TDR */
        R_SCI0->TDR_b.TDAT = *gp_sci0_tx_address;
        gp_sci0_tx_address++;
        g_sci0_tx_count --;
        R_SCI0->CCR0_b.TE = 1U;
        /* Clear TDRE and TEND flag */

//        SCIFA0.FSR.BIT.DR   &= 1;
//        SCIFA0.FSR.BIT.RDF  &= 1;
//        SCIFA0.FSR.BIT.PER  &= 1;
//        SCIFA0.FSR.BIT.FER  &= 1;
//        SCIFA0.FSR.BIT.BRK  &= 1;
//        SCIFA0.FSR.BIT.TDFE &= 0;
//        SCIFA0.FSR.BIT.TEND &= 0;
//        SCIFA0.FSR.BIT.ER   &= 1;             
    }
    
   tickstart = bsp_gettimercnt();
    while( R_SCI0->CSR_b.TEND == 0)
    {
      //wait transmit end  
        if(bsp_gettimercnt() - tickstart > TimeoutCnt)
        {
          return SCIFA_UART_ERR;
        }     
    }
     R_SCI0->CCR0_b.TE = 0U;
    
    R_BSP_PinWrite(PORT_485SS_DE,PIN_LEVEL_LOW);
    return SCIFA_UART_SUCCESS;   
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口dma传输中断中开启发送完成中断
 * RETURNS:
 *
****************************************************************************************************/
void dmac_callback_sci0_tx_end (dmac_callback_args_t *p_args)
{
      R_SCI0->CCR0_b.TEIE = 1U;    /* 发送结束中断 */
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串错误中断清除错误标志
 * RETURNS:
 *
****************************************************************************************************/
void r_sci0_eri_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
  
  uint32_t     data  = 0U;
  
  data = R_SCI0->RDR_b.RDAT;
  
  if(R_SCI0->CSR_b.ORER || R_SCI0->CSR_b.MFF || R_SCI0->CSR_b.PER || R_SCI0->CSR_b.FER)
  {
    //错误中断,清除错误标志位
    
    R_SCI0->CFCLR |= SCI_UART_RCVR_ERRCLR_MASK;

  }  
  
  /* Dummy read to ensure that interrupt event is cleared. */
  volatile uint32_t dummy = R_SCI0->CSR;
  FSP_PARAMETER_NOT_USED(dummy);  
  
  //todo
  //bsp_sci0_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);  
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
}


/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机fifo满中断开启，处理数据
 * RETURNS:
 *
****************************************************************************************************/
void r_sci0_rxi_isr(void)
{
  uint16_t  count = 0;
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM
  
  
  if(R_SCI0->CSR_b.ORER || R_SCI0->CSR_b.MFF || R_SCI0->CSR_b.PER || R_SCI0->CSR_b.FER)
  {
    //错误中断,清除错误标志位
    
    R_SCI0->CFCLR |= (uint32_t)SCI_UART_RCVR_ERRCLR_MASK;

  }

    /* Get the amount of receive data stored in FRDR register */
   uint16_t  dummy_fdr = R_SCI0->FRSR_b.R;
  
    /* Read data from the receive FIFO data register */
  while ((g_sci0_rx_length > g_sci0_rx_count) && (count < dummy_fdr))
  {
     *gp_sci0_rx_address = R_SCI0->RDR_b.RDAT;
      gp_sci0_rx_address++;
      g_sci0_rx_count++;
      count++;
  }    

#if 1  
    if( (0xAA == CommandRecv.StartFlag) && (g_sci0_rx_count > 0) )
    {
      if(g_sci0_rx_count>3)
      {
        if((*(uint16_t *)&CommandRecv.CommandLenLow+2) <= g_sci0_rx_count)
        {
            /* All data received */
           R_SCI0->CCR0_b.RE = 0U;          
            bsp_sci0_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);  
            RoserBusTransmit(&CommandRecv,&CommandSend,0);          
        }
        else if((*(uint16_t *)&CommandRecv.CommandLenLow+2) >= UART_RECV_MAX_NUM)
        {
            /* All data received */
           R_SCI0->CCR0_b.RE = 0U;
            bsp_sci0_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);
        }
       /* If remaining data is less than the receive trigger number, receive interrupt will not occur.
       In this case, set trigger number to 1 to force receive interrupt for each one byte of data in FRDR */
        else if(((*(uint16_t *)&CommandRecv.CommandLenLow+2) - g_sci0_rx_count < _SCIF_RX_TRIG_NUM_0) && (1U != R_SCI4->FCR_b.RTRG))
        {
            R_SCI0->FCR_b.RTRG  = (*(uint16_t *)&CommandRecv.CommandLenLow+2) - g_sci0_rx_count;
        }
      
      }
    }
    else
    {
        /* All data received */
        R_SCI0->CCR0_b.RE = 0U;
        bsp_sci0_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);
      
    }
    
    
    /* If remaining data is less than the receive trigger number, receive interrupt will not occur.
       In this case, set trigger number to 1 to force receive interrupt for each one byte of data in FRDR */
#endif  
  
  if(R_SCI0->CSR_b.RDRF)
  {
    //清除标志位
    R_SCI0->CFCLR_b.RDRFC = 1;
    
  }
   
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口发送完成中断函数
 * RETURNS:
 *
****************************************************************************************************/
void r_sci0_tei_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM
  
 
  /* Clear data ready detect flag */
  if (1U == R_SCI0->CSR_b.TEND)
  {
     R_BSP_PinWrite(PORT_485SS_DE,PIN_LEVEL_LOW);
    
     // 禁止发送完成中断
     R_SCI0->CCR0_b.RE = 1;
     R_SCI0->CCR0_b.RIE = 1;
     R_SCI0->CCR0_b.TE = 0;
     R_SCI0->CCR0_b.TEIE = 0;
     R_SCI0->CCR0_b.TIE = 0;
     
     // RS485DE=0;
     // bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);     
  }  
  
  /* 需要测试是接收空闲时：DR 为1时是否会进入发送完成中断 */
  if (1U == R_SCI0->FRSR_b.DR)
  {
      R_SCI0->FFCLR_b.DRC = 0U;
  }  
  
  /* Dummy read to ensure that interrupts are disabled. */
  volatile uint32_t dummy = R_SCI0->CCR0;
  FSP_PARAMETER_NOT_USED(dummy);
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口 停止发送
 * RETURNS:
 *
****************************************************************************************************/
void UART_TO_PC_STOP(void)
{
  
//   R_SCI0->CCR0_b.RE = 0;
//   R_SCI0->CCR0_b.RIE = 0;
   R_SCI4->CCR0_b.RE = 0;
   R_SCI4->CCR0_b.RIE = 0;
}



/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 485总线dma初始化
 * RETURNS:
 *
****************************************************************************************************/
static void sci_sci4_dma_init(void)
{
    g_sci_zx_tx_dma.p_api->open(g_sci_zx_tx_dma.p_ctrl, g_sci_zx_tx_dma.p_cfg);
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 总线用SCI4初始化，程序开启了GIC级的TEI，TXI中断(外设级需要再发送数据的时候开启), 开启了RXI中断(GIC级中断被禁止，触发DMA传输时需要外设中断标志),
 * 使能
 * @param buadrate  - 波特率，目前支持(4800/9600/19200/38400/57600/115200/2500000)
 * @param format    - 数据格式，0-8N1,1-8N2，2-8E1，3-8E2，4-8O1,5-8O2
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci4_init_zx(uint32_t buadrate, uint8_t format)
{ 
    /* SCIx外设基地址 */
    R_SCI0_Type * regBase = ((R_SCI0_Type *) R_SCI4_BASE);

    /* SCIx通道 */
    uint32_t channel = 4UL;

    /* Cancel SCI0 module stop state */
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_SCI, channel);
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);

    /* 禁止GIC级中断 */
    R_BSP_IrqDisable(VECTOR_NUMBER_SCI4_ERI);
    R_BSP_IrqDisable(VECTOR_NUMBER_SCI4_RXI);
//    R_BSP_IrqDisable(VECTOR_NUMBER_SCI4_TXI);
    R_BSP_IrqDisable(VECTOR_NUMBER_SCI4_TEI);

    /* Clear transmit/receive enable bits */
    regBase->CCR0_b.TE = 0U;
    regBase->CCR0_b.RE = 0U;

    /* Reset transmit/receive FIFO data register operation */
    regBase->FCR_b.TFRST = 1U;
    regBase->FCR_b.RFRST = 1U;

    /* Read and clear status flags */
    regBase->CFCLR = 0UL;
    regBase->FFCLR = 0UL;

    /* Set transmission/reception format */

    regBase->CCR1 = 0x00000000;
    /* TXD引脚在TE为0时的电平，0-低电平，1-高电平(CCR1.bit12是取反意思) */
    regBase->CCR1_b.SPB2DT = 1;
    /* TXD引脚在TE为0时的输出是否受SPB2DT控制，1-使能控制 */
    regBase->CCR1_b.SPB2IO = 1;
    /* TXD输出引脚电平取反 */
    regBase->CCR1_b.TINV = 0;
    /* RXD输入引脚电平取反 */
    regBase->CCR1_b.RINV = 0;
    /* RXD输入数字滤波时钟选择 */
    regBase->CCR1_b.NFCS = 0;
    /* RXD输入数字滤波使能，1-使能 */
    regBase->CCR1_b.NFEN = 0;

    
    /* 波特率配置 */
    bsp_sci_buadtrate(regBase, buadrate);

    /* 复位默认值 */
    regBase->CCR3 = 0x00001203;
    /* 字符长度，0/1-9bit，2-8bit，3-7bit */
    regBase->CCR3_b.CHR = 2;
    /* 0-MSB先发送，1-LSB先发送 */
    regBase->CCR3_b.LSBF = 1;
    /* 1-接收/发送数据反相使能 */
    regBase->CCR3_b.SINV = 0;
    /* 0-低电平作为起始位检测，1-下降沿作为起始位检测 */
    regBase->CCR3_b.RXDESEL = 1;
    /* 0-串口通讯模式 */
    regBase->CCR3_b.MOD = 0;
    /* 1-FIFO功能使能 */
    regBase->CCR3_b.FM = 1;
    /*0-不使能485硬件DE  1-RS485控制引脚驱动使能 */
    regBase->CCR3_b.DEN = 0;    

    /* 数据格式配置 */
    bsp_sci_format(regBase, format);

    /* 复位默认值 */
    regBase->CCR4 = 0x00000000;

    /* Wait for at least 1-bit interval */
    R_BSP_SoftwareDelay(10, BSP_DELAY_UNITS_MICROSECONDS);

    /* Set FIFO trigger conditions */

    /* FIFO控制寄存器复位默认值 */
    regBase->FCR = 0x00000000;
    /* FIFO发送缓存触发中断设定值(使用DMA进行发送时，需要设置为0x0F*/
    regBase->FCR_b.TTRG = 0x0F;
    /* FIFO接收缓存触发中断设定值(使用DMA进行接收时，需要设置为0) */
    regBase->FCR_b.RTRG = 0;
//    regBase->FCR_b.RTRG = _SCIF_RX_TRIG_NUM_0;

    /* Disable transmit/receive FIFO data register reset operation */
    regBase->FCR_b.TFRST = 1U;
    regBase->FCR_b.RFRST = 1U;

    /* 配置SCI中断优先级 */
    R_BSP_IrqCfg(VECTOR_NUMBER_SCI4_ERI, 12, NULL);
    R_BSP_IrqCfg(VECTOR_NUMBER_SCI4_RXI, 12, NULL);
    //R_BSP_IrqCfg(VECTOR_NUMBER_SCI4_TXI, 12, NULL);
    R_BSP_IrqCfg(VECTOR_NUMBER_SCI4_TEI, 12, NULL);

    /* 使能GIC级中断 */
#if 1
    R_BSP_IrqEnable(VECTOR_NUMBER_SCI4_ERI);
    R_BSP_IrqEnable(VECTOR_NUMBER_SCI4_RXI);
    //R_BSP_IrqEnable(VECTOR_NUMBER_SCI4_TXI);
    R_BSP_IrqEnable(VECTOR_NUMBER_SCI4_TEI);
#endif
    
    /* 外设级中断使能：(只有使能了外设级别的接收中断才可以触发DMA进行数据传输) */
    regBase->CCR0_b.RIE =  1U;    /* 接收中断 */
//    regBase->CCR0_b.TIE =  1U;    /* 发送空中断 */
//    regBase->CCR0_b.TEIE = 1U;    /* 发送结束中断 */

    /* 使能接收及使能发送  */
    regBase->CCR0_b.TE = 0U;
    regBase->CCR0_b.RE = 0U;
//    while(1U != regBase->CCR0_b.TE)
//    {
//        ;
//    }    
    
    /* 初始化 DMA发送 */
    sci_sci4_dma_init();  
    
    R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_LOW);
    bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);    
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口 接收函数
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci4_receive(uint8_t * rx_buf, uint16_t rx_num)
{
    R_SCI4->CCR0_b.TE  = 0U;
    R_SCI4->CCR0_b.RE  = 0U;
    g_sci4_rx_count =  0U;
    
    g_sci4_rx_length = rx_num;
    gp_sci4_rx_address = rx_buf;
    
    R_SCI4->FCR_b.RTRG = 8;
    
    R_SCI4->CCR0_b.RE  = 1U;
    R_SCI4->CCR0_b.RIE = 1U;    /* 接收中断 */
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口 发送函数
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci4_Send(uint8_t * tx_buf, uint16_t tx_num)
{
    R_SCI4->CCR0_b.TE  = 0U;
    R_SCI4->CCR0_b.RE  = 0U;
    g_sci4_tx_count =  tx_num;
    
//    g_SCI4_tx_length = tx_num;
    gp_sci4_tx_address = tx_buf;
    
//    R_SCI4->FCR_b.RTRG = 8;
    
    R_SCI4->CCR0_b.TE  = 1U;
    R_SCI4->CCR0_b.TEIE = 1U;    /* 接收中断 */
}


/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 485总线 dma数据发送
 * RETURNS:
 *
****************************************************************************************************/
void sci_sci4_dma_tx(uint8_t *txBuf, uint16_t length)
{
    /* SCIx外设基地址 */
    R_SCI0_Type * regBase = ((R_SCI0_Type *) R_SCI4_BASE);


    
    R_SCI4->CCR0 &= (uint32_t) ~(0x00100000 | 0x00200000);
    
    if(1 != R_SCI4->CSR_b.TEND)
    {
      return;
    }
 
    
    /* 禁能发送*/
    if(length > 1)
    {
      /* 目的地址： SCI串口的发送寄存器 */
      g_sci_zx_tx_dma.p_cfg->p_info->p_dest = (void *)(&(R_SCI4->TDR));

      /* 源地址： 数据寄存器(由于需要手动发送一个字节启动DMA传输，因此源起始地址需要注意) */
      g_sci_zx_tx_dma.p_cfg->p_info->p_src = (void *)(&txBuf[1]);

      /* 发送数据字节长度(DMA传输的字节长度是总字节长度减1，第1个字节靠手动发送) */
      g_sci_zx_tx_dma.p_cfg->p_info->length = (length - 1);

      /* Disable the corresponding IRQ when transferring using DMAC. */
      R_BSP_IrqDisable(VECTOR_NUMBER_SCI4_TXI);
      /* 重新配置后*/
      g_sci_zx_tx_dma.p_api->reconfigure(g_sci_zx_tx_dma.p_ctrl, g_sci_zx_tx_dma.p_cfg->p_info);
    }

     /* 发送一个字节用来启动DMA传输(DMA请求为边沿有效) */
    R_SCI4->CCR0 |= 0x00100000;
    
    R_SCI4->TDR_b.TDAT = txBuf[0];
    
    R_SCI4->CFCLR_b.TDREC = 1;
    
    R_SCI4->CCR0_b.TE  = 1U;
    R_SCI4->CCR0_b.TEIE = 1U;    /* 接收中断 */
    
}

/*******************************************************************************
* Function Name: userdef_scifa2_uart_receive
* Description  : Read data from the UART function of the SCIFA channel specified by
*              : the argument channel.
* Arguments    : uint8_t * data   : data pointer to read
* Return Value : SCIFA_UART_SUCCESS : Success
*              : SCIFA_UART_ERR     : Error
*******************************************************************************/
int32_t userdef_scifa4_uart_receive (uint8_t *rx_buf,uint16_t rx_num, uint32_t Timeout)
{   
    uint16_t Fsr_word;
    uint32_t tickstart;
    uint32_t TimeoutCnt = (uint32_t)(1000*Timeout*CMTW1_PPR_US);
      
    R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_LOW);
    /* Confirming receive error(ER,BRK,FER,PER,ORER) */
    //SCIFA0.FSR.WORD 使用这个会报错误，后面这个问题可能需要查一下
    Fsr_word = (R_SCI4->CSR_b.ORER || R_SCI4->CSR_b.MFF || R_SCI4->CSR_b.PER || R_SCI4->CSR_b.FER);
      

    if ((Fsr_word != 0))      
    {
        /* ---- Detect receive error ---- */
        /* Disable reception             */
        /* Reset receiving FIFO          */
        /* Clearing FIFO reception reset */
        /* Error bit clear               */
        /* Enable reception              */
        R_SCI4->CCR0_b.RE    = 0;
        R_SCI4->FCR_b.RFRST = 1;
        R_SCI4->FCR_b.RFRST = 0;
             
        R_SCI4->CFCLR |= (uint32_t)SCI_UART_RCVR_ERRCLR_MASK;

        g_sci4_rx_length   = rx_num;
        gp_sci4_rx_address = rx_buf;
    
        R_SCI4->FCR_b.RTRG = 0;    
        R_SCI4->CCR0_b.RE    = 1U;
     
        return SCIFA_UART_ERR;
    }

    if (rx_num < 1U)
    {
        return SCIFA_UART_ERR;  
    }
    else
    {
        g_sci4_rx_length  = rx_num;
        gp_sci4_rx_address = rx_buf;

        R_SCI4->FCR_b.RTRG = 0;
        R_SCI4->CCR0_b.RE    = 1U;
    }
    
    tickstart = bsp_gettimercnt();

    /* Is there receive FIFO data? */
    while (g_sci4_rx_length > 0) 
    {
        while(R_SCI4->CSR_b.RDRF == 0)
        {         
            if(bsp_gettimercnt() - tickstart > TimeoutCnt)
            {
              return SCIFA_UART_ERR;
            }                  
        }        
        
        *(gp_sci4_rx_address) = R_SCI4->RDR_b.RDAT;
        gp_sci4_rx_address++;           
        g_sci4_rx_length--;          
        /* Clear RDF */
        R_SCI4->CFCLR_b.RDRFC = 1;        
    }
    
    R_SCI4->CCR0_b.RE    = 0U;
    
    return SCIFA_UART_SUCCESS;        
}
/*******************************************************************************
* Function Name: userdef_scifa2_uart_send
* Description  : Write data to the UART function of the SCIFA channel specified by
*              : the argument channel.
* Arguments    : uint8_t data    : data to write
* Return Value : none
*******************************************************************************/
#pragma optimize=none
int32_t userdef_scifa4_uart_send (uint8_t *tx_buf,uint16_t tx_num, uint32_t Timeout)
{
    uint32_t tickstart;
    uint32_t condition = 16;
    uint32_t TimeoutCnt = (uint32_t)(1000*Timeout*CMTW1_PPR_US);
    
   /* Check if it is possible to transmit (TDFE flag) */
    R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_HIGH);
    if (tx_num < 1U)
    {
         return SCIFA_UART_ERR;
    }
    else
    {
        R_SCI4->CCR0_b.TE = 0U; 
        R_SCI4->CCR0_b.RE = 0U; 
        gp_sci4_tx_address = tx_buf;
        g_sci4_tx_count    = tx_num;
        R_SCI4->FCR_b.TTRG = 0x01;
    }
   
    tickstart = bsp_gettimercnt();
    while( R_SCI4->CSR_b.TEND == 0)
    {
      //wait transmit end  
        if(bsp_gettimercnt() - tickstart > TimeoutCnt)
        {
          return SCIFA_UART_ERR;
        }     
    }

    while (g_sci4_tx_count > 0)
    {

        // /* Write the receiving data in TDR */
        R_SCI4->TDR_b.TDAT = *gp_sci4_tx_address;
        gp_sci4_tx_address++;
        g_sci4_tx_count --;
        R_SCI4->CCR0_b.TE = 1U;
        
        
       tickstart = bsp_gettimercnt();
        while( R_SCI4->CSR_b.TEND == 0)
        {
          //wait transmit end  
            if(bsp_gettimercnt() - tickstart > TimeoutCnt)
            {
              return SCIFA_UART_ERR;
            }     
        }    
    }
    

    R_SCI4->CCR0_b.TE = 0U; 
    R_SCI4->CCR0_b.RE = 0U; 
    R_SCI4->FCR_b.TTRG = 0x0F;
    
    R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_LOW);
    return SCIFA_UART_SUCCESS;   
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口dma传输中断中开启发送完成中断
 * RETURNS:
 *
****************************************************************************************************/
void dmac_callback_sci4_tx_end (dmac_callback_args_t *p_args)
{
      R_SCI4->CCR0_b.TEIE = 1U;    /* 发送结束中断 */
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串错误中断清除错误标志
 * RETURNS:
 *
****************************************************************************************************/
void r_sci4_eri_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
  
  uint32_t     data  = 0U;
  
  data = R_SCI4->RDR_b.RDAT;
  
  if(R_SCI4->CSR_b.ORER || R_SCI4->CSR_b.MFF || R_SCI4->CSR_b.PER || R_SCI4->CSR_b.FER)
  {
    //错误中断,清除错误标志位
    
    R_SCI4->CFCLR |= SCI_UART_RCVR_ERRCLR_MASK;

  }  
  
  /* Dummy read to ensure that interrupt event is cleared. */
  volatile uint32_t dummy = R_SCI4->CSR;
  FSP_PARAMETER_NOT_USED(dummy);  
  
  //todo
  //bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);  
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 串口中断
 * RETURNS:
 *
****************************************************************************************************/
extern UINT32 gDevComId;
void r_sci4_rxi_isr(void)
{
 uint16_t  count = 0;
 
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM
  
  
  if(R_SCI4->CSR_b.ORER || R_SCI4->CSR_b.MFF || R_SCI4->CSR_b.PER || R_SCI4->CSR_b.FER)
  {
    //错误中断,清除错误标志位
    
    R_SCI4->CFCLR |= (uint32_t)SCI_UART_RCVR_ERRCLR_MASK;

  }

    /* Get the amount of receive data stored in FRDR register */
   uint16_t  dummy_fdr = R_SCI4->FRSR_b.R;
  
    /* Read data from the receive FIFO data register */
  while ((g_sci4_rx_length > g_sci4_rx_count) && (count < dummy_fdr))
  {
     *gp_sci4_rx_address = R_SCI4->RDR_b.RDAT;
      gp_sci4_rx_address++;
      g_sci4_rx_count++;
      count++;
  }    

#if 1  
    if( (0xAA == CommandRecv.StartFlag) && (g_sci4_rx_count > 0) )
    {
      if(g_sci4_rx_count>3)
      {
        if((*(uint16_t *)&CommandRecv.CommandLenLow+2) <= g_sci4_rx_count)
        {
            /* All data received */
            R_SCI4->CCR0_b.RE = 0U;          
            bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);  
                                   
            RoserBusTransmit(&CommandRecv,&CommandSend,gDevComId);          
        }
        else if((*(uint16_t *)&CommandRecv.CommandLenLow+2) >= UART_RECV_MAX_NUM)
        {
            /* All data received */
           R_SCI4->CCR0_b.RE = 0U;
            bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);
        }
       /* If remaining data is less than the receive trigger number, receive interrupt will not occur.
       In this case, set trigger number to 1 to force receive interrupt for each one byte of data in FRDR */
        else if(((*(uint16_t *)&CommandRecv.CommandLenLow+2) - g_sci4_rx_count < _SCIF_RX_TRIG_NUM_0) && (1U != R_SCI4->FCR_b.RTRG))
        {
            R_SCI4->FCR_b.RTRG  = (*(uint16_t *)&CommandRecv.CommandLenLow+2) - g_sci4_rx_count;
        }
      
      }
    }
    else
    {
        /* All data received */
        R_SCI4->CCR0_b.RE = 0U;
        bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);
      
    }
    
    
    /* If remaining data is less than the receive trigger number, receive interrupt will not occur.
       In this case, set trigger number to 1 to force receive interrupt for each one byte of data in FRDR */
#endif  
  
  if(R_SCI4->CSR_b.RDRF)
  {
    //清除标志位
    R_SCI4->CFCLR_b.RDRFC = 1;
    
  }
   
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 上位机串口发送完成中断函数
 * RETURNS:
 *
****************************************************************************************************/
void r_sci4_tei_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM
  
 
  /* Clear data ready detect flag */
  if (1U == R_SCI4->CSR_b.TEND)
  {
     R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_LOW);
    
     // 禁止发送完成中断
     R_SCI4->CCR0_b.RE = 1;
     R_SCI4->CCR0_b.RIE = 1;
     R_SCI4->CCR0_b.TE = 0;
     R_SCI4->CCR0_b.TEIE = 0;
     R_SCI4->CCR0_b.TIE = 0;
     
     // RS485DE=0;
     // bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);     
  }  
  
  /* 需要测试是接收空闲时：DR 为1时是否会进入发送完成中断 */
  if (1U == R_SCI4->FRSR_b.DR)
  {
      R_SCI4->FFCLR_b.DRC = 0U;
  }  
  
  /* Dummy read to ensure that interrupts are disabled. */
  volatile uint32_t dummy = R_SCI4->CCR0;
  FSP_PARAMETER_NOT_USED(dummy);
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
}
