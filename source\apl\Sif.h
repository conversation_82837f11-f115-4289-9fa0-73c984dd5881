/****************************************************************************************************
 *
 * FILE NAME:  Sif.h
 *
 * DESCRIPTION:  Serial Interface Definition File
 *
 * CREATED ON:  2021.02.07
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	07-02-2021 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef	_SIF_DEF_H_
#define	_SIF_DEF_H_


/****************************************************************************************************/
/*																									*/
/*		Serial Communication Interface Definition													*/
/*																									*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
#define SIF_TX_BUFSIZE		(512)			/* Serial IF send buffer size							*/
#define SIF_RX_BUFSIZE		(512) 			/* Serial IF receive buffer size						*/
/*--------------------------------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------*/
/*		Serial Communication Class																	*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct _CSIF{
/*--------------------------------------------------------------------------------------------------*/
	void	*Com;							/* Communication port									*/
/*--------------------------------------------------------------------------------------------------*/
	UINT8	AxisNumber;						/* axis number											*/
	UINT8	SqStep;							/* sequence step										*/
	UINT8	ErrCnt;							/* serial I/F error counter								*/
	UINT8	ErrCod;							/* serial I/F error code								*/
/*--------------------------------------------------------------------------------------------------*/
	UINT16	RxCmdLen;						/* command message length [byte]						*/
	UINT16	RxCmdCnt;						/* receive message count								*/
/*--------------------------------------------------------------------------------------------------*/
	UINT16	RxDmaScnt;						/* RxDMA start count									*/
	UINT16	RxDmaRcnt;						/* RxDMA saved count									*/
/*--------------------------------------------------------------------------------------------------*/
	UINT32	RxTimer;						/* Receive timer										*/
	UINT32	StxTimer;						/* Transmit timer										*/
/*--------------------------------------------------------------------------------------------------*/
	volatile UINT16	ReqStx;					/* staart Tx request									*/
	volatile UINT16	TxResLen;				/* response message length [byte]						*/
	volatile UINT16	TxResCnt;				/* response message count								*/
	UINT8	* volatile pTxRes;				/* response buffer pointer								*/
/*--------------------------------------------------------------------------------------------------*/
	void	(*DoRxTx)(struct _CSIF *this);	/* Exchange Serial Data									*/
	INT32	(*ChkCmd)(						/* Check Serial Command Data Receiving					*/
				struct _CSIF *this,			/* this pointer											*/
				UINT16 *pCmdLen );			/* Pointer to the command message length				*/
	INT32	(*StxRes)(						/* Start Serial Response Data Sending					*/
				struct _CSIF *this,			/* this pointer											*/
				UINT8 *ResTxBuf,			/* pointer to the response buffer						*/
				INT32   ResTxLen );			/* response message length								*/
/*--------------------------------------------------------------------------------------------------*/
	UINT8	*RxBuf;							/* receive data buffer									*/
	UINT8	*TxBuf;							/* transmit data buffer									*/
/*--------------------------------------------------------------------------------------------------*/
	UINT16	RcvBufSize;						/* rcv buf size 										*/
	UINT16	SndBufSize;						/* send buf size 										*/
/*--------------------------------------------------------------------------------------------------*/
	UINT8	*pBrtPrm;						/* pointer to the serial baudrate parameter             */
/*--------------------------------------------------------------------------------------------------*/
} CSIF;
/*--------------------------------------------------------------------------------------------------*/




#endif

