--board_file=E:\1.Work\1.code\1.Workspace\Renesas\rzn2l_1axis_lv\humanjoint\rzn2l-boot\FlashRSK_RZN2L_SerialFlash\FlashRSK_RZN2L_SerialFlash.board

--endian=little

--cpu=Cortex-R52

--drv_vector_table_base=__Vectors

--fpu=VFPv5Neon

-p

"C:\Program Files\IAR Systems\arm\config\debugger\Renesas\R9A07G084M04.ddf"

--semihosting

--device=R9A07G084M04

--multicore_nr_of_cores=1

--jet_standard_reset=2,300,200

"--reset_style=\"0,-,0,Disabled__no_reset_\""

"--reset_style=\"1,-,0,Software\""

"--reset_style=\"2,-,1,Hardware\""

"--reset_style=\"5,ResetHandler_Custom,0,Custom\""

--jet_power_from_probe=leave_on

--drv_interface=SWD

--drv_default_breakpoint=1

"--jet_board_cfg=C:\Program Files\IAR Systems\arm/config/debugger/Renesas/RZN2L.ProbeConfig"

--drv_catch_exceptions=0x0000




