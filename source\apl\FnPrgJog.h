/****************************************************************************************************
 *
 * FILE NAME:  FnPrgJog.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FN_PRG_JOG_H_
#define _FN_PRG_JOG_H_

#include "BaseDef.h"

/****************************************************************************************/
/*																						*/
/*		Data definition for program JOG operation Fn									*/
/*																						*/
/****************************************************************************************/
/*--------------------------------------------------------------------------------------*/
/*		Program JOG operation status definition											*/
/*--------------------------------------------------------------------------------------*/
#define	PJOG_INIT	0x00				/* Initial state								*/
#define	PJOG_ABORT	0x10				/* Suspended state								*/
#define	PJOG_START	0x01				/* Operating condition							*/
#define	PJOG_AFTER	0x11				/* Post-processing state						*/
#define	PJOG_END	0x02				/* Termination status							*/
#define	PJOG_ERROR	0x03				/* Abnormal termination							*/
/*--------------------------------------------------------------------------------------*/
/*		Program JOG operation command definition										*/
/*--------------------------------------------------------------------------------------*/
#define	PJOGCMD_NONE	0x00			/* No command									*/
#define	PJOGCMD_INIT	0x01			/* Initialize									*/
#define	PJOGCMD_ABORT	0x02			/* Interruption									*/
#define	PJOGCMD_START	0x03			/* operation									*/

#define	LMTLOWSPEED	10
/*--------------------------------------------------------------------------------------*/
/*		Program JOG operation related function definition								*/
/*--------------------------------------------------------------------------------------*/
PUBLIC void PcalPjogSw( void *Axis );			/* Program JOG operation related switches     */
PUBLIC void PcalPjogDistance( void *Axis );	   /* Program JOG movement distance [command unit] */
PUBLIC void PcalPjogRotspd( void *Axis );		/* Program JOG movement speed[min-1]	*/
PUBLIC void PcalPjogAcctime( void *Axis );		/* Program JOG acceleration/deceleration time[ms]*/
PUBLIC void PcalPjogWaitTime( void *Axis );	  /* Program JOG wait time[ms]				*/
PUBLIC void PcalPjogNum( void *Axis );			/* Program JOG move count				*/

PUBLIC  PRM_RSLT CpiPrgJogLoopExec(void *AxisHdl);
PUBLIC PRM_RSLT PnCmdPrgJog( void *AxisHdl );

#endif

