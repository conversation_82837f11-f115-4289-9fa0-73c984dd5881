/****************************************************************************************************
 *
 * FILE NAME:  FunListDef.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.12
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	12-08-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FUN_LIST_DEF_H_
#define _FUN_LIST_DEF_H_

#include "FunManager.h"
#include "MsgIfDef.h"
/*--------------------------------------------------------------------------------------------------*/
/*		Fn002 : JOG																					*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterJOGoperation(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteJOGoperation(FUNEXE *FunExe, void *Axis);
void     FnLeaveJOGoperation(FUNEXE *FunExe, void *Axis);

/*--------------------------------------------------------------------------------------------------*/
/*		Fn004 : Program JOG																			*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterPrgJog(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecutePrgJog(FUNEXE *FunExe, void *Axis);
void     FnLeavePrgJog(FUNEXE *FunExe, void *Axis);

/*--------------------------------------------------------------------------------------------------*/
/*		Fn005 : Inertial Ident																	*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterInertialoperation(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteInertialoperation(FUNEXE *FunExe, void *Axis);
void FnLeaveInertialoperation(FUNEXE *FunExe, void *Axis);

/*--------------------------------------------------------------------------------------------------*/
/*		Fn006 : Motor Ident																	*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterMotorIdentoperation(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteMotorIdentoperation(FUNEXE *FunExe, void *Axis);
void FnLeaveMotorIdentoperation(FUNEXE *FunExe, void *Axis);
PRM_RSLT PnCmdMotorIdent(void *Axis);

/*--------------------------------------------------------------------------------------------------*/
/*		Fn007 : Clamping Jaw																	*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterClampingJaw(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteClampingJaw(FUNEXE *FunExe, void *Axis);
void FnLeaveClampingJaw(FUNEXE *FunExe, void *Axis);


/*--------------------------------------------------------------------------------------------------*/
/*		Fn200 : Dynamic Auto Tuning Level Setting													*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterSetTuneLessLevel(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteSetTuneLessLevel(FUNEXE *FunExe, void *Axis);
void     FnLeaveSetTuneLessLevel(FUNEXE *FunExe, void *Axis);
INT32 FunDynamicAutoTuningBegin(FUNMSG *Fmsg);
INT32 FunDynamicAutoTuningMain(FUNMSG *Fmsg);
void FunDynamicAutoTuningEnd(FUNMSG *Fmsg);
/*--------------------------------------------------------------------------------------------------*/
/*		Fn201 : Advanced Autotuning																	*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterAdvAutoTuning(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteAdvAutoTuning(FUNEXE *FunExe, void *Axis);
void     FnLeaveAdvAutoTuning(FUNEXE *FunExe, void *Axis);
INT32 FunAdvAutoTuningBegin(FUNMSG *Fmsg);
INT32 FunAdvAutoTuningMain(FUNMSG *Fmsg);
void FunAdvAutoTuningEnd(FUNMSG *Fmsg);
/*--------------------------------------------------------------------------------------------------*/
/*		Fn202 : Refernce Input type Advanced Autotuning												*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterRefInAdvAutoTuning(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteRefInAdvAutoTuning(FUNEXE *FunExe, void *Axis);
void     FnLeaveRefInAdvAutoTuning(FUNEXE *FunExe, void *Axis);
INT32 FunRefInAdvAutoTuningBegin(FUNMSG *Fmsg);
INT32 FunRefInAdvAutoTuningMain(FUNMSG *Fmsg);
void FunRefInAdvAutoTuningEnd(FUNMSG *Fmsg);
/*--------------------------------------------------------------------------------------------------*/
/*		Fn203 : One Parameter Tuning																*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterOnePrmTuning(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteOnePrmTuning(FUNEXE *FunExe, void *Axis);
void     FnLeaveOnePrmTuning(FUNEXE *FunExe, void *Axis);
INT32 FunOneParamTuningBegin(FUNMSG *Fmsg);
INT32 FunOneParamTuningMain(FUNMSG *Fmsg);
void FunOneParamTuningEnd(FUNMSG *Fmsg);
/*--------------------------------------------------------------------------------------------------*/
/*		Fn204 : A-type Vibration Suppression Control Tuning											*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterAtypeVibTuning(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteAtypeVibTuning(FUNEXE *FunExe, void *Axis);
void     FnLeaveAtypeVibTuning(FUNEXE *FunExe, void *Axis);
INT32 FunAtypeVibTuningBegin(FUNMSG *Fmsg);
INT32 FunAtypeVibTuningMain(FUNMSG *Fmsg);
void FunAtypeVibTuningEnd(FUNMSG *Fmsg);
/*--------------------------------------------------------------------------------------------------*/
/*		Fn205 : Model Following Control with Vibration Suppression Tuning							*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterMfcTuning(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteMfcTuning(FUNEXE *FunExe, void *Axis);
void     FnLeaveMfcTuning(FUNEXE *FunExe, void *Axis);
INT32 FunVibMfcTuningBegin(FUNMSG *Fmsg);
INT32 FunVibMfcTuningMain(FUNMSG *Fmsg);
void FunVibMfcTuningEnd(FUNMSG *Fmsg);
/*--------------------------------------------------------------------------------------------------*/
/*		Fn206 : EasyFFT																				*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterEasyFft(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteEasyFft(FUNEXE *FunExe, void *Axis);
void     FnLeaveEasyFft(FUNEXE *FunExe, void *Axis);
INT32 FunEasyFftBegin(FUNMSG *Fmsg);
INT32 FunEasyFftMain(FUNMSG *Fmsg);
void FunEasyFftEnd(FUNMSG *Fmsg);
/*--------------------------------------------------------------------------------------------------*/
/*		Fn207 : Vibration Freq. Display and Notchi Filter Autoset									*/
/*--------------------------------------------------------------------------------------------------*/
PRM_RSLT FnEnterVibrationMonitor(FUNEXE *FunExe, void *Axis);
PRM_RSLT FnExecuteVibrationMonitor(FUNEXE *FunExe, void *Axis);
void     FnLeaveVibrationMonitor(FUNEXE *FunExe, void *Axis);
INT32 FunVibMonitorBegin(FUNMSG *Fmsg);
INT32 FunVibMonitorMain(FUNMSG *Fmsg);
void FunVibMonitorEnd(FUNMSG *Fmsg);




PRM_RSLT PnCmdJog(void *Axis);
void BpiJogExec(void *Axis);

/****************************************************************************************************/
/*		Auxiliary function management table Extern declaration										*/
/****************************************************************************************************/
extern  const FUNTBL FnFunTbl[];				// Auxiliary function management table				
extern  const UINT32 FnFunTblEntNum;				// Number of auxiliary function management tables registered


#endif

