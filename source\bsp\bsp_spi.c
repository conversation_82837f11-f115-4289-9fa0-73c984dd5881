#include "hal_data.h"
#include "bsp_spi.h"

/**
 * @file bsp_spi.c
 * @brief SPI communication driver implementation for Renesas RZN2L microcontroller
 * @details This file contains functions for SPI data transfer and reception
 */

/**
 * @brief Transfers data through the SPI interface
 * @param[in] tx_data   Pointer to transmit data buffer
 * @param[in] RxLenght  Number of data units to receive
 * @param[in] TxLenght  Number of data units to transmit
 * @param[in] Channel   SPI channel number (SPI_CH0 or SPI_CH1)
 * @return None
 * @note This function configures and initiates SPI data transmission
 *       It clears status registers, configures FIFO triggers, and loads data into transmit buffer
 */
void bsp_spi_transfer(uint32_t *tx_data, uint16_t RxLenght, uint16_t TxLenght,uint8_t Channel)
{
    R_SPI0_Type *spireg = (Channel == SPI_CH1)?(R_SPI1):(R_SPI3);
    
    
    /* Configure 32-Bit Mode. */
    uint32_t spcmd = spireg->SPCMD[0];  
    spcmd &= ~R_SPI0_SPCMD_SPB_Msk;
    spcmd |= 0x1F << R_SPI0_SPCMD_SPB_Pos;
    spireg->SPCMD[0] = spcmd;    

    /* Clear all status flags in the status register */
    spireg->SPSRC = (R_SPI0_SPSRC_SPDRFC_Msk) | (R_SPI0_SPSRC_OVRFC_Msk) | (R_SPI0_SPSRC_MODFC_Msk) |
                            (R_SPI0_SPSRC_PERFC_Msk) | (R_SPI0_SPSRC_UDRFC_Msk);

    /* Reset the FIFO by setting the SPFRST bit */
    spireg->SPFCR = R_SPI0_SPFCR_SPFRST_Msk;

    /* Disable the SPI transfer completion interrupt */
    spireg->SPCR_b.CENDIE = 0;


    /* Disable SPI operation before configuration */
    spireg->SPCR_b.SPE = 0;
    /* Configure transmit and receive FIFO trigger levels */
    spireg->SPDCR2_b.TTRG = TxLenght;      /* Set transmit FIFO trigger level */
    spireg->SPDCR2_b.RTRG = RxLenght-1;    /* Set receive FIFO trigger level */
    
    spireg->SPCR_b.SPE = 1;

    /* Load data into the transmit buffer */
    while(TxLenght-- && spireg->SPTFSR != 0)
    {
        spireg->SPDCR_b.SPRDTD = 1;         /* Set to transmit mode */
        spireg->SPDR_b.SPD = *tx_data++;    /* Write data to transmit register */
    }
    spireg->SPDCR_b.SPRDTD = 0;         /* Set to transmit mode */
    /* Enable the SPI Transfer. */
    
}

/**
 * @brief Checks the transmit buffer empty status of SPI
 * @param[in] Channel  SPI channel number (SPI_CH0 or SPI_CH1)
 * @return Transmit buffer empty flag status (1 = empty, 0 = not empty)
 * @note This function is used to determine if the SPI transmit buffer is ready for new data
 */
uint8_t bsp_spi_txstate(uint8_t Channel)
{
    R_SPI0_Type *spireg = (Channel == SPI_CH1)?(R_SPI1):(R_SPI3);

    /* Return the transmit buffer empty flag from status register */
    return spireg->SPSR_b.SPTEF;
}



/**
 * @brief Receives data from the SPI interface
 * @param[out] rx_data Pointer to receive data buffer
 * @param[in] length   Number of data units to receive
 * @param[in] Channel  SPI channel number (SPI_CH0 or SPI_CH1)
 * @return uint8_t     Status code (0 = success, 1 = error/incomplete data)
 * @note This function handles SPI data reception and error checking
 *       It checks for SPI errors, reads received data from FIFO, and clears status flags
 */
uint8_t bsp_spi_receive(uint32_t *rx_data, uint16_t length, uint8_t Channel)
{
    uint8_t result = 0;

    R_SPI0_Type *spireg = (Channel == SPI_CH1)?(R_SPI1):(R_SPI3);

    /* Check for SPI errors (overflow, mode fault, parity, underflow) */
    if(spireg->SPSR & SPI_ERROR_MASK)
    {
       /* Clear all error flags */
       spireg->SPSRC = (R_SPI0_SPSRC_PERFC_Msk) | (R_SPI0_SPSRC_MODFC_Msk) | (R_SPI0_SPSRC_OVRFC_Msk) |
                            (R_SPI0_SPSRC_UDRFC_Msk);

       /* Read the status register to ensure flags are cleared (dummy read) */
       uint16_t dummy = spireg->SPSRC;
      (void) ((dummy));
    }


    /* Check if the receive FIFO has the expected number of bytes */
    if(spireg->SPRFSR == length)
    {
      spireg->SPDCR_b.SPRDTD = 0;    /* Set to receive mode */

      /* Read all data from the receive FIFO */
      while (spireg->SPRFSR != 0)
      {
        *rx_data++ = spireg->SPDR;    /* Read data from receive register */
      }
    }
    else
    {
      result = 1;    /* Return error if received data count doesn't match expected length */
    }
    /* Clear the SPI Receive Data Ready Flag if set */
    if (1 == spireg->SPSR_b.SPDRF)
    {
        spireg->SPSRC_b.SPDRFC = 1;    /* Write 1 to clear the flag */
    }

    /* Clear the receive buffer full flag */
    spireg->SPSRC = R_SPI0_SPSRC_SPRFC_Msk;    /* Write 1 to clear the flag */

    /* If SPI is idle (not busy), disable SPI operation */
    if(0 == spireg->SPSR_b.IDLNF )
    {
      spireg->SPCR_b.SPE = 0;    /* Disable SPI operation */
    }

    return result;
}


/**
 * @brief Checks the receive buffer full status of SPI
 * @param[in] Channel  SPI channel number (SPI_CH0 or SPI_CH1)
 * @return Receive buffer full flag status (1 = full, 0 = not full)
 * @note This function is used to determine if the SPI receive buffer has data available to read
 */
uint8_t bsp_spi_rxstate(uint8_t Channel)
{
    R_SPI0_Type *spireg = (Channel == SPI_CH1)?(R_SPI1):(R_SPI3);

    /* Return the receive buffer full flag from status register */
    return spireg->SPSR_b.SPDRF;
}




/**
 * @brief Transfers data through the SPI interface
 * @param[in] tx_data   Pointer to transmit data buffer
 * @param[in] RxLenght  Number of data units to receive
 * @param[in] TxLenght  Number of data units to transmit
 * @param[in] Channel   SPI channel number (SPI_CH0 or SPI_CH1)
 * @return None
 * @note This function configures and initiates SPI data transmission
 *       It clears status registers, configures FIFO triggers, and loads data into transmit buffer
 */
void bsp_spi_readwrite(uint32_t *tx_data, uint16_t RxLenght, uint16_t TxLenght,uint8_t Channel)
{
    R_SPI0_Type *spireg = (Channel == SPI_CH1)?(R_SPI1):(R_SPI3);
    
    
    uint32_t spcmd = spireg->SPCMD[0];
        /* Configure 32-Bit Mode. */
    spcmd &= ~R_SPI0_SPCMD_SPB_Msk;
    spcmd |= 0x1F << R_SPI0_SPCMD_SPB_Pos;
    
    spireg->SPCMD[0] = spcmd;

    /* Clear all status flags in the status register */
    spireg->SPSRC = (R_SPI0_SPSRC_SPDRFC_Msk) | (R_SPI0_SPSRC_OVRFC_Msk) | (R_SPI0_SPSRC_MODFC_Msk) |
                            (R_SPI0_SPSRC_PERFC_Msk) | (R_SPI0_SPSRC_UDRFC_Msk);

    /* Reset the FIFO by setting the SPFRST bit */
    spireg->SPFCR = R_SPI0_SPFCR_SPFRST_Msk;

    /* Disable the SPI transfer completion interrupt */
    spireg->SPCR_b.CENDIE = 0;

    /* Disable SPI operation before configuration */
    spireg->SPCR_b.SPE = 0;
    /* Configure transmit and receive FIFO trigger levels */
    spireg->SPDCR2_b.TTRG = TxLenght;      /* Set transmit FIFO trigger level */
    spireg->SPDCR2_b.RTRG = RxLenght-1;    /* Set receive FIFO trigger level */
    
    spireg->SPCR_b.SPE = 1;

    uint32_t             tx_count = 0;
    while (spireg->SPTFSR != 0 && tx_count < TxLenght)
    {

       spireg->SPDR = ((uint32_t *) tx_data)[tx_count];;

        tx_count++;
    }
    spireg->SPDCR_b.SPRDTD = 0;         /* Set to transmit mode */
    /* Enable the SPI Transfer. */
    
}



/****************************************************************************************************
 * DESCRIPTION: Spi3 device init 
 * @param[in] mode: 0 - SPI3_BISSC_MODE 1 - ktm59xx spi mode, 2 - am600 spi mode
 * RETURNS:
 *
****************************************************************************************************/
void bsp_spi3_init(uint16_t mode)
{
  if(mode == SPI3_BISSC_MODE)  // Bissc spi mode
  {
    R_SPI_Open(&g_spi3_ctrl, &g_spi3_cfg[0]);
  }
  else if(mode == SPI3_KTM59XX_MODE)  // ktm59xx spi mode
  {
    R_SPI_Open(&g_spi3_ctrl, &g_spi3_cfg[1]);
  }
  else if(mode == SPI3_AM600_MODE)  // am600 spi mode
  {
    R_SPI_Open(&g_spi3_ctrl, &g_spi3_cfg[2]);
  } 
  else  // default is bissc spi mode
  {
    R_SPI_Open(&g_spi3_ctrl, &g_spi3_cfg[0]);
  }
}


/****************************************************************************************************
 * DESCRIPTION: Spi3 device close 
 * RETURNS:
 *
****************************************************************************************************/
void bsp_spi3_close (void)
{
  R_SPI_Close(&g_spi3_ctrl);
}

