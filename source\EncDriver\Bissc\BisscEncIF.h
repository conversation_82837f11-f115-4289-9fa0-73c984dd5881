/****************************************************************************************************
 *
 * FILE NAME:  Bissc.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.09.08
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	08-09-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _BISSC_IF_H_
#define _BISSC_IF_H_
#include "stdint.h"
#include "Encoder.h"


#define BISSC_READ_POSCMD     (0)
#define BISSC_READ_REGCMD     (1)
#define BISSC_WRITE_REGCMD    (2)

#define BISS_DATA_LENGTH 8
#define BISS_CRCLEN      6
#define BISS_ALARMLEN    2
#define BISS_HEAD        6



uint8_t hApi_IniBissC(uint16_t ch);
void hApi_BisscCmd(uint8_t Cmd, uint16_t ch);
uint8_t hApi_Bissc_GetPos(uint32_t *pdata,  uint16_t ch);
uint8_t hApi_2Bissc_PosDecoding(ENCODER *pEnc,ENCODER *pEnc2);
uint8_t hApi_Bissc_PosDecoding(ENCODER *pEnc,ENCODER *pEnc2);
#endif  //#ifndef _TAMAGAWA_IF_H_

