/****************************************************************************************************
 *
 * FILE NAME:  FnAtypeVibTune.h
 *
 * DESCRIPTION:  A Type Vibration Tuning
 *
 * CREATED ON:  2021.02.19
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	19-02-2021 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FN_ATYPE_VIB_TUNE_H_
#define _FN_ATYPE_VIB_TUNE_H_

#include "BaseDef.h"
#include "RegAccessIf.h"




/****************************************************************************************************/
/*																									*/
/*		Structure Definition																		*/
/*																									*/
/****************************************************************************************************/
typedef	struct AVIBTUNE
{
	struct {
		UINT16	AvibMode;			/* 0x20A0 : Type A damping function tuning mode setting			*/
		UINT16	AvibState;			/* 0x20A1 : A type damping function tuning completed			*/
		UINT16	AvibFreq;			/* 0x20A2 : Type A damping function tuning frequency			*/
		UINT16	AvibGain;			/* 0x20A3 : Type A damping function tuning gain					*/
	} OpeReg;
/*--------------------------------------------------------------------------------------------------*/
	struct {
		INT16	FftStat;			/* Frequency analysis status									*/
		INT16	dummy;				/* for Alignment												*/
		BOOL	FftReq;				/* Frequency analysis request									*/
		BOOL	Match;				/* Does the display match the operation?						*/
		INT32	VibDetLvl;			/* Vibration detection level									*/
		UINT32	TimeOutCtr;			/* Timeout detection counter									*/
	} var;
} AVIBTUNE;



/****************************************************************************************************/
/*																									*/
/*		Constant Definition																			*/
/*																									*/
/****************************************************************************************************/
enum AVIB_SEQ_STS {
	AVIB_SEQ_INIT		= 0,		
	AVIB_SEQ_MODESEL,				/* Automatic / manual mode selection							*/
	AVIB_SEQ_TRGWAIT,				/* Waiting for vibration detection trigger						*/
	AVIB_SEQ_MEASURE,				/* During frequency analysis									*/
	AVIB_SEQ_COMP,					/* Frequency analysis completed (waiting for filter setting)	*/
	AVIB_SEQ_SET,					/* Filter setting												*/
	AVIB_SEQ_FREQTUNE,				/* Frequency adjustment											*/
	AVIB_SEQ_GAINTUNE,				/* Damping gain adjustment										*/
	AVIB_SEQ_END,					/* End processing												*/
	AVIB_SEQ_NOOPWAIT,				/* No Operation 												*/
};
/*--------------------------------------------------------------------------------------------------*/
enum AVIB_CMP_STS {
	AVIB_STS_NONCOMP	= 0,		/* Vibration detection completed: Detection not completed		*/
	AVIB_STS_COMP,					/* Vibration detection completed: Detection completed			*/
	AVIB_STS_TIMEOUT,				/* Vibration detection completed: Cannot be detected (timeout)*/
	AVIB_STS_RANGEOV,				/* Vibration detection completed: Cannot be detected (outside the frequency range)*/
	AVIB_STS_NONVIB,				/* Vibration detection completed: Cannot be detected (Frequency cannot be specified)*/
};
/*--------------------------------------------------------------------------------------------------*/
#define		AVIB_FRQ_MIN			100		/* Lower limit of detection frequency 100Hz				*/
#define		AVIB_FRQ_MAX			1000	/* Upper limit of detection frequency 1000Hz			*/
/*--------------------------------------------------------------------------------------------------*/
#define		AVIB_MODE_MIN			0		/* Mode lower limit										*/
#define		AVIB_MODE_MAX			1		/* Mode upper limit										*/
/*--------------------------------------------------------------------------------------------------*/
#define		AVIB_GTUN_MAX			2		/* Gain tuning maximum digit							*/
#define		AVIB_FTUN_MAX			4		/* Maximum frequency tuning digit						*/
#define		AVIB_TUN_MIN			1		/* Gain / frequency tuning minimum digit				*/



/****************************************************************************************************/
/*																									*/
/*		Function Prototype Definition																*/
/*																									*/
/****************************************************************************************************/
PRM_RSLT	RpiFunSetAtypeVibTuningMode( AVIBTUNE *AvibTune, UINT16 Mode );
PRM_RSLT	RpiFunSetAtypeVibTuningFrequency( AVIBTUNE *AvibTune, UINT16 Frq );
PRM_RSLT	RpiFunSetAtypeVibTuningGain( AVIBTUNE *AvibTune, UINT16 Gain );
void		RpiRegSetAtypeVibTuningFrequency( AVIBTUNE *AvibTune, UINT16 Frq );
void		RpiRegSetAtypeVibTuningGain( AVIBTUNE *AvibTune, UINT16 Gain );



#endif

