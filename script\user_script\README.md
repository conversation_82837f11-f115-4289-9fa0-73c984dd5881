# CRC32 Checksum Tool

This tool adds a CRC32 checksum to binary files, which is useful for data integrity verification.

## File Structure

- Input file (`RF200.bin`) should be located in the same directory as the `script` folder
- Output file (`MeteoriteX01_yyyy_mm_dd.bin`) will be saved in the `Binary` folder (which will be created if it doesn't exist)

## Usage

### Adding CRC32 Checksum

#### Basic Usage
Simply run the batch file to add a CRC32 checksum to the default file:

```
crc_add.bat
```

This will:
1. Read the input file `RF200.bin` from the parent directory
2. Add a CRC32 checksum to it
3. Save the result as `MeteoriteX01_yyyy_mm_dd.bin` in the Binary folder (where yyyy_mm_dd is the current date)

#### Custom Files
You can specify custom input and output files:

```
crc_add.bat path\to\input.bin path\to\output.bin
```

### Verifying CRC32 Checksum

To verify a file that has a CRC32 checksum appended to it:

```
crc_verify.bat [file_with_crc]
```

If no file is specified, the script will look for today's output file in the Binary folder.

The script will:
1. Extract the appended CRC32 from the file
2. Calculate the CRC32 of the data portion
3. Compare the two values and report if they match

## How It Works

1. The `crc_add.bat` script reads the input binary file
2. Calculates a CRC32 checksum of the file content
3. Appends the 4-byte CRC32 value to the end of the file
4. Saves the result as a new file with the current date in the filename
5. Places the file in the Binary folder

The `crc_verify.bat` script performs the reverse process to validate the file integrity.

## Requirements

- Windows operating system
- srec_cat.exe (included in the package)
- srec_cmp.exe (included in the package, used for verification)

## Notes

- The CRC32 is appended in little-endian byte order
- The output file will be 4 bytes larger than the input file (the size of the CRC32 value)
- If the output file already exists, it will be overwritten
- The verification script returns exit code 0 if the CRC is valid, non-zero otherwise 