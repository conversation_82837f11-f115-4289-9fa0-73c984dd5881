/****************************************************************************************************
 *
 * FILE NAME:  BaseControls.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.27
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	27-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "PosManager.h"
#include "BaseControls.h"
#include "ExControls.h"
#include "Global.h"
#include "FunGenerator.h"
#include "Home.h"
#include "Mlib.h"
#include "math.h"
#include "GainChange.h"
#include "BasePrmCal.h"
#include "canfd_protocol.h"


PUBLIC void Cia402_UpdateFeedback(BASE_LOOP *BaseLoops,UINT32 DiStatus);
PRIVATE REAL32 BaseLinDecStopSpdRef(REAL32 SpdRef, REAL32 Dec);
PRIVATE void BpiTrajectoryPlanning(AXIS_HANDLE *AxisB);
PRIVATE REAL32 OvrTrqSpdLimCal(CTRL_CMD_MNG *CtrlCmdMng, CTRL_CMD_PRM *CtrlCmdPrm, INT32 SpdFbk);
PRIVATE REAL32 BpiIpdTorqueLimit(BASE_CTRL *pBaseCtrl, BASE_LOOP  *BaseLoops, REAL32 TrqRef);
PRIVATE INT32 BaseSoftStartSpdRef( CTRL_CMD_PRM *CtrlCmdPrm, INT32 SpdRef, INT32 SpdRefx );

extern PUBLIC void BpxJatOfflineCalc( JATHNDL	*jathdl, CTRL_CMD_PRM *CtrlCmdPrm, 
														CTRL_CMD_MNG *CtrlCmdMngr, INT32 TrqRefo_a );

extern void BpiJogExec(AXIS_HANDLE *Axis);
extern PUBLIC UINT16 BpiFindMotorParameter(AXIS_HANDLE *Axis);
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BaseControlInit(AXIS_HANDLE *AxisB)
{
    BASE_CTRL         *BaseCtrls;

	BaseCtrls   = AxisB->BaseCtrls;
	
//	BaseCtrls->GainChange.GselGains[0] = BaseCtrls->GainChange.GselGains[1];
//	BaseCtrls->GainChange.GselDobs[0] = BaseCtrls->GainChange.GselDobs[1];

	BaseCtrls->CtrlCmdPrm.OverTrqLoopGain[0] =BaseCtrls->CtrlCmdPrm.OverTrqLoopGain[1] ;
	BaseCtrls->CtrlCmdPrm.OverTrqIntegGain[0] =BaseCtrls->CtrlCmdPrm.OverTrqIntegGain[1] ;

	GselInitServoGainChange( &(BaseCtrls->GainChange) );

	#if NO_ADVFUNC
	EhSpeedObserverInitialize( &(BaseCtrls->EhVobsCtrl) );
	DisturbObserverInitialize( &(BaseCtrls->DobsCtrl), &(BaseCtrls->GainChange.GselDobs[0]) );
	#endif

	PosMngInitPositionManager( BaseCtrls );

	BaseCtrls->CtrlCmdMngr.dPosRefo = 0;

	FFCmdFilInitialize( &(BaseCtrls->VFFcmdFil) );

	FFCmdFilInitialize( &(BaseCtrls->TFFcmdFil) );

//	PcmdFilCalculatePrmVibSupFil(&(BaseCtrls->VibSupFil), 71, 100, 250000);
	#if NO_ADVFUNC
	TuneLessCtrlInitialize( &(BaseCtrls->TuneLessCtrl) );
	

	DetVibObsInitialize( AxisB->DetVib );

	ResVibATypeResVibInitialize( &(BaseCtrls->ResVib) );

	DetVibTraceInitialize( AxisB->DetVib );

	/* Initialize Settling Time Variable and Start Settling Time Process */
	StlgInitSettlingTime( &(BaseCtrls->SettlingTime), PS_CYCLEUS );
	#endif

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void TaskBInputCtrlCmd(AXIS_HANDLE *AxisB)
{
	INT32				GainChgTriger;
	CTRLMD				CtrlMode;
    
#if 0
	static UINT32 LockCnt = 0;

	if(LockCnt++ > 16000*3600*2)   // 
	{
		ALMSetGlobalAlarm( ALM_ISR_TIME );
		hApi_BaseBlock(AxisB->AxisID);
		return;
	}
#endif    

	BASE_CTRL *BaseCtrls =  AxisB->BaseCtrls;
	BE_SEQ_HNDL  *BaseSeq = AxisB->BaseSeq;

	BaseCtrls->ModeSw.var.Acceleration = AxisB->SeqCtrlOut->ModeSwAcc;
	
	/* Control mode: scan C instructions */
	CtrlMode.dw = AxisB->SeqCtrlOut->CtrlModeReq.dw;

	/* Scan C control mode command check */
	if( AxisB->SeqCtrlOut->CtrlModeReq.us.uwd_l == CTRL_MODE_NOCMD )
	{
		CtrlMode.b.cm = (UINT8)BaseCtrls->Cia402Axis.Objects->ModesOfOperationDisplay0x6061;
	}

	/* Control mode previous value */
	BaseCtrls->CtrlModeSet.CtrlModeLst.dw = BaseCtrls->CtrlModeSet.CtrlMode.dw;
	/* Control mode change information */
	BaseCtrls->CtrlModeSet.CtrlModeChg.dw = CtrlMode.dw ^ BaseCtrls->CtrlModeSet.CtrlMode.dw;
	/* Control mode current value */
	BaseCtrls->CtrlModeSet.CtrlMode.dw	 = CtrlMode.dw;

	BaseCtrls->BaseEnable = AxisB->BaseSeq->BeReqSeqOut;
	BaseCtrls->CmdEnable = AxisB->BaseSeq->CmdEnable && BaseCtrls->BaseEnable;

	/* Servo gain switching */
	if( (BaseCtrls->GainChange.AtGsel == TRUE)
		&& (BaseCtrls->TuneLessCtrl.var.TuneLessAct == FALSE) )					
	{ /* Automatic gain switching &&  Tuneless is disabled*/
		/* Switching judgment processing */
		GainChgTriger = GselDetGainChngTiming( BaseCtrls->PosCtrlSts.CoinSignal,
											   BaseCtrls->PosCtrlSts.NearSignal,
											   BaseCtrls->PosCtrlSts.RefZSignal,
											   BaseCtrls->GainChange.AtGselTrgA );
		
		GselAutoGainChange( BaseCtrls, GainChgTriger );	
	}
	else
	{ /* Manual gain switching process*/
		GselManualGainChange( BaseCtrls );				

		GselRstAutoGainChange( &(BaseCtrls->GainChange) );	
	}

	/* Zero control mode instruction processing: commands from task B common input processing */
//	if(BaseCtrls->CtrlModeSet.CtrlMode.b.zm != BASE_MODE_ZNONE) 
	{ 
//		BaseCtrls->CtrlModeSet.CtrlMode.b.zm    = BaseCtrls->Cia402Axis.StopCode;
		BaseCtrls->CtrlModeSet.ZctrlMode.zm     = BaseCtrls->CtrlModeSet.CtrlMode.b.zm;
		BaseCtrls->CtrlModeSet.ZctrlMode.zmchg  = BaseCtrls->CtrlModeSet.CtrlModeChg.b.zm;
	}


	BaseCtrls->CtrlCmdMngr.SpdFFC = 0;		
	BaseCtrls->CtrlCmdMngr.SpdFBC = 0;		
	BaseCtrls->CtrlCmdMngr.TrqFFC = 0;		
	BaseCtrls->CtrlCmdMngr.TrqFBC = 0;		


}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BaseServoControl(AXIS_HANDLE *AxisB)
{
	INT32           Tmp32;
	REAL32          LdStpDec = 0.0f;
	INT32 			DeltaPos;
	REAL32			TrqPosComp;
	REAL32			TrqPosCompKx = 0;
	REAL32		    DeltaComp;
	
#if 0
	static UINT32 LockCnt = 0;

	if(LockCnt++ > 16000*3600*2)   //
	{
		ALMSetGlobalAlarm( ALM_ISR_TIME );
		hApi_BaseBlock(AxisB->AxisID);
		return;
	}
#endif    
    
	BASE_LOOP       *BaseLoops = AxisB->BaseLoops;
	BASE_CTRL       *BaseCtrls = AxisB->BaseCtrls;
    BE_SEQ_HNDL     *BaseSeq = AxisB->BaseSeq;
	CiA402_PRM	    *Prm402 = AxisB->BaseCtrls->Cia402Axis.Objects;
	PRMDATA         *Prm = AxisB->Prm;
//    CTRL_CFG_PRM    *CtrlPrm = &AxisB->Prm->PnCtrlCfgPrm;
//    Inertia_FIND  *InertiaIdent = &AxisB->MotorIdentify->InertiaIdent;
//    MOTORPARAM_FIND*    MotorParamIdent = &AxisB->MotorIdentify->MotorParamIdent;
    POS_MNG_HNDL	*PosManager = &(BaseCtrls->PosManager);

	BpiTrajectoryPlanning(AxisB);

	
	switch(BaseCtrls->CtrlCmdMngr.CtrlMode)
	{
	case BASE_MODE_POS:
#if 1
		switch(BaseCtrls->CtrlCmdMngr.LastCtrlMode)
		{
		case BASE_MODE_TRQ:
     

                        BaseCtrls->CtrlCmdMngr.TrqRef = 0;
                        BaseCtrls->CtrlCmdMngr.SpdRef = 0;
                        BaseCtrls->CtrlCmdMngr.SpdRefFilo =0;
                        
                        BaseLoops->PosLoop.Per64a[0] = 0;
                        BaseLoops->PosLoop.Per64[0] = 0;
                        BaseLoops->PosLoop.Per64a[1] = 0;
                        BaseLoops->PosLoop.Per64[1] = 0;

                        MlibPerrcalx(PosManager->VirtualPositionError, 0, BaseLoops->PosLoop.Per64a);
                        MlibPerrcalx(PosManager->VirtualPositionError, 0, BaseLoops->PosLoop.Per64);
                        
                        BaseCtrls->CtrlCmdMngr.PosRef_l = PosManager->PgPos.apos[0] +PosManager->VirtualPositionError;  

                        
			if( BaseCtrls->TuneLessCtrl.var.TuneLessInvldReq == FALSE )
			{
				BaseCtrls->TuneLessCtrl.var.TuneLessAct = BaseCtrls->TuneLessCtrl.conf.TuningLessUse;
			}
                        
                      	break;

		case BASE_MODE_SPD:		                       
                        BaseCtrls->CtrlCmdMngr.SpdRef = 0;
                        BaseCtrls->CtrlCmdMngr.SpdRefFilo =0;
                        
                        BaseLoops->PosLoop.Per64a[0] = 0;
                        BaseLoops->PosLoop.Per64[0] = 0;
                        BaseLoops->PosLoop.Per64a[1] = 0;
                        BaseLoops->PosLoop.Per64[1] = 0;

                        MlibPerrcalx(PosManager->VirtualPositionError, 0, BaseLoops->PosLoop.Per64a);
                        MlibPerrcalx(PosManager->VirtualPositionError, 0, BaseLoops->PosLoop.Per64);


                        BaseCtrls->CtrlCmdMngr.PosRef_l = PosManager->PgPos.apos[0]  +PosManager->VirtualPositionError;   


			break;

		default:
			break;
		}
#endif 

        if(BaseCtrls->CtrlModeSet.ZctrlMode.zm != BASE_MODE_ZNONE 
                &&(AxisB->FnCmnCtrl->FnSvControl == FALSE) && (BaseCtrls->BaseEnable))
        {
           BaseCtrls->CtrlCmdMngr.PosRef = BaseLoops->BaseCtrls->CtrlCmdMngr.LdStpPosRef;
        }

	    BaseCtrls->CtrlCmdMngr.dPosRefo = PosMngNetPosCmdManager( BaseLoops,  BaseLoops->Bprm );  
            

		if(BaseCtrls->CtrlCmdPrm.SpdffCfg == FEED_FORWARD_INNER && BaseCtrls->MFControl.var.CurMFCModel == 0)
		{
			REAL32 SpdFFx = (REAL32)BaseCtrls->CtrlCmdMngr.dPosRefo * BaseCtrls->CtrlCmdPrm.Kspdff;

			BaseCtrls->CtrlCmdMngr.SpdffFilo = FlibLpfilter1(SpdFFx,
														BaseCtrls->CtrlCmdPrm.KspdffFil,
														BaseCtrls->CtrlCmdMngr.SpdffFilo );
            BaseCtrls->CtrlCmdMngr.SpdFFC += FlibLimitul(BaseCtrls->CtrlCmdMngr.SpdffFilo, (REAL32)0x01000000, (REAL32)-0x01000000 );

		}
		else if(BaseCtrls->CtrlCmdPrm.SpdffCfg == FEED_FORWARD_OUT 
               && (BaseCtrls->MFControl.var.CurMFCModel == 0 || BaseCtrls->MFControl.conf.MFCFF == TRUE) )
		{
            BaseCtrls->CtrlCmdMngr.SpdFFCFilo = FFcmdFilMoveAverageFilter( &(BaseCtrls->VFFcmdFil),
																   BaseCtrls->CtrlCmdMngr.NetSpdFFC ,
																   FALSE);
			BaseCtrls->CtrlCmdMngr.SpdFFC += BaseCtrls->CtrlCmdMngr.SpdFFCFilo;
		}
		else
		{
			BaseCtrls->CtrlCmdMngr.SpdFFC += 0;
		}
    
		if(BaseLoops->UseEncoder2)
		{
		   BaseCtrls->CtrlCmdMngr.SpdFFCFilo = Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, BaseCtrls->CtrlCmdMngr.SpdFFC );
		}
		else
		{
		   BaseCtrls->CtrlCmdMngr.SpdFFCFilo = BaseCtrls->CtrlCmdMngr.SpdFFC;
		}
	   

		
        BaseCtrls->CtrlCmdMngr.SpdFFCFilo = FlibLimitul( BaseCtrls->CtrlCmdMngr.SpdFFCFilo, NOR_MAXVAL_FLOAT, -NOR_MAXVAL_FLOAT );		
		if(BaseCtrls->CtrlCmdPrm.TrqffCfg == FEED_FORWARD_INNER && BaseCtrls->MFControl.var.CurMFCModel == 0)
		{
			REAL32 TrqFFx = BaseLoops->Bprm->KmotspdF *(BaseCtrls->CtrlCmdMngr.dPosRefo - BaseCtrls->CtrlCmdMngr.dPosRef_l);
            
			TrqFFx = TrqFFx * BaseCtrls->CtrlCmdPrm.Ktrqff;

			BaseCtrls->CtrlCmdMngr.TrqffFilo = FlibLpfilter1(TrqFFx,
														BaseCtrls->CtrlCmdPrm.KtrqffFil,
														BaseCtrls->CtrlCmdMngr.TrqffFilo );

			BaseCtrls->CtrlCmdMngr.TrqFFC += FlibLimitul(BaseCtrls->CtrlCmdMngr.TrqffFilo, (REAL32)0x01000000, (REAL32)-0x01000000 );
            
		}
		else if(BaseCtrls->CtrlCmdPrm.TrqffCfg == FEED_FORWARD_OUT
                && (BaseCtrls->MFControl.var.CurMFCModel == 0 || BaseCtrls->MFControl.conf.MFCFF == TRUE) )
		{
            BaseCtrls->CtrlCmdMngr.TrqFFCFilo = FFcmdFilMoveAverageFilter( &(BaseCtrls->TFFcmdFil),
																   BaseCtrls->CtrlCmdMngr.NetTrqFFC ,
																   FALSE);
            
			BaseCtrls->CtrlCmdMngr.TrqFFC += BaseCtrls->CtrlCmdMngr.TrqFFCFilo;
		}
		else
		{
			BaseCtrls->CtrlCmdMngr.TrqFFC += 0;
		}

		if((BaseSeq->BkSeqData.V.LastBrake == 1)&&(BaseSeq->BkSeqData.V.Brake == 0))
		{
				BaseCtrls->CtrlCmdMngr.TrqPosFlag = 1;
			  BaseCtrls->CtrlCmdMngr.TrqPosCompTime = 0;
		}
		
		BaseSeq->BkSeqData.V.LastBrake = BaseSeq->BkSeqData.V.Brake;
		
		DeltaPos = BaseLoops->PosLoop.PosErr;
		if((BaseCtrls->CtrlCmdMngr.TrqPosFlag)&&(BaseCtrls->PosDisCDisable != 1))
		{
			if (DeltaPos > 10 || DeltaPos < -10)
			{
				DeltaComp = BaseCtrls->CtrlCmdMngr.TrqPosCompTime*BaseCtrls->PosDisturCompKx/4000;
				TrqPosCompKx = BaseCtrls->PosDisturCompKx - DeltaComp;
				if(BaseCtrls->CtrlCmdMngr.TrqPosCompTime > 4000)
				{			 
					BaseCtrls->CtrlCmdMngr.TrqPosCompTime = 4000;
					BaseCtrls->CtrlCmdMngr.TrqPosFlag = 0;				
					BaseCtrls->CtrlCmdMngr.TrqPosComp = 0;
				}
				else
				{
					TrqPosComp = DeltaPos * TrqPosCompKx;		
					BaseCtrls->CtrlCmdMngr.TrqPosComp = TrqPosComp * BaseLoops->Bprm->Kmottrq;				
				}
				BaseCtrls->CtrlCmdMngr.TrqPosCompTime++;				
			}
		}
		else
		{
			BaseCtrls->CtrlCmdMngr.TrqPosComp = 0;
			BaseCtrls->CtrlCmdMngr.TrqPosCompTime = 0;
		}

		BaseCtrls->CtrlCmdMngr.TrqFFCFilo = BaseCtrls->CtrlCmdMngr.TrqFFC;

		BaseCtrls->CtrlCmdMngr.SpdRef = AxisB->CtrlLoopOut->SpdRefo;
		// BpxJatOfflineCalc( &BaseCtrls->JatHdl, &BaseCtrls->CtrlCmdPrm,
		// 				                 &BaseCtrls->CtrlCmdMngr, BaseLoops->TrqRefo );	
	
		break;
		
	case BASE_MODE_SPD: // Speed control mode
                  
		// Apply low-pass filter to speed reference
		BaseCtrls->CtrlCmdMngr.SpdRefFilo = FlibLpfilter1(BaseCtrls->CtrlCmdMngr.SpdRef,
									  				  BaseCtrls->CtrlCmdPrm.KVrfFil,
									   				  BaseCtrls->CtrlCmdMngr.SpdRefFilo );
		
		BaseCtrls->CtrlCmdMngr.SpdRef = BaseCtrls->CtrlCmdMngr.SpdRefFilo; // Update speed reference
        
		BaseCtrls->CtrlCmdMngr.SpdFFCFilo = 0; // Reset filtered speed feedforward
        
		// Handle torque feedforward configuration
		if(BaseCtrls->CtrlCmdPrm.TrqffCfg == FEED_FORWARD_INNER)
		{
			REAL32 TrqFFx = BaseCtrls->CtrlCmdMngr.SpdRef - BaseCtrls->CtrlCmdMngr.SpdRef_l; // Calculate torque feedforward
			TrqFFx = TrqFFx * BaseCtrls->CtrlCmdPrm.Ktrqff; // Scale by torque feedforward coefficient

			// Apply low-pass filter to torque feedforward
			BaseCtrls->CtrlCmdMngr.TrqffFilo = FlibLpfilter1(TrqFFx,
														BaseCtrls->CtrlCmdPrm.KtrqffFil,
														BaseCtrls->CtrlCmdMngr.TrqffFilo );
            
            BaseCtrls->CtrlCmdMngr.TrqFFC += BaseCtrls->CtrlCmdMngr.TrqffFilo; // Update torque feedforward command
		}
		else if(BaseCtrls->CtrlCmdPrm.TrqffCfg == FEED_FORWARD_OUT)
		{
            // Apply moving average filter for torque feedforward
            BaseCtrls->CtrlCmdMngr.TrqFFCFilo = FFcmdFilMoveAverageFilter(&(BaseCtrls->TFFcmdFil),
																   BaseCtrls->CtrlCmdMngr.NetTrqFFC,
																   FALSE);
			BaseCtrls->CtrlCmdMngr.TrqFFC += BaseCtrls->CtrlCmdMngr.TrqFFCFilo; // Update torque feedforward command
		}
		else
		{
			BaseCtrls->CtrlCmdMngr.TrqFFC += 0;
		}
		
		BaseCtrls->CtrlCmdMngr.TrqFFCFilo = BaseCtrls->CtrlCmdMngr.TrqFFC;

		break;

	case BASE_MODE_TRQ:

		BaseCtrls->CtrlCmdMngr.SpdFFCFilo = 0;
        BaseCtrls->CtrlCmdMngr.TrqFFCFilo = 0;
		break;
	default:
        break;	
	}


	// stop mode
	if(BaseCtrls->CtrlCmdMngr.CtrlMode == BASE_MODE_TRQ 
		&&(BaseCtrls->CtrlModeSet.ZctrlMode.zm != BASE_MODE_ZNONE))
	{
//		  BaseCtrls->CtrlCmdMngr.TrqRef = 0.0f;
	}
/*
	else if(BaseCtrls->CtrlModeSet.ZctrlMode.zm == ZCTRL_MODE_SLOW_STOP)
	{
		LdStpDec = (REAL32)Prm402->ProfileDeceleration0x6084 * BaseLoops->Bprm->Kmotspd/TASKB_FRQ;
		
		FlibRampCal(0.0f, &BaseCtrls->CtrlCmdMngr.LdStpSpdRef, LdStpDec);

	}
 */
	else if(BaseCtrls->CtrlModeSet.ZctrlMode.zm == BASE_MODE_LDSTOP )
	{
		LdStpDec = (REAL32)Prm402->QuickstopDeceleration0x6085 * BaseLoops->Bprm->Kmotspd/TASKB_FRQ;
                BaseCtrls->CtrlCmdMngr.LdStpPosRef = BaseCtrls->CtrlCmdMngr.PosRef;
		
		FlibRampCal(0.0f, &BaseCtrls->CtrlCmdMngr.LdStpSpdRef, LdStpDec);
	}
/*	
	else if(BaseCtrls->CtrlModeSet.ZctrlMode.zm == ZCTRL_MODE_CUR_STOP )
	{		
		BaseCtrls->CtrlCmdMngr.LdStpSpdRef = 0;
		BaseCtrls->CtrlCmdMngr.LdStpPosRef = BaseCtrls->CtrlCmdMngr.PosRef;
	}
*/
	else if(BaseCtrls->CtrlModeSet.ZctrlMode.zm == BASE_MODE_ZCLMP )
	{		
              BaseCtrls->CtrlCmdMngr.LdStpPosRef = BaseCtrls->CtrlCmdMngr.PosRef;
	}      
	else if(BaseCtrls->CtrlModeSet.ZctrlMode.zm != BASE_MODE_LDSTOP )
	{
		BaseCtrls->CtrlCmdMngr.LdStpSpdRef = BaseLoops->SpdRefSum;
                BaseCtrls->CtrlCmdMngr.LdStpPosRef = BaseCtrls->CtrlCmdMngr.PosRef;
	}


	if((BaseCtrls->CtrlCmdMngr.CtrlMode != BASE_MODE_POS)
				||(!BaseCtrls->BaseEnable)
		        || BaseCtrls->CtrlModeSet.ZctrlMode.zm == BASE_MODE_LDSTOP
		        || BaseCtrls->CtrlModeSet.ZctrlMode.zm == BASE_MODE_ZSTOP )
	{
		PosMngClrPosCmd(BaseLoops);
	}

	BaseCtrls->CtrlCmdMngr.PosRef_l = BaseCtrls->CtrlCmdMngr.PosRef;
	BaseCtrls->CtrlCmdMngr.dPosRef_l = BaseCtrls->CtrlCmdMngr.dPosRefo;
	BaseCtrls->CtrlCmdMngr.SpdRef_l = BaseCtrls->CtrlCmdMngr.SpdRef;
	BaseCtrls->CtrlCmdMngr.LastCtrlMode = BaseCtrls->CtrlCmdMngr.CtrlMode;
        
        PosManager->PgPos.apos[0] = Prm402->PositionActualValue0x6064;
       
}



/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void BpiTrajectoryPlanning(AXIS_HANDLE *AxisB)
{
	INT32           Tmp32;
	INT16           Tmp16;	
        BASE_LOOP       *BaseLoops = AxisB->BaseLoops;
	BASE_CTRL       *BaseCtrls = AxisB->BaseCtrls;
	CiA402_PRM	    *Prm402 = AxisB->BaseCtrls->Cia402Axis.Objects;
    CTRL_CFG_PRM    *CtrlPrm = &AxisB->Prm->PnCtrlCfgPrm;
        BASE_CFG_PRM    *CfgPrm = &AxisB->Prm->PnCfgPrm;
    Inertia_FIND  *InertiaIdent = &AxisB->MotorIdentify->InertiaIdent;
    MOTORPARAM_FIND*    MotorParamIdent = &AxisB->MotorIdentify->MotorParamIdent;
	REAL32			Jrat = CtrlPrm->Jrat;
        CHECK_ALARM             *CheckAlm = AxisB->CheckAlm;
    UINT16          FunctionSw = (CtrlPrm->FunctionSw); 
  

    if(!BaseLoops->Enc->V.PhaseReady)
    {
        BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_TRQ;
        return;
    }
    
	switch(BaseCtrls->CtrlModeSet.CtrlMode.b.cm )
	{
	case CTRL_MODE_PP:
		if((BaseCtrls->CtrlModeSet.ZctrlMode.zm != 0))
		{
			Prm402->PositionDemandValue0x6062 = Prm402->PositionActualValue0x6064;
			BaseCtrls->Cia402Axis.PP_Ctrl.FirstRun = 0; 		   
		}
                else if(BaseCtrls->CtrlCmdMngr.LastCtrlMode == BASE_MODE_SPD)
                {     
                    Prm402->PositionDemandValue0x6062 = Prm402->PositionActualValue0x6064 + BaseCtrls->PosManager.VirtualPositionError;

                    BaseCtrls->Cia402Axis.ChangControlMode = PVTPP_MODECHANGE;
		}
                else if(BaseCtrls->CtrlCmdMngr.LastCtrlMode == BASE_MODE_TRQ)
                {
                   Prm402->PositionDemandValue0x6062 = Prm402->PositionActualValue0x6064 + BaseCtrls->PosManager.VirtualPositionError;
                                  
                    BaseCtrls->Cia402Axis.ChangControlMode = PTTPP_MODECHANGE;
		}

		if(BaseCtrls->CmdEnable)
		{
			BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = Canfd_GetIpdPosTarget(); 
			BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads = Canfd_GetIpdSpdTarget();
			BaseCtrls->CtrlCmdMngr.IpdTrqFeedforwardNm = Canfd_GetIpdTrqFf();
		}
		else if((!BaseCtrls->BaseEnable) || (BaseCtrls->CtrlModeSet.ZctrlMode.zm != 0))
		{
			BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = BaseLoops->IpdPosFdbRad; 
			BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads = 0;
			BaseCtrls->CtrlCmdMngr.IpdTrqFeedforwardNm = 0;
			Canfd_SetIpdPosTarget(BaseLoops->IpdPosFdbRad);
			Canfd_SetIpdSpdTarget(0);
			Canfd_SetIpdTrqFf(0);
		}

		BaseCtrls->CtrlCmdMngr.IpdPosRadErr = BaseCtrls->CtrlCmdMngr.IpdPosTargetRad - BaseLoops->IpdPosFdbRad;
		BaseCtrls->CtrlCmdMngr.IpdSpdRadsErr = BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads - BaseLoops->IpdSpdFdbRads;

        
		Cia402_PP_Mode(&BaseCtrls->Cia402Axis, BaseCtrls->CmdEnable );
                                
                if(BaseLoops->UseEncoder2)
                {
                  Prm402->PositionDemandInternalvalue0x60FC = Prm402->PositionDemandValue0x6062 ;
                }
                else
                {
		Prm402->PositionDemandInternalvalue0x60FC = 
				Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, Prm402->PositionDemandValue0x6062 );
                }
	
		BaseCtrls->CtrlCmdMngr.PosRef = Prm402->PositionDemandInternalvalue0x60FC;
                BaseCtrls->CtrlCmdMngr.PosTragetErr =  Prm402->TargetPosition0x607A - Prm402->PositionDemandValue0x6062;
	
		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_POS;
                
	
		break;

	case CTRL_MODE_PV:
                if(BaseCtrls->CtrlCmdMngr.LastCtrlMode == BASE_MODE_POS)
                {
                    BaseCtrls->Cia402Axis.ChangControlMode = PPTPV_MODECHANGE; 
                }
                else if(BaseCtrls->CtrlCmdMngr.LastCtrlMode == BASE_MODE_TRQ)
                {
                    BaseCtrls->Cia402Axis.ChangControlMode = PTTPV_MODECHANGE;
		}
        
		Cia402_PV_Mode(&BaseCtrls->Cia402Axis, BaseCtrls->CmdEnable);
		
		Tmp32 = Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, Prm402->VelocityDemandValue0x606B );

		BaseCtrls->CtrlCmdMngr.SpdRef = (REAL32)Tmp32 * BaseLoops->Bprm->Kmotspd;
	
		BaseCtrls->CtrlCmdMngr.SpdRef = FlibLimitul( BaseCtrls->CtrlCmdMngr.SpdRef, NOR_MAXVAL_FLOAT, -NOR_MAXVAL_FLOAT );
                BaseCtrls->CtrlCmdMngr.SpdTragetErr  = Prm402->TargetVelocity0x60FF - Prm402->VelocityDemandValue0x606B;
	
		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_SPD;
	
		break;

	case CTRL_MODE_PT:
          
                if(BaseCtrls->CtrlCmdMngr.LastCtrlMode == BASE_MODE_POS)
                {
                    BaseCtrls->Cia402Axis.ChangControlMode = PPTPT_MODECHANGE;
		}
                else if(BaseCtrls->CtrlCmdMngr.LastCtrlMode == BASE_MODE_SPD)
                {
                    BaseCtrls->Cia402Axis.ChangControlMode = PVTPT_MODECHANGE;
		}
          
		Cia402_PT_Mode(&BaseCtrls->Cia402Axis, BaseCtrls->CmdEnable);
		
		BaseCtrls->CtrlCmdMngr.TrqRef = Prm402->TorqueDemand0x6074 * BaseLoops->Bprm->Kmottrq;
		BaseCtrls->CtrlCmdMngr.OvrTrq = 
				   OvrTrqSpdLimCal(&BaseCtrls->CtrlCmdMngr, &BaseCtrls->CtrlCmdPrm, BaseLoops->SpdFdb);
                BaseCtrls->CtrlCmdMngr.TrqTragetErr  = Prm402->TargetTorque0x6071 - Prm402->TorqueDemand0x6074;
	
		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_TRQ;

		break;

	case CTRL_MODE_CSP: 
                    
//		if((edsGetSyncSts() != PWM_DC_SYNC_DONE) || (BaseCtrls->DcSyncSig ==TRUE))
		{
			
			if(BaseCtrls->CmdEnable)
			{
				Prm402->PositionDemandValue0x6062 = Prm402->TargetPosition0x607A;

                                
			}
			else if((!BaseCtrls->BaseEnable) || (BaseCtrls->CtrlModeSet.ZctrlMode.zm != 0))
			{
                Prm402->TargetPosition0x607A = Prm402->PositionActualValue0x6064;
				Prm402->PositionDemandValue0x6062 = Prm402->PositionActualValue0x6064;
			}

			// for reflash position rad 
			if(BaseCtrls->CmdEnable)
			{
				BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = Canfd_GetIpdPosTarget(); 
				BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads = Canfd_GetIpdSpdTarget();
				BaseCtrls->CtrlCmdMngr.IpdTrqFeedforwardNm = Canfd_GetIpdTrqFf();
			}
			else if((!BaseCtrls->BaseEnable) || (BaseCtrls->CtrlModeSet.ZctrlMode.zm != 0))
			{
				BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = BaseLoops->IpdPosFdbRad; 
				BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads = 0;
				BaseCtrls->CtrlCmdMngr.IpdTrqFeedforwardNm = 0;
				Canfd_SetIpdPosTarget(BaseLoops->IpdPosFdbRad);
				Canfd_SetIpdSpdTarget(0);
				Canfd_SetIpdTrqFf(0);
			}

			BaseCtrls->CtrlCmdMngr.IpdPosRadErr = BaseCtrls->CtrlCmdMngr.IpdPosTargetRad - BaseLoops->IpdPosFdbRad;
			BaseCtrls->CtrlCmdMngr.IpdSpdRadsErr = BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads - BaseLoops->IpdSpdFdbRads;

			
			// Position limit
			if(Prm402->PositionDemandValue0x6062 < Prm402->MinSoftwarePositionLimit0x607D)
			{
				Prm402->PositionDemandValue0x6062 = Prm402->MinSoftwarePositionLimit0x607D;
				Prm402->Statusword0x6041 |= 0x2000;
				
			}
			else if(Prm402->PositionDemandValue0x6062 > Prm402->MaxSoftwarePositionLimit0x607D)
			{
				Prm402->PositionDemandValue0x6062 = Prm402->MaxSoftwarePositionLimit0x607D;
				Prm402->Statusword0x6041 |= 0x2000;
			}
			else
			{
			   Prm402->Statusword0x6041 &=~ 0x2000;
			}	
			Prm402->Statusword0x6041 |= 0x1000;
                        
                        if(BaseLoops->UseEncoder2)
                        {
                          Prm402->PositionDemandInternalvalue0x60FC = Prm402->PositionDemandValue0x6062 ;
                        }
                        else
                        {
			Prm402->PositionDemandInternalvalue0x60FC = 
					Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, Prm402->PositionDemandValue0x6062 );
                        }
                        
			BaseCtrls->CtrlCmdMngr.PosRef = Prm402->PositionDemandInternalvalue0x60FC;
                        
                        
			/* speed feed forward */
			Tmp32 = Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, Prm402->VelocityOffset0x60B1 );
			BaseCtrls->CtrlCmdMngr.NetSpdFFC = (REAL32)Tmp32 * BaseLoops->Bprm->Kmotspd;
			/* torque feed forward */
			BaseCtrls->CtrlCmdMngr.NetTrqFFC = Prm402->TorqueOffset0x60B2 * BaseLoops->Bprm->Kmottrq;
#if 1            
            if(FunctionSw & FUNC_DYNAMUC_PID)
            {   
                /* Kp,Kv,Ki */		
                BaseLoops->BaseCtrls->GainChange.GselGains[1].Kp = BaseLoops->Bprm->Kpx * (REAL32)(CtrlPrm->PosLoopHz)/10.0f;
                BaseLoops->BaseCtrls->GainChange.GselGains[1].Kv = BaseLoops->Bprm->Kvx * (( 100.0f + Jrat ) * (REAL32)(CtrlPrm->SpdLoopHz) ) / 1000.0f;

                if(CtrlPrm->SpdLoopTi == 0)
                {
                    BaseLoops->BaseCtrls->GainChange.GselGains[1].Kvi = 0;
                }
                else
                {
                    BaseLoops->BaseCtrls->GainChange.GselGains[1].Kvi = BaseLoops->GseGains->Kv * (REAL32)PS_CYCLEUS 
                                                /(10.0f * (REAL32)(CtrlPrm->SpdLoopTi));
                }
             }   
#endif           
			BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_POS;
		}
                
                BaseCtrls->CtrlCmdMngr.PosTragetErr =  Prm402->TargetPosition0x607A - Prm402->PositionDemandValue0x6062;
		
		break;

	case CTRL_MODE_CSV: 
//		if((edsGetSyncSts() != PWM_DC_SYNC_DONE) || (edsGetDcSyncFlag()==TRUE))
		{
			
			if(BaseCtrls->CmdEnable)
			{
				Prm402->VelocityDemandValue0x606B = Prm402->TargetVelocity0x60FF;
			}
			else if(!BaseCtrls->BaseEnable)
			{
				Prm402->VelocityDemandValue0x606B = 0;
			}


			
			 // Position limit warning
			if(Prm402->PositionActualValue0x6064 < Prm402->MinSoftwarePositionLimit0x607D)
			{
				Prm402->Statusword0x6041 |= 0x2000;
				
			}
			else if(Prm402->PositionActualValue0x6064 > Prm402->MaxSoftwarePositionLimit0x607D)
			{
				Prm402->Statusword0x6041 |= 0x2000;
			}
			else
			{
			   Prm402->Statusword0x6041 &=~ 0x2000;
			}	
			Prm402->Statusword0x6041 |= 0x1000;
			
			Tmp32 = Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, Prm402->VelocityDemandValue0x606B );
			BaseCtrls->CtrlCmdMngr.SpdRef = (REAL32)Tmp32 * BaseLoops->Bprm->Kmotspd;
			
			BaseCtrls->CtrlCmdMngr.SpdRef = FlibLimitul( BaseCtrls->CtrlCmdMngr.SpdRef, NOR_MAXVAL_FLOAT, -NOR_MAXVAL_FLOAT );
			/* torque feed forward */
			BaseCtrls->CtrlCmdMngr.NetTrqFFC = Prm402->TorqueOffset0x60B2 * BaseLoops->Bprm->Kmottrq;
#if 1  
            if(FunctionSw & FUNC_DYNAMUC_PID)
            { 
                /* Kv,Ki */
                BaseLoops->BaseCtrls->GainChange.GselGains[1].Kv = BaseLoops->Bprm->Kvx * (( 100.0f + Jrat ) * (REAL32)(CtrlPrm->SpdLoopHz) ) / 1000.0f;
                if(CtrlPrm->SpdLoopTi == 0)
                {
                    BaseLoops->BaseCtrls->GainChange.GselGains[1].Kvi = 0;
                }
                else
                {
                    BaseLoops->BaseCtrls->GainChange.GselGains[1].Kvi = BaseLoops->GseGains->Kv * (REAL32)PS_CYCLEUS 
                                              /(10.0f * (REAL32)(CtrlPrm->SpdLoopTi));
                }
            }   
#endif   
			BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_SPD;
		}
		BaseCtrls->CtrlCmdMngr.SpdTragetErr  = Prm402->TargetVelocity0x60FF - Prm402->VelocityDemandValue0x606B;
		break;
		
	case CTRL_MODE_CST:
//		if((edsGetSyncSts() != PWM_DC_SYNC_DONE) || (edsGetDcSyncFlag()==TRUE))
		{
			
			if(BaseCtrls->CmdEnable)
			{
				Prm402->TorqueDemand0x6074 = Prm402->TargetTorque0x6071;
			}
			else if(!BaseCtrls->BaseEnable)
			{
				Prm402->TorqueDemand0x6074 = 0;
			}
			
			// Position limit warning
			if(Prm402->PositionActualValue0x6064 < Prm402->MinSoftwarePositionLimit0x607D)
			{
				Prm402->Statusword0x6041 |= 0x2000;
				
			}
			else if(Prm402->PositionActualValue0x6064 > Prm402->MaxSoftwarePositionLimit0x607D)
			{
				Prm402->Statusword0x6041 |= 0x2000;
			}
			else
			{
			   Prm402->Statusword0x6041 &=~ 0x2000;
			}	
			
			Prm402->Statusword0x6041 |= 0x1000;
			
			BaseCtrls->CtrlCmdMngr.TrqRef = Prm402->TorqueDemand0x6074 * BaseLoops->Bprm->Kmottrq;

			BaseCtrls->CtrlCmdMngr.OvrTrq = 
					  OvrTrqSpdLimCal(&BaseCtrls->CtrlCmdMngr, &BaseCtrls->CtrlCmdPrm, BaseLoops->SpdFdb);
		
			BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_TRQ;
		}
		
		BaseCtrls->CtrlCmdMngr.TrqTragetErr  = Prm402->TargetTorque0x6071 - Prm402->TorqueDemand0x6074;
		break;
	
	case CTRL_MODE_HOME:
                if((BaseCtrls->CtrlCmdMngr.LastCtrlMode != BASE_MODE_POS) || (BaseCtrls->CtrlModeSet.ZctrlMode.zm != 0))
		{
			Prm402->PositionDemandValue0x6062 = Prm402->PositionActualValue0x6064;
			BaseCtrls->Cia402Axis.HOME_Ctrl.FirstRun = 0; 		   
		}
		Cia402_HOME_Mode(&BaseCtrls->Cia402Axis, AxisB, BaseCtrls->CmdEnable);
	
                if(BaseLoops->UseEncoder2)
                {
                  Prm402->PositionDemandInternalvalue0x60FC = Prm402->PositionDemandValue0x6062 ;
                }
                else
                {
		Prm402->PositionDemandInternalvalue0x60FC =
                    Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, Prm402->PositionDemandValue0x6062 );
                }
		BaseCtrls->CtrlCmdMngr.PosRef  = Prm402->PositionDemandInternalvalue0x60FC;
		
		if(BaseCtrls->Cia402Axis.HOME_Ctrl.PosChangFlag)
		{
			BaseCtrls->CtrlCmdMngr.PosRef_l = BaseCtrls->CtrlCmdMngr.PosRef;
		}

		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_POS;
	
		break;
	
	case CTRL_MODE_JOG:
#if 0
		JOGExec(&BaseCtrls->Cia402Axis, BaseCtrls->BaseEnable);
		Prm402->VelocityDemandValue0x606B = BaseCtrls->Cia402Axis.JOG_Ctrl.V.OutputVel;
		Tmp32 = Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, Prm402->VelocityDemandValue0x606B );
		BaseCtrls->CtrlCmdMngr.SpdRef = (REAL32)Tmp32 * BaseLoops->Bprm->Kmotspd;
		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_SPD;	
#endif
        
                BpiJogExec(AxisB);
        
		/* Return the tune-less function switch to its original position */
		BaseCtrls->TuneLessCtrl.var.TuneLessAct = BaseCtrls->TuneLessCtrl.conf.TuningLessUse;

		if(BaseCtrls->CmdEnable)
		{
			/* Soft start processing */
			BaseCtrls->CtrlCmdMngr.SpdRef = BaseSoftStartSpdRef( &(BaseCtrls->CtrlCmdPrm),
																AxisB->FnCmnCtrl->JogSpeed,
																BaseCtrls->CtrlCmdMngr.SpdRef );
		}
		else if(!BaseCtrls->BaseEnable)
		{
			BaseCtrls->CtrlCmdMngr.SpdRef = 0;
		}
		
		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_SPD;	
		
		break;
		
	 case CTRL_MODE_PJOG:
		BaseCtrls->TuneLessCtrl.var.TuneLessAct = BaseCtrls->TuneLessCtrl.conf.TuningLessUse;
		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_POS;
		break;
		
	 case CTRL_MODE_MOTORIdent:
		AxisB->Prm->PnAdvPrm.MotPrmEstSts =  BpiFindMotorParameter(AxisB);
		BaseLoops->VdRefVF = MotorParamIdent->V.FPVdRef;
		BaseLoops->VqRefVF = MotorParamIdent->V.FPVqRef;
		BaseLoops->ElecAngleVF = MotorParamIdent->V.FPElecAngleRef;
		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_VF;
//		MotorParaComplete(MotorParamIdent, AxisB->Prm);
		break;   
	case CTRL_MODE_FUN_SPD:
		FuncGeneratorExec(&BaseCtrls->Cia402Axis, BaseCtrls->CmdEnable);
		Prm402->VelocityDemandValue0x606B = BaseCtrls->Cia402Axis.FG_Ctrl.V.OutputRef;

		Tmp32 = Cia402_GearRatioCal(&BaseCtrls->Cia402Axis, Prm402->VelocityDemandValue0x606B );
		BaseCtrls->CtrlCmdMngr.SpdRef = (REAL32)Tmp32 * BaseLoops->Bprm->Kmotspd;

		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_SPD;	
	
		break;
	
	case CTRL_MODE_FUN_TRQ:
		FuncGeneratorExec(&BaseCtrls->Cia402Axis, BaseCtrls->CmdEnable);
//		Prm402->TorqueDemand0x6074 = BaseCtrls->Cia402Axis.FuncGen_Ctrl.OutputRef;
		if(BaseLoops->FunTqrCurType)
		{
			BaseCtrls->CtrlCmdMngr.WeakRef = 0.0f;
			BaseCtrls->CtrlCmdMngr.TrqRef = BaseCtrls->Cia402Axis.FG_Ctrl.V.OutputRef * BaseLoops->Bprm->Kmottrq;
			BaseCtrls->CtrlCmdMngr.OvrTrq = 
				OvrTrqSpdLimCal(&BaseCtrls->CtrlCmdMngr, &BaseCtrls->CtrlCmdPrm, BaseLoops->SpdFdb);
		}
		else
		{
			BaseCtrls->CtrlCmdMngr.WeakRef = BaseCtrls->Cia402Axis.FG_Ctrl.V.OutputRef * BaseLoops->Bprm->Kmottrq;  
			BaseCtrls->CtrlCmdMngr.TrqRef =  0.0;      
		}
                
                if(BaseCtrls->CmdEnable)
                {
                  BaseLoops->CurrentTest = 1;
                }
                else
                {
                  BaseLoops->CurrentTest = 1;
                }
	
		BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_TRQ;	
	
		break;
	case MOTOR_MODE_IPD:
		{
			if(BaseCtrls->CmdEnable)
			{
				if(AxisB->Prm->PnCtrlCfgPrm.CtrlSource)
				{
					BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = Canfd_GetIpdPosTarget(); 
					BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads = Canfd_GetIpdSpdTarget();
					BaseCtrls->CtrlCmdMngr.IpdTrqFeedforwardNm = Canfd_GetIpdTrqFf();
				}
				else
				{
					BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = (REAL32)Prm402->TargetPosition0x607A/(REAL32)BaseLoops->Bprm->FbPulse1*C2_PI; 
					BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads = (REAL32)Prm402->TargetVelocity0x60FF/(REAL32)BaseLoops->Bprm->FbPulse1*C2_PI;
					BaseCtrls->CtrlCmdMngr.IpdTrqFeedforwardNm = Prm402->TargetTorque0x6071;
				}
			}
			else if((!BaseCtrls->BaseEnable) || (BaseCtrls->CtrlModeSet.ZctrlMode.zm != 0))
			{
				BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = BaseLoops->IpdPosFdbRad; 
				BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads = 0;
				BaseCtrls->CtrlCmdMngr.IpdTrqFeedforwardNm = 0;
				Canfd_SetIpdPosTarget(BaseLoops->IpdPosFdbRad);
				Canfd_SetIpdSpdTarget(0);
				Canfd_SetIpdTrqFf(0);
			}
		

			BOOL RefDir = 0;

			if(BaseLoops->IpdSpdFdbRads < 0)
			{
				RefDir = 1;
			}
			else if(BaseLoops->IpdSpdFdbRads > 0)
			{
				RefDir = 0;
			}

			if((AxisB->Prm->PnCustomPrm.HjLimitEnble & 0x000F) && BaseCtrls->CmdEnable)
			{
                 AdetCheckIPDPosLimit(AxisB->AlmMngr, &AxisB->CheckAlm->OverLimit,BaseLoops->IpdPosFdbRad,RefDir);
			}

			if((AxisB->Prm->PnCustomPrm.HjLimitEnble & 0x0F00) && ((AxisB->Prm->PnCustomPrm.HjLimitEnble & 0x000F)))
			{	
				if(BaseLoops->IpdPosFdbRad > BaseLoops->IpdPosSfPLimit)
				{
					BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = BaseLoops->IpdPosSfPLimit;
				}
				else if(BaseLoops->IpdPosFdbRad < -BaseLoops->IpdPosSfPLimit)
				{
					BaseCtrls->CtrlCmdMngr.IpdPosTargetRad = -BaseLoops->IpdPosSfPLimit;
				}
			}
			
			BaseCtrls->CtrlCmdMngr.IpdPosTargetRadLast = BaseCtrls->CtrlCmdMngr.IpdPosTargetRad;
			BaseCtrls->CtrlCmdMngr.IpdSpdTargetRadsLast = BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads;

			BaseCtrls->CtrlCmdMngr.IpdPosRadErr = BaseCtrls->CtrlCmdMngr.IpdPosTargetRad - BaseLoops->IpdPosFdbRad;
			BaseCtrls->CtrlCmdMngr.IpdSpdRadsErr = BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads - BaseLoops->IpdSpdFdbRads;

			BaseLoops->IpdSpringTrqNm = BaseCtrls->CtrlCmdPrm.Kposstiff * BaseCtrls->CtrlCmdMngr.IpdPosRadErr;
			BaseLoops->IpdDampTrqNm   = BaseCtrls->CtrlCmdPrm.Kspdstiff * BaseCtrls->CtrlCmdMngr.IpdSpdRadsErr;
			BaseLoops->IpdTrqFeedforwardNm = BaseCtrls->CtrlCmdMngr.IpdTrqFeedforwardNm;

			REAL32 TrqRef = BaseLoops->IpdSpringTrqNm + BaseLoops->IpdDampTrqNm + BaseLoops->IpdTrqFeedforwardNm;
			REAL32 TrqRefLimit = TrqRef;

			REAL32 TrqRefDymLimit = BpiIpdTorqueLimit(BaseLoops->BaseCtrls, BaseLoops, TrqRefLimit);	

			if((AxisB->Prm->PnCustomPrm.HjLimitEnble & 0x0F00) == 0 && (AxisB->Prm->PnCustomPrm.HjLimitEnble & 0x000F))
			{
				TrqRefLimit = TrqRefDymLimit;
			}

                        
			BaseLoops->IpdTrqRef = TrqRefLimit;
                        
			REAL32 MotTrqRef = GearRatioInvCal(BaseLoops, TrqRefLimit);	

			BaseCtrls->CtrlCmdMngr.TrqRef = (MotTrqRef *1000.0f)/ BaseLoops->Bprm->Ktrqnm;

			BaseCtrls->CtrlCmdMngr.OvrTrq = 
					OvrTrqSpdLimCal(&BaseCtrls->CtrlCmdMngr, &BaseCtrls->CtrlCmdPrm, BaseLoops->SpdFdb);				
			BaseCtrls->CtrlCmdMngr.CtrlMode = BASE_MODE_TRQ;
			
		}		
		break;

	default:
		break;	
	}
	

	
    if(BaseCtrls->CtrlCmdMngr.CtrlMode == BASE_MODE_TRQ)
    {
      CheckAlm->OvrRun.OvrTrqInTrqMode = BaseCtrls->CtrlCmdMngr.TrqRef;
    }

    Prm402->ModesOfOperationDisplay0x6061 = Prm402->ModesOfOperation0x6060;


}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 OvrTrqSpdLimCal( CTRL_CMD_MNG *CtrlCmdMng, CTRL_CMD_PRM *CtrlCmdPrm, INT32 SpdFbk)
{
	INT32	nerr,nerpi;
	REAL32	nerp, neri;
	REAL32	overtorque;

	INT32	TrqRefi = CtrlCmdMng->TrqRef;
	INT32   SpdLim = (INT32)CtrlCmdPrm->OvrTrqSpdLim;

	if ( SpdFbk > 0 )
	{
	    nerr = SpdFbk - SpdLim;
	}
	else if ( SpdFbk < 0 )
	{
	    nerr = SpdFbk + SpdLim;
	}
	else
	{
	    nerr = ( TrqRefi >= 0 ) ? -SpdLim : SpdLim;
	}

	nerp = (REAL32)nerr * CtrlCmdPrm->OverTrqLoopGain[0];
	
	neri = FlibIntegral((REAL32)nerr, CtrlCmdPrm->OverTrqIntegGain[0], &CtrlCmdPrm->OverTrqIntegBuf[0] );

	nerpi = (INT32)(nerp + neri);

	/*Processing when the integrated value is different from the actual motor rotation direction */
	if ( (((SpdFbk ^ nerpi) < 0) && (TrqRefi >= 0)) || ((((SpdFbk - 1) ^ nerpi) < 0) && (TrqRefi < 0)) )
	{
		CtrlCmdPrm->OverTrqIntegBuf[0] = 0;
		CtrlCmdPrm->OverTrqIntegBuf[1] = 0;
		nerpi = 0;
	}

	overtorque = FlibLimitul( (REAL32)nerpi, (REAL32)0x01000000, (REAL32)-0x01000000 );

	return overtorque;
}


/****************************************************************************************************
 * DESCRIPTION:  Torque limit calculation processing
 *
 *																									
 *		        EstopTrqLmt -----||-------------------------------+-------> FwTrqLmt			
 *							    OT-TrqLmt   			 		  | 								
 *													   			  | 								
 *												+-------+   	  | 								
 *              FwIntTrqLmt ------------------->|     	|   	  | 								
 *												|   	|   	  | 							
 *												|  Min	+---|/|---+    	  								
 *		        FwExtTrqLmt -----||------------>|   	|  OT-TrqLmt		 								
 *							    P-CL			|    	|							  							
 *							                 	+-------+   		
 *	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BpiTorqueLimitControl( BASE_CTRL *pBaseCtrl,BOOL OtTrqLmtReq, ALARM *AlmMngr,BASE_LOOP  *BaseLoops) 
{
	INT32			FwTrqLmt;
	INT32			RvTrqLmt;
	INT32			CurLimitTrq = BaseLoops->Bprm->Kmottrq * 1000.0f;

    TRQ_LMT_DATA *TrqLimits = &pBaseCtrl->TrqLimitData;

	if( OtTrqLmtReq )
	{
		FwTrqLmt = TrqLimits->P.EstopTrqLmt;
		RvTrqLmt = TrqLimits->P.EstopTrqLmt;

	}
	else
	{
		if( TrqLimits->V.PclSignal )
		{
			FwTrqLmt = ( TrqLimits->P.FwIntTrqLmt < TrqLimits->P.FwExtTrqLmt ) ?
						 TrqLimits->P.FwIntTrqLmt:
						 TrqLimits->P.FwExtTrqLmt;
		}
		else
		{
			FwTrqLmt = TrqLimits->P.FwIntTrqLmt;
		}

		if( TrqLimits->V.NclSignal )
		{
			RvTrqLmt = ( TrqLimits->P.RvIntTrqLmt < TrqLimits->P.RvExtTrqLmt ) ?
						 TrqLimits->P.RvIntTrqLmt:
						 TrqLimits->P.RvExtTrqLmt;
		}
		else
		{
			RvTrqLmt = TrqLimits->P.RvIntTrqLmt;
		}
		
	}

	if(BaseLoops->FuncCurLimit && (AlmMngr->CurrntLimitFlag == 1))			
	{
		if(CurLimitTrq < FwTrqLmt)
		{
			FwTrqLmt = CurLimitTrq;
		}

		if(CurLimitTrq < RvTrqLmt)
		{
			RvTrqLmt = CurLimitTrq;
		}
	}


	TrqLimits->V.PosTrqLmtOut = FwTrqLmt;				
	TrqLimits->V.NegTrqLmtOut = -RvTrqLmt;				

}



/****************************************************************************************************
 * DESCRIPTION:  Torque limit calculation processing
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 BpiIpdTorqueLimit( BASE_CTRL *pBaseCtrl, BASE_LOOP  *BaseLoops, REAL32 TrqRef) 
{

	REAL32  pos = BaseLoops->IpdPosFdbRad;
	REAL32  vel = BaseLoops->IpdSpdFdbRads;
	REAL32  soft_min_vel = 0.0f;
    REAL32  soft_max_vel = 0.0f;
	REAL32  soft_min_tqr = 0.0f;
	REAL32  soft_max_tqr = 0.0f;

    TRQ_LMT_DATA *TrqLimits = &pBaseCtrl->TrqLimitData;

	soft_min_vel = -BaseLoops->IpdSfLimitKp*(pos - BaseLoops->IpdPosSfNLimit);
	soft_max_vel = -BaseLoops->IpdSfLimitKp*(pos - BaseLoops->IpdPosSfPLimit);

	soft_min_vel = FlibLimitul(soft_min_vel, BaseLoops->IpdSpdSfLimit, -BaseLoops->IpdSpdSfLimit);

	soft_max_vel = FlibLimitul(soft_max_vel, BaseLoops->IpdSpdSfLimit, -BaseLoops->IpdSpdSfLimit);

	soft_min_tqr = -BaseLoops->IpdSfLimitKv*(vel - soft_min_vel);

	soft_max_tqr = -BaseLoops->IpdSfLimitKv*(vel - soft_max_vel);

	soft_min_tqr = FlibLimitul(soft_min_tqr, BaseLoops->IpdTrqSfLimit, -BaseLoops->IpdTrqSfLimit);

	soft_max_tqr = FlibLimitul(soft_max_tqr, BaseLoops->IpdTrqSfLimit, -BaseLoops->IpdTrqSfLimit);

	TrqLimits->V.IpdNegTrqLmtOut = soft_min_tqr;
	TrqLimits->V.IpdPosTrqLmtOut = soft_max_tqr;

	if(TrqRef < soft_min_tqr)
	{
		return soft_min_tqr;
	}
	else if(TrqRef > soft_max_tqr)
	{
		return soft_max_tqr;
	}
	else
	{
		return TrqRef;
	}
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Speed command soft start calculation
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 BaseSoftStartSpdRef( CTRL_CMD_PRM *CtrlCmdPrm, INT32 SpdRef, INT32 SpdRefx )
{
	INT32	LastSpdRefx;

	LastSpdRefx = SpdRefx;


	if( SpdRef > SpdRefx )
	{
		if( SpdRefx >= 0 )
		{
			SpdRefx = SpdRefx + CtrlCmdPrm->SpdSfsAcc;
			if( SpdRefx > SpdRef )
			{
				SpdRefx = SpdRef;
			}
		}
		else
		{
			SpdRefx = SpdRefx + CtrlCmdPrm->SpdSfsDec;
			if( SpdRefx > SpdRef )
			{
				SpdRefx = SpdRef;
			}
		}
	}

	else if( SpdRef < SpdRefx )
	{
		if( SpdRefx <= 0 )
		{
			SpdRefx = SpdRefx - CtrlCmdPrm->SpdSfsAcc;
			if( SpdRefx < SpdRef )
			{
				SpdRefx = SpdRef;
			}
		}
		else
		{
			SpdRefx = SpdRefx - CtrlCmdPrm->SpdSfsDec;
			if( SpdRefx < SpdRef )
			{
				SpdRefx = SpdRef;
			}
		}
	}

	if(CtrlCmdPrm->SpdSfsAcc != CtrlCmdPrm->SpdSfsDec)
	{
		if( (LastSpdRefx != 0) && ((LastSpdRefx ^ SpdRefx) < 0) )
		{
			SpdRefx = 0;
		}
	}
	return( SpdRefx );
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Kernel Input Service for TaskB
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BpiInputMotorPosition( BASE_LOOP *BaseLoops,
                                                         PRMDATA *Prm, CTRL_LOOP_OUT *CtrlLoopOut)
{
	INT32				dMotPos, MotPos;
	INT32				dFencPos, FencPos;	
	POS_MNG_HNDL		*PosManager;
	BASE_CTRL 			*BaseControls;
	BPRMDAT 		    *Bprm; 

	BaseControls = BaseLoops->BaseCtrls;
	Bprm = BaseLoops->Bprm;

	PosManager		= &(BaseControls->PosManager);

/*--------------------------------------------------------------------------------------------------*/
/*		Calculate Motor Encoder Data for Servo Control												*/
/*--------------------------------------------------------------------------------------------------*/
//	if( Prm->PnMotPrm.MotType == MOTTYPE_LINEAR )
//	{
//		BaseControls->MotSts.MotCphPass = LmxSencScanServiceB( MencV,
//							 	 	 	 	 	 	 	 	&(BaseControls->CLatchClrReq) );
//	}
//	else
//	{ 
//		BaseControls->MotSts.MotCphPass = RmxSencScanServiceB( MencV,
//															&(BaseControls->CLatchClrReq) );
//	}
//	BaseControls->MotSts.MposFromCpos = Bprm->DirSign * MencV->MposFromCpos;

/*--------------------------------------------------------------------------------------------------*/
/*		FB position update																			*/
/*--------------------------------------------------------------------------------------------------*/
	MotPos = Bprm->DirSign * BaseLoops->Enc->V.MotPos;		/* Load MotorPosition					*/
	dMotPos = MotPos - BaseControls->MotSts.MotPos;			/* Cal. Delta Position					*/
	BaseControls->MotSts.MotPos = MotPos;					/* Update Motor Position				*/
	BaseControls->MotSts.dMotPos = dMotPos;					/* Update Delta Motor Position			*/

/*--------------------------------------------------------------------------------------------------*/
/*		FB speed update (for sequence processing)												    */
/*--------------------------------------------------------------------------------------------------*/
	BaseControls->MotSts.MotSpdForSeq = ( REAL32)dMotPos * Bprm->KmotspdB;

//	if( MencV->ReCalcAbsPosReq != FALSE )		
//	{
//		PosMngSenOnReCalcAbsoPos( MencV,
//		                          &(PosManager->MencPos),
//		                          &(PosManager->conf.Egear),
//		                          Prm->MencP.bit_dp,
//		                          Prm->MencP.limmlt,
//								  Bprm->RvsDir);
//		MencV->ReCalcAbsPosReq = FALSE;					
//	}

	BaseControls->CtrlCmdMngr.dPosFbki = dMotPos;
	BaseControls->CtrlCmdMngr.PosFbki = BaseControls->MotSts.MotPos;
	BaseControls->CtrlCmdMngr.FbkPosFromCpos = BaseControls->MotSts.MposFromCpos;

	/* Motor speed FB calculation */
	BaseControls->CtrlCmdMngr.SpdFbki = CtrlLoopOut->MotSpd_a;
	BaseControls->CtrlCmdMngr.SpdObsFbki = CtrlLoopOut->MotSpd_a;
}

/****************************************************************************************************
* DESCRIPTION:
* 	 Output monitor value
* RETURNS:
*
****************************************************************************************************/

 void BpiOutputNetStatus(BASE_CTRL *BaseControls, CTRL_LOOP_OUT *CtrlLoopOut, 
                                                          SEQ_CTRL_OUT *SeqCtrlOut, BPRMDAT *Bprm)
 {
	 INT32				 MotSpd;
	 REAL32				 VPosErrKp;
	 USHORT 			 RspCtrlBits;
	 POS_MNG_HNDL		 *PosManager;
 
	 PosManager 	 = &(BaseControls->PosManager);

	 MotSpd = (BaseControls->MotSts.MotSpd + BaseControls->CtrlCmdMngr.SpdFbki) / 2.0f;


	 BaseControls->MotSts.MotSpd = BaseControls->CtrlCmdMngr.SpdFbki;
	 

	 BaseControls->MotSts.MotSpdSumB += BaseControls->MotSts.MotSpdForSeq;	 
	 BaseControls->MotSts.MotSpdCntB++;
 

	 if(BaseControls->CtrlModeSet.CtrlMode.b.cm != BASE_MODE_POS)
	 { 
		 VPosErrKp = 1 ^ (SeqCtrlOut->OverTrvlSts & SeqCtrlOut->MotStop);
		 VPosErrKp = VPosErrKp/(BaseControls->GainChange.GselGains[0].Kp);
                 
 
		 PosManager->VirtualPositionError = MotSpd * VPosErrKp;

	 }
         
 
 }


