/****************************************************************************************************
 *
 * FILE NAME:  Bprm.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _BASE_PRM_H_
#define _BASE_PRM_H_
	
#include "BaseDef.h"


/*--------------------------------------------------------------------------------------------------*/
/*		Base Parameter Struct						        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
    BOOL	LinearMotType;	   //linear motor type
    BOOL    RvsDir;            //reverse rotation flag
    BOOL    UWVSeq;            //Motor line UWVSeq sequence
    INT32   DirSign;           //1:positive rotation, -1:reverse rotation
    UINT16  EncBitNum;         //Encoder single turn bit number   [bit]
    UINT16  MotPolePairNum;    //motor pole paire number
    REAL32  RatSpd;            //rated speed                      [rad/s]
    REAL32  MaxSpd;            //Max speed                        [rad/s]
    REAL32	OvrSpd;			   // OverSpeed						  [rad/s]	[m/s]
    REAL32  RatTrq;            //rated torque                     [Nm]
    REAL32  MaxTrq;            //Max torque                       [Nm]
    REAL32  RatCur;            //rated current                    [Apk]
    REAL32  MaxCur;            //Max current                      [Apk]
    REAL32  Vdc;               //DC voltage                       [V] 
    REAL32  MotW;              //motor power                      [W]
    REAL32  MotR;              //motor resistance                 [ohm]
    REAL32  MotLd;             //motor d-axis inductance          [H]
    REAL32  MotLq;             //motor q-axis inductance          [H]
    REAL32  MotEmf;            //motor EMF constant				  [Vrms/r/s]
    REAL32  Jmot;              //motor rotor inertia			  [kg*m^2]
	INT32	FbPulse;		   //Feedback PulseNo (Menc/Fenc) 	  [p/rev] 	[p/360deg]	
	INT32	FbPulse1;		   //Enc2 Feedback PulseNo (Menc/Fenc) 	  [p/rev] 	[p/360deg]		
//	REAL32	ImRs;
//	REAL32	ImRr;
//	REAL32	ImL0;
//	REAL32	ImLm;

	REAL32  RatFreq;           //rated frequency                  [Hz]

	REAL32  Keangle;           // Electric Angle Trans Factor		[Erad/Mrad]	[Erad/m]
	REAL32  Kencoder;          // encoder unit to position unit   [pulse]-->[cnt]    
    
    
	REAL32	OvrPspd;		   // Pulse Speed at OvrSpd			  [p/sec]     
	REAL32	MaxPspd;		   // Pulse Speed at MaxSpd			  [p/sec]     
	
	REAL32  Kpx;               // position control gain factor
	REAL32  Kvx;               // speed control gain factor
	REAL32  Kpvx;              // Position-Speed Control Gain for MFC & Predictive Control

	REAL32	SpdFbLpassFil;	   // Motor noise silent speed FBLPF setting[0.01ms]
	REAL32  SpdDetectUnit;     // Speed detection resolution [0.1r/min/pulse],[0.1mm/s/pulse]

	UINT16	PerOvrSpd;		   // Over  Speed Percent			  [0.01%] 
	INT32	NorOvrSpd;		   // Over  Speed Normalized		  [2^24]
	INT32	NorMaxSpd;		   // Max.  Speed Normalized		  [2^24/OS]
	INT32	NorRatSpd;		   // Rated Speed Normalized		  [2^24/OS]
	INT32	NorSuspSpd; 	   // Suspend Speed Normalized 		  [2^24/OS]	

	INT32	KMotSpdConv;	   // Speed conversion factor		  [0.1r/min,0.1mm/s->p/ms]
	REAL32  Kmotpls;           // motor encoder pulse factor      [pulse] --> [rad]     [pulse] --> [m]   
	REAL32  Kmotpls1;          // motor encoder2 pulse factor      [pulse] --> [rad]     [pulse] --> [m]   	
	REAL32  KmotspdF;          // Motor Speed Cal. Gain           [pulse/spd_loop cycle]->[2^24/OvrSpd]
	REAL32	Kmotspd;		   // Motor Speed Cal. Gain           [pulse/sec] --> [2^24/OvrSpd]
	REAL32	KmotVel;		   // Motor Speed Cal. Gain           [rad/sec] --> [2^24/OvrSpd]
	REAL32	Kmottrq;		   // Motor Torque Cal. Factor	      [0.1% --> 2^24/MaxTrq]
    REAL32  Ktrqnm;            // Motor Torque Cal. Factor       [2^24/MaxTrq --> 0.001nm]   
	REAL32  Ktrqper;           // Motor Torque Cal. Factor       [Nm --> 0.1%]   
	REAL32  Kspdrpm;           // Motor Speed Cal. Gain           [rpm] --> [2^24/OvrSpd]

	REAL32	KmotspdA;		   // TaskA Motor Speed Cal. Gain	
	REAL32	KmotspdB;		   // TaskB Motor Speed Cal. Gain					
	REAL32	KmotspdC;		   // TaskC Motor Speed Cal. Gain		

	UINT16  PerMaxTrq;         // Max. Torque Percent			  [%]	

	UINT8	AmpDcinType;	   // Amp DC Input Type               (AC:0, DC:1)
    
    REAL32  MaxSmpCur;         //Max Sample Current 
    UINT32  MotorRev;
    UINT32  LoadShaftRev;  
    
    UINT16  Enc2BitNum;         //Encoder single turn bit number   [bit]
}BPRMDAT;

/*--------------------------------------------------------------------------------------------------*/
/*		Base Parameter initial config						        							    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	UINT32	PnGear_b;		     // gear ratio numerator
	UINT32	PnGear_a;		     // gear ratio denominator
	UINT16	PnMaxSpd;	         // motor maximum speed            [rpm]
	UINT16  PnMaxCurS;           // device maximum current         [A]

} BPRM_INICFG;


#endif

