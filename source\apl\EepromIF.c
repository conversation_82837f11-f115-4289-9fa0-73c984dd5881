/****************************************************************************************************
 *
 * FILE NAME:  EepromIF.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.13
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	13-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "EepromIF.h"
#include "bsp_eeprom.h"
#include "HardApi.h"
#include "bsp_timer.h"


#define I2C1_SLAVE_ADDRESS               0x50
#define I2C_MEMADD_SIZE_8BIT            (0x00000001U)
#define I2C_MEMADD_SIZE_16BIT           (0x00000002U)
#define I2C_PageSize 32

/****************************************************************************************************
 * DESCRIPTION:
 *			  Wait for EEPROM Standby state
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void eepI2C_WaitEepromStandbyState(void) 
{
    uint32_t DelayCnt = (uint32_t)(5*1000*CMTW1_PPR_US);  
    
    uint32_t StartCnt =  hApi_GetTimerCounter();
    
    while(hApi_GetTimerCounter() - StartCnt < DelayCnt); 
}

/****************************************************************************************************
 * DESCRIPTION:
 *			  Writes more than one byte to the EEPROM with a single WRITE cycle. 
  			  The number of byte cant exceed the EEPROM page size.
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 eepI2C_PageWrite(UINT8* pBuffer, UINT16 WriteAddr, UINT16 NumByteToWrite)
{
	INT32 rslt;
    
	rslt = I2C_Mem_Write(I2C1_SLAVE_ADDRESS,WriteAddr,I2C_MEMADD_SIZE_16BIT,
		                                                                pBuffer,NumByteToWrite,100);
//	while (RIIC1.ICCR2.BIT.BBSY != 0);
    
	return rslt;
}


/****************************************************************************************************
 * DESCRIPTION:
 *            Reads a block of data from the EEPROM
 * RETURNS:
 *
****************************************************************************************************/
 INT32 eepI2C_BufferRead(UINT16 ReadAddr, UINT8* pBuffer, UINT16 NumByteToRead)
{
    INT32 rslt = 0;
    rslt = I2C_Mem_Read(I2C1_SLAVE_ADDRESS, ReadAddr, I2C_MEMADD_SIZE_16BIT,
										 pBuffer, NumByteToRead,100);
//    while (RIIC1.ICCR2.BIT.BBSY != 0);
    
    return rslt;
}

/****************************************************************************************************
 * DESCRIPTION:
 *            Writes buffer of data to the I2C EEPROM
 * RETURNS:
 *
****************************************************************************************************/
 void eepI2C_BufferWrite( UINT16 WriteAddr, UINT8* pBuffer, UINT16 NumByteToWrite)
{
#if 1
    UINT16 NumOfPage = 0, NumOfSingle = 0, Addr = 0, count = 0;
    
    Addr = WriteAddr % I2C_PageSize;
    count = I2C_PageSize - Addr;
    NumOfPage = NumByteToWrite / I2C_PageSize;
    NumOfSingle = NumByteToWrite % I2C_PageSize;
    eepI2C_WaitEepromStandbyState();
    /* If WriteAddr is I2C_PageSize aligned */
    if(Addr == 0) 
    {
        /* If NumByteToWrite < I2C_PageSize */
        if(NumOfPage == 0) 
        {
            eepI2C_PageWrite(pBuffer, WriteAddr, NumOfSingle);
            eepI2C_WaitEepromStandbyState();
        }
        /* If NumByteToWrite > I2C_PageSize */
        else 
        {
            while(NumOfPage--)
            {
                eepI2C_PageWrite(pBuffer, WriteAddr, I2C_PageSize); 
                eepI2C_WaitEepromStandbyState();
                WriteAddr += I2C_PageSize;
                pBuffer += I2C_PageSize;
            }
            if(NumOfSingle!=0)
            {
                eepI2C_PageWrite(pBuffer, WriteAddr, NumOfSingle);
                eepI2C_WaitEepromStandbyState();
            }
        }
    }
    /* If WriteAddr is not I2C_PageSize aligned */
    else 
    {
        /* If NumByteToWrite < I2C_PageSize */
        if(NumOfPage== 0) 
        {
            eepI2C_PageWrite(pBuffer, WriteAddr, NumOfSingle);
            eepI2C_WaitEepromStandbyState();
        }
        /* If NumByteToWrite > I2C_PageSize */
        else
        {
            NumByteToWrite -= count;
            NumOfPage = NumByteToWrite / I2C_PageSize; 
            NumOfSingle = NumByteToWrite % I2C_PageSize;

            if(count != 0)
            {
                eepI2C_PageWrite(pBuffer, WriteAddr, count);
                eepI2C_WaitEepromStandbyState();
                WriteAddr += count;
                pBuffer += count;
            }

            while(NumOfPage--)
            {
                eepI2C_PageWrite(pBuffer, WriteAddr, I2C_PageSize);
                eepI2C_WaitEepromStandbyState();
                WriteAddr += I2C_PageSize;
                pBuffer += I2C_PageSize;
            }
            if(NumOfSingle != 0)
            {
                eepI2C_PageWrite(pBuffer, WriteAddr, NumOfSingle);
                eepI2C_WaitEepromStandbyState();
            }
        }
    }
#endif
}


/****************************************************************************************************
 * DESCRIPTION:
 *            
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 EepdevReadValues(UINT16 address, UINT16 *rdValue, UINT16 rdWordSz)
{

	UINT16	eepAddress;
	INT32	rdCnt;
	INT32	rslt = I2C_DRV_SUCCESS;

	eepAddress = GET_EEP_PHYSICAL_ADDRESS( address );

	rslt = eepI2C_BufferRead(eepAddress, (UINT8*)rdValue, (2*rdWordSz));

	return rslt;

}


/****************************************************************************************************
 * DESCRIPTION:
 *            
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 EepdevWriteNoSumValues(UINT16 address, UINT16 *wrValue, UINT16 wrWordSz)
{
	UINT16	eepAddress;
	INT32	wrCnt;	
	INT32	rslt = 0;					

	eepAddress = GET_EEP_PHYSICAL_ADDRESS( address );

	eepI2C_BufferWrite(eepAddress, (UINT8*)wrValue, (2*wrWordSz));

	return rslt;
}


/****************************************************************************************************
 * DESCRIPTION:
 *            
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 EepdevWriteParameters(UINT16 address, UINT16 *wrValue, UINT16 wrWordSz)
{
	INT32	rslt, err;
	UINT16	cnt;
	UINT16	checksum;
	UINT16	wr_addr;
	UINT16	rdValue;
#if 1
	rslt = 0;
	checksum = 0;

	/* Write new values */
	for(cnt = 0; cnt < wrWordSz; cnt++)
	{
		wr_addr = address + (cnt * 2);
		/* Read a stored value */
		err = EepdevReadValues(wr_addr, &rdValue, 1);
		if(err != 0)
		{// error !!
			rslt = err;
			checksum = 0;
			break;
		}

		/* Verify and write a new value */
		if(rdValue != wrValue[cnt])
		{
			err = EepdevWriteNoSumValues(wr_addr, &wrValue[cnt], 1);
			if(err != 0)
			{// error !!
				rslt = err;
				checksum = 0;
				break;
			}
		}

		/* add a new sum value */
		checksum += (UINT16)(rdValue - wrValue[cnt]);
	}

	/* Write a new checksum value */
	if(checksum != 0)
	{
		if( (address & EEP_CHECKSUM_ADDRESS_MASK) == EEP_CMNPRM_ADDRESS )
		{ /* Axis Common Parameter */
			wr_addr = EEP_CMNPRM_CHKSUM_ADR;
		}
		else
		{
			wr_addr = (address & EEP_MAP_AXIS_MASK) + EEP_AXPRM_CHKSUM_ADR; 
		}

		wr_addr += EEP_MAP_HEADER_INFO_LENGTH;

		rslt = EepdevReadValues(wr_addr, &rdValue, 1);
		if(rslt == 0)
		{
			checksum += rdValue;
			rslt = EepdevWriteNoSumValues( wr_addr, &checksum, 1);
		}
	}
	else 
	{
		rslt = 1;
	}

#endif

	return rslt;

}


/****************************************************************************************************
 * DESCRIPTION:  Initialize the Eeprom queue
 *            
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void EepdevCreateQueue(EEP_QUE_HANDLE *hEepQue, EEP_QUE_BUFFER *QueBuf, UINT32 MaxQueSize)
{
	hEepQue->maxQueSize = MaxQueSize;
	hEepQue->QueIdx 	= 0;
	hEepQue->nextQueIdx = 0;
	hEepQue->QueBuffer	= QueBuf;
}


/****************************************************************************************************
 * DESCRIPTION:  Description: Send a que packet
 *            
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 EepdevPutQueue(EEP_QUE_HANDLE *hEepQue, UINT32 devID,
													  UINT16 address, UINT32 wrValue, UINT16 wrWordSz)
{
	UINT16	queCnt;
	UINT16	queIdx;

	queCnt = (hEepQue->nextQueIdx + 1) - hEepQue->QueIdx;
	if(queCnt > hEepQue->maxQueSize)
	{
		return -1;
	}

	queIdx = hEepQue->nextQueIdx;
	hEepQue->QueBuffer[queIdx].devID	= devID;
	hEepQue->QueBuffer[queIdx].address	= address;
	hEepQue->QueBuffer[queIdx].wrWordSz = wrWordSz;
	hEepQue->QueBuffer[queIdx].wrValue	= wrValue;

	++ queIdx;
	hEepQue->nextQueIdx = (queIdx >= hEepQue->maxQueSize) ? 0 : queIdx;
	return 0;
}

/****************************************************************************************************
* DESCRIPTION:  Flush the queue value
*			
* RETURNS:
*
****************************************************************************************************/
PUBLIC void EepdevWriteQuedValues(EEP_QUE_HANDLE *hEepQue, UINT32 maxWriteNum)
{
	UINT32	queIdx;
	UINT32	writeCnt;
	EEP_QUE_BUFFER	QueBuf;

	writeCnt = 0;
	queIdx = hEepQue->QueIdx;

	while ((hEepQue->nextQueIdx != queIdx) && (writeCnt < maxWriteNum))
	{
		QueBuf = hEepQue->QueBuffer[queIdx];

		EepdevWriteNoSumValues(QueBuf.address, (UINT16*)&(QueBuf.wrValue), QueBuf.wrWordSz);

		queIdx++;
		if(queIdx >= hEepQue->maxQueSize)
		{
			queIdx = 0;
		}

		hEepQue->QueIdx = queIdx;
		writeCnt++;
	}
}


