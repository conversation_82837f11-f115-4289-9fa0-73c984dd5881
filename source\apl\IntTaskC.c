/****************************************************************************************************
 *
 * FILE NAME:  IntTaskC.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.05
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	05-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Global.h"
#include "TaskCMain.h"
#include "HardApi.h"
#include "ecatappl.h"
#include "PowerManager.h"

#include "canfestival.h"
#include "fundefine.h"
#include "canfd_protocol.h"

extern  UINT32  Uid_Check_Result;


UINT16 TASKC_delay = 0;
UINT16 IPMTempCNT= 0 ;

extern float            gTestUa;
extern float            gTestUb;
extern volatile UINT16  gTeDeltaAg;
extern UINT16           OpenLoop;
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void IntTaskCExec(void)
{
    INT16                 ax_id;
    AXIS_HANDLE           *AxisC;
    AXIS_HANDLE			  *AxisCtmp;
    TASK_MEASURE_TIME	  *TaskProcessTime;	
    UINT32                SABinC_exeTime;
    POWER_MNG		      *PowerManager;
    INT32		           AlmWrnSts;
    HW_STATUS_REG          HwIputSignals;
//	Send_STATUS_REG        HwOutputSignals;  


    R_BSP_SoftwareDelay(TASKC_delay,BSP_DELAY_UNITS_MICROSECONDS);
	/* Read time stamp at the entrance of interrupt: for calculate the elapsed time of interrupt*/
    UINT32 IsrStartTime = hApi_GetTimerCounter();

//    timerForCan();
    AxisC = GetAxisHandle(0);
    TaskProcessTime = AxisC->TaskProcessTime;
    if(TaskProcessTime->SA_maxTime == 0) return;
    TaskProcessTime->SC_exeflg = TRUE;
    TaskProcessTime->crtTimePer1ms = TaskProcessTime->SA_sumTime
                                +TaskProcessTime->SB_sumTime
                                +TaskProcessTime->SC_crtTime;
    TaskProcessTime->SA_sumTime = 0;	
    TaskProcessTime->SB_sumTime = 0;
    if( TaskProcessTime->crtTimePer1ms > TaskProcessTime->maxTimePer1ms )
    {
            TaskProcessTime->maxTimePer1ms = TaskProcessTime->crtTimePer1ms;
    }

    PowerManager = AxisC->PowMngr;

    HwIputSignals.all = AxisC->HwIputSignals->all;

    
    if(HwIputSignals.bits.IpmA)
    {
        ALMSetServoAlarm( AxisC[0].AlmMngr, ALM_IPM );
    }
    
    if(HwIputSignals.bits.STO1 ) 
    {
        ALMSetGlobalAlarm(ALM_STO1 );
    }
    
    if(HwIputSignals.bits.STO2 ) 
    {
        ALMSetGlobalAlarm(ALM_STO2 );
    }

    AdetCheckUid(AxisC[0].AlmMngr, &AxisC[0].CheckAlm->UidCheck, Uid_Check_Result);
    
    
                    
    for( ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++ )
    {
        AxisCtmp = &AxisC[ax_id];
        
        TskcInputSeqStatus( AxisCtmp );
        

        
        gTestUa = AxisCtmp->Prm->PnCfgPrm.OpenLoopVa/1000.0;
        gTestUb = AxisCtmp->Prm->PnCfgPrm.OpenLoopVb/1000.0;
        OpenLoop = AxisCtmp->Prm->PnCfgPrm.OpenLoopT;
        gTeDeltaAg = AxisCtmp->Prm->PnCfgPrm.OpenLoopA;
    }

    PcmInputPowerStatus( PowerManager,HwIputSignals.bits.Acl, HwIputSignals.bits.RegenErr );
    
    PowerSysReset(PowerManager);

    PcmPowerManager( AxisC->PowMngr, 
                           AxisC->SeqCtrlOut->MotStop, 
                           AxisC->BaseCtrls->BaseEnable, 
                           AxisC->BaseCtrls->Cia402Axis.Cia402BeReq );
    
#if(1 == REGENE_USE)    
    PcmCountRegeneOnTime(AxisC->PowMngr);

    if( (PowerManager->MainPowChecker.P.DcbusWay== FALSE)			
       || ( (PowerManager->MainPowChecker.P.DcbusWay == TRUE)			
         && (PowerManager->RegeneCtrl.P.RegSyn == PNREGSYN) )  )		
    {
      if( ALMCheckGlobalAlarm(WRN_EXREG) == FALSE )				
      {
          UINT32	AlmOLInfo = AxisC->AlmMngr->Status.AlmOLInfo; 	
          AlmWrnSts = PcmCheckRegeneOverLoad( &(PowerManager->RegeneCtrl), &AlmOLInfo);
          if( AlmWrnSts != CHK_REGOL_NO_OPERATION )
          {
              if(0 != (AlmWrnSts & CHK_REGOL_SET_ALARM))
              {	
                  ALMSetGlobalAlarm( ALM_RGOL );
              }
              else if(0 != (AlmWrnSts & CHK_REGOL_SET_WARNING))
              {	
                  ALMSetGlobalAlarm( WRN_RGOLF );
              }
              else
              {	
                  ALMClearGlobalAlarm( WRN_RGOLF );
              }
          }
      } 
    }
#endif    

    
    for( ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++ )
    {
        AxisCtmp = &AxisC[ax_id];
        

        
        TskcAlarmDetections(AxisCtmp);

        TskcBaseDriveSequence(AxisCtmp);

//        SequenceIO_Loop(AxisCtmp->SequenceIO);     

        #if NO_DIDO
        SysBaseDIDOSlow(AxisCtmp);
        #endif
        TskcServoOnCtrl(AxisCtmp);

        TskcSequenceMain(AxisCtmp);
        

        MonitorParameter(AxisCtmp);
        
        // todo BaseEable  control fa
        if(ax_id == 0)
        {
           hApi_FanCtrl(AxisCtmp->BaseCtrls->BaseEnable,AxisCtmp->Prm->PnCfgPrm.FanCtrlDuty);
        }   
    }  
    
    #if NO_ETHERCAT
    ECAT_CheckTimer();
    #endif

    
   AxisC[0].CheckAlm->OverTemp.var.IPMTemp = hApi_GetEnvTemp(&AxisC[0].Prm->PnMoniPrm.IPMTemp);
   AxisC[0].CheckAlm->OverTemp.var.MotTemp = hApi_GetMotTemp(&AxisC[0].Prm->PnMoniPrm.MototTemp);

   AdetCheckOverTemp(AxisC[0].AlmMngr, &AxisC[0].CheckAlm->OverTemp);



    
    KlibCountTimeStamp(1, PowerManager->PowerSts.PowerOnEep);


    TaskProcessTime->SCCount++;  
    SABinC_exeTime = TaskProcessTime->SAinC_exeTime + TaskProcessTime->SBinC_exeTime;
//	TaskProcessTime->SC_crtTime = hApi_GetTimerCounter() - IsrStartTime - SABinC_exeTime;
    TaskProcessTime->SC_crtTime = hApi_GetTimerCounter() - IsrStartTime;
    if( TaskProcessTime->SC_crtTime > TaskProcessTime->SC_maxTime)
    {
            TaskProcessTime->SC_maxTime = TaskProcessTime->SC_crtTime;
    }

    TaskProcessTime->SC_exeflg = FALSE;
    TaskProcessTime->SAinC_exeTime = 0;
    TaskProcessTime->SBinC_exeTime = 0;
    
    if(TaskProcessTime->SC_maxTime > (UINT32)TASKC_MAX_TIME)
    {
        ALMSetGlobalAlarm( ALM_ISR_TIME );
    }

//    hApi_IWDG_FeedDog();

}

