/****************************************************************************************************
 *
 * FILE NAME:  FnEasyFFT.h
 *
 * DESCRIPTION: Easy FFT Measurement (Fn206)
 *
 * CREATED ON:  2021.02.20
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	20-02-2021 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FN_EASY_FFT_H_
#define _FN_EASY_FFT_H_

#include "BaseDef.h"
#include "RegAccessIf.h"
#include "PnPrmStruct.h"
#include "Bprm.h"


/****************************************************************************************************/
/*																									*/
/*		Structure Definition																		*/
/*																									*/
/****************************************************************************************************/
typedef	struct EASYFFT {
	struct {
		UINT16	EzFftMode;				/* 0x2060 : Easy FFT mode									*/
		UINT16	EzFftState;				/* 0x2061 : EasyFFT completion confirmation					*/
		UINT16	EzFftTrq;				/* 0x2062 : EasyFFT torque amplitude						*/
		UINT16	EzFftDetf;				/* 0x2410 : EasyFFT detection frequency						*/
		UINT16	EzFftFil1;				/* 0x2411 : EasyFFT filter frequency 1						*/
		UINT16	EzFftFil2;				/* 0x2412 : EasyFFT filter frequency 2						*/
	} OpeReg;
/*--------------------------------------------------------------------------------------------------*/
	struct {
		BOOL	UseHpFil;				/* Use high-pass filter										*/
/*--------------------------------------------------------------------------------------------------*/
		INT32	KnormFrq;				/* Frequency unit conversion gain							*/
		UINT32	Tolerance;				/* Tolerance of variation for each step = 100[Hz]			*/
		UINT16	UnitMaxFreq;			/* Maximum detection frequency for unit conversion[Hz]		*/
		UINT16	RetUnitShift;			/* Number of shifts for unit inverse conversion				*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	ReTryMethod;			/* Notch detection retry method								*/
		UINT16	ReTryMaxCount;			/* Number of notch detection retries						*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	*TblBufAddr;			/* Table operation command buffer address					*/
		UINT16	*TrcBufAddr;			/* Trace buffer address										*/
		UINT16	Dummy;					/* for Alignment											*/
	} conf;
/*--------------------------------------------------------------------------------------------------*/
	struct {
		BOOL	Zoom;					/* zoom														*/
		BOOL	DrvDir;					/* Driving direction										*/
		BOOL	Drived;					/* Operation completed										*/
		BOOL	SameDir;				/* Same direction driving									*/
		BOOL	AnalyzeState;			/* Anomaly detection										*/
		BOOL	SwpDir;					/* Sweep direction increase (0) / decrease (1)				*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	SeqPtr;					/* Sequence pointer											*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	NotchSw;				/* Notch switch												*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	FrqLowerLmt;			/* Lower limit unit of notch detection / vibration detection frequency [Hz]*/
		UINT16	FrqSwpMin;				/* Minimum value of sweep frequency Unit [Hz]				*/
		UINT16	FrqSwpMax;				/* Maximum value of sweep frequency Unit [Hz]				*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	OrgFrqLowerLmt;			/* Backup of the lower limit of notch detection / vibration detection frequency*/
		UINT16	OrgFrqSwpMin;			/* Backup of the minimum sweep frequency					*/
		UINT16	OrgFrqSwpMax;			/* Backup of maximum sweep frequency						*/
/*--------------------------------------------------------------------------------------------------*/
		UINT32	DetFrq0;				/* Frequency value (notch frequency) which is the maximum value of the absolute value*/
		UINT16	SpcPeak0;				/* Speed FB / actual torque value (absolute value), which is the maximum value of the absolute value*/
		UINT16	DetState0;				/* 															*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	PosDrvCount;			/* Number of forward operations								*/
		UINT16	NegDrvCount;			/* Number of reverse operations								*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	Step;					/* STEP state												*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	StepReTryCnt;			/* Retry Counter: The Pros and Cons of STEP					*/
		UINT16	AvrReTryCnt;			/* Retry Counter: Pros and cons of forward and reverse average*/
		UINT16	DetReTryCnt;			/* Retry counter: Pros and cons of detection				*/
/*--------------------------------------------------------------------------------------------------*/
		UINT16	DetCount;				/* Detection result counter									*/
		UINT16	DetState[ 4 ];			/* Detection result (status)								*/
		UINT32	DetFrq[ 4 ];			/* Detection result (frequency)								*/
		UINT32	DetAvrFrq[ 2 ];			/* Detection average result (frequency)						*/
/*--------------------------------------------------------------------------------------------------*/
		UINT32	FinalDetFrq;			/* Final result frequency									*/
/*--------------------------------------------------------------------------------------------------*/
		INT32	KTrqRef;				/* Sweep torque calculation gain							*/
/*--------------------------------------------------------------------------------------------------*/
		INT32	KHpFilter;				/* High pass filter gain									*/
/*--------------------------------------------------------------------------------------------------*/
		INT32	DeltaFrqRes;			/*For response processing * Sweep frequency increment / decrement value*/
		INT32	DeltaFrqRef;			/* Command creation * sweep frequency increment / decrement value*/
	} var;
} EASYFFT;



/****************************************************************************************************/
/*																									*/
/*		Constant Definition																			*/
/*																									*/
/****************************************************************************************************/
enum EZFFT_SEQ_STS {
	EZFFT_SEQ_INIT		= 0,		/* Initial processing											*/
	EZFFT_SEQ_TRQSET,				/* Sweep torque setting											*/
	EZFFT_SEQ_READY,				/* Operation preparation										*/
	EZFFT_SEQ_DRIVE,				/* During driving operation										*/
	EZFFT_SEQ_RESULT,				/* Operation completed (waiting for filter setting)				*/
	EZFFT_SEQ_END,					/* End processing												*/
};
/*--------------------------------------------------------------------------------------------------*/
enum EZFFT_LSEQ_STS {
	EZFFT_LSEQ_DRV		= 0,		/* driving														*/
	EZFFT_LSEQ_WAITSTOP,			/* Waiting for operation stop									*/
	EZFFT_LSEQ_DETFRQ,				/* During frequency analysis									*/
	EZFFT_LSEQ_AVRFRQ,				/* Forward / reverse difference averaging						*/
	EZFFT_LSEQ_STPEND,				/* Operation completed											*/
	EZFFT_LSEQ_AVRSTP,				/* Average of multiple trials									*/
	EZFFT_LSEQ_ZOOMREQ,				/* Request for retry around frequency analysis result			*/
	EZFFT_LSEQ_ERR,					/* Abnormal termination											*/
	EZFFT_LSEQ_WARN,				/* Warning end													*/
	EZFFT_LSEQ_DONE,				/* Successful completion										*/
};
/*--------------------------------------------------------------------------------------------------*/
enum EZFFT_CMP_STS {
	EZFFT_STS_END		= 0,		/* Done															*/
	EZFFT_STS_RUN,					/* Incomplete													*/
	EZFFT_STS_DETNG,				/* Peak value undetectable										*/
	EZFFT_STS_ERR,					/* Abnormal termination											*/
};
/*--------------------------------------------------------------------------------------------------*/
#define		EZFFT_SIZ_TRACE			1024	/* Trace size											*/
/*--------------------------------------------------------------------------------------------------*/
#define		EZFFT_DRV_FORWARD		0		/* Table driving direction: +							*/
#define		EZFFT_DRV_REVERSE		1		/* Table driving direction: -							*/
/*--------------------------------------------------------------------------------------------------*/
#define		EZFFT_ANASTS_OK			0		/* Frequency analysis result OK							*/
#define		EZFFT_ANASTS_NG			1		/* Frequency analysis result NG							*/
/*--------------------------------------------------------------------------------------------------*/
#define		EZFFT_STEP_1			0		/* Frequency analysis step1								*/
#define		EZFFT_STEP_2			1		/* Frequency analysis step2								*/
/*--------------------------------------------------------------------------------------------------*/
#define		EZFFT_ANAOBJ_REF		0		/* Frequency analysis target: Directive					*/
#define		EZFFT_ANAOBJ_RES		1		/* Frequency analysis target: Response					*/
/*--------------------------------------------------------------------------------------------------*/
#define		EZFFT_FRQ_L2H			0		/* Torque sweep direction: Low => High					*/
#define		EZFFT_FRQ_H2L			1		/* Torque sweep direction: High => Low					*/
/*--------------------------------------------------------------------------------------------------*/
#define		EZFFT_RETURN_UNIT		125		/* Constant for unit inverse conversion					*/
#define		EZFFT_DEVLVL_DIR		250		/* Allowable variation during forward and reverse operation [0.1%]*/
/*--------------------------------------------------------------------------------------------------*/
#define		EZFFT_CLOSE_MANUAL		4		/* Position control retry count 0 times 1 operation only (one-sided operation)*/
#define		EZFFT_ONESTEP_RETRY_0	5		/* No retries around the frequency analysis result, 0 retries*/
#define		EZFFT_ONESTEP_RETRY_1	6		/* No retries around the frequency analysis result, 1 retry*/
#define		EZFFT_OPEN_MANUAL		7		/* Torque control retry count 0 times Only one operation (one-sided operation)*/
/*--------------------------------------------------------------------------------------------------*/



/****************************************************************************************************/
/*																									*/
/*		Function Prototype Definition																*/
/*																									*/
/****************************************************************************************************/
void		EasyFftInitialize( EASYFFT *EasyFFT, UINT16 *TableBuf, UINT16 *TraceBuf, PRMDATA *Prm, BPRMDAT *Bprm);
PRM_RSLT	RpiFunSetEasyFftTrqLevel( EASYFFT *EasyFFT, UINT16 Lvl );





#endif

