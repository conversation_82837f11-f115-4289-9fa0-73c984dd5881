/***********************************************************************************************************************
 * Copyright [2020-2023] Renesas Electronics Corporation and/or its affiliates.  All Rights Reserved.
 *
 * This software and documentation are supplied by Renesas Electronics Corporation and/or its affiliates and may only
 * be used with products of Renesas Electronics Corp. and its affiliates ("Renesas").  No other uses are authorized.
 * Renesas products are sold pursuant to Renesas terms and conditions of sale.  Purchasers are solely responsible for
 * the selection and use of Renesas products and Renesas assumes no liability.  No license, express or implied, to any
 * intellectual property right is granted by Renesas.  This software is protected under all applicable laws, including
 * copyright laws. Renesas reserves the right to change or discontinue this software and/or this documentation.
 * THE SOFTWARE AND DOCUMENTATION IS DELIVERED TO YOU "AS IS," AND <PERSON><PERSON><PERSON>AS MAKES NO REPRESENTATIONS OR WARRANTIES, AND
 * TO THE FULLEST EXTENT PERMISSIBLE UNDER APPLICABLE LAW, DISCLAIMS ALL WARRANTIES, WHETHER EXPLICITLY OR IMPLICITLY,
 * INCLUDING WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NONINFRINGEMENT, WITH RESPECT TO THE
 * SOFTWARE OR DOCUMENTATION.  RENESAS SHALL HAVE NO LIABILITY ARISING OUT OF ANY SECURITY VULNERABILITY OR BREACH.
 * TO THE MAXIMUM EXTENT PERMITTED BY LAW, IN NO EVENT WILL RENESAS BE LIABLE TO YOU IN CONNECTION WITH THE SOFTWARE OR
 * DOCUMENTATION (OR ANY PERSON OR ENTITY CLAIMING RIGHTS DERIVED FROM YOU) FOR ANY LOSS, DAMAGES, OR CLAIMS WHATSOEVER,
 * INCLUDING, WITHOUT LIMITATION, ANY DIRECT, CONSEQUENTIAL, SPECIAL, INDIRECT, PUNITIVE, OR INCIDENTAL DAMAGES; ANY
 * LOST PROFITS, OTHER ECONOMIC DAMAGE, PROPERTY DAMAGE, OR PERSONAL INJURY; AND EVEN IF RENESAS HAS BEEN ADVISED OF THE
 * POSSIBILITY OF SUCH LOSS, DAMAGES, CLAIMS OR COSTS.
 **********************************************************************************************************************/

/*******************************************************************************************************************//**
 * @ingroup RENESAS_INTERFACES
 * @defgroup IOPORT_API I/O Port Interface
 * @brief Interface  for accessing I/O ports and configuring I/O functionality.
 *
 * @section IOPORT_API_SUMMARY Summary
 * The IOPort shared interface provides the ability to access the IOPorts of a device at both bit and port level.
 * Port and pin direction can be changed.
 *
 * IOPORT Interface description: @ref IOPORT
 *
 * @{
 **********************************************************************************************************************/

#ifndef R_IOPORT_API_H
#define R_IOPORT_API_H

/***********************************************************************************************************************
 * Includes
 **********************************************************************************************************************/

/* Common error codes and definitions. */
#include "bsp_api.h"

/* Common macro for FSP header files. There is also a corresponding FSP_FOOTER macro at the end of this file. */
FSP_HEADER

/**********************************************************************************************************************
 * Macro definitions
 **********************************************************************************************************************/
#define IOPORT_API_VERSION_MAJOR    (1U) // DEPRECATED
#define IOPORT_API_VERSION_MINOR    (2U) // DEPRECATED

/* Private definition to set enumeration values. */
#define IOPORT_P_OFFSET             (0U)
#define IOPORT_PM_OFFSET            (1U)
#define IOPORT_PMC_OFFSET           (3U)
#define IOPORT_PFC_OFFSET           (4U)
#define IOPORT_DRCTL_OFFSET         (8U)
#define IOPORT_RSELP_OFFSET         (14U)

/**********************************************************************************************************************
 * Typedef definitions
 **********************************************************************************************************************/

/** IO port type used with ports */
typedef uint16_t ioport_size_t;        ///< IO port size on this device

/** Superset of all peripheral functions.  */
typedef enum e_ioport_pin_pfc
{
    IOPORT_PIN_P000_PFC_00_ETH2_RXD3       = (0x00U << IOPORT_PFC_OFFSET), ///< P00_0 / ETHER_ETHn / ETH2_RXD3
    IOPORT_PIN_P000_PFC_02_D15             = (0x02U << IOPORT_PFC_OFFSET), ///< P00_0 / BSC / D15
    IOPORT_PIN_P000_PFC_03_SCK2            = (0x03U << IOPORT_PFC_OFFSET), ///< P00_0 / SCIn / SCK2
    IOPORT_PIN_P000_PFC_04_DE2             = (0x04U << IOPORT_PFC_OFFSET), ///< P00_0 / SCIn / DE2
    IOPORT_PIN_P000_PFC_05_HD15            = (0x05U << IOPORT_PFC_OFFSET), ///< P00_0 / PHOSTIF / HD15
    IOPORT_PIN_P001_PFC_00_IRQ0            = (0x00U << IOPORT_PFC_OFFSET), ///< P00_1 / IRQ / IRQ0
    IOPORT_PIN_P001_PFC_01_ETH2_RXDV       = (0x01U << IOPORT_PFC_OFFSET), ///< P00_1 / ETHER_ETHn / ETH2_RXDV
    IOPORT_PIN_P001_PFC_03_A13             = (0x03U << IOPORT_PFC_OFFSET), ///< P00_1 / BSC / A13
    IOPORT_PIN_P001_PFC_04_MTIC5U          = (0x04U << IOPORT_PFC_OFFSET), ///< P00_1 / MTU3n / MTIC5U
    IOPORT_PIN_P001_PFC_05_RXD2_SCL2_MISO2 = (0x05U << IOPORT_PFC_OFFSET), ///< P00_1 / SCIn / RXD2_SCL2_MISO2
    IOPORT_PIN_P002_PFC_00_ETH2_TXEN       = (0x00U << IOPORT_PFC_OFFSET), ///< P00_2 / ETHER_ETHn / ETH2_TXEN
    IOPORT_PIN_P002_PFC_02_RD              = (0x02U << IOPORT_PFC_OFFSET), ///< P00_2 / BSC / RD
    IOPORT_PIN_P002_PFC_03_MTIC5V          = (0x03U << IOPORT_PFC_OFFSET), ///< P00_2 / MTU3n / MTIC5V
    IOPORT_PIN_P002_PFC_04_TXD2_SDA2_MOSI2 = (0x04U << IOPORT_PFC_OFFSET), ///< P00_2 / SCIn / TXD2_SDA2_MOSI2
    IOPORT_PIN_P002_PFC_05_USB_OVRCUR      = (0x05U << IOPORT_PFC_OFFSET), ///< P00_2 / USB_HS / USB_OVRCUR
    IOPORT_PIN_P003_PFC_00_IRQ1            = (0x00U << IOPORT_PFC_OFFSET), ///< P00_3 / IRQ / IRQ1
    IOPORT_PIN_P003_PFC_01_ETH2_REFCLK     = (0x01U << IOPORT_PFC_OFFSET), ///< P00_3 / ETHER_ETHn / ETH2_REFCLK
    IOPORT_PIN_P003_PFC_02_RMII2_REFCLK    = (0x02U << IOPORT_PFC_OFFSET), ///< P00_3 / ETHER_ETHn / RMII2_REFCLK
    IOPORT_PIN_P003_PFC_04_RD_WR           = (0x04U << IOPORT_PFC_OFFSET), ///< P00_3 / BSC / RD_WR
    IOPORT_PIN_P003_PFC_05_MTIC5W          = (0x05U << IOPORT_PFC_OFFSET), ///< P00_3 / MTU3n / MTIC5W
    IOPORT_PIN_P003_PFC_06_SS2_CTS2_RTS2   = (0x06U << IOPORT_PFC_OFFSET), ///< P00_3 / SCIn / SS2_CTS2_RTS2
    IOPORT_PIN_P004_PFC_00_IRQ13           = (0x00U << IOPORT_PFC_OFFSET), ///< P00_4 / IRQ / IRQ13
    IOPORT_PIN_P004_PFC_01_ETH2_RXER       = (0x01U << IOPORT_PFC_OFFSET), ///< P00_4 / ETHER_ETHn / ETH2_RXER
    IOPORT_PIN_P004_PFC_03_WAIT            = (0x03U << IOPORT_PFC_OFFSET), ///< P00_4 / BSC / WAIT
    IOPORT_PIN_P004_PFC_04_MTIOC3A         = (0x04U << IOPORT_PFC_OFFSET), ///< P00_4 / MTU3n / MTIOC3A
    IOPORT_PIN_P004_PFC_05_GTIOC0A         = (0x05U << IOPORT_PFC_OFFSET), ///< P00_4 / GPTn / GTIOC0A
    IOPORT_PIN_P004_PFC_06_MCLK0           = (0x06U << IOPORT_PFC_OFFSET), ///< P00_4 / DSMIFn / MCLK0
    IOPORT_PIN_P004_PFC_07_HWAIT           = (0x07U << IOPORT_PFC_OFFSET), ///< P00_4 / PHOSTIF / HWAIT
    IOPORT_PIN_P005_PFC_00_ETHSW_PHYLINK2  = (0x00U << IOPORT_PFC_OFFSET), ///< P00_5 / ETHER_ETHSW / ETHSW_PHYLINK2
    IOPORT_PIN_P005_PFC_02_CS0             = (0x02U << IOPORT_PFC_OFFSET), ///< P00_5 / BSC / CS0
    IOPORT_PIN_P005_PFC_03_ESC_PHYLINK2    = (0x03U << IOPORT_PFC_OFFSET), ///< P00_5 / ETHER_ESC / ESC_PHYLINK2
    IOPORT_PIN_P005_PFC_04_MTIOC3C         = (0x04U << IOPORT_PFC_OFFSET), ///< P00_5 / MTU3n / MTIOC3C
    IOPORT_PIN_P005_PFC_05_GTIOC0B         = (0x05U << IOPORT_PFC_OFFSET), ///< P00_5 / GPTn / GTIOC0B
    IOPORT_PIN_P005_PFC_06_MDAT0           = (0x06U << IOPORT_PFC_OFFSET), ///< P00_5 / DSMIFn / MDAT0
    IOPORT_PIN_P005_PFC_07_ETHSW_PHYLINK0  = (0x07U << IOPORT_PFC_OFFSET), ///< P00_5 / ETHER_ETHSW / ETHSW_PHYLINK0
    IOPORT_PIN_P005_PFC_08_ESC_PHYLINK0    = (0x08U << IOPORT_PFC_OFFSET), ///< P00_5 / ETHER_ESC / ESC_PHYLINK0
    IOPORT_PIN_P006_PFC_00_ETH2_TXCLK      = (0x00U << IOPORT_PFC_OFFSET), ///< P00_6 / ETHER_ETHn / ETH2_TXCLK
    IOPORT_PIN_P006_PFC_01_CS5             = (0x01U << IOPORT_PFC_OFFSET), ///< P00_6 / BSC / CS5
    IOPORT_PIN_P006_PFC_02_MTIOC3B         = (0x02U << IOPORT_PFC_OFFSET), ///< P00_6 / MTU3n / MTIOC3B
    IOPORT_PIN_P006_PFC_03_GTIOC1A         = (0x03U << IOPORT_PFC_OFFSET), ///< P00_6 / GPTn / GTIOC1A
    IOPORT_PIN_P007_PFC_00_IRQ13           = (0x00U << IOPORT_PFC_OFFSET), ///< P00_7 / IRQ / IRQ13
    IOPORT_PIN_P007_PFC_01_RAS             = (0x01U << IOPORT_PFC_OFFSET), ///< P00_7 / BSC / RAS
    IOPORT_PIN_P007_PFC_02_MTIOC4A         = (0x02U << IOPORT_PFC_OFFSET), ///< P00_7 / MTU3n / MTIOC4A
    IOPORT_PIN_P007_PFC_03_GTIOC2A         = (0x03U << IOPORT_PFC_OFFSET), ///< P00_7 / GPTn / GTIOC2A
    IOPORT_PIN_P010_PFC_00_GMAC_MDIO       = (0x00U << IOPORT_PFC_OFFSET), ///< P01_0 / ETHER_GMAC / GMAC_MDIO
    IOPORT_PIN_P010_PFC_01_ETHSW_MDIO      = (0x01U << IOPORT_PFC_OFFSET), ///< P01_0 / ETHER_ETHSW / ETHSW_MDIO
    IOPORT_PIN_P010_PFC_02_CAS             = (0x02U << IOPORT_PFC_OFFSET), ///< P01_0 / BSC / CAS
    IOPORT_PIN_P010_PFC_03_ESC_MDIO        = (0x03U << IOPORT_PFC_OFFSET), ///< P01_0 / ETHER_ESC / ESC_MDIO
    IOPORT_PIN_P010_PFC_04_MTIOC4C         = (0x04U << IOPORT_PFC_OFFSET), ///< P01_0 / MTU3n / MTIOC4C
    IOPORT_PIN_P010_PFC_05_GTIOC3A         = (0x05U << IOPORT_PFC_OFFSET), ///< P01_0 / GPTn / GTIOC3A
    IOPORT_PIN_P010_PFC_06_CTS2            = (0x06U << IOPORT_PFC_OFFSET), ///< P01_0 / SCIn / CTS2
    IOPORT_PIN_P010_PFC_07_MCLK1           = (0x07U << IOPORT_PFC_OFFSET), ///< P01_0 / DSMIFn / MCLK1
    IOPORT_PIN_P011_PFC_00_GMAC_MDC        = (0x00U << IOPORT_PFC_OFFSET), ///< P01_1 / ETHER_GMAC / GMAC_MDC
    IOPORT_PIN_P011_PFC_01_ETHSW_MDC       = (0x01U << IOPORT_PFC_OFFSET), ///< P01_1 / ETHER_ETHSW / ETHSW_MDC
    IOPORT_PIN_P011_PFC_02_CKE             = (0x02U << IOPORT_PFC_OFFSET), ///< P01_1 / BSC / CKE
    IOPORT_PIN_P011_PFC_03_ESC_MDC         = (0x03U << IOPORT_PFC_OFFSET), ///< P01_1 / ETHER_ESC / ESC_MDC
    IOPORT_PIN_P011_PFC_04_MTIOC3D         = (0x04U << IOPORT_PFC_OFFSET), ///< P01_1 / MTU3n / MTIOC3D
    IOPORT_PIN_P011_PFC_05_GTIOC1B         = (0x05U << IOPORT_PFC_OFFSET), ///< P01_1 / GPTn / GTIOC1B
    IOPORT_PIN_P011_PFC_06_DE2             = (0x06U << IOPORT_PFC_OFFSET), ///< P01_1 / SCIn / DE2
    IOPORT_PIN_P011_PFC_07_MDAT1           = (0x07U << IOPORT_PFC_OFFSET), ///< P01_1 / DSMIFn / MDAT1
    IOPORT_PIN_P012_PFC_00_IRQ2            = (0x00U << IOPORT_PFC_OFFSET), ///< P01_2 / IRQ / IRQ2
    IOPORT_PIN_P012_PFC_01_ETH2_TXD3       = (0x01U << IOPORT_PFC_OFFSET), ///< P01_2 / ETHER_ETHn / ETH2_TXD3
    IOPORT_PIN_P012_PFC_02_CS2             = (0x02U << IOPORT_PFC_OFFSET), ///< P01_2 / BSC / CS2
    IOPORT_PIN_P012_PFC_03_MTIOC4B         = (0x03U << IOPORT_PFC_OFFSET), ///< P01_2 / MTU3n / MTIOC4B
    IOPORT_PIN_P012_PFC_04_GTIOC2B         = (0x04U << IOPORT_PFC_OFFSET), ///< P01_2 / GPTn / GTIOC2B
    IOPORT_PIN_P013_PFC_00_ETH2_TXD2       = (0x00U << IOPORT_PFC_OFFSET), ///< P01_3 / ETHER_ETHn / ETH2_TXD2
    IOPORT_PIN_P013_PFC_01_AH              = (0x01U << IOPORT_PFC_OFFSET), ///< P01_3 / BSC / AH
    IOPORT_PIN_P013_PFC_02_MTIOC4D         = (0x02U << IOPORT_PFC_OFFSET), ///< P01_3 / MTU3n / MTIOC4D
    IOPORT_PIN_P013_PFC_03_GTIOC3B         = (0x03U << IOPORT_PFC_OFFSET), ///< P01_3 / GPTn / GTIOC3B
    IOPORT_PIN_P014_PFC_00_IRQ3            = (0x00U << IOPORT_PFC_OFFSET), ///< P01_4 / IRQ / IRQ3
    IOPORT_PIN_P014_PFC_01_ETH2_TXD1       = (0x01U << IOPORT_PFC_OFFSET), ///< P01_4 / ETHER_ETHn / ETH2_TXD1
    IOPORT_PIN_P014_PFC_02_WE1_DQMLU       = (0x02U << IOPORT_PFC_OFFSET), ///< P01_4 / BSC / WE1_DQMLU
    IOPORT_PIN_P014_PFC_03_POE0            = (0x03U << IOPORT_PFC_OFFSET), ///< P01_4 / MTU_POE3 / POE0
    IOPORT_PIN_P015_PFC_00_ETH2_TXD0       = (0x00U << IOPORT_PFC_OFFSET), ///< P01_5 / ETHER_ETHn / ETH2_TXD0
    IOPORT_PIN_P015_PFC_01_WE0_DQMLL       = (0x01U << IOPORT_PFC_OFFSET), ///< P01_5 / BSC / WE0_DQMLL
    IOPORT_PIN_P016_PFC_00_GMAC_PTPTRG1    = (0x00U << IOPORT_PFC_OFFSET), ///< P01_6 / ETHER_GMAC / GMAC_PTPTRG1
    IOPORT_PIN_P016_PFC_01_TRACEDATA0      = (0x01U << IOPORT_PFC_OFFSET), ///< P01_6 / TRACE / TRACEDATA0
    IOPORT_PIN_P016_PFC_02_A20             = (0x02U << IOPORT_PFC_OFFSET), ///< P01_6 / BSC / A20
    IOPORT_PIN_P016_PFC_03_ESC_LATCH1      = (0x03U << IOPORT_PFC_OFFSET), ///< P01_6 / ETHER_ESC / ESC_LATCH1
    IOPORT_PIN_P016_PFC_04_ESC_LATCH0      = (0x04U << IOPORT_PFC_OFFSET), ///< P01_6 / ETHER_ESC / ESC_LATCH0
    IOPORT_PIN_P016_PFC_05_MTIOC1A         = (0x05U << IOPORT_PFC_OFFSET), ///< P01_6 / MTU3n / MTIOC1A
    IOPORT_PIN_P016_PFC_06_GTIOC9A         = (0x06U << IOPORT_PFC_OFFSET), ///< P01_6 / GPTn / GTIOC9A
    IOPORT_PIN_P016_PFC_07_CTS1            = (0x07U << IOPORT_PFC_OFFSET), ///< P01_6 / SCIn / CTS1
    IOPORT_PIN_P016_PFC_08_CANTXDP1        = (0x08U << IOPORT_PFC_OFFSET), ///< P01_6 / CANFDn / CANTXDP1
    IOPORT_PIN_P016_PFC_0A_HA20            = (0x0AU << IOPORT_PFC_OFFSET), ///< P01_6 / PHOSTIF / HA20
    IOPORT_PIN_P017_PFC_00_ETHSW_LPI1      = (0x00U << IOPORT_PFC_OFFSET), ///< P01_7 / ETHER_ETHSW / ETHSW_LPI1
    IOPORT_PIN_P017_PFC_01_TRACEDATA1      = (0x01U << IOPORT_PFC_OFFSET), ///< P01_7 / TRACE / TRACEDATA1
    IOPORT_PIN_P017_PFC_02_A19             = (0x02U << IOPORT_PFC_OFFSET), ///< P01_7 / BSC / A19
    IOPORT_PIN_P017_PFC_03_MTIOC1B         = (0x03U << IOPORT_PFC_OFFSET), ///< P01_7 / MTU3n / MTIOC1B
    IOPORT_PIN_P017_PFC_04_GTIOC9B         = (0x04U << IOPORT_PFC_OFFSET), ///< P01_7 / GPTn / GTIOC9B
    IOPORT_PIN_P017_PFC_05_ADTRG0          = (0x05U << IOPORT_PFC_OFFSET), ///< P01_7 / ADCn / ADTRG0
    IOPORT_PIN_P017_PFC_06_SCK1            = (0x06U << IOPORT_PFC_OFFSET), ///< P01_7 / SCIn / SCK1
    IOPORT_PIN_P017_PFC_07_SPI_RSPCK3      = (0x07U << IOPORT_PFC_OFFSET), ///< P01_7 / SPIn / SPI_RSPCK3
    IOPORT_PIN_P017_PFC_08_CANRX0          = (0x08U << IOPORT_PFC_OFFSET), ///< P01_7 / CANFDn / CANRX0
    IOPORT_PIN_P017_PFC_0A_HA19            = (0x0AU << IOPORT_PFC_OFFSET), ///< P01_7 / PHOSTIF / HA19
    IOPORT_PIN_P020_PFC_00_IRQ4            = (0x00U << IOPORT_PFC_OFFSET), ///< P02_0 / IRQ / IRQ4
    IOPORT_PIN_P020_PFC_01_ETHSW_LPI2      = (0x01U << IOPORT_PFC_OFFSET), ///< P02_0 / ETHER_ETHSW / ETHSW_LPI2
    IOPORT_PIN_P020_PFC_02_TRACEDATA2      = (0x02U << IOPORT_PFC_OFFSET), ///< P02_0 / TRACE / TRACEDATA2
    IOPORT_PIN_P020_PFC_03_A18             = (0x03U << IOPORT_PFC_OFFSET), ///< P02_0 / BSC / A18
    IOPORT_PIN_P020_PFC_04_GTADSML0        = (0x04U << IOPORT_PFC_OFFSET), ///< P02_0 / GPT / GTADSML0
    IOPORT_PIN_P020_PFC_05_RXD1_SCL1_MISO1 = (0x05U << IOPORT_PFC_OFFSET), ///< P02_0 / SCIn / RXD1_SCL1_MISO1
    IOPORT_PIN_P020_PFC_06_SPI_MISO3       = (0x06U << IOPORT_PFC_OFFSET), ///< P02_0 / SPIn / SPI_MISO3
    IOPORT_PIN_P020_PFC_07_CANTX1          = (0x07U << IOPORT_PFC_OFFSET), ///< P02_0 / CANFDn / CANTX1
    IOPORT_PIN_P020_PFC_08_USB_OTGID       = (0x08U << IOPORT_PFC_OFFSET), ///< P02_0 / USB_HS / USB_OTGID
    IOPORT_PIN_P020_PFC_0A_HA18            = (0x0AU << IOPORT_PFC_OFFSET), ///< P02_0 / PHOSTIF / HA18
    IOPORT_PIN_P021_PFC_00_ETHSW_PTPOUT1   = (0x00U << IOPORT_PFC_OFFSET), ///< P02_1 / ETHER_ETHSW / ETHSW_PTPOUT1
    IOPORT_PIN_P021_PFC_01_A17             = (0x01U << IOPORT_PFC_OFFSET), ///< P02_1 / BSC / A17
    IOPORT_PIN_P021_PFC_02_ESC_SYNC1       = (0x02U << IOPORT_PFC_OFFSET), ///< P02_1 / ETHER_ESC / ESC_SYNC1
    IOPORT_PIN_P021_PFC_03_ESC_SYNC0       = (0x03U << IOPORT_PFC_OFFSET), ///< P02_1 / ETHER_ESC / ESC_SYNC0
    IOPORT_PIN_P021_PFC_04_DE1             = (0x04U << IOPORT_PFC_OFFSET), ///< P02_1 / SCIn / DE1
    IOPORT_PIN_P021_PFC_05_HA17            = (0x05U << IOPORT_PFC_OFFSET), ///< P02_1 / PHOSTIF / HA17
    IOPORT_PIN_P022_PFC_00_IRQ14           = (0x00U << IOPORT_PFC_OFFSET), ///< P02_2 / IRQ / IRQ14
    IOPORT_PIN_P022_PFC_01_ETHSW_TDMAOUT0  = (0x01U << IOPORT_PFC_OFFSET), ///< P02_2 / ETHER_ETHSW / ETHSW_TDMAOUT0
    IOPORT_PIN_P022_PFC_02_A16             = (0x02U << IOPORT_PFC_OFFSET), ///< P02_2 / BSC / A16
    IOPORT_PIN_P022_PFC_03_MTIOC2A         = (0x03U << IOPORT_PFC_OFFSET), ///< P02_2 / MTU3n / MTIOC2A
    IOPORT_PIN_P022_PFC_04_GTIOC10A        = (0x04U << IOPORT_PFC_OFFSET), ///< P02_2 / GPTn / GTIOC10A
    IOPORT_PIN_P022_PFC_05_POE10           = (0x05U << IOPORT_PFC_OFFSET), ///< P02_2 / MTU_POE3 / POE10
    IOPORT_PIN_P022_PFC_06_TXD1_SDA1_MOSI1 = (0x06U << IOPORT_PFC_OFFSET), ///< P02_2 / SCIn / TXD1_SDA1_MOSI1
    IOPORT_PIN_P022_PFC_07_SPI_MOSI3       = (0x07U << IOPORT_PFC_OFFSET), ///< P02_2 / SPIn / SPI_MOSI3
    IOPORT_PIN_P022_PFC_08_CANTX0          = (0x08U << IOPORT_PFC_OFFSET), ///< P02_2 / CANFDn / CANTX0
    IOPORT_PIN_P022_PFC_0A_RTCAT1HZ        = (0x0AU << IOPORT_PFC_OFFSET), ///< P02_2 / RTC / RTCAT1HZ
    IOPORT_PIN_P022_PFC_0B_HA16            = (0x0BU << IOPORT_PFC_OFFSET), ///< P02_2 / PHOSTIF / HA16
    IOPORT_PIN_P023_PFC_00_IRQ15           = (0x00U << IOPORT_PFC_OFFSET), ///< P02_3 / IRQ / IRQ15
    IOPORT_PIN_P023_PFC_01_ETHSW_TDMAOUT1  = (0x01U << IOPORT_PFC_OFFSET), ///< P02_3 / ETHER_ETHSW / ETHSW_TDMAOUT1
    IOPORT_PIN_P023_PFC_02_A15             = (0x02U << IOPORT_PFC_OFFSET), ///< P02_3 / BSC / A15
    IOPORT_PIN_P023_PFC_03_AH              = (0x03U << IOPORT_PFC_OFFSET), ///< P02_3 / BSC / AH
    IOPORT_PIN_P023_PFC_04_MTIOC2B         = (0x04U << IOPORT_PFC_OFFSET), ///< P02_3 / MTU3n / MTIOC2B
    IOPORT_PIN_P023_PFC_05_GTIOC10B        = (0x05U << IOPORT_PFC_OFFSET), ///< P02_3 / GPTn / GTIOC10B
    IOPORT_PIN_P023_PFC_06_POE11           = (0x06U << IOPORT_PFC_OFFSET), ///< P02_3 / MTU_POE3 / POE11
    IOPORT_PIN_P023_PFC_07_SS1_CTS1_RTS1   = (0x07U << IOPORT_PFC_OFFSET), ///< P02_3 / SCIn / SS1_CTS1_RTS1
    IOPORT_PIN_P023_PFC_08_SPI_SSL30       = (0x08U << IOPORT_PFC_OFFSET), ///< P02_3 / SPIn / SPI_SSL30
    IOPORT_PIN_P023_PFC_09_CANRX1          = (0x09U << IOPORT_PFC_OFFSET), ///< P02_3 / CANFDn / CANRX1
    IOPORT_PIN_P023_PFC_0B_HA15            = (0x0BU << IOPORT_PFC_OFFSET), ///< P02_3 / PHOSTIF / HA15
    IOPORT_PIN_P024_PFC_00_TDO             = (0x00U << IOPORT_PFC_OFFSET), ///< P02_4 / JTAG/SWD / TDO
    IOPORT_PIN_P024_PFC_01_WE0_DQMLL       = (0x01U << IOPORT_PFC_OFFSET), ///< P02_4 / BSC / WE0_DQMLL
    IOPORT_PIN_P024_PFC_02_DE1             = (0x02U << IOPORT_PFC_OFFSET), ///< P02_4 / SCIn / DE1
    IOPORT_PIN_P024_PFC_03_SPI_SSL33       = (0x03U << IOPORT_PFC_OFFSET), ///< P02_4 / SPIn / SPI_SSL33
    IOPORT_PIN_P025_PFC_00_ETHSW_TDMAOUT3  = (0x00U << IOPORT_PFC_OFFSET), ///< P02_5 / ETHER_ETHSW / ETHSW_TDMAOUT3
    IOPORT_PIN_P025_PFC_01_TDI             = (0x01U << IOPORT_PFC_OFFSET), ///< P02_5 / JTAG/SWD / TDI
    IOPORT_PIN_P025_PFC_02_WE1_DQMLU       = (0x02U << IOPORT_PFC_OFFSET), ///< P02_5 / BSC / WE1_DQMLU
    IOPORT_PIN_P025_PFC_03_SCK5            = (0x03U << IOPORT_PFC_OFFSET), ///< P02_5 / SCIn / SCK5
    IOPORT_PIN_P025_PFC_04_SPI_SSL31       = (0x04U << IOPORT_PFC_OFFSET), ///< P02_5 / SPIn / SPI_SSL31
    IOPORT_PIN_P026_PFC_00_TMS_SWDIO       = (0x00U << IOPORT_PFC_OFFSET), ///< P02_6 / JTAG/SWD / TMS_SWDIO
    IOPORT_PIN_P026_PFC_01_RXD5_SCL5_MISO5 = (0x01U << IOPORT_PFC_OFFSET), ///< P02_6 / SCIn / RXD5_SCL5_MISO5
    IOPORT_PIN_P027_PFC_00_TCK_SWCLK       = (0x00U << IOPORT_PFC_OFFSET), ///< P02_7 / JTAG/SWD / TCK_SWCLK
    IOPORT_PIN_P027_PFC_01_TXD5_SDA5_MOSI5 = (0x01U << IOPORT_PFC_OFFSET), ///< P02_7 / SCIn / TXD5_SDA5_MOSI5
    IOPORT_PIN_P030_PFC_00_IRQ14           = (0x00U << IOPORT_PFC_OFFSET), ///< P03_0 / IRQ / IRQ14
    IOPORT_PIN_P030_PFC_01_TRACEDATA3      = (0x01U << IOPORT_PFC_OFFSET), ///< P03_0 / TRACE / TRACEDATA3
    IOPORT_PIN_P030_PFC_02_A14             = (0x02U << IOPORT_PFC_OFFSET), ///< P03_0 / BSC / A14
    IOPORT_PIN_P030_PFC_03_CS5             = (0x03U << IOPORT_PFC_OFFSET), ///< P03_0 / BSC / CS5
    IOPORT_PIN_P030_PFC_04_GTADSML1        = (0x04U << IOPORT_PFC_OFFSET), ///< P03_0 / GPT / GTADSML1
    IOPORT_PIN_P030_PFC_05_SCK2            = (0x05U << IOPORT_PFC_OFFSET), ///< P03_0 / SCIn / SCK2
    IOPORT_PIN_P030_PFC_06_SPI_SSL32       = (0x06U << IOPORT_PFC_OFFSET), ///< P03_0 / SPIn / SPI_SSL32
    IOPORT_PIN_P030_PFC_07_CANTXDP1        = (0x07U << IOPORT_PFC_OFFSET), ///< P03_0 / CANFDn / CANTXDP1
    IOPORT_PIN_P030_PFC_09_HA14            = (0x09U << IOPORT_PFC_OFFSET), ///< P03_0 / PHOSTIF / HA14
    IOPORT_PIN_P035_PFC_00_IRQ5            = (0x00U << IOPORT_PFC_OFFSET), ///< P03_5 / IRQ / IRQ5
    IOPORT_PIN_P035_PFC_01_ETH2_CRS        = (0x01U << IOPORT_PFC_OFFSET), ///< P03_5 / ETHER_ETHn / ETH2_CRS
    IOPORT_PIN_P035_PFC_02_A12             = (0x02U << IOPORT_PFC_OFFSET), ///< P03_5 / BSC / A12
    IOPORT_PIN_P035_PFC_03_MTIOC3A         = (0x03U << IOPORT_PFC_OFFSET), ///< P03_5 / MTU3n / MTIOC3A
    IOPORT_PIN_P035_PFC_04_GTIOC4A         = (0x04U << IOPORT_PFC_OFFSET), ///< P03_5 / GPTn / GTIOC4A
    IOPORT_PIN_P035_PFC_05_RXD2_SCL2_MISO2 = (0x05U << IOPORT_PFC_OFFSET), ///< P03_5 / SCIn / RXD2_SCL2_MISO2
    IOPORT_PIN_P035_PFC_06_MCLK2           = (0x06U << IOPORT_PFC_OFFSET), ///< P03_5 / DSMIFn / MCLK2
    IOPORT_PIN_P035_PFC_07_HA12            = (0x07U << IOPORT_PFC_OFFSET), ///< P03_5 / PHOSTIF / HA12
    IOPORT_PIN_P036_PFC_00_IRQ8            = (0x00U << IOPORT_PFC_OFFSET), ///< P03_6 / IRQ / IRQ8
    IOPORT_PIN_P036_PFC_01_ETH2_COL        = (0x01U << IOPORT_PFC_OFFSET), ///< P03_6 / ETHER_ETHn / ETH2_COL
    IOPORT_PIN_P036_PFC_02_TRACEDATA4      = (0x02U << IOPORT_PFC_OFFSET), ///< P03_6 / TRACE / TRACEDATA4
    IOPORT_PIN_P036_PFC_03_A11             = (0x03U << IOPORT_PFC_OFFSET), ///< P03_6 / BSC / A11
    IOPORT_PIN_P036_PFC_04_MTIOC3B         = (0x04U << IOPORT_PFC_OFFSET), ///< P03_6 / MTU3n / MTIOC3B
    IOPORT_PIN_P036_PFC_05_GTIOC4B         = (0x05U << IOPORT_PFC_OFFSET), ///< P03_6 / GPTn / GTIOC4B
    IOPORT_PIN_P036_PFC_06_TXD2_SDA2_MOSI2 = (0x06U << IOPORT_PFC_OFFSET), ///< P03_6 / SCIn / TXD2_SDA2_MOSI2
    IOPORT_PIN_P036_PFC_07_SPI_SSL13       = (0x07U << IOPORT_PFC_OFFSET), ///< P03_6 / SPIn / SPI_SSL13
    IOPORT_PIN_P036_PFC_08_MDAT2           = (0x08U << IOPORT_PFC_OFFSET), ///< P03_6 / DSMIFn / MDAT2
    IOPORT_PIN_P036_PFC_09_HA11            = (0x09U << IOPORT_PFC_OFFSET), ///< P03_6 / PHOSTIF / HA11
    IOPORT_PIN_P037_PFC_00_IRQ9            = (0x00U << IOPORT_PFC_OFFSET), ///< P03_7 / IRQ / IRQ9
    IOPORT_PIN_P037_PFC_01_ETH2_TXER       = (0x01U << IOPORT_PFC_OFFSET), ///< P03_7 / ETHER_ETHn / ETH2_TXER
    IOPORT_PIN_P037_PFC_02_TRACEDATA5      = (0x02U << IOPORT_PFC_OFFSET), ///< P03_7 / TRACE / TRACEDATA5
    IOPORT_PIN_P037_PFC_03_A10             = (0x03U << IOPORT_PFC_OFFSET), ///< P03_7 / BSC / A10
    IOPORT_PIN_P037_PFC_04_MTIOC3C         = (0x04U << IOPORT_PFC_OFFSET), ///< P03_7 / MTU3n / MTIOC3C
    IOPORT_PIN_P037_PFC_05_GTIOC5A         = (0x05U << IOPORT_PFC_OFFSET), ///< P03_7 / GPTn / GTIOC5A
    IOPORT_PIN_P037_PFC_06_SCK3            = (0x06U << IOPORT_PFC_OFFSET), ///< P03_7 / SCIn / SCK3
    IOPORT_PIN_P037_PFC_07_HA10            = (0x07U << IOPORT_PFC_OFFSET), ///< P03_7 / PHOSTIF / HA10
    IOPORT_PIN_P040_PFC_00_TRACEDATA6      = (0x00U << IOPORT_PFC_OFFSET), ///< P04_0 / TRACE / TRACEDATA6
    IOPORT_PIN_P040_PFC_01_A9              = (0x01U << IOPORT_PFC_OFFSET), ///< P04_0 / BSC / A9
    IOPORT_PIN_P040_PFC_02_MTIOC3D         = (0x02U << IOPORT_PFC_OFFSET), ///< P04_0 / MTU3n / MTIOC3D
    IOPORT_PIN_P040_PFC_03_GTIOC5B         = (0x03U << IOPORT_PFC_OFFSET), ///< P04_0 / GPTn / GTIOC5B
    IOPORT_PIN_P040_PFC_04_RXD3_SCL3_MISO3 = (0x04U << IOPORT_PFC_OFFSET), ///< P04_0 / SCIn / RXD3_SCL3_MISO3
    IOPORT_PIN_P040_PFC_05_HA9             = (0x05U << IOPORT_PFC_OFFSET), ///< P04_0 / PHOSTIF / HA9
    IOPORT_PIN_P041_PFC_00_CKIO            = (0x00U << IOPORT_PFC_OFFSET), ///< P04_1 / BSC / CKIO
    IOPORT_PIN_P041_PFC_01_TXD3_SDA3_MOSI3 = (0x01U << IOPORT_PFC_OFFSET), ///< P04_1 / SCIn / TXD3_SDA3_MOSI3
    IOPORT_PIN_P041_PFC_02_SPI_MOSI0       = (0x02U << IOPORT_PFC_OFFSET), ///< P04_1 / SPIn / SPI_MOSI0
    IOPORT_PIN_P041_PFC_03_IIC_SDA2        = (0x03U << IOPORT_PFC_OFFSET), ///< P04_1 / IICn / IIC_SDA2
    IOPORT_PIN_P041_PFC_04_HCKIO           = (0x04U << IOPORT_PFC_OFFSET), ///< P04_1 / PHOSTIF / HCKIO
    IOPORT_PIN_P044_PFC_00_IRQ10           = (0x00U << IOPORT_PFC_OFFSET), ///< P04_4 / IRQ / IRQ10
    IOPORT_PIN_P044_PFC_01_TRACEDATA7      = (0x01U << IOPORT_PFC_OFFSET), ///< P04_4 / TRACE / TRACEDATA7
    IOPORT_PIN_P044_PFC_02_A8              = (0x02U << IOPORT_PFC_OFFSET), ///< P04_4 / BSC / A8
    IOPORT_PIN_P044_PFC_03_GTADSMP0        = (0x03U << IOPORT_PFC_OFFSET), ///< P04_4 / GPT / GTADSMP0
    IOPORT_PIN_P044_PFC_04_POE10           = (0x04U << IOPORT_PFC_OFFSET), ///< P04_4 / MTU_POE3 / POE10
    IOPORT_PIN_P044_PFC_05_CTS3            = (0x05U << IOPORT_PFC_OFFSET), ///< P04_4 / SCIn / CTS3
    IOPORT_PIN_P044_PFC_06_SPI_RSPCK1      = (0x06U << IOPORT_PFC_OFFSET), ///< P04_4 / SPIn / SPI_RSPCK1
    IOPORT_PIN_P044_PFC_08_HA8             = (0x08U << IOPORT_PFC_OFFSET), ///< P04_4 / PHOSTIF / HA8
    IOPORT_PIN_P045_PFC_00_A7              = (0x00U << IOPORT_PFC_OFFSET), ///< P04_5 / BSC / A7
    IOPORT_PIN_P045_PFC_01_DE3             = (0x01U << IOPORT_PFC_OFFSET), ///< P04_5 / SCIn / DE3
    IOPORT_PIN_P045_PFC_02_ETHSW_PTPOUT0   = (0x02U << IOPORT_PFC_OFFSET), ///< P04_5 / ETHER_ETHSW / ETHSW_PTPOUT0
    IOPORT_PIN_P045_PFC_03_ESC_SYNC0       = (0x03U << IOPORT_PFC_OFFSET), ///< P04_5 / ETHER_ESC / ESC_SYNC0
    IOPORT_PIN_P045_PFC_04_ESC_SYNC1       = (0x04U << IOPORT_PFC_OFFSET), ///< P04_5 / ETHER_ESC / ESC_SYNC1
    IOPORT_PIN_P045_PFC_05_HA7             = (0x05U << IOPORT_PFC_OFFSET), ///< P04_5 / PHOSTIF / HA7
    IOPORT_PIN_P046_PFC_00_ETH1_TXER       = (0x00U << IOPORT_PFC_OFFSET), ///< P04_6 / ETHER_ETHn / ETH1_TXER
    IOPORT_PIN_P046_PFC_01_A6              = (0x01U << IOPORT_PFC_OFFSET), ///< P04_6 / BSC / A6
    IOPORT_PIN_P046_PFC_02_DACK            = (0x02U << IOPORT_PFC_OFFSET), ///< P04_6 / DMAC / DACK
    IOPORT_PIN_P046_PFC_03_RTCAT1HZ        = (0x03U << IOPORT_PFC_OFFSET), ///< P04_6 / RTC / RTCAT1HZ
    IOPORT_PIN_P046_PFC_04_HA6             = (0x04U << IOPORT_PFC_OFFSET), ///< P04_6 / PHOSTIF / HA6
    IOPORT_PIN_P047_PFC_00_ETH0_TXER       = (0x00U << IOPORT_PFC_OFFSET), ///< P04_7 / ETHER_ETHn / ETH0_TXER
    IOPORT_PIN_P047_PFC_01_A5              = (0x01U << IOPORT_PFC_OFFSET), ///< P04_7 / BSC / A5
    IOPORT_PIN_P047_PFC_02_SPI_SSL21       = (0x02U << IOPORT_PFC_OFFSET), ///< P04_7 / SPIn / SPI_SSL21
    IOPORT_PIN_P047_PFC_03_ETH2_TXER       = (0x03U << IOPORT_PFC_OFFSET), ///< P04_7 / ETHER_ETHn / ETH2_TXER
    IOPORT_PIN_P047_PFC_04_HA5             = (0x04U << IOPORT_PFC_OFFSET), ///< P04_7 / PHOSTIF / HA5
    IOPORT_PIN_P050_PFC_00_IRQ12           = (0x00U << IOPORT_PFC_OFFSET), ///< P05_0 / IRQ / IRQ12
    IOPORT_PIN_P050_PFC_01_ETH1_CRS        = (0x01U << IOPORT_PFC_OFFSET), ///< P05_0 / ETHER_ETHn / ETH1_CRS
    IOPORT_PIN_P050_PFC_02_A4              = (0x02U << IOPORT_PFC_OFFSET), ///< P05_0 / BSC / A4
    IOPORT_PIN_P050_PFC_03_MTIOC4A         = (0x03U << IOPORT_PFC_OFFSET), ///< P05_0 / MTU3n / MTIOC4A
    IOPORT_PIN_P050_PFC_04_GTIOC6A         = (0x04U << IOPORT_PFC_OFFSET), ///< P05_0 / GPTn / GTIOC6A
    IOPORT_PIN_P050_PFC_05_CMTW0_TOC0      = (0x05U << IOPORT_PFC_OFFSET), ///< P05_0 / CMTWn / CMTW0_TOC0
    IOPORT_PIN_P050_PFC_06_SS5_CTS5_RTS5   = (0x06U << IOPORT_PFC_OFFSET), ///< P05_0 / SCIn / SS5_CTS5_RTS5
    IOPORT_PIN_P050_PFC_07_CANTXDP0        = (0x07U << IOPORT_PFC_OFFSET), ///< P05_0 / CANFDn / CANTXDP0
    IOPORT_PIN_P050_PFC_08_USB_VBUSEN      = (0x08U << IOPORT_PFC_OFFSET), ///< P05_0 / USB_HS / USB_VBUSEN
    IOPORT_PIN_P050_PFC_09_MCLK3           = (0x09U << IOPORT_PFC_OFFSET), ///< P05_0 / DSMIFn / MCLK3
    IOPORT_PIN_P050_PFC_0B_HA4             = (0x0BU << IOPORT_PFC_OFFSET), ///< P05_0 / PHOSTIF / HA4
    IOPORT_PIN_P051_PFC_00_IRQ13           = (0x00U << IOPORT_PFC_OFFSET), ///< P05_1 / IRQ / IRQ13
    IOPORT_PIN_P051_PFC_01_ETH1_COL        = (0x01U << IOPORT_PFC_OFFSET), ///< P05_1 / ETHER_ETHn / ETH1_COL
    IOPORT_PIN_P051_PFC_02_A3              = (0x02U << IOPORT_PFC_OFFSET), ///< P05_1 / BSC / A3
    IOPORT_PIN_P051_PFC_03_MTIOC4B         = (0x03U << IOPORT_PFC_OFFSET), ///< P05_1 / MTU3n / MTIOC4B
    IOPORT_PIN_P051_PFC_04_GTIOC6B         = (0x04U << IOPORT_PFC_OFFSET), ///< P05_1 / GPTn / GTIOC6B
    IOPORT_PIN_P051_PFC_05_CMTW0_TIC1      = (0x05U << IOPORT_PFC_OFFSET), ///< P05_1 / CMTWn / CMTW0_TIC1
    IOPORT_PIN_P051_PFC_06_CTS5            = (0x06U << IOPORT_PFC_OFFSET), ///< P05_1 / SCIn / CTS5
    IOPORT_PIN_P051_PFC_07_CANRXDP0        = (0x07U << IOPORT_PFC_OFFSET), ///< P05_1 / CANFDn / CANRXDP0
    IOPORT_PIN_P051_PFC_08_USB_EXICEN      = (0x08U << IOPORT_PFC_OFFSET), ///< P05_1 / USB_HS / USB_EXICEN
    IOPORT_PIN_P051_PFC_09_MDAT3           = (0x09U << IOPORT_PFC_OFFSET), ///< P05_1 / DSMIFn / MDAT3
    IOPORT_PIN_P051_PFC_0B_HA3             = (0x0BU << IOPORT_PFC_OFFSET), ///< P05_1 / PHOSTIF / HA3
    IOPORT_PIN_P052_PFC_00_IRQ14           = (0x00U << IOPORT_PFC_OFFSET), ///< P05_2 / IRQ / IRQ14
    IOPORT_PIN_P052_PFC_01_ETH0_CRS        = (0x01U << IOPORT_PFC_OFFSET), ///< P05_2 / ETHER_ETHn / ETH0_CRS
    IOPORT_PIN_P052_PFC_02_A2              = (0x02U << IOPORT_PFC_OFFSET), ///< P05_2 / BSC / A2
    IOPORT_PIN_P052_PFC_03_MTIOC4C         = (0x03U << IOPORT_PFC_OFFSET), ///< P05_2 / MTU3n / MTIOC4C
    IOPORT_PIN_P052_PFC_04_GTETRGSA        = (0x04U << IOPORT_PFC_OFFSET), ///< P05_2 / GPT_POEG / GTETRGSA
    IOPORT_PIN_P052_PFC_05_GTIOC7A         = (0x05U << IOPORT_PFC_OFFSET), ///< P05_2 / GPTn / GTIOC7A
    IOPORT_PIN_P052_PFC_06_CMTW0_TOC0      = (0x06U << IOPORT_PFC_OFFSET), ///< P05_2 / CMTWn / CMTW0_TOC0
    IOPORT_PIN_P052_PFC_07_DE5             = (0x07U << IOPORT_PFC_OFFSET), ///< P05_2 / SCIn / DE5
    IOPORT_PIN_P052_PFC_08_IIC_SCL1        = (0x08U << IOPORT_PFC_OFFSET), ///< P05_2 / IICn / IIC_SCL1
    IOPORT_PIN_P052_PFC_09_CANRX0          = (0x09U << IOPORT_PFC_OFFSET), ///< P05_2 / CANFDn / CANRX0
    IOPORT_PIN_P052_PFC_0A_DREQ            = (0x0AU << IOPORT_PFC_OFFSET), ///< P05_2 / DMAC / DREQ
    IOPORT_PIN_P052_PFC_0B_USB_VBUSEN      = (0x0BU << IOPORT_PFC_OFFSET), ///< P05_2 / USB_HS / USB_VBUSEN
    IOPORT_PIN_P052_PFC_0D_HA2             = (0x0DU << IOPORT_PFC_OFFSET), ///< P05_2 / PHOSTIF / HA2
    IOPORT_PIN_P053_PFC_00_IRQ15           = (0x00U << IOPORT_PFC_OFFSET), ///< P05_3 / IRQ / IRQ15
    IOPORT_PIN_P053_PFC_01_ETH0_COL        = (0x01U << IOPORT_PFC_OFFSET), ///< P05_3 / ETHER_ETHn / ETH0_COL
    IOPORT_PIN_P053_PFC_02_A1              = (0x02U << IOPORT_PFC_OFFSET), ///< P05_3 / BSC / A1
    IOPORT_PIN_P053_PFC_03_MTIOC4D         = (0x03U << IOPORT_PFC_OFFSET), ///< P05_3 / MTU3n / MTIOC4D
    IOPORT_PIN_P053_PFC_04_GTETRGSB        = (0x04U << IOPORT_PFC_OFFSET), ///< P05_3 / GPT_POEG / GTETRGSB
    IOPORT_PIN_P053_PFC_05_GTIOC7B         = (0x05U << IOPORT_PFC_OFFSET), ///< P05_3 / GPTn / GTIOC7B
    IOPORT_PIN_P053_PFC_06_POE11           = (0x06U << IOPORT_PFC_OFFSET), ///< P05_3 / MTU_POE3 / POE11
    IOPORT_PIN_P053_PFC_07_CMTW0_TIC0      = (0x07U << IOPORT_PFC_OFFSET), ///< P05_3 / CMTWn / CMTW0_TIC0
    IOPORT_PIN_P053_PFC_08_SCK4            = (0x08U << IOPORT_PFC_OFFSET), ///< P05_3 / SCIn / SCK4
    IOPORT_PIN_P053_PFC_09_IIC_SDA1        = (0x09U << IOPORT_PFC_OFFSET), ///< P05_3 / IICn / IIC_SDA1
    IOPORT_PIN_P053_PFC_0A_CANTX0          = (0x0AU << IOPORT_PFC_OFFSET), ///< P05_3 / CANFDn / CANTX0
    IOPORT_PIN_P053_PFC_0B_USB_EXICEN      = (0x0BU << IOPORT_PFC_OFFSET), ///< P05_3 / USB_HS / USB_EXICEN
    IOPORT_PIN_P053_PFC_0D_HA1             = (0x0DU << IOPORT_PFC_OFFSET), ///< P05_3 / PHOSTIF / HA1
    IOPORT_PIN_P054_PFC_00_IRQ12           = (0x00U << IOPORT_PFC_OFFSET), ///< P05_4 / IRQ / IRQ12
    IOPORT_PIN_P054_PFC_01_ETHSW_LPI0      = (0x01U << IOPORT_PFC_OFFSET), ///< P05_4 / ETHER_ETHSW / ETHSW_LPI0
    IOPORT_PIN_P054_PFC_02_A0              = (0x02U << IOPORT_PFC_OFFSET), ///< P05_4 / BSC / A0
    IOPORT_PIN_P054_PFC_03_GTIOC14A        = (0x03U << IOPORT_PFC_OFFSET), ///< P05_4 / GPTn / GTIOC14A
    IOPORT_PIN_P054_PFC_04_RXD4_SCL4_MISO4 = (0x04U << IOPORT_PFC_OFFSET), ///< P05_4 / SCIn / RXD4_SCL4_MISO4
    IOPORT_PIN_P054_PFC_05_SPI_SSL00       = (0x05U << IOPORT_PFC_OFFSET), ///< P05_4 / SPIn / SPI_SSL00
    IOPORT_PIN_P054_PFC_06_CANTXDP0        = (0x06U << IOPORT_PFC_OFFSET), ///< P05_4 / CANFDn / CANTXDP0
    IOPORT_PIN_P054_PFC_07_DACK            = (0x07U << IOPORT_PFC_OFFSET), ///< P05_4 / DMAC / DACK
    IOPORT_PIN_P054_PFC_08_USB_OVRCUR      = (0x08U << IOPORT_PFC_OFFSET), ///< P05_4 / USB_HS / USB_OVRCUR
    IOPORT_PIN_P054_PFC_0A_HA0             = (0x0AU << IOPORT_PFC_OFFSET), ///< P05_4 / PHOSTIF / HA0
    IOPORT_PIN_P055_PFC_00_ETHSW_PHYLINK1  = (0x00U << IOPORT_PFC_OFFSET), ///< P05_5 / ETHER_ETHSW / ETHSW_PHYLINK1
    IOPORT_PIN_P055_PFC_02_ESC_PHYLINK1    = (0x02U << IOPORT_PFC_OFFSET), ///< P05_5 / ETHER_ESC / ESC_PHYLINK1
    IOPORT_PIN_P055_PFC_03_GTIOC14B        = (0x03U << IOPORT_PFC_OFFSET), ///< P05_5 / GPTn / GTIOC14B
    IOPORT_PIN_P055_PFC_04_CMTW0_TOC1      = (0x04U << IOPORT_PFC_OFFSET), ///< P05_5 / CMTWn / CMTW0_TOC1
    IOPORT_PIN_P055_PFC_05_SPI_RSPCK2      = (0x05U << IOPORT_PFC_OFFSET), ///< P05_5 / SPIn / SPI_RSPCK2
    IOPORT_PIN_P056_PFC_00_IRQ12           = (0x00U << IOPORT_PFC_OFFSET), ///< P05_6 / IRQ / IRQ12
    IOPORT_PIN_P056_PFC_01_ETH1_RXER       = (0x01U << IOPORT_PFC_OFFSET), ///< P05_6 / ETHER_ETHn / ETH1_RXER
    IOPORT_PIN_P056_PFC_03_GTIOC15A        = (0x03U << IOPORT_PFC_OFFSET), ///< P05_6 / GPTn / GTIOC15A
    IOPORT_PIN_P056_PFC_04_CMTW1_TIC0      = (0x04U << IOPORT_PFC_OFFSET), ///< P05_6 / CMTWn / CMTW1_TIC0
    IOPORT_PIN_P056_PFC_05_SPI_SSL22       = (0x05U << IOPORT_PFC_OFFSET), ///< P05_6 / SPIn / SPI_SSL22
    IOPORT_PIN_P057_PFC_00_ETH1_TXD2       = (0x00U << IOPORT_PFC_OFFSET), ///< P05_7 / ETHER_ETHn / ETH1_TXD2
    IOPORT_PIN_P057_PFC_02_GTIOC15B        = (0x02U << IOPORT_PFC_OFFSET), ///< P05_7 / GPTn / GTIOC15B
    IOPORT_PIN_P057_PFC_03_CMTW1_TOC1      = (0x03U << IOPORT_PFC_OFFSET), ///< P05_7 / CMTWn / CMTW1_TOC1
    IOPORT_PIN_P057_PFC_04_TXD4_SDA4_MOSI4 = (0x04U << IOPORT_PFC_OFFSET), ///< P05_7 / SCIn / TXD4_SDA4_MOSI4
    IOPORT_PIN_P057_PFC_05_SPI_SSL23       = (0x05U << IOPORT_PFC_OFFSET), ///< P05_7 / SPIn / SPI_SSL23
    IOPORT_PIN_P060_PFC_00_ETH1_TXD3       = (0x00U << IOPORT_PFC_OFFSET), ///< P06_0 / ETHER_ETHn / ETH1_TXD3
    IOPORT_PIN_P060_PFC_02_GTIOC16A        = (0x02U << IOPORT_PFC_OFFSET), ///< P06_0 / GPTn / GTIOC16A
    IOPORT_PIN_P060_PFC_03_CMTW1_TOC0      = (0x03U << IOPORT_PFC_OFFSET), ///< P06_0 / CMTWn / CMTW1_TOC0
    IOPORT_PIN_P060_PFC_04_SS4_CTS4_RTS4   = (0x04U << IOPORT_PFC_OFFSET), ///< P06_0 / SCIn / SS4_CTS4_RTS4
    IOPORT_PIN_P060_PFC_05_SPI_SSL23       = (0x05U << IOPORT_PFC_OFFSET), ///< P06_0 / SPIn / SPI_SSL23
    IOPORT_PIN_P060_PFC_06_CANRX1          = (0x06U << IOPORT_PFC_OFFSET), ///< P06_0 / CANFDn / CANRX1
    IOPORT_PIN_P061_PFC_00_ETH1_REFCLK     = (0x00U << IOPORT_PFC_OFFSET), ///< P06_1 / ETHER_ETHn / ETH1_REFCLK
    IOPORT_PIN_P061_PFC_01_RMII1_REFCLK    = (0x01U << IOPORT_PFC_OFFSET), ///< P06_1 / ETHER_ETHn / RMII1_REFCLK
    IOPORT_PIN_P061_PFC_03_GTIOC16B        = (0x03U << IOPORT_PFC_OFFSET), ///< P06_1 / GPTn / GTIOC16B
    IOPORT_PIN_P061_PFC_04_CTS4            = (0x04U << IOPORT_PFC_OFFSET), ///< P06_1 / SCIn / CTS4
    IOPORT_PIN_P061_PFC_05_SPI_SSL22       = (0x05U << IOPORT_PFC_OFFSET), ///< P06_1 / SPIn / SPI_SSL22
    IOPORT_PIN_P061_PFC_06_CANTX1          = (0x06U << IOPORT_PFC_OFFSET), ///< P06_1 / CANFDn / CANTX1
    IOPORT_PIN_P062_PFC_00_ETH1_TXD1       = (0x00U << IOPORT_PFC_OFFSET), ///< P06_2 / ETHER_ETHn / ETH1_TXD1
    IOPORT_PIN_P062_PFC_02_GTIOC17A        = (0x02U << IOPORT_PFC_OFFSET), ///< P06_2 / GPTn / GTIOC17A
    IOPORT_PIN_P062_PFC_03_CANRXDP1        = (0x03U << IOPORT_PFC_OFFSET), ///< P06_2 / CANFDn / CANRXDP1
    IOPORT_PIN_P063_PFC_00_ETH1_TXD0       = (0x00U << IOPORT_PFC_OFFSET), ///< P06_3 / ETHER_ETHn / ETH1_TXD0
    IOPORT_PIN_P063_PFC_02_GTIOC17B        = (0x02U << IOPORT_PFC_OFFSET), ///< P06_3 / GPTn / GTIOC17B
    IOPORT_PIN_P063_PFC_03_CMTW1_TIC1      = (0x03U << IOPORT_PFC_OFFSET), ///< P06_3 / CMTWn / CMTW1_TIC1
    IOPORT_PIN_P063_PFC_04_DE4             = (0x04U << IOPORT_PFC_OFFSET), ///< P06_3 / SCIn / DE4
    IOPORT_PIN_P063_PFC_05_SPI_MISO1       = (0x05U << IOPORT_PFC_OFFSET), ///< P06_3 / SPIn / SPI_MISO1
    IOPORT_PIN_P063_PFC_06_CANTXDP1        = (0x06U << IOPORT_PFC_OFFSET), ///< P06_3 / CANFDn / CANTXDP1
    IOPORT_PIN_P064_PFC_00_ETH1_TXCLK      = (0x00U << IOPORT_PFC_OFFSET), ///< P06_4 / ETHER_ETHn / ETH1_TXCLK
    IOPORT_PIN_P064_PFC_02_GTIOC11A        = (0x02U << IOPORT_PFC_OFFSET), ///< P06_4 / GPTn / GTIOC11A
    IOPORT_PIN_P064_PFC_03_SPI_MOSI1       = (0x03U << IOPORT_PFC_OFFSET), ///< P06_4 / SPIn / SPI_MOSI1
    IOPORT_PIN_P065_PFC_00_ETH1_TXEN       = (0x00U << IOPORT_PFC_OFFSET), ///< P06_5 / ETHER_ETHn / ETH1_TXEN
    IOPORT_PIN_P065_PFC_02_GTIOC11B        = (0x02U << IOPORT_PFC_OFFSET), ///< P06_5 / GPTn / GTIOC11B
    IOPORT_PIN_P066_PFC_00_ETH1_RXD0       = (0x00U << IOPORT_PFC_OFFSET), ///< P06_6 / ETHER_ETHn / ETH1_RXD0
    IOPORT_PIN_P066_PFC_02_GTIOC12A        = (0x02U << IOPORT_PFC_OFFSET), ///< P06_6 / GPTn / GTIOC12A
    IOPORT_PIN_P066_PFC_03_SPI_SSL10       = (0x03U << IOPORT_PFC_OFFSET), ///< P06_6 / SPIn / SPI_SSL10
    IOPORT_PIN_P067_PFC_00_ETH1_RXD1       = (0x00U << IOPORT_PFC_OFFSET), ///< P06_7 / ETHER_ETHn / ETH1_RXD1
    IOPORT_PIN_P067_PFC_02_GTIOC12B        = (0x02U << IOPORT_PFC_OFFSET), ///< P06_7 / GPTn / GTIOC12B
    IOPORT_PIN_P067_PFC_03_SPI_SSL11       = (0x03U << IOPORT_PFC_OFFSET), ///< P06_7 / SPIn / SPI_SSL11
    IOPORT_PIN_P070_PFC_00_ETH1_RXD2       = (0x00U << IOPORT_PFC_OFFSET), ///< P07_0 / ETHER_ETHn / ETH1_RXD2
    IOPORT_PIN_P070_PFC_02_GTIOC13A        = (0x02U << IOPORT_PFC_OFFSET), ///< P07_0 / GPTn / GTIOC13A
    IOPORT_PIN_P071_PFC_00_ETH1_RXD3       = (0x00U << IOPORT_PFC_OFFSET), ///< P07_1 / ETHER_ETHn / ETH1_RXD3
    IOPORT_PIN_P071_PFC_02_GTIOC13B        = (0x02U << IOPORT_PFC_OFFSET), ///< P07_1 / GPTn / GTIOC13B
    IOPORT_PIN_P072_PFC_00_ETH1_RXDV       = (0x00U << IOPORT_PFC_OFFSET), ///< P07_2 / ETHER_ETHn / ETH1_RXDV
    IOPORT_PIN_P073_PFC_00_ETH1_RXCLK      = (0x00U << IOPORT_PFC_OFFSET), ///< P07_3 / ETHER_ETHn / ETH1_RXCLK
    IOPORT_PIN_P074_PFC_00_IRQ1            = (0x00U << IOPORT_PFC_OFFSET), ///< P07_4 / IRQ / IRQ1
    IOPORT_PIN_P074_PFC_01_ADTRG0          = (0x01U << IOPORT_PFC_OFFSET), ///< P07_4 / ADCn / ADTRG0
    IOPORT_PIN_P074_PFC_02_USB_VBUSIN      = (0x02U << IOPORT_PFC_OFFSET), ///< P07_4 / USB_HS / USB_VBUSIN
    IOPORT_PIN_P084_PFC_00_ETH0_RXD3       = (0x00U << IOPORT_PFC_OFFSET), ///< P08_4 / ETHER_ETHn / ETH0_RXD3
    IOPORT_PIN_P084_PFC_02_MTIOC6A         = (0x02U << IOPORT_PFC_OFFSET), ///< P08_4 / MTU3n / MTIOC6A
    IOPORT_PIN_P085_PFC_00_ETH0_RXDV       = (0x00U << IOPORT_PFC_OFFSET), ///< P08_5 / ETHER_ETHn / ETH0_RXDV
    IOPORT_PIN_P085_PFC_01_MTIOC6B         = (0x01U << IOPORT_PFC_OFFSET), ///< P08_5 / MTU3n / MTIOC6B
    IOPORT_PIN_P086_PFC_00_ETH0_RXCLK      = (0x00U << IOPORT_PFC_OFFSET), ///< P08_6 / ETHER_ETHn / ETH0_RXCLK
    IOPORT_PIN_P086_PFC_01_MTIOC6C         = (0x01U << IOPORT_PFC_OFFSET), ///< P08_6 / MTU3n / MTIOC6C
    IOPORT_PIN_P087_PFC_00_GMAC_MDC        = (0x00U << IOPORT_PFC_OFFSET), ///< P08_7 / ETHER_GMAC / GMAC_MDC
    IOPORT_PIN_P087_PFC_01_ETHSW_MDC       = (0x01U << IOPORT_PFC_OFFSET), ///< P08_7 / ETHER_ETHSW / ETHSW_MDC
    IOPORT_PIN_P087_PFC_03_ESC_MDC         = (0x03U << IOPORT_PFC_OFFSET), ///< P08_7 / ETHER_ESC / ESC_MDC
    IOPORT_PIN_P087_PFC_04_MTIOC6D         = (0x04U << IOPORT_PFC_OFFSET), ///< P08_7 / MTU3n / MTIOC6D
    IOPORT_PIN_P090_PFC_00_GMAC_MDIO       = (0x00U << IOPORT_PFC_OFFSET), ///< P09_0 / ETHER_GMAC / GMAC_MDIO
    IOPORT_PIN_P090_PFC_01_ETHSW_MDIO      = (0x01U << IOPORT_PFC_OFFSET), ///< P09_0 / ETHER_ETHSW / ETHSW_MDIO
    IOPORT_PIN_P090_PFC_03_ESC_MDIO        = (0x03U << IOPORT_PFC_OFFSET), ///< P09_0 / ETHER_ESC / ESC_MDIO
    IOPORT_PIN_P090_PFC_04_MTIOC7A         = (0x04U << IOPORT_PFC_OFFSET), ///< P09_0 / MTU3n / MTIOC7A
    IOPORT_PIN_P091_PFC_00_ETH0_REFCLK     = (0x00U << IOPORT_PFC_OFFSET), ///< P09_1 / ETHER_ETHn / ETH0_REFCLK
    IOPORT_PIN_P091_PFC_01_RMII0_REFCLK    = (0x01U << IOPORT_PFC_OFFSET), ///< P09_1 / ETHER_ETHn / RMII0_REFCLK
    IOPORT_PIN_P091_PFC_02_MTIOC7B         = (0x02U << IOPORT_PFC_OFFSET), ///< P09_1 / MTU3n / MTIOC7B
    IOPORT_PIN_P092_PFC_00_IRQ0            = (0x00U << IOPORT_PFC_OFFSET), ///< P09_2 / IRQ / IRQ0
    IOPORT_PIN_P092_PFC_01_ETH0_RXER       = (0x01U << IOPORT_PFC_OFFSET), ///< P09_2 / ETHER_ETHn / ETH0_RXER
    IOPORT_PIN_P092_PFC_03_MTIOC7C         = (0x03U << IOPORT_PFC_OFFSET), ///< P09_2 / MTU3n / MTIOC7C
    IOPORT_PIN_P093_PFC_00_ETH0_TXD3       = (0x00U << IOPORT_PFC_OFFSET), ///< P09_3 / ETHER_ETHn / ETH0_TXD3
    IOPORT_PIN_P093_PFC_01_MTIOC7D         = (0x01U << IOPORT_PFC_OFFSET), ///< P09_3 / MTU3n / MTIOC7D
    IOPORT_PIN_P094_PFC_00_ETH0_TXD2       = (0x00U << IOPORT_PFC_OFFSET), ///< P09_4 / ETHER_ETHn / ETH0_TXD2
    IOPORT_PIN_P095_PFC_00_ETH0_TXD1       = (0x00U << IOPORT_PFC_OFFSET), ///< P09_5 / ETHER_ETHn / ETH0_TXD1
    IOPORT_PIN_P096_PFC_00_ETH0_TXD0       = (0x00U << IOPORT_PFC_OFFSET), ///< P09_6 / ETHER_ETHn / ETH0_TXD0
    IOPORT_PIN_P097_PFC_00_ETH0_TXCLK      = (0x00U << IOPORT_PFC_OFFSET), ///< P09_7 / ETHER_ETHn / ETH0_TXCLK
    IOPORT_PIN_P100_PFC_00_ETH0_TXEN       = (0x00U << IOPORT_PFC_OFFSET), ///< P10_0 / ETHER_ETHn / ETH0_TXEN
    IOPORT_PIN_P101_PFC_00_ETH0_RXD0       = (0x00U << IOPORT_PFC_OFFSET), ///< P10_1 / ETHER_ETHn / ETH0_RXD0
    IOPORT_PIN_P102_PFC_00_ETH0_RXD1       = (0x00U << IOPORT_PFC_OFFSET), ///< P10_2 / ETHER_ETHn / ETH0_RXD1
    IOPORT_PIN_P103_PFC_00_ETH0_RXD2       = (0x00U << IOPORT_PFC_OFFSET), ///< P10_3 / ETHER_ETHn / ETH0_RXD2
    IOPORT_PIN_P103_PFC_01_RTCAT1HZ        = (0x01U << IOPORT_PFC_OFFSET), ///< P10_3 / RTC / RTCAT1HZ
    IOPORT_PIN_P104_PFC_00_IRQ11           = (0x00U << IOPORT_PFC_OFFSET), ///< P10_4 / IRQ / IRQ11
    IOPORT_PIN_P104_PFC_01_ETHSW_PHYLINK0  = (0x01U << IOPORT_PFC_OFFSET), ///< P10_4 / ETHER_ETHSW / ETHSW_PHYLINK0
    IOPORT_PIN_P104_PFC_03_ESC_PHYLINK0    = (0x03U << IOPORT_PFC_OFFSET), ///< P10_4 / ETHER_ESC / ESC_PHYLINK0
    IOPORT_PIN_P124_PFC_01_ETH1_CRS        = (0x01U << IOPORT_PFC_OFFSET), ///< P12_4 / ETHER_ETHn / ETH1_CRS
    IOPORT_PIN_P124_PFC_02_TRACEDATA0      = (0x02U << IOPORT_PFC_OFFSET), ///< P12_4 / TRACE / TRACEDATA0
    IOPORT_PIN_P124_PFC_03_D15             = (0x03U << IOPORT_PFC_OFFSET), ///< P12_4 / BSC / D15
    IOPORT_PIN_P124_PFC_04_MTIOC8B         = (0x04U << IOPORT_PFC_OFFSET), ///< P12_4 / MTU3n / MTIOC8B
    IOPORT_PIN_P124_PFC_05_GTIOC8B         = (0x05U << IOPORT_PFC_OFFSET), ///< P12_4 / GPTn / GTIOC8B
    IOPORT_PIN_P124_PFC_06_SPI_SSL01       = (0x06U << IOPORT_PFC_OFFSET), ///< P12_4 / SPIn / SPI_SSL01
    IOPORT_PIN_P124_PFC_08_MBX_HINT        = (0x08U << IOPORT_PFC_OFFSET), ///< P12_4 / MBXSEM / MBX_HINT
    IOPORT_PIN_P132_PFC_00_IRQ5            = (0x00U << IOPORT_PFC_OFFSET), ///< P13_2 / IRQ / IRQ5
    IOPORT_PIN_P132_PFC_02_ETHSW_PTPOUT2   = (0x02U << IOPORT_PFC_OFFSET), ///< P13_2 / ETHER_ETHSW / ETHSW_PTPOUT2
    IOPORT_PIN_P132_PFC_03_TRACEDATA6      = (0x03U << IOPORT_PFC_OFFSET), ///< P13_2 / TRACE / TRACEDATA6
    IOPORT_PIN_P132_PFC_04_D9              = (0x04U << IOPORT_PFC_OFFSET), ///< P13_2 / BSC / D9
    IOPORT_PIN_P132_PFC_05_ESC_I2CCLK      = (0x05U << IOPORT_PFC_OFFSET), ///< P13_2 / ETHER_ESC / ESC_I2CCLK
    IOPORT_PIN_P132_PFC_06_MTIOC0A         = (0x06U << IOPORT_PFC_OFFSET), ///< P13_2 / MTU3n / MTIOC0A
    IOPORT_PIN_P132_PFC_07_GTIOC10A        = (0x07U << IOPORT_PFC_OFFSET), ///< P13_2 / GPTn / GTIOC10A
    IOPORT_PIN_P132_PFC_08_POE8            = (0x08U << IOPORT_PFC_OFFSET), ///< P13_2 / MTU_POE3 / POE8
    IOPORT_PIN_P132_PFC_09_SS1_CTS1_RTS1   = (0x09U << IOPORT_PFC_OFFSET), ///< P13_2 / SCIn / SS1_CTS1_RTS1
    IOPORT_PIN_P132_PFC_0A_SPI_MISO0       = (0x0AU << IOPORT_PFC_OFFSET), ///< P13_2 / SPIn / SPI_MISO0
    IOPORT_PIN_P132_PFC_0B_IIC_SCL0        = (0x0BU << IOPORT_PFC_OFFSET), ///< P13_2 / IICn / IIC_SCL0
    IOPORT_PIN_P132_PFC_0C_MCLK4           = (0x0CU << IOPORT_PFC_OFFSET), ///< P13_2 / DSMIFn / MCLK4
    IOPORT_PIN_P132_PFC_0E_A13             = (0x0EU << IOPORT_PFC_OFFSET), ///< P13_2 / BSC / A13
    IOPORT_PIN_P133_PFC_01_ETHSW_PTPOUT3   = (0x01U << IOPORT_PFC_OFFSET), ///< P13_3 / ETHER_ETHSW / ETHSW_PTPOUT3
    IOPORT_PIN_P133_PFC_02_TRACEDATA7      = (0x02U << IOPORT_PFC_OFFSET), ///< P13_3 / TRACE / TRACEDATA7
    IOPORT_PIN_P133_PFC_03_D8              = (0x03U << IOPORT_PFC_OFFSET), ///< P13_3 / BSC / D8
    IOPORT_PIN_P133_PFC_04_ESC_I2CDATA     = (0x04U << IOPORT_PFC_OFFSET), ///< P13_3 / ETHER_ESC / ESC_I2CDATA
    IOPORT_PIN_P133_PFC_05_MTIOC0C         = (0x05U << IOPORT_PFC_OFFSET), ///< P13_3 / MTU3n / MTIOC0C
    IOPORT_PIN_P133_PFC_06_MTIOC0B         = (0x06U << IOPORT_PFC_OFFSET), ///< P13_3 / MTU3n / MTIOC0B
    IOPORT_PIN_P133_PFC_07_GTIOC10B        = (0x07U << IOPORT_PFC_OFFSET), ///< P13_3 / GPTn / GTIOC10B
    IOPORT_PIN_P133_PFC_08_CMTW1_TOC0      = (0x08U << IOPORT_PFC_OFFSET), ///< P13_3 / CMTWn / CMTW1_TOC0
    IOPORT_PIN_P133_PFC_09_CTS1            = (0x09U << IOPORT_PFC_OFFSET), ///< P13_3 / SCIn / CTS1
    IOPORT_PIN_P133_PFC_0A_SPI_RSPCK0      = (0x0AU << IOPORT_PFC_OFFSET), ///< P13_3 / SPIn / SPI_RSPCK0
    IOPORT_PIN_P133_PFC_0B_IIC_SDA0        = (0x0BU << IOPORT_PFC_OFFSET), ///< P13_3 / IICn / IIC_SDA0
    IOPORT_PIN_P133_PFC_0C_MDAT4           = (0x0CU << IOPORT_PFC_OFFSET), ///< P13_3 / DSMIFn / MDAT4
    IOPORT_PIN_P133_PFC_0E_RD              = (0x0EU << IOPORT_PFC_OFFSET), ///< P13_3 / BSC / RD
    IOPORT_PIN_P134_PFC_01_ESC_RESETOUT    = (0x01U << IOPORT_PFC_OFFSET), ///< P13_4 / ETHER_ESC / ESC_RESETOUT
    IOPORT_PIN_P134_PFC_02_MTIOC0D         = (0x02U << IOPORT_PFC_OFFSET), ///< P13_4 / MTU3n / MTIOC0D
    IOPORT_PIN_P134_PFC_03_GTIOC8B         = (0x03U << IOPORT_PFC_OFFSET), ///< P13_4 / GPTn / GTIOC8B
    IOPORT_PIN_P134_PFC_05_A0              = (0x05U << IOPORT_PFC_OFFSET), ///< P13_4 / BSC / A0
    IOPORT_PIN_P135_PFC_00_XSPI0_WP1       = (0x00U << IOPORT_PFC_OFFSET), ///< P13_5 / XSPIn / XSPI0_WP1
    IOPORT_PIN_P135_PFC_01_GMAC_PTPTRG0    = (0x01U << IOPORT_PFC_OFFSET), ///< P13_5 / ETHER_GMAC / GMAC_PTPTRG0
    IOPORT_PIN_P135_PFC_02_ESC_LATCH0      = (0x02U << IOPORT_PFC_OFFSET), ///< P13_5 / ETHER_ESC / ESC_LATCH0
    IOPORT_PIN_P135_PFC_03_ESC_LATCH1      = (0x03U << IOPORT_PFC_OFFSET), ///< P13_5 / ETHER_ESC / ESC_LATCH1
    IOPORT_PIN_P135_PFC_04_MTCLKA          = (0x04U << IOPORT_PFC_OFFSET), ///< P13_5 / MTU3 / MTCLKA
    IOPORT_PIN_P135_PFC_05_SPI_RSPCK1      = (0x05U << IOPORT_PFC_OFFSET), ///< P13_5 / SPIn / SPI_RSPCK1
    IOPORT_PIN_P135_PFC_06_IIC_SCL2        = (0x06U << IOPORT_PFC_OFFSET), ///< P13_5 / IICn / IIC_SCL2
    IOPORT_PIN_P136_PFC_00_XSPI0_WP0       = (0x00U << IOPORT_PFC_OFFSET), ///< P13_6 / XSPIn / XSPI0_WP0
    IOPORT_PIN_P136_PFC_01_ETHSW_PTPOUT0   = (0x01U << IOPORT_PFC_OFFSET), ///< P13_6 / ETHER_ETHSW / ETHSW_PTPOUT0
    IOPORT_PIN_P136_PFC_02_ESC_SYNC0       = (0x02U << IOPORT_PFC_OFFSET), ///< P13_6 / ETHER_ESC / ESC_SYNC0
    IOPORT_PIN_P136_PFC_03_ESC_SYNC1       = (0x03U << IOPORT_PFC_OFFSET), ///< P13_6 / ETHER_ESC / ESC_SYNC1
    IOPORT_PIN_P136_PFC_04_MTCLKB          = (0x04U << IOPORT_PFC_OFFSET), ///< P13_6 / MTU3 / MTCLKB
    IOPORT_PIN_P137_PFC_00_XSPI0_ECS1      = (0x00U << IOPORT_PFC_OFFSET), ///< P13_7 / XSPIn / XSPI0_ECS1
    IOPORT_PIN_P137_PFC_01_GMAC_PTPTRG1    = (0x01U << IOPORT_PFC_OFFSET), ///< P13_7 / ETHER_GMAC / GMAC_PTPTRG1
    IOPORT_PIN_P137_PFC_02_ESC_LATCH1      = (0x02U << IOPORT_PFC_OFFSET), ///< P13_7 / ETHER_ESC / ESC_LATCH1
    IOPORT_PIN_P137_PFC_03_ESC_LATCH0      = (0x03U << IOPORT_PFC_OFFSET), ///< P13_7 / ETHER_ESC / ESC_LATCH0
    IOPORT_PIN_P137_PFC_04_MTCLKC          = (0x04U << IOPORT_PFC_OFFSET), ///< P13_7 / MTU3 / MTCLKC
    IOPORT_PIN_P137_PFC_05_MBX_HINT        = (0x05U << IOPORT_PFC_OFFSET), ///< P13_7 / MBXSEM / MBX_HINT
    IOPORT_PIN_P140_PFC_00_XSPI0_INT0      = (0x00U << IOPORT_PFC_OFFSET), ///< P14_0 / XSPIn / XSPI0_INT0
    IOPORT_PIN_P140_PFC_01_ETHSW_PTPOUT1   = (0x01U << IOPORT_PFC_OFFSET), ///< P14_0 / ETHER_ETHSW / ETHSW_PTPOUT1
    IOPORT_PIN_P140_PFC_02_ESC_SYNC1       = (0x02U << IOPORT_PFC_OFFSET), ///< P14_0 / ETHER_ESC / ESC_SYNC1
    IOPORT_PIN_P140_PFC_03_ESC_SYNC0       = (0x03U << IOPORT_PFC_OFFSET), ///< P14_0 / ETHER_ESC / ESC_SYNC0
    IOPORT_PIN_P140_PFC_04_MTCLKD          = (0x04U << IOPORT_PFC_OFFSET), ///< P14_0 / MTU3 / MTCLKD
    IOPORT_PIN_P141_PFC_00_XSPI0_INT1      = (0x00U << IOPORT_PFC_OFFSET), ///< P14_1 / XSPIn / XSPI0_INT1
    IOPORT_PIN_P141_PFC_01_ETH1_COL        = (0x01U << IOPORT_PFC_OFFSET), ///< P14_1 / ETHER_ETHn / ETH1_COL
    IOPORT_PIN_P141_PFC_03_MTIOC8A         = (0x03U << IOPORT_PFC_OFFSET), ///< P14_1 / MTU3n / MTIOC8A
    IOPORT_PIN_P141_PFC_04_GTIOC8A         = (0x04U << IOPORT_PFC_OFFSET), ///< P14_1 / GPTn / GTIOC8A
    IOPORT_PIN_P141_PFC_06_GMAC_PTPTRG1    = (0x06U << IOPORT_PFC_OFFSET), ///< P14_1 / ETHER_GMAC / GMAC_PTPTRG1
    IOPORT_PIN_P141_PFC_07_ESC_LATCH0      = (0x07U << IOPORT_PFC_OFFSET), ///< P14_1 / ETHER_ESC / ESC_LATCH0
    IOPORT_PIN_P141_PFC_08_ESC_LATCH1      = (0x08U << IOPORT_PFC_OFFSET), ///< P14_1 / ETHER_ESC / ESC_LATCH1
    IOPORT_PIN_P141_PFC_09_HSPI_IO0        = (0x09U << IOPORT_PFC_OFFSET), ///< P14_1 / SHOSTIF / HSPI_IO0
    IOPORT_PIN_P142_PFC_00_IRQ6            = (0x00U << IOPORT_PFC_OFFSET), ///< P14_2 / IRQ / IRQ6
    IOPORT_PIN_P142_PFC_01_XSPI0_ECS0      = (0x01U << IOPORT_PFC_OFFSET), ///< P14_2 / XSPIn / XSPI0_ECS0
    IOPORT_PIN_P142_PFC_02_ETH0_CRS        = (0x02U << IOPORT_PFC_OFFSET), ///< P14_2 / ETHER_ETHn / ETH0_CRS
    IOPORT_PIN_P142_PFC_04_MTIOC8B         = (0x04U << IOPORT_PFC_OFFSET), ///< P14_2 / MTU3n / MTIOC8B
    IOPORT_PIN_P142_PFC_05_GTIOC8B         = (0x05U << IOPORT_PFC_OFFSET), ///< P14_2 / GPTn / GTIOC8B
    IOPORT_PIN_P142_PFC_07_ETH2_CRS        = (0x07U << IOPORT_PFC_OFFSET), ///< P14_2 / ETHER_ETHn / ETH2_CRS
    IOPORT_PIN_P142_PFC_08_HSPI_CK         = (0x08U << IOPORT_PFC_OFFSET), ///< P14_2 / SHOSTIF / HSPI_CK
    IOPORT_PIN_P143_PFC_00_XSPI0_RSTO1     = (0x00U << IOPORT_PFC_OFFSET), ///< P14_3 / XSPIn / XSPI0_RSTO1
    IOPORT_PIN_P143_PFC_01_ETH0_COL        = (0x01U << IOPORT_PFC_OFFSET), ///< P14_3 / ETHER_ETHn / ETH0_COL
    IOPORT_PIN_P143_PFC_04_MTIOC0A         = (0x04U << IOPORT_PFC_OFFSET), ///< P14_3 / MTU3n / MTIOC0A
    IOPORT_PIN_P143_PFC_06_ETH2_COL        = (0x06U << IOPORT_PFC_OFFSET), ///< P14_3 / ETHER_ETHn / ETH2_COL
    IOPORT_PIN_P143_PFC_07_HSPI_IO1        = (0x07U << IOPORT_PFC_OFFSET), ///< P14_3 / SHOSTIF / HSPI_IO1
    IOPORT_PIN_P144_PFC_00_XSPI0_DS        = (0x00U << IOPORT_PFC_OFFSET), ///< P14_4 / XSPIn / XSPI0_DS
    IOPORT_PIN_P144_PFC_01_BS              = (0x01U << IOPORT_PFC_OFFSET), ///< P14_4 / BSC / BS
    IOPORT_PIN_P144_PFC_02_ESC_IRQ         = (0x02U << IOPORT_PFC_OFFSET), ///< P14_4 / ETHER_ESC / ESC_IRQ
    IOPORT_PIN_P144_PFC_03_MTIOC0B         = (0x03U << IOPORT_PFC_OFFSET), ///< P14_4 / MTU3n / MTIOC0B
    IOPORT_PIN_P144_PFC_04_HBS             = (0x04U << IOPORT_PFC_OFFSET), ///< P14_4 / PHOSTIF / HBS
    IOPORT_PIN_P145_PFC_00_XSPI0_CKN       = (0x00U << IOPORT_PFC_OFFSET), ///< P14_5 / XSPIn / XSPI0_CKN
    IOPORT_PIN_P145_PFC_01_CS3             = (0x01U << IOPORT_PFC_OFFSET), ///< P14_5 / BSC / CS3
    IOPORT_PIN_P145_PFC_02_POE8            = (0x02U << IOPORT_PFC_OFFSET), ///< P14_5 / MTU_POE3 / POE8
    IOPORT_PIN_P145_PFC_03_HSPI_INT        = (0x03U << IOPORT_PFC_OFFSET), ///< P14_5 / SHOSTIF / HSPI_INT
    IOPORT_PIN_P146_PFC_00_XSPI0_CKP       = (0x00U << IOPORT_PFC_OFFSET), ///< P14_6 / XSPIn / XSPI0_CKP
    IOPORT_PIN_P146_PFC_01_A21             = (0x01U << IOPORT_PFC_OFFSET), ///< P14_6 / BSC / A21
    IOPORT_PIN_P147_PFC_00_XSPI0_IO0       = (0x00U << IOPORT_PFC_OFFSET), ///< P14_7 / XSPIn / XSPI0_IO0
    IOPORT_PIN_P147_PFC_01_A22             = (0x01U << IOPORT_PFC_OFFSET), ///< P14_7 / BSC / A22
    IOPORT_PIN_P147_PFC_02_SCK5            = (0x02U << IOPORT_PFC_OFFSET), ///< P14_7 / SCIn / SCK5
    IOPORT_PIN_P147_PFC_03_SPI_MISO1       = (0x03U << IOPORT_PFC_OFFSET), ///< P14_7 / SPIn / SPI_MISO1
    IOPORT_PIN_P147_PFC_04_BS              = (0x04U << IOPORT_PFC_OFFSET), ///< P14_7 / BSC / BS
    IOPORT_PIN_P150_PFC_00_XSPI0_IO1       = (0x00U << IOPORT_PFC_OFFSET), ///< P15_0 / XSPIn / XSPI0_IO1
    IOPORT_PIN_P150_PFC_01_A23             = (0x01U << IOPORT_PFC_OFFSET), ///< P15_0 / BSC / A23
    IOPORT_PIN_P150_PFC_02_RXD5_SCL5_MISO5 = (0x02U << IOPORT_PFC_OFFSET), ///< P15_0 / SCIn / RXD5_SCL5_MISO5
    IOPORT_PIN_P150_PFC_03_SPI_MOSI1       = (0x03U << IOPORT_PFC_OFFSET), ///< P15_0 / SPIn / SPI_MOSI1
    IOPORT_PIN_P150_PFC_04_CKE             = (0x04U << IOPORT_PFC_OFFSET), ///< P15_0 / BSC / CKE
    IOPORT_PIN_P151_PFC_00_XSPI0_IO2       = (0x00U << IOPORT_PFC_OFFSET), ///< P15_1 / XSPIn / XSPI0_IO2
    IOPORT_PIN_P151_PFC_01_A24             = (0x01U << IOPORT_PFC_OFFSET), ///< P15_1 / BSC / A24
    IOPORT_PIN_P151_PFC_02_MTIOC0C         = (0x02U << IOPORT_PFC_OFFSET), ///< P15_1 / MTU3n / MTIOC0C
    IOPORT_PIN_P151_PFC_03_TXD5_SDA5_MOSI5 = (0x03U << IOPORT_PFC_OFFSET), ///< P15_1 / SCIn / TXD5_SDA5_MOSI5
    IOPORT_PIN_P151_PFC_04_SPI_SSL10       = (0x04U << IOPORT_PFC_OFFSET), ///< P15_1 / SPIn / SPI_SSL10
    IOPORT_PIN_P151_PFC_05_CAS             = (0x05U << IOPORT_PFC_OFFSET), ///< P15_1 / BSC / CAS
    IOPORT_PIN_P152_PFC_00_XSPI0_IO3       = (0x00U << IOPORT_PFC_OFFSET), ///< P15_2 / XSPIn / XSPI0_IO3
    IOPORT_PIN_P152_PFC_01_A25             = (0x01U << IOPORT_PFC_OFFSET), ///< P15_2 / BSC / A25
    IOPORT_PIN_P152_PFC_02_MTIOC0D         = (0x02U << IOPORT_PFC_OFFSET), ///< P15_2 / MTU3n / MTIOC0D
    IOPORT_PIN_P152_PFC_03_SS5_CTS5_RTS5   = (0x03U << IOPORT_PFC_OFFSET), ///< P15_2 / SCIn / SS5_CTS5_RTS5
    IOPORT_PIN_P152_PFC_04_SPI_SSL11       = (0x04U << IOPORT_PFC_OFFSET), ///< P15_2 / SPIn / SPI_SSL11
    IOPORT_PIN_P152_PFC_05_RAS             = (0x05U << IOPORT_PFC_OFFSET), ///< P15_2 / BSC / RAS
    IOPORT_PIN_P153_PFC_00_XSPI0_IO4       = (0x00U << IOPORT_PFC_OFFSET), ///< P15_3 / XSPIn / XSPI0_IO4
    IOPORT_PIN_P153_PFC_01_MTIOC8C         = (0x01U << IOPORT_PFC_OFFSET), ///< P15_3 / MTU3n / MTIOC8C
    IOPORT_PIN_P153_PFC_02_MCLK1           = (0x02U << IOPORT_PFC_OFFSET), ///< P15_3 / DSMIFn / MCLK1
    IOPORT_PIN_P153_PFC_03_D11             = (0x03U << IOPORT_PFC_OFFSET), ///< P15_3 / BSC / D11
    IOPORT_PIN_P154_PFC_00_XSPI0_IO5       = (0x00U << IOPORT_PFC_OFFSET), ///< P15_4 / XSPIn / XSPI0_IO5
    IOPORT_PIN_P154_PFC_01_MTIOC8D         = (0x01U << IOPORT_PFC_OFFSET), ///< P15_4 / MTU3n / MTIOC8D
    IOPORT_PIN_P154_PFC_02_MDAT1           = (0x02U << IOPORT_PFC_OFFSET), ///< P15_4 / DSMIFn / MDAT1
    IOPORT_PIN_P154_PFC_03_D12             = (0x03U << IOPORT_PFC_OFFSET), ///< P15_4 / BSC / D12
    IOPORT_PIN_P155_PFC_00_XSPI0_IO6       = (0x00U << IOPORT_PFC_OFFSET), ///< P15_5 / XSPIn / XSPI0_IO6
    IOPORT_PIN_P155_PFC_01_MCLK2           = (0x01U << IOPORT_PFC_OFFSET), ///< P15_5 / DSMIFn / MCLK2
    IOPORT_PIN_P155_PFC_02_D13             = (0x02U << IOPORT_PFC_OFFSET), ///< P15_5 / BSC / D13
    IOPORT_PIN_P156_PFC_00_XSPI0_IO7       = (0x00U << IOPORT_PFC_OFFSET), ///< P15_6 / XSPIn / XSPI0_IO7
    IOPORT_PIN_P156_PFC_01_SPI_SSL12       = (0x01U << IOPORT_PFC_OFFSET), ///< P15_6 / SPIn / SPI_SSL12
    IOPORT_PIN_P156_PFC_02_MDAT2           = (0x02U << IOPORT_PFC_OFFSET), ///< P15_6 / DSMIFn / MDAT2
    IOPORT_PIN_P156_PFC_03_D14             = (0x03U << IOPORT_PFC_OFFSET), ///< P15_6 / BSC / D14
    IOPORT_PIN_P157_PFC_00_XSPI0_CS0       = (0x00U << IOPORT_PFC_OFFSET), ///< P15_7 / XSPIn / XSPI0_CS0
    IOPORT_PIN_P157_PFC_01_CTS5            = (0x01U << IOPORT_PFC_OFFSET), ///< P15_7 / SCIn / CTS5
    IOPORT_PIN_P157_PFC_02_SPI_SSL13       = (0x02U << IOPORT_PFC_OFFSET), ///< P15_7 / SPIn / SPI_SSL13
    IOPORT_PIN_P157_PFC_03_TEND            = (0x03U << IOPORT_PFC_OFFSET), ///< P15_7 / DMAC / TEND
    IOPORT_PIN_P160_PFC_00_XSPI0_CS1       = (0x00U << IOPORT_PFC_OFFSET), ///< P16_0 / XSPIn / XSPI0_CS1
    IOPORT_PIN_P160_PFC_01_ETH0_TXER       = (0x01U << IOPORT_PFC_OFFSET), ///< P16_0 / ETHER_ETHn / ETH0_TXER
    IOPORT_PIN_P160_PFC_02_TXD0_SDA0_MOSI0 = (0x02U << IOPORT_PFC_OFFSET), ///< P16_0 / SCIn / TXD0_SDA0_MOSI0
    IOPORT_PIN_P160_PFC_03_SPI_MOSI3       = (0x03U << IOPORT_PFC_OFFSET), ///< P16_0 / SPIn / SPI_MOSI3
    IOPORT_PIN_P160_PFC_04_MCLK3           = (0x04U << IOPORT_PFC_OFFSET), ///< P16_0 / DSMIFn / MCLK3
    IOPORT_PIN_P160_PFC_06_ETH2_REFCLK     = (0x06U << IOPORT_PFC_OFFSET), ///< P16_0 / ETHER_ETHn / ETH2_REFCLK
    IOPORT_PIN_P160_PFC_07_HSPI_CS         = (0x07U << IOPORT_PFC_OFFSET), ///< P16_0 / SHOSTIF / HSPI_CS
    IOPORT_PIN_P161_PFC_00_XSPI0_RESET0    = (0x00U << IOPORT_PFC_OFFSET), ///< P16_1 / XSPIn / XSPI0_RESET0
    IOPORT_PIN_P161_PFC_01_CMTW0_TOC1      = (0x01U << IOPORT_PFC_OFFSET), ///< P16_1 / CMTWn / CMTW0_TOC1
    IOPORT_PIN_P161_PFC_02_ADTRG0          = (0x02U << IOPORT_PFC_OFFSET), ///< P16_1 / ADCn / ADTRG0
    IOPORT_PIN_P161_PFC_03_RXD0_SCL0_MISO0 = (0x03U << IOPORT_PFC_OFFSET), ///< P16_1 / SCIn / RXD0_SCL0_MISO0
    IOPORT_PIN_P161_PFC_04_SPI_MISO3       = (0x04U << IOPORT_PFC_OFFSET), ///< P16_1 / SPIn / SPI_MISO3
    IOPORT_PIN_P161_PFC_05_MDAT3           = (0x05U << IOPORT_PFC_OFFSET), ///< P16_1 / DSMIFn / MDAT3
    IOPORT_PIN_P161_PFC_07_CS2             = (0x07U << IOPORT_PFC_OFFSET), ///< P16_1 / BSC / CS2
    IOPORT_PIN_P161_PFC_08_HCS1            = (0x08U << IOPORT_PFC_OFFSET), ///< P16_1 / PHOSTIF / HCS1
    IOPORT_PIN_P162_PFC_00_NMI             = (0x00U << IOPORT_PFC_OFFSET), ///< P16_2 / IRQ / NMI
    IOPORT_PIN_P162_PFC_01_XSPI0_RESET1    = (0x01U << IOPORT_PFC_OFFSET), ///< P16_2 / XSPIn / XSPI0_RESET1
    IOPORT_PIN_P162_PFC_02_CTS0            = (0x02U << IOPORT_PFC_OFFSET), ///< P16_2 / SCIn / CTS0
    IOPORT_PIN_P162_PFC_03_SPI_RSPCK3      = (0x03U << IOPORT_PFC_OFFSET), ///< P16_2 / SPIn / SPI_RSPCK3
    IOPORT_PIN_P162_PFC_04_USB_EXICEN      = (0x04U << IOPORT_PFC_OFFSET), ///< P16_2 / USB_HS / USB_EXICEN
    IOPORT_PIN_P162_PFC_06_HSPI_IO2        = (0x06U << IOPORT_PFC_OFFSET), ///< P16_2 / SHOSTIF / HSPI_IO2
    IOPORT_PIN_P162_PFC_07_HERROUT         = (0x07U << IOPORT_PFC_OFFSET), ///< P16_2 / PHOSTIF / HERROUT
    IOPORT_PIN_P163_PFC_00_IRQ7            = (0x00U << IOPORT_PFC_OFFSET), ///< P16_3 / IRQ / IRQ7
    IOPORT_PIN_P163_PFC_01_XSPI0_RSTO0     = (0x01U << IOPORT_PFC_OFFSET), ///< P16_3 / XSPIn / XSPI0_RSTO0
    IOPORT_PIN_P163_PFC_02_ETH1_TXER       = (0x02U << IOPORT_PFC_OFFSET), ///< P16_3 / ETHER_ETHn / ETH1_TXER
    IOPORT_PIN_P163_PFC_03_GTADSMP1        = (0x03U << IOPORT_PFC_OFFSET), ///< P16_3 / GPT / GTADSMP1
    IOPORT_PIN_P163_PFC_04_SCK0            = (0x04U << IOPORT_PFC_OFFSET), ///< P16_3 / SCIn / SCK0
    IOPORT_PIN_P163_PFC_05_SPI_SSL30       = (0x05U << IOPORT_PFC_OFFSET), ///< P16_3 / SPIn / SPI_SSL30
    IOPORT_PIN_P163_PFC_07_ETH1_CRS        = (0x07U << IOPORT_PFC_OFFSET), ///< P16_3 / ETHER_ETHn / ETH1_CRS
    IOPORT_PIN_P163_PFC_08_CS3             = (0x08U << IOPORT_PFC_OFFSET), ///< P16_3 / BSC / CS3
    IOPORT_PIN_P163_PFC_09_HSPI_IO3        = (0x09U << IOPORT_PFC_OFFSET), ///< P16_3 / SHOSTIF / HSPI_IO3
    IOPORT_PIN_P165_PFC_00_MTIC5U          = (0x00U << IOPORT_PFC_OFFSET), ///< P16_5 / MTU3n / MTIC5U
    IOPORT_PIN_P165_PFC_01_TXD0_SDA0_MOSI0 = (0x01U << IOPORT_PFC_OFFSET), ///< P16_5 / SCIn / TXD0_SDA0_MOSI0
    IOPORT_PIN_P165_PFC_02_A15             = (0x02U << IOPORT_PFC_OFFSET), ///< P16_5 / BSC / A15
    IOPORT_PIN_P165_PFC_03_HSPI_IO4        = (0x03U << IOPORT_PFC_OFFSET), ///< P16_5 / SHOSTIF / HSPI_IO4
    IOPORT_PIN_P166_PFC_00_IRQ8            = (0x00U << IOPORT_PFC_OFFSET), ///< P16_6 / IRQ / IRQ8
    IOPORT_PIN_P166_PFC_01_MTIC5V          = (0x01U << IOPORT_PFC_OFFSET), ///< P16_6 / MTU3n / MTIC5V
    IOPORT_PIN_P166_PFC_02_RXD0_SCL0_MISO0 = (0x02U << IOPORT_PFC_OFFSET), ///< P16_6 / SCIn / RXD0_SCL0_MISO0
    IOPORT_PIN_P166_PFC_03_CS0             = (0x03U << IOPORT_PFC_OFFSET), ///< P16_6 / BSC / CS0
    IOPORT_PIN_P166_PFC_04_HSPI_IO5        = (0x04U << IOPORT_PFC_OFFSET), ///< P16_6 / SHOSTIF / HSPI_IO5
    IOPORT_PIN_P166_PFC_05_HCS0            = (0x05U << IOPORT_PFC_OFFSET), ///< P16_6 / PHOSTIF / HCS0
    IOPORT_PIN_P167_PFC_00_MTIC5W          = (0x00U << IOPORT_PFC_OFFSET), ///< P16_7 / MTU3n / MTIC5W
    IOPORT_PIN_P167_PFC_01_SCK0            = (0x01U << IOPORT_PFC_OFFSET), ///< P16_7 / SCIn / SCK0
    IOPORT_PIN_P167_PFC_02_XSPI1_IO0       = (0x02U << IOPORT_PFC_OFFSET), ///< P16_7 / XSPIn / XSPI1_IO0
    IOPORT_PIN_P167_PFC_03_A13             = (0x03U << IOPORT_PFC_OFFSET), ///< P16_7 / BSC / A13
    IOPORT_PIN_P167_PFC_04_HA13            = (0x04U << IOPORT_PFC_OFFSET), ///< P16_7 / PHOSTIF / HA13
    IOPORT_PIN_P170_PFC_00_ESC_IRQ         = (0x00U << IOPORT_PFC_OFFSET), ///< P17_0 / ETHER_ESC / ESC_IRQ
    IOPORT_PIN_P170_PFC_01_SS0_CTS0_RTS0   = (0x01U << IOPORT_PFC_OFFSET), ///< P17_0 / SCIn / SS0_CTS0_RTS0
    IOPORT_PIN_P170_PFC_02_XSPI1_IO1       = (0x02U << IOPORT_PFC_OFFSET), ///< P17_0 / XSPIn / XSPI1_IO1
    IOPORT_PIN_P173_PFC_00_TRACECTL        = (0x00U << IOPORT_PFC_OFFSET), ///< P17_3 / TRACE / TRACECTL
    IOPORT_PIN_P173_PFC_01_GTETRGA         = (0x01U << IOPORT_PFC_OFFSET), ///< P17_3 / GPT_POEG / GTETRGA
    IOPORT_PIN_P173_PFC_02_POE0            = (0x02U << IOPORT_PFC_OFFSET), ///< P17_3 / MTU_POE3 / POE0
    IOPORT_PIN_P173_PFC_03_ADTRG1          = (0x03U << IOPORT_PFC_OFFSET), ///< P17_3 / ADCn / ADTRG1
    IOPORT_PIN_P173_PFC_04_SPI_SSL31       = (0x04U << IOPORT_PFC_OFFSET), ///< P17_3 / SPIn / SPI_SSL31
    IOPORT_PIN_P173_PFC_05_DREQ            = (0x05U << IOPORT_PFC_OFFSET), ///< P17_3 / DMAC / DREQ
    IOPORT_PIN_P173_PFC_07_XSPI1_IO2       = (0x07U << IOPORT_PFC_OFFSET), ///< P17_3 / XSPIn / XSPI1_IO2
    IOPORT_PIN_P174_PFC_00_TRACECLK        = (0x00U << IOPORT_PFC_OFFSET), ///< P17_4 / TRACE / TRACECLK
    IOPORT_PIN_P174_PFC_01_MTIOC3C         = (0x01U << IOPORT_PFC_OFFSET), ///< P17_4 / MTU3n / MTIOC3C
    IOPORT_PIN_P174_PFC_02_GTETRGB         = (0x02U << IOPORT_PFC_OFFSET), ///< P17_4 / GPT_POEG / GTETRGB
    IOPORT_PIN_P174_PFC_03_GTIOC0A         = (0x03U << IOPORT_PFC_OFFSET), ///< P17_4 / GPTn / GTIOC0A
    IOPORT_PIN_P174_PFC_04_CTS3            = (0x04U << IOPORT_PFC_OFFSET), ///< P17_4 / SCIn / CTS3
    IOPORT_PIN_P174_PFC_05_SPI_SSL32       = (0x05U << IOPORT_PFC_OFFSET), ///< P17_4 / SPIn / SPI_SSL32
    IOPORT_PIN_P174_PFC_07_XSPI1_IO3       = (0x07U << IOPORT_PFC_OFFSET), ///< P17_4 / XSPIn / XSPI1_IO3
    IOPORT_PIN_P174_PFC_08_DACK            = (0x08U << IOPORT_PFC_OFFSET), ///< P17_4 / DMAC / DACK
    IOPORT_PIN_P175_PFC_01_MTIOC3A         = (0x01U << IOPORT_PFC_OFFSET), ///< P17_5 / MTU3n / MTIOC3A
    IOPORT_PIN_P175_PFC_02_GTETRGC         = (0x02U << IOPORT_PFC_OFFSET), ///< P17_5 / GPT_POEG / GTETRGC
    IOPORT_PIN_P175_PFC_03_GTIOC0B         = (0x03U << IOPORT_PFC_OFFSET), ///< P17_5 / GPTn / GTIOC0B
    IOPORT_PIN_P175_PFC_04_TEND            = (0x04U << IOPORT_PFC_OFFSET), ///< P17_5 / DMAC / TEND
    IOPORT_PIN_P175_PFC_05_USB_OVRCUR      = (0x05U << IOPORT_PFC_OFFSET), ///< P17_5 / USB_HS / USB_OVRCUR
    IOPORT_PIN_P176_PFC_00_MTIOC3B         = (0x00U << IOPORT_PFC_OFFSET), ///< P17_6 / MTU3n / MTIOC3B
    IOPORT_PIN_P176_PFC_01_GTIOC1A         = (0x01U << IOPORT_PFC_OFFSET), ///< P17_6 / GPTn / GTIOC1A
    IOPORT_PIN_P176_PFC_02_SCK3            = (0x02U << IOPORT_PFC_OFFSET), ///< P17_6 / SCIn / SCK3
    IOPORT_PIN_P176_PFC_04_XSPI1_DS        = (0x04U << IOPORT_PFC_OFFSET), ///< P17_6 / XSPIn / XSPI1_DS
    IOPORT_PIN_P176_PFC_05_RD_WR           = (0x05U << IOPORT_PFC_OFFSET), ///< P17_6 / BSC / RD_WR
    IOPORT_PIN_P176_PFC_06_HWRSTB          = (0x06U << IOPORT_PFC_OFFSET), ///< P17_6 / PHOSTIF / HWRSTB
    IOPORT_PIN_P177_PFC_00_MTIOC4A         = (0x00U << IOPORT_PFC_OFFSET), ///< P17_7 / MTU3n / MTIOC4A
    IOPORT_PIN_P177_PFC_01_MTIOC4C         = (0x01U << IOPORT_PFC_OFFSET), ///< P17_7 / MTU3n / MTIOC4C
    IOPORT_PIN_P177_PFC_02_GTIOC2A         = (0x02U << IOPORT_PFC_OFFSET), ///< P17_7 / GPTn / GTIOC2A
    IOPORT_PIN_P177_PFC_03_GTIOC3A         = (0x03U << IOPORT_PFC_OFFSET), ///< P17_7 / GPTn / GTIOC3A
    IOPORT_PIN_P177_PFC_04_RXD3_SCL3_MISO3 = (0x04U << IOPORT_PFC_OFFSET), ///< P17_7 / SCIn / RXD3_SCL3_MISO3
    IOPORT_PIN_P177_PFC_05_DACK            = (0x05U << IOPORT_PFC_OFFSET), ///< P17_7 / DMAC / DACK
    IOPORT_PIN_P177_PFC_07_XSPI1_CKP       = (0x07U << IOPORT_PFC_OFFSET), ///< P17_7 / XSPIn / XSPI1_CKP
    IOPORT_PIN_P177_PFC_08_RD              = (0x08U << IOPORT_PFC_OFFSET), ///< P17_7 / BSC / RD
    IOPORT_PIN_P177_PFC_09_HRD             = (0x09U << IOPORT_PFC_OFFSET), ///< P17_7 / PHOSTIF / HRD
    IOPORT_PIN_P180_PFC_00_MTIOC4C         = (0x00U << IOPORT_PFC_OFFSET), ///< P18_0 / MTU3n / MTIOC4C
    IOPORT_PIN_P180_PFC_01_MTIOC4A         = (0x01U << IOPORT_PFC_OFFSET), ///< P18_0 / MTU3n / MTIOC4A
    IOPORT_PIN_P180_PFC_02_GTIOC3A         = (0x02U << IOPORT_PFC_OFFSET), ///< P18_0 / GPTn / GTIOC3A
    IOPORT_PIN_P180_PFC_03_GTIOC2A         = (0x03U << IOPORT_PFC_OFFSET), ///< P18_0 / GPTn / GTIOC2A
    IOPORT_PIN_P180_PFC_04_TXD3_SDA3_MOSI3 = (0x04U << IOPORT_PFC_OFFSET), ///< P18_0 / SCIn / TXD3_SDA3_MOSI3
    IOPORT_PIN_P180_PFC_05_WE0_DQMLL       = (0x05U << IOPORT_PFC_OFFSET), ///< P18_0 / BSC / WE0_DQMLL
    IOPORT_PIN_P180_PFC_06_HSPI_IO6        = (0x06U << IOPORT_PFC_OFFSET), ///< P18_0 / SHOSTIF / HSPI_IO6
    IOPORT_PIN_P180_PFC_07_HWR0            = (0x07U << IOPORT_PFC_OFFSET), ///< P18_0 / PHOSTIF / HWR0
    IOPORT_PIN_P181_PFC_00_IRQ10           = (0x00U << IOPORT_PFC_OFFSET), ///< P18_1 / IRQ / IRQ10
    IOPORT_PIN_P181_PFC_01_MTIOC3D         = (0x01U << IOPORT_PFC_OFFSET), ///< P18_1 / MTU3n / MTIOC3D
    IOPORT_PIN_P181_PFC_02_GTIOC1B         = (0x02U << IOPORT_PFC_OFFSET), ///< P18_1 / GPTn / GTIOC1B
    IOPORT_PIN_P181_PFC_03_ADTRG1          = (0x03U << IOPORT_PFC_OFFSET), ///< P18_1 / ADCn / ADTRG1
    IOPORT_PIN_P181_PFC_04_SS3_CTS3_RTS3   = (0x04U << IOPORT_PFC_OFFSET), ///< P18_1 / SCIn / SS3_CTS3_RTS3
    IOPORT_PIN_P181_PFC_06_WE1_DQMLU       = (0x06U << IOPORT_PFC_OFFSET), ///< P18_1 / BSC / WE1_DQMLU
    IOPORT_PIN_P181_PFC_07_HSPI_IO7        = (0x07U << IOPORT_PFC_OFFSET), ///< P18_1 / SHOSTIF / HSPI_IO7
    IOPORT_PIN_P181_PFC_08_HWR1            = (0x08U << IOPORT_PFC_OFFSET), ///< P18_1 / PHOSTIF / HWR1
    IOPORT_PIN_P182_PFC_00_MTIOC4B         = (0x00U << IOPORT_PFC_OFFSET), ///< P18_2 / MTU3n / MTIOC4B
    IOPORT_PIN_P182_PFC_01_MTIOC4D         = (0x01U << IOPORT_PFC_OFFSET), ///< P18_2 / MTU3n / MTIOC4D
    IOPORT_PIN_P182_PFC_02_GTIOC2B         = (0x02U << IOPORT_PFC_OFFSET), ///< P18_2 / GPTn / GTIOC2B
    IOPORT_PIN_P182_PFC_03_GTIOC3B         = (0x03U << IOPORT_PFC_OFFSET), ///< P18_2 / GPTn / GTIOC3B
    IOPORT_PIN_P182_PFC_05_XSPI1_CS0       = (0x05U << IOPORT_PFC_OFFSET), ///< P18_2 / XSPIn / XSPI1_CS0
    IOPORT_PIN_P182_PFC_06_ETH1_COL        = (0x06U << IOPORT_PFC_OFFSET), ///< P18_2 / ETHER_ETHn / ETH1_COL
    IOPORT_PIN_P182_PFC_07_BS              = (0x07U << IOPORT_PFC_OFFSET), ///< P18_2 / BSC / BS
    IOPORT_PIN_P182_PFC_08_SCK0            = (0x08U << IOPORT_PFC_OFFSET), ///< P18_2 / SCIn / SCK0
    IOPORT_PIN_P182_PFC_09_IIC_SDA2        = (0x09U << IOPORT_PFC_OFFSET), ///< P18_2 / IICn / IIC_SDA2
    IOPORT_PIN_P183_PFC_00_IRQ0            = (0x00U << IOPORT_PFC_OFFSET), ///< P18_3 / IRQ / IRQ0
    IOPORT_PIN_P183_PFC_01_MTIOC4D         = (0x01U << IOPORT_PFC_OFFSET), ///< P18_3 / MTU3n / MTIOC4D
    IOPORT_PIN_P183_PFC_02_MTIOC4B         = (0x02U << IOPORT_PFC_OFFSET), ///< P18_3 / MTU3n / MTIOC4B
    IOPORT_PIN_P183_PFC_03_GTIOC3B         = (0x03U << IOPORT_PFC_OFFSET), ///< P18_3 / GPTn / GTIOC3B
    IOPORT_PIN_P183_PFC_04_GTIOC2B         = (0x04U << IOPORT_PFC_OFFSET), ///< P18_3 / GPTn / GTIOC2B
    IOPORT_PIN_P183_PFC_05_CMTW1_TIC1      = (0x05U << IOPORT_PFC_OFFSET), ///< P18_3 / CMTWn / CMTW1_TIC1
    IOPORT_PIN_P183_PFC_06_CANRXDP1        = (0x06U << IOPORT_PFC_OFFSET), ///< P18_3 / CANFDn / CANRXDP1
    IOPORT_PIN_P183_PFC_08_XSPI1_IO4       = (0x08U << IOPORT_PFC_OFFSET), ///< P18_3 / XSPIn / XSPI1_IO4
    IOPORT_PIN_P183_PFC_09_ETH2_CRS        = (0x09U << IOPORT_PFC_OFFSET), ///< P18_3 / ETHER_ETHn / ETH2_CRS
    IOPORT_PIN_P183_PFC_0A_CKE             = (0x0AU << IOPORT_PFC_OFFSET), ///< P18_3 / BSC / CKE
    IOPORT_PIN_P184_PFC_00_IRQ1            = (0x00U << IOPORT_PFC_OFFSET), ///< P18_4 / IRQ / IRQ1
    IOPORT_PIN_P184_PFC_01_MTIC5U          = (0x01U << IOPORT_PFC_OFFSET), ///< P18_4 / MTU3n / MTIC5U
    IOPORT_PIN_P184_PFC_02_TXD4_SDA4_MOSI4 = (0x02U << IOPORT_PFC_OFFSET), ///< P18_4 / SCIn / TXD4_SDA4_MOSI4
    IOPORT_PIN_P184_PFC_03_SPI_RSPCK2      = (0x03U << IOPORT_PFC_OFFSET), ///< P18_4 / SPIn / SPI_RSPCK2
    IOPORT_PIN_P184_PFC_05_XSPI1_IO5       = (0x05U << IOPORT_PFC_OFFSET), ///< P18_4 / XSPIn / XSPI1_IO5
    IOPORT_PIN_P184_PFC_06_ETH1_CRS        = (0x06U << IOPORT_PFC_OFFSET), ///< P18_4 / ETHER_ETHn / ETH1_CRS
    IOPORT_PIN_P184_PFC_07_CAS             = (0x07U << IOPORT_PFC_OFFSET), ///< P18_4 / BSC / CAS
    IOPORT_PIN_P184_PFC_08_CANTX0          = (0x08U << IOPORT_PFC_OFFSET), ///< P18_4 / CANFDn / CANTX0
    IOPORT_PIN_P185_PFC_00_TRACECTL        = (0x00U << IOPORT_PFC_OFFSET), ///< P18_5 / TRACE / TRACECTL
    IOPORT_PIN_P185_PFC_01_MTIC5V          = (0x01U << IOPORT_PFC_OFFSET), ///< P18_5 / MTU3n / MTIC5V
    IOPORT_PIN_P185_PFC_02_RXD4_SCL4_MISO4 = (0x02U << IOPORT_PFC_OFFSET), ///< P18_5 / SCIn / RXD4_SCL4_MISO4
    IOPORT_PIN_P185_PFC_03_SPI_MOSI2       = (0x03U << IOPORT_PFC_OFFSET), ///< P18_5 / SPIn / SPI_MOSI2
    IOPORT_PIN_P185_PFC_05_XSPI1_IO6       = (0x05U << IOPORT_PFC_OFFSET), ///< P18_5 / XSPIn / XSPI1_IO6
    IOPORT_PIN_P185_PFC_06_ETH2_COL        = (0x06U << IOPORT_PFC_OFFSET), ///< P18_5 / ETHER_ETHn / ETH2_COL
    IOPORT_PIN_P185_PFC_07_RAS             = (0x07U << IOPORT_PFC_OFFSET), ///< P18_5 / BSC / RAS
    IOPORT_PIN_P185_PFC_08_CANRX0          = (0x08U << IOPORT_PFC_OFFSET), ///< P18_5 / CANFDn / CANRX0
    IOPORT_PIN_P186_PFC_00_IRQ11           = (0x00U << IOPORT_PFC_OFFSET), ///< P18_6 / IRQ / IRQ11
    IOPORT_PIN_P186_PFC_01_TRACECLK        = (0x01U << IOPORT_PFC_OFFSET), ///< P18_6 / TRACE / TRACECLK
    IOPORT_PIN_P186_PFC_02_MTIC5W          = (0x02U << IOPORT_PFC_OFFSET), ///< P18_6 / MTU3n / MTIC5W
    IOPORT_PIN_P186_PFC_03_ADTRG0          = (0x03U << IOPORT_PFC_OFFSET), ///< P18_6 / ADCn / ADTRG0
    IOPORT_PIN_P186_PFC_04_SCK4            = (0x04U << IOPORT_PFC_OFFSET), ///< P18_6 / SCIn / SCK4
    IOPORT_PIN_P186_PFC_05_SPI_MISO2       = (0x05U << IOPORT_PFC_OFFSET), ///< P18_6 / SPIn / SPI_MISO2
    IOPORT_PIN_P186_PFC_06_IIC_SCL2        = (0x06U << IOPORT_PFC_OFFSET), ///< P18_6 / IICn / IIC_SCL2
    IOPORT_PIN_P186_PFC_08_XSPI1_IO7       = (0x08U << IOPORT_PFC_OFFSET), ///< P18_6 / XSPIn / XSPI1_IO7
    IOPORT_PIN_P186_PFC_09_ETH1_COL        = (0x09U << IOPORT_PFC_OFFSET), ///< P18_6 / ETHER_ETHn / ETH1_COL
    IOPORT_PIN_P186_PFC_0A_DE4             = (0x0AU << IOPORT_PFC_OFFSET), ///< P18_6 / SCIn / DE4
    IOPORT_PIN_P190_PFC_00_USB_VBUSEN      = (0x00U << IOPORT_PFC_OFFSET), ///< P19_0 / USB_HS / USB_VBUSEN
    IOPORT_PIN_P201_PFC_00_ETHSW_TDMAOUT0  = (0x00U << IOPORT_PFC_OFFSET), ///< P20_1 / ETHER_ETHSW / ETHSW_TDMAOUT0
    IOPORT_PIN_P201_PFC_01_ESC_LINKACT0    = (0x01U << IOPORT_PFC_OFFSET), ///< P20_1 / ETHER_ESC / ESC_LINKACT0
    IOPORT_PIN_P201_PFC_02_ETHSW_PTPOUT3   = (0x02U << IOPORT_PFC_OFFSET), ///< P20_1 / ETHER_ETHSW / ETHSW_PTPOUT3
    IOPORT_PIN_P202_PFC_00_ETHSW_TDMAOUT1  = (0x00U << IOPORT_PFC_OFFSET), ///< P20_2 / ETHER_ETHSW / ETHSW_TDMAOUT1
    IOPORT_PIN_P202_PFC_01_ESC_LEDRUN      = (0x01U << IOPORT_PFC_OFFSET), ///< P20_2 / ETHER_ESC / ESC_LEDRUN
    IOPORT_PIN_P202_PFC_02_ESC_LEDSTER     = (0x02U << IOPORT_PFC_OFFSET), ///< P20_2 / ETHER_ESC / ESC_LEDSTER
    IOPORT_PIN_P202_PFC_03_DE3             = (0x03U << IOPORT_PFC_OFFSET), ///< P20_2 / SCIn / DE3
    IOPORT_PIN_P202_PFC_04_ETHSW_PTPOUT2   = (0x04U << IOPORT_PFC_OFFSET), ///< P20_2 / ETHER_ETHSW / ETHSW_PTPOUT2
    IOPORT_PIN_P203_PFC_00_ETHSW_TDMAOUT2  = (0x00U << IOPORT_PFC_OFFSET), ///< P20_3 / ETHER_ETHSW / ETHSW_TDMAOUT2
    IOPORT_PIN_P203_PFC_01_ESC_LEDERR      = (0x01U << IOPORT_PFC_OFFSET), ///< P20_3 / ETHER_ESC / ESC_LEDERR
    IOPORT_PIN_P203_PFC_02_ETHSW_PTPOUT1   = (0x02U << IOPORT_PFC_OFFSET), ///< P20_3 / ETHER_ETHSW / ETHSW_PTPOUT1
    IOPORT_PIN_P204_PFC_00_ETHSW_TDMAOUT3  = (0x00U << IOPORT_PFC_OFFSET), ///< P20_4 / ETHER_ETHSW / ETHSW_TDMAOUT3
    IOPORT_PIN_P204_PFC_01_ESC_LINKACT1    = (0x01U << IOPORT_PFC_OFFSET), ///< P20_4 / ETHER_ESC / ESC_LINKACT1
    IOPORT_PIN_P204_PFC_02_ETHSW_PTPOUT0   = (0x02U << IOPORT_PFC_OFFSET), ///< P20_4 / ETHER_ETHSW / ETHSW_PTPOUT0
    IOPORT_PIN_P211_PFC_00_TRACEDATA0      = (0x00U << IOPORT_PFC_OFFSET), ///< P21_1 / TRACE / TRACEDATA0
    IOPORT_PIN_P211_PFC_01_D0              = (0x01U << IOPORT_PFC_OFFSET), ///< P21_1 / BSC / D0
    IOPORT_PIN_P211_PFC_02_MTIOC6A         = (0x02U << IOPORT_PFC_OFFSET), ///< P21_1 / MTU3n / MTIOC6A
    IOPORT_PIN_P211_PFC_03_GTIOC14A        = (0x03U << IOPORT_PFC_OFFSET), ///< P21_1 / GPTn / GTIOC14A
    IOPORT_PIN_P211_PFC_04_CMTW0_TIC0      = (0x04U << IOPORT_PFC_OFFSET), ///< P21_1 / CMTWn / CMTW0_TIC0
    IOPORT_PIN_P211_PFC_05_SCK5            = (0x05U << IOPORT_PFC_OFFSET), ///< P21_1 / SCIn / SCK5
    IOPORT_PIN_P211_PFC_06_SPI_SSL20       = (0x06U << IOPORT_PFC_OFFSET), ///< P21_1 / SPIn / SPI_SSL20
    IOPORT_PIN_P211_PFC_07_IIC_SCL1        = (0x07U << IOPORT_PFC_OFFSET), ///< P21_1 / IICn / IIC_SCL1
    IOPORT_PIN_P211_PFC_08_MCLK0           = (0x08U << IOPORT_PFC_OFFSET), ///< P21_1 / DSMIFn / MCLK0
    IOPORT_PIN_P211_PFC_0A_ESC_SYNC0       = (0x0AU << IOPORT_PFC_OFFSET), ///< P21_1 / ETHER_ESC / ESC_SYNC0
    IOPORT_PIN_P211_PFC_0B_ESC_SYNC1       = (0x0BU << IOPORT_PFC_OFFSET), ///< P21_1 / ETHER_ESC / ESC_SYNC1
    IOPORT_PIN_P211_PFC_0C_HSPI_INT        = (0x0CU << IOPORT_PFC_OFFSET), ///< P21_1 / SHOSTIF / HSPI_INT
    IOPORT_PIN_P211_PFC_0D_HD0             = (0x0DU << IOPORT_PFC_OFFSET), ///< P21_1 / PHOSTIF / HD0
    IOPORT_PIN_P212_PFC_00_TRACEDATA1      = (0x00U << IOPORT_PFC_OFFSET), ///< P21_2 / TRACE / TRACEDATA1
    IOPORT_PIN_P212_PFC_01_D1              = (0x01U << IOPORT_PFC_OFFSET), ///< P21_2 / BSC / D1
    IOPORT_PIN_P212_PFC_02_MTIOC6B         = (0x02U << IOPORT_PFC_OFFSET), ///< P21_2 / MTU3n / MTIOC6B
    IOPORT_PIN_P212_PFC_03_GTIOC14B        = (0x03U << IOPORT_PFC_OFFSET), ///< P21_2 / GPTn / GTIOC14B
    IOPORT_PIN_P212_PFC_04_CMTW0_TIC1      = (0x04U << IOPORT_PFC_OFFSET), ///< P21_2 / CMTWn / CMTW0_TIC1
    IOPORT_PIN_P212_PFC_05_RXD5_SCL5_MISO5 = (0x05U << IOPORT_PFC_OFFSET), ///< P21_2 / SCIn / RXD5_SCL5_MISO5
    IOPORT_PIN_P212_PFC_06_SPI_MISO2       = (0x06U << IOPORT_PFC_OFFSET), ///< P21_2 / SPIn / SPI_MISO2
    IOPORT_PIN_P212_PFC_07_IIC_SDA1        = (0x07U << IOPORT_PFC_OFFSET), ///< P21_2 / IICn / IIC_SDA1
    IOPORT_PIN_P212_PFC_08_MDAT0           = (0x08U << IOPORT_PFC_OFFSET), ///< P21_2 / DSMIFn / MDAT0
    IOPORT_PIN_P212_PFC_0A_ESC_SYNC0       = (0x0AU << IOPORT_PFC_OFFSET), ///< P21_2 / ETHER_ESC / ESC_SYNC0
    IOPORT_PIN_P212_PFC_0B_ESC_SYNC1       = (0x0BU << IOPORT_PFC_OFFSET), ///< P21_2 / ETHER_ESC / ESC_SYNC1
    IOPORT_PIN_P212_PFC_0C_HD1             = (0x0CU << IOPORT_PFC_OFFSET), ///< P21_2 / PHOSTIF / HD1
    IOPORT_PIN_P213_PFC_00_TRACEDATA2      = (0x00U << IOPORT_PFC_OFFSET), ///< P21_3 / TRACE / TRACEDATA2
    IOPORT_PIN_P213_PFC_01_D2              = (0x01U << IOPORT_PFC_OFFSET), ///< P21_3 / BSC / D2
    IOPORT_PIN_P213_PFC_02_MTIOC6C         = (0x02U << IOPORT_PFC_OFFSET), ///< P21_3 / MTU3n / MTIOC6C
    IOPORT_PIN_P213_PFC_03_GTIOC15A        = (0x03U << IOPORT_PFC_OFFSET), ///< P21_3 / GPTn / GTIOC15A
    IOPORT_PIN_P213_PFC_04_TXD5_SDA5_MOSI5 = (0x04U << IOPORT_PFC_OFFSET), ///< P21_3 / SCIn / TXD5_SDA5_MOSI5
    IOPORT_PIN_P213_PFC_05_SPI_SSL33       = (0x05U << IOPORT_PFC_OFFSET), ///< P21_3 / SPIn / SPI_SSL33
    IOPORT_PIN_P213_PFC_06_MCLK1           = (0x06U << IOPORT_PFC_OFFSET), ///< P21_3 / DSMIFn / MCLK1
    IOPORT_PIN_P213_PFC_08_NMI             = (0x08U << IOPORT_PFC_OFFSET), ///< P21_3 / IRQ / NMI
    IOPORT_PIN_P213_PFC_09_HD2             = (0x09U << IOPORT_PFC_OFFSET), ///< P21_3 / PHOSTIF / HD2
    IOPORT_PIN_P214_PFC_00_TRACEDATA3      = (0x00U << IOPORT_PFC_OFFSET), ///< P21_4 / TRACE / TRACEDATA3
    IOPORT_PIN_P214_PFC_01_D3              = (0x01U << IOPORT_PFC_OFFSET), ///< P21_4 / BSC / D3
    IOPORT_PIN_P214_PFC_02_MTIOC6D         = (0x02U << IOPORT_PFC_OFFSET), ///< P21_4 / MTU3n / MTIOC6D
    IOPORT_PIN_P214_PFC_03_GTIOC15B        = (0x03U << IOPORT_PFC_OFFSET), ///< P21_4 / GPTn / GTIOC15B
    IOPORT_PIN_P214_PFC_04_SS5_CTS5_RTS5   = (0x04U << IOPORT_PFC_OFFSET), ///< P21_4 / SCIn / SS5_CTS5_RTS5
    IOPORT_PIN_P214_PFC_05_SPI_SSL02       = (0x05U << IOPORT_PFC_OFFSET), ///< P21_4 / SPIn / SPI_SSL02
    IOPORT_PIN_P214_PFC_06_MDAT1           = (0x06U << IOPORT_PFC_OFFSET), ///< P21_4 / DSMIFn / MDAT1
    IOPORT_PIN_P214_PFC_08_ETHSW_PTPOUT1   = (0x08U << IOPORT_PFC_OFFSET), ///< P21_4 / ETHER_ETHSW / ETHSW_PTPOUT1
    IOPORT_PIN_P214_PFC_09_ESC_SYNC0       = (0x09U << IOPORT_PFC_OFFSET), ///< P21_4 / ETHER_ESC / ESC_SYNC0
    IOPORT_PIN_P214_PFC_0A_ESC_SYNC1       = (0x0AU << IOPORT_PFC_OFFSET), ///< P21_4 / ETHER_ESC / ESC_SYNC1
    IOPORT_PIN_P214_PFC_0B_HD3             = (0x0BU << IOPORT_PFC_OFFSET), ///< P21_4 / PHOSTIF / HD3
    IOPORT_PIN_P214_PFC_0C_MBX_HINT        = (0x0CU << IOPORT_PFC_OFFSET), ///< P21_4 / MBXSEM / MBX_HINT
    IOPORT_PIN_P215_PFC_00_IRQ6            = (0x00U << IOPORT_PFC_OFFSET), ///< P21_5 / IRQ / IRQ6
    IOPORT_PIN_P215_PFC_01_TRACEDATA4      = (0x01U << IOPORT_PFC_OFFSET), ///< P21_5 / TRACE / TRACEDATA4
    IOPORT_PIN_P215_PFC_02_D4              = (0x02U << IOPORT_PFC_OFFSET), ///< P21_5 / BSC / D4
    IOPORT_PIN_P215_PFC_03_MTIOC7A         = (0x03U << IOPORT_PFC_OFFSET), ///< P21_5 / MTU3n / MTIOC7A
    IOPORT_PIN_P215_PFC_04_GTIOC16A        = (0x04U << IOPORT_PFC_OFFSET), ///< P21_5 / GPTn / GTIOC16A
    IOPORT_PIN_P215_PFC_05_CMTW1_TOC1      = (0x05U << IOPORT_PFC_OFFSET), ///< P21_5 / CMTWn / CMTW1_TOC1
    IOPORT_PIN_P215_PFC_06_ADTRG1          = (0x06U << IOPORT_PFC_OFFSET), ///< P21_5 / ADCn / ADTRG1
    IOPORT_PIN_P215_PFC_07_CTS5            = (0x07U << IOPORT_PFC_OFFSET), ///< P21_5 / SCIn / CTS5
    IOPORT_PIN_P215_PFC_08_SPI_MISO0       = (0x08U << IOPORT_PFC_OFFSET), ///< P21_5 / SPIn / SPI_MISO0
    IOPORT_PIN_P215_PFC_09_MCLK2           = (0x09U << IOPORT_PFC_OFFSET), ///< P21_5 / DSMIFn / MCLK2
    IOPORT_PIN_P215_PFC_0B_HD4             = (0x0BU << IOPORT_PFC_OFFSET), ///< P21_5 / PHOSTIF / HD4
    IOPORT_PIN_P216_PFC_00_IRQ9            = (0x00U << IOPORT_PFC_OFFSET), ///< P21_6 / IRQ / IRQ9
    IOPORT_PIN_P216_PFC_01_TRACEDATA5      = (0x01U << IOPORT_PFC_OFFSET), ///< P21_6 / TRACE / TRACEDATA5
    IOPORT_PIN_P216_PFC_02_D5              = (0x02U << IOPORT_PFC_OFFSET), ///< P21_6 / BSC / D5
    IOPORT_PIN_P216_PFC_03_MTIOC7B         = (0x03U << IOPORT_PFC_OFFSET), ///< P21_6 / MTU3n / MTIOC7B
    IOPORT_PIN_P216_PFC_04_GTIOC16B        = (0x04U << IOPORT_PFC_OFFSET), ///< P21_6 / GPTn / GTIOC16B
    IOPORT_PIN_P216_PFC_05_CTS0            = (0x05U << IOPORT_PFC_OFFSET), ///< P21_6 / SCIn / CTS0
    IOPORT_PIN_P216_PFC_06_TEND            = (0x06U << IOPORT_PFC_OFFSET), ///< P21_6 / DMAC / TEND
    IOPORT_PIN_P216_PFC_07_MDAT2           = (0x07U << IOPORT_PFC_OFFSET), ///< P21_6 / DSMIFn / MDAT2
    IOPORT_PIN_P216_PFC_08_HD5             = (0x08U << IOPORT_PFC_OFFSET), ///< P21_6 / PHOSTIF / HD5
    IOPORT_PIN_P217_PFC_00_IRQ10           = (0x00U << IOPORT_PFC_OFFSET), ///< P21_7 / IRQ / IRQ10
    IOPORT_PIN_P217_PFC_01_TRACEDATA6      = (0x01U << IOPORT_PFC_OFFSET), ///< P21_7 / TRACE / TRACEDATA6
    IOPORT_PIN_P217_PFC_02_D6              = (0x02U << IOPORT_PFC_OFFSET), ///< P21_7 / BSC / D6
    IOPORT_PIN_P217_PFC_03_MTIOC7C         = (0x03U << IOPORT_PFC_OFFSET), ///< P21_7 / MTU3n / MTIOC7C
    IOPORT_PIN_P217_PFC_04_GTIOC17A        = (0x04U << IOPORT_PFC_OFFSET), ///< P21_7 / GPTn / GTIOC17A
    IOPORT_PIN_P217_PFC_05_DE0             = (0x05U << IOPORT_PFC_OFFSET), ///< P21_7 / SCIn / DE0
    IOPORT_PIN_P217_PFC_06_DREQ            = (0x06U << IOPORT_PFC_OFFSET), ///< P21_7 / DMAC / DREQ
    IOPORT_PIN_P217_PFC_07_MCLK3           = (0x07U << IOPORT_PFC_OFFSET), ///< P21_7 / DSMIFn / MCLK3
    IOPORT_PIN_P217_PFC_08_HD6             = (0x08U << IOPORT_PFC_OFFSET), ///< P21_7 / PHOSTIF / HD6
    IOPORT_PIN_P220_PFC_00_IRQ15           = (0x00U << IOPORT_PFC_OFFSET), ///< P22_0 / IRQ / IRQ15
    IOPORT_PIN_P220_PFC_01_TRACEDATA7      = (0x01U << IOPORT_PFC_OFFSET), ///< P22_0 / TRACE / TRACEDATA7
    IOPORT_PIN_P220_PFC_02_D7              = (0x02U << IOPORT_PFC_OFFSET), ///< P22_0 / BSC / D7
    IOPORT_PIN_P220_PFC_03_MTIOC7D         = (0x03U << IOPORT_PFC_OFFSET), ///< P22_0 / MTU3n / MTIOC7D
    IOPORT_PIN_P220_PFC_04_GTIOC17B        = (0x04U << IOPORT_PFC_OFFSET), ///< P22_0 / GPTn / GTIOC17B
    IOPORT_PIN_P220_PFC_05_DE5             = (0x05U << IOPORT_PFC_OFFSET), ///< P22_0 / SCIn / DE5
    IOPORT_PIN_P220_PFC_06_MDAT3           = (0x06U << IOPORT_PFC_OFFSET), ///< P22_0 / DSMIFn / MDAT3
    IOPORT_PIN_P220_PFC_07_HD7             = (0x07U << IOPORT_PFC_OFFSET), ///< P22_0 / PHOSTIF / HD7
    IOPORT_PIN_P221_PFC_00_TRACECTL        = (0x00U << IOPORT_PFC_OFFSET), ///< P22_1 / TRACE / TRACECTL
    IOPORT_PIN_P221_PFC_01_D8              = (0x01U << IOPORT_PFC_OFFSET), ///< P22_1 / BSC / D8
    IOPORT_PIN_P221_PFC_02_ESC_LINKACT2    = (0x02U << IOPORT_PFC_OFFSET), ///< P22_1 / ETHER_ESC / ESC_LINKACT2
    IOPORT_PIN_P221_PFC_03_POE4            = (0x03U << IOPORT_PFC_OFFSET), ///< P22_1 / MTU_POE3 / POE4
    IOPORT_PIN_P221_PFC_04_SS4_CTS4_RTS4   = (0x04U << IOPORT_PFC_OFFSET), ///< P22_1 / SCIn / SS4_CTS4_RTS4
    IOPORT_PIN_P221_PFC_05_HD8             = (0x05U << IOPORT_PFC_OFFSET), ///< P22_1 / PHOSTIF / HD8
    IOPORT_PIN_P221_PFC_06_GTETRGB         = (0x06U << IOPORT_PFC_OFFSET), ///< P22_1 / GPT_POEG / GTETRGB
    IOPORT_PIN_P222_PFC_00_IRQ4            = (0x00U << IOPORT_PFC_OFFSET), ///< P22_2 / IRQ / IRQ4
    IOPORT_PIN_P222_PFC_01_TRACECLK        = (0x01U << IOPORT_PFC_OFFSET), ///< P22_2 / TRACE / TRACECLK
    IOPORT_PIN_P222_PFC_02_D9              = (0x02U << IOPORT_PFC_OFFSET), ///< P22_2 / BSC / D9
    IOPORT_PIN_P222_PFC_03_MTIOC8C         = (0x03U << IOPORT_PFC_OFFSET), ///< P22_2 / MTU3n / MTIOC8C
    IOPORT_PIN_P222_PFC_04_GTETRGSA        = (0x04U << IOPORT_PFC_OFFSET), ///< P22_2 / GPT_POEG / GTETRGSA
    IOPORT_PIN_P222_PFC_05_SPI_SSL12       = (0x05U << IOPORT_PFC_OFFSET), ///< P22_2 / SPIn / SPI_SSL12
    IOPORT_PIN_P222_PFC_07_HD9             = (0x07U << IOPORT_PFC_OFFSET), ///< P22_2 / PHOSTIF / HD9
    IOPORT_PIN_P222_PFC_08_MCLK1           = (0x08U << IOPORT_PFC_OFFSET), ///< P22_2 / DSMIFn / MCLK1
    IOPORT_PIN_P223_PFC_00_D10             = (0x00U << IOPORT_PFC_OFFSET), ///< P22_3 / BSC / D10
    IOPORT_PIN_P223_PFC_01_MTIOC8D         = (0x01U << IOPORT_PFC_OFFSET), ///< P22_3 / MTU3n / MTIOC8D
    IOPORT_PIN_P223_PFC_02_GTETRGSB        = (0x02U << IOPORT_PFC_OFFSET), ///< P22_3 / GPT_POEG / GTETRGSB
    IOPORT_PIN_P223_PFC_04_RXD5_SCL5_MISO5 = (0x04U << IOPORT_PFC_OFFSET), ///< P22_3 / SCIn / RXD5_SCL5_MISO5
    IOPORT_PIN_P223_PFC_05_HD10            = (0x05U << IOPORT_PFC_OFFSET), ///< P22_3 / PHOSTIF / HD10
    IOPORT_PIN_P237_PFC_00_ETH2_RXD0       = (0x00U << IOPORT_PFC_OFFSET), ///< P23_7 / ETHER_ETHn / ETH2_RXD0
    IOPORT_PIN_P237_PFC_02_D11             = (0x02U << IOPORT_PFC_OFFSET), ///< P23_7 / BSC / D11
    IOPORT_PIN_P237_PFC_03_BS              = (0x03U << IOPORT_PFC_OFFSET), ///< P23_7 / BSC / BS
    IOPORT_PIN_P237_PFC_04_MTIOC0A         = (0x04U << IOPORT_PFC_OFFSET), ///< P23_7 / MTU3n / MTIOC0A
    IOPORT_PIN_P237_PFC_05_GTETRGA         = (0x05U << IOPORT_PFC_OFFSET), ///< P23_7 / GPT_POEG / GTETRGA
    IOPORT_PIN_P237_PFC_06_SCK1            = (0x06U << IOPORT_PFC_OFFSET), ///< P23_7 / SCIn / SCK1
    IOPORT_PIN_P237_PFC_07_MCLK4           = (0x07U << IOPORT_PFC_OFFSET), ///< P23_7 / DSMIFn / MCLK4
    IOPORT_PIN_P237_PFC_09_HD11            = (0x09U << IOPORT_PFC_OFFSET), ///< P23_7 / PHOSTIF / HD11
    IOPORT_PIN_P240_PFC_00_ETH2_RXD1       = (0x00U << IOPORT_PFC_OFFSET), ///< P24_0 / ETHER_ETHn / ETH2_RXD1
    IOPORT_PIN_P240_PFC_02_D12             = (0x02U << IOPORT_PFC_OFFSET), ///< P24_0 / BSC / D12
    IOPORT_PIN_P240_PFC_03_CKE             = (0x03U << IOPORT_PFC_OFFSET), ///< P24_0 / BSC / CKE
    IOPORT_PIN_P240_PFC_04_MTIOC0B         = (0x04U << IOPORT_PFC_OFFSET), ///< P24_0 / MTU3n / MTIOC0B
    IOPORT_PIN_P240_PFC_05_GTETRGB         = (0x05U << IOPORT_PFC_OFFSET), ///< P24_0 / GPT_POEG / GTETRGB
    IOPORT_PIN_P240_PFC_06_RXD1_SCL1_MISO1 = (0x06U << IOPORT_PFC_OFFSET), ///< P24_0 / SCIn / RXD1_SCL1_MISO1
    IOPORT_PIN_P240_PFC_07_DREQ            = (0x07U << IOPORT_PFC_OFFSET), ///< P24_0 / DMAC / DREQ
    IOPORT_PIN_P240_PFC_08_MDAT4           = (0x08U << IOPORT_PFC_OFFSET), ///< P24_0 / DSMIFn / MDAT4
    IOPORT_PIN_P240_PFC_0A_HD12            = (0x0AU << IOPORT_PFC_OFFSET), ///< P24_0 / PHOSTIF / HD12
    IOPORT_PIN_P241_PFC_00_ETH2_RXCLK      = (0x00U << IOPORT_PFC_OFFSET), ///< P24_1 / ETHER_ETHn / ETH2_RXCLK
    IOPORT_PIN_P241_PFC_02_D13             = (0x02U << IOPORT_PFC_OFFSET), ///< P24_1 / BSC / D13
    IOPORT_PIN_P241_PFC_03_CAS             = (0x03U << IOPORT_PFC_OFFSET), ///< P24_1 / BSC / CAS
    IOPORT_PIN_P241_PFC_04_MTIOC0C         = (0x04U << IOPORT_PFC_OFFSET), ///< P24_1 / MTU3n / MTIOC0C
    IOPORT_PIN_P241_PFC_05_GTETRGC         = (0x05U << IOPORT_PFC_OFFSET), ///< P24_1 / GPT_POEG / GTETRGC
    IOPORT_PIN_P241_PFC_06_POE8            = (0x06U << IOPORT_PFC_OFFSET), ///< P24_1 / MTU_POE3 / POE8
    IOPORT_PIN_P241_PFC_07_MCLK5           = (0x07U << IOPORT_PFC_OFFSET), ///< P24_1 / DSMIFn / MCLK5
    IOPORT_PIN_P241_PFC_09_HD13            = (0x09U << IOPORT_PFC_OFFSET), ///< P24_1 / PHOSTIF / HD13
    IOPORT_PIN_P242_PFC_00_ETH2_RXD2       = (0x00U << IOPORT_PFC_OFFSET), ///< P24_2 / ETHER_ETHn / ETH2_RXD2
    IOPORT_PIN_P242_PFC_02_D14             = (0x02U << IOPORT_PFC_OFFSET), ///< P24_2 / BSC / D14
    IOPORT_PIN_P242_PFC_03_RAS             = (0x03U << IOPORT_PFC_OFFSET), ///< P24_2 / BSC / RAS
    IOPORT_PIN_P242_PFC_04_MTIOC0D         = (0x04U << IOPORT_PFC_OFFSET), ///< P24_2 / MTU3n / MTIOC0D
    IOPORT_PIN_P242_PFC_05_GTETRGD         = (0x05U << IOPORT_PFC_OFFSET), ///< P24_2 / GPT_POEG / GTETRGD
    IOPORT_PIN_P242_PFC_06_TXD1_SDA1_MOSI1 = (0x06U << IOPORT_PFC_OFFSET), ///< P24_2 / SCIn / TXD1_SDA1_MOSI1
    IOPORT_PIN_P242_PFC_07_MDAT5           = (0x07U << IOPORT_PFC_OFFSET), ///< P24_2 / DSMIFn / MDAT5
    IOPORT_PIN_P242_PFC_09_HD14            = (0x09U << IOPORT_PFC_OFFSET), ///< P24_2 / PHOSTIF / HD14

    /** Marks end of enum - used by parameter checking */
    IOPORT_PERIPHERAL_END
} ioport_pin_pfc_t;

/** Options to configure pin functions  */
typedef enum e_ioport_cfg_options
{
    IOPORT_CFG_PORT_DIRECTION_HIZ          = 0x00000000 << IOPORT_PM_OFFSET, ///< Sets the pin direction to Hi-Z
    IOPORT_CFG_PORT_DIRECTION_INPUT        = 0x00000001 << IOPORT_PM_OFFSET, ///< Sets the pin direction to input (default)
    IOPORT_CFG_PORT_DIRECTION_OUTPUT       = 0x00000002 << IOPORT_PM_OFFSET, ///< Sets the pin direction to output
    IOPORT_CFG_PORT_DIRECTION_OUTPUT_INPUT = 0x00000003 << IOPORT_PM_OFFSET, ///< Sets the pin direction to output (data is input to input buffer)
    IOPORT_CFG_PORT_OUTPUT_LOW             = 0x00000000 << IOPORT_P_OFFSET,  ///< Sets the pin level to low
    IOPORT_CFG_PORT_OUTPUT_HIGH            = 0x00000001 << IOPORT_P_OFFSET,  ///< Sets the pin level to high
    IOPORT_CFG_PORT_GPIO               = 0x00000000 << IOPORT_PMC_OFFSET,    ///< Enables pin to operate as an GPIO pin
    IOPORT_CFG_PORT_PERI               = 0x00000001 << IOPORT_PMC_OFFSET,    ///< Enables pin to operate as a peripheral pin
    IOPORT_CFG_DRIVE_LOW               = 0x00000000 << IOPORT_DRCTL_OFFSET,  ///< Sets pin drive output to low
    IOPORT_CFG_DRIVE_MID               = 0x00000001 << IOPORT_DRCTL_OFFSET,  ///< Sets pin drive output to medium
    IOPORT_CFG_DRIVE_HIGH              = 0x00000002 << IOPORT_DRCTL_OFFSET,  ///< Sets pin drive output to high
    IOPORT_CFG_DRIVE_UHIGH             = 0x00000003 << IOPORT_DRCTL_OFFSET,  ///< Sets pin drive output to ultra high
    IOPORT_CFG_PULLUP_DOWN_DISABLE     = 0x00000000 << IOPORT_DRCTL_OFFSET,  ///< Disables the pin's pull-up / pull-down
    IOPORT_CFG_PULLUP_ENABLE           = 0x00000004 << IOPORT_DRCTL_OFFSET,  ///< Enables the pin's internal pull-up
    IOPORT_CFG_PULLDOWN_ENABLE         = 0x00000008 << IOPORT_DRCTL_OFFSET,  ///< Enables the pin's pull-down
    IOPORT_CFG_SCHMITT_TRIGGER_DISABLE = 0x00000000 << IOPORT_DRCTL_OFFSET,  ///< Disables schmitt trigger input
    IOPORT_CFG_SCHMITT_TRIGGER_ENABLE  = 0x00000010 << IOPORT_DRCTL_OFFSET,  ///< Enables schmitt trigger input
    IOPORT_CFG_SLEW_RATE_SLOW          = 0x00000000 << IOPORT_DRCTL_OFFSET,  ///< Sets the slew rate to slow
    IOPORT_CFG_SLEW_RATE_FAST          = 0x00000020 << IOPORT_DRCTL_OFFSET,  ///< Sets the slew rate to fast
    IOPORT_CFG_REGION_SAFETY           = 0x00000000 << IOPORT_RSELP_OFFSET,  ///< Selects safety region
    IOPORT_CFG_REGION_NSAFETY          = 0x00000001 << IOPORT_RSELP_OFFSET,  ///< Selects non safety region
    IOPORT_CFG_PIM_TTL                 = 0x00000020,                         ///< This macro has been unsupported
    IOPORT_CFG_NMOS_ENABLE             = 0x00000040,                         ///< This macro has been unsupported
    IOPORT_CFG_PMOS_ENABLE             = 0x00000080,                         ///< This macro has been unsupported
    IOPORT_CFG_DRIVE_HS_HIGH           = 0x00000800,                         ///< This macro has been unsupported
    IOPORT_CFG_DRIVE_MID_IIC           = 0x00000C00,                         ///< This macro has been unsupported
    IOPORT_CFG_EVENT_RISING_EDGE       = 0x00001000,                         ///< This macro has been unsupported
    IOPORT_CFG_EVENT_FALLING_EDGE      = 0x00002000,                         ///< This macro has been unsupported
    IOPORT_CFG_EVENT_BOTH_EDGES        = 0x00003000,                         ///< This macro has been unsupported
    IOPORT_CFG_IRQ_ENABLE              = 0x00004000,                         ///< This macro has been unsupported
    IOPORT_CFG_ANALOG_ENABLE           = 0x00008000,                         ///< This macro has been unsupported
    IOPORT_CFG_PERIPHERAL_PIN          = 0x00010000                          ///< This macro has been unsupported
} ioport_cfg_options_t;

/** Pin identifier and pin configuration value */
typedef struct st_ioport_pin_cfg
{
    uint32_t          pin_cfg;         ///< Pin configuration - Use ioport_cfg_options_t parameters to configure
    bsp_io_port_pin_t pin;             ///< Pin identifier
} ioport_pin_cfg_t;

/** Multiple pin configuration data for loading into multiple registers by R_IOPORT_Open()  */
typedef struct st_ioport_cfg
{
    uint16_t                 number_of_pins; ///< Number of pins for which there is configuration data
    ioport_pin_cfg_t const * p_pin_cfg_data; ///< Pin configuration data
    const void             * p_extend;       ///< Pointer to hardware extend configuration
} ioport_cfg_t;

/** IOPORT control block.  Allocate an instance specific control block to pass into the IOPORT API calls.
 * @par Implemented as
 * - ioport_instance_ctrl_t
 */
typedef void ioport_ctrl_t;

/** IOPort driver structure. IOPort functions implemented at the HAL layer will follow this API. */
typedef struct st_ioport_api
{
    /** Initialize internal driver data and initial pin configurations.  Called during startup.  Do
     * not call this API during runtime.  Use @ref ioport_api_t::pinsCfg for runtime reconfiguration of
     * multiple pins.
     * @par Implemented as
     * - @ref R_IOPORT_Open()
     * @param[in]  p_cfg                Pointer to pin configuration data array.
     */
    fsp_err_t (* open)(ioport_ctrl_t * const p_ctrl, const ioport_cfg_t * p_cfg);

    /** Close the API.
     * @par Implemented as
     * - @ref R_IOPORT_Close()
     *
     * @param[in]   p_ctrl  Pointer to control structure.
     **/
    fsp_err_t (* close)(ioport_ctrl_t * const p_ctrl);

    /** Configure multiple pins.
     * @par Implemented as
     * - @ref R_IOPORT_PinsCfg()
     * @param[in]  p_cfg                Pointer to pin configuration data array.
     */
    fsp_err_t (* pinsCfg)(ioport_ctrl_t * const p_ctrl, const ioport_cfg_t * p_cfg);

    /** Configure settings for an individual pin.
     * @par Implemented as
     * - @ref R_IOPORT_PinCfg()
     * @param[in]  pin                  Pin to be read.
     * @param[in]  cfg                  Configuration options for the pin.
     */
    fsp_err_t (* pinCfg)(ioport_ctrl_t * const p_ctrl, bsp_io_port_pin_t pin, uint32_t cfg);

    /** Read the event input data of the specified pin and return the level.
     * @par Implemented as
     * - @ref R_IOPORT_PinEventInputRead()
     * @param[in]  pin                  Pin to be read.
     * @param[in]  p_pin_event         Pointer to return the event data.
     */
    fsp_err_t (* pinEventInputRead)(ioport_ctrl_t * const p_ctrl, bsp_io_port_pin_t pin, bsp_io_level_t * p_pin_event);

    /** Write pin event data.
     * @par Implemented as
     * - @ref R_IOPORT_PinEventOutputWrite()
     * @param[in]  pin                  Pin event data is to be written to.
     * @param[in]  pin_value            Level to be written to pin output event.
     */
    fsp_err_t (* pinEventOutputWrite)(ioport_ctrl_t * const p_ctrl, bsp_io_port_pin_t pin, bsp_io_level_t pin_value);

    /** Read level of a pin.
     * @par Implemented as
     * - @ref R_IOPORT_PinRead()
     * @param[in]  pin                  Pin to be read.
     * @param[in]  p_pin_value          Pointer to return the pin level.
     */
    fsp_err_t (* pinRead)(ioport_ctrl_t * const p_ctrl, bsp_io_port_pin_t pin, bsp_io_level_t * p_pin_value);

    /** Write specified level to a pin.
     * @par Implemented as
     * - @ref R_IOPORT_PinWrite()
     * @param[in]  pin                  Pin to be written to.
     * @param[in]  level                State to be written to the pin.
     */
    fsp_err_t (* pinWrite)(ioport_ctrl_t * const p_ctrl, bsp_io_port_pin_t pin, bsp_io_level_t level);

    /** Set the direction of one or more pins on a port.
     * @par Implemented as
     * - @ref R_IOPORT_PortDirectionSet()
     * @param[in]  port                 Port being configured.
     * @param[in]  direction_values     Value controlling direction of pins on port (1 - output, 0 - input).
     * @param[in]  mask                 Mask controlling which pins on the port are to be configured.
     */
    fsp_err_t (* portDirectionSet)(ioport_ctrl_t * const p_ctrl, bsp_io_port_t port, ioport_size_t direction_values,
                                   ioport_size_t mask);

    /** Read captured event data for a port.
     * @par Implemented as
     * - @ref R_IOPORT_PortEventInputRead()
     * @param[in]  port                 Port to be read.
     * @param[in]  p_event_data         Pointer to return the event data.
     */
    fsp_err_t (* portEventInputRead)(ioport_ctrl_t * const p_ctrl, bsp_io_port_t port, ioport_size_t * p_event_data);

    /** Write event output data for a port.
     * @par Implemented as
     * - @ref R_IOPORT_PortEventOutputWrite()
     * @param[in]  port                 Port event data will be written to.
     * @param[in]  event_data           Data to be written as event data to specified port.
     * @param[in]  mask_value           Each bit set to 1 in the mask corresponds to that bit's value in event data.
     * being written to port.
     */
    fsp_err_t (* portEventOutputWrite)(ioport_ctrl_t * const p_ctrl, bsp_io_port_t port, ioport_size_t event_data,
                                       ioport_size_t mask_value);

    /** Read states of pins on the specified port.
     * @par Implemented as
     * - @ref R_IOPORT_PortRead()
     * @param[in]  port                 Port to be read.
     * @param[in]  p_port_value         Pointer to return the port value.
     */
    fsp_err_t (* portRead)(ioport_ctrl_t * const p_ctrl, bsp_io_port_t port, ioport_size_t * p_port_value);

    /** Write to multiple pins on a port.
     * @par Implemented as
     * - @ref R_IOPORT_PortWrite()
     * @param[in]  port                 Port to be written to.
     * @param[in]  value                Value to be written to the port.
     * @param[in]  mask                 Mask controlling which pins on the port are written to.
     */
    fsp_err_t (* portWrite)(ioport_ctrl_t * const p_ctrl, bsp_io_port_t port, ioport_size_t value, ioport_size_t mask);

    /** DEPRECATED Return the version of the IOPort driver.
     * @par Implemented as
     * - @ref R_IOPORT_VersionGet()
     * @param[out]  p_data              Memory address to return version information to.
     */
    fsp_err_t (* versionGet)(fsp_version_t * p_data);
} ioport_api_t;

/** This structure encompasses everything that is needed to use an instance of this interface. */
typedef struct st_ioport_instance
{
    ioport_ctrl_t      * p_ctrl;       ///< Pointer to the control structure for this instance
    ioport_cfg_t const * p_cfg;        ///< Pointer to the configuration structure for this instance
    ioport_api_t const * p_api;        ///< Pointer to the API structure for this instance
} ioport_instance_t;

/* Common macro for FSP header files. There is also a corresponding FSP_HEADER macro at the top of this file. */
FSP_FOOTER

#endif

/*******************************************************************************************************************//**
 * @} (end defgroup IOPORT_API)
 **********************************************************************************************************************/
