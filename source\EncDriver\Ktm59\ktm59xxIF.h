#ifndef __KTM59XX_H
#define __KTM59XX_H

/**
 * @file ktm59xx.h
 * @brief KTM59xx芯片驱动头文件
 * @details 定义了操作KTM59xx芯片的命令码、数据结构和函数接口
 */

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stdint.h"
#include "Encoder.h"

/**
 * @brief KTM59xx芯片命令码定义
 */
#define KTM59_READ_ANGLE                     0x2300000000000000  /**< 读取角度命令码 */
#define KTM59_WRITE_GEARBOX                  0xCB00000000000000  /**< 写入齿轮箱参数命令码 */
#define KTM59_WRITE_REG                      0x5B000000          /**< 写寄存器命令码 */
#define KTM59_READ_REG                       0x62000000          /**< 读寄存器命令码 */
#define KTM59_NULL_WRITE                     0x46000000          /**< 空写命令码 */
#define KTM59_NULL_READ                      0x15000000          /**< 空读命令码 */
  
  

/**
 * @brief KTM59xx芯片数据长度定义
 */
#define KTM59_ANGLE_DATA_MAX_LENGTH          64                  /**< 角度数据最大长度（64位） */

/**
 * @brief 角度数据结构体
 * @details 存储从KTM59xx芯片读取的角度值、圈数和状态信息
 */
typedef struct
{
  uint32_t Angle;            /**< 角度值（24位） */
  uint32_t RevolutionCount;  /**< 圈数计数 */
  uint8_t  AngleStatus;      /**< 状态信息（2位） */
} Angle_T;

/**
 * @brief SPI数据发送函数
 * @param[in] ptxdata 发送数据缓冲区指针
 * @param[in] txsize 发送数据大小
 * @param[in] rxsize 接收数据大小
 * @return None
 */
void KTM59_SPI_Transmit(uint32_t *ptxdata, uint16_t txsize, uint16_t rxsize);

/**
 * @brief SPI数据接收函数
 * @param[out] prxdata 接收数据缓冲区指针
 * @param[in] size 接收数据大小
 * @return uint8_t 接收状态（0=成功，1=失败）
 */
uint8_t KTM59_SPI_Receive(uint32_t *prxdata, uint16_t size);

/**
 * @brief SPI数据发送和接收函数
 * @param[in] ptxdata 发送数据缓冲区指针
 * @param[out] prxdata 接收数据缓冲区指针
 * @param[in] txsize 发送数据大小
 * @param[in] rxsize 接收数据大小
 * @return None
 */
uint8_t KTM59_SPI_TransmitReceive(uint32_t *ptxdata, uint32_t *prxdata, uint16_t txsize, uint16_t rxsize);

/**
 * @brief 写KTM59xx寄存器
 * @param[in] addr 寄存器地址
 * @param[in] data 写入的数据
 * @return uint8_t 写入状态（1=成功，0=失败）
 */
uint8_t KTM59_WriteReg(uint16_t addr, uint16_t data);

/**
 * @brief 读KTM59xx寄存器
 * @param[in] addr 寄存器地址
 * @return uint8_t 读取的寄存器值
 */
uint8_t KTM59_ReadReg(uint16_t addr);

/**
 * @brief 发送读取角度命令
 * @return None
 */
void KTM59_ReadAngleCmd(void);

/**
 * @brief 接收角度数据
 * @param[out] prxdata 接收数据缓冲区指针
 * @return uint8_t 接收状态（0=成功，1=失败）
 */
uint8_t KTM59_ReadAngle(uint32_t *prxdata);

/**
 * @brief 写入齿轮箱参数
 * @param[in] cycCount 循环计数值
 * @param[in] revCount 圈数计数值
 * @return None
 */
void KTM59_WriteGearBox(uint16_t cycCount, uint32_t revCount);

/**
 * @brief 读取角度值并进行CRC校验
 * @param[in] input 输入数据缓冲区指针
 * @param[out] singleTrun 单圈角度值指针
 * @param[out] muiltTrun 多圈角度值指针
 * @param[out] status 状态信息指针
 * @return uint8_t 校验结果（1=校验成功，0=校验失败）
 */
uint8_t KTM59_ReadAngleDecoding(uint32_t *input, int32_t *singleTrun, int32_t *muiltTrun, uint16_t *status);


/**
 * @brief 初始化编码器-只在初始化伺服的时候写一次
 * @return 0:成功 1:失败
 */
uint8_t KTM59_Init(void);

/**
 * @brief 出厂初始化编码器-只在初始化伺服的时候写一次
 */
uint8_t hApi_KTM59_Factory_Init(void);  


/**
 * @brief 出厂自校准
 * @return 0:成功 1:失败
 */
uint8_t hApi_KTM59_Factory_AutoCalib(void);


/**
 * @brief 出厂手动线性校准
 * @return 0:成功 1:失败
 */
uint8_t hApi_KTM59_Factory_ManualCalib(void);


/**
 * @brief 初始化编码器-只在初始化伺服的时候写一次
 * @return 0:成功 1:失败
 */
uint8_t hApi_IniKtm59(void);

/** 
 * @brief 读取编码器数据
 * @param[out] pdata 读取的数据缓冲区指针
 * @return 0:成功 1:失败
 */
uint8_t hApi_KTM59_GetPos(uint32_t *pdata);

/**
 * @brief 编码器数据解码
 * @param[in] input 输入数据缓冲区指针
 * @param[out] singleTrun 单圈角度值指针
 * @param[out] muiltTrun 多圈角度值指针
 * @param[out] status 状态信息指针
 */
uint8_t hApi_KTM59_PosDecoding(ENCODER *pEnc,ENCODER *pEnc2); 

#ifdef __cplusplus
}
#endif


#endif /* __KTM59XX_H */
