/****************************************************************************************************
 *
 * FILE NAME:  ComUart.h
 *
 * DESCRIPTION:  
 *
 * CREATED ON:  2019.06.27
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	27-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "hal_data.h"
#include "bsp_adc.h"

static void R_S12AD0_Create(void);
static void R_S12AD1_Create(void);
/***********************************************************************************************************************
 * Exported global variables (to be accessed by other files)
 **********************************************************************************************************************/
static void r_adc0_init(void)
{
    unsigned long dummy;

    /* Cancel ADC unit0 stop state in LPC */
    dummy=0u;
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_ADC12, dummy);
    dummy = BSP_MSTP_REG_FSP_IP_ADC12(dummy);
    dummy = R_ADC120->ADCSR;   /* Dummy-read for the module-stop state(2) */
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);

    /* Cancel S12ADC0 module stop state */
    R_SYSC_NS->MSTPCRC_b.MSTPCRC06 = 0U;

    /* Disable and clear interrupt flags of S12AD0 module */
    R_ADC120->ADCSR_b.ADIE     = 0U;
    R_ADC120->ADCSR_b.GBADIE   = 0U;
    R_ADC120->ADCMPCR_b.CMPAIE = 0U;
    R_ADC120->ADCMPCR_b.CMPBIE = 0U;

    /* Set S12AD0 control registers */
    R_ADC120->ADCSR  = _AD_DBLTRIGGER_DISABLE | _AD_SYNC_TRIGGER | _AD_SINGLE_SCAN_MODE;
    R_ADC120->ADCER  = _AD_RESOLUTION_12BIT | _AD_AUTO_CLEARING_DISABLE | _AD_RIGHT_ALIGNMENT;
    R_ADC120->ADADC  = _AD_4_TIME_CONVERSION | _AD_AVERAGE_MODE;
    R_ADC120->ADADS0 = _AD0_ADDAVG_CHANNEL_SELECT;

    /* Set AD conversion start trigger sources */
    R_ADC120->ADSTRGR = _AD_TRSA_TRGA0N;

    /* Set channels and sampling time */
    R_ADC120->ADSSTR[0] = _AD0_SAMPLING_STATE_0;
    R_ADC120->ADSSTR[1] = _AD0_SAMPLING_STATE_1;
    R_ADC120->ADSSTR[2] = _AD0_SAMPLING_STATE_2;
    R_ADC120->ADSSTR[3] = _AD0_SAMPLING_STATE_3;
    R_ADC120->ADANSA0 = _AD0_CHANNEL_SELECT_A;
    R_ADC120->ADSHCR  = _AD0_DSH_CHANNEL_SELECT | _AD0_DSH_SAMPLING_STATE;

    /* Set compare control register */
    R_ADC120->ADCMPCR    = _AD_WINDOWB_DISABLE | _AD_WINDOWA_DISABLE | _AD_WINDOWFUNCTION_DISABLE;
    R_ADC120->ADCMPANSR0 = _AD0_COMPARECHANNEL_SELECT;
    R_ADC120->ADCMPLR0   = _AD0_COMPARELEVEL_SELECT;
    R_ADC120->ADCMPDR0   = 0x0000U;
    R_ADC120->ADCMPDR1   = 0x0000U;

}

/***********************************************************************************************************************
* Function Name: R_S12AD1_Create
* Description  : This function initializes the AD1 converter.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
static void r_adc1_init(void)
{
    unsigned long dummy;

    /* Cancel ADC unit1 stop state in LPC */
    dummy=1u;
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_ADC12, dummy);
    dummy = BSP_MSTP_REG_FSP_IP_ADC12(dummy);
    dummy = R_ADC121->ADCSR;   /* Dummy-read for the module-stop state(2) */
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);

    /* Cancel S12ADC1 module stop state */
    R_SYSC_NS->MSTPCRC_b.MSTPCRC07 = 0U;

    /* Disable and clear interrupt flags of S12AD0 module */
    R_ADC121->ADCSR_b.ADIE     = 0U;
    R_ADC121->ADCSR_b.GBADIE   = 0U;
    R_ADC121->ADCMPCR_b.CMPAIE = 0U;
    R_ADC121->ADCMPCR_b.CMPBIE = 0U;

    /* Set S12AD0 control registers */
    R_ADC121->ADCSR  = _AD_DBLTRIGGER_DISABLE | _AD_SYNC_TRIGGER | _AD_SINGLE_SCAN_MODE;
    R_ADC121->ADCER  = _AD_RESOLUTION_12BIT | _AD_AUTO_CLEARING_DISABLE | _AD_RIGHT_ALIGNMENT;
    R_ADC121->ADADC  = _AD_1_TIME_CONVERSION | _AD_AVERAGE_MODE;
    R_ADC121->ADADS0 = _AD1_ADDAVG_CHANNEL_SELECT;

    /* Set AD conversion start trigger sources */
//    R_ADC121->ADSTRGR = _AD_TRSA_TRGA0N;

    /* Set channels and sampling time */
    R_ADC121->ADSSTR[0] = _AD1_SAMPLING_STATE_0;
    R_ADC121->ADSSTR[1] = _AD1_SAMPLING_STATE_1;
    R_ADC121->ADSSTR[2] = _AD1_SAMPLING_STATE_2;
    R_ADC121->ADSSTR[3] = _AD1_SAMPLING_STATE_3;
    R_ADC121->ADSSTR[4] = _AD1_SAMPLING_STATE_4;
    R_ADC121->ADSSTR[5] = _AD1_SAMPLING_STATE_5;
    R_ADC121->ADSSTR[6] = _AD1_SAMPLING_STATE_6;
    R_ADC121->ADSSTR[7] = _AD1_SAMPLING_STATE_7;
    R_ADC121->ADANSA0 = _AD1_CHANNEL_SELECT_A;
    R_ADC121->ADSHCR  = _AD0_DSH_CHANNEL_SELECT | _AD0_DSH_SAMPLING_STATE;

    /* Set compare control register */
    R_ADC121->ADCMPCR    = _AD_WINDOWB_DISABLE | _AD_WINDOWA_DISABLE | _AD_WINDOWFUNCTION_DISABLE;
    R_ADC121->ADCMPANSR0 = _AD1_COMPARECHANNEL_SELECT;
    R_ADC121->ADCMPLR0   = _AD1_COMPARELEVEL_SELECT;
    R_ADC121->ADCMPDR0   = 0x0000U;
    R_ADC121->ADCMPDR1   = 0x0000U;
    
    R_ADC121->ADCSR_b.ADST = 1U;
    
}
/***********************************************************************************************************************
* Function Name: bsp_usb_init 
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_adc_init(void)
{
    r_adc0_init();
    r_adc1_init();  
    
    R_ADC120->ADCSR_b.TRGE = 1U;
    R_ADC121->ADCSR_b.TRGE = 1U;
}
/***********************************************************************************************************************
* Function Name: bsp_usb_init 
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_adc0_getvalue(ad_channel_t channel, uint16_t * const buffer)
{
    if (channel == ADCHANNEL0)
    {
        *buffer = (uint16_t)(R_ADC120->ADDR[0]);
    }
    else if (channel == ADCHANNEL1)
    {
        *buffer = (uint16_t)(R_ADC120->ADDR[1]);
    }
    else if (channel == ADCHANNEL2)
    {
        *buffer = (uint16_t)(R_ADC120->ADDR[2]);
    }
    else if (channel == ADCHANNEL3)
    {
        *buffer = (uint16_t)(R_ADC120->ADDR[3]);
    }
}


/***********************************************************************************************************************
* Function Name: bsp_adc1_getvalue 
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_adc1_getvalue(ad_channel_t channel, uint16_t * buffer)
{
    if (channel == ADCHANNEL0)
    {
        *buffer = (uint16_t)(R_ADC121->ADDR[0]);
    }
    else if (channel == ADCHANNEL1)
    {
        *buffer = (uint16_t)(R_ADC121->ADDR[1]);
    }
    else if (channel == ADCHANNEL2)
    {
        *buffer = (uint16_t)(R_ADC121->ADDR[2]);
    }
    else if (channel == ADCHANNEL3)
    {
        *buffer = (uint16_t)(R_ADC121->ADDR[3]);
    }
    else if (channel == ADCHANNEL4)
    {
        *buffer = (uint16_t)(R_ADC121->ADDR[4]);
    }
    else if (channel == ADCHANNEL5)
    {
        *buffer = (uint16_t)(R_ADC121->ADDR[5]);
    }
    else if (channel == ADCHANNEL6)
    {
        *buffer = (uint16_t)(R_ADC121->ADDR[6]);
    }
    else if (channel == ADCHANNEL7)
    {
        *buffer = (uint16_t)(R_ADC121->ADDR[7]);
    }
}

/***********************************************************************************************************************
* Function Name: R_S12AD1_Start
* Description  : This function starts the AD1 converter.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void R_S12AD1_Start(void)
{
   R_ADC121->ADCSR_b.ADST = 1U;
}