/****************************************************************************************************
 *
 * FILE NAME:  JatOffLine.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.17
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	17-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _JAT_OFFLINE_H_
#define _J<PERSON>_OFFLINE_H_

#include "BaseDef.h"
#include "ResVibCtrl.h"

/*--------------------------------------------------------------------------------------------------*/
/*	Constant definition																			    */
/*--------------------------------------------------------------------------------------------------*/
#define	JSHBITNUM		7							// Accumulated torque shift number					

/*--------------------------------------------------------------------------------------------------*/
/*	Identification sequence constant definition														*/
/*--------------------------------------------------------------------------------------------------*/
#define	JATINITIAL		0x00						// Inertia unexecuted					
#define	JATCWSTART		0x01						// Forward rotation -> reverse rotation inertia identification execution		
#define	JATCCWSTART		0x02						// Reverse rotation -> forward rotation Inertia identification execution				
#define	JATSUMEND		0x04						// Completion of torque integration					
#define	JATEXEEND		0x08						// Completion of inertia identification				

/*--------------------------------------------------------------------------------------------------*/
/*	Identification status constant definition														*/
/*--------------------------------------------------------------------------------------------------*/
#define	JATINITIAL		0x00						// Inertia unexecuted	
#define	JATCWCOMP		0x01						// Forward rotation -> reverse rotation inertia identification execution complete				
#define	JATCCWCOMP		0x02						// Reverse rotation -> forward rotation inertia identification execution complete				
#define	JATCOMP			(JATCWCOMP|JATCCWCOMP)		// Completion of inertia identification	
#define	JATJRATECOMP	0x04						// Inertia identification value update completed		
#define	JATEXEERR		0x40						// Running error		
#define	JATERR			0x80						// Error status	

/****************************************************************************************************/
/*																									*/
/*		Variables Definition																		*/
/*																									*/
/****************************************************************************************************/
typedef struct 
{
	UINT32	kp;							// Kp:Pn202		
	UINT32	jrate;						// Inertia ratio: Pn205
	UINT32	vffgn;						// Speed FF gain: Pn214	
	UINT32  vffcfg;    
} PRMBK;

typedef struct 
{
	REAL32	mkv;						// Model Kv			
	REAL32	mkvi;						// Model Kvi			
	REAL32	mklpf;						// Model Tf					
	REAL32	mkj;						// Model inertia gain				
	REAL32	mjrate;						// Model inertia					
	REAL32	jfil;						// Identification value update Low-pass filter gain					
	REAL32	jverrfil;					// Speed deviation low-pass filter gain for identification error detection			
} JATP;

typedef struct 
{
	UINT8	enable;						// Inertia executing flag 0: Not executed 1: Running
	UINT8	status;						// Inertia identification status				
	UINT8	jarte_start;				// Inertia identification sequence				
	UINT16	jrate;						// Inertia identification value					[%]		
	REAL32	mnfb;						// Model feedback speed							[2^24/OvrSpd]	
	REAL32	mnfbwrk;					// 										
	REAL32  mvref;						// Model speed command							[2^24/OvrSpd]					
	REAL32	mnerp;						// Model speed deviation (proportional)					
	REAL32	mneri;						// Model speed deviation (integral)			
	REAL32	mtrf;						// Model torque									[2^24/MaxTrq]
	REAL32	mneribf;					//						
	INT32	jverr;						// Speed deviation for identification error		[2^24/OvrSpd]
	INT32	sum1;						// Forward rotation->reverse rotation Actual torque command integrated value				
	INT32	msum1;						// Forward rotation->reverse rotation model torque command integrated value				
	INT32	sum2;						// Reverse rotation->forward rotation Actual torque command integrated value						
	INT32	msum2;						// Reverse rotation->forward rotation model torque command integrated value					
	INT32	j0;							// Forward rotation->reverse rotation Total inertia identification value						
	INT32	j1;							// Total inertia identification value					
	INT32	j2;							// Reverse rotation->forward rotation Total inertia identification value						
	INT32	TargetDist;					// Torque integration start distance (identification movement distance x 1/2) [command unit]
	INT32	fbsum;						// FB position during energy identification (absolute value from the energy start position) [command unit]
	INT32	fbsumrem;					// 													
	BOOL	JatPrmSetReq;				//													
} JATV;

typedef struct 
{
	JATP	jatp;							// Parameters related to inertia identification		
	JATP	jatp_wk;						// Parameters related to inertia identification (for holding parameters)
	JATV	jatv;							// Inertia identification related variables					
	PRMBK	prmbk;							// Related parameter Backup value						
	UINT16	EstimatJrat;					// Load inertia ratio identification value			[%]			
}JATHNDL;


#endif

