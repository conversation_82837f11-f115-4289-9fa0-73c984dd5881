/*
 * bsp_uart.h
 *
 *  Created on: Jun 10, 2022
 *      Author: xgj12
 */

#ifndef BSP_ENC_H_
#define BSP_ENC_H_
#include <stdint.h>

    
#define  FAC_2000KBPS_2CH  200000
#define  FAC_2500KBPS_2CH  250000


#define TMGW_DATA_ID_0 0x02
#define TMGW_DATA_ID_1 0x8A
#define TMGW_DATA_ID_2 0x92
#define TMGW_DATA_ID_3 0x1A          // Read ABS & ABM
#define TMGW_DATA_ID_6 0x32
#define TMGW_DATA_ID_7 0xBA          // Clear erroro
#define TMGW_DATA_ID_8 0xC2
#define TMGW_DATA_ID_C 0x62          // Clear error and multi-turn
#define TMGW_DATA_ID_D 0xEA


#define TMGW_DATA_LENGTH 11





   
/* 绝对式编码器串口初始化和 数据读写 */
void bsp_sci23_init_encoder(uint32_t buadrate, uint8_t axis_num);
void bsp_sci2_3_transmit(uint8_t axis_num ,uint16_t tx_num, uint8_t *pdata);
uint8_t bsp_sci2_3_receive(uint8_t axis_num,uint16_t rx_num, uint8_t *pdata);

void bsp_sci2_3_transmit_timeout(uint16_t axis_num ,uint16_t tx_num, uint8_t *pdata, uint16_t timeout);
uint8_t bsp_sci2_3_receive_timeout(uint16_t axis_num ,uint16_t rx_num, uint8_t *pdata, uint16_t timeout);
   
/* 增量式编码器初始化和 读写 */   
void bsp_mtu1_init(uint8_t mode, uint32_t period);
void bsp_mtu2_init(uint8_t mode, uint32_t period);

uint16_t bsp_opmtu1_cnt(uint8_t operation);
uint16_t bsp_opmtu2_cnt(uint8_t operation);
#endif /* BSP_ENC_H_ */
