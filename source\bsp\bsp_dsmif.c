/***********************************************************************************************************************
 * DIS<PERSON><PERSON>IMER
 * This software is supplied by Renesas Electronics Corporation and is only intended for use with Renesas products. No
 * other uses are authorized. This software is owned by Renesas Electronics Corporation and is protected under all
 * applicable laws, including copyright laws.
 * THIS SOFTWARE IS PROVIDED "AS IS" AND REN<PERSON>AS MAKES NO WARRANTIES REGARDING
 * THIS SOFTWARE, WHETH<PERSON> EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED. TO THE MAXIMUM
 * EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES
 * SHALL BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR ANY REASON RELATED TO THIS
 * SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
 * Renesas reserves the right, without notice, to make changes to this software and to discontinue the availability of
 * this software. By using this software, you agree to the additional terms and conditions found by accessing the
 * following link:
 * http://www.renesas.com/disclaimer
 *
 * Copyright (C) 2021 Renesas Electronics Corporation. All rights reserved.
 **********************************************************************************************************************/
/***********************************************************************************************************************
 **       DSMIF APIs for RZ/T2 
 **       
 **
 **       revision 0.9 (31.Mar.2021)
 **
 **********************************************************************************************************************/

/*******************************************************************************
Includes <System Includes> , "Project Includes"
*******************************************************************************/
#include "hal_data.h"
#include "bsp_dsmif.h"


/*******************************************************************************
*  dsmif  init
*******************************************************************************/
void bsp_dsmif_init(void)
{
    g_dsmif0.p_api->open(g_dsmif0.p_ctrl, g_dsmif0.p_cfg);
    g_dsmif1.p_api->open(g_dsmif1.p_ctrl, g_dsmif1.p_cfg);

    g_dsmif0.p_api->scanStart(g_dsmif0.p_ctrl);
    g_dsmif1.p_api->scanStart(g_dsmif1.p_ctrl);
    
#if 0    
    R_DSMIF0->DSCSTRTR_b.STRTRG0 = 1;
    R_DSMIF0->DSCSTRTR_b.STRTRG1 = 1;
    R_DSMIF0->DSCSTRTR_b.STRTRG2 = 1;


    R_DSMIF1->DSCSTRTR_b.STRTRG0 = 1;
    R_DSMIF1->DSCSTRTR_b.STRTRG1 = 1;
    R_DSMIF1->DSCSTRTR_b.STRTRG2 = 1;
#endif    
}


/*******************************************************************************
*  
*******************************************************************************/
uint16_t bsp_dsmif_read(uint16_t AxisID, uint16_t dsmifchnum)
{
  uint16_t AdcValue = 0; 
    
   if(AxisID == 0)
   {
      if(dsmifchnum == 0)
      {
        AdcValue = R_DSMIF0->CH[0].DSCCDRA_b.CDRA;
      }
      else
      {
#if 0
        AdcValue = R_DSMIF1->CH[1].DSCCDRA_b.CDRA;
#else
        AdcValue = R_DSMIF0->CH[1].DSCCDRA_b.CDRA;
#endif         
        
      }
   }
   else  if(AxisID == 1)
   {
      if(dsmifchnum == 0)
      {
#if 0
        AdcValue = R_DSMIF0->CH[1].DSCCDRA_b.CDRA;
#else
        AdcValue = R_DSMIF1->CH[1].DSCCDRA_b.CDRA;
#endif  
      }
      else
      {
        AdcValue = R_DSMIF1->CH[2].DSCCDRA_b.CDRA;
      }     
   }
   
   return AdcValue;

}



/*******************************************************************************
End of function dsmif_set_scthres
*******************************************************************************/
/* End of File */
