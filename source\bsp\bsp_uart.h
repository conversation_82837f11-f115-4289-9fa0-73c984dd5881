/*
 * bsp_uart.h
 *
 *  Created on: Jun 10, 2022
 *      Author: xgj12
 */

#ifndef BSP_UART_H_
#define BSP_UART_H_
  
   
/** SCI CCR0 register bit masks */
#define SCI_UART_CCR0_IDSEL_MASK              (0x00000400)
#define SCI_UART_CCR0_TEIE_MASK               (0x00200000)
#define SCI_UART_CCR0_RE_MASK                 (0x00000001)
#define SCI_UART_CCR0_TE_MASK                 (0x00000010)
#define SCI_UART_CCR0_RIE_MASK                (0x00010000)
#define SCI_UART_CCR0_TIE_MASK                (0x00100000)  
   
/** SCI CCR1 register bit offsets */
#define SCI_UART_CCR1_CTSE_OFFSET             (0U)
#define SCI_UART_CCR1_SPB2DT_BIT              (4U)
#define SCI_UART_CCR1_OUTPUT_ENABLE_MASK      (0x00000020)
#define SCI_UART_CCR1_PE_OFFSET               (8U)
#define SCI_UART_CCR1_PM_OFFSET               (9U)
#define SCI_UART_CCR1_PM_PE_VALUE_MASK        (0x03U)
#define SCI_UART_CCR1_NFCS_OFFSET             (24U)
#define SCI_UART_CCR1_NFCS_VALUE_MASK         (0x07U)
#define SCI_UART_CCR1_NFEN_OFFSET             (28U)

   
/** SCI CCR3 register bit masks */
#define SCI_UART_CCR3_BPEN_OFFSET             (7U)
#define SCI_UART_CCR3_CHR_OFFSET              (8U)
#define SCI_UART_CCR3_STP_OFFSET              (14U)
#define SCI_UART_CCR3_RxDSEL_OFFSET           (15U)
#define SCI_UART_CCR3_FM_OFFSET               (20U)
#define SCI_UART_CCR3_CKE_OFFSET              (24U)
#define SCI_UART_CCR3_CKE_MASK                (0x03000000U)
#define SCI_UART_CCR3_CKE_VALUE_MASK          (0x03U)
   
/** SCI CSR register receiver error bit masks */
#define SCI_UART_CSR_ORER_MASK                (0x01000000)
#define SCI_UART_CSR_FER_MASK                 (0x10000000)
#define SCI_UART_CSR_PER_MASK                 (0x08000000)
#define SCI_UART_RCVR_ERR_MASK                (SCI_UART_CSR_ORER_MASK | SCI_UART_CSR_FER_MASK | SCI_UART_CSR_PER_MASK)


/** SCI CFCLR register receiver clear error bit masks */
#define SCI_UART_CFCLR_ORERC_MASK             (0x01000000)
#define SCI_UART_CFCLR_FERC_MASK              (0x10000000)
#define SCI_UART_CFCLE_PERC_MASK              (0x08000000)
#define SCI_UART_RCVR_ERRCLR_MASK             (SCI_UART_CFCLR_ORERC_MASK | SCI_UART_CFCLR_FERC_MASK | \
                                               SCI_UART_CFCLE_PERC_MASK)   
 
#define SCI_UART_CFCLR_ALL_FLAG_CLEAR         (0xBD070010)
#define SCI_UART_FFCLR_ALL_FLAG_CLEAR         (0x00000001)


/* FIFO buffer maximum size */
#define _SCIF_FIFO_MAX_SIZE                     (0x10U) /* Size of 16-stage FIFO buffer */




#define SCI_BR_4K     0  // 4800
#define SCI_BR_9K     1  // 9600
#define SCI_BR_19K    2  // 19200
#define SCI_BR_38K    3  // 38400
#define SCI_BR_57K    4  // 57600
#define SCI_BR_115K   5  // 115200
#define SCI_BR_2500K  6  // 2500000


#define SCIFA_UART_SUCCESS          (0)
#define SCIFA_UART_ERR              (-1)
#define SCIFA_UART_BUSY             (-2)
/***********************************************************************************************************************
Macro definitions
***********************************************************************************************************************/
#define _SCIF_RX_TRIG_NUM_0                    (0x08U) /* Receive FIFO data trigger number */

void bsp_sci_buadtrate(R_SCI0_Type * regBase, uint32_t buadrate);
void bsp_sci_format(R_SCI0_Type * regBase, uint8_t format);

void bsp_sci0_init_ss(uint32_t buadrate, uint8_t format);
void bsp_sci4_init_zx(uint32_t buadrate, uint8_t format);



void sci_sci0_dma_tx(uint8_t *txBuf, uint16_t length);
void sci_sci4_dma_tx(uint8_t *txBuf, uint16_t length);


void bsp_sci0_receive(uint8_t * rx_buf, uint16_t rx_num);
void bsp_sci0_Send(uint8_t * tx_buf, uint16_t tx_num);
int32_t userdef_scifa0_uart_receive (uint8_t *rx_buf,uint16_t rx_num, uint32_t Timeout);
int32_t userdef_scifa0_uart_send (uint8_t *tx_buf,uint16_t tx_num, uint32_t Timeout);

void bsp_sci4_receive(uint8_t * rx_buf, uint16_t rx_num);
void bsp_sci4_Send(uint8_t * tx_buf, uint16_t tx_num);
int32_t userdef_scifa4_uart_receive (uint8_t *rx_buf,uint16_t rx_num, uint32_t Timeout);
int32_t userdef_scifa4_uart_send (uint8_t *tx_buf,uint16_t tx_num, uint32_t Timeout);

void UART_TO_PC_STOP(void);
#endif /* BSP_UART_H_ */
