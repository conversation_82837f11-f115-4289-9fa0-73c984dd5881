/****************************************************************************************************
 *
 * FILE NAME:  FunManager.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FUN_MANAGER_H_
#define _FUN_MANAGER_H_

#include "RegAccessIf.h"


/*--------------------------------------------------------------------------------------------------*/
/*	 Definition of the application data																*/
/*--------------------------------------------------------------------------------------------------*/
typedef enum 
{
	FCMD_STOP	= 0x00,					// Stop			
	FCMD_EXEC	= 0x01,					// Execution		
	FCMD_UP		= 0x02,					// UP, forward rotation, forward direction
	FCMD_DOWN	= 0x03,					// DOWN, reverse rotation, reverse direction
	FCMD_SV		= 0x04,					// Servo on / off*/
	FCMD_CH1	= 0x05,					// CH1,U phase selection		
	FCMD_CH2	= 0x06,					// CH2,V phase selection		
	FCMD_EWRT	= 0x07,					// EEPROM write processing		
	FCMD_EXEC2	= 0x08,					// Auxiliary execution		
	FCMD_MTEXEC	= 0x11,					// Maintain operation mode (execute)	
	FCMD_MTUP	= 0x12,					// Maintain operation mode (normal rotation)
	FCMD_MTDOWN	= 0x13,					// Maintain operation mode (reverse)
	FCMD_NONE	= 0xFF,					// No command
} FCMD_CODE;

/*--------------------------------------------------------------------------------------------------*/
/*	 Definition of the number of Function mode														*/
/*--------------------------------------------------------------------------------------------------*/
#define FUN_MODE_NUM	2
typedef enum 
{
	FUN_CH1 = 0,	/* the first mode */
	FUN_CH2 = 1,	/* the second mode */
} FUN_EXECH;


/*--------------------------------------------------------------------------------------------------*/
/*	 Members that can be referenced/operated on the auxiliary function application side				*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{ 
	FCMD_CODE	CmdCode;				/* Instruction code */
	UINT32		State;					/* Sequence status managed by Fn app*/
										/*(0 reset at Fn mode start) */
	UINT32		LongTimer;				/* Timer for Fn app */
	BOOL		HoldFnMode;				/* Fn mode continuation flag */
	PRM_RSLT	ErrorResult;			/* Fn mode execution result */
} FUNEXE;

/*--------------------------------------------------------------------------------------------------*/
/*	  Parameter writing restriction table definition when executing auxiliary function				*/
/*--------------------------------------------------------------------------------------------------*/
#define FUNWRTDISFN_MAX 20				/* Fn number maximum registration number					*/
#define FUNWRTDISPN_MAX 40				/* Pn number maximum registration number					*/

typedef struct 
{
	UINT16 FnNo ;						/* Write protected Fn number				*/
	UINT16 FnMode ;						/* Register number for write protection		*/
	UINT16 PnNo[FUNWRTDISPN_MAX] ;		/* Write-protected Pn number				*/
} FUNWRTDISFNPNTbl ;


/*--------------------------------------------------------------------------------------------------*/
/*	  Structure of the Function Table Attribute														*/
/*--------------------------------------------------------------------------------------------------*/
typedef PRM_RSLT (*FUN_ENTER_CALLBACK)(FUNEXE*, void*);
typedef PRM_RSLT (*FUN_EXE_CALLBACK)(FUNEXE*, void*);
typedef void (*FUN_LEAVE_CALLBACK)(FUNEXE*, void*);

typedef struct	
{
	UINT8				RunLevel;		/* No.1/2 OpeReg ParaRun(1(No.1)/2(No.2)/0(NG))		*/
	UINT8				ParaTrace;		/* Executable mode channel bits	*/
	UINT8				AxCommon;		/* Access lock to all axes 	*/
	UINT8				ExePrmProhibit;	/* Access lock in Parameter write prohibited mode */
	UINT8				AccessLevel;	/* AccessLevel(USR1/USR2/etc)	*/
	INT8				SName[12];		/* FnXXX Short Name for Display	*/
	FUN_ENTER_CALLBACK	EnterFunc;		/* Begin Function for FnFunc */
	FUN_EXE_CALLBACK	ExecuteFunc;	/* Execute Function for FnFunc */
	FUN_LEAVE_CALLBACK	LeaveFunc;		/* End Function for FnFunc	*/

	INT32				(*OpeBeginFunc)(void *Fmsg);	/* Operator Begin Function				*/
	INT32				(*OpeMainFunc)(void *Fmsg);		/* Operator Main Function				*/
	void				(*OpeEndFunc) (void *Fmsg);		/* Operator End Function				*/

} FUNDEF;

typedef const FUNDEF	CFUNDEF;		/* const FUNDEF Type Definition	*/
typedef CFUNDEF*		CPFUNDEF;		/* const FUNDEF Type Definition	*/

typedef struct 
{
	UINT16	FnNo;					/* FnNo.						*/
	UINT16	FnMode;					/* FnMode for Register I/F		*/
	CFUNDEF	*FunDefAttr;			/* Pointer to FUNDEF(FnFuncDef)	*/
} FUNTBL;

typedef const FUNTBL	CFUNTBL;	/* const FUNTBL Type Definition	*/
typedef CFUNTBL*		CPFUNTBL;	/* const FUNTBL Type Definition	*/

/*--------------------------------------------------------------------------------------------------*/
/*	   Structure of the Utility Function Manager Handle												*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT32		TraceMode;				/* Trace mode */
	BOOL		modeLock;				/* Operation mode locked */
	UINT32		AccessLevel;			/* Access level */
} FUN_AXCOMMON;

typedef struct 
{
	CPFUNTBL	FunTable;			/* Function Table */
	UINT32		NumOfFunTable;		/* Number of the Function Table list */

	void		*CallbackParam;		/* Callback function parameter */
	FUN_AXCOMMON *AxCommon;			/* Flag information common to all axes */
	void		*RegManager;		/* Register manager handle pointer */

	CPFUNTBL	cFunMode[FUN_MODE_NUM];		/* Pointer to the currently executing FunctionTable */
	FUNEXE		FunExeState[FUN_MODE_NUM];	/* Running state */
} FUN_MANAGER;


/*--------------------------------------------------------------------------------------------------*/
/*	    API functions																				*/
/*--------------------------------------------------------------------------------------------------*/
void FunInitialize(FUN_MANAGER *FunMngr, void *RegMngr,
				   FUN_AXCOMMON *FunAxCommon, CPFUNTBL FunTable, UINT32 TableNum);
PRM_RSLT FunSetOpeModeRegister(FUN_MANAGER *FunMngr, FUN_EXECH ch, UINT32 mode, void* ClbkParam);
PRM_RSLT FunSetTraceModeRegister(FUN_MANAGER *FunMngr, UINT32 mode);
PRM_RSLT FunSetOpeCommandRegister(FUN_MANAGER *FunMngr, FUN_EXECH ch, UINT32 cmd);
void FunRunTimeService(FUN_MANAGER *FunMngr, FUN_EXECH ch);
USHORT FunGetOpeModeRegister(FUN_MANAGER *FunMngr, FUN_EXECH ch);

PRM_RSLT PnCmdJog(void *Axis);
#endif

