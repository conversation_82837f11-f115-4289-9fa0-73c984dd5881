/****************************************************************************************************
 *
 * FILE NAME:  ComRegMngr.c
 *
 * DESCRIPTION:   Register and Parameter Access Interface
 *
 * CREATED ON:  2019.11.19
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	19-11-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseDef.h"
#include "BaseSetting.h"
#include "RegMngr.h"
#include "RegAccessIf.h"
#include "Mlib.h"
#include "sdoserv.h"
#include "Global.h"
#include "def.h"
#include "modbus.h"
//UINT16 ControlSource = 0;


#if 0
/* Fn execution register access definition */
typedef struct 
{
	UINT32 adrs;
	UINT32 data;
} REG_FNACC_TBL;
typedef const REG_FNACC_TBL CREG_FNACC_TBL;


/* Register table definition */
CREG_FNACC_TBL fnSysReset[] = {
	{0x80004000, 0x2006},
	{0x80004002, 0x1}
};
CREG_FNACC_TBL fnAlmHistClr[] = {
	{0x80004000, 0x1006},
	{0x80004002, 0x1}
};
CREG_FNACC_TBL fnReCalcPrm[] = {
	{0x80004000, 0x2008},
	{0x80004002, 0x1}
};
CREG_FNACC_TBL fnInitPrm[] = {
	{0x80004000, 0x1005},
	{0x80004002, 0x1}
};
CREG_FNACC_TBL fnCancelAccess[] = {
	{0x80004004, 0x0}
};
#endif
	

/*--------------------------------------------------------------------------------------------------*/
/*	 Swap processing is common for big and little													*/
/*--------------------------------------------------------------------------------------------------*/
typedef union 
{
	INT32	val;
	struct
	{
		UINT16	us_l;	// word 0
		UINT16	us_h;	// word 1
	}word;
	
	struct
	{
		UINT8	uc_ll;
		UINT8	uc_lh;
		UINT8	uc_hl;
		UINT8	uc_hh;
	}byte;
}REG_SWAP_VAR;


/****************************************************************************************************
* DESCRIPTION:
*			  Copy a LONG data to specified type buffer
* RETURNS:
*
****************************************************************************************************/
PRIVATE void regCopyData(void *dst, INT32 src, UINT32 bsz, UINT32 d_type)
{
	UINT8	*ucwk;
	UINT16	*uswk;
	UINT32	*ulwk;
	UINT32 i, num;

	num = (bsz/d_type);
	if(d_type == 1)
	{
		ucwk = (UINT8*)dst;
		for(i = 0; i < num; i++)
		{	ucwk[i] = (UINT8)((UINT32)src >> (i*8));}
	}
	else if(d_type == 2)
	{
		uswk = (USHORT*)dst;
		for(i = 0; i < num; i++)
		{	uswk[i] = (UINT16)((UINT32)src >> (i*16));}
	}
	else
	{
		ulwk = (UINT32*)dst;
		ulwk[0] = src;
	}
}


/****************************************************************************************************
* DESCRIPTION:
*			  Get a LONG data from specified type buffer
* RETURNS:
*
****************************************************************************************************/
PRIVATE  INT32 regGetData(void *dst, UINT32 bsz, UINT32 d_type)
{
	UINT8	*ucwk;
	UINT16	*uswk;
	INT32	value, num, i;

	num = (bsz/d_type);
	value = 0;
	if(d_type == 1)
	{
		ucwk = (UCHAR*)dst;
		for(i = 0; i < num; i++)
		{	value |= (ucwk[i] << (8*i));}
	}
	else if(d_type == 2)
	{
		uswk = (USHORT*)dst;
		for(i = 0; i < num; i++)
		{	value |= (uswk[i] << (16*i));}
	}
	else
	{
		value = *((INT32*)dst);
	}
	return value;
}


/****************************************************************************************************
* DESCRIPTION:
*			  Swap data in specified type 
* RETURNS:
*
****************************************************************************************************/
PRIVATE void regSwapAllData(INT32 *dst, UINT32 bsz, UINT32 d_type)
{
	REG_SWAP_VAR	wk;
	UINT32 i, num;

	num = (bsz+3)/4;
	if(d_type == 1)
	{
		for(i = 0; i < num; i++)
		{
			wk.val = dst[i];
			dst[i]	= (wk.byte.uc_hh<<24) | (wk.byte.uc_hl<<16)
					| (wk.byte.uc_lh<<8 ) |  wk.byte.uc_ll;
		}
	}
	else if(d_type == 2)
	{
		for(i = 0; i < num; i++)
		{
			wk.val = dst[i];
			dst[i]	= (wk.word.us_h << 16) | wk.word.us_l;
		}
	}
	else
	{
		/* do nothing */
	}
}


/****************************************************************************************************
* DESCRIPTION:
*			  Initialization process
* RETURNS:
*
****************************************************************************************************/
PUBLIC void RegInit( REG_HNDL *hReg, PRMDATA *Prm, void *hRegMngr )
{
	MlibResetByteMemory( hReg, sizeof(REG_HNDL) );

	hReg->Prm = Prm;
	hReg->hSvRegMngr = hRegMngr;

	hReg->accSeq = 0;

}

/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
UINT8 PrmEcatObjRead( UINT16 Index, UINT8 Subindex, UINT32 Size, UINT16 *pData, UINT8 bCompleteAccess )
{
	//PRM_RSLT		rc;
    UINT8		    rc = 0;
	REG_ACC_T 		AccessPrm={0};
	UINT32			accSz=0, prmSz =0;
	UINT8           SubindexTmp = Subindex;
	INT32			rdValue;
    INT32           tmp=0;
    

	while(accSz < Size)
	{
		AccessPrm.MemAddr = (Index<<16) + SubindexTmp;
		RegMngrGetAttrByMemAddr(&AccessPrm);
		
		/* Get Paremter Attribute */
		if(NULL != RegMngrGetAttrByMemAddr(&AccessPrm))
		{
			prmSz = AccessPrm.PrmAttr->DataLength;
			if( Size < (accSz + prmSz) )
			{
				rc = ABORTIDX_PARAM_LENGTH_ERROR;
				break;
			}
            AccessPrm.ArrayIdx = 0;
            AccessPrm.IfHandle = NULL;
			/* Read a Paremter */
			tmp = PrmReadValue(&AccessPrm, &rdValue, prmSz);
			if(tmp == PRM_RSLT_LIMIT_ERR)
			{
                rc = ABORTIDX_VALUE_EXCEEDED;
				return rc;
			}
            else if(tmp == PRM_RSLT_RWACC_ERR)
			{
                rc = ABORTIDX_COMPLETE_ACCESS_NOT_SUPPORTED ;
				return rc;
			}
            else if(tmp < 0)
			{
                rc = ABORTIDX_GENERAL_ERROR;
				return rc;
			}

			regCopyData(&pData[accSz], rdValue, prmSz, 1);
			accSz = accSz + prmSz;
		}
		else
		{
			rc = ABORTIDX_OBJECT_NOT_EXISTING;
			break;
		}
		AccessPrm.MemAddr += prmSz;
		SubindexTmp++;

		if(!bCompleteAccess)
		{
			if(accSz != Size)
			{
				rc = ABORTIDX_PARAM_LENGTH_ERROR;
			}
			break;
		}
	}

    return rc;
	
}


/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
UINT32 PrmCANObjRead( UINT16 Index, UINT8 Subindex, UINT32 *Size, UINT16 *pData, UINT8 bCompleteAccess )
{
	//PRM_RSLT		rc;
  UINT32		    rc = 0;
	REG_ACC_T 		AccessPrm={0};
	UINT32			  accSz=0, prmSz =0;
	UINT8         SubindexTmp = Subindex;
	INT32			    rdValue;
  INT32         tmp;
 
	while(accSz < *Size)
	{
		AccessPrm.MemAddr = (Index<<16) + SubindexTmp;
		RegMngrGetAttrByMemAddr(&AccessPrm);
		
		/* Get Paremter Attribute */
		if(NULL != RegMngrGetAttrByMemAddr(&AccessPrm))
		{
			prmSz = AccessPrm.PrmAttr->DataLength;
			
			if(*Size == SDO_MAX_LENGTH_TRANSFER)
			{
				*Size = prmSz;
			}
			
			if( *Size < (accSz + prmSz) )
			{
				rc = OD_LENGTH_DATA_INVALID;
				break;
			}
            AccessPrm.ArrayIdx = 0;
            AccessPrm.IfHandle = NULL;
			/* Read a Paremter */
			tmp = PrmReadValue(&AccessPrm, &rdValue, prmSz);
			if(tmp == PRM_RSLT_LIMIT_ERR)
			{
                rc = OD_VALUE_RANGE_EXCEEDED;
				return rc;
			}
            else if(tmp == PRM_RSLT_RWACC_ERR)
			{
                rc = OD_WRITE_NOT_ALLOWED ;
				return rc;
			}
            else if(tmp < 0)
			{
                rc = SDOABT_GENERAL_ERROR;
				return rc;
			}

			regCopyData(&pData[accSz], rdValue, prmSz, 1);
			accSz = accSz + prmSz;
		}
		else
		{
			rc = OD_NO_SUCH_OBJECT;
			break;
		}
		AccessPrm.MemAddr += prmSz;
		SubindexTmp++;

		if(!bCompleteAccess)
		{
			if(accSz != *Size)
			{
				rc = OD_LENGTH_DATA_INVALID;
			}
			break;
		}
	}

    return rc;
	
}


/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
#if 0
UINT32 PrmModBusObjRead( UINT16 Index, UINT8 Subindex, UINT32 *Size, UINT16 *pData, UINT8 bCompleteAccess )

#endif
UINT32 PrmModBusObjRead(UINT16 *pData, UINT32 Size, REG_ACC_T *AccessPrm)
{
#if 0
    UINT32		    rc = 0;
    REG_ACC_T 		AccessPrm={0};
    UINT32			accSz=0, prmSz =0;
    UINT8           SubindexTmp = Subindex;
    INT32		    rdValue;
    INT32           tmp;
 
    AccessPrm.MemAddr = (Index<<16) + SubindexTmp;
    
    if(0 != RegMngrGetAttrByMemAddr(&AccessPrm))
    {
      
      if(*Size < AccessPrm.PrmAttr->DataLength)
      {
         rc = MODBUS_PRM_ERROR;
      }
      else
      {
          prmSz = AccessPrm.PrmAttr->DataLength;
          AccessPrm.ArrayIdx = 0;
          AccessPrm.IfHandle = NULL;
          /* Read a Paremter */
          tmp = PrmReadValue(&AccessPrm, &rdValue, prmSz);        
        
          if(tmp == PRM_RSLT_LIMIT_ERR)
          {
              rc = MODBUS_PRM_ERROR;
              return rc;
          }
          else if(tmp == PRM_RSLT_RWACC_ERR)
          {
              rc = MODBUS_PRM_ERROR ;
              return rc;
          }
          else if(tmp < 0)
          {
              rc = MODBUS_PRM_ERROR;
              return rc;
          }

          regCopyData(&pData[accSz], rdValue, prmSz, 1);  
          
          *Size = *Size -  AccessPrm.PrmAttr->DataLength;
      }
      
    }
    else
    {
      rc = MODBUS_ADDR_ERROR;
    }
    

    return rc;
#endif
    UINT32		    rc = 0;
    INT32			accSz=0;
    INT32		    rdValue;
    INT32           tmp;

    
    
    AccessPrm->ArrayIdx = 0;
    AccessPrm->IfHandle = NULL;
    /* Read a Paremter */
    tmp = PrmReadValue(AccessPrm, &rdValue, Size);        
    
    if(tmp == PRM_RSLT_LIMIT_ERR)
    {
        rc = MODBUS_PRM_ERROR;
        return rc;
    }
    else if(tmp == PRM_RSLT_RWACC_ERR)
    {
        rc = MODBUS_PRM_ERROR ;
        return rc;
    }
    else if(tmp < 0)
    {
         rc = MODBUS_PRM_ERROR;
        return rc;
    }
    
    regCopyData(&pData[accSz], rdValue, Size, 1);  
    
	return rc;
}
/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
UINT8 PrmUartObjWrite( UINT16 Index, UINT8 Subindex, UINT32 Size, UINT16 *pData, UINT8 bCompleteAccess )
{
	//PRM_RSLT		rc;
    UINT8		    rc = 0;
	REG_ACC_T		AccessPrm={0};
	INT32			prmSz=0, accSz=0;
	UINT8           SubindexTmp = Subindex;
	INT32			wrValue;
    INT32           tmp;
    UINT32          EEPROM_Write;
    EEPROM_Write = Size & 0x80;
    Size = Size & 0x7F;
    
    AXIS_HANDLE *AxisA;
    
    AxisA = (AXIS_HANDLE*)GetAxisHandle(0);
    
    if(((0x6000 == (Index & 0xFF00))||(0x6800 == (Index & 0xFF00))) 
                &&((AxisA[0].Prm->PnCtrlCfgPrm.CtrlSource!=UartCOMCtrl)))
    {
      return ABORTIDX_DATA_CANNOT_BE_ACCESSED_BECAUSE_OF_LOCAL_CONTROL;  
    }      
    else
    {
    
	while(accSz < Size)
	{
		AccessPrm.MemAddr = (Index<<16) + SubindexTmp;
		RegMngrGetAttrByMemAddr(&AccessPrm);
		
		/* Get Paremter Attribute */
		if(NULL != RegMngrGetAttrByMemAddr(&AccessPrm))
		{
			prmSz = AccessPrm.PrmAttr->DataLength;
			if(Size < (accSz + prmSz))
			{
				return ABORTIDX_PARAM_LENGTH_ERROR;
			}
            AccessPrm.ArrayIdx = 0;
            AccessPrm.IfHandle = NULL;
			
			/* Write a Paremter */
            if(EEPROM_Write>0)
            {
                AccessPrm.EepWrite = TRUE;
            }
            else
            {
			AccessPrm.EepWrite = FALSE;
            }
			wrValue = regGetData(&pData[accSz], prmSz, 1);
			tmp = PrmWriteValue(&AccessPrm, &wrValue, prmSz);
			if(tmp == PRM_RSLT_LIMIT_ERR)
			{
                rc = ABORTIDX_VALUE_EXCEEDED;
				return rc;
			}
            else if(tmp == PRM_RSLT_RWACC_ERR)
			{
                rc = ABORTIDX_COMPLETE_ACCESS_NOT_SUPPORTED ;
				return rc;
			}
            else if(tmp < 0)
			{
                 rc = ABORTIDX_GENERAL_ERROR;
				return rc;
			}

			accSz = accSz + prmSz;
		}
		else
		{
			return ABORTIDX_OBJECT_NOT_EXISTING;
		}
		AccessPrm.MemAddr += prmSz;
		SubindexTmp++;

		if(!bCompleteAccess)
		{
			if(accSz != Size)
			{
				rc = ABORTIDX_PARAM_LENGTH_ERROR;
			}
			break;
		}
		
	}
        
         	return rc;
      }

}
/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
UINT8 PrmEcatObjWrite( UINT16 Index, UINT8 Subindex, UINT32 Size, UINT16 *pData, UINT8 bCompleteAccess )
{
	//PRM_RSLT		rc;
    UINT8		    rc = 0;
    REG_ACC_T		AccessPrm={0};
    INT32			prmSz=0, accSz=0;
    UINT8           SubindexTmp = Subindex;
    INT32			wrValue;
    INT32           tmp;
    UINT32          EEPROM_Write;
    EEPROM_Write = Size & 0x80;
    Size = Size & 0x7F;
    
    AXIS_HANDLE *AxisA;
    
    AxisA = (AXIS_HANDLE*)GetAxisHandle(0);
    
    if(((0x6000 == (Index & 0xFF00))||(0x6800 == (Index & 0xFF00))) 
                &&((AxisA[0].Prm->PnCtrlCfgPrm.CtrlSource!=BUSTpyeCtrl)))
    {
      return ABORTIDX_DATA_CANNOT_BE_ACCESSED_BECAUSE_OF_LOCAL_CONTROL;  
    }      
    else
    {
    
	while(accSz < Size)
	{
		AccessPrm.MemAddr = (Index<<16) + SubindexTmp;
		RegMngrGetAttrByMemAddr(&AccessPrm);
		
		/* Get Paremter Attribute */
		if(NULL != RegMngrGetAttrByMemAddr(&AccessPrm))
		{
			prmSz = AccessPrm.PrmAttr->DataLength;
			if(Size < (accSz + prmSz))
			{
				return ABORTIDX_PARAM_LENGTH_ERROR;
			}
            AccessPrm.ArrayIdx = 0;
            AccessPrm.IfHandle = NULL;
			
			/* Write a Paremter */
            if(EEPROM_Write>0)
            {
                AccessPrm.EepWrite = TRUE;
            }
            else
            {
			AccessPrm.EepWrite = FALSE;
            }
			wrValue = regGetData(&pData[accSz], prmSz, 1);
			tmp = PrmWriteValue(&AccessPrm, &wrValue, prmSz);
			if(tmp == PRM_RSLT_LIMIT_ERR)
			{
                rc = ABORTIDX_VALUE_EXCEEDED;
				return rc;
			}
            else if(tmp == PRM_RSLT_RWACC_ERR)
			{
                rc = ABORTIDX_COMPLETE_ACCESS_NOT_SUPPORTED ;
				return rc;
			}
            else if(tmp < 0)
			{
                 rc = ABORTIDX_GENERAL_ERROR;
				return rc;
			}

			accSz = accSz + prmSz;
		}
		else
		{
			return ABORTIDX_OBJECT_NOT_EXISTING;
		}
		AccessPrm.MemAddr += prmSz;
		SubindexTmp++;

		if(!bCompleteAccess)
		{
			if(accSz != Size)
			{
				rc = ABORTIDX_PARAM_LENGTH_ERROR;
			}
			break;
		}
		
	}
        
        return rc;
    }

}

/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
UINT32 PrmCANObjWrite( UINT16 Index, UINT8 Subindex, UINT32 Size, UINT16 *pData, UINT8 bCompleteAccess )
{
	//PRM_RSLT		rc;
  UINT32		    rc = 0;
	REG_ACC_T		AccessPrm={0};
	INT32			prmSz=0, accSz=0;
	UINT8           SubindexTmp = Subindex;
	INT32			wrValue;
    INT32           tmp;
    UINT32          EEPROM_Write;
    EEPROM_Write = Size & 0x80;
    Size = Size & 0x7F;
    
    AXIS_HANDLE *AxisA;
    
    AxisA = (AXIS_HANDLE*)GetAxisHandle(0);
    
    if(((0x6000 == (Index & 0xFF00))||(0x6800 == (Index & 0xFF00))) 
                &&((AxisA[0].Prm->PnCtrlCfgPrm.CtrlSource != BUSTpyeCtrl)))
    {
      return OD_WRITE_NOT_ALLOWED;  
    } 
    else
    {
    
	while(accSz < Size)
	{
		AccessPrm.MemAddr = ((UINT32)Index<<16) + SubindexTmp;
//		RegMngrGetAttrByMemAddr(&AccessPrm);
		
		/* Get Paremter Attribute */
		if(NULL != RegMngrGetAttrByMemAddr(&AccessPrm))
		{
			prmSz = AccessPrm.PrmAttr->DataLength;
			if(Size < (accSz + prmSz))
			{
				return OD_LENGTH_DATA_INVALID;
			}
            AccessPrm.ArrayIdx = 0;
            AccessPrm.IfHandle = NULL;
			
			/* Write a Paremter */
            if(EEPROM_Write>0)
            {
                AccessPrm.EepWrite = TRUE;
            }
            else
            {
			AccessPrm.EepWrite = FALSE;
            }
			wrValue = regGetData(&pData[accSz], prmSz, 1);
			tmp = PrmWriteValue(&AccessPrm, &wrValue, prmSz);
			if(tmp == PRM_RSLT_LIMIT_ERR)
			{
                rc = OD_VALUE_RANGE_EXCEEDED;
				return rc;
			}
            else if(tmp == PRM_RSLT_RWACC_ERR)
			{
                rc = OD_WRITE_NOT_ALLOWED ;
				return rc;
			}
            else if(tmp < 0)
			{
                 rc = SDOABT_GENERAL_ERROR;
				return rc;
			}

			accSz = accSz + prmSz;
		}
		else
		{
			return OD_NO_SUCH_OBJECT;
		}
		AccessPrm.MemAddr += prmSz;
		SubindexTmp++;

		if(!bCompleteAccess)
		{
			if(accSz != Size)
			{
				rc = OD_LENGTH_DATA_INVALID;
			}
			break;
		}
		
	}
        
        return rc;
    }
}

/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
UINT32 PrmModbusObjLenGet( UINT16 ArrayIdx, UINT32 *Lenght, REG_ACC_T *AccessPrm )
{   
    UINT32		    rc = 0;
    
    AccessPrm->ArrayIdx  = ArrayIdx;
    
    if(NULL != RegMngrGetAttrByArrayIdx(AccessPrm))
    {
      *Lenght = AccessPrm->PrmAttr->DataLength;
    }
    else
    {
       rc = MODBUS_ADDR_ERROR;
    }
    
    return rc;  
}
/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
UINT32 PrmModbusObjWrite(UINT16 *pData, UINT32 Size, REG_ACC_T *AccessPrm)
{

    UINT32		    rc = 0;
	INT32			accSz=0;
	INT32			wrValue;
    UINT32          EEPROM_Write;
//    EEPROM_Write    = (Size) & 0x80;
//    Size = (Size) & 0x7F;
    INT32           tmp;
    
    AccessPrm->ArrayIdx = 0;
    AccessPrm->IfHandle = NULL;
    
        
    wrValue = regGetData(&pData[accSz], Size, 1);
    
    tmp = PrmWriteValue(AccessPrm, &wrValue, Size);
    
    if(tmp == PRM_RSLT_LIMIT_ERR)
    {
        rc = MODBUS_PRM_ERROR;
        return rc;
    }
    else if(tmp == PRM_RSLT_RWACC_ERR)
    {
        rc = MODBUS_PRM_ERROR ;
        return rc;
    }
    else if(tmp < 0)
    {
         rc = MODBUS_PRM_ERROR;
        return rc;
    }
	return rc;
}



/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
void *PrmObjGetRamPtr( UINT16 Index, UINT8 Subindex )                                     //add by jrh 2021/11/26
{
	REG_ACC_T 		AccessPrm={0};
    void		    *pRamV = NULL;

	AccessPrm.MemAddr = (Index<<16) + Subindex;
	
	/* Get Paremter Attribute */
	if(NULL != RegMngrGetAttrByMemAddr(&AccessPrm))
	{
	
        if(NULL != AccessPrm.PrmAttr->GetRamPtrCallback)
        {
            pRamV = AccessPrm.PrmAttr->GetRamPtrCallback(
                            AccessPrm.ArrayIdx, AccessPrm.hRegCfg->GetRamPtrParam);
        }
		
	}
	
	return pRamV;

}

/****************************************************************************************************
* DESCRIPTION: //add by lly 20231204
*			  
* RETURNS:
*
****************************************************************************************************/
PUBLIC PRM_ATTR * RegMngrGetAttrByArrayIdx(REG_ACC_T *AccessPrm)                                   
{
	 UINT32			 ax_no;
	 UINT32			 CoeIndex;   
	 AXIS_HANDLE     *Axis;

	Axis = GetAxisHandle(0);
	AccessPrm->hRegCfg = NULL;
	AccessPrm->PrmAttr = NULL;
    
    
   ax_no = (AccessPrm->ArrayIdx & 0x1000)?1:0;

//	ax_no = (AccessPrm.MemAddr & 0x08000000)?1:0;
//	CoeIndex = AccessPrm.MemAddr&0xF7FFFFFF;
//
//	if((AccessPrm->ArrayIdx & 0x8000) == 0x8000)
//	{
//		AccessPrm->hRegCfg = Axis[ax_no].RegManager->hFnReg;
//		AccessPrm->EepWrite = FALSE;	
//	}
//	else
	{
		AccessPrm->hRegCfg = Axis[ax_no].RegManager->hPnReg;
		AccessPrm->EepWrite = FALSE;	
	}

	if(NULL != AccessPrm->hRegCfg)
	{
		AccessPrm->PrmAttr = PrmObjGetIndexByArrayIdx(AccessPrm->hRegCfg, (AccessPrm->ArrayIdx & 0x0FFF));
    } 
    
    return AccessPrm->PrmAttr;
}
/****************************************************************************************************
* DESCRIPTION:
*			  
* RETURNS:
*
****************************************************************************************************/
PUBLIC PRM_ATTR*  PrmObjGetIndexByArrayIdx(REGIF_CONFIG_T *RegCfg, UINT16 Number)
{
  
	PRM_ATTR   	    *pPrmAttr;
	PRM_ATTR 	    *FindAttr;
	PRM_TBL		    *PrmListTable;
	UINT32 			low, high, mid;
	UINT32 			idx;

	low = 0;
	high = RegCfg->NumOfTableEntry - 2;
	FindAttr = NULL;

	while(low <= high)
	{
		mid = (low + high) >> 1;
		PrmListTable = &RegCfg->PrmListTable[mid];
		pPrmAttr = (PRM_ATTR*)PrmListTable->Attribute;

		if(Number < PrmListTable->Number)
		{
			high = mid - 1;
		}
		else if(Number > PrmListTable->Number)
		{
			low = mid + 1;
		}
		else 
		{
			FindAttr = pPrmAttr;
			break;
		}
	}

	return FindAttr;  
}