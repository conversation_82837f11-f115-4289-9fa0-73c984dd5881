/****************************************************************************************************
 *
 * FILE NAME:  MotorPoleFind.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-08-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef MOTRO_POLE_FIND_H
#define MOTRO_POLE_FIND_H

#include "MpfHandler.h"
#include "PnPrmStruct.h"
#include "Alarm.h"
#include "Bprm.h"



/****************************************************************************************************/
/*		DEFINES																						*/
/****************************************************************************************************/
#define PHASE_DEG000		0
#define PHASE_DEG022_5		4096
#define PHASE_DEG030		5461
#define PHASE_DEG045		8192
#define PHASE_DEG060		10922
#define PHASE_DEG090		16384
#define PHASE_DEG120		21845
#define PHASE_DEG135		24576
#define PHASE_DEG180		32768
#define PHASE_DEG225		40960
#define PHASE_DEG315		57344
#define PHASE_DEG360		65536
/*--------------------------------------------------------------------------------------------------*/
#define SINPHASE_360		16384
#define SINPHASE_090		4096
#define SINPHASE_045		2048


/*--------------------------------------------------------------------------------------------------*/
/*		Magnetic pole detection sequence step														*/
/*--------------------------------------------------------------------------------------------------*/
enum POLE_FIND_SEQ_STEP {
	MPF_INIT		= 0,
	MPF_INIT2,
	MPF_DRIVE,
	MPF_CAL,
	MPF_STOP,
	MPF_CONFIRM,
	MPF_END,
};

/*--------------------------------------------------------------------------------------------------*/
/*		Speed command creation sequence step														*/
/*--------------------------------------------------------------------------------------------------*/
enum POLE_FIND_VREF_STEP {
	MPFLAU_INIT		= 0,
	MPFLAU_ACC,
	MPFLAU_CONST,
	MPFLAU_DEC,
	MPFLAU_WAIT,
};

/*--------------------------------------------------------------------------------------------------*/
/*		Position command creation sequence step														*/
/*--------------------------------------------------------------------------------------------------*/
enum POLE_FIND_PREF_STEP {
	MPFPOS_INIT		= 0,
	MPFPOS_PLUS,
	MPFPOS_WAIT1,
	MPFPOS_MINUS,
	MPFPOS_WAIT2,
};

/*--------------------------------------------------------------------------------------------------*/
/*		Thrust command creation sequence step														*/
/*--------------------------------------------------------------------------------------------------*/
enum POLE_FIND_TREF_STEP {
	MPFTRQ_INIT		= 0,
	MPFTRQ_ACC,
	MPFTRQ_CONST,
	MPFTRQ_DEC,
	MPFTRQ_END,
};

/*--------------------------------------------------------------------------------------------------*/
/*		Operation confirmation sequence step														*/
/*--------------------------------------------------------------------------------------------------*/
enum POLE_FIND_CONF_STEP {
	MPFCSTEP_WAIT		= 0,
	MPFCSTEP_PHSCHANGE,
	MPFCSTEP_ADJUST,
	MPFCSTEP_PHSSTORE,
	MPFCSTEP_PHSREVERT,
};

/*--------------------------------------------------------------------------------------------------*/
/*		8-division area check sequence step															*/
/*--------------------------------------------------------------------------------------------------*/
enum POLE_FIND_ZONE_CHK_STEP {
	PAREACHECK_000	= 0,
	PAREACHECK_001,
	PAREACHECK_002,
	PAREACHECK_003,
};

/*--------------------------------------------------------------------------------------------------*/
/*		d-q axis deviation direction																*/
/*--------------------------------------------------------------------------------------------------*/
#define AXISERR_EQUAL		0				/* dAxisMax = qAxisMax									*/
#define AXISERR_MINUS		1				/* dAxisMax > qAxisMax									*/
#define AXISERR_PLUS		2				/* dAxisMax < qAxisMax									*/

/*--------------------------------------------------------------------------------------------------*/
/*		Other constants																				*/
/*--------------------------------------------------------------------------------------------------*/
#define TRYCOUNT			3


/****************************************************************************************************/
/*		PROTOTYPE																					*/
/****************************************************************************************************/
PUBLIC INT32 MpoleFindSpeedControl( MPFIND *MpFind, MPFEXEINFO *MpfExeInfo, BOOL BeComplete );
PUBLIC ALMID_T MpoleFindSequence( MPFIND *MpFind, MPFEXEINFO *MpfExeInfo, BOOL PdetReq, BOOL BaseEnable );

/****************************************************************************************************/
/*		for Parameter Calculations																	*/
/****************************************************************************************************/
typedef struct 
{
	UINT16	pdetloophz;				// Pnxxx : Magnetic pole detection speed loop gain
	UINT16	pdetpitime;				// Pnxxx : Magnetic pole detection speed Loop integration time
	UINT16	pdetmaxspd;				// Pnxxx : Magnetic pole detection command speed	
	UINT16	pdetmaxt;				// Pnxxx : Magnetic pole detection command acceleration/deceleration time
	UINT16	pdetclmpt;				// Pnxxx : Magnetic pole detection command constant speed time
	UINT16	pdetwait;				// Pnxxx : Magnetic pole detection command wait time	
	UINT16	pdetrepeat;				// Pnxxx : Number of magnetic pole detection repetitions
	UINT16	pdetdistok;				// Pnxxx : Magnetic pole detection movable range	

	UINT16	pdetjrate;				// Pnxxx : Magnetic pole detection load level		
	UINT16	pdetintegless;			// Pnxxx : Magnetic pole detection integral less gain	
	UINT16	pdetinteglesst;			// Pnxxx : Start time without magnetic pole detection integration
	UINT16	pdetmaxspd_r;			// Pnxxx : Magnetic pole detection command speed	
	UINT16	pdetdistok_r;			// Pnxxx : Magnetic pole detection movable range	
	UINT16	pdetmaxtrq;				// Pnxxx : Magnetic pole detection confirmation torque command
	UINT16	pdetinctime;			// Pnxxx : Magnetic pole detection confirmation torque command adjustment time
	UINT16	pdetconsttime;			// Pnxxx : Magnetic pole detection confirmation torque command fixed time
	UINT16	pdetdegreeok;			// Pnxxx : Allowable range of magnetic pole detection error
	UINT16	pdetrevlvspdref;		// Pnxxx : Reverse running level at magnetic pole detection speed command
	UINT16	pdetrevlvwait;			// Pnxxx : Reverse running level during magnetic pole detection waiting time
	UINT16	pdetoslevel;			// Pnxxx : Overspeed level at magnetic pole detection			
} MPFIND_UPRM;

PUBLIC void MpoleFind_CalcSpeedCommandPrm( MPFIND *MpFind, PRMDATA *PnPrm, 
															BPRMDAT *Bprm, MPFIND_UPRM *PdetPrm );
PUBLIC void MpoleFind_CalcSpeedControlPrm( MPFIND *MpFind,UINT16 trqfil11, 
															 BPRMDAT *Bprm, MPFIND_UPRM *PdetPrm  );
PUBLIC void MpoleFind_CalcErrorLevel( MPFIND *MpFind, PRMDATA *PnPrm, 
															 BPRMDAT *Bprm, MPFIND_UPRM *PdetPrm  );

#endif

