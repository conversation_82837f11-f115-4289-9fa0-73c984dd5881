<project>
  <files
    default_encoding="GB2312"
    readonly="false"
    maxfilesize="10485760"
    enforce_portability="false">
    <dir
      name=".">
      <watched
        excludes=".*"
        subdirs="true">
        <filter
          name="Assembly"/>
        <filter
          name="C"/>
        <filter
          name="C++"/>
        <filter
          name="CSS"/>
        <filter
          name="CUDA"/>
        <filter
          name="Html"/>
        <filter
          name="Javascript"/>
        <filter
          name="Objective-C"/>
        <filter
          name="Objective-C++"/>
        <filter
          name="Php"/>
        <filter
          name="TypeScript"/>
        <filter
          name="Xml"/>
      </watched>
      <dir
        name="Document">
        <dir
          name="FSP_xml"/>
        <dir
          name="ecat_xml"/>
      </dir>
      <dir
        name="FlashRSK_RZN2L_SerialFlash">
        <dir
          name="framework2">
          <dir
            name="template"/>
        </dir>
      </dir>
      <dir
        name="SSC_project"
        excluded="true">
        <dir
          name="Src_jhl"/>
        <dir
          name="Src_rt"/>
        <dir
          name="ecat"/>
      </dir>
      <dir
        name="rzn">
        <dir
          name="arm">
          <dir
            name="CMSIS_5">
            <dir
              name="CMSIS">
              <dir
                name="Core_R">
                <dir
                  name="Include"/>
              </dir>
            </dir>
          </dir>
        </dir>
        <dir
          name="board">
          <dir
            name="custom"/>
        </dir>
        <dir
          name="fsp">
          <dir
            name="inc">
            <dir
              name="api"/>
            <dir
              name="instances"/>
          </dir>
          <dir
            name="src">
            <dir
              name="bsp">
              <dir
                name="cmsis">
                <dir
                  name="Device">
                  <dir
                    name="RENESAS">
                    <dir
                      name="Include"/>
                    <dir
                      name="Source"/>
                  </dir>
                </dir>
              </dir>
              <dir
                name="mcu">
                <dir
                  name="all"/>
                <dir
                  name="rzn2l"/>
              </dir>
            </dir>
            <dir
              name="r_canfd"/>
            <dir
              name="r_cmt"/>
            <dir
              name="r_dmac"/>
            <dir
              name="r_dsmif"/>
            <dir
              name="r_ether_phy">
              <dir
                name="targets">
                <dir
                  name="other"/>
              </dir>
            </dir>
            <dir
              name="r_ether_selector"/>
            <dir
              name="r_ioport"/>
            <dir
              name="r_spi"/>
            <dir
              name="r_usb_basic">
              <dir
                name="src">
                <dir
                  name="driver">
                  <dir
                    name="inc"/>
                </dir>
                <dir
                  name="hw">
                  <dir
                    name="inc"/>
                </dir>
              </dir>
            </dir>
            <dir
              name="r_usb_pcdc">
              <dir
                name="src">
                <dir
                  name="inc"/>
              </dir>
            </dir>
            <dir
              name="r_xspi_qspi"/>
            <dir
              name="rm_ethercat_ssc_port"/>
          </dir>
        </dir>
      </dir>
      <dir
        name="rzn_cfg">
        <dir
          name="driver"/>
        <dir
          name="fsp_cfg">
          <dir
            name="bsp"/>
        </dir>
      </dir>
      <dir
        name="rzn_gen"/>
      <dir
        name="source">
        <dir
          name="Canopen"/>
        <dir
          name="IAP"/>
        <dir
          name="UserLib"/>
        <dir
          name="apl">
          <dir
            name="adv"/>
          <dir
            name="ecat_eeprom"/>
        </dir>
        <dir
          name="bsp">
          <dir
            name="flash"/>
        </dir>
        <dir
          name="ecat"/>
        <dir
          name="modbus"/>
      </dir>
      <dir
        name="src"/>
    </dir>
  </files>
  <languages>
    <language
      name="Assembly"/>
    <language
      name="C++"/>
    <language
      name="Web"/>
  </languages>
  <file_types/>
  <annotations_options/>
  <assembly_options
    assembler="Coldfire 68K"
    leading_spaces_option="true"
    save_macro_expansion_text="false"
    search_for_include_files_among_project_files="true"
    treat_system_includes_as_user_includes="true"
    use_case_insensitive_lookup_for_includes="false"/>
  <comparison_options
    comparison_db=""
    git_repo="./."
    git_commit=""
    diff_flags="1"/>
  <cpp_options
    add_found_include_files="false"
    add_found_system_include_files="false"
    allow_nested_comments="false"
    analyze_headers_in_isolation="true"
    bracket_depth="256"
    cache_ast_files="false"
    c_std=""
    compiler="Microsoft Visual C++"
    compiler_include_path=""
    create_implicit_special_member_functions="false"
    create_references_in_assembly="true"
    create_references_in_inactive_code="true"
    create_references_to_local_objects="true"
    create_references_to_macros_during_macro_expansion="false"
    create_references_to_parameters="true"
    cxx_std=""
    cuda_arch="sm_20"
    delayed_template_parsing="true"
    triple=""
    ignore_dir_in_include_files="false"
    ignore_preprocessor_conditionals="false"
    ios_min_version="5.0"
    linkage_append_text=""
    linkage_prepend_text=""
    macos_min_version="10.7"
    macro_expansion_trunc="0"
    merge_structs="true"
    msc_version=""
    prompt_for_missing_include_files="false"
    objectDataMembers="false"
    objectFunctionMembers="false"
    save_comments_associated_with_entities="true"
    save_duplicate_references="false"
    save_macro_expansion_text="false"
    simplify_macro_expansion="false"
    search_for_include_files_among_project_files="true"
    start_worker_processes_serially="false"
    sysroot=""
    treat_system_includes_as_user_includes="true"
    compare_includes_by_content="false"
    use_clang="true"
    use_include_cache="true"
    objc_memory_mode="MrrMode"
    worker_process_timeout="2"/>
  <dependency_options
    exclude_standard="true"
    use_includes="true"
    mode="link"/>
  <imports/>
  <metrics
    declared_in_file_display_mode="no_path"
    file_display_mode="no_path"
    show_declared_in_file="false"
    show_function_parameter_types="false"
    write_column_titles="true"
    add_uniquename_column="false"
    metric_filter="28532"/>
  <reports
    date_stamping="false"
    display_parameters="false"
    index_by_method="false"
    group_by_directory="false"
    filename_mode="short"
    generation_status="needs_generation"
    generation_time="0"
    html_clear_directory="false"
    html_generation="true"
    html_mode="alpha"
    headertext_mode="false"
    headerusertext_mode="false"
    headertext_text="Report generated by Understand"
    headerusertext_text=""
    html_size="250"
    text_clear_directory="false"
    text_generation="true"
    text_mode="single">
    <report
      name="Data Dictionary"
      enabled="true"/>
    <report
      name="File Contents"
      enabled="true"/>
    <report
      name="Program Unit Cross Reference"
      enabled="true"/>
    <report
      name="Object Cross Reference"
      enabled="true"/>
    <report
      name="Type Cross Reference"
      enabled="true"/>
    <report
      name="Macro Cross Reference"
      enabled="true"/>
    <report
      name="Include File Cross Reference"
      enabled="true"/>
    <report
      name="Declaration Tree"
      enabled="true"/>
    <report
      name="Extend Tree"
      enabled="false"/>
    <report
      name="Invocation Tree"
      enabled="false"/>
    <report
      name="Simple Invocation Tree"
      enabled="true"/>
    <report
      name="Imports"
      enabled="true"/>
    <report
      name="With Tree"
      enabled="true"/>
    <report
      name="Simple With Tree"
      enabled="true"/>
    <report
      name="Generic Instantiation"
      enabled="true"/>
    <report
      name="Exception Cross Reference"
      enabled="true"/>
    <report
      name="Renames"
      enabled="true"/>
    <report
      name="Program Unit Complexity"
      enabled="true"/>
    <report
      name="Project Metrics"
      enabled="true"/>
    <report
      name="Program Unit Metrics"
      enabled="true"/>
    <report
      name="File Metrics"
      enabled="true"/>
    <report
      name="File Average Metrics"
      enabled="true"/>
    <report
      name="Fortran Extension Usage"
      enabled="true"/>
    <report
      name="Class Metrics"
      enabled="true"/>
    <report
      name="Class OO Metrics"
      enabled="true"/>
    <report
      name="Implicitly Declared Objects"
      enabled="true"/>
    <report
      name="Uninitialized Items"
      enabled="true"/>
    <report
      name="Unused Objects and Functions"
      enabled="true"/>
    <report
      name="Unused Objects"
      enabled="true"/>
    <report
      name="Unused Types"
      enabled="true"/>
    <report
      name="Unused Program Units"
      enabled="true"/>
    <report
      name="Uses Not Needed"
      enabled="true"/>
    <report
      name="Withs Not Needed"
      enabled="true"/>
  </reports>
  <web_options
    add_found_imported_js_files="true"
    allow_asp_style_php_tags="false"
    allow_jquery="false"
    allow_node_js="false"
    allow_short_php_tags="true"
    js_module_search_path=""
    node_js_predefined_config_file="$CONF:/understand/javascript/nodejs_predefined.txt"
    node_js_search_path=""
    save_php_comments="true"
    search_strings_for_entity_names="false"
    php_version="5.3"/>
</project>
