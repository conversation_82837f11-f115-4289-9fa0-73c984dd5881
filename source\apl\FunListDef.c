/****************************************************************************************************
 *
 * FILE NAME:  FunListDef.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.12
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	12-08-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "FunListDef.h"
#include "RegAccessIf.h"



/****************************************************************************************************/
/*		Fn002 : JOG																					*/
/****************************************************************************************************/
CFUNDEF	fndef_fn002 = {
	1,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"JOG        ",   							// Auxiliary function abbreviation	
	FnEnterJOGoperation,   						// Start processing function pointer    
	FnExecuteJOGoperation,  				 	// Execution processing function pointer 	
	FnLeaveJOGoperation,   						// Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End		
};


/****************************************************************************************************/
/*		Fn004 : Program JOG																			*/
/****************************************************************************************************/
CFUNDEF	fndef_fn004 = {
	1,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"Program JOG ",   							// Auxiliary function abbreviation	
	FnEnterPrgJog,   							// Start processing function pointer    
	FnExecutePrgJog,  				 			// Execution processing function pointer 	
	FnLeavePrgJog,   							// Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End			
};

/****************************************************************************************************/
/*		Fn005 : Inertial Idnet																		*/
/****************************************************************************************************/
CFUNDEF	fndef_fn005 = {
	1,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"InertiaIdent",   							// Auxiliary function abbreviation	
	FnEnterInertialoperation,   				// Start processing function pointer    
	FnExecuteInertialoperation,  				// Execution processing function pointer 	
	FnLeaveInertialoperation,   				// Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End			
};

/****************************************************************************************************/
/*		Fn006 : Motor Idnet																		*/
/****************************************************************************************************/
CFUNDEF	fndef_fn006 = {
	1,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"MotorIdent",   							// Auxiliary function abbreviation	
	FnEnterMotorIdentoperation,   				// Start processing function pointer    
	FnExecuteMotorIdentoperation,  				// Execution processing function pointer 	
	FnLeaveMotorIdentoperation,   				// Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End			
};

/****************************************************************************************************/
/*		Fn007 : Clamping Jaw																		*/
/****************************************************************************************************/
CFUNDEF	fndef_fn007 = {
	1,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"Clamping Jaw",   							// Auxiliary function abbreviation	
	FnEnterClampingJaw,   				// Start processing function pointer    
	FnExecuteClampingJaw,  				// Execution processing function pointer 	
	FnLeaveClampingJaw,   				// Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End			
};


/****************************************************************************************************/
/*		Fn200 : Dynamic Auto Tuning Level Setting													*/
/****************************************************************************************************/
CFUNDEF	fndef_fn200 = {
	2,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"TuneLvlSet",   							// Auxiliary function abbreviation	
	FnEnterSetTuneLessLevel,   				    // Start processing function pointer    
	FnExecuteSetTuneLessLevel,  				// Execution processing function pointer 	
	FnLeaveSetTuneLessLevel,   					// Termination function pointer	
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End	
//	FunDynamicAutoTuningBegin,   				// Pointer to the Operator Begin		
//	FunDynamicAutoTuningMain,   				// Pointer to the Operator Main	
//	FunDynamicAutoTuningEnd,   					// Pointer to the Operator End	
};

/****************************************************************************************************/
/*		Fn201 : Advanced Autotuning																	*/
/****************************************************************************************************/
CFUNDEF	fndef_fn201 = {
	0,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"AAT       ",   							// Auxiliary function abbreviation	
	FnEnterAdvAutoTuning,   					// Start processing function pointer    
	FnExecuteAdvAutoTuning,  					// Execution processing function pointer 	
	FnLeaveAdvAutoTuning,   					// Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End	
//	FunAdvAutoTuningBegin,   					// Pointer to the Operator Begin		
//	FunAdvAutoTuningMain,   					// Pointer to the Operator Main	
//	FunAdvAutoTuningEnd,   						// Pointer to the Operator End	
};

#if 0

/****************************************************************************************************/
/*		Fn202 : Refernce Input type Advanced Autotuning												*/
/****************************************************************************************************/
CFUNDEF	fndef_fn202 = {
	2,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"Ref-AAT    ",   							// Auxiliary function abbreviation	
	FnEnterRefInAdvAutoTuning,   				// Start processing function pointer    
	FnExecuteRefInAdvAutoTuning,  				// Execution processing function pointer 	
	FnLeaveRefInAdvAutoTuning,   				// Termination function pointer	
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End		
//	FunRefInAdvAutoTuningBegin,   				// Pointer to the Operator Begin		
//	FunRefInAdvAutoTuningMain,   				// Pointer to the Operator Main	
//	FunRefInAdvAutoTuningEnd,   				// Pointer to the Operator End	
};
#endif


/****************************************************************************************************/
/*		Fn203 : One Parameter Tuning																*/
/****************************************************************************************************/
CFUNDEF	fndef_fn203 = {
	2,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"OnePrmTun  ",   							// Auxiliary function abbreviation	
	FnEnterOnePrmTuning,   						// Start processing function pointer    
	FnExecuteOnePrmTuning,  					// Execution processing function pointer 	
	FnLeaveOnePrmTuning,   						// Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End	
//	FunOneParamTuningBegin,   					// Pointer to the Operator Begin		
//	FunOneParamTuningMain,   					// Pointer to the Operator Main	
//	FunOneParamTuningEnd,   					// Pointer to the Operator End	
};


/****************************************************************************************************/
/*		Fn204 : A-type Vibration Suppression Control Tuning											*/
/****************************************************************************************************/
CFUNDEF	fndef_fn204 = {
	2,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"A-Vib Sup  ",   							// Auxiliary function abbreviation	
	FnEnterAtypeVibTuning,   					// Start processing function pointer    
	FnExecuteAtypeVibTuning,  					// Execution processing function pointer 	
	FnLeaveAtypeVibTuning,   					// Termination function pointer	
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End		
//	FunAtypeVibTuningBegin,   					// Pointer to the Operator Begin		
//	FunAtypeVibTuningMain,   					// Pointer to the Operator Main	
//	FunAtypeVibTuningEnd,   					// Pointer to the Operator End	
};



/****************************************************************************************************/
/*		Fn205 : Model Following Control with Vibration Suppression Tuning							*/
/****************************************************************************************************/
CFUNDEF	fndef_fn205 = {
	2,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"Vib Sup    ",   							// Auxiliary function abbreviation	
	FnEnterMfcTuning,   						// Start processing function pointer    
	FnExecuteMfcTuning,  						// Execution processing function pointer 	
	FnLeaveMfcTuning, 						    // Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End	
//	FunVibMfcTuningBegin,   					// Pointer to the Operator Begin		
//	FunVibMfcTuningMain,   						// Pointer to the Operator Main	
//	FunVibMfcTuningEnd,   						// Pointer to the Operator End	
};

#if 0

/****************************************************************************************************/
/*		Fn206 : EasyFFT																				*/
/****************************************************************************************************/
CFUNDEF	fndef_fn206 = {
	0,    										// No.1/2 OpeReg ParaRun		
	FALSE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"Easy FFT   ",   							// Auxiliary function abbreviation	
	FnEnterEasyFft,   							// Start processing function pointer    
	FnExecuteEasyFft,  							// Execution processing function pointer 	
	FnLeaveEasyFft,   							// Termination function pointer		
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End	
//	FunEasyFftBegin,   							// Pointer to the Operator Begin		
//	FunEasyFftMain,   							// Pointer to the Operator Main	
//	FunEasyFftEnd,   							// Pointer to the Operator End	
};


/****************************************************************************************************/
/*		Fn207 : Vibration Freq. Display and Notchi Filter Autoset									*/
/****************************************************************************************************/
CFUNDEF	fndef_fn207 = {
	2,    										// No.1/2 OpeReg ParaRun		
	TRUE,    									// ParaRun with DataTrace			
	FALSE,    									// Common mode to all Axes		
	FALSE,    									// Parameter Write prohibited		
	ACCLVL_USER1,   							// Access level		   
	"V-Monitor ",   							// Auxiliary function abbreviation	
	FnEnterVibrationMonitor,   					// Start processing function pointer    
	FnExecuteVibrationMonitor,  				// Execution processing function pointer 	
	FnLeaveVibrationMonitor,   					// Termination function pointer	
	NULL,   									// Pointer to the Operator Begin		
	NULL,   									// Pointer to the Operator Main	
	NULL,   									// Pointer to the Operator End		
//	FunVibMonitorBegin,   						// Pointer to the Operator Begin		
//	FunVibMonitorMain,   						// Pointer to the Operator Main	
//	FunVibMonitorEnd,   						// Pointer to the Operator End	
};
#endif


/****************************************************************************************************/
/*		Auxiliary function management table															*/
/****************************************************************************************************/
CFUNTBL	FnFunTbl[] = {
/* FnNo,  FnMode,  FnDef																			*/
/*--------------------------------------------------------------------------------------------------*/

{ 0x0002, 0x1002, &fndef_fn002		},	/* JOG														*/
{ 0x0004, 0x1004, &fndef_fn004		},	/* Program JOG												*/
{ 0x0005, 0x1005, &fndef_fn005		},	/* Inertial Idnet                                           */
{ 0x0006, 0x1006, &fndef_fn006		},	/* Motor Idnet                                              */
// { 0x0007, 0x1007, &fndef_fn007		},	/* Clamping Jaw                                              */
// { 0x0200, 0x1200, &fndef_fn200		},	/* Dynamic Auto Tuning Level Setting						*/
// { 0x0201, 0x1201, &fndef_fn201		},	/* Advanced Autotuning										*/
// //{ 0x0202, 0x1202, &fndef_fn202		},	/* Refernce Input type Advanced Autotuning					*/
// { 0x0203, 0x1203, &fndef_fn203		},	/* One Parameter Tuning 									*/
// { 0x0204, 0x1204, &fndef_fn204		},	/* A-type Vibration Suppression Control Tuning				*/
// { 0x0205, 0x1205, &fndef_fn205		},	/* Model Following Control with Vibration Suppression Tuning */
//{ 0x0206, 0x1206, &fndef_fn206		},	/* EasyFFT													*/
//{ 0x0207, 0x1207, &fndef_fn207		},	/* Vibration Freq. Display and Notchi Filter Autoset		*/

};
const UINT32 FnFunTblEntNum = sizeof(FnFunTbl)/sizeof(FnFunTbl[0]);


