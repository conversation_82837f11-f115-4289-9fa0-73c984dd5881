/****************************************************************************************************
 *
 * FILE NAME:  EepromIF.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.13
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	13-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _EEPROM_IF_H_
#define	_EEPROM_IF_H_

#include "BaseDef.h"

#define EEP_MAP_HEADER_INFO_ADDRESS		0x0000U
#define EEP_MAP_HEADER_INFO_LENGTH		0x0040U

#define EEP_MAP_HEADER_INFO_VERSION		0x0001U /*when EEP save Number change,Need to change it*/
#define EEP_MAP_HEADER_HARDWARE_VERSION		0x0400U /*when Hardware change,Need to change it,only can be written once*/
/* Do not change this address, the kernel module reads this value at start-up. */
#define EEP_NOSUM_CMN_BLOCK_ADDRESS		0x0040U	/* Top Address of the no-sum value for axis common */
#define EEP_NOSUM_CMN_BLOCK_LENGTH		0x0040U


#define EEP_NOSUM_BLOCK_ADDRESS			0x0080U	/* Top Address of the no-sum value for axises */

#define EEP_MAP_PADR_MASK				0x7FFFU	/* physical address mask */

#define EEP_CMNPRM_ADDRESS				0x0100U	/* Top Address of the user common parameters */
#define EEP_AXPRM_TOP_ADDRESS			0x0180U	/* Top Address of the user axis parameters */
#define EEP_CMNPRM_CHKSUM_ADR			0x0100U	/* Top Address of the user common parameters */
#define EEP_AXPRM_CHKSUM_ADR			0x0180U	/* Top Address of the user axis parameters */


//#define EEP_MAP_BLOCK_LENGTH			0x0800U	/* a block length of the user paraeters */
#define EEP_MAP_AXIS_OFFSET				0x1000U	/* axis offset of each user paraeters */
//#define EEP_MAP_SDABLOCK_LENGTH			0x0100U	/* a block length of the SDA paraeters */
#define EEP_CHECKSUM_ADDRESS_MASK		0xFF00U	/* checksum address mask */
//#define EEP_MAP_SDA_OFFSET				0x0700U	/* offset address of the SDA paraeters */

#define EEP_MAP_AXIS_MASK				0xF000U	/* axis no mask for detect eeprom number */
//#define EEP_MAP_AXIS_SFT				12U		/* EEP_MAP_AXIS_OFFSET */		
//#define EEP_MAP_PADR_MASK				0x0FFFU	/* physical address mask */

//#define EEP_MAX_QUEUES				    256


#define GET_EEP_PHYSICAL_ADDRESS(adr)	((adr)&(EEP_MAP_PADR_MASK))

#define EEP_NOSUM_ADDRESS(no, ofs)	(EEP_NOSUM_BLOCK_ADDRESS+(EEP_MAP_AXIS_OFFSET*(no))+(ofs))		
#define EEP_NOSUMCMN_ADDRESS(ofs)	(EEP_NOSUM_CMN_BLOCK_ADDRESS+(ofs))


#define I2C_DRV_SUCCESS		(0)						/* I2C Driver Success*/
#define I2C_TIMEOUT_ERR		(3)					    /* I2C Driver Timeout Error	*/


/*--------------------------------------------------------------------------------------------------*/
/*	   Offset address of the no-checksum informations											    */
/*--------------------------------------------------------------------------------------------------*/
enum EEP_NOCHKSUM_OFS
{
	EEP_OLINFO_OFS = 0x00,
	EEP_ALMINFO_OFS = 0x02,
	EEP_ALMCODE_OFS = 0x04,
	EEP_ALMTIME_OFS = 0x18,
	EEP_ENGINFO_OFS = 0x40,
	EEP_MOTORSN_OFS = 0x52,	
	EEP_ALMMON_OFS = 0x62,
	EEP_LCDINF_OFS = 0xA2,
};
	
/*--------------------------------------------------------------------------------------------------*/
/*	   Structure of the no-checksum informations for each Axis 									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16	OLInfo;
	UINT16	AlarmInfo;
	UINT16	AlarmCode[10];
	UINT32	AlarmTime[10];
	UINT8	EngInfo[16];
	UINT16	Dummy;	
	UINT16	Dummy2; 
	UINT16	AlarmMon[32];
	UINT16	LcdInfo[55];

} EEP_NOCHKSUM_VAL_T;


/*--------------------------------------------------------------------------------------------------*/
/*	   Offset address of the no-checksum informations for the device unit						    */
/*--------------------------------------------------------------------------------------------------*/
enum EEP_NOCHKSUM_CMN_OFS
{
	EEP_TIMESTAMP_OFS = 0x00,
	EEP_OPTHST_OFS = 0x04,
	EEP_OPTID_OFS = 0x06,
	EEP_BOOTINF_OFS = 0x0C,
	EEP_AVRINTERTMPR_OFS = 0x16,		
	EEP_RESERVED_CMN_OFS = 0x18,		
};


/*--------------------------------------------------------------------------------------------------*/
/*	   Structure of EEPROM Block headder 											            	*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16	SizeBlockA;
	UINT16	FormatID;
	UINT32	VendorID;
	UINT32	ProductID;
	UINT16	BoardID;
	UINT16	Data;
	CHAR	ProductName[32];
	CHAR	SerialNumber[16];
	UINT16	Checksum;
	UINT16	SizeBlockB;
} EEP_HEADER_INFO;


/*--------------------------------------------------------------------------------------------------*/
/*	   Structure of EEPROM Que buffer 											            	    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT32			devID;
	UINT16			address;
	UINT16			wrWordSz;
	UINT32			wrValue;
} EEP_QUE_BUFFER;


/*--------------------------------------------------------------------------------------------------*/
/*	   Structure of EEPROM Que handle											            	    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT32			maxQueSize;
	UINT32			QueIdx;
	UINT32			nextQueIdx;
	EEP_QUE_BUFFER	*QueBuffer;
} EEP_QUE_HANDLE;


PUBLIC INT32 EepdevReadValues(UINT16 address, UINT16 *rdValue, UINT16 rdWordSz);
PUBLIC INT32 EepdevWriteNoSumValues(UINT16 address, UINT16 *wrValue, UINT16 wrWordSz);
PUBLIC INT32 EepdevWriteParameters(UINT16 address, UINT16 *wrValue, UINT16 wrWordSz);

PUBLIC void EepdevCreateQueue(EEP_QUE_HANDLE *hEepQue, EEP_QUE_BUFFER *QueBuf, UINT32 MaxQueSize);
PUBLIC INT32 EepdevPutQueue(EEP_QUE_HANDLE *hEepQue, UINT32 devID,
													  UINT16 address, UINT32 wrValue, UINT16 wrWordSz);
PUBLIC void EepdevWriteQuedValues(EEP_QUE_HANDLE *hEepQue, UINT32 maxWriteNum);


#endif // _EEPROM_IF_H_

