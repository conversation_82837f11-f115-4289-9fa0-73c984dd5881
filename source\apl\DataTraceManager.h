/****************************************************************************************************
 *
 * FILE NAME:  DataTraceManager.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.28
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	28-10-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _DATA_TRACE_MANAGER_H_
#define	_DATA_TRACE_MANAGER_H_
	
#include "RegAccessIF.h"
#include "MemobusIF.h"

/*--------------------------------------------------------------------------------------------------*/
/*		Variable Type Definition																	*/
/*--------------------------------------------------------------------------------------------------*/
/*--------------------------------------------------------------------------------------------------*/
/*		Trace execution command definitions														    */
/*--------------------------------------------------------------------------------------------------*/
#define	TRCCMD_NOCMD			0x00				// No command							        
#define	TRCCMD_DATATRACE		0x01				// Trace execution instructions			        
#define	TRCCMD_RINGTRACE		0x02				// Continuous trace execution instructions		
#define	TRCCMD_CONTTRACE		0x03				// Trace execution instructions(maintenance mode) 
/*--------------------------------------------------------------------------------------------------*/
#define	TRC_CH_MAXNUM			4					// Number of trace channels					
/*--------------------------------------------------------------------------------------------------*/
#define	OPEMODE_NOCMD			0x0000				// Normal mode									
#define	OPEMODE_DATATRACE		0x2000				// Data trace mode						
#define	OPEMODE_RINGTRACE		0x200A				// Continuous trace mode				
/*--------------------------------------------------------------------------------------------------*/
#define	TRC_BUF_SIZE			0x6000				// Data trace buffer size(UINT16)		
/*--------------------------------------------------------------------------------------------------*/
#define	DATATRACE_SHORT			0x00				// Short data trace						
#define	DATATRACE_LONG			0x01				// Long data trace					
/*--------------------------------------------------------------------------------------------------*/
#define	IODATA_MERGE			0x00				// Combining IO data with variable data
#define	IODATA_DIV				0x01				// Store IO data in a dedicated area
/*--------------------------------------------------------------------------------------------------*/
#define	SINGLE_TRG				0x00				// Single trigger data trace
#define	MULTI_TRG				0x01				// Multi trigger data trace
/*--------------------------------------------------------------------------------------------------*/
#define	TRG_SEL2_USE			0x0F				// Use trigger data selection 2			
#define	NET_TRG_SEL				0x0F				// Use network trigger			


/*--------------------------------------------------------------------------------------------------*/
/*		Trace control area														                    */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{
	UINT16	TrcMode;								// Trace mode							
	UINT16	DataSize;								// Trace data length						
	UINT16	AreaSize;								// Use area size						
	UINT16	SampIntv;								// Trace sampling interval				
//	UINT16	SampTime;								// Trace sampling unit[Us]		
//	UINT16	SampLlim;								// Trace sampling interval lower limit				
//	UINT16	SampUlim;								// Trace sampling interval upper limit				
	UINT16	MaxAreaSize;							// Maximum trace buffer length					
	UINT16	OpeModeTrc;								// Trace dedicated operation mode setting / reference			
	UINT16	OpeCmdTrc;								// Trace dedicated operation settings				
	UINT16	TrcBuffSize;							// Trace buffer length					
	UINT16	MaxTrcBuffSize;							// Maximum trace buffer length					
	/*----------------------------------------------------------------------------------------------*/
//	UINT16	DataIOTrcSel[TRC_CH_MAXNUM];			// Data and I / O trace target selection(1--16)
//	UINT16	TrcAxisSel[TRC_CH_MAXNUM];				// Axis selection for data trace(1--16)		
//	UINT16	TrcDataSel[TRC_CH_MAXNUM];				// Data trace target data selection(1--16)
//	UINT16	IOTrcAxisSel[TRC_CH_MAXNUM];			// (1--16)	
//	UINT16	IOTrcDataSel[TRC_CH_MAXNUM];			// I/O trace 1 target axis selection(1--16)	
//	UINT32	*TrcAdr[TRC_CH_MAXNUM];					// Trace target address specification
	/*----------------------------------------------------------------------------------------------*/
	UINT16	PtrgDnum;								// Number of pre-trigger data							
	UINT16	TrgSel;									// Trigger condition								
	INT32	TrgLevel;								// Trigger level								
	UINT16	TrgPoint;								// Trigger position							
	UINT16	TrcState;								// Trace complete						
	UINT32 	RngTrcPtr;								// Continuous trace writing position					
	/*----------------------------------------------------------------------------------------------*/
	UINT16	MTrgMode;								// Multi-trigger setting						
	UINT16	MTrgSel;								// Trigger condition 2								
	INT32	MTrgLevel;								// Trigger level 2								
} TRCREG;



/*--------------------------------------------------------------------------------------------------*/
/*		Real address definition																		*/
/*--------------------------------------------------------------------------------------------------*/
typedef	union 
{
	UINT16	*ShortAdrData;							// Real addressing pointer 								
	UINT32	*LongAdrData;							// Real addressing pointer 								
} TRCADRDATA;

/*--------------------------------------------------------------------------------------------------*/
/*		Trace target definition																		*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{					
	UINT16	TrcExeflg;								// Trace execution control flag (0x01: execution, other: non-execution)
	void	*TrcBuf;								// Trace data storage buffer					
	TRCADRDATA	Adr;								// Real address definition										

	struct
	{							
		void	*AxisPtr;							// Numerical trace AXIS_HANDLE 			
		UINT16	VxSftL;								// Numeric trace variable left shift scale
		UINT16	VxSftR;								// Numeric trace variable right shift scale		
		INT32	VxData;								// Numerical trace data				
		INT32	VxGain;								// Numerical trace calculation gain	
		BOOL	AvrgSel;							// Numerical trace average selection: TRUE: Averaging, FALSE: No	
		INT32	(*GetData)( void *Axis );			// Trace variable address acquisition function
	} TrcV;

	struct 
	{
		void	*AxisPtr;							// Bit trace AXIS_HANDLE		
		BOOL	BtvData;							// Bit trace variable address	
		BOOL	(*GetData)( void *Axis );			// Trace variable address acquisition function
	} TrcB;

} TRCOBJ;


/*--------------------------------------------------------------------------------------------------*/
/*		Data trace individual parameters (target data, target axis)								    */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{
	UINT16	TrcAxis[TRC_CH_MAXNUM];					// Axis selection for data trace(1--16)
	UINT16	TrcData[TRC_CH_MAXNUM];					// Data trace target data selection(1--16)
	UINT16	IOTrcAxis[TRC_CH_MAXNUM];				// I/O trace 1 target axis selection(1--16)
	UINT16	IOTrcData[TRC_CH_MAXNUM];				// I/O trace 1 target data selection(1--16)
} TRCINDIVPRM;


/*--------------------------------------------------------------------------------------------------*/
/*		Trace trigger variable information definition												*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{						
	UINT16	TrgSel;									// Trace trigger selection									
	UINT16	TrgEdge;								// Trace trigger edge specification			
	UINT16	TrgVsft;								// Trace trigger data shift			
	INT32	TrgLevel;								// Trace trigger level				
	UINT32	TrgBitSetMsk;							// Trace trigger bit mask				
	void	*TrgBufx;								// Trace trigger data buffer		
}TRCTRG;

/*--------------------------------------------------------------------------------------------------*/
/*		Trace execution parameter definition														*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{					
	UINT16	TrcPrmOk;								// Trace parameter OK							
	UINT16	TrcDataType;							// Trace data type						
	UINT16	TrcIOType;								// IO trace data type				
	UINT32	TrcDnum;								// Number of trace data					
	UINT32	DataSampIntv;							// Trace data sampling interval		
	INT32	UpperLmt;								// Trace data upper limit					
	INT32	LowerLmt;								// Trace data lower limit					
	UINT32	TrcBitSet;								// For trace bit set					
	UINT32	TrcBitClr;								// For clearing trace bit					
	/*----------------------------------------------------------------------------------------------*/
	TRCTRG	TrcTrg[2];								// Trace trigger variable information (multi-trigger supported)	
	TRCOBJ	TrcObj[TRC_CH_MAXNUM];					// Trace variable information						
} TRCEXEP;

/*--------------------------------------------------------------------------------------------------*/
/*		Trace execution control variable definition													*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{					
	UINT16	TrcCmd;									// Trace execution command							
	UINT16	Opened;	 								// Trace function already open						
	UINT16	OpmRun;									// During trace operation mode execution (for OpeMode control)	
	UINT16	TrcStep;								// Trace execution step								
	UINT16	TrcSamp;								// Trace sampling									
	UINT16	TrcIndx;								// Trace index								
	UINT16	TrcRepeat;								// Trace index		    
	UINT16	TrcPrelss;								// Trace index	    
	UINT16	TrcTrigNum;								//    
	UINT16  RngTrcCnt;								// Number of continuous trace writes					
	INT32	TrgVar[2];								// Trigger check variable((current value, previous value))	
	INT32	MTrgVar[2];								// Trigger check variable (current value, previous value) (multi-trigger supported)
	BOOL	NetTrigger;								// Network trigger information							
} TRCEXEV;

/*--------------------------------------------------------------------------------------------------*/
/*		Variable definition for data trace management												*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{				
	UINT32	TrcVarTblEntNum;						// Number of registered numeric trace definition tables
	UINT32	TrcVarTblSrhIniW;						// Numerical trace definition table search partition width initial value
	UINT32	TrcBitTblEntNum;						// Number of registered bit trace definition tables
	UINT32	TrcBitTblSrhIniW;						// Bit trace definition table search partition width initial value			
} TRCMNGP;

/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{					
	UINT32	TrcOpeRegWrtCnt;						// For parameter calculation execution check	
	UINT32	TdrvOpeRegWrtCnt;						// For parameter calculation execution check	
} TRCMNGV;

typedef	struct	
{					
    UINT16 WriteDnum;
    UINT16 SaveDnum;
    UINT16 ChannelAddr;
    UINT16 FrameCNT;
    UINT16 bSendEn;
    UINT16 bReSendEn;
    UINT16 AxisIndex;
    UINT16 DeviceAddr;
} TRCUPLOAD;
/*--------------------------------------------------------------------------------------------------*/
/*		Trace execution variable definition												            */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{
	TRCREG	TrcReg;									// Trace control area register
	TRCEXEP	TrcExeP;								// Trace execution parameters
	TRCEXEV	TrcExeV;								// Trace execution control variables
	TRCMNGP	TrcMngP;								// Trace management parameters
	TRCMNGV	TrcMngV;								// Trace management data variables
	TRCINDIVPRM	TrcIncivPrm;						// Trace individual parameters	
    TRCUPLOAD   TrcUpload;
	void	*AxisPtr;							    // Bit trace AXIS_HANDLE	
}TRCHNDL;

/****************************************************************************************************/
/*		PROTOTYPE																					*/
/****************************************************************************************************/
PUBLIC void	  DtrcInitDtrcManager( TRCHNDL *TrcHndl, UINT16 *TraceBuffer);
PUBLIC PRM_RSLT DtrcCalcParameter( TRCHNDL *TrcHndl, UINT16 *TraceBuffer );
PUBLIC PRM_RSLT DtrcWriteOpeModeTrcRegister( TRCHNDL *TrcHndl, UINT16 WrData );
PUBLIC PRM_RSLT DtrcWriteOpeCmdTrcRegister( TRCHNDL *TrcHndl, UINT16 WrData );
PUBLIC void	  DtrcExecute( TRCHNDL *TrcHndl );
PUBLIC void	  DtrcProcedure( TRCHNDL *TrcHndl, INT32 Idx );
PUBLIC void	  DtrcSetTrcVar( TRCHNDL *TrcHndl );
PUBLIC void	  DtrcResetAverageVar( TRCHNDL *TrcHndl );
PUBLIC void	  DtrcInitExecute( TRCHNDL *TrcHndl );
PUBLIC void	  DtrcInitExecuteRingTrc( TRCHNDL *TrcHndl );
PUBLIC void	  DtrcSetNetworkTrigger( TRCHNDL *TrcHndl, BOOL Trigger ); /* <S012> */

/****************************************************************************************************/
/*		API's																						*/
/****************************************************************************************************/
PUBLIC UINT32 DtrcGetSampIntv( TRCHNDL *TrcHndl );
PUBLIC UINT32 DtrcGetTrcDataNum( TRCHNDL *TrcHndl );
PUBLIC void   DtrcSetTrgPointToZero( TRCHNDL *TrcHndl );
PUBLIC INT32  DtrcSearchTrcVarTbl( TRCHNDL *TrcHndl, INT32 SelNo );

/***************************************** end of file **********************************************/



#endif  // _DATA_TRACE_MANAGER_H_
