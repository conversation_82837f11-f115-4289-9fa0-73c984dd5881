/****************************************************************************************************
 *
 * FILE NAME:  MLib.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseDef.h"
#include "Mlib.h"
#include "math.h"

#define	CCSW_HsMulgain			1	

#define	SCUP31_SXINC( x, s, wk )\
		wk=0x00008000; if( (UINT32)x <  (UINT32)wk ){ x=(x<<16); s=s+16;}\
		wk=(wk<<8)   ; if( (UINT32)x <  (UINT32)wk ){ x=(x<< 8); s=s+ 8;}\
		wk=(wk<<4)   ; if( (UINT32)x <  (UINT32)wk ){ x=(x<< 4); s=s+ 4;}\
		wk=(wk<<2)   ; if( (UINT32)x <  (UINT32)wk ){ x=(x<< 2); s=s+ 2;}\
		wk=(wk<<1)   ; if( (UINT32)x <  (UINT32)wk ){ x=(x<< 1); s=s+ 1;}
/*--------------------------------------------------------------------------------------------------*/
/*		SCUP31_SXDEC( ) : x = (x<<sx), s = (s-sx),  0x40000000 <= x < 0x80000000					*/
/*--------------------------------------------------------------------------------------------------*/
#define	SCUP31_SXDEC( x, s, wk )\
		wk=0x00008000; if( (UINT32)x <  (UINT32)wk ){ x=(x<<16); s=s-16;}\
		wk=(wk<<8)   ; if( (UINT32)x <  (UINT32)wk ){ x=(x<< 8); s=s- 8;}\
		wk=(wk<<4)   ; if( (UINT32)x <  (UINT32)wk ){ x=(x<< 4); s=s- 4;}\
		wk=(wk<<2)   ; if( (UINT32)x <  (UINT32)wk ){ x=(x<< 2); s=s- 2;}\
		wk=(wk<<1)   ; if( (UINT32)x <  (UINT32)wk ){ x=(x<< 1); s=s- 1;}
		

/****************************************************************************************************
 * DESCRIPTION:
 *		Unsigned 32-bit division function (rounded)
 *		1) Calculation result : kx = (ax/bx)<<rep;								
 *		2) Calculation condition : 0 < ax < bx <= 0x7FFFFFFF
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 XasmDIVUB( INT32 ax, INT32 bx, INT32 rep )
{
	INT32	i,kx;
	ax = (ax<<1);
	for( kx=0,i=0; i<rep; i++ )
	{
		kx = (kx<<1);
		if( (UINT32)ax >= (UINT32)bx )
		{
			kx = kx + 1;
			ax = (UINT32)ax - (UINT32)bx;
		}
		ax = (ax<<1);
	}
	if( (UINT32)ax >= (UINT32)bx ){ kx = kx + 1;}
	return( kx );
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Unsigned 32-bit division function (rounded)
 *		1) Calculation result : {kx,sx} = {(ax/bx)<<(rep-0/1), sx=sx+(rep-0/1)]};								
 *		2) Calculation condition : 0 < ax <= 0x7FFFFFFF, 0 < bx <= 0x7FFFFFFF, (ax/2) < bx < (2*ax)
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 XasmDIVUXB( INT32 ax, INT32 bx, INT32 *sx, INT32 rep )
{
	INT32	i,kx;
	
	if( (UINT32)ax < (UINT32)bx )
	{
		ax = (ax<<1); *sx = *sx + 1;
	}
	for( kx=0,i=0; i<rep; i++ )
	{
		kx = (kx<<1);
		if( (UINT32)ax >= (UINT32)bx )
		{
			kx = kx + 1;
			ax = (UINT32)ax - (UINT32)bx;
		}
		ax = (ax<<1);
	}
	if( (UINT32)ax >= (UINT32)bx ){ kx = kx + 1;}
	*sx = *sx + rep - 1;
	return( kx );
}


/****************************************************************************************************
 * DESCRIPTION:
 *		
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 XasmDIVUXA( INT32 ax, INT32 bx, INT32 *sx, INT32 rep )
{
	INT32	i,kx;

	if( (UINT32)ax < (UINT32)bx )
	{
		ax = (ax<<1); *sx = *sx + 1;
	}
	for( kx=0,i=0; i<(rep-1); i++ )
	{
		if( (UINT32)ax >= (UINT32)bx )
		{
			kx = kx + 1;
			ax = (UINT32)ax - (UINT32)bx;
		}
		kx = (kx<<1); ax = (ax<<1);
	}
	if( (UINT32)ax >= (UINT32)bx ){ kx = kx + 1;}
	*sx = *sx + rep - 1;

	return( kx );
}


/****************************************************************************************************
 * DESCRIPTION:
 *		{kx,sx} = (a*b/c)<<sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalKxgain( INT32 a, INT32 b, INT32 c, INT32 *psx, INT32 smax )
{
	INT32	wk;
	INT32	kx;
	INT32	sx;
	INT32	sy;
	INT32	sign;
	INT32	x,xx[2];

	sx = (psx != NULL)? (*psx) : 0;

	if( (a==0)||(b==0)||(c==0) )
	{
		if( psx != NULL ){ *psx = 0;}
		return( 0 );
	}

	sign = 0;
	if( a < 0 ){ a=-a; sign=sign^0x01;}
	if( b < 0 ){ b=-b; sign=sign^0x01;}
	if( c < 0 ){ c=-c; sign=sign^0x01;}
/*--------------------------------------------------------------------------------------------------*/
/*		????																					*/
/*--------------------------------------------------------------------------------------------------*/
	MULU32U32( (UINT32)a, (UINT32)b, (UINT32*)xx );
/*--------------------------------------------------------------------------------------------------*/
/*		?????????? : 0x40000000 <= x < 0x80000000											*/
/*--------------------------------------------------------------------------------------------------*/
	if( xx[1] > 0 )
	{
		sy = 32;
		x  = xx[1];
		SCUP31_SXDEC( x, sy, wk );
		x = x + (((UINT32)xx[0])>>(sy));
		sx = sx - sy;
	}
	else if( xx[0] > 0 )
	{
		x  = xx[0];
		SCUP31_SXINC( x, sx, wk );
	}
	else
	{
		sx = sx - 1;
		x  = (((UINT32)xx[0])>>1);
	}
/*--------------------------------------------------------------------------------------------------*/
/*		?????????? : 0x40000000 <= c < 0x80000000											*/
/*--------------------------------------------------------------------------------------------------*/
	SCUP31_SXDEC( c, sx, wk );
/*--------------------------------------------------------------------------------------------------*/
/*		?? : kx = x/c	(32bit/32bit,???:24bit)													*/
/*--------------------------------------------------------------------------------------------------*/
	kx = XasmDIVUXA( x, c, &sx, 24 );
/*--------------------------------------------------------------------------------------------------*/
/*		??????																				*/
/*--------------------------------------------------------------------------------------------------*/
	if( smax == 0 )									/* ????									*/
	{
		if( sign ){ kx = -kx;}						/* ????									*/
	}
/*--------------------------------------------------------------------------------------------------*/
	else if( smax > 0 )								/* ????????							*/
	{
		kx = kx>>1; sx = sx - 1;					/* kx : 24bit --> 23bit						*/
		if( (sx-smax)>31 ){ kx = 0;}				/* ??????????						*/
		if(  sx < 0      ){ kx=0x007FFFFF; sx=0;}	/* ????(2^23-1)?????				*/
		if(  sx > smax   ){ kx=(kx>>(sx-smax)); sx=smax;}	/* smax?????					*/
		if(  sign != 0   ){ kx = -kx;}				/* ????									*/
		kx = (sx<<24) + (kx&0x00FFFFFF);			/* ????????							*/
	}
/*--------------------------------------------------------------------------------------------------*/
	else if( smax == -1 )							/* ????????							*/
	{
		kx = kx>>1; sx = sx - 1;					/* kx : 24bit --> 23bit						*/
		if( (sx > 127) || (sx < -128) )				/* sx????								*/
		{
			kx = sx = 0;							/* ??????????????				*/
		}
		if( sign != 0 ){ kx = -kx;}					/* ????									*/
		kx = (sx<<24) + (kx&0x00FFFFFF);			/* ????????							*/
	}
/*--------------------------------------------------------------------------------------------------*/
	else if( (smax == -2) && CCSW_HsMulgain )		/* ????????(16bit.16bit)			*/
	{												/* for MlibHsMulgain, MlibHsIntegral,etc	*/
		sx = sx - 16;
		if( sx == 0 )      { /*kx = kx*/;        }		/* ???????							*/
		else if( sx >= 24 ){ kx = 0;         }		/* ??????????						*/
		else if( sx <= -8 ){ kx = 0x7FFFFFFF;}		/* ???(2^31-1)?????					*/
		else if( sx >= 0  ){ kx = kx>>sx;    }		/* ???&????							*/
		else               { kx = kx<<(-sx); }		/* ???&????							*/
		sx = 16;									/* ????(sx=16)							*/
		if( sign != 0 ){ kx = -kx;}					/* ????									*/
	}
/*--------------------------------------------------------------------------------------------------*/
	else if( smax == -24 )							/* INT32???????(????)				*/
	{
		if( sx < 0 )      { kx = 0x01000000;}		/* ???(2^24)?????					*/
		else if( sx > 32 ){	kx = 0;         }		/* ??????????						*/
		else if( sx == 0 ){	/*kx = kx*/; 		}		/* ???????							*/
		else   { kx = (((kx>>(sx-1))+1)>>1);}		/* ???&????							*/
		sx = 0;										/* ????(sx=0)							*/
		if( sign != 0  ){ kx = -kx;}				/* ????									*/
	}
/*--------------------------------------------------------------------------------------------------*/
	else											/* INT32???????(????)				*/
	{
		kx = kx << 6; sx = sx + 6;
		if( sx < 0 )      { kx = 0x40000000;}   	/* ???(2^30)?????					*/
		else if( sx > 32 ){	kx = 0;         }   	/* ??????????						*/
		else if( sx == 0 ){	/*kx = kx*/; 		}   	/* ???????							*/
		else   { kx = (((kx>>(sx-1))+1)>>1);}		/* ???&????							*/
		sx = 0;										/* ????(sx=0)							*/
		if( sign != 0  ){ kx = -kx;}				/* ????									*/
	}
/*--------------------------------------------------------------------------------------------------*/
	if( psx != NULL ){ *psx = sx;}
	return( kx );

}

/****************************************************************************************************
 * DESCRIPTION:
 *		Proportional gain addition : {kx,sx} = {ka,sa} + {kb,sb}
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalKxaddx( INT32 ka, INT32 sa, INT32 kb, INT32 sb, INT32 *psx )
{
	INT32	kx;
	INT32	ds;

	ds = sa - sb;
/*--------------------------------------------------------------------------------------------------*/
	if( ka ==   0 ){ *psx = sb; return( kb );}
	if( kb ==   0 ){ *psx = sa; return( ka );}
	if( ds >=  32 ){ *psx = sb; return( kb );}
	if( ds <= -32 ){ *psx = sa; return( ka );}
/*--------------------------------------------------------------------------------------------------*/
	if( ds < 0 ){ kb = (kb>>(-ds));}
	if( ds > 0 ){ ka = (ka>>ds); sa = sb;}

	kx = ka + kb;
	if( ((ka>0)&&(kb>0)&&(kx<=0)) || ((ka<0)&&(kb<0)&&(kx>=0)) )
	{
		kx = (ka>>1) + (kb>>1); sa--;
	}
	if( kx == 0 ){ sa = 0;}
/*--------------------------------------------------------------------------------------------------*/
	*psx = sa;
	return( kx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *		Proportional gain subtraction : {kx,sx} = {ka,sa} - {kb,sb}
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalKxsubx( INT32 ka, INT32 sa, INT32 kb, INT32 sb, INT32 *psx )
{
	INT32	kx;
	INT32	ds;

	ds = sa - sb;

	if( (ka == 0) || (ds >= 32)  )
	{
		if( kb == 0x80000000 )
		{
			kb = kb >> 1; sb--;
		}
		*psx = sb; return( -kb );
	}

	if( kb ==   0 ){ *psx = sa; return(  ka );}
	if( ds <= -32 ){ *psx = sa; return(  ka );}

	if( ds < 0 ){ kb = (kb>>(-ds));}
	if( ds > 0 ){ ka = (ka>>ds); sa = sb;}

	kx = ka - kb;
	if( ((ka>0)&&(kb<0)&&(kx<=0)) || ((ka<0)&&(kb>0)&&(kx>=0)) )
	{
		kx = (ka>>1) - (kb>>1); sa--;
	}
	if( kx == 0 ){ sa = 0;}

	*psx = sa;
	return( kx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *		Proportional gain multiplication : {kx,sx} = (a*b*c)<<sx;	0x40000000 <= kx < 0x80000000
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalKxmulx( INT32 a, INT32 b, INT32 c, INT32 *psx )
{
	INT32	kx;
	INT32	sx;
	INT32	wk;
	INT32	sign;
	INT32	xx[2];
	INT32	yy[2];
	INT32	zz[2];
	INT32	xxx[3];

	if( (a==0)||(b==0)||(c==0) )
	{
		*psx = 0;
		return( 0 );
	}

	sign = 0;
	if( a < 0 ) { a = -a; sign =  1;}
	if( b < 0 ) { b = -b; sign += 1;}
	if( c < 0 ) { c = -c; sign += 1;}

	MULU32U32( (UINT32)a,    (UINT32)b, (UINT32*)xx );
	MULU32U32( (UINT32)xx[0], (UINT32)c, (UINT32*)yy );
	MULU32U32( (UINT32)xx[1], (UINT32)c, (UINT32*)zz );
	xxx[0] = yy[0];
	xxx[1] = zz[0] + yy[1];
	xxx[2] = zz[1];
	if( ((UINT32)xxx[1]<(UINT32)zz[0])||((UINT32)xxx[1]<(UINT32)yy[1]) )
	{
		xxx[2] = xxx[2] + 1;
	}

	if( xxx[2] != 0 )
	{
		sx = 0;
		kx = xxx[2];
		SCUP31_SXINC( kx, sx, wk );
		kx = kx + (((UINT32)xxx[1])>>(32-sx));
		sx = sx - 64;
	}

	else if( xxx[1] != 0 )
	{
		if( xxx[1] < 0 )
		{
			sx = -33;
			kx = (((UINT32)xxx[1])>>1);
		}
		else
		{
			sx = 0;
			kx = xxx[1];
			SCUP31_SXINC( kx, sx, wk );
			kx = kx + ((((UINT32)xxx[0])>>(31-sx))>>1);
			sx = sx - 32;
		}
	}

	else
	{
		if( xxx[0] < 0 )
		{
			sx = -1;
			kx = (((UINT32)xxx[0])>>1);
		}
		else
		{
			sx = 0;
			kx = xxx[0];
			SCUP31_SXINC( kx, sx, wk );
		}
	}

	*psx = *psx + sx;
	if( sign & 0x01 )
	{
		return( -kx );
	}
	else
	{
		return(  kx );
	}

}


/****************************************************************************************************
 * DESCRIPTION:
 *		Proportional gain division : {kx,sx} = {ka,sa}/{kb,sb};
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalKxdivx( INT32 ka, INT32 sa, INT32 kb, INT32 sb, INT32 smax )
{
	INT32	kx;
	INT32	sx;
	INT32	wk;
	INT32	sign;

	if( (ka==0)||(kb==0) )
	{
		return( 0 );
	}

	sign = 0;
	if( ka < 0 ) { ka = -ka; sign =  1;}
	if( kb < 0 ) { kb = -kb; sign += 1;}

	sx = sa - sb;
	SCUP31_SXINC( ka, sx, wk );
	SCUP31_SXDEC( kb, sx, wk );

	kx = XasmDIVUXA( ka, kb, &sx, 23 );

	if( (sx-smax)>31 ){ kx = 0;}
	if( sx > smax    ){ kx=(kx>>(sx-smax)); sx=smax;}
	if( sx < 0       ){ kx=0x007FFFFF;      sx=0;   }
	if( sign & 0x01  ){ kx = -kx;}
	kx = (sx<<24) + (kx&0x00FFFFFF);

	return( kx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *		
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibScalKxgain( INT32 a, INT32 b, INT32 c, INT32 *psx, INT32 smax )
{
	INT32	kx;

/*--------------------------------------------------------------------------------------------------*/
/*		call MlibPcalKxgain()																		*/
/*--------------------------------------------------------------------------------------------------*/
	if( psx != NULL ){ *psx = 0;}
	kx = MlibPcalKxgain( a, b, c, psx, smax );
/*--------------------------------------------------------------------------------------------------*/
	return( kx );

}

/****************************************************************************************************
 * DESCRIPTION:
 *		{kx,sx} = {ka,sa}*b/c<<sx;	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibScalKskxkx( INT32 ksa, INT32 b, INT32 c, INT32 *psx, INT32 smax )
{
	INT32	a;
	INT32	kx,sx;

	a  = ((ksa<<8)>>8);
	sx = (ksa>>24);

	/* call MlibPcalKxgain() */
	if( psx != NULL )
	{
		*psx = sx;
		kx = MlibPcalKxgain( a, b, c, psx, smax );
	}
	else
	{
		kx = MlibPcalKxgain( a, b, c, &sx, smax );
	}

	return( kx );

}



/****************************************************************************************************
 * DESCRIPTION:
 *           Extended gain calculation : {kx,sx} = a*{kb,sb}/c<<sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalKxkskx( INT32 a, INT32 ksb, INT32 c, INT32 *psx, INT32 smax )
{
	INT32	b;
	INT32	kx,sx;

	b  = ((ksb<<8)>>8);
	sx = (ksb>>24);

	if( psx != NULL )
	{
		*psx += sx;
		kx = MlibPcalKxgain( a, b, c, psx, smax );
	}
	else
	{
		kx = MlibPcalKxgain( a, b, c, &sx, smax );
	}

	return( kx );

}

/****************************************************************************************************
 * DESCRIPTION:
 *           Extended gain calculation : {kx,sx} = a*{kb,sb}/{kc,sc}<<sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalKxksks( INT32 a, INT32 ksb, INT32 ksc, INT32 *psx, INT32 smax )
{
	INT32	b,c;
	INT32	kx,sx;

	b  = ((ksb<<8)>>8);
	c  = ((ksc<<8)>>8);
	sx = (ksb>>24) - (ksc>>24);

	if( psx != NULL )
	{
		*psx += sx;
		kx = MlibPcalKxgain( a, b, c, psx, smax );
	}
	else
	{
		kx = MlibPcalKxgain( a, b, c, &sx, smax );
	}

	return( kx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *           Extended gain calculation (for calculation start) : {kx,sx} = a*{kb,sb}/{kc,sc}<<sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibScalKxksks( INT32 a, INT32 ksb, INT32 ksc, INT32 *psx, INT32 smax )
{
	INT32	b,c;
	INT32	kx,sx;


	b  = ((ksb<<8)>>8);
	c  = ((ksc<<8)>>8);
	sx = (ksb>>24) - (ksc>>24);

	if( psx != NULL )
	{
		*psx = sx;
		kx = MlibPcalKxgain( a, b, c, psx, smax );
	}
	else
	{
		kx = MlibPcalKxgain( a, b, c, &sx, smax );
	}

	return( kx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Extended gain calculation (for calculation start) : {kx,sx} = a*{kb,sb}/c<<sx;		
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibScalKxkskx( INT32 a, INT32 ksb, INT32 c, INT32 *psx, INT32 smax )
{
	INT32	b;
	INT32	kx,sx;

	b  = ((ksb<<8)>>8);
	sx = (ksb>>24);

	if( psx != NULL )
	{
		*psx = sx;
		kx = MlibPcalKxgain( a, b, c, psx, smax );
	}
	else
	{
		kx = MlibPcalKxgain( a, b, c, &sx, smax );
	}

	return( kx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Extended gain calculation (for calculation start) : {kx,sx} = {ka,sa}*{kb,sb}/c<<sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibScalKskskx( INT32 ksa, INT32 ksb, INT32 c, INT32 *psx, INT32 smax )
{
	INT32	a,b;
	INT32	kx,sx;

	a  = ((ksa<<8)>>8);
	b  = ((ksb<<8)>>8);
	sx = (ksa>>24) + (ksb>>24);

	if( psx != NULL )
	{
		*psx = sx;
		kx = MlibPcalKxgain( a, b, c, psx, smax );
	}
	else
	{
		kx = MlibPcalKxgain( a, b, c, &sx, smax );
	}

	return( kx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *           First-order filter gain calculation
 *																									
 *		1) Time constant :  kf =  (ts/(tx+ts))<<24															
 *																									
 *						      (2*hz*ts)*(2^24)														
 *		2) 0.1Hz  :  kf = -------------------------  ,  (10^7/pai) = 3183098.862					
 *						   (2*hz*ts) + (10^7/pai)													
 *																									
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalKf1gain(
		INT32	tx,			// Time constant (Tx)/frequency(Fx)[xs],[0.1Hz] (X=0:Tx, X=1:Fx)
		INT32	ts,			// Calculation cycle[xs],[us/ns] (Y=0:us, Y=1:ns)	
		INT32	insel	)	// Input specification (0xYX) [X:Tx/Fx selection, Y:us/ns selection]
{
	INT32	kx;					
	INT32	ax;				
	INT32	bx;				
	UINT32	xx[2];				

	if( (tx == 0) || (ts == 0) )
//    if( ts == 0 )
	{
		return( 0 );
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Gain calculation process: Time constant [xs]												*/
/*--------------------------------------------------------------------------------------------------*/
/*				  ts																				*/
/*		kf =  ---------- * (2^24)																	*/
/*				ts + tx																				*/
/*--------------------------------------------------------------------------------------------------*/
	if( (insel & 0x0F) == 0x00 )		
	{
		ax = ts;								/* Min( ax )= 1us									*/
		bx = ts + tx;							/* Max( bx )= 1us + 16777215us (Max(tx)=16.7 sec)	*/
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Gain calculation process: Frequency [0.1Hz] && Calculation cycle [us]						*/
/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*					(hz*ts)*(2^24)																	*/
/*		kf = ---------------------------  ,  (10^7/(2*pai)) = 1591549.431							*/
/*			  (hz*ts) + (10^7/(2*pai))																*/
/*																									*/
/*		Ts= 125us : Max.Frequency = 2^32 /  125 / 10 = 3435973.837 Hz								*/
/*		Ts=1000us : Max.Frequency = 2^32 / 1000 / 10 =  429496.729 Hz								*/
/*		Min( kf ) = ((hz*ts) / 1591549) * 2^24 = 10.5414 * (hz*ts) = 10.5414						*/
/*																									*/
/*--------------------------------------------------------------------------------------------------*/
	else if( (insel & 0xF0) == 0x00 )		
	{
		MULU32U32( ts, tx, xx );				/* xx[] = hz*ts										*/
		if( xx[1] > 0x00 ){ return( 0 );}		/* if( (hz*ts) >= 2^32 ){ kf = 0;}					*/

		if( xx[0] >= 0x10000000 ){ ax = (xx[0]>>2); bx = ax +  397887;}
		else                     { ax = (xx[0]<<2); bx = ax + 6366198;}
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Gain calculation process: Frequency [0.1Hz] && Calculation cycle [ns]						*/
/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*					(hz*ts)*(2^24)																	*/
/*		kf = ---------------------------  ,  (10^10/(2*pai)) = 1591549431 = 0x5EDD1DF7				*/
/*			  (hz*ts) + (10^10/(2*pai))																*/
/*																									*/
/*		Ts=125us : Max.Frequency = 2^40 / 125000 / 10 = 879609.302 Hz								*/
/*		Min( kf ) = ((hz*ts) / 1591549431) * 2^24 = 0.0105414 * (hz*ts) --> Min( hz*ts ) >= 95		*/
/*																									*/
/*--------------------------------------------------------------------------------------------------*/
	else if( ts >= 1000 )				
	{
		MULU32U32( ts, tx, xx );				/* xx[] = hz*ts										*/
		if( xx[1] > 0xFF ){ return( 0 );}		/* if( (hz*ts) >= 2^40 ){ kf = 0;}					*/
		bx = (1591549431 >> 2);					/* bx = (0x5EDD1DF7>>2) = 0x17B7477D				*/
		/*------------------------------------------------------------------------------------------*/
		if(      xx[1] > 0x0F ){ ax = (xx[0]>>10) + (xx[1]<<22); bx = ax + (bx>>8);}
		else if( xx[1] > 0x00 ){ ax = (xx[0]>> 6) + (xx[1]<<26); bx = ax + (bx>>4);}
		else                   { ax = (xx[0]>> 2)              ; bx = ax + (bx>>0);}
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Input error: Calculation cycle [ns] <1000[ns]												*/
/*--------------------------------------------------------------------------------------------------*/
	else
	{
		return( 0 );
	}

	kx = XasmDIVUB( ax, bx, 24 );				/* kx = (ax/bx) * 2^24								*/

	return( kx );

}
/****************************************************************************************************
 * DESCRIPTION:
 *           First-order filter gain calculation
 *																									
 *		1) Time constant :  kf =  (ts/(tx+ts))<<24															
 *																									
 *						      (2*hz*ts)*(2^24)														
 *		2) 0.1Hz  :  kf = -------------------------  ,  (10^7/pai) = 3183098.862					
 *						   (2*hz*ts) + (10^7/pai)													
 *																									
 * RETURNS:
 *
****************************************************************************************************/
#pragma optimize=none
PUBLIC UINT32 MlibPcalKf1gain1(
		UINT32	tx,			// Time constant (Tx)/frequency(Fx)[xs],[0.1Hz] (X=0:Tx, X=1:Fx)
		UINT32	ts,			// Calculation cycle[xs],[us/ns] (Y=0:us, Y=1:ns)	
		UINT32	insel	)	// Input specification (0xYX) [X:Tx/Fx selection, Y:us/ns selection]
{
	UINT32	kx;					
	REAL32	ax;				
	REAL32	bx;				
	UINT32	xx[2];		
    REAL32  div = 0;

	if( (tx == 0) || (ts == 0) )
//    if( ts == 0 )
	{
		return( 0 );
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Gain calculation process: Time constant [xs]												*/
/*--------------------------------------------------------------------------------------------------*/
/*				  ts																				*/
/*		kf =  ---------- * (2^24)																	*/
/*				ts + tx																				*/
/*--------------------------------------------------------------------------------------------------*/
	if( (insel & 0x0F) == 0x00 )		
	{
		ax = ts;								/* Min( ax )= 1us									*/
		bx = ts + tx;							/* Max( bx )= 1us + 16777215us (Max(tx)=16.7 sec)	*/
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Gain calculation process: Frequency [0.1Hz] && Calculation cycle [us]						*/
/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*					(hz*ts)*(2^24)																	*/
/*		kf = ---------------------------  ,  (10^7/(2*pai)) = 1591549.431							*/
/*			  (hz*ts) + (10^7/(2*pai))																*/
/*																									*/
/*		Ts= 125us : Max.Frequency = 2^32 /  125 / 10 = 3435973.837 Hz								*/
/*		Ts=1000us : Max.Frequency = 2^32 / 1000 / 10 =  429496.729 Hz								*/
/*		Min( kf ) = ((hz*ts) / 1591549) * 2^24 = 10.5414 * (hz*ts) = 10.5414						*/
/*																									*/
/*--------------------------------------------------------------------------------------------------*/
//	else if( (insel & 0xF0) == 0x00 )		
//	{
//		MULU32U32( ts, tx, xx );				/* xx[] = hz*ts										*/
//		if( xx[1] > 0x00 ){ return( 0 );}		/* if( (hz*ts) >= 2^32 ){ kf = 0;}					*/
//
//		if( xx[0] >= 0x10000000 ){ ax = (xx[0]>>2); bx = ax +  397887;}
//		else                     { ax = (xx[0]<<2); bx = ax + 6366198;}
//	}
/*--------------------------------------------------------------------------------------------------*/
/*		Gain calculation process: Frequency [0.1Hz] && Calculation cycle [ns]						*/
/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*					(hz*ts)*(2^24)																	*/
/*		kf = ---------------------------  ,  (10^10/(2*pai)) = 1591549431 = 0x5EDD1DF7				*/
/*			  (hz*ts) + (10^10/(2*pai))																*/
/*																									*/
/*		Ts=125us : Max.Frequency = 2^40 / 125000 / 10 = 879609.302 Hz								*/
/*		Min( kf ) = ((hz*ts) / 1591549431) * 2^24 = 0.0105414 * (hz*ts) --> Min( hz*ts ) >= 95		*/
/*																									*/
/*--------------------------------------------------------------------------------------------------*/
//	else if( ts >= 1000 )				
//	{
//		MULU32U32( ts, tx, xx );				/* xx[] = hz*ts										*/
//		if( xx[1] > 0xFF ){ return( 0 );}		/* if( (hz*ts) >= 2^40 ){ kf = 0;}					*/
//		bx = (1591549431 >> 2);					/* bx = (0x5EDD1DF7>>2) = 0x17B7477D				*/
//		/*------------------------------------------------------------------------------------------*/
//		if(      xx[1] > 0x0F ){ ax = (xx[0]>>10) + (xx[1]<<22); bx = ax + (bx>>8);}
//		else if( xx[1] > 0x00 ){ ax = (xx[0]>> 6) + (xx[1]<<26); bx = ax + (bx>>4);}
//		else                   { ax = (xx[0]>> 2)              ; bx = ax + (bx>>0);}
//	}
/*--------------------------------------------------------------------------------------------------*/
/*		Input error: Calculation cycle [ns] <1000[ns]												*/
/*--------------------------------------------------------------------------------------------------*/
	else
	{
		return( 0 );
	}

	kx = XasmDIVUB( ax, bx, 31 );				/* kx = (ax/bx) * 2^24								*/
	return( kx );
}
/****************************************************************************************************
 * DESCRIPTION:
 *			
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibKsgain2Long( INT32 Input )
{
	INT32	kx;
	INT32	sx;

	kx = (((INT32)Input << 8) >> 8);
	sx = ((INT32)Input >> 24);

	return	kx >> sx;
}


/****************************************************************************************************
 * DESCRIPTION:
 *           Gain multiplication (Limit standard: 2 ^ 24): rv = (kx * u) >> sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibMulgain( INT32 u, INT32 k )
{
	INT32	x;
	INT32	xx[2];
	INT32	kx = (k<<8);
	INT32	sx = (((UINT32)k)>>24);

	MUL3232( u, kx, xx );
	x = (xx[1] >> sx);

	if( x > 0 )
	{
		return( 0x01000000 );
	}
	else if( x < -1 )
	{
		return( -0x01000000 );
	}
	else
	{
		return( (xx[1]<<(24-sx)) + (((((UINT32)xx[0]>>sx)>>7)+1)>>1) );
	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *           Gain multiplication(Limit:Signed29bit) : rv = (kx*u)>>sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibMulgain29( INT32 u, INT32 k )
{
	INT32	x;
	INT32	xx[2];
	INT32	kx = (k<<8);
	INT32	sx = (((UINT32)k)>>24);


	MUL3232( u, kx, xx );
	x = ((xx[1]>>sx)>>4);

	if( x > 0 )
	{
		return(  0x0FFFFFFF );
	}
	else if( x < -1 )
	{
		return( -0x10000000 );
	}
	else
	{
		x = (xx[1]<<(24-sx)) + (((((UINT32)xx[0]>>sx)>>7)+1)>>1);
		if( x > 0x0FFFFFFF ){ x = 0x0FFFFFFF;}
		return( x );
	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *           Gain multiplication(Limit:Signed30bit) : rv = (kx*u)>>sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibMulgain30( INT32 u, INT32 k )
{
	INT32	x;
	INT32	xx[2];
	INT32	kx = (k<<8);
	INT32	sx = (((UINT32)k)>>24);

	MUL3232( u, kx, xx );
	x = ((xx[1]>>sx)>>5);

	if( x > 0 )
	{
		return(  0x1FFFFFFF );
	}
	else if( x < -1 )
	{
		return( -0x20000000 );
	}
	else
	{
		x = (xx[1]<<(24-sx)) + (((((UINT32)xx[0]>>sx)>>7)+1)>>1);
		if( x > 0x1FFFFFFF ){ x = 0x1FFFFFFF;}
		return( x );
	}

}


/****************************************************************************************************
 * DESCRIPTION:
 *           Gain multiplication(Limit:Signed32bit) : rv = (kx*u)>>sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibMulgain32( INT32 u, INT32 k )
{
	INT32	x;
	INT32	xx[2];
	INT32	kx = (k<<8);
	INT32	sx = (((UINT32)k)>>24);

	MUL3232( u, kx, xx );
	x = ((xx[1]>>sx)>>7);

	if( x > 0 )
	{
		return(  0x7FFFFFFF );
	}
	else if( x < -1 )
	{
		return(  0x80000000 );
	}
	else
	{
		x = (xx[1]<<(24-sx)) + (((((UINT32)xx[0]>>sx)>>7)+1)>>1);
		if( (xx[1]>=0)&&(x<0) ) return( 0x7fffffff );
		return( x );
	}

}


/****************************************************************************************************
 * DESCRIPTION:
 *           Gain multiplication (without limit): rv = (kx*u)>>sx;
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibMulgainNolim( INT32 u, INT32 k )
{
	INT32	xx[2];
	INT32	kx = (k<<8);
	INT32	sx = (((UINT32)k)>>24);

	MUL3232( u, kx, xx );
	return( (xx[1]<<(24-sx)) + (((((UINT32)xx[0]>>sx)>>7)+1)>>1) );

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 FlibLimitul( REAL32 u, REAL32 ulim, REAL32 llim )
{
	if( u > ulim )
	{
		return( ulim );
	}
	if( u < llim )
	{
		return( llim );
	}
	return( u );
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibLimitul( INT32 u, INT32 ulim, INT32 llim )
{
	if( u > ulim )
	{
		return( ulim );
	}
	if( u < llim )
	{
		return( llim );
	}
	return( u );
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibLimitS32( INT32 u, INT32 lim )
{
	return (u>lim)?lim:u;
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT32 MlibLimitu32( UINT32 u, UINT32 lim )
{
	return (u>lim)?lim:u;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibMulhigh32( INT32 a, INT32 b )
{
	INT32 xx[2];
	MUL3232(a,b,xx);
	return( xx[1] + ((UINT32)xx[0]>>31) );
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibMulhighu32( INT32 a, INT32 b )
{
	UINT32 xx[2];
	MULU32U32(a,b,xx);
	return( xx[1] + ((UINT32)xx[0]>>31) );
}

/****************************************************************************************************
 * DESCRIPTION:
 *              rv=iu; iu+=kx*u
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 FlibIntegral( REAL32 u, REAL32 k, REAL32 *iu )
{
	REAL32 ax;
	
	ax = (u * k);
	*iu = *iu + ax;
	
	if( *iu >= (REAL32)INTGRAL_NORMAXVAL )
	{
		*iu =  (REAL32)INTGRAL_NORMAXVAL;
	}
	else if( *iu < - (REAL32)INTGRAL_NORMAXVAL )
	{
		*iu = - (REAL32)INTGRAL_NORMAXVAL;
	}

	return (*iu);
}


/****************************************************************************************************
 * DESCRIPTION:
 *              iu=iu+(kx*u); rv=((iu[1]+1)>>1);
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibIntegral( INT32 u, INT32 k, INT32 iu[2] )
{
	INT32	x;
	INT32	xx[2];
	INT32	carry;
	INT32	kx = (k<<8);
	INT32	sx = (((UINT32)k)>>24);

	MUL3232( u, kx, xx );
	x = (xx[1] >> sx);

	if( x > 0 )
	{
		iu[1] = iu[1] + (INT32)0x02000000;
	}
	else if( x < -1 )
	{
		iu[1] = iu[1] - (INT32)0x02000000;
	}
	else
	{
		carry = ( (UINT32)(iu[0]+(xx[0]<<(25-sx))) < (UINT32)iu[0] );
		iu[0] = iu[0] + (xx[0]<<(25-sx));
		iu[1] = iu[1] + (xx[1]<<(25-sx)) + (((UINT32)xx[0]>>sx)>>7) + carry;
	}

	if( iu[1] >= (INT32)0x02000000 )
	{
		iu[0] = 0;
		iu[1] = 0x02000000;
	}
	else if( iu[1] < (INT32)-0x02000000 )
	{
		iu[0] =  0;
		iu[1] = -0x02000000;
	}

	return( ((iu[1]+1)>>1) );

}



/****************************************************************************************************
 * DESCRIPTION:
 *              rv = x + ((kx*(u-x))>>24);	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibLpfilter1( INT32 u, INT32 k, INT32 x )
{
	INT32	wk1;
	INT32	xx[2];

    /* Gain check */
	if( k == 0 )
	{
		return( u );
	}

	MUL3232( (u-x), k, xx );

	wk1 = (xx[1]<<8) + (((((UINT32)xx[0])>>23)+1)>>1);

	if( wk1 == 0 )
	{
		return( x + ((u-x)>0) - ((u-x)<0) );
	}
	else
	{
		return( x + wk1 );
	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *              rv = x + ((kx*(u-x))>>24);	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibLpfilter12( INT32 u, INT32 k, INT32 x ,INT32* rem )
{
	INT32	wk1;
	INT32	xx[2];
	INT32   ret;
    
    INT32 dPos = 0;

    /* Gain check */
	if( k == 0 )
	{
		*rem = 0;
		return( u );
	}
	
	u = u + *rem;

	MUL3232(u, k, xx );

	wk1 = (xx[1]<<1) + (((((UINT32)xx[0])>>30)+1)>>1);
  
	if( wk1 == 0 && (u!=0))
	{
        dPos = ((u)>0) - ((u)<0);
	}    
	else
	{
        dPos = wk1;
	}

    ret = dPos;
    
	*rem = (u - dPos);

	return( ret );
}

/****************************************************************************************************
 * DESCRIPTION:
 *              rv = x + (kx*(u-x));	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 FlibLpfilter1( REAL32 u, REAL32 k, REAL32 x )
{
	REAL32	ax;
	REAL32	wk1;

	if( k == 0.0f )
	{
		return( u );
	}

	ax = ( u - x );
	wk1 = ( ax * k );
	x = ( x + wk1 );

	return( x );
}

/****************************************************************************************************
 * DESCRIPTION:
 *              rv=u-z; z=z+((kx*(u-z))>>24);	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibHpfilter1( INT32 u, INT32 k, INT32 *z )
{
	INT32	wk1;
	INT32	xx[2];


	if( k == 0 )
	{
		*z = 0;
		return( u );
	}

    *(INT64*)xx = ((INT64)(u-*z))*((INT64)(k));

	wk1 = (xx[1]<<8) + (((((UINT32)xx[0])>>23)+1)>>1);

	if( wk1 == 0 )
	{
		*z = *z + ((u-*z)>0) - ((u-*z)<0);
	}
	else
	{
		*z = *z + wk1;
	}

	return( u - *z );

}


/****************************************************************************************************
 * DESCRIPTION:
 *              rv=u-z; z=z+(kx*(u-z));	
 *              rv=(1-k)*)(u+y1-u1)
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 FlibHpfilter1( REAL32 u, REAL32 k, REAL32 *z )
{
	REAL32	x;

	if( k == 0.0f  ||k == 1.0f  ) 
	{
		z[0] = u;
		z[1] = u;
		return( u );
	}
    
	x = (1.0f-k)*(u + z[0] - z[1]);

	z[0] = x;
	z[1] = u;
	
//	*z = FlibLpfilter1( u, k, *z );
//	u = u - *z;

	return( x );
}


/****************************************************************************************************
 * DESCRIPTION:
 *             	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL FlibRampCal (REAL32 u, REAL32 *iu, REAL32 Delta)
{
    REAL32 err;
	
    err = u - *iu;
	
    if (err > Delta)
    {
    	*iu = *iu + Delta;
        return FALSE;
    }
    else if (err < -Delta)
	{
		*iu = *iu - Delta;
    	return FALSE;
	}
    else
	{
		*iu = u;
		return TRUE;
	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *             	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL MlibRampCal (INT32 u, INT32 *iu, INT32 Delta)
{
    INT32 err;
	
    err = u - *iu;
	
    if (err > Delta)
    {
    	*iu = *iu + Delta;
        return FALSE;
    }
    else if (err < -Delta)
	{
		*iu = *iu - Delta;
    	return FALSE;
	}
    else
	{
		*iu = u;
		return TRUE;
	}

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Secondary low-pass filter (bilinear transformation)	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 FlibLowPassfilter2( REAL32 u, REAL32 k[4], REAL32 z[4] )
{
	REAL32	x[6];

	x[0] = u * k[0];
	x[1] = z[0] * k[3];
	x[2] = z[1] * k[0];
	x[3] = z[2] * k[1];
	x[4] = z[3] * k[2];
	x[5] = x[0] + x[1] + x[2] + x[3] - x[4];

	z[3] = z[2];
	z[2] = x[5];
	z[1] = z[0];
	z[0] = u;

	return( x[5] );
}

PUBLIC INT32 MlibLowPassfilter2( INT32 u, INT32 k[4], INT32 z[4] )
{
	
	INT32	x[6];

	x[0] = MlibMulgain29( u,  k[0] );
	x[1] = MlibMulgain29( z[0], k[3] );
	x[2] = MlibMulgain29( z[1], k[0] );
	x[3] = MlibMulgain29( z[2], k[1] );
	x[4] = MlibMulgain29( z[3], k[2] );


	x[5] = x[0] + x[1] + x[2] + x[3] - x[4];

	z[3] = z[2];
	z[2] = x[5];
	z[1] = z[0];
	z[0] = u;

	return( x[5] );
}


/****************************************************************************************************
 * DESCRIPTION:
 *       Notch filter parameter caculate
 *       Fs: sample frequency(Hz)(50--5000)
 *       Qx: Q factor
 *       Dx: Depth
 *       Fc: Notch filter frequency
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void FilbPcalNotchFilter2(REAL32 Fc, REAL32 Qx ,REAL32 Dx ,REAL32 a[4] , REAL32 Fs)
{
/****************************************************************************************************/
/*				 2*(Wx^2 - Fs^2)				  Fs^2 + Dx*Wx*Fs/Qx + Wx^2 						*/
/*		a[1] = ------------------------		a[0] = ---------------------------						*/
/*			  Fs^2 + Wx*Fs/Qx + Wx^2			   Fs^2 + Wx*Fs/Qx + Wx^2							*/
/*																									*/
/*																									*/
/*			  Fs^2 - Wx*Fs/Qx + wx^2			  Fs^2 - Dx*Wx*Fs/Qx + Wx^2 						*/
/*		a[3] = ------------------------		a[2] = ---------------------------						*/
/*			  Fs^2 + Wx*Fs/Qx + Wx^2			   Fs^2 + Wx*Fs/Qx + Wx^2							*/
/*																									*/
/****************************************************************************************************/
    REAL32 Wx =  Fc * PI;                         // Wx = (Fc*2*PAI)*(1/2)
    REAL32 Cx = Fs*Fs + Wx*Fs/Qx + Wx*Wx  ;       // Cx = Fs^2 + Wx*Fs/Qx + Wx^2
    
    a[0] = (Fs*Fs + Dx*Wx*Fs/Qx + Wx*Wx) / Cx;
    
    a[1] = 2.0f*(Wx*Wx - Fs*Fs) / Cx;
    
    a[2] = (Fs*Fs- Dx*Wx*Fs/Qx + Wx*Wx) / Cx;
    
    a[3] = (Fs*Fs- Wx*Fs/Qx + Wx*Wx)  / Cx;
	
}

/****************************************************************************************************
 * DESCRIPTION:
 *       Notch filter	Yk + a1*Y[k-1] + a3*Y[k-2] = a0*X[k] + a1* X[k-1] +a2*X[k-2];
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 FlibNotchFilter2(REAL32 In, REAL32 Y[3], REAL32 X[3], REAL32 a[4])
{
	if(a[0] != 0)
	{
		X[0] = In;
		Y[0] = (a[0] * X[0]) + (a[1] * X[1]) + (a[2] * X[2]) - (a[1] * Y[1] ) - (a[3] * Y[2]) ;

		Y[2] = Y[1];
		Y[1] = Y[0];

		X[2] = X[1];
		X[1] = X[0];
	}
	else
	{
		Y[0] = In;
		Y[1] = In;
		Y[2] = In;

		X[0] = In;
		X[1] = In;
		X[2] = In;
	}
	return(Y[0]);
}

/****************************************************************************************************
 * DESCRIPTION:
 *       First-order filter gain calculation
 * 																					
 * 	 1) Time constant :  kf =  (ts/(tx+ts))<<24 													
 * 																							
 * 						   (2*hz*ts)*(2^24) 												
 * 	 2) 0.1Hz  :  kf = -------------------------  ,  (10^7/pai) = 3183098.862			
 * 						(2*hz*ts) + (10^7/pai)												
 * 																						
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 FlibPcalKf1gain(
	   INT32	tx, 		// Time constant (Tx)/frequency(Fx)[xs],[0.1Hz] (X=0:Tx, X=1:Fx)
	   INT32	ts, 		// Calculation cycle[xs],[us/ns] (Y=0:us, Y=1:ns)	
	   INT32	insel	)	// Input specification (0xYX) [X:Tx/Fx selection, Y:us/ns selection]

{
	INT32	Klpf;
	REAL32	fKlpf;

	Klpf = MlibPcalKf1gain( tx, ts, insel );
	fKlpf = (REAL32)Klpf / (REAL32)( 1 << 24 );
	
	return( fKlpf );

}

/****************************************************************************************************
 * DESCRIPTION:
 *             
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MlibResetByteMemory( void *mempx, INT32 xbyte )
{
	INT32 i;

	for( i=0; i<xbyte; i++ )
	{
		((UINT8 *)mempx)[i] = 0;
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *             
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MlibResetLongMemory( void *mempx, INT32 xlwd )
{
	INT32 i;

	for( i=0; i<xlwd; i++ )
	{
		((INT32 *)mempx)[i] = 0;
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *             	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MlibCopyByteMemory(void *dstMem, const void *srcMem, INT32 nbyte )
{
	CHAR *dst, *src;

	dst = (CHAR*)dstMem;
	src = (CHAR*)srcMem;
	while( nbyte > 0 )
	{
		*dst = *src;
		dst++;
		src++;
		nbyte--;
	}
}


/****************************************************************************************************
 * DESCRIPTION:
 *             	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MlibCopyLongMemory( void *dstMem, void *srcMem, INT32 nbyte )
{
	UINT32 *dst, *src;

	dst = (UINT32*)dstMem;
	src = (UINT32*)srcMem;
	while( nbyte > 0 )
	{
		*dst = *src;
		dst++;
		src++;
		nbyte--;
	}
}


/****************************************************************************************************
 * DESCRIPTION:
 *             	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT16 MlibSqrtu32( UINT32 a )
{
	UINT32	i;
	UINT32	x,y;

	for( x=y=i=0; i<16; i++ )
	{
		x = ((x<<2)|(a>>30));
		a = (a<<2);
		y = (y<<1);
		if( x > y )
		{
			x = x - y - 1;
			y = y + 2;
		}
	}
	
	if( (x<<1) > y )
	{
		y = y + 2;
	}
	y = y>>1;
	y = (y > 0xFFFF)? 0xFFFF : y;

	return( (UINT16)y );

}


/****************************************************************************************************
 * DESCRIPTION:
 *             	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT32 MlibSqrtu64( UINT32 a0, UINT32 a1 )
{
	UINT32	i;
	UINT32	x,y,z;
	UINT32	xx,yy;

	for( x=y=i=0; i<16; i++ )
	{
		x  = ((x<<2)|(a1>>30));
		a1 = (a1<<2);
		y  = (y<<1);
		if( x > y )
		{
			x = x - y - 1;
			y = y + 2;
		}
	}
	
	for( i=0; i<14; i++ )
	{
		x  = ((x<<2)|(a0>>30));
		a0 = (a0<<2);
		y  = (y<<1);
		if( x > y )
		{
			x = x - y - 1;
			y = y + 2;
		}
	}

	xx = (x>>30);
	x  = ((x<<2)|(a0>>30));
	a0 = (a0<<2);
	yy = (y>>31);
	y  = (y<<1);
	if( xx > yy )
	{
		xx = xx - yy - (x<=y);
		x  = x - y - 1;
		y  = y + 2;
	}
	else if( (xx==yy)&&(x>y) )
	{
		x  = x - y - 1;
		y  = y + 2;
	}

	xx = (xx<<2)|(x>>30);
	x  = ((x<<2)|(a0>>30));
	yy = (yy<<1)|(y>>31);
	y  = (y<<1);
	if( xx > yy )
	{
		xx = xx - yy - (x<=y);
		x  = x - y - 1;
		y  = y + 2;
	}
	else if( (xx==yy)&&(x>y) )
	{
		xx = xx - yy;
		x  = x - y - 1;
		y  = y + 2;
	}

	z  = (yy<<31)+(y>>1);
	xx = (xx<<1)|(x>>31); x = (x<<1);
	if( (z!=0xFFFFFFFF)&&((xx>yy)||((xx==yy)&&(x>y))) )
	{
		z = z + 1;
	}

	return( z );

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Search bit ON (Search from LSB/MSB, Return value: BitNo / NG (-1))	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibSrhbiton( UINT32 data, UINT32 dir )
{
	INT32	BitNo = 0;

	if( data == 0 )
	{
		return( -1 );
	}

	/* Search from LSB */
	else if( dir == SRH_FROM_LSB )
	{
		if( (data & 0xFFFF) == 0 ){ BitNo += 16; data = data>>16;}
		if( (data & 0x00FF) == 0 ){ BitNo +=  8; data = data>>8 ;}
		if( (data & 0x000F) == 0 ){ BitNo +=  4; data = data>>4 ;}
		if( (data & 0x0003) == 0 ){ BitNo +=  2; data = data>>2 ;}
		if( (data & 0x0001) == 0 ){ BitNo +=  1;}
	}
	/* Search from MSB */
	else
	{
		if( (data & 0xFFFF0000) != 0 ){ BitNo += 16; data = data>>16;}
		if( (data & 0x0000FF00) != 0 ){ BitNo +=  8; data = data>>8 ;}
		if( (data & 0x000000F0) != 0 ){ BitNo +=  4; data = data>>4 ;}
		if( (data & 0x0000000C) != 0 ){ BitNo +=  2; data = data>>2 ;}
		if( (data & 0x00000002) != 0 ){ BitNo +=  1;}
	}

	return( BitNo );

}


/****************************************************************************************************
 * DESCRIPTION:
 *       CRC16 setting (for MEMOBUS), Return value: None	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MlibSetCRC16MB( UINT8 *MsgBuf, INT32 MsgLen )
{
	INT32	i;
	UINT32	a = 0xFFFF;
	UINT32	b = 0xA001;

	for( i=0; i<(MsgLen-2); i++ )
	{
		a = a ^ (UINT32)MsgBuf[i];
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);	
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);	
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);	
	}

	MsgBuf[MsgLen-2] = (UINT8)(a>>0);
	MsgBuf[MsgLen-1] = (UINT8)(a>>8);

	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *       CRC16 check (for MEMOBUS), Return value: TRUE (normal) / FALSE (abnormal)	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL MlibChkCRC16MB( UINT8 *MsgBuf, INT32 MsgLen )
{
	INT32	i;
	UINT32	a = 0xFFFF;
	UINT32	b = 0xA001;


	for( i=0; i<(MsgLen-2); i++ )
	{
		a = a ^ (UINT32)MsgBuf[i];
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		/* 1	*/
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		/* 2	*/
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		/* 3	*/
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		/* 4	*/
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		/* 5	*/
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		/* 6	*/
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		/* 7	*/
		a = (a & 0x01)? ((a>>1)^b) : (a>>1);		/* 8	*/
	}

	if( a == (UINT32)(MsgBuf[MsgLen-2]+(MsgBuf[MsgLen-1]<<8)) ){ return(TRUE);}else{ return(FALSE);}
}


/****************************************************************************************************
 * DESCRIPTION:
 *       Position deviation calculation X (FB unit) per64 [] = per64 [] + pcmd-pfbk; rv = per64 [0];	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPerrcalx( INT32 pcmdx, INT32 pfbkx, INT32 per64[2] )
{
	INT32	perrx;				
	INT32	carry;				
	INT32	perrchk;		

	perrx = pcmdx - pfbkx;
	carry = (((UINT32)(per64[0]+perrx)) < ((UINT32)(per64[0])));

	per64[0] = per64[0] + (perrx);
	per64[1] = per64[1] + (perrx>>31) + carry;

	perrchk = (per64[1]<<2) + (((UINT32)per64[0])>>30);
	if( perrchk >  0 ){ return(  0x3FFFFFFF );}
	if( perrchk < -1 ){ return( -0x40000000 );}

	return( per64[0] );

}

PUBLIC INT32 FlibPerrcalx( INT32 pcmdx, INT32 pfbkx, INT32 per64[2] )	
{
	INT32	perrx;			
	INT32	carry;		
	INT32	perrchk;			
	perrx = pcmdx - pfbkx;
	carry = (((UINT32)(per64[0]+perrx)) < ((UINT32)(per64[0])));
	per64[0] = per64[0] + (perrx);
	per64[1] = per64[1] + (perrx>>31) + carry;
	perrchk = (per64[1]<<2) + (((UINT32)per64[0])>>30);
	if( perrchk >  0 ){ return(  0x3FFFFFFF );}
	if( perrchk < -1 ){ return( -0x40000000 );}
	return( per64[0] );
}


/****************************************************************************************************
 * DESCRIPTION:
 *       Position FB calculation with remainder (for MFC): rv = (kx * u + pfbrem) >> sx;	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPfbkxremNolim( INT32 u, INT32 k, INT32 *pfbrem )
{
	INT32	x;
	INT32	xx[2];
	INT32	kx = (k<<8);
	INT32	sx = ((UINT32)k)>>24;
	INT32	remx = *pfbrem;

	MUL3232( u, kx, xx );


	if( sx <= 24 )
	{
		x = (xx[1]<<(24-sx)) + ((((UINT32)xx[0])>>sx)>>8);
		remx = remx + (((UINT32)xx[0]<<(24-sx))>>8);
	}
	else
	{
		x = xx[1]>>(sx-24);
		remx = remx + (((UINT32)xx[1]<<(56-sx))>>8) + (((((UINT32)xx[0]>>(sx-24))>>7)+1)>>1);
	}

	if( remx >= (INT32)0x00800000 )
	{
		x = x + 1;
		remx = remx - (INT32)0x00800000;
		remx = remx - (INT32)0x00800000;
	}

	*pfbrem = remx;
	return( x );

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Gain multiplication with bias remainder	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibMulkxBiasrem( INT32 u, INT32 k, INT32 *rem, INT32 bias )
{
	INT32	x;
	INT32	xx[2];
	INT32	kx = (k<<8);
	INT32	sx = (((UINT32)k)>>24);
	INT32	kb = (bias<<8);
	INT32	sb = (((UINT32)bias)>>24);

	MUL3232( u, kx, xx );
	*rem = *rem + ((UINT32)(xx[0]<<(24-sx))>>8);
	x = (xx[1]<<(24-sx)) + (((UINT32)xx[0]>>sx)>>8);
	if( *rem >= 0x00800000 )
	{
		x = x + 1;
		*rem = *rem - 0x01000000;
	}

	if( u >= 0 )
	{
		x = x + (((UINT32)kb>>sb)>>8);
		*rem = *rem + ((UINT32)(kb<<(24-sb))>>8);
		if( *rem >= 0x00800000 )
		{
			x = x + 1;
			*rem = *rem - 0x01000000;
		}
	}
	else
	{
		x = x - (((UINT32)kb>>sb)>>8);
		*rem = *rem - ((UINT32)(kb<<(24-sb))>>8);
		if( *rem < -0x00800000 )
		{
			x = x - 1;
			*rem = *rem + 0x01000000;
		}
	}

	return( x );

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Second-order filter gain calculation (0.1Hz input)	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MlibPcalKf2gain(
		INT32	hz,			/* Filter frequency			[0.1Hz]										*/
		INT32	dx,			/* Filter damping coefficient(&)[0.001]									*/
		INT32	ts,			/* Scan time			[us/ns] (Y=0:us, Y=1:ns)						*/
		INT32	kf[2],		/* Gain calculation result  [--]										*/
		INT32	insel	)	/* Input specification(0xYX)[X:not used, Y=us/ns sel.]					*/
{
	INT32	sx;					
	INT32	ax,as;				
	INT32	bx,bs;				
	INT32	kx[2];				
	INT32	kunit = ((insel & 0xF0)==0)? 1 : 1000;


	if( (hz == 0) || (ts == 0) || ((insel & 0xF0)&&(ts < 1000)) )
	{
		kf[0] = kf[1] = 0;
		return;
	}

	if( dx <    50 ){ dx =    50;}
	if( dx > 10000 ){ dx = 10000;}
/*--------------------------------------------------------------------------------------------------*/
/*		Calculation of first-order filter gain : ts:[us], hz:[0.1Hz]								*/
/*--------------------------------------------------------------------------------------------------*/
/*							ts	                        ts*hz										*/
/*		formula : kf[0] = --------- * 2^24 = ---------------------------- * 2^24					*/
/*				 		  ts + tx            ts*hz + (10^10/(4*pai*dx))								*/
/*--------------------------------------------------------------------------------------------------*/
	as = bs = 0;
	ax = MlibPcalKxmulx( ts,    hz,        dx, &as ) >> 1;
	bx = MlibPcalKxmulx( kunit, 795774715, 1,  &bs ) >> 1;
	if( as > bs ){ ax = ax >> (as - bs);}		/* Min(ax) = 1us * 1[0.1Hz] * 50 = 50			*/
	if( bs > as ){ bx = bx >> (bs - as);}		/* Max(bx) = 795774715 + 50 = 795774765			*/
	kx[0] = XasmDIVUB( ax, (ax+bx), 24 );		/* kx[0]=(ax/(ax+bx))*2^24 : Min(kx[0])=1.054	*/
/*--------------------------------------------------------------------------------------------------*/
/*		Compute integral gain : ts:[us], hz:[0.1Hz], 												*/
/*--------------------------------------------------------------------------------------------------*/
/*				 			   2*pai*hz*ts		  2*pai*hz*ts		  hz*ts							*/
/*		formula : kf[1] = --------------------- = -------------- = ----------------					*/
/*						  2*(dx/1000)*(10^7)	   2*dx*10^4	   dx*3183.098862					*/
/*--------------------------------------------------------------------------------------------------*/
	as = bs = 0;
	ax = MlibPcalKxmulx( ts,    hz, 100000,    &as );
	bx = MlibPcalKxmulx( kunit, dx, 318309886, &bs );
	sx = as - bs;								/* kx[1] = (ax/bx)*2^sx							*/
	kx[1] = XasmDIVUXB( ax, bx, &sx, 23 );		/* Min(kx[1]) = 1/(10000*3183.1)*2^24 = 0.527 	*/
	/*----------------------------------------------------------------------------------------------*/
	if( (sx-24) > 31 ){ kx[1] = 0; sx = 24;}
	else if( sx > 24 ){ kx[1] = (kx[1]>>(sx-24)); sx = 24;}
	if( kx[1] == 0   ){ kx[1] = 1;}				/* Min(kx)= 0.527 = 0 --> 1 (Currently not required:ts>1us)*/
	kx[1] = kx[1] + (sx<<24);					/* kx[1] = {kx,sx}								*/

	kf[0] = kx[0];
	kf[1] = kx[1];
	return;

}


PUBLIC void FlibPcalKf2gain(
		float	hz, 		/* Filter frequency			[Hz]									   */
		float	dx, 		/* Filter damping coefficient(&) 	[-] 							   */
		float	ts, 		/* Scan time			[sec]									       */
		float	kf[2])		/* Gain calculation result			[--]						       */
{
	float	ax; 			
	float	bx; 			
	if( (hz == (float)0.0) || (ts == (float)0.0) )
	{
		kf[0] = kf[1] = 0.0;
		return;
	}
	if( dx < (float)0.05   ){ dx = 0.05; }
	if( dx > (float)10.000 ){ dx = 10.000;}
		ax = ts * hz * dx * 4 * PI;				/*													*/
		bx = ax + 1.0;							/*													*/
		kf[0] = ax / bx;						/* kf[0] = (ax/bx)									*/
		ax = (ts * hz * PI);					/*													*/
		bx = dx;								/*													*/
		kf[1] = ax / bx;						/* kf[1] = (ax/bx)									*/
		return;
}

/****************************************************************************************************
 * DESCRIPTION:
 *       Position command moving average filter	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcmdMafil( INT32 pcmdin, INT32 pmafnum, PMAFV *pmafvar, INT32 *pmafbuf )
{
	INT32	subx;
	INT32	outx;

	if( pmafnum <= 1 )
	{
		pmafvar->zcntx = (pcmdin != 0)? 1 : 0;
		return( pcmdin );
	}

	subx = pmafvar->ksub * pmafbuf[pmafvar->idx];
	pmafbuf[pmafvar->idx] = pcmdin;
	if( ++pmafvar->idx >= pmafnum )
	{
		pmafvar->idx = 0;
		pmafvar->ksub = 1;
	}

	pmafvar->sumx = pmafvar->sumx - subx + pcmdin;
	outx = (pmafvar->sumx + pmafvar->remx)/pmafnum;
	pmafvar->remx = (pmafvar->sumx + pmafvar->remx) - (pmafnum * outx);

	if( pcmdin != 0 )
	{
		pmafvar->zcntx = (UINT16)pmafnum;
	}
	else if( pmafvar->zcntx > 0 )
	{
		--pmafvar->zcntx;
	}

	return( outx );

}

/****************************************************************************************************
 * DESCRIPTION:
 *       Position command moving average filter 2	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcmdMafilSec( INT32 pcmdin, INT32 pmafnum, HIGHFV *pmafvarSec, INT32 *pmafbufSec )  
{
	INT32	subx;
	INT32	outx;

	if( pmafnum <= 1 )
	{
		pmafvarSec->zcntxSec = (pcmdin != 0)? 1 : 0;
		return( pcmdin );
	}

	subx = pmafvarSec->ksubSec * pmafbufSec[pmafvarSec->idxSec];
	pmafbufSec[pmafvarSec->idxSec] = pcmdin;
	if( ++pmafvarSec->idxSec >= pmafnum )
	{
		pmafvarSec->idxSec = 0;
		pmafvarSec->ksubSec = 1;
	}

	pmafvarSec->sumxSec = pmafvarSec->sumxSec - subx + pcmdin;
	outx = (pmafvarSec->sumxSec + pmafvarSec->remxSec)/pmafnum;
	pmafvarSec->remxSec = (pmafvarSec->sumxSec + pmafvarSec->remxSec) - (pmafnum * outx);

	if( pcmdin != 0 )
	{
		pmafvarSec->zcntxSec = (UINT16)pmafnum;
	}
	else if( pmafvarSec->zcntxSec > 0 )
	{
		--pmafvarSec->zcntxSec;
	}
	
	return( outx );
	
}


/****************************************************************************************************
 * DESCRIPTION:
 *       Position command interpolation Moving average filter	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcmdImafil( INT32 pcmdin, INT32 shift, INT32 index, PIMFV *pimfvar )
{
	INT32	x;
	INT32	outx;

	if( shift == 0 )
	{
		pimfvar->zcntx = (pcmdin != 0)? 1 : 0;
		return( pcmdin );
	}

	if( index == 0 )
	{
		pimfvar->oldpcmd = pimfvar->newpcmd;
		pimfvar->newpcmd = pcmdin;
		if( pcmdin != 0 )
		{
			pimfvar->zcntx = (2 << shift);
		}
	}

	pimfvar->sumx = pimfvar->sumx + (pimfvar->newpcmd - pimfvar->oldpcmd);
	x = pimfvar->sumx + pimfvar->remx;
	outx = (x >> (2*shift));
	pimfvar->remx = x - (outx << (2*shift));
	if( pimfvar->zcntx > 0 )
	{
		--pimfvar->zcntx;
	}

	return( outx );

}

/****************************************************************************************************
 * DESCRIPTION:
 *       Position command interpolation Moving average filter 2	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcmdImafilSec( INT32 pcmdin, INT32 shift, INT32 indexSec, HIMFV *pimfvarSec ) 
{
	INT32	x;
	INT32	outx;

	if( shift == 0 )
	{
		pimfvarSec->zcntxSec = (pcmdin != 0)? 1 : 0;
		return( pcmdin );
	}

	if( indexSec == 0 )
	{
		pimfvarSec->oldpcmdSec = pimfvarSec->newpcmdSec;
		pimfvarSec->newpcmdSec = pcmdin;
		if( pcmdin != 0 )
		{
			pimfvarSec->zcntxSec = (2 << shift);
		}
	}

	pimfvarSec->sumxSec = pimfvarSec->sumxSec + (pimfvarSec->newpcmdSec - pimfvarSec->oldpcmdSec);
	x = pimfvarSec->sumxSec + pimfvarSec->remxSec;
	outx = (x >> (2*shift));
	pimfvarSec->remxSec = x - (outx << (2*shift));
	if( pimfvarSec->zcntxSec > 0 )
	{
		--pimfvarSec->zcntxSec;
	}

	return( outx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Position command index acceleration / deceleration filter	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcmdExpfil( INT32 pcmdin, INT32 kexp, INT32 pbias, PEXFV *pexfvar )
{
	INT32	outx;
	INT32	xx[2];
	INT32	kx = (kexp<<8);
	INT32	sx = (((UINT32)kexp)>>24);


	if( kexp == 0 )
	{
		pexfvar->sumx = 0;
		return( pcmdin );
	}

	pexfvar->sumx = pexfvar->sumx + pcmdin;


	MUL3232( pexfvar->sumx, kx, xx );
	outx = (xx[1]<<(24-sx)) + (((((UINT32)xx[0]>>sx)>>7)+1)>>1);

	if( pexfvar->sumx > pbias )
	{
		outx = outx + pbias;
		if( outx > pexfvar->sumx ){ outx = pexfvar->sumx;}
	}
	else if( pexfvar->sumx < -pbias )
	{
		outx = outx - pbias;
		if( outx < pexfvar->sumx ){ outx = pexfvar->sumx;}
	}
	else
	{
		outx = pexfvar->sumx;
	}

	if( (outx==0)&&(pexfvar->sumx!=0) )
	{
		outx = (pexfvar->sumx > 0)? 1 : -1;
	}

	pexfvar->sumx = pexfvar->sumx - outx;
	return( outx );

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Position command secondary low-pass filter	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcmdLpfil2( INT32 pcmdin, INT32 kf[2], INT32 z[2] )
{
	INT32	x;
	INT32	xx[2];
	INT32	kx,sx;

	if( kf[0] == 0 )
	{
		z[0] = z[1] = 0;
		return( pcmdin );
	}

	z[0] = z[0] + pcmdin;


	MUL3232( (z[0]-z[1]), kf[0], xx );
	x = (xx[1]<<8) + (((((UINT32)xx[0])>>23)+1)>>1);
	if( (x==0) && (z[1]!=z[0]) )
	{
		x = (z[0]>z[1]) - (z[0]<z[1]);
	}
	z[1] = z[1] + x;


	kx = (kf[1]<<8);
	sx = (((UINT32)kf[1])>>24);
	MUL3232( z[1], kx, xx );
	x = (xx[1]<<(24-sx)) + (((((UINT32)xx[0]>>sx)>>7)+1)>>1);
	if( (x==0) && (z[0]!=0) )
	{
		x = (z[0] > 0)? 1 : -1;
	}
	z[0] = z[0] - x;

	return( x );

}

PUBLIC float FlibPcmdLpfil2( float pcmdin, float kf[2], float z[2] )
{
	INT32	x;
	INT32	zz[2];
	zz[0 ]= z[0];
	zz[1 ]= z[1];
	if( kf[0] == (float)0.0 )
	{
		z[0] = z[1] = 0.0;
		return( pcmdin );
	}
	zz[0] = zz[0] + pcmdin;
	x = ( (zz[0]-zz[1]) * kf[0] );
	if( (x== 0 ) && (zz[1]!=zz[0]) )
	{
		x = (zz[0]>zz[1]) - (zz[0]<zz[1]);
	}
	zz[1] = zz[1] + x;
	x = ( zz[1] * kf[1] );
//		if( (x==(float)0.0) && (z[0]!=0) )
	if( (x==0) && (zz[0]!=0) )
	{
//			x = (z[0] > 0)? 1 : -1;
		x = (zz[0] > 0)? 1 : -1;
	}
//		z[0] = z[0] - (INT32)x;
	zz[0] = zz[0] - x;

	z[0 ]= zz[0];
	z[1 ]= zz[1];

//		return( (INT32)x );
	return( x );
}

/****************************************************************************************************
 * DESCRIPTION:
 *       Pulse command electronic gear calculation rv=(B/A)*pcmda	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcmdEgear( INT32 pcmda, EGEAR *egear, INT32 *pcmdrem )
{
	INT32		pcmdb;			
	INT32	wk1[2];				
	INT32	wk2[2];			

/*--------------------------------------------------------------------------------------------------*/
/*	Position command calculation : pcmdb = k1*pcmda + ((k2*pcmda)>>31);								*/
/*--------------------------------------------------------------------------------------------------*/
	MUL3232( pcmda, egear->k1, wk1 );
	MUL3232( pcmda, egear->k2, wk2 );
	pcmdb = wk1[0] + (wk2[1]<<1) + ((UINT32)wk2[0]>>31);

/*--------------------------------------------------------------------------------------------------*/
/*	Position command remainder calculation : pcmdrem = pcmdrem + ( B*pcmda - A*pcmdb )				*/
/*--------------------------------------------------------------------------------------------------*/
	MUL3232( pcmda, egear->b, wk1 );
	MUL3232( pcmdb, egear->a, wk2 );
	*pcmdrem = *pcmdrem + ((UINT32)wk1[0] - (UINT32)wk2[0]);

/*--------------------------------------------------------------------------------------------------*/
/*	Position command surplus check : (-A/2) <= pcmdrem < (A/2)										*/
/*--------------------------------------------------------------------------------------------------*/
	if( *pcmdrem <  0                 ){ --pcmdb; *pcmdrem += egear->a;}
	if( *pcmdrem >= egear->a          ){ ++pcmdb; *pcmdrem -= egear->a;}
	if( *pcmdrem >= ((egear->a+1)>>1) ){ ++pcmdb; *pcmdrem -= egear->a;}

	return( pcmdb );

}


#define LPX_ABS( u )  (((u)>=0) ? (u) : -(u))

INT32 XasmMulhigh32down( INT32 a, INT32 b )
{
	INT32	xx[2];
	MUL3232( a, b, xx );
	return( xx[1] );
}

/****************************************************************************************************
 * DESCRIPTION:
 *       Position command index acceleration / deceleration filter	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcmdMaker(
		INT32	pcmdin0,			/* Position command input (lower 32 bits)[pulse]				*/
		INT32	pcmdin1,			/* Position command input (upper 32bit)[pulse]					*/
		INT32	pcmdspd,			/* Position command speed (max/feed)[2^24/OvrSpd]				*/
		PCMKPV *pcmdmkr,			/* Position command generator calculation variables(P&V)[-] 	*/
		UINT32	pcmkmode	)		/* Position command creation mode		[-] 					*/

{
	INT32	carry;							/* 64-bit operation Carry calculation					*/
	INT32	px64[2];						/* Pulse amount until deceleration stop					*/
	INT32	per64[2];						/* Pulse deviation (with 64-bit sign)					*/
	INT32	pex64[2];						/* Pulse deviation during deceleration stop(per64[]-px64[]) */
	INT32	pex,x1,x2;						/* For pulse amount calculation during deceleration stop */
	PCMKP	*pcmkprm = &pcmdmkr->P;			/* Position command generator parameters				*/
	PCMKV	*pcmkvar = &pcmdmkr->V;			/* Position command generator calculation variables		*/


/*--------------------------------------------------------------------------------------------------*/
/*		Position command creation mode change check													*/
/*--------------------------------------------------------------------------------------------------*/
	pcmkvar->modechgf = ((pcmkmode & 0xFF) != pcmkvar->pcmkmode)? TRUE : FALSE;
    
	pcmkvar->pcmkmode = (UCHAR)pcmkmode;


	switch( pcmkmode )
	{
/****************************************************************************************************/
/*																									*/
/*		Positioning mode																			*/
/*																									*/
/****************************************************************************************************/
	case PCMKMODE_POS:
/*--------------------------------------------------------------------------------------------------*/
/*		Start process																				*/
/*--------------------------------------------------------------------------------------------------*/
		if( pcmkvar->modechgf )
		{
			pcmkvar->vpxrem = 0;
			pcmkvar->cmderrf = FALSE;
		}

/*--------------------------------------------------------------------------------------------------*/
/*		Position command speed (maximum)[xpulse/scan] : maxvp = osvpm * pcmdspd / 2^24				*/
/*--------------------------------------------------------------------------------------------------*/
		pcmkvar->maxvp = XasmMulhigh32down( (pcmkprm->osvpm << 4), (LPX_ABS(pcmdspd) << 4) );

/*--------------------------------------------------------------------------------------------------*/
/*		64-bit position command: Position command deviation calculation								*/
/*--------------------------------------------------------------------------------------------------*/
		if( pcmkprm->pcmd64f )
		{
			per64[0] = pcmdin0 - pcmkvar->pcmdout[0];
			carry = ((UINT32)pcmdin0 < (UINT32)pcmkvar->pcmdout[0]);
			per64[1] = pcmdin1 - pcmkvar->pcmdout[1] - carry;
		/*------------------------------------------------------------------------------------------*/
			if( (per64[1]>>(30-pcmkprm->pshlx)) > 0 )
			{
				per64[1] =  0x3FFFFFFF;
			}
			else if( (per64[1]>>(30-pcmkprm->pshlx)) < -1 )
			{
				per64[1] = -0x3FFFFFFF;
			}
			else
			{
				x1       = (((UINT32)per64[0]>>(31-pcmkprm->pshlx))>>1);
				carry    = ((UINT32)(per64[0]<<pcmkprm->pshlx) < (UINT32)pcmkvar->vpxrem);
				per64[0] = (per64[0]<<pcmkprm->pshlx) - pcmkvar->vpxrem;
				per64[1] = (per64[1]<<pcmkprm->pshlx) + x1 - (pcmkvar->vpxrem>>31) - carry;
			}
		}
/*--------------------------------------------------------------------------------------------------*/
/*		32-bit position command: Calculation of position command deviation															*/
/*--------------------------------------------------------------------------------------------------*/
		else
		{
			x1       = pcmdin0 - pcmkvar->pcmdout[0];
			carry    = ((UINT32)(x1<<pcmkprm->pshlx) < (UINT32)pcmkvar->vpxrem);
			per64[0] = (x1<<pcmkprm->pshlx) - pcmkvar->vpxrem;
			per64[1] = ((x1>>(31-pcmkprm->pshlx))>>1) - (pcmkvar->vpxrem>>31) - carry;
		}

/*--------------------------------------------------------------------------------------------------*/
/*		Check convergence conditions																			*/
/*--------------------------------------------------------------------------------------------------*/
		if( ( per64[1] >  0 ) || 
			( per64[1] < -1 ) ||
			( (per64[1]^per64[0]) < 0 ) )
		{
			;
		}
		else if( (-pcmkprm->vpdec <= per64[0]) && (per64[0] <= pcmkprm->vpdec) )
		{
			x1 = per64[0] - pcmkvar->vpx;
			if( (x1 >= -pcmkprm->vpdec) && (x1 <= pcmkprm->vpdec) )
			{
				pcmkvar->vpx = per64[0];
				pcmkvar->calendf = (per64[0]==0)? TRUE : FALSE;
				break;
			}
		}
		
/*--------------------------------------------------------------------------------------------------*/
/*		Speed check																				*/
/*--------------------------------------------------------------------------------------------------*/
		pcmkvar->calendf = FALSE;
		pcmkvar->avp = ( pcmkvar->vpx > 0 ) ? pcmkvar->vpx : -pcmkvar->vpx;
		if( pcmkvar->avp >= (pcmkvar->maxvp + pcmkprm->vpdec) )
		{
			if( pcmkvar->vpx > 0 )
			{
				pcmkvar->vpx = pcmkvar->vpx - pcmkprm->vpdec;
			}
			else
			{
				pcmkvar->vpx = pcmkvar->vpx + pcmkprm->vpdec;
			}
			break;
		}

/*--------------------------------------------------------------------------------------------------*/
/*		Calculation of position command pulse amount until deceleration stop (zero speed)												*/
/*--------------------------------------------------------------------------------------------------*/
		pcmkvar->n   = pcmkvar->avp / pcmkprm->vpdec;
		pcmkvar->rem = pcmkvar->avp - pcmkvar->n * pcmkprm->vpdec;
		if( ((pcmkvar->n+1)&0x01) == 0x00 )
		{
			MUL3232( ((pcmkvar->n + 1)>>1), (pcmkvar->avp + pcmkvar->rem), &px64[0] );
		}
		else
		{
			MUL3232( (pcmkvar->n + 1), ((pcmkvar->avp + pcmkvar->rem)>>1), &px64[0] );
		}

/*--------------------------------------------------------------------------------------------------*/
/*		Area check & calculation of position command deviation at deceleration stop					*/
/*--------------------------------------------------------------------------------------------------*/
		if( (pcmkvar->vpx >= 0) && (per64[1] >= 0) && ((per64[1]|per64[0]) != 0) )
		{
			pex64[0] = per64[0] - px64[0];
			pex64[1] = per64[1] - px64[1] - ((UINT32)per64[0] < (UINT32)px64[0]);
			x1 = pex64[0]^pex64[1];
			if( ( x1 >= 0 )&&(( pex64[1]==0 )||( pex64[1]==-1 )) )
			{
				pex = pex64[0];
			}
			else if( pex64[1] >= 0 )
			{
				pcmkvar->vpx = pcmkvar->vpx + pcmkprm->vpacc;
				if( pcmkvar->vpx > pcmkvar->maxvp ){ pcmkvar->vpx = pcmkvar->maxvp;}
				break;
			}
			else
			{
				pcmkvar->vpx = pcmkvar->vpx - pcmkprm->vpdec;
				if( pcmkvar->vpx < -pcmkvar->maxvp ){ pcmkvar->vpx = -pcmkvar->maxvp;}
				break;
			}
		}
/*--------------------------------------------------------------------------------------------------*/
		else if( (pcmkvar->vpx <= 0) && (per64[1] < 0) )
		{
			pex64[0] = per64[0] + px64[0];
			pex64[1] = per64[1] + px64[1] + ((UINT32)pex64[0] < (UINT32)per64[0]);
			x1 = pex64[0]^pex64[1];
			if( ( x1 >= 0 )&&(( pex64[1]==0 )||( pex64[1]==-1 )) )
			{
				pex = (pex64[0]==0x80000000) ? 0x7FFFFFFF : -pex64[0];
			}
			else if( pex64[1] >= 0 )
			{
				pcmkvar->vpx = pcmkvar->vpx + pcmkprm->vpdec;
				if( pcmkvar->vpx > pcmkvar->maxvp ){ pcmkvar->vpx = pcmkvar->maxvp;}
				break;
			}
			else
			{
				pcmkvar->vpx = pcmkvar->vpx - pcmkprm->vpacc;
				if( pcmkvar->vpx < -pcmkvar->maxvp ){ pcmkvar->vpx = -pcmkvar->maxvp;}
				break;
			}
		}
/*--------------------------------------------------------------------------------------------------*/
		else if( (pcmkvar->vpx < 0) && (per64[1] >= 0) )
		{
			pcmkvar->vpx = pcmkvar->vpx + pcmkprm->vpdec;
			if( pcmkvar->vpx > pcmkvar->maxvp ){ pcmkvar->vpx = pcmkvar->maxvp;}
			break;
		}
/*--------------------------------------------------------------------------------------------------*/
		else
		{
			pcmkvar->vpx = pcmkvar->vpx - pcmkprm->vpdec;
			if( pcmkvar->vpx < -pcmkvar->maxvp ){ pcmkvar->vpx = -pcmkvar->maxvp;}
			break;
		}

/*--------------------------------------------------------------------------------------------------*/
/*		Calculation during acceleration																*/
/*--------------------------------------------------------------------------------------------------*/
		if( pex > pcmkvar->n )
		{
			x1 = pcmkprm->vparx + pcmkvar->rem - pcmkprm->vpdec;
			x2 = pcmkprm->vpapx + pcmkprm->vpamx*pcmkvar->avp + pcmkvar->n*pcmkprm->vparx + ((x1>0)?x1:0);
		/*------------------------------------------------------------------------------------------*/
		/*	pex >= (vpapx+vpamx*avp+n*vparx+xxx)													*/
		/*------------------------------------------------------------------------------------------*/
			if( pex >= x2 )
			{
				pcmkvar->avp = pcmkvar->avp + pcmkprm->vpacc;
			}
		/*------------------------------------------------------------------------------------------*/
		/*	pex >= (avp+vpdec)																		*/
		/*------------------------------------------------------------------------------------------*/
			else if( pex >= (pcmkvar->avp + pcmkprm->vpdec) )
			{
				if( pex >= (8*pcmkvar->avp + 36*pcmkprm->vpdec) )
				{
					pex = pex - (8*pcmkvar->avp + 36*pcmkprm->vpdec);
					pcmkvar->avp = pcmkvar->avp + 8*pcmkprm->vpdec;
				}
				if( pex >= (4*pcmkvar->avp + 10*pcmkprm->vpdec) )
				{
					pex = pex - (4*pcmkvar->avp + 10*pcmkprm->vpdec);
					pcmkvar->avp = pcmkvar->avp + 4*pcmkprm->vpdec;
				}
				if( pex >= (2*pcmkvar->avp + 3*pcmkprm->vpdec) )
				{
					pex = pex - (2*pcmkvar->avp + 3*pcmkprm->vpdec);
					pcmkvar->avp = pcmkvar->avp + 2*pcmkprm->vpdec;
				}
				if( pex >= (pcmkvar->avp + pcmkprm->vpdec) )
				{
					pcmkvar->avp = pcmkvar->avp + pcmkprm->vpdec;
				}
			}
		/*------------------------------------------------------------------------------------------*/
		/*	pex > (n+1)*(vpdec-rem)																	*/
		/*------------------------------------------------------------------------------------------*/
			else if( pex > ((pcmkvar->n + 1)*(pcmkprm->vpdec - pcmkvar->rem)) )
			{
				pcmkvar->avp = pcmkvar->avp + pcmkprm->vpdec;
				pcmkvar->avp = pcmkvar->avp - (pcmkvar->avp - pex + pcmkvar->n + 1)/(pcmkvar->n + 2);
			}
		/*------------------------------------------------------------------------------------------*/
		/*	n < pex <= (n+1)*(vpdec-rem)															*/
		/*------------------------------------------------------------------------------------------*/
			else
			{
				pcmkvar->avp = pcmkvar->avp + pex/(pcmkvar->n + 1);
			}
		}
/*--------------------------------------------------------------------------------------------------*/
/*		Calculation during deceleration																*/
/*--------------------------------------------------------------------------------------------------*/
		else if( pex < 0 )
		{
		/*------------------------------------------------------------------------------------------*/
		/*	pex <= -avp																				*/
		/*------------------------------------------------------------------------------------------*/
			if( pex <= -pcmkvar->avp )
			{
				pcmkvar->avp = pcmkvar->avp - pcmkprm->vpdec;
			}
		/*------------------------------------------------------------------------------------------*/
		/*	-avp < pex <= -(n+1)*rem																*/
		/*------------------------------------------------------------------------------------------*/
			else if( pex <= -(pcmkvar->n + 1)*pcmkvar->rem )
			{
				pcmkvar->avp = pcmkvar->avp - pcmkprm->vpdec + (pex + pcmkvar->avp)/pcmkvar->n;
			}
		/*------------------------------------------------------------------------------------------*/
		/*	-(n+1)*rem < pex < 0																	*/
		/*------------------------------------------------------------------------------------------*/
			else
			{
				pcmkvar->avp = pcmkvar->avp - (pcmkvar->n - pex)/(pcmkvar->n + 1);
			}
		}

/*--------------------------------------------------------------------------------------------------*/
/*		Limit & Sign operation																		*/
/*--------------------------------------------------------------------------------------------------*/
		x1 = ( pcmkvar->avp > pcmkvar->maxvp )? pcmkvar->maxvp : pcmkvar->avp;
		pcmkvar->vpx = ( per64[1] >= 0 )? x1 : -x1;

		break;

/****************************************************************************************************/
/*		Deceleration stop mode																		*/
/****************************************************************************************************/
	default: 
/*--------------------------------------------------------------------------------------------------*/
/*		Start process																				*/
/*--------------------------------------------------------------------------------------------------*/
		pcmkvar->cmderrf = FALSE;
/*--------------------------------------------------------------------------------------------------*/
/*		Calculation of position command speed																			*/
/*--------------------------------------------------------------------------------------------------*/
		if( pcmkvar->vpx >= 0 )
		{
			pcmkvar->vpx = pcmkvar->vpx - pcmkprm->vpdec;
			if( pcmkvar->vpx <= 0 )
			{
				pcmkvar->vpx = 0;
				pcmkvar->calendf = TRUE;
			}
			else
			{
				pcmkvar->calendf = FALSE;
			}
		}
		else
		{
			pcmkvar->vpx = pcmkvar->vpx + pcmkprm->vpdec;
			if( pcmkvar->vpx >= 0 )
			{
				pcmkvar->vpx = 0;
				pcmkvar->calendf = TRUE;
			}
			else
			{
				pcmkvar->calendf = FALSE;
			}
		}
		break;
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Calculation end process: Update position command output (absolute value)					*/
/*--------------------------------------------------------------------------------------------------*/
	x1 = pcmkvar->vpx + pcmkvar->vpxrem;
	x2 = (x1>=0)? (x1>>pcmkprm->pshlx) : -((-x1)>>pcmkprm->pshlx);
	pcmkvar->vpxrem = x1 - (x2<<pcmkprm->pshlx);
	pcmkvar->pcmdout[0] = pcmkvar->pcmdout[0] + x2;
	carry = ((UINT32)pcmkvar->pcmdout[0] < (UINT32)x2);
	pcmkvar->pcmdout[1] = pcmkvar->pcmdout[1] + (x2>>31) + carry;
/*--------------------------------------------------------------------------------------------------*/
	return( x2 );
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Position command generator Positioning parameter calculation A
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibPcalaPcmdMaker( 		
		INT32	maxspd, 			/* Maximum speed					[2^24/OvrSpd]				*/
		INT32	acctime,			/* Acceleration time				[xs]						*/
		INT32	dectime,			/* Deceleration time				[xs]						*/
		INT32	scantime,			/* Scan time						[xs]						*/
		PCMKP	*pcmkprm	)		/* Calculation result output structure pointer[-]				*/

{
	INT32	maxvp;
	UINT32	xx[2];
	PCMKP	wkprm;

/*--------------------------------------------------------------------------------------------------*/
/*	Maximum position command speed[xpulse/scan] : maxvp = osvpm * maxspd / 2^24						*/
/*--------------------------------------------------------------------------------------------------*/
	MULU32U32( (pcmkprm->osvpm << 8), maxspd, xx );
	maxvp = (xx[1] > 0)? xx[1] : 0;
/*--------------------------------------------------------------------------------------------------*/
/*	Position command acceleration[xpulse/scan/scan] : vpacc = maxvp * scantime / acctime			*/
/*--------------------------------------------------------------------------------------------------*/
	wkprm.vpacc = MlibScalKxgain( maxvp, scantime, acctime, NULL, -24 );
	if( wkprm.vpacc <= 0 ){ wkprm.vpacc = 1;}
/*--------------------------------------------------------------------------------------------------*/
/*	Position command deceleration[xpulse/scan/scan] : vpdec = maxvp * scantime / dectime			*/
/*--------------------------------------------------------------------------------------------------*/
	wkprm.vpdec = MlibScalKxgain( maxvp, scantime, dectime, NULL, -24 );
	if( wkprm.vpdec <= 0 ){ wkprm.vpdec = 1;}
/*--------------------------------------------------------------------------------------------------*/
/*	Quotient (vpamx), remainder (vparx), acceleration deceleration stop pulse amount (vpapx)		*/
/*--------------------------------------------------------------------------------------------------*/
/*	vpamx = vpacc / vpdec																			*/
/*	vparx = vpacc % vpdec																			*/
/*	vpapx = (vpamx + 1) * (vpacc + vparx) / 2														*/
/*--------------------------------------------------------------------------------------------------*/
	wkprm.vpamx = wkprm.vpacc / wkprm.vpdec;
	wkprm.vparx = wkprm.vpacc - (wkprm.vpamx*wkprm.vpdec);
	if( wkprm.vpamx & 0x0001 )
	{
		wkprm.vpapx = ((wkprm.vpamx+1)>>1)*(wkprm.vpacc+wkprm.vparx);
	}
	else
	{
		wkprm.vpapx = (wkprm.vpamx+1)*((wkprm.vpacc+wkprm.vparx)>>1);
	}
/*--------------------------------------------------------------------------------------------------*/
/*	parameter settings																				*/
/*--------------------------------------------------------------------------------------------------*/
	pcmkprm->vpacc	= wkprm.vpacc;
	pcmkprm->vpdec	= wkprm.vpdec;
	pcmkprm->vpamx	= wkprm.vpamx;
	pcmkprm->vparx	= wkprm.vparx;
	pcmkprm->vpapx	= wkprm.vpapx;
	pcmkprm->maxspd = maxspd;

	return( TRUE );

}
/****************************************************************************************************
 * DESCRIPTION:
 *		 Position command generator Initial parameter calculation	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC double Mlibpow(double x, int y) {
    double result = 1.0;

    if (y < 0) {
        x = 1 / x;
        y = -y;
    }

    while (y > 0) {
        if (y & 1) {
            result *= x;
        }
        x *= x;
        y >>= 1;
    }

    return result;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		 Position command generator Initial parameter calculation	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibIpcalPcmdMaker(
		INT32	ksosvp, 			/* OS pulse speed						[xpulse/scan]			*/
		INT32	maxspdm,			/* Maximum motor speed					[2^24/OvrSpd]			*/
		INT32	maxspdi,			/* Interpolation mode Maximum speed 	[2^24/OvrSpd]			*/
		INT32	pcmd64f,			/* Position command 64-bit flag 		[TRUE/FALSE]			*/
		PCMKP	*pcmkprm	)		/* Calculation result output structure pointer[-]				*/

{
	INT32	kx,sx;
	UINT32	xx[2];
	UINT32	yy[2];

/*--------------------------------------------------------------------------------------------------*/
/*		OS motor pulse speed[xpulse/scan] 														    */
/*--------------------------------------------------------------------------------------------------*/

		sx = ((INT32)ksosvp >> 24) + 1;		
		kx = (((INT32)ksosvp << 8 ) >> 7);
		if( sx > 16 )
		{
			kx = (kx >> (sx - 16));
			sx = 16;
		}

/*--------------------------------------------------------------------------------------------------*/
/*		Maximum motor pulse speed[xpulse/scan] : maxvpm = osvpm * maxspdm / 2^24					*/
/*--------------------------------------------------------------------------------------------------*/
		MULU32U32( (kx << 8), maxspdm, xx );

/*--------------------------------------------------------------------------------------------------*/
/*		Interpolation mode Maximum pulse speed[xpulse/scan] : maxipv = osvpm * maxspdi / 2^24 		*/
/*--------------------------------------------------------------------------------------------------*/
		MULU32U32( (kx << 8), maxspdi, yy );

/*--------------------------------------------------------------------------------------------------*/
/*		parameter settings																			*/
/*--------------------------------------------------------------------------------------------------*/
		pcmkprm->osvpm	 = kx;
		pcmkprm->maxvpm  = xx[1];
		pcmkprm->maxipv  = yy[1];
		pcmkprm->pshlx	 = (UINT8)sx;
		pcmkprm->pcmd64f = (UINT8)pcmd64f;

		return( TRUE );

}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Linear Accelerator (LAU) filter: rv = z + dz or rv = z-dz;	
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibLaufilter(INT32 u, INT32 z, INT32 dz )				
{
	INT32	x;

	if( dz == 0 )					
	{								
		x = u;
	}
	else if( u > z )			
	{
		x = z + dz;
		if( x > u ){ x = u;}
	}
	else							
	{
		x = z - dz;
		if( x < u ){ x = u;}
	}
	return( x );

}


/****************************************************************************************************
 * DESCRIPTION:
 *			
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 MlibSins16( INT32 a, INT32 unit )
{
	REAL32	rv;
	REAL32	rad;

	if( (unit & 0x01) == 0 )	/* Input : 360deg/3600		*/
	{
		rad = DEG2RAD * (REAL32)a;
	}
	else						/* Input : 360deg/16384		*/
	{
		rad = FOURTEENBIT2RAD * (REAL32)a;
	}

	rv = sinf( rad );

	if( (unit & 0x10) == 0 )	/* Output : 1.0000/10000	*/
	{
		rv = rv * 10000.0;
	}
	else						/* Output : 1.0000/16384	*/
	{
		rv = rv * 16384.0;
	}
	return ( (INT32)rv );
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 High-speed Sin calculation table: Input: [360deg/1024], Output: [1.0/16384]	
 * RETURNS:
 *
****************************************************************************************************/			
const INT16 MlibFastSinTbl[1024] = {
		 0,   101,	 201,	302,   402,   503,	 603,	704,
	   804,   904,	1005,  1105,  1205,  1306,	1406,  1506,
	  1606,  1706,	1806,  1906,  2006,  2105,	2205,  2305,
	  2404,  2503,	2603,  2702,  2801,  2900,	2999,  3098,
	  3196,  3295,	3393,  3492,  3590,  3688,	3786,  3883,
	  3981,  4078,	4176,  4273,  4370,  4467,	4563,  4660,
	  4756,  4852,	4948,  5044,  5139,  5235,	5330,  5425,
	  5520,  5614,	5708,  5803,  5897,  5990,	6084,  6177,
	  6270,  6363,	6455,  6547,  6639,  6731,	6823,  6914,
	  7005,  7096,	7186,  7276,  7366,  7456,	7545,  7635,
	  7723,  7812,	7900,  7988,  8076,  8163,	8250,  8337,
	  8423,  8509,	8595,  8680,  8765,  8850,	8935,  9019,
	  9102,  9186,	9269,  9352,  9434,  9516,	9598,  9679,
	  9760,  9841,	9921, 10001, 10080, 10159, 10238, 10316,
	 10394, 10471, 10549, 10625, 10702, 10778, 10853, 10928,
	 11003, 11077, 11151, 11224, 11297, 11370, 11442, 11514,
	 11585, 11656, 11727, 11797, 11866, 11935, 12004, 12072,
	 12140, 12207, 12274, 12340, 12406, 12472, 12537, 12601,
	 12665, 12729, 12792, 12854, 12916, 12978, 13039, 13100,
	 13160, 13219, 13279, 13337, 13395, 13453, 13510, 13567,
	 13623, 13678, 13733, 13788, 13842, 13896, 13949, 14001,
	 14053, 14104, 14155, 14206, 14256, 14305, 14354, 14402,
	 14449, 14497, 14543, 14589, 14635, 14680, 14724, 14768,
	 14811, 14854, 14896, 14937, 14978, 15019, 15059, 15098,
	 15137, 15175, 15213, 15250, 15286, 15322, 15357, 15392,
	 15426, 15460, 15493, 15525, 15557, 15588, 15619, 15649,
	 15679, 15707, 15736, 15763, 15791, 15817, 15843, 15868,
	 15893, 15917, 15941, 15964, 15986, 16008, 16029, 16049,
	 16069, 16088, 16107, 16125, 16143, 16160, 16176, 16192,
	 16207, 16221, 16235, 16248, 16261, 16273, 16284, 16295,
	 16305, 16315, 16324, 16332, 16340, 16347, 16353, 16359,
	 16364, 16369, 16373, 16376, 16379, 16381, 16383, 16384,
/*--------------------------------------------------------------------------------------------------*/
	 16384, 16384, 16383, 16381, 16379, 16376, 16373, 16369,
	 16364, 16359, 16353, 16347, 16340, 16332, 16324, 16315,
	 16305, 16295, 16284, 16273, 16261, 16248, 16235, 16221,
	 16207, 16192, 16176, 16160, 16143, 16125, 16107, 16088,
	 16069, 16049, 16029, 16008, 15986, 15964, 15941, 15917,
	 15893, 15868, 15843, 15817, 15791, 15763, 15736, 15707,
	 15679, 15649, 15619, 15588, 15557, 15525, 15493, 15460,
	 15426, 15392, 15357, 15322, 15286, 15250, 15213, 15175,
	 15137, 15098, 15059, 15019, 14978, 14937, 14896, 14854,
	 14811, 14768, 14724, 14680, 14635, 14589, 14543, 14497,
	 14449, 14402, 14354, 14305, 14256, 14206, 14155, 14104,
	 14053, 14001, 13949, 13896, 13842, 13788, 13733, 13678,
	 13623, 13567, 13510, 13453, 13395, 13337, 13279, 13219,
	 13160, 13100, 13039, 12978, 12916, 12854, 12792, 12729,
	 12665, 12601, 12537, 12472, 12406, 12340, 12274, 12207,
	 12140, 12072, 12004, 11935, 11866, 11797, 11727, 11656,
	 11585, 11514, 11442, 11370, 11297, 11224, 11151, 11077,
	 11003, 10928, 10853, 10778, 10702, 10625, 10549, 10471,
	 10394, 10316, 10238, 10159, 10080, 10001,	9921,  9841,
	  9760,  9679,	9598,  9516,  9434,  9352,	9269,  9186,
	  9102,  9019,	8935,  8850,  8765,  8680,	8595,  8509,
	  8423,  8337,	8250,  8163,  8076,  7988,	7900,  7812,
	  7723,  7635,	7545,  7456,  7366,  7276,	7186,  7096,
	  7005,  6914,	6823,  6731,  6639,  6547,	6455,  6363,
	  6270,  6177,	6084,  5990,  5897,  5803,	5708,  5614,
	  5520,  5425,	5330,  5235,  5139,  5044,	4948,  4852,
	  4756,  4660,	4563,  4467,  4370,  4273,	4176,  4078,
	  3981,  3883,	3786,  3688,  3590,  3492,	3393,  3295,
	  3196,  3098,	2999,  2900,  2801,  2702,	2603,  2503,
	  2404,  2305,	2205,  2105,  2006,  1906,	1806,  1706,
	  1606,  1506,	1406,  1306,  1205,  1105,	1005,	904,
	   804,   704,	 603,	503,   402,   302,	 201,	101,
/*--------------------------------------------------------------------------------------------------*/
		 0,  -101,	-201,  -302,  -402,  -503,	-603,  -704,
	  -804,  -904, -1005, -1105, -1205, -1306, -1406, -1506,
	 -1606, -1706, -1806, -1906, -2006, -2105, -2205, -2305,
	 -2404, -2503, -2603, -2702, -2801, -2900, -2999, -3098,
	 -3196, -3295, -3393, -3492, -3590, -3688, -3786, -3883,
	 -3981, -4078, -4176, -4273, -4370, -4467, -4563, -4660,
	 -4756, -4852, -4948, -5044, -5139, -5235, -5330, -5425,
	 -5520, -5614, -5708, -5803, -5897, -5990, -6084, -6177,
	 -6270, -6363, -6455, -6547, -6639, -6731, -6823, -6914,
	 -7005, -7096, -7186, -7276, -7366, -7456, -7545, -7635,
	 -7723, -7812, -7900, -7988, -8076, -8163, -8250, -8337,
	 -8423, -8509, -8595, -8680, -8765, -8850, -8935, -9019,
	 -9102, -9186, -9269, -9352, -9434, -9516, -9598, -9679,
	 -9760, -9841, -9921,-10001,-10080,-10159,-10238,-10316,
	-10394,-10471,-10549,-10625,-10702,-10778,-10853,-10928,
	-11003,-11077,-11151,-11224,-11297,-11370,-11442,-11514,
	-11585,-11656,-11727,-11797,-11866,-11935,-12004,-12072,
	-12140,-12207,-12274,-12340,-12406,-12472,-12537,-12601,
	-12665,-12729,-12792,-12854,-12916,-12978,-13039,-13100,
	-13160,-13219,-13279,-13337,-13395,-13453,-13510,-13567,
	-13623,-13678,-13733,-13788,-13842,-13896,-13949,-14001,
	-14053,-14104,-14155,-14206,-14256,-14305,-14354,-14402,
	-14449,-14497,-14543,-14589,-14635,-14680,-14724,-14768,
	-14811,-14854,-14896,-14937,-14978,-15019,-15059,-15098,
	-15137,-15175,-15213,-15250,-15286,-15322,-15357,-15392,
	-15426,-15460,-15493,-15525,-15557,-15588,-15619,-15649,
	-15679,-15707,-15736,-15763,-15791,-15817,-15843,-15868,
	-15893,-15917,-15941,-15964,-15986,-16008,-16029,-16049,
	-16069,-16088,-16107,-16125,-16143,-16160,-16176,-16192,
	-16207,-16221,-16235,-16248,-16261,-16273,-16284,-16295,
	-16305,-16315,-16324,-16332,-16340,-16347,-16353,-16359,
	-16364,-16369,-16373,-16376,-16379,-16381,-16383,-16384,
/*--------------------------------------------------------------------------------------------------*/
	-16384,-16384,-16383,-16381,-16379,-16376,-16373,-16369,
	-16364,-16359,-16353,-16347,-16340,-16332,-16324,-16315,
	-16305,-16295,-16284,-16273,-16261,-16248,-16235,-16221,
	-16207,-16192,-16176,-16160,-16143,-16125,-16107,-16088,
	-16069,-16049,-16029,-16008,-15986,-15964,-15941,-15917,
	-15893,-15868,-15843,-15817,-15791,-15763,-15736,-15707,
	-15679,-15649,-15619,-15588,-15557,-15525,-15493,-15460,
	-15426,-15392,-15357,-15322,-15286,-15250,-15213,-15175,
	-15137,-15098,-15059,-15019,-14978,-14937,-14896,-14854,
	-14811,-14768,-14724,-14680,-14635,-14589,-14543,-14497,
	-14449,-14402,-14354,-14305,-14256,-14206,-14155,-14104,
	-14053,-14001,-13949,-13896,-13842,-13788,-13733,-13678,
	-13623,-13567,-13510,-13453,-13395,-13337,-13279,-13219,
	-13160,-13100,-13039,-12978,-12916,-12854,-12792,-12729,
	-12665,-12601,-12537,-12472,-12406,-12340,-12274,-12207,
	-12140,-12072,-12004,-11935,-11866,-11797,-11727,-11656,
	-11585,-11514,-11442,-11370,-11297,-11224,-11151,-11077,
	-11003,-10928,-10853,-10778,-10702,-10625,-10549,-10471,
	-10394,-10316,-10238,-10159,-10080,-10001, -9921, -9841,
	 -9760, -9679, -9598, -9516, -9434, -9352, -9269, -9186,
	 -9102, -9019, -8935, -8850, -8765, -8680, -8595, -8509,
	 -8423, -8337, -8250, -8163, -8076, -7988, -7900, -7812,
	 -7723, -7635, -7545, -7456, -7366, -7276, -7186, -7096,
	 -7005, -6914, -6823, -6731, -6639, -6547, -6455, -6363,
	 -6270, -6177, -6084, -5990, -5897, -5803, -5708, -5614,
	 -5520, -5425, -5330, -5235, -5139, -5044, -4948, -4852,
	 -4756, -4660, -4563, -4467, -4370, -4273, -4176, -4078,
	 -3981, -3883, -3786, -3688, -3590, -3492, -3393, -3295,
	 -3196, -3098, -2999, -2900, -2801, -2702, -2603, -2503,
	 -2404, -2305, -2205, -2105, -2006, -1906, -1806, -1706,
	 -1606, -1506, -1406, -1306, -1205, -1105, -1005,  -904,
	  -804,  -704,	-603,  -503,  -402,  -302,	-201,  -101,
};


