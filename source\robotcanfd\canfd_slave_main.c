/**
 * @file canfd_slave.c
 * @brief CANFD从站功能模块实现
 */
#include <string.h>
#include "hal_data.h"
#include "bsp_api.h"
#include "r_canfd.h"
#include "canfd_slave.h"
#include "Global.h"
#include "canfd_protocol.h"
#include "flash_if.h"
#include "BaseLoops.h"
#include "Cia402Appl.h"
#include "bsp_can.h"
#include "canfd_common.h"
#include "cal_crc.h"


CANFD_SlaveConfig config;
extern  can_frame_t g_can_rx0_frame;
CANFD_Frame Rxframe;
// CANFD_Frame Txframe;
CANFD_Frame_FIFO   TxframeFifo;

bool g_tx0_complete_flag = true;
bool g_canfd_glerr_flag = false;
bool g_canfd_cherr_flag = false;
/* 电机控制和状态变量 */
HUMAN_ROBOT_CTRL HumanRotCtrl = {0};

uint8_t Canfd_O<PERSON>_Buffer[CANFD_OTA_BUFFER_SIZE] = {0};
uint8_t Canfd_Ota_RxBuffer[CANFD_OAT_MAX_RDATE] = {0};

static uint8_t canfd_writedata_to_fifo(CANFD_Frame *Txframe);



static uint8_t canfd_writedata_to_fifo(CANFD_Frame *Txframe)
{
    memcpy(&TxframeFifo.tx_data[TxframeFifo.windex], Txframe, sizeof(CANFD_Frame));  
       
    TxframeFifo.windex++;

    if(TxframeFifo.windex >= 16)
    {
        TxframeFifo.windex = 0;
    }
    TxframeFifo.tx_data_num++;  

    return TxframeFifo.tx_data_num;
}



/**
 * @brief 初始化    
 */

void Canfd_Slave_Init(void)
{
    AXIS_HANDLE *AxisN;
    AxisN = (AXIS_HANDLE*)GetAxisHandle(0);	
    /* 配置从站 */
    config.slaveAddress = AxisN->Prm->PnCfgPrm.DevID+ 0x10 -1;  /* 从站地址为2 */
    config.ComStatus = 0;   /* 通信状态 */

    g_tx0_complete_flag = true;
    g_canfd_glerr_flag = false;
    g_canfd_cherr_flag = false;
    
    CanSetAfl(config.slaveAddress);

    firmware_init();
     
    config.canfdInterface = (void*)&g_canfd0_ctrl; /* 使用CANFD接口 */
    if (CANFD_SlaveInit(&config) != CANFD_OK) {
        return;
    }
    /* 注册回调函数 */
    CANFD_RegisterControlCallback(MotorControlCallback);
}


/**
 * @brief 监控CANFD状态
 */
void Canfd_State_Monitor(void)
{
    AXIS_HANDLE *AxisN;
    AxisN = (AXIS_HANDLE*)GetAxisHandle(0);	

    AxisN->Prm->PnMoniPrm.CanFdCh0SR = Canfd_GetCh0SR();
    AxisN->Prm->PnMoniPrm.CanFdCh0ER = Canfd_GetCh0ER();
    AxisN->Prm->PnMoniPrm.CanFdGlSR = Canfd_GetCh0Gl0SR();
    AxisN->Prm->PnMoniPrm.CanFdGlER = Canfd_GetCh0Gl0ER();
    AxisN->Prm->PnMoniPrm.CanFdTQ0SR = Canfd_GetCh0TQ0SR();
    AxisN->Prm->PnMoniPrm.Ch0CanFdSR = Canfd_GetCh0CFSR();
}



/**
 * @brief CANFD 主从发送，接收到主站请求数据后发送
 */
uint8_t canfd_response()
{
    CANFD_Frame Txframe;
    
    if(CANFD_ParkResponseData(&Txframe, &Rxframe,0) == CANFD_OK)
    {
        canfd_writedata_to_fifo(&Txframe);
    }
    return 0;
}

/**
 * @brief CANFD 主从发送，接收到主站请求数据后发送
 */
uint8_t CanFd_Send()
{
    // 准备电机状态信息
    can_frame_t txframe_can;
    CANFD_Frame *Txframe;
    
    if(TxframeFifo.tx_data_num > TxframeFifo.tx_datamax_num)
    {
        TxframeFifo.tx_datamax_num = TxframeFifo.tx_data_num;
    }

    if(TxframeFifo.tx_data_num > 0 && g_tx0_complete_flag)
    {
      Txframe = &TxframeFifo.tx_data[TxframeFifo.rindex];
      txframe_can.id = Txframe->can_id;
      txframe_can.id_mode = (can_id_mode_t)Txframe->id_mode;
      txframe_can.type = (can_frame_type_t)Txframe->type;
      txframe_can.data_length_code = Txframe->dlc;
      
      if(Rxframe.options != 0)
      {
         txframe_can.options = Rxframe.options;
      }
      else
      {
        txframe_can.options = 0x06;
      }
      
      // 打包CANFD帧头部
      PackFrameHeader(Txframe, &txframe_can.data[0]);
      memcpy(&txframe_can.data[4], &Txframe->data[0],Txframe->header.length);   

      // 发送CANFD数据
      if(R_CANFD_Write(&g_canfd0_ctrl, CANFD_TX_BUFFER_0, &txframe_can)!=FSP_SUCCESS) return 1; 

      TxframeFifo.rindex++;
      if(TxframeFifo.rindex >= 16)
      {
          TxframeFifo.rindex = 0;
      }
      TxframeFifo.tx_data_num--;

      g_tx0_complete_flag = false;
    }
    
    return 0;
}   



/**
 * @brief CANFD 中断回调函数
 */
void canfd0_callback (can_callback_args_t * p_args)
{
    switch (p_args->event)
    {
        case CAN_EVENT_RX_COMPLETE:    /* Receive complete event. */
        {
            // 从CANFD接收缓冲区读取数据
            // memset(&Rxframe.data[0], 0, 60);
            memcpy(&g_can_rx0_frame, &(p_args->frame), sizeof(can_frame_t));   

            UnPackFrameHeader(&Rxframe, &(p_args->frame));    
                
            // memcpy(&Rxframe.data[0], &(p_args->frame.data[4]), );    
            memcpy(&Rxframe.data[0], &(p_args->frame.data[4]), Rxframe.header.length);
            
            // 解析CANFD数据帧
            if(CANFD_OK == CANFD_ProcessRxFrame(&Rxframe))
            {
                canfd_response();
            }   
            break;
        }
        case CAN_EVENT_TX_COMPLETE:    /* Transmit complete event. */
        {
            /* Handle event */
            g_tx0_complete_flag = true;
            break;
        }
        case CAN_EVENT_ERR_GLOBAL:  
        {
            /* Global error. */
          /* Handle event */
            TxframeFifo.rindex = 0;
            TxframeFifo.windex = 0;
            TxframeFifo.tx_data_num = 0;         
            break;
        }
        case CAN_EVENT_ERR_CHANNEL:                       /* Channel error. */
        {
          /* Get error status */
          /* Handle event */
          TxframeFifo.rindex = 0;
          TxframeFifo.windex = 0;
          TxframeFifo.tx_data_num = 0;

            break;
        }
        default:
        {
            break;
        }
    }
}



/**
 * @brief 电机控制回调函数
 * 
 * @param controlData 电机控制参数
 */
void MotorControlCallback(CANFD_MotorControl *controlData) {
    
    AXIS_HANDLE *AxisN;
    AxisN = (AXIS_HANDLE*)GetAxisHandle(0);
    REAL32 TargetTorque = 0; 
    /* 更新电机控制模式和运行状态 */
    
   HumanRotCtrl.CovCtrlCmd.controlMode = controlData->controlMode;   

//   if(controlData->TargetSetFlag == 1)
   if(controlData->runCommand && controlData->TargetSetFlag == 1)
   {
        Canfd_SetCanBreakEnable(1);
   }

    HumanRotCtrl.Timeout.Count = 0;
    /* 根据控制模式解析不同的参数 */
    switch (controlData->controlMode) {
        case MOTOR_MODE_POSITION: // csp
            HumanRotCtrl.CovCtrlCmd.controlMode  = CTRL_MODE_CSP;
            HumanRotCtrl.CovCtrlCmd.targetPosCnt = (int32_t)((controlData->targetPosition - 32767)*HumanRotCtrl.Prm.PosiontQ2Cnt);
            HumanRotCtrl.CovCtrlCmd.targetPosRad  = uint_to_float(controlData->targetPosition, HumanRotCtrl.Prm.Min_Postion, HumanRotCtrl.Prm.Max_Postion, 16);
            break;
            
        case MOTOR_MODE_VELOCITY://csv
            HumanRotCtrl.CovCtrlCmd.controlMode = CTRL_MODE_CSV;
            HumanRotCtrl.CovCtrlCmd.targetVelCnts = (int32_t)((controlData->targetVelocity-32767)*HumanRotCtrl.Prm.VelocityQ2Cnts);
            HumanRotCtrl.CovCtrlCmd.targetVelRads = uint_to_float(controlData->targetVelocity, HumanRotCtrl.Prm.Min_Velocity, HumanRotCtrl.Prm.Max_Velocity, 16);
            break;
            
        case MOTOR_MODE_TORQUE://cst
            HumanRotCtrl.CovCtrlCmd.controlMode = CTRL_MODE_CST;   
            TargetTorque= ((REAL32)controlData->targetTorque- 32767)*HumanRotCtrl.Prm.TorqueQ2Nm;  // 32768 ->maxtrq
            HumanRotCtrl.CovCtrlCmd.torqueFeedffNm = uint_to_float(controlData->targetTorque, HumanRotCtrl.Prm.Min_Torque, HumanRotCtrl.Prm.Max_Torque, 16);
            HumanRotCtrl.CovCtrlCmd.targetTorque  = TargetTorque;
            break;
            
        case MOTOR_MODE_IMPEDANCE:          
            HumanRotCtrl.CovCtrlCmd.controlMode = MOTOR_MODE_IPD;   
            HumanRotCtrl.CovCtrlCmd.targetPosRad  = uint_to_float(controlData->targetPosition, HumanRotCtrl.Prm.Min_Postion, HumanRotCtrl.Prm.Max_Postion, 16);
            HumanRotCtrl.CovCtrlCmd.targetVelRads = uint_to_float(controlData->targetVelocity, HumanRotCtrl.Prm.Min_Velocity, HumanRotCtrl.Prm.Max_Velocity, 16);
            HumanRotCtrl.CovCtrlCmd.torqueFeedffNm = uint_to_float(controlData->torqueFeedforward, HumanRotCtrl.Prm.Min_Torque, HumanRotCtrl.Prm.Max_Torque, 16);
            HumanRotCtrl.CovCtrlCmd.positionKp = uint_to_float(controlData->positionKp, 0, HumanRotCtrl.Prm.IpdKpMax, 16);
            HumanRotCtrl.CovCtrlCmd.velocityKd = uint_to_float(controlData->velocityKd, 0, HumanRotCtrl.Prm.IpdKdMax, 16);
            break;
        default:
            break;
    }

   if(!AxisN->BaseCtrls->CmdEnable)
   {
        HumanRotCtrl.CovCtrlCmd.targetPosCnt    = AxisN->BaseLoops->IpdPosFdb;
        HumanRotCtrl.CovCtrlCmd.targetVelCnts   = 0;
        HumanRotCtrl.CovCtrlCmd.targetPosRad    = AxisN->BaseLoops->IpdPosFdbRad;
        HumanRotCtrl.CovCtrlCmd.targetVelRads   = AxisN->BaseLoops->IpdSpdFdbRads; 
        HumanRotCtrl.CovCtrlCmd.torqueFeedffNm  = 0;             
        HumanRotCtrl.CovCtrlCmd.targetTorque    = 0;
   }    

    HumanRotCtrl.CovCtrlCmd.runCommand = controlData->runCommand; 

    if(AxisN->Prm->PnCtrlCfgPrm.CtrlSource != 1)  return ;

    switch (HumanRotCtrl.CovCtrlCmd.controlMode) {   
        case CTRL_MODE_CSP: // csp
            AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 = CTRL_MODE_CSP;
            AxisN->BaseCtrls->Cia402Axis.Objects->TargetPosition0x607A = HumanRotCtrl.CovCtrlCmd.targetPosCnt;
            break;
            
        case CTRL_MODE_CSV://csv
            AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 = CTRL_MODE_CSV;
            AxisN->BaseCtrls->Cia402Axis.Objects->TargetVelocity0x60FF = HumanRotCtrl.CovCtrlCmd.targetVelCnts;
            // gMotorData.accelerationTime = controlData->modeParams.velocityMode.accelerationTime;
            break;
            
        case CTRL_MODE_CST://cst
            AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 = CTRL_MODE_CST;   
            TargetTorque = GearRatioInvCal(AxisN->BaseLoops, HumanRotCtrl.CovCtrlCmd.targetTorque);
            TargetTorque = (REAL32)(TargetTorque * AxisN->BaseLoops->Bprm->Ktrqper);    
            AxisN->BaseCtrls->Cia402Axis.Objects->TargetTorque0x6071   = (INT32)(TargetTorque);
            break;
            
        case MOTOR_MODE_IPD:
            AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 = MOTOR_MODE_IPD;          

            AxisN->BaseCtrls->CtrlCmdPrm.Kposstiff = HumanRotCtrl.CovCtrlCmd.positionKp;
            AxisN->BaseCtrls->CtrlCmdPrm.Kspdstiff = HumanRotCtrl.CovCtrlCmd.velocityKd;

            // disp
            AxisN->Prm->PnCtrlCfgPrm.PosStiff = HumanRotCtrl.CovCtrlCmd.positionKp*100;
            AxisN->Prm->PnCtrlCfgPrm.SpdStiff = HumanRotCtrl.CovCtrlCmd.velocityKd*100;
            break;
        default:
            break;
      }  

      controlData->TargetSetFlag = 0;   
}



/**
 * @brief 获取IPD位置目标下限
 * 
 * @param void 
 * @return float 
 */

float Canfd_GetIpdPosNLmit  (void)
{
    return HumanRotCtrl.Prm.IpdPosNLmit;
}

/**
 * @brief 获取IPD位置目标上限
 * 
 * @param void 
 * @return float 
 */

float Canfd_GetIpdPosPLmit  (void)
{
    return HumanRotCtrl.Prm.IpdPosPLmit;
}

/**
 * @brief 获取IPD位置目标
 * 
 * @param void 
 * @return float 
 */

float Canfd_GetIpdPosTarget  (void)
{
    return HumanRotCtrl.CovCtrlCmd.targetPosRad;
}

/**
 * @brief 更新IPD位置目标
 * 
 * @param void 
 * @return float 
 */

void Canfd_SetIpdPosTarget  (float pos)
{
    HumanRotCtrl.CovCtrlCmd.targetPosRad = pos;
}


/**
 * @brief 获取IPD速度目标
 * 
 * @param void 
 * @return float 
 */
float Canfd_GetIpdSpdTarget  (void)
{
    return HumanRotCtrl.CovCtrlCmd.targetVelRads;
}

/**
 * @brief 更新IPD速度目标
 * 
 * @param void 
 * @return float 
 */
void Canfd_SetIpdSpdTarget  (float spd)
{
    HumanRotCtrl.CovCtrlCmd.targetVelRads = spd;
}

/**
 * @brief 获取IPD转矩前馈
 * 
 * @param void 
 * @return float 
 */
float Canfd_GetIpdTrqFf  (void)
{
    return HumanRotCtrl.CovCtrlCmd.torqueFeedffNm;
}

/** 
 * @brief 更新IPD转矩前馈
 * 
 * @param void 
 * @return float 
 */
void Canfd_SetIpdTrqFf  (float trq)
{
    HumanRotCtrl.CovCtrlCmd.torqueFeedffNm = trq;
}

/**
 * @brief 获取IPD转矩目标上限
 * 
 * @param void 
 * @return float 
 */
float Canfd_GetIpdTrqLimit(void)
{
    return HumanRotCtrl.Prm.Max_Torque;
}


/**
 * @brief 设置CAN断线保护使能
 * 
 * @param enable 使能状态
 * condition : only when receive canfd frame cmd 0x21 
 */
void Canfd_SetCanBreakEnable(uint8_t enable)
{
    static uint8_t canbreak_cnt = 0;
    canbreak_cnt++;
    if(canbreak_cnt >= 1)
    {
        canbreak_cnt = 0;
        HumanRotCtrl.Timeout.CanBreakEnable = enable;
    }
}


/**
 * @brief 获取电机超时状态
 * 
 * @param void 
 * @return uint8_t 
 */

uint8_t Canfd_GetTimeoutFlag(void)
{
    return HumanRotCtrl.Timeout.CanBreakFlag;
}       

/**
 * @brief 重置电机超时状态
 * 
 * @param void 
 * @return uint8_t 
 */

void Canfd_ResetTimeoutFlag(void)
{
    HumanRotCtrl.Timeout.CanBreakFlag = 0;
    HumanRotCtrl.Timeout.Count = 0;
    HumanRotCtrl.Timeout.Enable = 0;
    HumanRotCtrl.Timeout.CanBreakEnable = 0;
}   
/**
 * @brief 获取电机状态
 * 
 * @param void 
 * @return void 
 */
static void Canfd_GetServoState(void)
{
    AXIS_HANDLE *AxisN;
    AxisN = (AXIS_HANDLE*)GetAxisHandle(0);	

    if(AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 == CTRL_MODE_CSP)
    {
        HumanRotCtrl.statusData.controlMode  = MOTOR_MODE_POSITION;
    }
    else if(AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 == CTRL_MODE_CSV)
    {
        HumanRotCtrl.statusData.controlMode   = MOTOR_MODE_VELOCITY;
    }   
    else if(AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 == CTRL_MODE_CST)
    {
        HumanRotCtrl.statusData.controlMode  = MOTOR_MODE_TORQUE;
    }
    else if(AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 == MOTOR_MODE_IPD)
    {
        HumanRotCtrl.statusData.controlMode  = MOTOR_MODE_IMPEDANCE;
    }
    else if(AxisN->BaseCtrls->Cia402Axis.Objects->ModesOfOperation0x6060 == CTRL_MODE_HOME)
    {
        HumanRotCtrl.statusData.controlMode   = MOTOR_MODE_ZEROING;
    }
    else  
    {
        HumanRotCtrl.statusData.controlMode   = MOTOR_MODE_DEFAULT;
    }

    if(AxisN->Prm->PnCtrlCfgPrm.CtrlSource == 1)
    {
        if(AxisN->BaseCtrls->Cia402Axis.State == STATE_OPERATION_ENABLED)
        {
            HumanRotCtrl.statusData.runningState = MOTOR_STATE_RUNNING;
            if(HumanRotCtrl.Prm.CanFdBreakTime > 0)
            {
                HumanRotCtrl.Timeout.Enable = 1;  // todo 
            }
        }
        else
        {
            if(AxisN->BaseCtrls->Cia402Axis.State == STATE_FAULT_REACTION_ACTIVE ||
            AxisN->BaseCtrls->Cia402Axis.State == STATE_FAULT)
            {
                HumanRotCtrl.statusData.runningState = MOTOR_STATE_FAULT;
            }
            else
            {
                HumanRotCtrl.statusData.runningState = MOTOR_STATE_STOPPED;
            }
        }
    }

    if(HumanRotCtrl.Timeout.Enable == 1 && HumanRotCtrl.Timeout.CanBreakEnable == 1)
    {
        HumanRotCtrl.Timeout.Count++;
        if(HumanRotCtrl.Timeout.Count >= HumanRotCtrl.Prm.CanFdBreakTime*8)
        {
            HumanRotCtrl.Timeout.CanBreakFlag = 1;
            HumanRotCtrl.Timeout.CanBreakEnable = 0;
            HumanRotCtrl.Timeout.Count = 0;
            HumanRotCtrl.Timeout.UserEnable = 0;
        }
    } 
    else
    {   
        HumanRotCtrl.Timeout.Count = 0;
    }


    // statusData->zeroingState = gMotorData.zeroingState;  // todo     
    HumanRotCtrl.statusData.currentQ  = float_to_uint(AxisN->BaseCtrls->Cia402Axis.Objects->CurrentActualValue0x6078, HumanRotCtrl.Prm.RefMinCur, HumanRotCtrl.Prm.RefMaxCur, 16);
    HumanRotCtrl.statusData.voltageQ  = float_to_uint(AxisN->PowMngr->PowerSts.DcVolt, 0, HumanRotCtrl.Prm.RefMaxVol, 16);
    HumanRotCtrl.statusData.velocityQ = float_to_uint(AxisN->BaseLoops->IpdSpdFdbRads, HumanRotCtrl.Prm.Min_Velocity, HumanRotCtrl.Prm.Max_Velocity, 16);
    HumanRotCtrl.statusData.positionQ = float_to_uint(AxisN->BaseLoops->IpdPosFdbRad, HumanRotCtrl.Prm.Min_Postion, HumanRotCtrl.Prm.Max_Postion, 16);
    HumanRotCtrl.statusData.torqueQ   = float_to_uint(AxisN->BaseLoops->TrqFdbBNm, HumanRotCtrl.Prm.Min_Torque, HumanRotCtrl.Prm.Max_Torque, 16);
    HumanRotCtrl.statusData.torquefdbQ =   float_to_uint(AxisN->BaseLoops->IpdTrqRef,HumanRotCtrl.Prm.Min_Torque, HumanRotCtrl.Prm.Max_Torque, 16);

    HumanRotCtrl.statusData.velocityrads =(HumanRotCtrl.statusData.velocityQ -32767)*HumanRotCtrl.Prm.VelocityQ2Rads;
    HumanRotCtrl.statusData.positionrad = (HumanRotCtrl.statusData.positionQ -32767)*HumanRotCtrl.Prm.PosiontQ2Rad;

    HumanRotCtrl.statusData.AppOrBoot = 0;      // 0 : app 1:boot
    HumanRotCtrl.statusData.DevIsRready = 1;    // 0 : not ready 1:ready
    HumanRotCtrl.statusData.DevHardVersion = 0; // 
    HumanRotCtrl.statusData.DevBootVersion = 0; // 
    HumanRotCtrl.statusData.DevAppVersion = 0;  // 0 : app 1:boot
    HumanRotCtrl.statusData.FirmwareCode = 0;   //     

}



/**
 * @brief 获取电机报警状态
 * 
 * @param void 
 * @return void 
 */
void Canfd_GetServoAlarm(void)
{
    AXIS_HANDLE *AxisN;
    AxisN = (AXIS_HANDLE*)GetAxisHandle(0);	

    HumanRotCtrl.statusData.alarmFlags.alarm1_b.OverVoltage = ALMCheckEachState(AxisN->AlmMngr, ALM_OV);
    HumanRotCtrl.statusData.alarmFlags.alarm1_b.UnderVoltage = ALMCheckEachState(AxisN->AlmMngr, ALM_PUV);
    HumanRotCtrl.statusData.alarmFlags.alarm1_b.OverCurrent = ALMCheckEachState(AxisN->AlmMngr, ALM_OC);
    HumanRotCtrl.statusData.alarmFlags.alarm1_b.PhaseLoss = ALMCheckEachState(AxisN->AlmMngr, ALM_PHALACK);
    HumanRotCtrl.statusData.alarmFlags.alarm1_b.Overload = ALMCheckEachState(AxisN->AlmMngr, ALM_OLF1);
    HumanRotCtrl.statusData.alarmFlags.alarm1_b.OverTemp = ALMCheckEachState(AxisN->AlmMngr, ALM_FAN_TEMP);  
    HumanRotCtrl.statusData.alarmFlags.alarm1_b.Stall = ALMCheckEachState(AxisN->AlmMngr, ALM_STALL);
    HumanRotCtrl.statusData.alarmFlags.alarm1_b.Runaway = ALMCheckEachState(AxisN->AlmMngr, ALM_ORUN);
    
    HumanRotCtrl.statusData.alarmFlags.alarm2_b.Hall = 0;
    HumanRotCtrl.statusData.alarmFlags.alarm2_b.Encoder1 = ALMCheckEachState(AxisN->AlmMngr, ALM_ENCBRK);
    HumanRotCtrl.statusData.alarmFlags.alarm2_b.Encoder2 = ALMCheckEachState(AxisN->AlmMngr, ALM_ENCCRC);
    HumanRotCtrl.statusData.alarmFlags.alarm2_b.PositiveLimit = ALMCheckEachState(AxisN->AlmMngr, ALM_HPOSLMT);
    HumanRotCtrl.statusData.alarmFlags.alarm2_b.NegativeLimit = ALMCheckEachState(AxisN->AlmMngr, ALM_HNEGLMT);
    HumanRotCtrl.statusData.alarmFlags.alarm2_b.SettingError = ALMCheckEachState(AxisN->AlmMngr, ALM_PRM);
    HumanRotCtrl.statusData.alarmFlags.alarm2_b.TrackingError = ALMCheckEachState(AxisN->AlmMngr, ALM_OF);
    HumanRotCtrl.statusData.alarmFlags.alarm2_b.CanBreak = ALMCheckEachState(AxisN->AlmMngr, ALM_ECAT_Break);
    
    HumanRotCtrl.statusData.alarmFlags.alarm3_b.AlmIpm = ALMCheckEachState(AxisN->AlmMngr, ALM_IPM);   
    HumanRotCtrl.statusData.alarmFlags.alarm3_b.OverloadWrn  = ALMCheckEachState(AxisN->AlmMngr, WRN_OLF) | ALMCheckEachState(AxisN->AlmMngr, WRN_OLF1);  
    HumanRotCtrl.statusData.alarmFlags.alarm3_b.IPMOvertTemp = ALMCheckEachState(AxisN->AlmMngr, ALM_IPM_OTM); 
    HumanRotCtrl.statusData.alarmFlags.alarm3_b.PosLimitWrn = ALMCheckEachState(AxisN->AlmMngr, WRN_POSLIMIT);
}

/**
 * @brief 电机使能
 * 
 * @param Control 电机使能状态
 * @return uint8_t 电机使能状态
 */
uint8_t Canfd_ServoCtrl(uint8_t Control)
{
    AXIS_HANDLE *AxisN;
    AxisN = (AXIS_HANDLE*)GetAxisHandle(0);	

     Canfd_GetServoState();

    if(AxisN->Prm->PnCtrlCfgPrm.CtrlSource != 1)  return 0;

    // if(AxisN->Prm->PnCustomPrm.AutoEnable == 1 || 
    //     (HumanRotCtrl.CovCtrlCmd.runCommand == MOTOR_CMD_RUN ))
    if(AxisN->Prm->PnCustomPrm.AutoEnable == 1)
    {
        if(AxisN->BaseCtrls->Cia402Axis.State == STATE_SWITCH_ON_DISABLED)
        {
            AxisN->BaseCtrls->Cia402Axis.Objects->Controlword0x6040 = 6;
        }
        else if(AxisN->BaseCtrls->Cia402Axis.State == STATE_READY_TO_SWITCH_ON)
        {
            AxisN->BaseCtrls->Cia402Axis.Objects->Controlword0x6040 = 7;       
        }
        else if(AxisN->BaseCtrls->Cia402Axis.State == STATE_SWITCHED_ON)
        {
            AxisN->BaseCtrls->Cia402Axis.Objects->Controlword0x6040 = 15;
        }
        else if(AxisN->BaseCtrls->Cia402Axis.State == STATE_OPERATION_ENABLED)
        {
            AxisN->BaseCtrls->Cia402Axis.Objects->Controlword0x6040 = 15;
        }
        if(AxisN->BaseCtrls->Cia402Axis.State == STATE_FAULT_REACTION_ACTIVE ||
           AxisN->BaseCtrls->Cia402Axis.State == STATE_FAULT)
        {
            AxisN->Prm->PnCustomPrm.AutoEnable = 0;
        }
    }
    else
    {
        AxisN->BaseCtrls->Cia402Axis.Objects->Controlword0x6040 = 6;
    }  
    
    return 1;
    
}


  /**
 * @brief Initialize the firmware update module
 *
 * @return Status_t Status code
 */
void firmware_process(void)
{

   if(s_update_state.var.update_in_progress == 0)
   {
        return;
   }

   uint32_t current_time = 0;    
   uint8_t Timeout = 0;
   uint8_t  ComErrTimes = 0 ;
   uint8_t  FlashErrTimes = 0;
   uint8_t  LastFrame = 0;
   uint32_t Saddr = 0;
   uint32_t flashdestination = 0;
   uint8_t UpgradeFlag = 0;
   uint8_t UpgradeMark = 0;
   
  uint32_t *CrcAddr = NULL;
  uint32_t cal_crc32 = 0;
  uint32_t bin_crc32 =  0;         

   UpgradeFlag = *(uint8_t *)UpgradeMark_ADDRESS_START;
   
   if(UpgradeFlag == 0x01) //
   {
     Saddr =    APPLICATION_ADDRESS1_START;
   }
   else
   {
     Saddr = APPLICATION_ADDRESS2_START;
   }
 
   flashdestination = Saddr;
   CANFD_Frame Txframe;
   /* Initialize update state */
   while(s_update_state.var.update_in_progress && s_update_state.var.status == FIRMWARE_STATUS_UPDATING)
   {
       switch(s_update_state.var.step)
       {
           case FIRMWARE_DATA_REQUEST:// 请求数据打包-并发送
                firmware_pack_data_request(&Txframe);  
                canfd_writedata_to_fifo(&Txframe);  
                s_update_state.var.last_request_time = Canfd_GetTimeMs();
                s_update_state.var.waiting_for_response = 1;
                s_update_state.var.step = FIRMWARE_DATA_RESPONSE;
               break;
           case FIRMWARE_DATA_RESPONSE:// 等待数据
               Timeout = Canfd_TimeoutCheck(s_update_state.conf.timeout_ms, s_update_state.var.last_request_time);
               if(s_update_state.var.waiting_for_response == 0)
               {
                    // 接收数据的个数
                    s_update_state.var.allrxdatenum  +=  s_update_state.var.chunk_size;
                    s_update_state.var.buffrxdatenum +=  s_update_state.var.chunk_size;

                    if(s_update_state.var.allrxdatenum > s_update_state.conf.firmware_size)  
                    {
                        s_update_state.var.status = FIRMWARE_STATUS_LENGTH_ERROR;
                        s_update_state.var.step = FIRMWARE_UPDATE_END;
                    }
                    else if(s_update_state.var.allrxdatenum == s_update_state.conf.firmware_size)
                    {
                        memcpy(&Canfd_Ota_Buffer[s_update_state.var.bufferIndx], &Canfd_Ota_RxBuffer[0], s_update_state.var.chunk_size);
                        s_update_state.var.step = FIRMWARE_WRITE_FLASH;  // 固件数据接收完毕，最后一次烧录
                        LastFrame = 1;
                        break;
                    }
                    else
                    {       
                        memcpy(&Canfd_Ota_Buffer[s_update_state.var.bufferIndx], &Canfd_Ota_RxBuffer[0], s_update_state.var.chunk_size);
                        s_update_state.var.current_offset += s_update_state.var.chunk_size;
                        s_update_state.var.bufferIndx += s_update_state.var.chunk_size;
                        s_update_state.var.packet_id++;
                        s_update_state.var.packet_id = s_update_state.var.packet_id & 0x03;

                        if(s_update_state.var.allrxdatenum  +  s_update_state.var.chunk_size > s_update_state.conf.firmware_size)    
                        {
                            s_update_state.var.chunk_size = s_update_state.conf.firmware_size - s_update_state.var.allrxdatenum;
                        }
                        else if(s_update_state.var.buffrxdatenum  +  s_update_state.var.chunk_size > CANFD_OTA_BUFFER_SIZE)
                        {
                            s_update_state.var.chunk_size = CANFD_OTA_BUFFER_SIZE - s_update_state.var.buffrxdatenum;
                        }
                        else
                        {
                            s_update_state.var.chunk_size = CANFD_OAT_MAX_RDATE;
                        }

                        if(s_update_state.var.buffrxdatenum >= CANFD_OTA_BUFFER_SIZE)
                        {
                            s_update_state.var.step = FIRMWARE_WRITE_FLASH; // 写入flash
                        }
                        else
                        {
                            s_update_state.var.step = FIRMWARE_DATA_REQUEST; // 请求数据
                        }                       
                    }
                    ComErrTimes = 0;
               }
               else if(Timeout == 1 && s_update_state.var.waiting_for_response == 1)
               {
                    ComErrTimes++; 
                    s_update_state.var.status = FIRMWARE_STATUS_TIMEOUT;
                   if(ComErrTimes >= s_update_state.conf.max_retries)
                   {
                      s_update_state.var.step = FIRMWARE_UPDATE_END;
                   }
                   s_update_state.var.step = FIRMWARE_DATA_REQUEST;
               }
               break;
           case FIRMWARE_WRITE_FLASH:// 写入flash
               s_update_state.var.buffrxdatenum = 0; // 写入成功后，置0   
               s_update_state.var.bufferIndx = 0;
                /* Write received data in Flash *///  2022/01/26_写flash这个地方还没修改
                if (FLASH_If_Write(flashdestination, (uint8_t*) Canfd_Ota_Buffer, CANFD_OTA_BUFFER_SIZE) == FLASHIF_OK)
                {
                    if(LastFrame == 1)
                    {
                        s_update_state.var.step = FIRMWARE_CHECKSUM;  // 整个bin文件烧录完成
                    }
                    else
                    {
                        flashdestination += CANFD_OTA_BUFFER_SIZE;
                        s_update_state.var.step = FIRMWARE_DATA_REQUEST;
                    }
                    FlashErrTimes = 0;
                }
                else /* An error occurred while writing to Flash memory */
                {
                    FlashErrTimes++;
                    s_update_state.var.status = FIRMWARE_STATUS_TIMEOUT;
                    if(FlashErrTimes >= s_update_state.conf.max_retries)
                    {
                        s_update_state.var.step = FIRMWARE_UPDATE_END;
                    }
                    s_update_state.var.step = FIRMWARE_WRITE_FLASH;
                }
               break;
           case FIRMWARE_CHECKSUM:// crc 校验
                CrcAddr = (uint32_t*)(Saddr + s_update_state.conf.firmware_size - 4);
                cal_crc32 = calc_crc32((uint8_t *)Saddr, s_update_state.conf.firmware_size-4);
                bin_crc32 =  *CrcAddr;           

                if(cal_crc32 == bin_crc32)
                {
                    if(UpgradeFlag != 1)
                    {
                        UpgradeMark = 0x01; 
                        FLASH_If_Write(UpgradeMark_ADDRESS_START, &UpgradeMark ,1);       
                    }
                    else if(UpgradeFlag == 1)
                    {
                        UpgradeMark = 0xFF; 
                        FLASH_If_Write(UpgradeMark_ADDRESS_START, &UpgradeMark ,1);    		//Erase the sector of flash upgrade flag      
                    }   
                    s_update_state.var.status = FIRMWARE_STATUS_SUCCESS;
                    s_update_state.var.step = FIRMWARE_UPDATE_END;
                }
                else
                {
                    s_update_state.var.status = FIRMWARE_STATUS_CRC_ERROR;
                    s_update_state.var.step = FIRMWARE_UPDATE_END;
                }
                break;
           case FIRMWARE_UPDATE_END:// 更新完成，失败 or 成功
                firmware_status_response(&Txframe);
                canfd_writedata_to_fifo(&Txframe);
                current_time = Canfd_GetTimeMs();
                while( Canfd_GetTimeMs() - current_time < 100)  //wait 100 ms  and then reset
                {}
                hApi_SystemReset();
                break;  
           default:
               break;
       }
   }
}