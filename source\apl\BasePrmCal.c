/****************************************************************************************************
 *
 * FILE NAME:  BasePrmCal.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.16
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	16-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseLoops.h"
#include "BasePrmCal.h"
#include "MotorIdentification.h"
#include "MotorPoleFind.h"
#include "PnPrmStruct.h"
#include "Mlib.h"
#include "math.h"
#include "fundefine.h"

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BprmMotEncParamCal(BPRMDAT *Bprm, ENCODER *EncV, ENCODER *EncV2, MOTOR_IDENTIFY *MotIdt, PRMDATA *PnPrm)
{
	REAL32    fw;
	UINT16    Imax;
	UINT32    wk=0;
    INT32     temp = 0;
	INT16	   temp1 = 0;    
   
	Bprm->Vdc = UDC_LEVEL;
	//if motor max current <= device max current
	if( PnPrm->PnMotPrm.MaxCur <= PnPrm->PnCfgPrm.DevMaxCur)
	{
		Imax = PnPrm->PnMotPrm.MaxCur;
	}
	else
	{
		Imax = PnPrm->PnCfgPrm.DevMaxCur;
	}

	Bprm->MaxCur = (REAL32)Imax*C_FSqrt2 / 10.0f;
	Bprm->RatCur = (REAL32)PnPrm->PnMotPrm.RatCur*C_FSqrt2/10.0f;

#if 0
	if(Bprm->MaxCur > 3.0*Bprm->RatCur)
	{
		Bprm->MaxCur = 3.0*Bprm->RatCur;
	}

	if(Bprm->MaxCur > MAX_SMP_CUR)
	{
		Bprm->MaxCur = MAX_SMP_CUR;
	}
#endif

	Bprm->MaxTrq =  (REAL32)PnPrm->PnMotPrm.MaxTrq/100.0f;    // Nm or N 
	Bprm->RatTrq =  (REAL32)PnPrm->PnMotPrm.RatTrq/100.0f;

    if(PnPrm->PnMotPrm.MotType == MOTTYPE_LINEAR)
    {
        Bprm->MaxSpd = (REAL32)PnPrm->PnMotPrm.MaxSpd*0.001;  //  Bprm->MaxSpd[m/s]  PnPrm->PnMotPrm.MaxSpd[mm/s]  MotorPitch[mm] 
        Bprm->RatSpd = (REAL32)PnPrm->PnMotPrm.RatSpd*0.001;  //  Bprm->RatSpd[m/s]  PnPrm->PnMotPrm.RatSpd[mm/s]  MotorPitch[mm]       
    }
    else if(PnPrm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)
    {
        Bprm->MaxSpd = (REAL32)PnPrm->PnMotPrm.MaxSpd*0.001;  //  Bprm->MaxSpd[m/s]  PnPrm->PnMotPrm.MaxSpd[mm/s]  MotorPitch[mm] 
        Bprm->RatSpd = (REAL32)PnPrm->PnMotPrm.RatSpd*0.001;  //  Bprm->RatSpd[m/s]  PnPrm->PnMotPrm.RatSpd[mm/s]  MotorPitch[mm]      

    }
    else
    {
        Bprm->MaxSpd = (REAL32)PnPrm->PnMotPrm.MaxSpd*C2PAI/60.0f;   // MaxSpd[rpm]
        Bprm->RatSpd = (REAL32)PnPrm->PnMotPrm.RatSpd*C2PAI/60.0f;
    }
	
    if(PnPrm->PnMotPrm.MotType == MOTTYPE_LINEAR)
    {
//        Bprm->MotEmf = (REAL32)PnPrm->PnMotPrm.MotEmf *100.0f*C_FSqrt3Inv/ (REAL32)C10POW7;                   // Ke/sqrt(3)
//        Bprm->MotEmf = C_FSqrt2*Bprm->MotEmf/C2PAI*((REAL32)PnPrm->PnMotPrm.MotorPitch/1000.0f);              // Vpk/rad/s
//        Bprm->Jmot = (REAL32)PnPrm->PnMotPrm.Jmot/100.0f;                                                     // kg
      
        Bprm->MotEmf = (REAL32)PnPrm->PnMotPrm.MotEmf*C_FSqrt3Inv /100.0f;                   //PnPrm->PnMotPrm.MotEmf[ 0.01mV/m/s]
        Bprm->Jmot = (REAL32)PnPrm->PnMotPrm.Jmot/100.0f;           
    }
        else if(PnPrm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)
    {
//        Bprm->MotEmf = (REAL32)PnPrm->PnMotPrm.MotEmf *100.0f*C_FSqrt3Inv/ (REAL32)C10POW7;                   // Ke/sqrt(3)
//        Bprm->MotEmf = C_FSqrt2*Bprm->MotEmf/C2PAI*((REAL32)PnPrm->PnMotPrm.MotorPitch/1000.0f);              // Vpk/rad/s
//        Bprm->Jmot = (REAL32)PnPrm->PnMotPrm.Jmot/100.0f;                                                     // kg
      
        Bprm->MotEmf = (REAL32)PnPrm->PnMotPrm.MotEmf*C_FSqrt3Inv /100.0f/(REAL32)C10POW3;                   //PnPrm->PnMotPrm.MotEmf[ 0.01mV/m/s]
        Bprm->Jmot = (REAL32)PnPrm->PnMotPrm.Jmot/100.0f;  


    }
    else
    {
        Bprm->MotEmf = (REAL32)PnPrm->PnMotPrm.MotEmf *60.0f*C_FSqrt3Inv/ (REAL32)C10POW5;  // Ke/sqrt(3)
        Bprm->MotEmf = C_FSqrt2*Bprm->MotEmf/C2PAI;              // Vpk/rad/s
        Bprm->Jmot = (REAL32)PnPrm->PnMotPrm.Jmot/1000000.0f;
    }     

    if((PnPrm->PnMotPrm.EncConfig & 0xFF)  != 0)
    {
        EncV->P.EncUsersftBitS =  PnPrm->PnMotPrm.EncConfig&0xFF;
        if(EncV->P.EncUsersftBitS < PnPrm->PnMotPrm.AbsEncBitS)
        {
            Bprm->EncBitNum  = EncV->P.EncUsersftBitS;
            EncV->P.EncUserBitSwNum =   PnPrm->PnMotPrm.AbsEncBitS - EncV->P.EncUsersftBitS;
        }
        else
        {
            Bprm->EncBitNum = PnPrm->PnMotPrm.AbsEncBitS;
            EncV->P.EncUserBitSwNum = 0;
        }
        
    }
    else
    {
        Bprm->EncBitNum = PnPrm->PnMotPrm.AbsEncBitS;
        EncV->P.EncUserBitSwNum = 0;
    } 
    
    if((PnPrm->PnMotPrm.EncConfig2 & 0xFF)  != 0)
    {
        EncV2->P.EncUsersftBitS =  PnPrm->PnMotPrm.EncConfig2&0xFF;
        if(EncV2->P.EncUsersftBitS < PnPrm->PnMotPrm.AbsEncBitS2)
        
        {
            Bprm->Enc2BitNum  = EncV2->P.EncUsersftBitS;
            EncV2->P.EncUserBitSwNum =   PnPrm->PnMotPrm.AbsEncBitS2 - EncV2->P.EncUsersftBitS;
        }
        else
        {
            Bprm->Enc2BitNum = PnPrm->PnMotPrm.AbsEncBitS2;
            EncV2->P.EncUserBitSwNum = 0;
        }
        
    }
    else
    {
        Bprm->Enc2BitNum = PnPrm->PnMotPrm.AbsEncBitS2;
        EncV2->P.EncUserBitSwNum = 0;
    }     
    
     
	Bprm->MotPolePairNum = PnPrm->PnMotPrm.MotPolePairs; 
		
	Bprm->MotR = (REAL32)PnPrm->PnMotPrm.MotR/2000.0f;        // Ra = Rab/2
	Bprm->MotLq = (REAL32)PnPrm->PnMotPrm.MotLq/100000.0f;     // La = Lab/2
    Bprm->MotLd = (REAL32)PnPrm->PnMotPrm.MotLd/100000.0f;     // La = Lab/2

    EncV->P.EncType = PnPrm->PnMotPrm.EncType;
    EncV2->P.EncType = PnPrm->PnMotPrm.EncType2;
    EncV->P.PolePairs = PnPrm->PnMotPrm.MotPolePairs;
    
    

    
    if(EncV->P.EncType == ENC_TYPE_INCREMENT)
    {
        EncV->P.EncPPR = PnPrm->PnMotPrm.AbzEncPPR;
        EncV->P.EncSftBitNum = 16;
        EncV->P.MechAngleOffset = 0;
        EncV->P.PhaseOffset = 0;
        
        if(PnPrm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)
        {
          EncV->V.PhaseReady = TRUE;
        }
    }
    else if(PnPrm->PnMotPrm.MotType == MOTTYPE_LINEAR)
    {
        EncV->P.EncPPR = PnPrm->PnMotPrm.AbzEncPPR;
        EncV->P.EncBitS = PnPrm->PnMotPrm.AbsEncBitS;
        EncV->P.EncSftBitNum = 32 - PnPrm->PnMotPrm.AbsEncBitS;   
		EncV->V.PhaseReady = TRUE;
    }
    else if(PnPrm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)
    {
        EncV->P.EncPPR = PnPrm->PnMotPrm.AbzEncPPR;
        EncV->P.EncBitS = PnPrm->PnMotPrm.AbsEncBitS;
        EncV->P.EncSftBitNum = 32 - PnPrm->PnMotPrm.AbsEncBitS;   
        EncV->V.PhaseReady = TRUE;
    }    
    else
    {
        EncV->P.EncPPR = 1L << Bprm->EncBitNum;
	    EncV->P.EncBitS = Bprm->EncBitNum;
        EncV->P.EncSftBitNum = 32 - Bprm->EncBitNum;   
	    EncV->V.PhaseReady = TRUE;
    }

    if(EncV2->P.EncType == ENC_TYPE_INCREMENT)
    {
        EncV2->P.EncPPR = PnPrm->PnMotPrm.AbzEncPPR2;
        EncV2->P.EncSftBitNum = 16;
        EncV2->P.MechAngleOffset = 0;
        EncV2->P.PhaseOffset = 0;
    }
    else if(PnPrm->PnMotPrm.MotType == MOTTYPE_LINEAR)
    {
        EncV2->P.EncPPR = PnPrm->PnMotPrm.AbzEncPPR2;
        EncV2->P.EncBitS = PnPrm->PnMotPrm.AbsEncBitS2;
        EncV2->P.EncSftBitNum = 32 - PnPrm->PnMotPrm.AbsEncBitS2;   
        EncV2->V.PhaseReady = TRUE;
    }
	 else if(PnPrm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)
    {
        EncV2->P.EncPPR = PnPrm->PnMotPrm.AbzEncPPR2;
        EncV2->P.EncBitS = PnPrm->PnMotPrm.AbsEncBitS2;
        EncV2->P.EncSftBitNum = 32 - PnPrm->PnMotPrm.AbsEncBitS2;   
        EncV2->V.PhaseReady = TRUE;
    }      
    else
    {
        EncV2->P.EncPPR = 1L << Bprm->Enc2BitNum ;
        EncV2->P.EncBitS = Bprm->Enc2BitNum ;
        EncV2->P.EncSftBitNum = 32 - Bprm->Enc2BitNum ;   
	    EncV2->V.PhaseReady = TRUE;
    }
    EncV->P.EncPolePairs = (REAL32)EncV->P.EncPPR/(REAL32)EncV->P.PolePairs;
    
    if(EncV->P.EncType != ENC_TYPE_INCREMENT)
    {
        EncV->P.EncBitM = PnPrm->PnMotPrm.AbsEncBitM;  
        EncV->P.MechAngleOffset = PnPrm->PnMotPrm.AbsEncOffset;
    }
    
    if(EncV2->P.EncType != ENC_TYPE_INCREMENT)
    {
        EncV2->P.EncBitM = PnPrm->PnMotPrm.AbsEncBitM2;  
    }
     EncV->P.DataLen = PnPrm->PnMotPrm.BisscDLen;
     EncV->P.AlignLen = PnPrm->PnMotPrm.BisscDLen - PnPrm->PnMotPrm.AbsEncBitM - PnPrm->PnMotPrm.AbsEncBitS;
     EncV2->P.DataLen = PnPrm->PnMotPrm.BisscDLen2;
     EncV2->P.AlignLen = PnPrm->PnMotPrm.BisscDLen2 - PnPrm->PnMotPrm.AbsEncBitM2 - PnPrm->PnMotPrm.AbsEncBitS2;
    
    
	if(PnPrm->PnCfgPrm.ResDir)
	{
		Bprm->RvsDir = TRUE;
		Bprm->DirSign = -1;
	}
	else
	{
		Bprm->RvsDir = FALSE;
		Bprm->DirSign = 1;
	}

    if(PnPrm->PnMotPrm.MotType == MOTTYPE_LINEAR)
    {
        Bprm->FbPulse  = PnPrm->PnMotPrm.AbzEncPPR;                                                                           // cnt 
        Bprm->FbPulse1 = PnPrm->PnMotPrm.AbzEncPPR2;
        Bprm->Kmotpls  = (REAL32)PnPrm->PnMotPrm.MotorPitch*0.001/(REAL32)Bprm->FbPulse;                                     // m/cnt
        Bprm->Kmotpls1 = (REAL32)PnPrm->PnMotPrm.MotorPitch*0.001/(REAL32)Bprm->FbPulse1;
        Bprm->SpdDetectUnit = (REAL32)KPI_TBCOUNTMS * (REAL32)PnPrm->PnMotPrm.MotorPitch / (REAL32)Bprm->FbPulse *10.0f;       
    }
    else if(PnPrm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)
    {
        Bprm->FbPulse = PnPrm->PnMotPrm.AbzEncPPR;                                                         // cnt 
        Bprm->FbPulse1 = PnPrm->PnMotPrm.AbzEncPPR2;
        Bprm->Kmotpls = (REAL32)PnPrm->PnMotPrm.MotorPitch*0.001/(REAL32)Bprm->FbPulse;                    // m/cnt 
        Bprm->Kmotpls1 = (REAL32)PnPrm->PnMotPrm.MotorPitch*0.001/(REAL32)Bprm->FbPulse1;
        Bprm->SpdDetectUnit = (REAL32)KPI_TBCOUNTMS * (REAL32)PnPrm->PnMotPrm.MotorPitch / (REAL32)Bprm->FbPulse *10.0f;     
    }
    else
    {
        Bprm->FbPulse = EncV->P.EncPPR;
        Bprm->FbPulse1 = EncV2->P.EncPPR;
        Bprm->Kmotpls = C2PAI/ (REAL32)Bprm->FbPulse;
        Bprm->Kmotpls1 = C2PAI/ (REAL32)Bprm->FbPulse1;
        Bprm->SpdDetectUnit = (REAL32)KPI_TBCOUNTMS * 600000.0F / (REAL32)Bprm->FbPulse;
    }

	Bprm->PerOvrSpd = 1000;
	Bprm->NorOvrSpd = 0x01000000;
	Bprm->NorMaxSpd = MlibScalKxgain( Bprm->NorOvrSpd, 10000, (Bprm->PerOvrSpd+10000), NULL, -24 );
	Bprm->NorRatSpd = MlibScalKxgain( Bprm->NorMaxSpd, PnPrm->PnMotPrm.RatSpd, PnPrm->PnMotPrm.MaxSpd, NULL, -24 );	
	Bprm->NorSuspSpd = MlibScalKxgain( Bprm->NorMaxSpd, 1, 10, NULL, -24 );


    if(PnPrm->PnMotPrm.MotType == MOTTYPE_LINEAR)
    {
        Bprm->KMotSpdConv = MlibScalKxgain( Bprm->FbPulse, 1, PnPrm->PnMotPrm.MotorPitch*10*1000, NULL, -1 );
    }
    else if(PnPrm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)
    {
        Bprm->KMotSpdConv = MlibScalKxgain( Bprm->FbPulse, 1, PnPrm->PnMotPrm.MotorPitch*10*1000, NULL, -1 );
    }
    else
    {
        Bprm->KMotSpdConv = MlibScalKxgain( Bprm->FbPulse, 1, 60*10*1000, NULL, -1 );   // 0.1r/min 
    }

/*--------------------------------------------------------------------------------------------------*/
/*		Calculation of maximum speed [rad/s]														*/
/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*				     MaxSpd * (10000+PerOvrSpd)						MaxSpd    : [rad/s] 			*/
/*		OvrSpd = ------------------------------------				PerOvrSpd : [0.01%]				*/
/*				           10000																	*/
/*																									*/
/*--------------------------------------------------------------------------------------------------*/
	Bprm->OvrSpd = (REAL32)Bprm->MaxSpd * (REAL32)(Bprm->PerOvrSpd + 10000) / 10000.0F;

/*-------------------------------------------------------------------------------------------------*/
/*		speed factor:[pulse/sec] --> [2^24/OvrSpd]				                                   */
/*-------------------------------------------------------------------------------------------------*/
/*																								   */
/*				    Kmotpls * 2^24 					Kmotpls :	[rad/pulse]		[m/pulse]		   */
/*		Kmotspd = ------------------				OvrSpd  :	[rad/s]			[m/s]			   */
/*				        OvrSpd																	   */
/*																								   */
/*-------------------------------------------------------------------------------------------------*/

	Bprm->Kmotspd = (REAL32)0x1000000 * Bprm->Kmotpls / Bprm->MaxSpd;
	Bprm->KmotspdF =  (Bprm->Kmotspd * 1000000.0F / (REAL32)PS_CYCLEUS);
	Bprm->KmotVel = (REAL32)0x1000000 / Bprm->MaxSpd;

    if(PnPrm->PnMotPrm.MotType == MOTTYPE_LINEAR)
    {
          Bprm->Kspdrpm =  Bprm->KmotVel*0.001;  // mm/s -> 2^24
    }
    else if(PnPrm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)
    {
          Bprm->Kspdrpm =  Bprm->KmotVel*0.001;  // mm/s -> 2^24
    }
    else
    {
         Bprm->Kspdrpm =  Bprm->Kmotspd *(REAL32)Bprm->FbPulse/60.0f;
    }


	Bprm->KmotspdA = (Bprm->Kmotspd * 1000000000.0F / TASKA_CYCLENS);
	Bprm->KmotspdB = (Bprm->Kmotspd * 1000000.0F / TASKB_CYCLEUS);
	Bprm->KmotspdC = (Bprm->Kmotspd * 1000000.0F / TASKC_CYCLEUS);

/*-------------------------------------------------------------------------------------------------*/
/*		torque factor: [0.1% --> 2^24/MaxTrq]												   */
/*-------------------------------------------------------------------------------------------------*/
/*																								   */
/*					RatedTrq * 2^24					RatedTrq :	[Nm] 		                       */
/*		Kmottrq = -------------------				MaxTrq	:	[Nm] 				               */
/*				     1000 * MaxTrq															       */
/*																								   */
/*-------------------------------------------------------------------------------------------------*/
	fw = (REAL32)0x1000000*Bprm->RatTrq/Bprm->MaxTrq;
	Bprm->Kmottrq = fw/1000.0f;
    Bprm->Ktrqnm = 1000.0f*Bprm->MaxTrq/(REAL32)0x1000000;
	Bprm->PerMaxTrq = 100.0f*Bprm->MaxCur/Bprm->RatCur;
    Bprm->Ktrqper   = 1000.0f/Bprm->RatTrq;
/*-------------------------------------------------------------------------------------------------*/
/*		position control gain factor				                                               */
/*-------------------------------------------------------------------------------------------------*/
/*																								   */
/*				Kmotpls * 2^24 						Kfbpls :	[rad/pulse]		[m/pulse]		   */
/*		Kpx = -----------------						MaxSpd :	[rad/s]			[m/s]			   */
/*				   MaxSpd																		   */
/*																								   */
/*-------------------------------------------------------------------------------------------------*/
    if(PnPrm->PnMotPrm.UseEncoder2)
    {
        Bprm->Kpx = (REAL32)0x1000000 * Bprm->Kmotpls1 / Bprm->MaxSpd; 
    }
    else
    {
        Bprm->Kpx = (REAL32)0x1000000 * Bprm->Kmotpls / Bprm->MaxSpd; 
    }
	

/*-------------------------------------------------------------------------------------------------*/
/*		speed control gain factor				                                    			   */
/*-------------------------------------------------------------------------------------------------*/
/*																						 		   */
/*				2*PAI * MaxSpd * Jmot				MaxSpd :	[rad/s] 		[m/s]			   */
/*		Kvx = ------------------------- 			Jmot   :	[kg*m^2]		[kg]			   */
/*					  MaxTrq						MaxTrq :	[Nm]			[N] 			   */
/*																								   */
/*-------------------------------------------------------------------------------------------------*/  
       
        fw = Bprm->MaxSpd * C2PAI;
        
        if(PnPrm->PnMotPrm.MotType == 1 || PnPrm->PnMotPrm.MotType == 2)
        {
//          Bprm->Kvx = fw * Bprm->Jmot / Bprm->MaxTrq
//              * (REAL32)PnPrm->PnMotPrm.MotorPitch /1000.0f/ C2PAI
//                 * (REAL32)PnPrm->PnMotPrm.MotorPitch /1000.0f/ C2PAI;
          Bprm->Kvx = fw * Bprm->Jmot / Bprm->MaxTrq;          
        }
        else
        {
          
          Bprm->Kvx = fw * Bprm->Jmot / Bprm->MaxTrq;
        }
	
/*--------------------------------------------------------------------------------------------------*/
/*		Calculation of position-speed gain intermediate parameter	Rotary			Linear			*/
/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*						  Jmot * 2^24				Kmotpls :	[rad/pulse] 	[m/pulse]			*/
/*		Kpvx = Kmotpls * --------------- 			Jmot   :	[kg*m^2]		[kg]				*/
/*							MaxTrq					MaxTrq :	[Nm]			[N] 				*/
/*																									*/
/*--------------------------------------------------------------------------------------------------*/
        if(PnPrm->PnMotPrm.UseEncoder2)
        {
            fw = Bprm->Kmotpls1 * (REAL32)0x1000000;
        }
        else    
        {
            fw = Bprm->Kmotpls * (REAL32)0x1000000;
        }
        
        if(PnPrm->PnMotPrm.MotType == 1 || PnPrm->PnMotPrm.MotType == 2)
        {
//          Bprm->Kpvx = fw * Bprm->Jmot / Bprm->MaxTrq
//                  * (REAL32)PnPrm->PnMotPrm.MotorPitch /1000.0f/ C2PAI
//                       * (REAL32)PnPrm->PnMotPrm.MotorPitch /1000.0f/ C2PAI;
            Bprm->Kpvx = fw * Bprm->Jmot / Bprm->MaxTrq;          
        }
        else
        {
            Bprm->Kpvx = fw * Bprm->Jmot / Bprm->MaxTrq;
        }

/*--------------------------------------------------------------------------------------------------*/
/*		Overpulse speed calculation  [pulse/sec]													*/
/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*					OvrSpd [rad/sec]																*/
/*		OvrPspd = ----------------------															*/
/*					Kmotpls [rad/pulse]																*/
/*																									*/
/*--------------------------------------------------------------------------------------------------*/
	Bprm->OvrPspd = Bprm->OvrSpd / Bprm->Kmotpls;
	Bprm->MaxPspd = Bprm->OvrPspd * (REAL32)Bprm->NorMaxSpd / (REAL32)Bprm->NorOvrSpd;

    BprmMotIdentifyPrmCal(Bprm, MotIdt, PnPrm);

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BprmMotIdentifyPrmCal(BPRMDAT *Bprm, MOTOR_IDENTIFY *MotIdt, PRMDATA *PnPrm)
{
    MotIdt->FhaseFind.P.FFMethod = PnPrm->PnCtrlCfgPrm.FFMethod;
    if(PnPrm->PnCtrlCfgPrm.FFCur == 0)
    {
      MotIdt->FhaseFind.P.FFCur       = (REAL32)PnPrm->PnMotPrm.RatCur*1.414/10.0f;
      MotIdt->MotorParamIdent.P.FPLCurRef = (REAL32)PnPrm->PnMotPrm.RatCur*1.414/10.0f;
    }
    else
    {
      MotIdt->FhaseFind.P.FFCur       = (REAL32)PnPrm->PnCtrlCfgPrm.FFCur*1.414/10.0f;
      MotIdt->MotorParamIdent.P.FPLCurRef = (REAL32)PnPrm->PnCtrlCfgPrm.FFCur*1.414/10.0f;
    }
    MotIdt->FhaseFind.P.FFVolt = MotIdt->FhaseFind.P.FFCur * Bprm->MotR/UDC_LEVEL; 
    MotIdt->FhaseFind.P.FFStableTime = PnPrm->PnCtrlCfgPrm.FFStableTime*1000.0f/(REAL32)TASKC_CYCLEUS;
    
    REAL32 RampDelta = PnPrm->PnCtrlCfgPrm.FFRampTime *1000.0f/(REAL32)TASKC_CYCLEUS;
    MotIdt->FhaseFind.P.FFRampDelta = MotIdt->FhaseFind.P.FFVolt/(REAL32)RampDelta;

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BprmUnitTransFacterCal( BPRMDAT *Bprm, UINT32 Gear_b, UINT32 Gear_a )
{


}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
#define MIN_CUR_LOOP_TI 10  //  [0.01ms]
PUBLIC void PcalBaseCurCtrl( BASE_LOOP *BaseLoops ,UINT16 *pCurgn, UINT16 *pTi)
{
	  REAL32  fw;
	  REAL32  Ts=0.001;
          REAL32 Curgn = (REAL32)*pCurgn;
          REAL32 Ti = (REAL32)*pTi;
/*-------------------------------------------------------------------------------------------------*/
/*		current control gain 											        				   */
/*-------------------------------------------------------------------------------------------------*/
/*																								   */
/*										                             		                       */
/*		Cp = 2PAI* Curgn* L/ Kpwm,	Ci = (Cp*R/L)*Ts*2^2		Kpwm = sqrt(3)*Udc/3               */
/*							            						                                   */
/*																								   */
/*-------------------------------------------------------------------------------------------------*/
	fw = C_FSqrt3Inv * UDC_LEVEL;  
	BaseLoops->CurLoop.P.KpQ = C2PAI * BaseLoops->Bprm->MotLq* (REAL32)Curgn/fw ;
	
    if(BaseLoops->CurTiType == 1)
    {
      Ts = Ti;
      
    }
	else
    {
	Ts = 100000.0f*BaseLoops->Bprm->MotLq/BaseLoops->Bprm->MotR; // 0.01ms
	if(Ts<MIN_CUR_LOOP_TI)
		Ts = MIN_CUR_LOOP_TI;
        *pTi = Ts;
    }
    BaseLoops->CurLoop.P.KiQ = BaseLoops->CurLoop.P.KpQ * CUR_CYCLENS/((REAL32)Ts*10000.0f);

	BaseLoops->CurLoop.P.KpD = C2PAI * BaseLoops->Bprm->MotLd* (REAL32)Curgn/fw ;
    if(BaseLoops->CurTiType == 1)
    {
      Ts = Ti;
      
    }
    else
    {
	Ts = 100000.0f*BaseLoops->Bprm->MotLd/BaseLoops->Bprm->MotR; // 0.01ms
	if(Ts<MIN_CUR_LOOP_TI)
		Ts = MIN_CUR_LOOP_TI;
    }
    BaseLoops->CurLoop.P.KiD = BaseLoops->CurLoop.P.KpD * CUR_CYCLENS/((REAL32)Ts*10000.0f);
	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcalBaseWfCtrl( BASE_LOOP *BaseLoops ,PRMDATA *PnPrm)
{
    REAL32  fw1,fw2;

    fw1 = PnPrm->PnMotPrm.MaxSpd * BaseLoops->Bprm->Kspdrpm;
    fw2 = PnPrm->PnMotPrm.RatSpd * BaseLoops->Bprm->Kspdrpm;
    
    BaseLoops->CurLoop.P.WfV1Rat = fw2;
    if(fw1 <= fw2 )
    {
        BaseLoops->CurLoop.P.KwfId = 0;
    }
    else
    {        
        BaseLoops->CurLoop.P.KwfId = (REAL32)PnPrm->PnAdvPrm.WfIdSet * 0.001f /(fw1 - fw2 );
        BaseLoops->CurLoop.P.KwfId *= BaseLoops->Bprm->RatCur;
    }   
    

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcalBaseSpdCtrl( BASE_LOOP *BaseLoops, PRMDATA *PnPrm, UINT32 GselNo )
{
	GSELGAINS			*GselGains;
	TUNELESS_PRM		*TunLessPrm;
	CTRL_CMD_PRM		*CtrlCmdPrm;
	BASE_CTRL           *BaseCtrls;
	REAL32				Jrat;
	REAL32				Loophz;
	REAL32				Pitime;
	
	BaseCtrls = BaseLoops->BaseCtrls;
	CtrlCmdPrm	= &(BaseCtrls->CtrlCmdPrm);
	GselGains	= &(BaseCtrls->GainChange.GselGains[GselNo]);
	Jrat		= PnPrm->PnCtrlCfgPrm.Jrat;

	if( GselNo == 1 )
	{ 
		Loophz = (PnPrm->PnCtrlCfgPrm.SpdLoopHz);
		Pitime = (PnPrm->PnCtrlCfgPrm.SpdLoopTi);
	}
	else if( GselNo == 2 )
	{ 
		Loophz = PnPrm->PnCtrlCfgPrm.SpdLoopHz2;
		Pitime = PnPrm->PnCtrlCfgPrm.SpdLoopTi2;
	}
	else if( GselNo == 5 )
	{ 
		TunLessPrm = &(BaseCtrls->TuneLessCtrl.TuneLessPrm);
		Loophz = TunLessPrm->kv2;
		Pitime = TunLessPrm->Ti;
		Jrat = TunLessPrm->jrate;
	}
	else
	{ 
		Loophz = (PnPrm->PnCtrlCfgPrm.SpdLoopHz);
		Pitime = (PnPrm->PnCtrlCfgPrm.SpdLoopTi);
	}

	if(CtrlCmdPrm->IPSpdControl)
	{
		CtrlCmdPrm->PI_rate = PnPrm->PnCtrlCfgPrm.SpdKpdff/100.0f;

		GselGains->Kv = BaseLoops->Bprm->Kvx * (( 100.0f + Jrat ) * Loophz ) / 1000.0f;
		GselGains->Kvi = GselGains->Kv * (float)PS_CYCLEUS / ( 10.0f * Pitime);
	}
	else
	{
		CtrlCmdPrm->PI_rate = 1.0f;

		GselGains->Kv = BaseLoops->Bprm->Kvx * (( 100.0f + Jrat ) * Loophz ) / 1000.0f;
		GselGains->Kvi = GselGains->Kv * (float)PS_CYCLEUS / ( 10.0f * Pitime);
	}


}

 /****************************************************************************************************
  * DESCRIPTION:
  *
  * RETURNS:
  *
 ****************************************************************************************************/
PUBLIC void PcalSpeedLimitGain( BASE_CTRL *BaseCtrls, BPRMDAT *Bprm, PRMDATA *PnPrm, INT32 GselNo )
{
    UINT32			 SpdLimGain;
	INT32			 SpdLimIntegGain;
	UINT16			 Loophz;
	REAL32			 Jrat;
	CTRL_CMD_PRM	 *CtrlCmdPrm;

	CtrlCmdPrm  = &BaseCtrls->CtrlCmdPrm;
	Loophz = (PnPrm->PnCtrlCfgPrm.SpdLoopHz);
	Jrat = PnPrm->PnCtrlCfgPrm.Jrat;

	if( Loophz < 100 )
	{
		SpdLimGain 	 = 100;
		SpdLimIntegGain = 32000;
	}
	else if( Loophz < 200 )
	{
		SpdLimGain 	 = Loophz;
		SpdLimIntegGain = 3200000 / Loophz;
	}
	else
	{
		SpdLimGain 	 = Loophz;
		SpdLimIntegGain = 16000;
	}

	CtrlCmdPrm->OverTrqLoopGain[GselNo] = 
		 Bprm->Kvx * (REAL32)(( 100 + Jrat ) * SpdLimGain ) / 1000.0f;
	CtrlCmdPrm->OverTrqIntegGain[GselNo] = 
		 CtrlCmdPrm->OverTrqLoopGain[GselNo] * (REAL32)PS_CYCLEUS / (REAL32)SpdLimIntegGain;


}
														 
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcalBasePosPCtrll( BASE_LOOP  *BaseLoops, UINT32 Posgn, UINT32 GselNo )
{
	GSELGAINS			*GselGains;
	BASE_CTRL			*BaseCtrls;

	BaseCtrls = BaseLoops->BaseCtrls;

	GselGains = &(BaseCtrls->GainChange.GselGains[GselNo]);

	GselGains->Kp = BaseLoops->Bprm->Kpx * Posgn / 10.0F;

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 PcalSpdFFGain( REAL32 Kpx, INT32 ffgn, INT32 ScanTime )
{

	REAL32	KdPff;
	REAL32	fw;
	fw = Kpx * 1000000.0F / (REAL32)ScanTime;
	KdPff = fw * (REAL32)ffgn / 100.0F;

	return	KdPff;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 PcalTrqFFGain( REAL32 Kvx, PRMDATA *PnPrm, INT32 ScanTime )
{
	REAL32	KdPff;
	REAL32	fw;
    REAL32  KTffx;
    
   KTffx = Kvx*(PnPrm->PnCtrlCfgPrm.Jrat+100)/100;
   KTffx = KTffx/C2PAI;
   fw = KTffx * 1000000.0F / (REAL32)ScanTime;
   KdPff = fw * (REAL32)PnPrm->PnCtrlCfgPrm.TrqffGain / 100.0F;

   return	KdPff;
}

/****************************************************************************************************
* DESCRIPTION:
*         Torque filter: Low-pass filter parameter calculation 
*         Only the first torque filter can be used as the torque filter in the torque control mode.
* RETURNS:
*
****************************************************************************************************/
PUBLIC void PcalBaseTrqLpassFilter( TRQFIL *TrqFilData, BASE_CTRL *BaseCtrls, 
													  INT32 trqfil11, INT32 trqfil12, INT32 GselNo )
{
  REAL32		  cyc_time, tmp;
  GSELGAINS 	  *GselGains;

  GselGains = &(BaseCtrls->GainChange.GselGains[GselNo]);

  cyc_time = TASKB_CYCLEUS;

  tmp = (REAL32)trqfil11 * 10.0f;                      // 0.01ms -> us
  TrqFilData->A.Klpf = cyc_time/(tmp + cyc_time);      // Kf = ts/(tx+ts)
 
  tmp = (REAL32)trqfil12 * 10.0f;                      // 0.01ms -> us
  GselGains->Klpf = cyc_time/(tmp + cyc_time);         // Kf = ts/(tx+ts)

}


/****************************************************************************************************
* DESCRIPTION:
* 	  Second-order low-pass filter parameter calculation (bilinear transformation)
* 	  hz: Filter frequency[Hz] 
* 	  qx:Quality factor 
* 	  ts:Calculation cycle[sec]  
* 	  kn:Calculation result storage 
* RETURNS:
*
****************************************************************************************************/
PRIVATE void f_PcalLowfilter2(REAL32 hz, REAL32 qx, REAL32 ts, REAL32 kn[4] ) 											  
{
	REAL32   ka,kb,kc;
	REAL32   kx,ky;

//  if ( hz > 2000 )
//  {
//	  hz = ( hz * (3.0f * hz - 1894.0f) + 11788000.0f) / 10000.0f;
//  }
    

	if( hz > 1000 )
	{
		hz = ( hz * (3 * hz - 1894) + 11788000) / 10000;
	}


//  kc = (C2PAI*hz)*(C2PAI*hz)*ts*ts; 					// wn*wn*ts*ts			  

//  kb = 2*(C2PAI*hz)*ts/qx;								// 2*wn*ts/qx				   

//  kx = 4 + (C2PAI*hz)*(C2PAI*hz)*ts*ts; 				// 4 + wn*wn*ts*ts			  
//  ky = 2*(4 - (C2PAI*hz)*(C2PAI*hz)*ts*ts); 			// 2*(4 - wn*wn*ts*ts)		   

//  ka = kx + kb; 										// 4 + 2*wn*ts/qx + wn*wn*ts*ts
//  kb = kx - kb; 										// 4 - 2*wn*ts/qx + wn*wn*ts*ts 
#if 1
	kc = C2PAI*hz*ts/1000000.0f;
	kc = kc*kc;

	kb = hz * ts / qx * 2*C2PAI/1000.0f;

	kx = 4 + kc;
	ky = 2*(4 - kc);

	ka = kx + kb;
	kb = kx - kb;


	kn[0] = kc / ka;										// kc / ka					   
	kn[1] = ky / ka;										// ky / ka						
	kn[2] = kb / ka;										// kb / ka					  
	kn[3] = 2 * kn[0];									// 2 * K[0] 	

#else

	kc = PI*hz*ts/1000000.0f;
	kc = tanf(kc);             // K

	kb = kc*1000.0f/qx;        // K/Q
	kc = kc*kc;                // K^2

	ka = kc+kb+1;

	kx = 2*(1-kc);
	ky = kc-kb+1;

	kn[0] = kc/ka;
	kn[1] = kx/ka;
	kn[2] = ky/ka;
	kn[3] = 2*kn[0];
	
#endif


  return;

}


PRIVATE INT32 PcalLowfilter2(INT32	hz, INT32 qx, INT32	ts, INT32 kn[4] )				
{
	INT32	ka,sa,kb,sb,kc,sc;
	INT32	kx,sx,ky,sy = 0;

	sa = 0;
	sx = 0;
	sy = 0;

	if( hz > 1000 )
	{
		hz = (INT32)( (INT16)hz * (INT16)(3 * hz - 1894) + 11788000) / 10000;
	}

	sc = 15;
	kc = MlibPcalKxgain( hz*hz,  ts*ts,  773019,   &sc, 0 );		/* wn*wn*ts*ts					*/
																	/* 773019=1000000^2/(2PI^2)/2^15*/
	sb = 0;
	kb = MlibPcalKxgain( hz,     ts,     qx,       &sb, 0 );
	kb = MlibPcalKxgain( kb,     125664, 10000000, &sb, 0 );		/* 2*wn*ts/qx					*/
																	/* 125664 = 2*2PI*10000			*/

	kx = MlibPcalKxaddx( 4<<24,  24,  kc,  sc,  &sx );				/* 4 + wn*wn*ts*ts				*/
	ky = MlibPcalKxsubx( 4<<24,  24,  kc,  sc,  &sy );				/* 2*(4 - wn*wn*ts*ts)			*/
	sy = sy - 1;
	ka = MlibPcalKxaddx( kx,     sx,  kb,  sb,  &sa );				/* 4 + 2*wn*ts/qx + wn*wn*ts*ts	*/
	kb = MlibPcalKxsubx( kx,     sx,  kb,  sb,  &sb );				/* 4 - 2*wn*ts/qx + wn*wn*ts*ts	*/

	kn[0] = MlibPcalKxdivx( kc,  sc,  ka,  sa,  24  );				/* {kc,sc}/{ka,sa}				*/
	kn[1] = MlibPcalKxdivx( ky,  sy,  ka,  sa,  24  );				/* {ky,sy}/{ka,sa}				*/
	kn[2] = MlibPcalKxdivx( kb,  sb,  ka,  sa,  24  );				/* {kb,sb}/{ka,sa}				*/
	kn[3] = MlibPcalKxdivx( kc,  (sc - 1),  ka,  sa,  24  );		/*  2 * K[0]					*/

	return( 0 );
}



/****************************************************************************************************
* DESCRIPTION:
*       Torque filter: Secondary low-pass filter parameter calculation
* RETURNS:
*
****************************************************************************************************/
PUBLIC void PcalBaseTrqLpassFilter2( TRQFIL *TrqFilData, UINT16 secfil, UINT16 secq )
{
	REAL32 		  hz;
	REAL32 		  qx;

	hz = secfil;			  // frequency [Hz] 							
	qx = 10 * secq;			  // Q constant [0.001]						

	f_PcalLowfilter2( hz, qx, TASKB_CYCLEUS, TrqFilData->A.Klpf2 );
	f_PcalLowfilter2( hz, qx, TASKB_CYCLEUS, TrqFilData->P.Klpf2 );

}


/****************************************************************************************************
* DESCRIPTION:
*       Torque filter (after torque compensation): Low-pass filter parameter calculation 
* RETURNS:
*
****************************************************************************************************/
PUBLIC REAL32 PcalBaseTrqLpassFilter3( INT32 Trqfil, INT32 ScanTime )
{
	REAL32 tmp , res;
	tmp = (REAL32)Trqfil * 10.0f;						 // 0.01ms -> us
	res = (REAL32)ScanTime/(tmp + (REAL32)ScanTime); 	 // Kf = ts/(tx+ts)

	return res;

}
							  

/****************************************************************************************************
 * DESCRIPTION:
 *       Normalized observer gain calculation: ObsGain [0.1Hz] --> NormalizedObsGain [rad/ts]
 *
 *					ObsGain * 2*PAI * CycleNs[ns]												  
 *			  x = -------------------------------												  
 *						   10 * 10^9															  
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 f_BpxNorObsGainCal( REAL32 ObsGain , REAL32 scantime)
{
	REAL32 x;

	x = ObsGain * C2PAI * scantime / 10.0f/1000000000.0f;

	return( x );
}


/****************************************************************************************************
 * DESCRIPTION:
 *       Normalized observer binar gain calculation : ObsJGain [%] --> NormalizedObsJGain 
 *
 *					      MaxTrq * 100 * CycleNs[ns] * ObsJGain[%]									
 *				x = ------------------------------------------------							
 *					  OvrSpd * Jmot * (100+Prm.jrate) * 10^9 * 100								
 *																									
 *					  2*PAI * 100 * CycleNs * ObsJGain											
 *				  = --------------------------------------											
 *					  Kvx * (100+Prm.jrate) * 10^9 * 100											
 *																									
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC REAL32 f_BpxNorObsJGainCal( REAL32 Kvx, REAL32 jrate, REAL32 ObsJGain , REAL32 scantime )
{

	REAL32 x;
	
	x = C2PAI * scantime * ObsJGain / (Kvx * (100.0f+jrate) * 1000000000.0f);

	return( x );

}

/****************************************************************************************************
 * DESCRIPTION:
 *       Normalized observer binar gain calculation : ObsJGain [%] --> NormalizedObsJGain 
 *
 *					      MaxTrq * 100 * CycleNs[ns] * ObsJGain[%]									
 *				x = ------------------------------------------------							
 *					  OvrSpd * Jmot * (100+Prm.jrate) * 10^9 * 100								
 *																									
 *					  2*PAI * 100 * CycleNs * ObsJGain											
 *				  = --------------------------------------											
 *					  Kvx * (100+Prm.jrate) * 10^9 * 100											
 *																									
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 BpxNorObsJGainCal( INT32 Kvx, INT32 jrate, INT32 ObsJGain , INT32 scantime )
{
   INT32    x;
   INT32    kx,sx;
   INT32    ky,sy;

   sy = 0;
   sx = 0;

   kx = MlibPcalKxmulx( ObsJGain, scantime, C2PAIE7, &sx );
   ky = MlibScalKskxkx( Kvx, C10POW7, 1, &sy, 0 );
   ky = MlibPcalKxmulx( ky, C10POW9, (100+jrate), &sy );
   x = MlibPcalKxdivx( kx, sx, ky, sy, 24 );

   return( x );

}									  

/****************************************************************************************************
* DESCRIPTION:
*         Pos Ref filter: Low-pass filter parameter calculation 
*         Only the first Pos Ref filter can be used as the torque filter in the torque control mode.
* RETURNS:
*
****************************************************************************************************/
PUBLIC void PcalBasePosRefLpassFilter( BASE_CTRL *BaseCtrls, INT32 PosRefFil)
{
  INT32 cyc_time;

  cyc_time = TASKB_CYCLEUS;

  BaseCtrls->CtrlCmdPrm.PosRefKlpf = MlibPcalKf1gain1(10*PosRefFil,cyc_time,0);
}
