/*
* This source file is part of the EtherCAT Slave Stack Code licensed by Beckhoff Automation GmbH & Co KG, 33415 Verl, Germany.
* The corresponding license agreement applies. This hint shall not be removed.
*/

/**
* \addtogroup app app
* @{
*/

/**
\file appObjects
\author ET9300Utilities.ApplicationHandler (Version 1.6.4.0) | <EMAIL>

\brief app specific objects<br>
\brief NOTE : This file will be overwritten if a new object dictionary is generated!<br>
*/

#if defined(_APP_) && (_APP_ == 1)
#define PROTO
#else
#define PROTO extern
#endif
/******************************************************************************
*                    Object 0x1600 : RxPdo1 Axis A
******************************************************************************/
/**
* \addtogroup 0x1600 0x1600 | RxPdo1 Axis A
* @{
* \brief Object 0x1600 (RxPdo1 Axis A) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - RxPdo1<br>
* SubIndex 2 - RxPdo2<br>
* SubIndex 3 - RxPdo3<br>
* SubIndex 4 - RxPdo4<br>
* SubIndex 5 - RxPdo5<br>
* SubIndex 6 - RxPdo6<br>
* SubIndex 7 - RxPdo7<br>
* SubIndex 8 - RxPdo8<br>
* SubIndex 9 - RxPdo9<br>
* SubIndex 10 - RxPdo10<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x1600[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READWRITE },
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex1 - RxPdo1 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex2 - RxPdo2 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex3 - RxPdo3 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex4 - RxPdo4 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex5 - RxPdo5 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex6 - RxPdo6 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex7 - RxPdo7 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex8 - RxPdo8 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex9 - RxPdo9 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }}; /* Subindex10 - RxPdo10 */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x1600[] = "RxPdo1 Axis A\000"
"RxPdo1\000"
"RxPdo2\000"
"RxPdo3\000"
"RxPdo4\000"
"RxPdo5\000"
"RxPdo6\000"
"RxPdo7\000"
"RxPdo8\000"
"RxPdo9\000"
"RxPdo10\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT32 SI1; /* Subindex1 -  */
UINT32 SI2; /* Subindex2 -  */
UINT32 SI3; /* Subindex3 -  */
UINT32 SI4; /* Subindex4 -  */
UINT32 SI5; /* Subindex5 -  */
UINT32 SI6; /* Subindex6 -  */
UINT32 SI7; /* Subindex7 -  */
UINT32 SI8; /* Subindex8 -  */
UINT32 SI9; /* Subindex9 -  */
UINT32 SI10; /* Subindex10 -  */
} OBJ_STRUCT_PACKED_END
TOBJ1600;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ1600 RxPdo1AxisA0x1600
#if defined(_APP_) && (_APP_ == 1)
={10,0x60400010,0x607A0020,0x60B10020,0x60B20010,0x00000000,0x00000000,0x00000000,0x00000000,0x00000000,0x00000000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x1A00 : TxPdo1 Axis A
******************************************************************************/
/**
* \addtogroup 0x1A00 0x1A00 | TxPdo1 Axis A
* @{
* \brief Object 0x1A00 (TxPdo1 Axis A) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - TxPdo1<br>
* SubIndex 2 - TxPdo2<br>
* SubIndex 3 - TxPdo3<br>
* SubIndex 4 - TxPdo4<br>
* SubIndex 5 - TxPdo5<br>
* SubIndex 6 - TxPdo6<br>
* SubIndex 7 - TxPdo7<br>
* SubIndex 8 - TxPdo8<br>
* SubIndex 9 - TxPdo9<br>
* SubIndex 10 - TxPdo10<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x1A00[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READWRITE },
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex1 - TxPdo1 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex2 - TxPdo2 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex3 - TxPdo3 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex4 - TxPdo4 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex5 - TxPdo5 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex6 - TxPdo6 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex7 - TxPdo7 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex8 - TxPdo8 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex9 - TxPdo9 */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }}; /* Subindex10 - TxPdo10 */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x1A00[] = "TxPdo1 Axis A\000"
"TxPdo1\000"
"TxPdo2\000"
"TxPdo3\000"
"TxPdo4\000"
"TxPdo5\000"
"TxPdo6\000"
"TxPdo7\000"
"TxPdo8\000"
"TxPdo9\000"
"TxPdo10\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT32 SI1; /* Subindex1 -  */
UINT32 SI2; /* Subindex2 -  */
UINT32 SI3; /* Subindex3 -  */
UINT32 SI4; /* Subindex4 -  */
UINT32 SI5; /* Subindex5 -  */
UINT32 SI6; /* Subindex6 -  */
UINT32 SI7; /* Subindex7 -  */
UINT32 SI8; /* Subindex8 -  */
UINT32 SI9; /* Subindex9 -  */
UINT32 SI10; /* Subindex10 -  */
} OBJ_STRUCT_PACKED_END
TOBJ1A00;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ1A00 TxPdo1AxisA0x1A00
#if defined(_APP_) && (_APP_ == 1)
={10,0x60410010,0x60640020,0x606C0020,0x60770010,0x60610008,0x00000000,0x00000000,0x00000000,0x00000000,0x00000000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x1C12 : SyncManager 2 assignment
******************************************************************************/
/**
* \addtogroup 0x1C12 0x1C12 | SyncManager 2 assignment
* @{
* \brief Object 0x1C12 (SyncManager 2 assignment) definition
*/
#ifdef _OBJD_
/**
* \brief Entry descriptions<br>
* 
* Subindex 0<br>
* Subindex 1 - n (the same entry description is used)<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x1C12[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READWRITE },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }};

/**
* \brief Object name definition<br>
* For Subindex 1 to n the syntax 'Subindex XXX' is used
*/
OBJCONST UCHAR OBJMEM aName0x1C12[] = "SyncManager 2 assignment\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16   u16SubIndex0;  /**< \brief Subindex 0 */
UINT16 aEntries[1];  /**< \brief Subindex 1 - 1 */
} OBJ_STRUCT_PACKED_END
TOBJ1C12;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ1C12 sRxPDOassign
#if defined(_APP_) && (_APP_ == 1)
={0,{0}}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x1C13 : SyncManager 3 assignment
******************************************************************************/
/**
* \addtogroup 0x1C13 0x1C13 | SyncManager 3 assignment
* @{
* \brief Object 0x1C13 (SyncManager 3 assignment) definition
*/
#ifdef _OBJD_
/**
* \brief Entry descriptions<br>
* 
* Subindex 0<br>
* Subindex 1 - n (the same entry description is used)<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x1C13[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READWRITE },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }};

/**
* \brief Object name definition<br>
* For Subindex 1 to n the syntax 'Subindex XXX' is used
*/
OBJCONST UCHAR OBJMEM aName0x1C13[] = "SyncManager 3 assignment\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16   u16SubIndex0;  /**< \brief Subindex 0 */
UINT16 aEntries[1];  /**< \brief Subindex 1 - 1 */
} OBJ_STRUCT_PACKED_END
TOBJ1C13;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ1C13 sTxPDOassign
#if defined(_APP_) && (_APP_ == 1)
={0,{0}}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x2000 : Axis A Motor Parameters
******************************************************************************/
/**
* \addtogroup 0x2000 0x2000 | Axis A Motor Parameters
* @{
* \brief Object 0x2000 (Axis A Motor Parameters) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Motor Type<br>
* SubIndex 2 - Motro ID<br>
* SubIndex 3 - Motor Rated Power<br>
* SubIndex 4 - Motor Rated Voltage<br>
* SubIndex 5 - Motor Rated Current<br>
* SubIndex 6 - Motor Rated Torque<br>
* SubIndex 7 - Motor Rated Speed<br>
* SubIndex 8 - Motor Max Current<br>
* SubIndex 9 - Motor Max Torque<br>
* SubIndex 10 - Motor Max Speed<br>
* SubIndex 11 - Motor Pole Pairs Number<br>
* SubIndex 12 - Motor Winding Resistance<br>
* SubIndex 13 - Motor Winding Inductance<br>
* SubIndex 14 - Motor Rotor Inertia<br>
* SubIndex 15 - Motor Back EMF<br>
* SubIndex 16 - Motor Torque Constant<br>
* SubIndex 17 - Motor Mechanical Constant<br>
* SubIndex 18 - Encoder Type<br>
* SubIndex 19 - ABS Encoder Multi-turn Bit<br>
* SubIndex 20 - ABS Encoder Single-turn Bit<br>
* SubIndex 21 - ABS Encoder offset<br>
* SubIndex 22 - ABZ Encoder Pules<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x2000[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex1 - Motor Type */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex2 - Motro ID */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex3 - Motor Rated Power */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex4 - Motor Rated Voltage */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex5 - Motor Rated Current */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex6 - Motor Rated Torque */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex7 - Motor Rated Speed */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex8 - Motor Max Current */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex9 - Motor Max Torque */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex10 - Motor Max Speed */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex11 - Motor Pole Pairs Number */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex12 - Motor Winding Resistance */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex13 - Motor Winding Inductance */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex14 - Motor Rotor Inertia */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex15 - Motor Back EMF */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex16 - Motor Torque Constant */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex17 - Motor Mechanical Constant */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex18 - Encoder Type */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex19 - ABS Encoder Multi-turn Bit */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex20 - ABS Encoder Single-turn Bit */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex21 - ABS Encoder offset */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }}; /* Subindex22 - ABZ Encoder Pules */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x2000[] = "Axis A Motor Parameters\000"
"Motor Type\000"
"Motro ID\000"
"Motor Rated Power\000"
"Motor Rated Voltage\000"
"Motor Rated Current\000"
"Motor Rated Torque\000"
"Motor Rated Speed\000"
"Motor Max Current\000"
"Motor Max Torque\000"
"Motor Max Speed\000"
"Motor Pole Pairs Number\000"
"Motor Winding Resistance\000"
"Motor Winding Inductance\000"
"Motor Rotor Inertia\000"
"Motor Back EMF\000"
"Motor Torque Constant\000"
"Motor Mechanical Constant\000"
"Encoder Type\000"
"ABS Encoder Multi-turn Bit\000"
"ABS Encoder Single-turn Bit\000"
"ABS Encoder offset\000"
"ABZ Encoder Pules\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT16 MotorType; /* Subindex1 - Motor Type */
UINT16 MotroID; /* Subindex2 - Motro ID */
UINT16 MotorRatedPower; /* Subindex3 - Motor Rated Power */
UINT16 MotorRatedVoltage; /* Subindex4 - Motor Rated Voltage */
UINT16 MotorRatedCurrent; /* Subindex5 - Motor Rated Current */
UINT16 MotorRatedTorque; /* Subindex6 - Motor Rated Torque */
UINT16 MotorRatedSpeed; /* Subindex7 - Motor Rated Speed */
UINT16 MotorMaxCurrent; /* Subindex8 - Motor Max Current */
UINT16 MotorMaxTorque; /* Subindex9 - Motor Max Torque */
UINT16 MotorMaxSpeed; /* Subindex10 - Motor Max Speed */
UINT16 MotorPolePairsNumber; /* Subindex11 - Motor Pole Pairs Number */
UINT16 MotorWindingResistance; /* Subindex12 - Motor Winding Resistance */
UINT16 MotorWindingInductance; /* Subindex13 - Motor Winding Inductance */
UINT16 MotorRotorInertia; /* Subindex14 - Motor Rotor Inertia */
UINT16 MotorBackEMF; /* Subindex15 - Motor Back EMF */
UINT16 MotorTorqueConstant; /* Subindex16 - Motor Torque Constant */
UINT16 MotorMechanicalConstant; /* Subindex17 - Motor Mechanical Constant */
UINT16 EncoderType; /* Subindex18 - Encoder Type */
UINT16 ABSEncoderMultiTurnBit; /* Subindex19 - ABS Encoder Multi-turn Bit */
UINT16 ABSEncoderSingleTurnBit; /* Subindex20 - ABS Encoder Single-turn Bit */
UINT32 ABSEncoderOffset; /* Subindex21 - ABS Encoder offset */
UINT32 ABZEncoderPules; /* Subindex22 - ABZ Encoder Pules */
} OBJ_STRUCT_PACKED_END
TOBJ2000;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ2000 AxisAMotorParameters0x2000
#if defined(_APP_) && (_APP_ == 1)
={22,0x0000,0x0000,0x0014,0x0000,0x0012,0x0040,0x0BB8,0x0033,0x00BF,0x0F3C,0x0004,0x1D4C,0x0960,0x001B,0x1004,0x0027,0x0140,0x0002,0x0010,0x0011,0x00000000,0x00002710}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x2001 : Axis A Basic Configuration Parameters
******************************************************************************/
/**
* \addtogroup 0x2001 0x2001 | Axis A Basic Configuration Parameters
* @{
* \brief Object 0x2001 (Axis A Basic Configuration Parameters) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Device Type<br>
* SubIndex 2 - Device Voltage Grade<br>
* SubIndex 3 - Device Rated Power<br>
* SubIndex 4 - Device Max Power<br>
* SubIndex 5 - Device Rated Current<br>
* SubIndex 6 - Device Max Current<br>
* SubIndex 7 - Rotate Direction<br>
* SubIndex 8 - Over Travel Stop Mode<br>
* SubIndex 9 - Force Stop Mode<br>
* SubIndex 10 - Alarm Stop Mode<br>
* SubIndex 11 - Fault NO.1 Stop Mode<br>
* SubIndex 12 - Brake Release Delay Time<br>
* SubIndex 13 - Brake Active Delay Time<br>
* SubIndex 14 - Brake Active Velocity<br>
* SubIndex 15 - Brake Active Allowed Delay Time<br>
* SubIndex 16 - Brake Enable<br>
* SubIndex 17 - LED Warn Select<br>
* SubIndex 18 - Regen Resistor Min Value<br>
* SubIndex 19 - Regen Resistor Type<br>
* SubIndex 20 - Res Heat Dissipation Coeff<br>
* SubIndex 21 - Ext Regen Resistor Power<br>
* SubIndex 22 - Ext Regen Resistor Value<br>
* SubIndex 23 - System Init<br>
* SubIndex 24 - DcBus Input Select<br>
* SubIndex 25 - Ac Off Detect Time<br>
* SubIndex 26 - Power Charge Wait Time<br>
* SubIndex 27 - Power Ready Wait Time<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x2001[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READ }, /* Subindex1 - Device Type */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READ }, /* Subindex2 - Device Voltage Grade */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READ }, /* Subindex3 - Device Rated Power */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READ }, /* Subindex4 - Device Max Power */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READ }, /* Subindex5 - Device Rated Current */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READ }, /* Subindex6 - Device Max Current */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex7 - Rotate Direction */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex8 - Over Travel Stop Mode */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex9 - Force Stop Mode */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex10 - Alarm Stop Mode */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex11 - Fault NO.1 Stop Mode */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex12 - Brake Release Delay Time */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex13 - Brake Active Delay Time */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex14 - Brake Active Velocity */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex15 - Brake Active Allowed Delay Time */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex16 - Brake Enable */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex17 - LED Warn Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex18 - Regen Resistor Min Value */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex19 - Regen Resistor Type */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex20 - Res Heat Dissipation Coeff */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex21 - Ext Regen Resistor Power */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex22 - Ext Regen Resistor Value */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex23 - System Init */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex24 - DcBus Input Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex25 - Ac Off Detect Time */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex26 - Power Charge Wait Time */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }}; /* Subindex27 - Power Ready Wait Time */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x2001[] = "Axis A Basic Configuration Parameters\000"
"Device Type\000"
"Device Voltage Grade\000"
"Device Rated Power\000"
"Device Max Power\000"
"Device Rated Current\000"
"Device Max Current\000"
"Rotate Direction\000"
"Over Travel Stop Mode\000"
"Force Stop Mode\000"
"Alarm Stop Mode\000"
"Fault NO.1 Stop Mode\000"
"Brake Release Delay Time\000"
"Brake Active Delay Time\000"
"Brake Active Velocity\000"
"Brake Active Allowed Delay Time\000"
"Brake Enable\000"
"LED Warn Select\000"
"Regen Resistor Min Value\000"
"Regen Resistor Type\000"
"Res Heat Dissipation Coeff\000"
"Ext Regen Resistor Power\000"
"Ext Regen Resistor Value\000"
"System Init\000"
"DcBus Input Select\000"
"Ac Off Detect Time\000"
"Power Charge Wait Time\000"
"Power Ready Wait Time\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT16 DeviceType; /* Subindex1 - Device Type */
UINT16 DeviceVoltageGrade; /* Subindex2 - Device Voltage Grade */
UINT16 DeviceRatedPower; /* Subindex3 - Device Rated Power */
UINT16 DeviceMaxPower; /* Subindex4 - Device Max Power */
UINT16 DeviceRatedCurrent; /* Subindex5 - Device Rated Current */
UINT16 DeviceMaxCurrent; /* Subindex6 - Device Max Current */
UINT16 RotateDirection; /* Subindex7 - Rotate Direction */
UINT16 OverTravelStopMode; /* Subindex8 - Over Travel Stop Mode */
UINT16 ForceStopMode; /* Subindex9 - Force Stop Mode */
UINT16 AlarmStopMode; /* Subindex10 - Alarm Stop Mode */
UINT16 FaultNO1StopMode; /* Subindex11 - Fault NO.1 Stop Mode */
UINT16 BrakeReleaseDelayTime; /* Subindex12 - Brake Release Delay Time */
UINT16 BrakeActiveDelayTime; /* Subindex13 - Brake Active Delay Time */
UINT16 BrakeActiveVelocity; /* Subindex14 - Brake Active Velocity */
UINT16 BrakeActiveAllowedDelayTime; /* Subindex15 - Brake Active Allowed Delay Time */
UINT16 BrakeEnable; /* Subindex16 - Brake Enable */
UINT16 LEDWarnSelect; /* Subindex17 - LED Warn Select */
UINT16 RegenResistorMinValue; /* Subindex18 - Regen Resistor Min Value */
UINT16 RegenResistorType; /* Subindex19 - Regen Resistor Type */
UINT16 ResHeatDissipationCoeff; /* Subindex20 - Res Heat Dissipation Coeff */
UINT16 ExtRegenResistorPower; /* Subindex21 - Ext Regen Resistor Power */
UINT16 ExtRegenResistorValue; /* Subindex22 - Ext Regen Resistor Value */
UINT16 SystemInit; /* Subindex23 - System Init */
UINT16 DcBusInputSelect; /* Subindex24 - DcBus Input Select */
UINT16 AcOffDetectTime; /* Subindex25 - Ac Off Detect Time */
UINT16 PowerChargeWaitTime; /* Subindex26 - Power Charge Wait Time */
UINT16 PowerReadyWaitTime; /* Subindex27 - Power Ready Wait Time */
} OBJ_STRUCT_PACKED_END
TOBJ2001;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ2001 AxisABasicConfigurationParameters0x2001
#if defined(_APP_) && (_APP_ == 1)
={27,0x0000,0x017C,0x004B,0x0096,0x0028,0x0050,0x0000,0x0000,0x0000,0x0000,0x0000,0x00FA,0x0096,0x001E,0x01F4,0x0000,0x0001,0x0028,0x0003,0x001E,0x0028,0x0030,0x0000,0x0000,0x0000,0x0000,0x0000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x2002 : Axis A Motion control parameter
******************************************************************************/
/**
* \addtogroup 0x2002 0x2002 | Axis A Motion control parameter
* @{
* \brief Object 0x2002 (Axis A Motion control parameter) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Control Source Option<br>
* SubIndex 2 - Position Loop Gain<br>
* SubIndex 3 - Speed Loop Gain<br>
* SubIndex 4 - Speed Loop Time Constant<br>
* SubIndex 5 - Load Inertia Ratio<br>
* SubIndex 6 - Speed Loop Gain 2<br>
* SubIndex 7 - Speed Loop Time Constant 2<br>
* SubIndex 8 - Speed PDFF Coff<br>
* SubIndex 9 - Current Loop Gain<br>
* SubIndex 10 - Speed Mode Switch<br>
* SubIndex 11 - Mode Switch Torque Value<br>
* SubIndex 12 - Mode Switch Speed Value<br>
* SubIndex 13 - Mode Switch Acc Value<br>
* SubIndex 14 - Mode Switch PosErr Value<br>
* SubIndex 15 - TrqFF Control Select<br>
* SubIndex 16 - TrqFF Filter Fime Constant<br>
* SubIndex 17 - TrqFF Gain<br>
* SubIndex 18 - SpdFF Control Select<br>
* SubIndex 19 - SpdFF Filter Fime Constant<br>
* SubIndex 20 - SpdFF Gain<br>
* SubIndex 21 - Servo On Speed Limit<br>
* SubIndex 22 - TrqCtrl Speed Limit<br>
* SubIndex 23 - Speed Average Filter Config<br>
* SubIndex 24 - First TrqCmd Filter Time<br>
* SubIndex 25 - Second TrqCmd Filter Freq<br>
* SubIndex 26 - Second TrqCmd Filter Q <br>
* SubIndex 27 - Forward Internal Torque Limit<br>
* SubIndex 28 - Reverse Internal Torque Limit<br>
* SubIndex 29 - Forward External Torque Limit<br>
* SubIndex 30 - Reverse External Torque Limit<br>
* SubIndex 31 - Emergency Stop Torque Limit<br>
* SubIndex 32 - Phase Find Method<br>
* SubIndex 33 - Phase Find Ramp Time<br>
* SubIndex 34 - Phase Find Stabilize Time<br>
* SubIndex 35 - Phase Find Current <br>
* SubIndex 36 - Function Generater Type<br>
* SubIndex 37 - Function Generater Number<br>
* SubIndex 38 - Function Generater Frequency<br>
* SubIndex 39 - Function Generater Amplitude<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x2002[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex1 - Control Source Option */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex2 - Position Loop Gain */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex3 - Speed Loop Gain */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex4 - Speed Loop Time Constant */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex5 - Load Inertia Ratio */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex6 - Speed Loop Gain 2 */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex7 - Speed Loop Time Constant 2 */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex8 - Speed PDFF Coff */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex9 - Current Loop Gain */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex10 - Speed Mode Switch */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex11 - Mode Switch Torque Value */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex12 - Mode Switch Speed Value */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex13 - Mode Switch Acc Value */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex14 - Mode Switch PosErr Value */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex15 - TrqFF Control Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex16 - TrqFF Filter Fime Constant */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex17 - TrqFF Gain */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex18 - SpdFF Control Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex19 - SpdFF Filter Fime Constant */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex20 - SpdFF Gain */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex21 - Servo On Speed Limit */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex22 - TrqCtrl Speed Limit */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex23 - Speed Average Filter Config */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex24 - First TrqCmd Filter Time */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex25 - Second TrqCmd Filter Freq */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex26 - Second TrqCmd Filter Q  */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex27 - Forward Internal Torque Limit */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex28 - Reverse Internal Torque Limit */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex29 - Forward External Torque Limit */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex30 - Reverse External Torque Limit */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex31 - Emergency Stop Torque Limit */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex32 - Phase Find Method */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex33 - Phase Find Ramp Time */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex34 - Phase Find Stabilize Time */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex35 - Phase Find Current  */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex36 - Function Generater Type */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex37 - Function Generater Number */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex38 - Function Generater Frequency */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }}; /* Subindex39 - Function Generater Amplitude */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x2002[] = "Axis A Motion control parameter\000"
"Control Source Option\000"
"Position Loop Gain\000"
"Speed Loop Gain\000"
"Speed Loop Time Constant\000"
"Load Inertia Ratio\000"
"Speed Loop Gain 2\000"
"Speed Loop Time Constant 2\000"
"Speed PDFF Coff\000"
"Current Loop Gain\000"
"Speed Mode Switch\000"
"Mode Switch Torque Value\000"
"Mode Switch Speed Value\000"
"Mode Switch Acc Value\000"
"Mode Switch PosErr Value\000"
"TrqFF Control Select\000"
"TrqFF Filter Fime Constant\000"
"TrqFF Gain\000"
"SpdFF Control Select\000"
"SpdFF Filter Fime Constant\000"
"SpdFF Gain\000"
"Servo On Speed Limit\000"
"TrqCtrl Speed Limit\000"
"Speed Average Filter Config\000"
"First TrqCmd Filter Time\000"
"Second TrqCmd Filter Freq\000"
"Second TrqCmd Filter Q \000"
"Forward Internal Torque Limit\000"
"Reverse Internal Torque Limit\000"
"Forward External Torque Limit\000"
"Reverse External Torque Limit\000"
"Emergency Stop Torque Limit\000"
"Phase Find Method\000"
"Phase Find Ramp Time\000"
"Phase Find Stabilize Time\000"
"Phase Find Current \000"
"Function Generater Type\000"
"Function Generater Number\000"
"Function Generater Frequency\000"
"Function Generater Amplitude\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT16 ControlSourceOption; /* Subindex1 - Control Source Option */
UINT16 PositionLoopGain; /* Subindex2 - Position Loop Gain */
UINT16 SpeedLoopGain; /* Subindex3 - Speed Loop Gain */
UINT16 SpeedLoopTimeConstant; /* Subindex4 - Speed Loop Time Constant */
UINT16 LoadInertiaRatio; /* Subindex5 - Load Inertia Ratio */
UINT16 SpeedLoopGain2; /* Subindex6 - Speed Loop Gain 2 */
UINT16 SpeedLoopTimeConstant2; /* Subindex7 - Speed Loop Time Constant 2 */
UINT16 SpeedPDFFCoff; /* Subindex8 - Speed PDFF Coff */
UINT16 CurrentLoopGain; /* Subindex9 - Current Loop Gain */
UINT16 SpeedModeSwitch; /* Subindex10 - Speed Mode Switch */
UINT16 ModeSwitchTorqueValue; /* Subindex11 - Mode Switch Torque Value */
UINT16 ModeSwitchSpeedValue; /* Subindex12 - Mode Switch Speed Value */
UINT16 ModeSwitchAccValue; /* Subindex13 - Mode Switch Acc Value */
UINT16 ModeSwitchPosErrValue; /* Subindex14 - Mode Switch PosErr Value */
UINT16 TrqFFControlSelect; /* Subindex15 - TrqFF Control Select */
UINT16 TrqFFFilterFimeConstant; /* Subindex16 - TrqFF Filter Fime Constant */
UINT16 TrqFFGain; /* Subindex17 - TrqFF Gain */
UINT16 SpdFFControlSelect; /* Subindex18 - SpdFF Control Select */
UINT16 SpdFFFilterFimeConstant; /* Subindex19 - SpdFF Filter Fime Constant */
UINT16 SpdFFGain; /* Subindex20 - SpdFF Gain */
UINT16 ServoOnSpeedLimit; /* Subindex21 - Servo On Speed Limit */
UINT16 TrqCtrlSpeedLimit; /* Subindex22 - TrqCtrl Speed Limit */
UINT16 SpeedAverageFilterConfig; /* Subindex23 - Speed Average Filter Config */
UINT16 FirstTrqCmdFilterTime; /* Subindex24 - First TrqCmd Filter Time */
UINT16 SecondTrqCmdFilterFreq; /* Subindex25 - Second TrqCmd Filter Freq */
UINT16 SecondTrqCmdFilterQ; /* Subindex26 - Second TrqCmd Filter Q  */
UINT16 ForwardInternalTorqueLimit; /* Subindex27 - Forward Internal Torque Limit */
UINT16 ReverseInternalTorqueLimit; /* Subindex28 - Reverse Internal Torque Limit */
UINT16 ForwardExternalTorqueLimit; /* Subindex29 - Forward External Torque Limit */
UINT16 ReverseExternalTorqueLimit; /* Subindex30 - Reverse External Torque Limit */
UINT16 EmergencyStopTorqueLimit; /* Subindex31 - Emergency Stop Torque Limit */
UINT16 PhaseFindMethod; /* Subindex32 - Phase Find Method */
UINT16 PhaseFindRampTime; /* Subindex33 - Phase Find Ramp Time */
UINT16 PhaseFindStabilizeTime; /* Subindex34 - Phase Find Stabilize Time */
UINT16 PhaseFindCurrent; /* Subindex35 - Phase Find Current  */
UINT16 FunctionGeneraterType; /* Subindex36 - Function Generater Type */
UINT16 FunctionGeneraterNumber; /* Subindex37 - Function Generater Number */
UINT16 FunctionGeneraterFrequency; /* Subindex38 - Function Generater Frequency */
UINT32 FunctionGeneraterAmplitude; /* Subindex39 - Function Generater Amplitude */
} OBJ_STRUCT_PACKED_END
TOBJ2002;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ2002 AxisAMotionControlParameter0x2002
#if defined(_APP_) && (_APP_ == 1)
={39,0x0000,0x0190,0x0190,0x07D0,0x0000,0x0190,0x07D0,0x0064,0x03E8,0x0000,0x00C8,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0BB8,0x0BB8,0x0000,0x0064,0x1388,0x0032,0x012C,0x012C,0x0064,0x0064,0x012C,0x0001,0x0064,0x0064,0x0000,0x0001,0x0000,0x000A,0x00000000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x2003 : Axis A Advanced Configuration Parameters
******************************************************************************/
/**
* \addtogroup 0x2003 0x2003 | Axis A Advanced Configuration Parameters
* @{
* \brief Object 0x2003 (Axis A Advanced Configuration Parameters) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Vibration Suppression Option<br>
* SubIndex 2 - VibSup Freq<br>
* SubIndex 3 - VibSup Gain Comp<br>
* SubIndex 4 - VibSup Damping Gain<br>
* SubIndex 5 - VibSup Filter Time1 Comp<br>
* SubIndex 6 - VibSup Filter Time2 Comp<br>
* SubIndex 7 - VubSup Damping Gain2<br>
* SubIndex 8 - VibSup Freq2<br>
* SubIndex 9 - Disturbance observer gain<br>
* SubIndex 10 - Disturbance observer gain 2<br>
* SubIndex 11 - Disturbance observer coefficient<br>
* SubIndex 12 - Disturbance observer freq correction<br>
* SubIndex 13 - Disturbance observer gain correction<br>
* SubIndex 14 - Speed observer gain<br>
* SubIndex 15 - Speed observer pos compensation gain<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x2003[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex1 - Vibration Suppression Option */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex2 - VibSup Freq */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex3 - VibSup Gain Comp */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex4 - VibSup Damping Gain */
{ DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE }, /* Subindex5 - VibSup Filter Time1 Comp */
{ DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE }, /* Subindex6 - VibSup Filter Time2 Comp */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex7 - VubSup Damping Gain2 */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex8 - VibSup Freq2 */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex9 - Disturbance observer gain */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex10 - Disturbance observer gain 2 */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex11 - Disturbance observer coefficient */
{ DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE }, /* Subindex12 - Disturbance observer freq correction */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex13 - Disturbance observer gain correction */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex14 - Speed observer gain */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }}; /* Subindex15 - Speed observer pos compensation gain */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x2003[] = "Axis A Advanced Configuration Parameters\000"
"Vibration Suppression Option\000"
"VibSup Freq\000"
"VibSup Gain Comp\000"
"VibSup Damping Gain\000"
"VibSup Filter Time1 Comp\000"
"VibSup Filter Time2 Comp\000"
"VubSup Damping Gain2\000"
"VibSup Freq2\000"
"Disturbance observer gain\000"
"Disturbance observer gain 2\000"
"Disturbance observer coefficient\000"
"Disturbance observer freq correction\000"
"Disturbance observer gain correction\000"
"Speed observer gain\000"
"Speed observer pos compensation gain\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT16 VibrationSuppressionOption; /* Subindex1 - Vibration Suppression Option */
UINT16 VibSupFreq; /* Subindex2 - VibSup Freq */
UINT16 VibSupGainComp; /* Subindex3 - VibSup Gain Comp */
UINT16 VibSupDampingGain; /* Subindex4 - VibSup Damping Gain */
INT16 VibSupFilterTime1Comp; /* Subindex5 - VibSup Filter Time1 Comp */
INT16 VibSupFilterTime2Comp; /* Subindex6 - VibSup Filter Time2 Comp */
UINT16 VubSupDampingGain2; /* Subindex7 - VubSup Damping Gain2 */
UINT16 VibSupFreq2; /* Subindex8 - VibSup Freq2 */
UINT16 DisturbanceObserverGain; /* Subindex9 - Disturbance observer gain */
UINT16 DisturbanceObserverGain2; /* Subindex10 - Disturbance observer gain 2 */
UINT16 DisturbanceObserverCoefficient; /* Subindex11 - Disturbance observer coefficient */
INT16 DisturbanceObserverFreqCorrection; /* Subindex12 - Disturbance observer freq correction */
UINT16 DisturbanceObserverGainCorrection; /* Subindex13 - Disturbance observer gain correction */
UINT16 SpeedObserverGain; /* Subindex14 - Speed observer gain */
UINT16 SpeedObserverPosCompensationGain; /* Subindex15 - Speed observer pos compensation gain */
} OBJ_STRUCT_PACKED_END
TOBJ2003;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ2003 AxisAAdvancedConfigurationParameters0x2003
#if defined(_APP_) && (_APP_ == 1)
={15,0x0010,0x03E8,0x0064,0x0000,0x0000,0x0000,0x0000,0x4E20,0x0064,0x0064,0x0000,0x0000,0x0064,0x0028,0x0096}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x2004 : Axis A Digital input parameter
******************************************************************************/
/**
* \addtogroup 0x2004 0x2004 | Axis A Digital input parameter
* @{
* \brief Object 0x2004 (Axis A Digital input parameter) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - DI1 Fuction Select<br>
* SubIndex 2 - DI1 Logic Select<br>
* SubIndex 3 - DI2 Fuction Select<br>
* SubIndex 4 - DI2 Logic Select<br>
* SubIndex 5 - DI3 Fuction Select<br>
* SubIndex 6 - DI3 Logic Select<br>
* SubIndex 7 - DI4 Fuction Select<br>
* SubIndex 8 - DI4 Logic Select<br>
* SubIndex 9 - DI5 Fuction Select<br>
* SubIndex 10 - DI5 Logic Select<br>
* SubIndex 11 - DI6 Fuction Select<br>
* SubIndex 12 - DI6 Logic Select<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x2004[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex1 - DI1 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex2 - DI1 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex3 - DI2 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex4 - DI2 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex5 - DI3 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex6 - DI3 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex7 - DI4 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex8 - DI4 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex9 - DI5 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex10 - DI5 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex11 - DI6 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }}; /* Subindex12 - DI6 Logic Select */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x2004[] = "Axis A Digital input parameter\000"
"DI1 Fuction Select\000"
"DI1 Logic Select\000"
"DI2 Fuction Select\000"
"DI2 Logic Select\000"
"DI3 Fuction Select\000"
"DI3 Logic Select\000"
"DI4 Fuction Select\000"
"DI4 Logic Select\000"
"DI5 Fuction Select\000"
"DI5 Logic Select\000"
"DI6 Fuction Select\000"
"DI6 Logic Select\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT16 DI1FuctionSelect; /* Subindex1 - DI1 Fuction Select */
UINT16 DI1LogicSelect; /* Subindex2 - DI1 Logic Select */
UINT16 DI2FuctionSelect; /* Subindex3 - DI2 Fuction Select */
UINT16 DI2LogicSelect; /* Subindex4 - DI2 Logic Select */
UINT16 DI3FuctionSelect; /* Subindex5 - DI3 Fuction Select */
UINT16 DI3LogicSelect; /* Subindex6 - DI3 Logic Select */
UINT16 DI4FuctionSelect; /* Subindex7 - DI4 Fuction Select */
UINT16 DI4LogicSelect; /* Subindex8 - DI4 Logic Select */
UINT16 DI5FuctionSelect; /* Subindex9 - DI5 Fuction Select */
UINT16 DI5LogicSelect; /* Subindex10 - DI5 Logic Select */
UINT16 DI6FuctionSelect; /* Subindex11 - DI6 Fuction Select */
UINT16 DI6LogicSelect; /* Subindex12 - DI6 Logic Select */
} OBJ_STRUCT_PACKED_END
TOBJ2004;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ2004 AxisADigitalInputParameter0x2004
#if defined(_APP_) && (_APP_ == 1)
={12,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x2005 : Axis A Digital output parameter
******************************************************************************/
/**
* \addtogroup 0x2005 0x2005 | Axis A Digital output parameter
* @{
* \brief Object 0x2005 (Axis A Digital output parameter) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - DO1 Fuction Select<br>
* SubIndex 2 - DO1 Logic Select<br>
* SubIndex 3 - DO2 Fuction Select<br>
* SubIndex 4 - DO2 Logic Select<br>
* SubIndex 5 - DO3 Fuction Select<br>
* SubIndex 6 - DO3 Logic Select<br>
* SubIndex 7 - DO4 Fuction Select<br>
* SubIndex 8 - DO4 Logic Select<br>
* SubIndex 9 - DO5 Fuction Select<br>
* SubIndex 10 - DO5 Logic Select<br>
* SubIndex 11 - DO6 Fuction Select<br>
* SubIndex 12 - DO6 Logic Select<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x2005[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex1 - DO1 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex2 - DO1 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex3 - DO2 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex4 - DO2 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex5 - DO3 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex6 - DO3 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex7 - DO4 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex8 - DO4 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex9 - DO5 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex10 - DO5 Logic Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex11 - DO6 Fuction Select */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }}; /* Subindex12 - DO6 Logic Select */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x2005[] = "Axis A Digital output parameter\000"
"DO1 Fuction Select\000"
"DO1 Logic Select\000"
"DO2 Fuction Select\000"
"DO2 Logic Select\000"
"DO3 Fuction Select\000"
"DO3 Logic Select\000"
"DO4 Fuction Select\000"
"DO4 Logic Select\000"
"DO5 Fuction Select\000"
"DO5 Logic Select\000"
"DO6 Fuction Select\000"
"DO6 Logic Select\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT16 DO1FuctionSelect; /* Subindex1 - DO1 Fuction Select */
UINT16 DO1LogicSelect; /* Subindex2 - DO1 Logic Select */
UINT16 DO2FuctionSelect; /* Subindex3 - DO2 Fuction Select */
UINT16 DO2LogicSelect; /* Subindex4 - DO2 Logic Select */
UINT16 DO3FuctionSelect; /* Subindex5 - DO3 Fuction Select */
UINT16 DO3LogicSelect; /* Subindex6 - DO3 Logic Select */
UINT16 DO4FuctionSelect; /* Subindex7 - DO4 Fuction Select */
UINT16 DO4LogicSelect; /* Subindex8 - DO4 Logic Select */
UINT16 DO5FuctionSelect; /* Subindex9 - DO5 Fuction Select */
UINT16 DO5LogicSelect; /* Subindex10 - DO5 Logic Select */
UINT16 DO6FuctionSelect; /* Subindex11 - DO6 Fuction Select */
UINT16 DO6LogicSelect; /* Subindex12 - DO6 Logic Select */
} OBJ_STRUCT_PACKED_END
TOBJ2005;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ2005 AxisADigitalOutputParameter0x2005
#if defined(_APP_) && (_APP_ == 1)
={12,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x2006 : Axis A Fault protection parameter
******************************************************************************/
/**
* \addtogroup 0x2006 0x2006 | Axis A Fault protection parameter
* @{
* \brief Object 0x2006 (Axis A Fault protection parameter) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Vdc OV Level<br>
* SubIndex 2 - Vdc Discharge Level<br>
* SubIndex 3 - Vdc Uv Level<br>
* SubIndex 4 - Vdc Uv Filter<br>
* SubIndex 5 - Vdc Uv Warn Level<br>
* SubIndex 6 - Over Speed Value<br>
* SubIndex 7 - PosErr Warn Level<br>
* SubIndex 8 - PosErr Alarm Level<br>
* SubIndex 9 - Svon PosErr Warn Level<br>
* SubIndex 10 - Svon PosErr Alarm Level<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x2006[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex1 - Vdc OV Level */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex2 - Vdc Discharge Level */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex3 - Vdc Uv Level */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex4 - Vdc Uv Filter */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex5 - Vdc Uv Warn Level */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex6 - Over Speed Value */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex7 - PosErr Warn Level */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex8 - PosErr Alarm Level */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }, /* Subindex9 - Svon PosErr Warn Level */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE }}; /* Subindex10 - Svon PosErr Alarm Level */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x2006[] = "Axis A Fault protection parameter\000"
"Vdc OV Level\000"
"Vdc Discharge Level\000"
"Vdc Uv Level\000"
"Vdc Uv Filter\000"
"Vdc Uv Warn Level\000"
"Over Speed Value\000"
"PosErr Warn Level\000"
"PosErr Alarm Level\000"
"Svon PosErr Warn Level\000"
"Svon PosErr Alarm Level\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT16 VdcOVLevel; /* Subindex1 - Vdc OV Level */
UINT16 VdcDischargeLevel; /* Subindex2 - Vdc Discharge Level */
UINT16 VdcUvLevel; /* Subindex3 - Vdc Uv Level */
UINT16 VdcUvFilter; /* Subindex4 - Vdc Uv Filter */
UINT16 VdcUvWarnLevel; /* Subindex5 - Vdc Uv Warn Level */
UINT16 OverSpeedValue; /* Subindex6 - Over Speed Value */
UINT32 PosErrWarnLevel; /* Subindex7 - PosErr Warn Level */
UINT32 PosErrAlarmLevel; /* Subindex8 - PosErr Alarm Level */
UINT32 SvonPosErrWarnLevel; /* Subindex9 - Svon PosErr Warn Level */
UINT32 SvonPosErrAlarmLevel; /* Subindex10 - Svon PosErr Alarm Level */
} OBJ_STRUCT_PACKED_END
TOBJ2006;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ2006 AxisAFaultProtectionParameter0x2006
#if defined(_APP_) && (_APP_ == 1)
={10,0x01A4,0x017C,0x00C8,0x0064,0x00D2,0x1194,0x00000064,0x00050000,0x00000064,0x00050000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x2007 : Axis A Auxiliary Parameter
******************************************************************************/
/**
* \addtogroup 0x2007 0x2007 | Axis A Auxiliary Parameter
* @{
* \brief Object 0x2007 (Axis A Auxiliary Parameter) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - System Reset<br>
* SubIndex 2 - Factory Reset<br>
* SubIndex 3 - Fault Reset<br>
* SubIndex 4 - Clear Error History<br>
* SubIndex 5 - Phase Find Start<br>
* SubIndex 6 - Save Prm to Eeprom<br>
* SubIndex 7 - Load Prm from Eeprom<br>
* SubIndex 8 - Function Generater Start<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x2007[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex1 - System Reset */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex2 - Factory Reset */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex3 - Fault Reset */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex4 - Clear Error History */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex5 - Phase Find Start */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex6 - Save Prm to Eeprom */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }, /* Subindex7 - Load Prm from Eeprom */
{ DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE }}; /* Subindex8 - Function Generater Start */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x2007[] = "Axis A Auxiliary Parameter\000"
"System Reset\000"
"Factory Reset\000"
"Fault Reset\000"
"Clear Error History\000"
"Phase Find Start\000"
"Save Prm to Eeprom\000"
"Load Prm from Eeprom\000"
"Function Generater Start\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT16 SystemReset; /* Subindex1 - System Reset */
UINT16 FactoryReset; /* Subindex2 - Factory Reset */
UINT16 FaultReset; /* Subindex3 - Fault Reset */
UINT16 ClearErrorHistory; /* Subindex4 - Clear Error History */
UINT16 PhaseFindStart; /* Subindex5 - Phase Find Start */
UINT16 SavePrmToEeprom; /* Subindex6 - Save Prm to Eeprom */
UINT16 LoadPrmFromEeprom; /* Subindex7 - Load Prm from Eeprom */
UINT16 FunctionGeneraterStart; /* Subindex8 - Function Generater Start */
} OBJ_STRUCT_PACKED_END
TOBJ2007;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ2007 AxisAAuxiliaryParameter0x2007
#if defined(_APP_) && (_APP_ == 1)
={8,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000,0x0000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6007 : Axis A CANopen abort option code
******************************************************************************/
/**
* \addtogroup 0x6007 0x6007 | Axis A CANopen abort option code
* @{
* \brief Object 0x6007 (Axis A CANopen abort option code) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6007 = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6007[] = "Axis A CANopen abort option code\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisACANopenAbortOptionCode0x6007
#if defined(_APP_) && (_APP_ == 1)
= 0x0000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x603F : Axis A Last error code
******************************************************************************/
/**
* \addtogroup 0x603F 0x603F | Axis A Last error code
* @{
* \brief Object 0x603F (Axis A Last error code) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x603F = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x603F[] = "Axis A Last error code\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisALastErrorCode0x603F;
/** @}*/



/******************************************************************************
*                    Object 0x6040 : Axis A Control word
******************************************************************************/
/**
* \addtogroup 0x6040 0x6040 | Axis A Control word
* @{
* \brief Object 0x6040 (Axis A Control word) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6040 = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6040[] = "Axis A Control word\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAControlWord0x6040
#if defined(_APP_) && (_APP_ == 1)
= 0x0000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6041 : Axis A Statusword
******************************************************************************/
/**
* \addtogroup 0x6041 0x6041 | Axis A Statusword
* @{
* \brief Object 0x6041 (Axis A Statusword) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6041 = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6041[] = "Axis A Statusword\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAStatusword0x6041;
/** @}*/



/******************************************************************************
*                    Object 0x605A : Axis A Quick stop option code
******************************************************************************/
/**
* \addtogroup 0x605A 0x605A | Axis A Quick stop option code
* @{
* \brief Object 0x605A (Axis A Quick stop option code) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x605A = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x605A[] = "Axis A Quick stop option code\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisAQuickStopOptionCode0x605A
#if defined(_APP_) && (_APP_ == 1)
= 0x0002
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x605B : Axis A Shutdown option code
******************************************************************************/
/**
* \addtogroup 0x605B 0x605B | Axis A Shutdown option code
* @{
* \brief Object 0x605B (Axis A Shutdown option code) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x605B = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x605B[] = "Axis A Shutdown option code\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisAShutdownOptionCode0x605B
#if defined(_APP_) && (_APP_ == 1)
= 0x0002
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x605C : Axis A Disable operation option code
******************************************************************************/
/**
* \addtogroup 0x605C 0x605C | Axis A Disable operation option code
* @{
* \brief Object 0x605C (Axis A Disable operation option code) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x605C = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x605C[] = "Axis A Disable operation option code\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisADisableOperationOptionCode0x605C
#if defined(_APP_) && (_APP_ == 1)
= 0x0001
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x605D : Axis A Halt option code
******************************************************************************/
/**
* \addtogroup 0x605D 0x605D | Axis A Halt option code
* @{
* \brief Object 0x605D (Axis A Halt option code) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x605D = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x605D[] = "Axis A Halt option code\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisAHaltOptionCode0x605D
#if defined(_APP_) && (_APP_ == 1)
= 0x0001
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x605E : Axis A Fault Reaction Code
******************************************************************************/
/**
* \addtogroup 0x605E 0x605E | Axis A Fault Reaction Code
* @{
* \brief Object 0x605E (Axis A Fault Reaction Code) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x605E = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x605E[] = "Axis A Fault Reaction Code\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisAFaultReactionCode0x605E
#if defined(_APP_) && (_APP_ == 1)
= 0x0001
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6060 : Axis A Modes of operation
******************************************************************************/
/**
* \addtogroup 0x6060 0x6060 | Axis A Modes of operation
* @{
* \brief Object 0x6060 (Axis A Modes of operation) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6060 = { DEFTYPE_INTEGER8 , 0x08 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6060[] = "Axis A Modes of operation\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT8 AxisAModesOfOperation0x6060
#if defined(_APP_) && (_APP_ == 1)
= 0x08
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6061 : Axis A Modes of operation display
******************************************************************************/
/**
* \addtogroup 0x6061 0x6061 | Axis A Modes of operation display
* @{
* \brief Object 0x6061 (Axis A Modes of operation display) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6061 = { DEFTYPE_INTEGER8 , 0x08 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6061[] = "Axis A Modes of operation display\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT8 AxisAModesOfOperationDisplay0x6061;
/** @}*/



/******************************************************************************
*                    Object 0x6062 : Axis A Position demand value
******************************************************************************/
/**
* \addtogroup 0x6062 0x6062 | Axis A Position demand value
* @{
* \brief Object 0x6062 (Axis A Position demand value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6062 = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6062[] = "Axis A Position demand value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAPositionDemandValue0x6062;
/** @}*/



/******************************************************************************
*                    Object 0x6063 : Axis A Position actual internalvalue
******************************************************************************/
/**
* \addtogroup 0x6063 0x6063 | Axis A Position actual internalvalue
* @{
* \brief Object 0x6063 (Axis A Position actual internalvalue) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6063 = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6063[] = "Axis A Position actual internalvalue\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAPositionActualInternalvalue0x6063;
/** @}*/



/******************************************************************************
*                    Object 0x6064 : Axis A Position actual value
******************************************************************************/
/**
* \addtogroup 0x6064 0x6064 | Axis A Position actual value
* @{
* \brief Object 0x6064 (Axis A Position actual value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6064 = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6064[] = "Axis A Position actual value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAPositionActualValue0x6064;
/** @}*/



/******************************************************************************
*                    Object 0x6065 : Axis A Following error window
******************************************************************************/
/**
* \addtogroup 0x6065 0x6065 | Axis A Following error window
* @{
* \brief Object 0x6065 (Axis A Following error window) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6065 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6065[] = "Axis A Following error window\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAFollowingErrorWindow0x6065
#if defined(_APP_) && (_APP_ == 1)
= 0x00001388
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6066 : Axis A Following error timeout
******************************************************************************/
/**
* \addtogroup 0x6066 0x6066 | Axis A Following error timeout
* @{
* \brief Object 0x6066 (Axis A Following error timeout) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6066 = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6066[] = "Axis A Following error timeout\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAFollowingErrorTimeout0x6066
#if defined(_APP_) && (_APP_ == 1)
= 0x01F4
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6067 : Axis A Position window
******************************************************************************/
/**
* \addtogroup 0x6067 0x6067 | Axis A Position window
* @{
* \brief Object 0x6067 (Axis A Position window) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6067 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6067[] = "Axis A Position window\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAPositionWindow0x6067
#if defined(_APP_) && (_APP_ == 1)
= 0x00000064
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6068 : Axis A Position window time
******************************************************************************/
/**
* \addtogroup 0x6068 0x6068 | Axis A Position window time
* @{
* \brief Object 0x6068 (Axis A Position window time) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6068 = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6068[] = "Axis A Position window time\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAPositionWindowTime0x6068
#if defined(_APP_) && (_APP_ == 1)
= 0x00C8
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6069 : Axis A Velocity sensor actual value
******************************************************************************/
/**
* \addtogroup 0x6069 0x6069 | Axis A Velocity sensor actual value
* @{
* \brief Object 0x6069 (Axis A Velocity sensor actual value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6069 = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6069[] = "Axis A Velocity sensor actual value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAVelocitySensorActualValue0x6069;
/** @}*/



/******************************************************************************
*                    Object 0x606B : Axis A Velocity demand value
******************************************************************************/
/**
* \addtogroup 0x606B 0x606B | Axis A Velocity demand value
* @{
* \brief Object 0x606B (Axis A Velocity demand value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x606B = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x606B[] = "Axis A Velocity demand value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAVelocityDemandValue0x606B;
/** @}*/



/******************************************************************************
*                    Object 0x606C : Axis A Velocity actual value
******************************************************************************/
/**
* \addtogroup 0x606C 0x606C | Axis A Velocity actual value
* @{
* \brief Object 0x606C (Axis A Velocity actual value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x606C = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x606C[] = "Axis A Velocity actual value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAVelocityActualValue0x606C;
/** @}*/



/******************************************************************************
*                    Object 0x606D : Axis A Velocity window
******************************************************************************/
/**
* \addtogroup 0x606D 0x606D | Axis A Velocity window
* @{
* \brief Object 0x606D (Axis A Velocity window) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x606D = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x606D[] = "Axis A Velocity window\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAVelocityWindow0x606D
#if defined(_APP_) && (_APP_ == 1)
= 0x1388
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x606E : Axis A Velocity window time
******************************************************************************/
/**
* \addtogroup 0x606E 0x606E | Axis A Velocity window time
* @{
* \brief Object 0x606E (Axis A Velocity window time) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x606E = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x606E[] = "Axis A Velocity window time\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAVelocityWindowTime0x606E
#if defined(_APP_) && (_APP_ == 1)
= 0x0064
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x606F : Axis A Velocity threshold
******************************************************************************/
/**
* \addtogroup 0x606F 0x606F | Axis A Velocity threshold
* @{
* \brief Object 0x606F (Axis A Velocity threshold) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x606F = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x606F[] = "Axis A Velocity threshold\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAVelocityThreshold0x606F
#if defined(_APP_) && (_APP_ == 1)
= 0x0BB8
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6070 : Axis A Velocity threshold time
******************************************************************************/
/**
* \addtogroup 0x6070 0x6070 | Axis A Velocity threshold time
* @{
* \brief Object 0x6070 (Axis A Velocity threshold time) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6070 = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6070[] = "Axis A Velocity threshold time\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAVelocityThresholdTime0x6070
#if defined(_APP_) && (_APP_ == 1)
= 0x0064
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6071 : Axis A Target torque
******************************************************************************/
/**
* \addtogroup 0x6071 0x6071 | Axis A Target torque
* @{
* \brief Object 0x6071 (Axis A Target torque) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6071 = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6071[] = "Axis A Target torque\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisATargetTorque0x6071
#if defined(_APP_) && (_APP_ == 1)
= 0x0000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6074 : Axis A Torque demand
******************************************************************************/
/**
* \addtogroup 0x6074 0x6074 | Axis A Torque demand
* @{
* \brief Object 0x6074 (Axis A Torque demand) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6074 = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6074[] = "Axis A Torque demand\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisATorqueDemand0x6074;
/** @}*/



/******************************************************************************
*                    Object 0x6077 : Axis A Torque actual value
******************************************************************************/
/**
* \addtogroup 0x6077 0x6077 | Axis A Torque actual value
* @{
* \brief Object 0x6077 (Axis A Torque actual value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6077 = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6077[] = "Axis A Torque actual value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisATorqueActualValue0x6077;
/** @}*/



/******************************************************************************
*                    Object 0x607A : Axis A Target position
******************************************************************************/
/**
* \addtogroup 0x607A 0x607A | Axis A Target position
* @{
* \brief Object 0x607A (Axis A Target position) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x607A = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x607A[] = "Axis A Target position\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisATargetPosition0x607A
#if defined(_APP_) && (_APP_ == 1)
= 0x00000000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x607C : Axis A Home offset
******************************************************************************/
/**
* \addtogroup 0x607C 0x607C | Axis A Home offset
* @{
* \brief Object 0x607C (Axis A Home offset) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x607C = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x607C[] = "Axis A Home offset\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAHomeOffset0x607C
#if defined(_APP_) && (_APP_ == 1)
= 0x00000000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x607D : Axis A Position_range_limit
******************************************************************************/
/**
* \addtogroup 0x607D 0x607D | Axis A Position_range_limit
* @{
* \brief Object 0x607D (Axis A Position_range_limit) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Min software position limit<br>
* SubIndex 2 - Max software position limit<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x607D[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING }, /* Subindex1 - Min software position limit */
{ DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING }}; /* Subindex2 - Max software position limit */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x607D[] = "Axis A Position_range_limit\000"
"Min software position limit\000"
"Max software position limit\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
INT32 MinSoftwarePositionLimit; /* Subindex1 - Min software position limit */
INT32 MaxSoftwarePositionLimit; /* Subindex2 - Max software position limit */
} OBJ_STRUCT_PACKED_END
TOBJ607D;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ607D AxisAPosition_range_limit0x607D
#if defined(_APP_) && (_APP_ == 1)
={2,0x80000000,0x7FFFFFFF}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x607F : Axis A Max profile velocity
******************************************************************************/
/**
* \addtogroup 0x607F 0x607F | Axis A Max profile velocity
* @{
* \brief Object 0x607F (Axis A Max profile velocity) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x607F = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x607F[] = "Axis A Max profile velocity\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAMaxProfileVelocity0x607F
#if defined(_APP_) && (_APP_ == 1)
= 0x00640000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6081 : Axis A Profile velocity
******************************************************************************/
/**
* \addtogroup 0x6081 0x6081 | Axis A Profile velocity
* @{
* \brief Object 0x6081 (Axis A Profile velocity) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6081 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6081[] = "Axis A Profile velocity\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAProfileVelocity0x6081
#if defined(_APP_) && (_APP_ == 1)
= 0x00140000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6082 : Axis A End Velocity
******************************************************************************/
/**
* \addtogroup 0x6082 0x6082 | Axis A End Velocity
* @{
* \brief Object 0x6082 (Axis A End Velocity) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6082 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6082[] = "Axis A End Velocity\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAEndVelocity0x6082
#if defined(_APP_) && (_APP_ == 1)
= 0x00000000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6083 : Axis A Profile acceleration
******************************************************************************/
/**
* \addtogroup 0x6083 0x6083 | Axis A Profile acceleration
* @{
* \brief Object 0x6083 (Axis A Profile acceleration) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6083 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6083[] = "Axis A Profile acceleration\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAProfileAcceleration0x6083
#if defined(_APP_) && (_APP_ == 1)
= 0x00640000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6084 : Axis A Profile deceleration
******************************************************************************/
/**
* \addtogroup 0x6084 0x6084 | Axis A Profile deceleration
* @{
* \brief Object 0x6084 (Axis A Profile deceleration) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6084 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6084[] = "Axis A Profile deceleration\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAProfileDeceleration0x6084
#if defined(_APP_) && (_APP_ == 1)
= 0x00640000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6085 : Axis A Quick stop_deceleration
******************************************************************************/
/**
* \addtogroup 0x6085 0x6085 | Axis A Quick stop_deceleration
* @{
* \brief Object 0x6085 (Axis A Quick stop_deceleration) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6085 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6085[] = "Axis A Quick stop_deceleration\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAQuickStop_deceleration0x6085
#if defined(_APP_) && (_APP_ == 1)
= 0x00640000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6086 : Axis A Motion profile type
******************************************************************************/
/**
* \addtogroup 0x6086 0x6086 | Axis A Motion profile type
* @{
* \brief Object 0x6086 (Axis A Motion profile type) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6086 = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6086[] = "Axis A Motion profile type\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisAMotionProfileType0x6086
#if defined(_APP_) && (_APP_ == 1)
= 0x0000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6087 : Axis A Torque slope
******************************************************************************/
/**
* \addtogroup 0x6087 0x6087 | Axis A Torque slope
* @{
* \brief Object 0x6087 (Axis A Torque slope) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6087 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6087[] = "Axis A Torque slope\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisATorqueSlope0x6087
#if defined(_APP_) && (_APP_ == 1)
= 0x0000003C
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6088 : Axis A Torque profile type
******************************************************************************/
/**
* \addtogroup 0x6088 0x6088 | Axis A Torque profile type
* @{
* \brief Object 0x6088 (Axis A Torque profile type) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6088 = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6088[] = "Axis A Torque profile type\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisATorqueProfileType0x6088
#if defined(_APP_) && (_APP_ == 1)
= 0x0000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6091 : Axis A Gear ratio
******************************************************************************/
/**
* \addtogroup 0x6091 0x6091 | Axis A Gear ratio
* @{
* \brief Object 0x6091 (Axis A Gear ratio) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Motor Revolutions<br>
* SubIndex 2 - Load Shaft Revolutions<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x6091[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING }, /* Subindex1 - Motor Revolutions */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING }}; /* Subindex2 - Load Shaft Revolutions */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x6091[] = "Axis A Gear ratio\000"
"Motor Revolutions\000"
"Load Shaft Revolutions\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT32 MotorRevolutions; /* Subindex1 - Motor Revolutions */
UINT32 LoadShaftRevolutions; /* Subindex2 - Load Shaft Revolutions */
} OBJ_STRUCT_PACKED_END
TOBJ6091;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ6091 AxisAGearRatio0x6091
#if defined(_APP_) && (_APP_ == 1)
={2,0x00000001,0x00000001}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6092 : Axis A Feed constant
******************************************************************************/
/**
* \addtogroup 0x6092 0x6092 | Axis A Feed constant
* @{
* \brief Object 0x6092 (Axis A Feed constant) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Feed<br>
* SubIndex 2 - Shaft Revolutions<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x6092[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_TXPDOMAPPING }, /* Subindex1 - Feed */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_TXPDOMAPPING }}; /* Subindex2 - Shaft Revolutions */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x6092[] = "Axis A Feed constant\000"
"Feed\000"
"Shaft Revolutions\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT32 Feed; /* Subindex1 - Feed */
UINT32 ShaftRevolutions; /* Subindex2 - Shaft Revolutions */
} OBJ_STRUCT_PACKED_END
TOBJ6092;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ6092 AxisAFeedConstant0x6092
#if defined(_APP_) && (_APP_ == 1)
={2,0x00000001,0x00000001}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6098 : Axis A Homing method
******************************************************************************/
/**
* \addtogroup 0x6098 0x6098 | Axis A Homing method
* @{
* \brief Object 0x6098 (Axis A Homing method) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x6098 = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x6098[] = "Axis A Homing method\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisAHomingMethod0x6098
#if defined(_APP_) && (_APP_ == 1)
= 0x0000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x6099 : Axis A Homing speeds
******************************************************************************/
/**
* \addtogroup 0x6099 0x6099 | Axis A Homing speeds
* @{
* \brief Object 0x6099 (Axis A Homing speeds) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Fast Homing Speed<br>
* SubIndex 2 - Slow Homing Speed<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x6099[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING }, /* Subindex1 - Fast Homing Speed */
{ DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING }}; /* Subindex2 - Slow Homing Speed */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x6099[] = "Axis A Homing speeds\000"
"Fast Homing Speed\000"
"Slow Homing Speed\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
UINT32 FastHomingSpeed; /* Subindex1 - Fast Homing Speed */
UINT32 SlowHomingSpeed; /* Subindex2 - Slow Homing Speed */
} OBJ_STRUCT_PACKED_END
TOBJ6099;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ6099 AxisAHomingSpeeds0x6099
#if defined(_APP_) && (_APP_ == 1)
={2,0x0000C350,0x00004E20}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x609A : Axis A Homing acceleration
******************************************************************************/
/**
* \addtogroup 0x609A 0x609A | Axis A Homing acceleration
* @{
* \brief Object 0x609A (Axis A Homing acceleration) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x609A = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x609A[] = "Axis A Homing acceleration\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAHomingAcceleration0x609A
#if defined(_APP_) && (_APP_ == 1)
= 0x0000C350
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60A4 : Axis A Profile jerk
******************************************************************************/
/**
* \addtogroup 0x60A4 0x60A4 | Axis A Profile jerk
* @{
* \brief Object 0x60A4 (Axis A Profile jerk) definition
*/
#ifdef _OBJD_
/**
* \brief Object entry descriptions<br>
* <br>
* SubIndex 0<br>
* SubIndex 1 - Profile Jerk 1<br>
* SubIndex 2 - Profile Jerk 2<br>
* SubIndex 3 - Profile Jerk 3<br>
* SubIndex 4 - Profile Jerk 4<br>
* SubIndex 5 - Profile Jerk 5<br>
* SubIndex 6 - Profile Jerk 6<br>
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM asEntryDesc0x60A4[] = {
{ DEFTYPE_UNSIGNED8 , 0x8 , ACCESS_READ },
{ DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE }, /* Subindex1 - Profile Jerk 1 */
{ DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE }, /* Subindex2 - Profile Jerk 2 */
{ DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE }, /* Subindex3 - Profile Jerk 3 */
{ DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE }, /* Subindex4 - Profile Jerk 4 */
{ DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE }, /* Subindex5 - Profile Jerk 5 */
{ DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE }}; /* Subindex6 - Profile Jerk 6 */

/**
* \brief Object/Entry names
*/
OBJCONST UCHAR OBJMEM aName0x60A4[] = "Axis A Profile jerk\000"
"Profile Jerk 1\000"
"Profile Jerk 2\000"
"Profile Jerk 3\000"
"Profile Jerk 4\000"
"Profile Jerk 5\000"
"Profile Jerk 6\000\377";
#endif //#ifdef _OBJD_

#ifndef _APP_OBJECTS_H_
/**
* \brief Object structure
*/
typedef struct OBJ_STRUCT_PACKED_START {
UINT16 u16SubIndex0;
INT32 ProfileJerk1; /* Subindex1 - Profile Jerk 1 */
INT32 ProfileJerk2; /* Subindex2 - Profile Jerk 2 */
INT32 ProfileJerk3; /* Subindex3 - Profile Jerk 3 */
INT32 ProfileJerk4; /* Subindex4 - Profile Jerk 4 */
INT32 ProfileJerk5; /* Subindex5 - Profile Jerk 5 */
INT32 ProfileJerk6; /* Subindex6 - Profile Jerk 6 */
} OBJ_STRUCT_PACKED_END
TOBJ60A4;
#endif //#ifndef _APP_OBJECTS_H_

/**
* \brief Object variable
*/
PROTO TOBJ60A4 AxisAProfileJerk0x60A4
#if defined(_APP_) && (_APP_ == 1)
={6,0x00000000,0x00000000,0x00000000,0x00000000,0x00000000,0x00000000}
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60B0 : Axis A Position offset
******************************************************************************/
/**
* \addtogroup 0x60B0 0x60B0 | Axis A Position offset
* @{
* \brief Object 0x60B0 (Axis A Position offset) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60B0 = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60B0[] = "Axis A Position offset\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAPositionOffset0x60B0
#if defined(_APP_) && (_APP_ == 1)
= 0x00000000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60B1 : Axis A Velocity offset
******************************************************************************/
/**
* \addtogroup 0x60B1 0x60B1 | Axis A Velocity offset
* @{
* \brief Object 0x60B1 (Axis A Velocity offset) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60B1 = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60B1[] = "Axis A Velocity offset\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAVelocityOffset0x60B1
#if defined(_APP_) && (_APP_ == 1)
= 0x00000000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60B2 : Axis A Torque offset
******************************************************************************/
/**
* \addtogroup 0x60B2 0x60B2 | Axis A Torque offset
* @{
* \brief Object 0x60B2 (Axis A Torque offset) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60B2 = { DEFTYPE_INTEGER16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60B2[] = "Axis A Torque offset\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT16 AxisATorqueOffset0x60B2
#if defined(_APP_) && (_APP_ == 1)
= 0x0000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60C5 : Axis A Max acceleration
******************************************************************************/
/**
* \addtogroup 0x60C5 0x60C5 | Axis A Max acceleration
* @{
* \brief Object 0x60C5 (Axis A Max acceleration) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60C5 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60C5[] = "Axis A Max acceleration\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAMaxAcceleration0x60C5
#if defined(_APP_) && (_APP_ == 1)
= 0x03E80000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60C6 : Axis A Max deceleration
******************************************************************************/
/**
* \addtogroup 0x60C6 0x60C6 | Axis A Max deceleration
* @{
* \brief Object 0x60C6 (Axis A Max deceleration) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60C6 = { DEFTYPE_UNSIGNED32 , 0x20 , ACCESS_READWRITE | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60C6[] = "Axis A Max deceleration\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT32 AxisAMaxDeceleration0x60C6
#if defined(_APP_) && (_APP_ == 1)
= 0x03E80000
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60E0 : Axis A Positive Torque Limit Value
******************************************************************************/
/**
* \addtogroup 0x60E0 0x60E0 | Axis A Positive Torque Limit Value
* @{
* \brief Object 0x60E0 (Axis A Positive Torque Limit Value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60E0 = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60E0[] = "Axis A Positive Torque Limit Value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisAPositiveTorqueLimitValue0x60E0
#if defined(_APP_) && (_APP_ == 1)
= 0x07D0
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60E1 : Axis A Negative Torque Limit Value
******************************************************************************/
/**
* \addtogroup 0x60E1 0x60E1 | Axis A Negative Torque Limit Value
* @{
* \brief Object 0x60E1 (Axis A Negative Torque Limit Value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60E1 = { DEFTYPE_UNSIGNED16 , 0x10 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60E1[] = "Axis A Negative Torque Limit Value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO UINT16 AxisANegativeTorqueLimitValue0x60E1
#if defined(_APP_) && (_APP_ == 1)
= 0x07D0
#endif
;
/** @}*/



/******************************************************************************
*                    Object 0x60F4 : Axis A Following error actual value
******************************************************************************/
/**
* \addtogroup 0x60F4 0x60F4 | Axis A Following error actual value
* @{
* \brief Object 0x60F4 (Axis A Following error actual value) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60F4 = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READ | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60F4[] = "Axis A Following error actual value\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAFollowingErrorActualValue0x60F4;
/** @}*/



/******************************************************************************
*                    Object 0x60FC : Axis A Position demand internalvalue
******************************************************************************/
/**
* \addtogroup 0x60FC 0x60FC | Axis A Position demand internalvalue
* @{
* \brief Object 0x60FC (Axis A Position demand internalvalue) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60FC = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READ | OBJACCESS_TXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60FC[] = "Axis A Position demand internalvalue\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisAPositionDemandInternalvalue0x60FC;
/** @}*/



/******************************************************************************
*                    Object 0x60FF : Axis A Target velocity
******************************************************************************/
/**
* \addtogroup 0x60FF 0x60FF | Axis A Target velocity
* @{
* \brief Object 0x60FF (Axis A Target velocity) definition
*/
#ifdef _OBJD_
/**
* \brief Entry description
*/
OBJCONST TSDOINFOENTRYDESC    OBJMEM sEntryDesc0x60FF = { DEFTYPE_INTEGER32 , 0x20 , ACCESS_READWRITE | OBJACCESS_RXPDOMAPPING };
/**
* \brief Object name
*/
OBJCONST UCHAR OBJMEM aName0x60FF[] = "Axis A Target velocity\000\377";
#endif //#ifdef _OBJD_

/**
* \brief Object variable
*/
PROTO INT32 AxisATargetVelocity0x60FF
#if defined(_APP_) && (_APP_ == 1)
= 0x00000000
#endif
;
/** @}*/







#ifdef _OBJD_
TOBJECT    OBJMEM ApplicationObjDic[] = {
/* Object 0x1600 */
{NULL , NULL ,  0x1600 , {DEFTYPE_PDOMAPPING , 10 | (OBJCODE_REC << 8)} , asEntryDesc0x1600 , aName0x1600 , &RxPdo1AxisA0x1600 , NULL , NULL , 0x0000 },
/* Object 0x1A00 */
{NULL , NULL ,  0x1A00 , {DEFTYPE_PDOMAPPING , 10 | (OBJCODE_REC << 8)} , asEntryDesc0x1A00 , aName0x1A00 , &TxPdo1AxisA0x1A00 , NULL , NULL , 0x0000 },
/* Object 0x1C12 */
{NULL , NULL ,  0x1C12 , {DEFTYPE_UNSIGNED16 , 1 | (OBJCODE_ARR << 8)} , asEntryDesc0x1C12 , aName0x1C12 , &sRxPDOassign , NULL , NULL , 0x0000 },
/* Object 0x1C13 */
{NULL , NULL ,  0x1C13 , {DEFTYPE_UNSIGNED16 , 1 | (OBJCODE_ARR << 8)} , asEntryDesc0x1C13 , aName0x1C13 , &sTxPDOassign , NULL , NULL , 0x0000 },
/* Object 0x2000 */
{NULL , NULL ,  0x2000 , {DEFTYPE_RECORD , 22 | (OBJCODE_REC << 8)} , asEntryDesc0x2000 , aName0x2000 , &AxisAMotorParameters0x2000 , NULL , NULL , 0x0000 },
/* Object 0x2001 */
{NULL , NULL ,  0x2001 , {DEFTYPE_RECORD , 27 | (OBJCODE_REC << 8)} , asEntryDesc0x2001 , aName0x2001 , &AxisABasicConfigurationParameters0x2001 , NULL , NULL , 0x0000 },
/* Object 0x2002 */
{NULL , NULL ,  0x2002 , {DEFTYPE_RECORD , 39 | (OBJCODE_REC << 8)} , asEntryDesc0x2002 , aName0x2002 , &AxisAMotionControlParameter0x2002 , NULL , NULL , 0x0000 },
/* Object 0x2003 */
{NULL , NULL ,  0x2003 , {DEFTYPE_RECORD , 15 | (OBJCODE_REC << 8)} , asEntryDesc0x2003 , aName0x2003 , &AxisAAdvancedConfigurationParameters0x2003 , NULL , NULL , 0x0000 },
/* Object 0x2004 */
{NULL , NULL ,  0x2004 , {DEFTYPE_RECORD , 12 | (OBJCODE_REC << 8)} , asEntryDesc0x2004 , aName0x2004 , &AxisADigitalInputParameter0x2004 , NULL , NULL , 0x0000 },
/* Object 0x2005 */
{NULL , NULL ,  0x2005 , {DEFTYPE_RECORD , 12 | (OBJCODE_REC << 8)} , asEntryDesc0x2005 , aName0x2005 , &AxisADigitalOutputParameter0x2005 , NULL , NULL , 0x0000 },
/* Object 0x2006 */
{NULL , NULL ,  0x2006 , {DEFTYPE_RECORD , 10 | (OBJCODE_REC << 8)} , asEntryDesc0x2006 , aName0x2006 , &AxisAFaultProtectionParameter0x2006 , NULL , NULL , 0x0000 },
/* Object 0x2007 */
{NULL , NULL ,  0x2007 , {DEFTYPE_RECORD , 8 | (OBJCODE_REC << 8)} , asEntryDesc0x2007 , aName0x2007 , &AxisAAuxiliaryParameter0x2007 , NULL , NULL , 0x0000 },
/* Object 0x6007 */
{NULL , NULL ,  0x6007 , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6007 , aName0x6007 , &AxisACANopenAbortOptionCode0x6007 , NULL , NULL , 0x0000 },
/* Object 0x603F */
{NULL , NULL ,  0x603F , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x603F , aName0x603F , &AxisALastErrorCode0x603F , NULL , NULL , 0x0000 },
/* Object 0x6040 */
{NULL , NULL ,  0x6040 , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6040 , aName0x6040 , &AxisAControlWord0x6040 , NULL , NULL , 0x0000 },
/* Object 0x6041 */
{NULL , NULL ,  0x6041 , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6041 , aName0x6041 , &AxisAStatusword0x6041 , NULL , NULL , 0x0000 },
/* Object 0x605A */
{NULL , NULL ,  0x605A , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x605A , aName0x605A , &AxisAQuickStopOptionCode0x605A , NULL , NULL , 0x0000 },
/* Object 0x605B */
{NULL , NULL ,  0x605B , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x605B , aName0x605B , &AxisAShutdownOptionCode0x605B , NULL , NULL , 0x0000 },
/* Object 0x605C */
{NULL , NULL ,  0x605C , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x605C , aName0x605C , &AxisADisableOperationOptionCode0x605C , NULL , NULL , 0x0000 },
/* Object 0x605D */
{NULL , NULL ,  0x605D , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x605D , aName0x605D , &AxisAHaltOptionCode0x605D , NULL , NULL , 0x0000 },
/* Object 0x605E */
{NULL , NULL ,  0x605E , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x605E , aName0x605E , &AxisAFaultReactionCode0x605E , NULL , NULL , 0x0000 },
/* Object 0x6060 */
{NULL , NULL ,  0x6060 , {DEFTYPE_INTEGER8 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6060 , aName0x6060 , &AxisAModesOfOperation0x6060 , NULL , NULL , 0x0000 },
/* Object 0x6061 */
{NULL , NULL ,  0x6061 , {DEFTYPE_INTEGER8 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6061 , aName0x6061 , &AxisAModesOfOperationDisplay0x6061 , NULL , NULL , 0x0000 },
/* Object 0x6062 */
{NULL , NULL ,  0x6062 , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6062 , aName0x6062 , &AxisAPositionDemandValue0x6062 , NULL , NULL , 0x0000 },
/* Object 0x6063 */
{NULL , NULL ,  0x6063 , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6063 , aName0x6063 , &AxisAPositionActualInternalvalue0x6063 , NULL , NULL , 0x0000 },
/* Object 0x6064 */
{NULL , NULL ,  0x6064 , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6064 , aName0x6064 , &AxisAPositionActualValue0x6064 , NULL , NULL , 0x0000 },
/* Object 0x6065 */
{NULL , NULL ,  0x6065 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6065 , aName0x6065 , &AxisAFollowingErrorWindow0x6065 , NULL , NULL , 0x0000 },
/* Object 0x6066 */
{NULL , NULL ,  0x6066 , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6066 , aName0x6066 , &AxisAFollowingErrorTimeout0x6066 , NULL , NULL , 0x0000 },
/* Object 0x6067 */
{NULL , NULL ,  0x6067 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6067 , aName0x6067 , &AxisAPositionWindow0x6067 , NULL , NULL , 0x0000 },
/* Object 0x6068 */
{NULL , NULL ,  0x6068 , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6068 , aName0x6068 , &AxisAPositionWindowTime0x6068 , NULL , NULL , 0x0000 },
/* Object 0x6069 */
{NULL , NULL ,  0x6069 , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6069 , aName0x6069 , &AxisAVelocitySensorActualValue0x6069 , NULL , NULL , 0x0000 },
/* Object 0x606B */
{NULL , NULL ,  0x606B , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x606B , aName0x606B , &AxisAVelocityDemandValue0x606B , NULL , NULL , 0x0000 },
/* Object 0x606C */
{NULL , NULL ,  0x606C , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x606C , aName0x606C , &AxisAVelocityActualValue0x606C , NULL , NULL , 0x0000 },
/* Object 0x606D */
{NULL , NULL ,  0x606D , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x606D , aName0x606D , &AxisAVelocityWindow0x606D , NULL , NULL , 0x0000 },
/* Object 0x606E */
{NULL , NULL ,  0x606E , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x606E , aName0x606E , &AxisAVelocityWindowTime0x606E , NULL , NULL , 0x0000 },
/* Object 0x606F */
{NULL , NULL ,  0x606F , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x606F , aName0x606F , &AxisAVelocityThreshold0x606F , NULL , NULL , 0x0000 },
/* Object 0x6070 */
{NULL , NULL ,  0x6070 , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6070 , aName0x6070 , &AxisAVelocityThresholdTime0x6070 , NULL , NULL , 0x0000 },
/* Object 0x6071 */
{NULL , NULL ,  0x6071 , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6071 , aName0x6071 , &AxisATargetTorque0x6071 , NULL , NULL , 0x0000 },
/* Object 0x6074 */
{NULL , NULL ,  0x6074 , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6074 , aName0x6074 , &AxisATorqueDemand0x6074 , NULL , NULL , 0x0000 },
/* Object 0x6077 */
{NULL , NULL ,  0x6077 , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6077 , aName0x6077 , &AxisATorqueActualValue0x6077 , NULL , NULL , 0x0000 },
/* Object 0x607A */
{NULL , NULL ,  0x607A , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x607A , aName0x607A , &AxisATargetPosition0x607A , NULL , NULL , 0x0000 },
/* Object 0x607C */
{NULL , NULL ,  0x607C , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x607C , aName0x607C , &AxisAHomeOffset0x607C , NULL , NULL , 0x0000 },
/* Object 0x607D */
{NULL , NULL ,  0x607D , {DEFTYPE_RECORD , 2 | (OBJCODE_REC << 8)} , asEntryDesc0x607D , aName0x607D , &AxisAPosition_range_limit0x607D , NULL , NULL , 0x0000 },
/* Object 0x607F */
{NULL , NULL ,  0x607F , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x607F , aName0x607F , &AxisAMaxProfileVelocity0x607F , NULL , NULL , 0x0000 },
/* Object 0x6081 */
{NULL , NULL ,  0x6081 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6081 , aName0x6081 , &AxisAProfileVelocity0x6081 , NULL , NULL , 0x0000 },
/* Object 0x6082 */
{NULL , NULL ,  0x6082 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6082 , aName0x6082 , &AxisAEndVelocity0x6082 , NULL , NULL , 0x0000 },
/* Object 0x6083 */
{NULL , NULL ,  0x6083 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6083 , aName0x6083 , &AxisAProfileAcceleration0x6083 , NULL , NULL , 0x0000 },
/* Object 0x6084 */
{NULL , NULL ,  0x6084 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6084 , aName0x6084 , &AxisAProfileDeceleration0x6084 , NULL , NULL , 0x0000 },
/* Object 0x6085 */
{NULL , NULL ,  0x6085 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6085 , aName0x6085 , &AxisAQuickStop_deceleration0x6085 , NULL , NULL , 0x0000 },
/* Object 0x6086 */
{NULL , NULL ,  0x6086 , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6086 , aName0x6086 , &AxisAMotionProfileType0x6086 , NULL , NULL , 0x0000 },
/* Object 0x6087 */
{NULL , NULL ,  0x6087 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6087 , aName0x6087 , &AxisATorqueSlope0x6087 , NULL , NULL , 0x0000 },
/* Object 0x6088 */
{NULL , NULL ,  0x6088 , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6088 , aName0x6088 , &AxisATorqueProfileType0x6088 , NULL , NULL , 0x0000 },
/* Object 0x6091 */
{NULL , NULL ,  0x6091 , {DEFTYPE_RECORD , 2 | (OBJCODE_REC << 8)} , asEntryDesc0x6091 , aName0x6091 , &AxisAGearRatio0x6091 , NULL , NULL , 0x0000 },
/* Object 0x6092 */
{NULL , NULL ,  0x6092 , {DEFTYPE_RECORD , 2 | (OBJCODE_REC << 8)} , asEntryDesc0x6092 , aName0x6092 , &AxisAFeedConstant0x6092 , NULL , NULL , 0x0000 },
/* Object 0x6098 */
{NULL , NULL ,  0x6098 , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x6098 , aName0x6098 , &AxisAHomingMethod0x6098 , NULL , NULL , 0x0000 },
/* Object 0x6099 */
{NULL , NULL ,  0x6099 , {DEFTYPE_RECORD , 2 | (OBJCODE_REC << 8)} , asEntryDesc0x6099 , aName0x6099 , &AxisAHomingSpeeds0x6099 , NULL , NULL , 0x0000 },
/* Object 0x609A */
{NULL , NULL ,  0x609A , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x609A , aName0x609A , &AxisAHomingAcceleration0x609A , NULL , NULL , 0x0000 },
/* Object 0x60A4 */
{NULL , NULL ,  0x60A4 , {DEFTYPE_RECORD , 6 | (OBJCODE_REC << 8)} , asEntryDesc0x60A4 , aName0x60A4 , &AxisAProfileJerk0x60A4 , NULL , NULL , 0x0000 },
/* Object 0x60B0 */
{NULL , NULL ,  0x60B0 , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60B0 , aName0x60B0 , &AxisAPositionOffset0x60B0 , NULL , NULL , 0x0000 },
/* Object 0x60B1 */
{NULL , NULL ,  0x60B1 , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60B1 , aName0x60B1 , &AxisAVelocityOffset0x60B1 , NULL , NULL , 0x0000 },
/* Object 0x60B2 */
{NULL , NULL ,  0x60B2 , {DEFTYPE_INTEGER16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60B2 , aName0x60B2 , &AxisATorqueOffset0x60B2 , NULL , NULL , 0x0000 },
/* Object 0x60C5 */
{NULL , NULL ,  0x60C5 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60C5 , aName0x60C5 , &AxisAMaxAcceleration0x60C5 , NULL , NULL , 0x0000 },
/* Object 0x60C6 */
{NULL , NULL ,  0x60C6 , {DEFTYPE_UNSIGNED32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60C6 , aName0x60C6 , &AxisAMaxDeceleration0x60C6 , NULL , NULL , 0x0000 },
/* Object 0x60E0 */
{NULL , NULL ,  0x60E0 , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60E0 , aName0x60E0 , &AxisAPositiveTorqueLimitValue0x60E0 , NULL , NULL , 0x0000 },
/* Object 0x60E1 */
{NULL , NULL ,  0x60E1 , {DEFTYPE_UNSIGNED16 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60E1 , aName0x60E1 , &AxisANegativeTorqueLimitValue0x60E1 , NULL , NULL , 0x0000 },
/* Object 0x60F4 */
{NULL , NULL ,  0x60F4 , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60F4 , aName0x60F4 , &AxisAFollowingErrorActualValue0x60F4 , NULL , NULL , 0x0000 },
/* Object 0x60FC */
{NULL , NULL ,  0x60FC , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60FC , aName0x60FC , &AxisAPositionDemandInternalvalue0x60FC , NULL , NULL , 0x0000 },
/* Object 0x60FF */
{NULL , NULL ,  0x60FF , {DEFTYPE_INTEGER32 , 0 | (OBJCODE_VAR << 8)} , &sEntryDesc0x60FF , aName0x60FF , &AxisATargetVelocity0x60FF , NULL , NULL , 0x0000 },
{NULL,NULL, 0xFFFF, {0, 0}, NULL, NULL, NULL, NULL}};
#endif    //#ifdef _OBJD_

#undef PROTO

/** @}*/
#define _APP_OBJECTS_H_
