/****************************************************************************************************
 *
 * FILE NAME:  RemVibFreq.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.30
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	30-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _REM_VIB_FREQ_H_
#define _REM_VIB_FREQ_H_

#include "BaseDef.h"


/****************************************************************************************************/
/*		STRUCT DEFINITION																			*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Data type definition for residual vibration frequency calculation							*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{				
	struct
	{							
		INT32	RemVibDetWidth;					/* Residual vibration frequency detection width [pulse] */
		INT32	RemVibFil;						/* Position deviation filter frequency for residual vibration frequency detection[Hz]*/
	} conf;

	struct
	{
		INT16	State;							/* Status 											*/
		INT16	Dir;							/* Current actual movement direction 				*/
		INT16	RevCnt;							/* Number of inversions								*/
		INT16	Dummy;							/* For Alignment									*/
		UINT32	TimeOutCtr;						/* Timeout counter 									*/
		UINT32	PeakCntTimer;					/* Timer for measuring residual vibration time		*/
		UINT32	PeakCnt;						/* Residual vibration time counter: Present			*/
		UINT32	LastPeakCnt;					/* Residual vibration time counter :1 time before	*/
		UINT32	LLastpeakCnt;					/* Residual vibration time counter 2 times before (one before in the same direction)*/
		INT32	PeakValue;						/* Overshoot amount	[pulse]							*/
		UINT32	FreqCnt;						/* Residual vibration frequency counter				*/
		INT32	LastPosErr;						/* Last position deviation				[pulse]		*/
		INT32	PosErrFilOut;					/* Position deviation after filtering [pulse]		*/
		INT32	PosErrFilOut2;					/* Position deviation after filtering [pulse]		*/
		UINT32	MeanDeno;						/* Weighted averaging denominator 					*/
	} var;

	struct
	{							
		INT32	UnRvibFrequency;				/* Residual vibration frequency		[0.1Hz]			*/
	} mon;
} REMVIBFREQ;



/****************************************************************************************************/
/*		DEFINES																						*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Definition for residual vibration detection													*/
/*--------------------------------------------------------------------------------------------------*/
#define REMVIB_CYCLEMS		(TASKC_CYCLEMS)			/* Minimum output frequency counter value(1Hz)	*/
#define REMVIB_MINOUT		(1000/REMVIB_CYCLEMS)	/* Minimum output frequency counter value(1Hz)	*/
#define REMVIB_MINDET		(REMVIB_MINOUT*12/10)	/* Minimum detection frequency counter value(1.2Hz) */
#define REMVIB_MINDETWIDTH	3						/* Minimum detection width		[pulse]			*/
#define REMVIB_FILFREQ		5000					/* Residual vibration detection filter frequency[0.1Hz] */
#define REMVIB_WEIGHT_NUME	12						/* Averaging weighted numerator					*/
#define REMVIB_WEIGHT_DENO	10						/* Averaging weighted denominator 				*/
#define REMVIB_DENO_OFFSET	1000					/* If the unit of averaging weight integration is 100, then 1/100*/
#define REMVIB_TIMEOUT		(REMVIB_MINDET/REMVIB_CYCLEMS)
													/* Timeout time	[ms]							*/
/*--------------------------------------------------------------------------------------------------*/
/*	Residual vibration detection sequence definition												*/
/*--------------------------------------------------------------------------------------------------*/
enum REMVIBSEQ
{
	RVM_INIT		= 0,					/* Initialization										*/
	RVM_COIN,								/* Waiting for positioning completion					*/
	RVM_PEAKCHK_F,							/* Peak detection (before vibration frequency detection) */
	RVM_PEAKCHK_L,							/* Peak detection (start of vibration frequency detection) */
	RVM_COMP,								/* Vibration frequency calculation						*/
};



/****************************************************************************************************/
/*		PROTOTYPE																					*/
/****************************************************************************************************/
PUBLIC void RemVibChkCondition( REMVIBFREQ *RemVibFreq, BOOL PosCtrlMode );
PUBLIC void RemVibCalculateFreq( REMVIBFREQ *RemVibFreq, INT32 CtrlPosErr, BOOL CoinSts, BOOL RefzSts );

/****************************************************************************************************/
/*		API																							*/
/****************************************************************************************************/
PUBLIC INT32 RemVibGetRemVibFreq( REMVIBFREQ *RemVibFreq );
PUBLIC void RemVibCalculateMonitorPrm( REMVIBFREQ *RemVibFreq, 
    	                           					  UINT16 remdetw, INT32 CoinLevel, INT32 ScanTime );
PUBLIC void RemVibRsetRemVibrationFreq( REMVIBFREQ *RemVibFreq );


#endif
