/****************************************************************************************************
 *
 * FILE NAME:  PcmdFilter.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.05.26
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	26-05-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _PCMD_FILTER_H_
#define _PCMD_FILTER_H_
	
#include "BaseDef.h"
#include "Mlib.h"

#define  MAFIL_BUFNUM			256				        // Number of moving average filter buffers	
#define  SUMX_LIM				0x3FFFFFFF				
#define  MAXOMEGA2(AvfCycleNs)	(127323954/AvfCycleNs)	    // 127323954 = 10^9 * 0.8 / (2*PAI)	
					

/*--------------------------------------------------------------------------------------------------*/
/*		Parameter variable definition for exponential acceleration / deceleration filter			*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{					
	struct
	{							
		INT32	Kf[2];							// Exponential acceleration / deceleration filter gain
		INT32	Pbias[2];						// Exponential acceleration / deceleration filter bias		
	} conf;

	struct
	{							
		INT32	Filo;							// Filter output
		PEXFV	Pexfvar;						// Variable structure for exponential acceleration / deceleration filter calculation
		BOOL	RefZStatus;						// completion status				
	} var;
} EXPFIL;


/*--------------------------------------------------------------------------------------------------*/
/*		Parameter definition for moving average filter												*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{					
	struct	
	{							
		INT32	PmafNum[2];						// Number of moving averages
		UINT8	ImafSft[2];						// Number of interpolation moving averages(2^Sft)
		UINT8	SpareByte[2];					// Reserved						

		INT32	PmafNumSec[2];					// Moving average 2 times							
		UINT8	ImafSftSec[2];					// Interpolation moving average 2 times(2^Sft)
		UINT8	SpareByteSec[2];				// Reserved 2					
	} conf;

	struct
	{						
		INT32	PcmdInX;						// For long-term interpolation moving average filter calculation
		INT32	PcmdInY;						// For long-term interpolation moving average filter calculation
		INT32	Index;							// For interpolation moving average filter calculation
		INT32	Filo;							// Filter output										
		BOOL	RefZStatus;						// completion status									
		PMAFV	Pmafvar;						// Variable structure for moving average filter calculation
		PIMFV	Pimfvar;						// Variable structure for interpolation moving average filter calculatio
		INT32	Pmafbuf[ MAFIL_BUFNUM ];		// Buffer for moving average filter calculation
		INT32	PcmdInXSec;						// For long-term interpolation moving average filter 2 calculation					*/
		INT32	PcmdInYSec;						// For long-term interpolation moving average filter 2 calculation					
		INT32	IndexSec;						// Interpolation moving average filter 2 calculation						
		INT32	FiloSec;						// Filter 2 output								
		HIGHFV	PmafvarSec;						// Moving average filter 2 calculation variable structure					
		HIMFV	PimfvarSec;						// Interpolation moving average filter 2 calculation variable structure				
		INT32	PmafbufSec[ MAFIL_BUFNUM ];		// Moving average filter 2 calculation buffer					
	} var;
} MAFIL;


/*--------------------------------------------------------------------------------------------------*/
/*		Position command filter variable definition													*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{				
	EXPFIL	ExpFil;								// Variable for acceleration / deceleration filter	
	MAFIL	MaFil;								// Variable for moving average filter				
} PCMDFIL;


/*--------------------------------------------------------------------------------------------------*/
/*		Parameter definition for vibration suppression filter					  				    */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{					
	INT32			Kff1;						// Vibration suppression filter gain 1			
	INT32			Kff2;						// Vibration suppression filter gain 2					
	INT32			Kff3;						// Vibration suppression filter gain 3					
	BOOL			enable;						// Vibration suppression filter function selection	
} VIBSUPFILPRM;

typedef	struct 
{					
	struct 
	{								
		VIBSUPFILPRM	VibSupPrm;				// Vibration suppression filter parameters		
		VIBSUPFILPRM	Pexe;					// Parameters for vibration suppression filter for execution	
		BOOL			VibSupSetting;			// Vibration suppression filter setting				
	} conf;

	struct
	{									
		INT32			rem1;					// Vibration suppression filter calculation					
		INT32			rem2;					// Vibration suppression filter calculation						
		INT32			rem3;					// Vibration suppression filter calculation					
		INT32			wkbf1;					// Vibration suppression filter calculation				
		INT32			wkbf2;					// Vibration suppression filter calculation					
		INT32			FilioErr[2];			// Vibration suppression filterd eviation between input and output
		INT32			Buf;					// Vibration suppression filter buffer					
		INT32			Filo;					// Vibration suppression filter output					
	} var;
} VIBSUPFIL;



/****************************************************************************************************/
/*		PROTOTYPE																					*/
/****************************************************************************************************/
void	PcmdFilInitialize( PCMDFIL *PcmdFil, VIBSUPFIL *VibSupFil );
void	FFCmdFilInitialize( MAFIL *pMaFil );								
INT32	FFcmdFilMoveAverageFilter( MAFIL *pMaFil, INT32 FFcmdIn, BOOL FilStop );	
INT32	PcmdFilRuntimeService( PCMDFIL *PcmdFil, INT32 PcmdIn, INT32 FilRest, BOOL FilStop );

void	PcmdFilCalculatePrmExpFilter( EXPFIL *ExpFil, UINT16 PrmTime, REAL32 Kmotpls,
													 REAL32 MaxVel, INT32 SvCycleUs );
void PcmdFilCalculatePrmExpFilBias( EXPFIL *ExpFil, UINT16 expbias1, INT32 SvCycleUs );

void	PcmdFilCalculatePrmMoveAvergeFilter( MAFIL *MaFil, UINT16 PrmTime, 
														   UINT16 Highrad, INT32 SvCycleUs );
void    PcmdFilCalculatePrmHighFilter( MAFIL *MaFil, UINT16 PrmTime, 
												 UINT16 Highrad, INT32 SvCycleUs );

void	PcmdFilSetRefZStatus( PCMDFIL *PcmdFil, BOOL status );
BOOL	PcmdFilGetRefZStatus( PCMDFIL *PcmdFil );

INT32	PcmdFilVibSupFilter( VIBSUPFIL *VibSupFil, INT32 dPcmd , BOOL *RefZSignal );
BOOL	PcmdFilCalculatePrmVibSupFilSW( VIBSUPFIL *VibSupFil, UINT32 mdlsw,
												    UINT32 ff_feq, UINT32 ff_fil, INT32 ScanTimeNs );
void	PcmdFilCalculatePrmVibSupFil( VIBSUPFIL *VibSupFil, UINT32 ff_frq, UINT32 ff_fil, INT32 ScanTimeNs );


#endif  // end _PCMD_FILTER_H_


