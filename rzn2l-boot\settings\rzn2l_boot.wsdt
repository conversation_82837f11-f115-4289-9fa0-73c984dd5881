<?xml version="1.0"?>
<Workspace>
    <ConfigDictionary>
        <CurrentConfigs>
            <Project>rzn2l_boot/Debug</Project>
        </CurrentConfigs>
    </ConfigDictionary>
    <WindowStorage>
        <ChildIdMap>
            <TB_CMSISPACK>34048</TB_CMSISPACK>
            <TB_MAIN2>34049</TB_MAIN2>
            <WIN_BREAKPOINTS>34050</WIN_BREAKPOINTS>
            <WIN_BUILD>34051</WIN_BUILD>
            <WIN_CALL_GRAPH>34052</WIN_CALL_GRAPH>
            <WIN_CUSTOM_SFR>34053</WIN_CUSTOM_SFR>
            <WIN_C_STAT>34054</WIN_C_STAT>
            <WIN_DEBUG_LOG>34055</WIN_DEBUG_LOG>
            <WIN_FIND_ALL_DECLARATIONS>34056</WIN_FIND_ALL_DECLARATIONS>
            <WIN_FIND_ALL_REFERENCES>34057</WIN_FIND_ALL_REFERENCES>
            <WIN_FIND_IN_FILES>34058</WIN_FIND_IN_FILES>
            <WIN_SELECT_AMBIGUOUS_DEFINITIONS>34059</WIN_SELECT_AMBIGUOUS_DEFINITIONS>
            <WIN_SOURCEBROWSE_LOG>34060</WIN_SOURCEBROWSE_LOG>
            <WIN_SOURCE_BROWSE2>34061</WIN_SOURCE_BROWSE2>
            <WIN_TOOL_OUTPUT>34062</WIN_TOOL_OUTPUT>
            <WIN_WORKSPACE>34063</WIN_WORKSPACE>
            <WIN_POWER_LOG_SETUP>34064</WIN_POWER_LOG_SETUP>
        </ChildIdMap>
        <Desktop>
            <IarPane-34048>
                <ToolBarCmdIds>
                    <item>34001</item>
                    <item>0</item>
                </ToolBarCmdIds>
            </IarPane-34048>
            <IarPane-34049>
                <ToolBarCmdIds>
                    <item>57600</item>
                    <item>57601</item>
                    <item>57603</item>
                    <item>33024</item>
                    <item>0</item>
                    <item>57607</item>
                    <item>0</item>
                    <item>57635</item>
                    <item>57634</item>
                    <item>57637</item>
                    <item>0</item>
                    <item>57643</item>
                    <item>57644</item>
                    <item>0</item>
                    <item>33090</item>
                    <item>33057</item>
                    <item>57636</item>
                    <item>57640</item>
                    <item>57641</item>
                    <item>33026</item>
                    <item>33065</item>
                    <item>33063</item>
                    <item>33064</item>
                    <item>33053</item>
                    <item>33054</item>
                    <item>0</item>
                    <item>33035</item>
                    <item>33036</item>
                    <item>34399</item>
                    <item>0</item>
                    <item>33038</item>
                    <item>33039</item>
                    <item>0</item>
                </ToolBarCmdIds>
            </IarPane-34049>
            <IarPane-34055>
                <col-names>
                    <item>Log</item>
                    <item>_I0</item>
                </col-names>
                <col-widths>
                    <item>1537</item>
                    <item>20</item>
                </col-widths>
                <DebugLogLevel>2</DebugLogLevel>
                <LiveFile>$PROJ_DIR$\DebugLog.log</LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>0</LiveFilterLevel>
            </IarPane-34055>
            <IarPane-34063>
                <NodeDict>
                    <ExpandedNode>rzn2l_boot</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/Flex Software</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/Flex Software/Build Configuration</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/Flex Software/Generated Data</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/Flex Software/Program Entry</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/Flex Software/source</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/source</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/source/IAP</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/source/bsp</ExpandedNode>
                    <ExpandedNode>rzn2l_boot/source/flash</ExpandedNode>
                </NodeDict>
            </IarPane-34063>
            <IarPane-34058>
                <col-names>
                    <item>Line</item>
                    <item>Matched</item>
                    <item>Path</item>
                    <item>String</item>
                </col-names>
                <col-widths>
                    <item>67</item>
                    <item>100</item>
                    <item>444</item>
                    <item>246</item>
                </col-widths>
            </IarPane-34058>
            <ControlBarVersion>
                <Major>14</Major>
                <Minor>34</Minor>
            </ControlBarVersion>
            <MFCToolBarParameters>
                <Tooltips>1</Tooltips>
                <ShortcutKeys>1</ShortcutKeys>
                <LargeIcons>0</LargeIcons>
                <MenuAnimation>0</MenuAnimation>
                <RecentlyUsedMenus>1</RecentlyUsedMenus>
                <MenuShadows>1</MenuShadows>
                <ShowAllMenusAfterDelay>1</ShowAllMenusAfterDelay>
                <CommandsUsage>0F0100003800138600000B000000598400000100000008800000010000002981000001000000048100000200000010860000070000002CE1000001000000219700000100000056840000020000002681000003000000AF06000007000000BD800000010000009B800000010000002C97000001000000D884000002000000ED8000000100000020810000010000000F810000010000000C810000010000001D8100004A0000005E84000001000000379700000100000026970000010000005686000009000000178100000100000022870000010000002881000001000000209700000100000055840000370000002BE100000A000000808C0000010000001481000006000000008100002E0000003C970000010000000E8100007D0000002B970000010000005E86000001000000D78400000100000009970000020000001797000001000000E98000000100000005DC0000010000001486000003000000259700000100000009800000010000001186000006000000058100000A0000004681000001000000DC840000010000001C970000020000001E810000010000005F840000020000005D860000010000001986000001000000199700000100000035E1000001000000</CommandsUsage>
            </MFCToolBarParameters>
            <CommandManager>
                <CommandsWithoutImages>73000D8400000F84000008840000FFFFFFFF54840000328100001C81000009840000538400003C8400003D840000408400004C8400003E8400004B8400004D8400003F8400003A8400003B8400005A8400005B8400003C9700002A8F0000069700003B97000012DE000002DE00000BDE000003DE000005DE000006DE000000DE000001DE00002092000028920000299200003792000038920000349200003392000004DE00001E9200000DDE0000249200001D92000007DE00000C8400003384000078840000118400005E8400005F84000008800000098000000A8000000B8000000C800000158000000A81000001E80000008800000188000002880000038800000488000005880000778400000784000086840000808C000044D500002DDE00001FDE000020DE000021DE000026DE000028DE000023DE000022DE000024DE000027DE000025DE00002CDE0000558400005684000059840000D6840000D7840000D8840000D9840000DA840000DB840000DC840000DD840000DE840000DF840000E0840000E1840000E9840000EA840000248100007C8400007D8400007E840000838400008484000025920000818400008284000020F1000010F0000000F0000020F0000030F0000060F00000</CommandsWithoutImages>
                <MenuUserImages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enuUserImages>
            </CommandManager>
            <Pane-59393>
                <ID>0</ID>
                <RectRecentFloat>0A0000000A0000006E0000006E000000</RectRecentFloat>
                <RectRecentDocked>00000000E003000080070000F1030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-59393>
            <BasePane-59393>
                <IsVisible>1</IsVisible>
            </BasePane-59393>
            <Pane-34050>
                <ID>34050</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34050>
            <BasePane-34050>
                <IsVisible>0</IsVisible>
            </BasePane-34050>
            <IarPane-34050>
                <col-names>
                    <item>Extra</item>
                    <item>Location</item>
                    <item>Type</item>
                    <item>_I0</item>
                </col-names>
                <col-widths>
                    <item>500</item>
                    <item>200</item>
                    <item>100</item>
                    <item>35</item>
                </col-widths>
                <BreakSortOrder>2</BreakSortOrder>
            </IarPane-34050>
            <Pane--1>
                <ID>4294967295</ID>
                <RectRecentFloat>000000003B03000080070000F7030000</RectRecentFloat>
                <RectRecentDocked>000000002403000080070000E0030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane--1>
            <BasePane--1>
                <IsVisible>1</IsVisible>
            </BasePane--1>
            <Pane-34051>
                <ID>34051</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>040000003C0300007C070000C6030000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34051>
            <BasePane-34051>
                <IsVisible>1</IsVisible>
            </BasePane-34051>
            <IarPane-34051>
                <col-names>
                    <item>File</item>
                    <item>Line</item>
                    <item>Messages</item>
                    <item>_I0</item>
                </col-names>
                <col-widths>
                    <item>460</item>
                    <item>40</item>
                    <item>1386</item>
                    <item>20</item>
                </col-widths>
                <BuildLogLevel>2</BuildLogLevel>
                <LiveFile>$WS_DIR$\BuildLog.log</LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>0</LiveFilterLevel>
            </IarPane-34051>
            <Pane-34055>
                <ID>34055</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>040000003C0300007C070000C6030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34055>
            <BasePane-34055>
                <IsVisible>1</IsVisible>
            </BasePane-34055>
            <Pane-34056>
                <ID>34056</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>040000003C0300007C070000C6030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34056>
            <BasePane-34056>
                <IsVisible>0</IsVisible>
            </BasePane-34056>
            <IarPane-34056>
                <ColumnWidth0>666</ColumnWidth0>
                <ColumnWidth1>95</ColumnWidth1>
                <ColumnWidth2>1142</ColumnWidth2>
                <FilterLevel>2</FilterLevel>
                <LiveFile />
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34056>
            <Pane-34057>
                <ID>34057</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>040000003C0300007C070000C6030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34057>
            <BasePane-34057>
                <IsVisible>0</IsVisible>
            </BasePane-34057>
            <IarPane-34057>
                <ColumnWidth0>666</ColumnWidth0>
                <ColumnWidth1>95</ColumnWidth1>
                <ColumnWidth2>1142</ColumnWidth2>
                <FilterLevel>2</FilterLevel>
                <LiveFile />
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34057>
            <Pane-34058>
                <ID>34058</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>040000003C0300007C070000C6030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34058>
            <BasePane-34058>
                <IsVisible>1</IsVisible>
            </BasePane-34058>
            <Pane-34059>
                <ID>34059</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>040000003C0300007C070000C6030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34059>
            <BasePane-34059>
                <IsVisible>1</IsVisible>
            </BasePane-34059>
            <IarPane-34059>
                <ColumnWidth0>444</ColumnWidth0>
                <ColumnWidth1>63</ColumnWidth1>
                <ColumnWidth2>762</ColumnWidth2>
                <FilterLevel>2</FilterLevel>
                <LiveFile></LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34059>
            <Pane-34060>
                <ID>34060</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>040000003C0300007C070000C6030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34060>
            <BasePane-34060>
                <IsVisible>1</IsVisible>
            </BasePane-34060>
            <IarPane-34060>
                <FilterLevel>2</FilterLevel>
                <LiveFile>$WS_DIR/SourceBrowseLog.log</LiveFile>
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34060>
            <Pane-34062>
                <ID>34062</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>040000003C0300007C070000C6030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34062>
            <BasePane-34062>
                <IsVisible>0</IsVisible>
            </BasePane-34062>
            <IarPane-34062>
                <FilterLevel>2</FilterLevel>
                <LiveFile />
                <LiveLogEnabled>0</LiveLogEnabled>
                <LiveFilterLevel>-1</LiveFilterLevel>
            </IarPane-34062>
            <Pane-34052>
                <ID>34052</ID>
                <RectRecentFloat>8007000017000000000A0000A8000000</RectRecentFloat>
                <RectRecentDocked>00000000000000008002000091000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34052>
            <BasePane-34052>
                <IsVisible>0</IsVisible>
            </BasePane-34052>
            <IarPane-34052>
                <cg_type>
                    <item>2</item>
                </cg_type>
                <cg_symbol>
                    <item />
                </cg_symbol>
                <cg_user>
                    <item />
                </cg_user>
                <cg_display>
                    <item>&lt;Right-click on a symbol in the editor to show a call graph&gt;</item>
                </cg_display>
                <cg_def_file>
                    <item />
                </cg_def_file>
                <cg_def_line>
                    <item>0</item>
                </cg_def_line>
                <cg_def_col>
                    <item>0</item>
                </cg_def_col>
                <cg_call_file>
                    <item />
                </cg_call_file>
                <cg_call_line>
                    <item>0</item>
                </cg_call_line>
                <cg_call_col>
                    <item>0</item>
                </cg_call_col>
                <col-names>
                    <item>File</item>
                    <item>Function</item>
                    <item>Line</item>
                </col-names>
                <col-widths>
                    <item>200</item>
                    <item>700</item>
                    <item>100</item>
                </col-widths>
            </IarPane-34052>
            <Pane-34053>
                <ID>34053</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>000000007202000080070000B4020000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34053>
            <BasePane-34053>
                <IsVisible>0</IsVisible>
            </BasePane-34053>
            <IarPane-34053>
                <col-names>
                    <item>Access</item>
                    <item>Address</item>
                    <item>Name</item>
                    <item>Size</item>
                    <item>Zone</item>
                    <item>_I0</item>
                </col-names>
                <col-widths>
                    <item>120</item>
                    <item>150</item>
                    <item>150</item>
                    <item>50</item>
                    <item>120</item>
                    <item>20</item>
                </col-widths>
            </IarPane-34053>
            <Pane-34054>
                <ID>34054</ID>
                <RectRecentFloat>8007000017000000A2080000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34054>
            <BasePane-34054>
                <IsVisible>0</IsVisible>
            </BasePane-34054>
            <IarPane-34054>
                <kCStatFilterHistoryKey />
                <col-names>
                    <item>Check</item>
                    <item>File</item>
                    <item>Line</item>
                    <item>Message</item>
                    <item>Severity</item>
                </col-names>
                <col-widths>
                    <item>200</item>
                    <item>200</item>
                    <item>100</item>
                    <item>500</item>
                    <item>100</item>
                </col-widths>
            </IarPane-34054>
            <Pane-34061>
                <ID>34061</ID>
                <RectRecentFloat>8007000017000000000A0000A8000000</RectRecentFloat>
                <RectRecentDocked>00000000000000008002000091000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34061>
            <BasePane-34061>
                <IsVisible>0</IsVisible>
            </BasePane-34061>
            <IarPane-34061>
                <SB_FileFilter>
                    <item>2</item>
                </SB_FileFilter>
                <SB_TypeFilter>
                    <item>0</item>
                </SB_TypeFilter>
                <SB_SBW_File>
                    <item>D:\Code\Ruisa\rzn2l-boot\Debug\BrowseInfo\rzn2l_boot.pbw</item>
                </SB_SBW_File>
                <col-names>
                    <item>File</item>
                    <item>Name</item>
                    <item>Scope</item>
                    <item>Symbol type</item>
                </col-names>
                <col-widths>
                    <item>300</item>
                    <item>300</item>
                    <item>300</item>
                    <item>300</item>
                </col-widths>
            </IarPane-34061>
            <Pane-34063>
                <ID>34063</ID>
                <RectRecentFloat>80070000170000008608000078010000</RectRecentFloat>
                <RectRecentDocked>00000000320000003801000020030000</RectRecentDocked>
                <RecentFrameAlignment>4096</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34063>
            <BasePane-34063>
                <IsVisible>1</IsVisible>
            </BasePane-34063>
            <DockingManager-256>
                <DockingPaneAndPaneDividers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ockingPaneAndPaneDividers>
            </DockingManager-256>
            <MFCToolBar-34048>
                <Name>CMSIS-Pack</Name>
                <Buttons>00200000010000000100FFFF01001100434D4643546F6F6C426172427574746F6ED18400000200000026060000FFFEFF00000000000000000000000000010000000100000000000000FFFEFF0A43004D005300490053002D005000610063006B0018000000</Buttons>
            </MFCToolBar-34048>
            <Pane-34048>
                <ID>34048</ID>
                <RectRecentFloat>0A0000000A0000006E0000006E000000</RectRecentFloat>
                <RectRecentDocked>3A03000000000000680300001A000000</RectRecentDocked>
                <RecentFrameAlignment>8192</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>24</MRUWidth>
                <PinState>0</PinState>
            </Pane-34048>
            <BasePane-34048>
                <IsVisible>1</IsVisible>
            </BasePane-34048>
            <MFCToolBar-34049>
                <Name>Main</Name>
                <Buttons>00200000010000002000FFFF01001100434D4643546F6F6C426172427574746F6E00E100000000000054060000FFFEFF000000000000000000000000000100000001000000018001E100000000000055060000FFFEFF000000000000000000000000000100000001000000018003E100000000000057060000FFFEFF0000000000000000000000000001000000010000000180008100000000000038060000FFFEFF00000000000000000000000000010000000100000001800000000001000000FFFFFFFFFFFEFF000000000000000000000000000100000001000000018007E10000000000005A060000FFFEFF00000000000000000000000000010000000100000001800000000001000000FFFFFFFFFFFEFF000000000000000000000000000100000001000000018023E10000000004005C060000FFFEFF000000000000000000000000000100000001000000018022E10000000004005B060000FFFEFF000000000000000000000000000100000001000000018025E10000000000005E060000FFFEFF00000000000000000000000000010000000100000001800000000001000000FFFFFFFFFFFEFF00000000000000000000000000010000000100000001802BE100000000040061060000FFFEFF00000000000000000000000000010000000100000001802CE100000000040062060000FFFEFF00000000000000000000000000010000000100000001800000000001000000FFFFFFFFFFFEFF000000000000000000000000000100000001000000FFFF01000D005061737465436F6D626F426F784281000000000000FFFFFFFFFFFEFF000000000000000000010000000000000001000000B400000002002050FFFFFFFFFFFEFF0096000000000000000000018021810000000004004A060000FFFEFF000000000000000000000000000100000001000000018024E10000000000005D060000FFFEFF000000000000000000000000000100000001000000018028E10000000004005F060000FFFEFF000000000000000000000000000100000001000000018029E100000000000060060000FFFEFF0000000000000000000000000001000000010000000180028100000000000039060000FFFEFF000000000000000000000000000100000001000000018029810000000000004E060000FFFEFF000000000000000000000000000100000001000000018027810000000000004C060000FFFEFF000000000000000000000000000100000001000000018028810000000000004D060000FFFEFF00000000000000000000000000010000000100000001801D8100000000000046060000FFFEFF00000000000000000000000000010000000100000001801E8100000000040047060000FFFEFF00000000000000000000000000010000000100000001800000000001000000FFFFFFFFFFFEFF00000000000000000000000000010000000100000001800B810000020000003D060000FFFEFF00000000000000000000000000010000000100000001800C810000020000003E060000FFFEFF00000000000000000000000000010000000100000001805F8600000200000051060000FFFEFF00000000000000000000000000010000000100000001800000000001000000FFFFFFFFFFFEFF00000000000000000000000000010000000100000001800E8100000200000040060000FFFEFF00000000000000000000000000010000000100000001800F8100000200000041060000FFFEFF00000000000000000000000000010000000100000000000000FFFEFF044D00610069006E0024030000</Buttons>
            </MFCToolBar-34049>
            <Pane-34049>
                <ID>34049</ID>
                <RectRecentFloat>0A0000000A0000006E0000006E000000</RectRecentFloat>
                <RectRecentDocked>00000000000000003A0300001A000000</RectRecentDocked>
                <RecentFrameAlignment>8192</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>0</IsFloating>
                <MRUWidth>804</MRUWidth>
                <PinState>0</PinState>
            </Pane-34049>
            <BasePane-34049>
                <IsVisible>1</IsVisible>
            </BasePane-34049>
            <Pane-34064>
                <ID>34064</ID>
                <RectRecentFloat>000000001700000022010000C8000000</RectRecentFloat>
                <RectRecentDocked>000000000000000022010000B1000000</RectRecentDocked>
                <RecentFrameAlignment>32768</RecentFrameAlignment>
                <RecentRowIndex>0</RecentRowIndex>
                <IsFloating>1</IsFloating>
                <MRUWidth>32767</MRUWidth>
                <PinState>0</PinState>
            </Pane-34064>
            <BasePane-34064>
                <IsVisible>0</IsVisible>
            </BasePane-34064>
            <IarPane-34064 />
        </Desktop>
        <MDIWindows>
            <MDIClientArea-0>
                <MDITabsState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absState>
            </MDIClientArea-0>
        </MDIWindows>
    </WindowStorage>
</Workspace>
