#include "hal_data.h"
#include "bsp_pwm.h"


static void bsp_pwm_mtu34_init(uint16_t period, uint16_t dead);
static void bsp_pwm_mtu67_init(uint16_t period, uint16_t dead);


extern void IntTaskAExec(void);  
extern void IntTaskBExec(void); 
extern void IntTaskCExec(void); 
/***********************************************************************************************************************
* Function Name: bsp_pwm_mtu34_init 
* Description  : This functio  the PWM module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
static void bsp_pwm_mtu34_init(uint16_t period, uint16_t dead)
{
  
   /* MTU3定时器计数时钟为 PCLKH/1, 计数边沿为时钟的上升沿, 定时器计数器TCNT清零禁止 */
    R_MTU3->TCR = 0x00;
    R_MTU3->TCR2 = 0x00;

    /* MTU4定时器计数时钟为 PCLKH/1, 计数边沿为时钟的上升沿, 定时器计数器TCNT清零禁止 */
    R_MTU4->TCR = 0x00;
    R_MTU4->TCR2 = 0x00;

    /* 死区最小设定值是1，即使不使用死区功能 */
    if (dead < 1)
    {
        dead = 1;
    }

    /* MTU3定时器计数器起始值必须设置为死区时间设定值，如果禁用死区，其值应为1, MTU4一定要清零 */
    R_MTU3->TCNT = dead;
    R_MTU4->TCNT = 0x0000;

    /* 定时器MTU3、MTU4同步设置/清零功能禁用 */
    R_MTU->TSYRA &= 0xC0;

    /* 定时器通用寄存器(用于设定通道比较值) */
    R_MTU3->TGRB = (uint16_t)((period >> 2) + dead / 2);    /* U-phase output compare register */
    R_MTU3->TGRD = (uint16_t)((period >> 2) + dead / 2);    /* U-phase output buffer register */
    R_MTU4->TGRA = (uint16_t)((period >> 2) + dead / 2);    /* V-phase output compare register */
    R_MTU4->TGRC = (uint16_t)((period >> 2) + dead / 2);    /* V-phase output buffer register */
    R_MTU4->TGRB = (uint16_t)((period >> 2) + dead / 2);    /* W-phase output compare register */
    R_MTU4->TGRD = (uint16_t)((period >> 2) + dead / 2);    /* W-phase output buffer register */

    /* TDDRA死区时间设定寄存器，MTU3/MTU4使用 */
    R_MTU->TDDRA = dead;

    /* TCDRA是MTU4定时器周期设定值 */
    R_MTU->TCDRA = period;
    /* TCBRA是MTU4定时器周期设定值TCDRA缓存 */
    R_MTU->TCBRA = period;

    /* MTU3定时器周期值，一定是周期设定值+死区时间(死区禁用时为1) */
    R_MTU3->TGRA = (uint16_t)(period + dead);
    /* TGRC是TGRA的缓存寄存器 */
    R_MTU3->TGRC = (uint16_t)(period + dead);

    /* 设置PWM信号输出的电平逻辑 */

    /* MTU3/MTU4 */

    /* TOCL是写使能，0-写访问使能 */
    R_MTU->TOCR1A_b.TOCL = 0;
    /* PWM同步输出禁止 0-禁止 */
    R_MTU->TOCR1A_b.PSYE = 0;
    /* 选择TOCR1作为TOC的控制 */
    R_MTU->TOCR1A_b.TOCS = 0;
    /* 在互补输出模式及同步复位模式有效 */

    R_MTU->TOCR1A_b.OLSP = 1;   /* Initial output:L, Active level:H */
    R_MTU->TOCR1A_b.OLSN = 1;   /* Initial output:L, Active level:H */

    /* 0x0E: 在计数0时进行映射传输 0x0D: 在周期值时进行映射传输 0x0F: 在周期和0时都进行映射传输*/
    R_MTU3->TMDR1_b.MD = 0x0F;
    /* TGRC和TGRA同时使用，TGRC作为TGRA的映射寄存器 1-使能映射 */
    R_MTU3->TMDR1_b.BFA = 1;
    /* TGRD和TGRB同时使用，TGRD作为TGRB的映射寄存器 1-使能映射 */
    R_MTU3->TMDR1_b.BFB = 1;

    /* 双缓冲模式禁止 */
    R_MTU->TMDR2A_b.DRS = 0x00;

    /* MTU4计数器下溢时触发ADC转换请求，还有一个计数器和TGA比较匹配可以触发ADC转换 */
    R_MTU4->TIER_b.TTGE2 = 1;

    /* 在MTU3-CH3计数值和TGA(周期值)匹配时产生中断 */
#if 1
    R_MTU3->TIER_b.TGIEA = 0;
    /* 在MTU4下溢时产生中断 */
    R_MTU4->TIER_b.TCIEV = 0;
#else
    R_MTU3->TIER_b.TGIEA = 1;
#endif    
}
/***********************************************************************************************************************
* Function Name: bsp_pwm_mtu67_init
* Description  : This function initializes the MTU3 Unit 6/7 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
static void bsp_pwm_mtu67_init(uint16_t period, uint16_t dead)
{
    /* MTU6定时器计数时钟为 PCLKH/1, 计数边沿为时钟的上升沿, 定时器计数器TCNT清零禁止 */
    R_MTU6->TCR = 0x00;
    R_MTU6->TCR2 = 0x00;

    /* MTU7定时器计数时钟为 PCLKH/1, 计数边沿为时钟的上升沿, 定时器计数器TCNT清零禁止 */
    R_MTU7->TCR = 0x00;
    R_MTU7->TCR2 = 0x00;

    /* 死区最小设定值是1，即使不使用死区功能 */
    if (dead < 1)
    {
        dead = 1;
    }

    /* MTU3定时器计数器起始值必须设置为死区时间设定值，如果禁用死区，其值应为1, MTU4一定要清零 */
    R_MTU6->TCNT = dead;
    R_MTU7->TCNT = 0x0000;

    /* 定时器MTU6、MTU7同步设置/清零功能禁用 */
    R_MTU->TSYRB &= 0xC0;

    /* 定时器通用寄存器(用于设定通道比较值) */
    R_MTU6->TGRB = (uint16_t)((period >> 1) + dead / 2);    /* U-phase output compare register */
    R_MTU6->TGRD = (uint16_t)((period >> 1) + dead / 2);    /* U-phase output buffer register */
    R_MTU7->TGRA = (uint16_t)((period >> 1) + dead / 2);    /* V-phase output compare register */
    R_MTU7->TGRC = (uint16_t)((period >> 1) + dead / 2);    /* V-phase output buffer register */
    R_MTU7->TGRB = (uint16_t)((period >> 1) + dead / 2);    /* W-phase output compare register */
    R_MTU7->TGRD = (uint16_t)((period >> 1) + dead / 2);    /* W-phase output buffer register */

    /* TDDRA死区时间设定寄存器，MTU6/MTU7使用 */
    R_MTU->TDDRB = dead;

    /* TCDRA是MTU7定时器周期设定值 */
    R_MTU->TCDRB = period;
    /* TCBRA是MTU7定时器周期设定值TCDRA缓存 */
    R_MTU->TCBRB = period;

    /* MTU6定时器周期值，一定是周期设定值+死区时间(死区禁用时为1) */
    R_MTU6->TGRA = (uint16_t)(period + dead);
    /* TGRC是TGRA的缓存寄存器 */
    R_MTU6->TGRC = (uint16_t)(period + dead);


    /* 设置PWM信号输出的电平逻辑 */

    /* TOCL是写使能，0-写访问使能 */
    R_MTU->TOCR1B_b.TOCL = 0;
    /* PWM同步输出禁止 0-禁止 */
    R_MTU->TOCR1B_b.PSYE = 0;
    /* 选择TOCR1作为TOC的控制 */
    R_MTU->TOCR1B_b.TOCS = 0;
    /* 在互补输出模式及同步复位模式有效 */
    R_MTU->TOCR1B_b.OLSP = 1;   /* Initial output:L, Active level:H */
    R_MTU->TOCR1B_b.OLSN = 1;   /* Initial output:L, Active level:H */


    /* 0x0E: 在计数0时进行映射传输 0x0D: 在周期值时进行映射传输 0x0F: 在周期和0时都进行映射传输*/
    R_MTU6->TMDR1_b.MD = 0x0F;
    /* TGRC和TGRA同时使用，TGRC作为TGRA的映射寄存器 1-使能映射 */
    R_MTU6->TMDR1_b.BFA = 1;
    /* TGRD和TGRB同时使用，TGRD作为TGRB的映射寄存器 1-使能映射 */
    R_MTU6->TMDR1_b.BFB = 1;

    /* 双缓冲模式禁止 */
    R_MTU->TMDR2B_b.DRS = 0x00;

    /* MTU4计数器下溢时触发ADC转换请求，还有一个计数器和TGA比较匹配可以触发ADC转换 */
    R_MTU7->TIER_b.TTGE2 = 1;

    /* 在MTU3-CH3计数值和TGA(周期值)匹配时产生中断 */
    R_MTU6->TIER_b.TGIEA = 0;
    /* 在MTU4下溢时产生中断 */
    R_MTU7->TIER_b.TCIEV = 0;
}

/***********************************************************************************************************************
* Function Name: bsp_pwm_mtu0_init
* Description  : This function initializes the MTU3 Unit0 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_pwm_mtu0_init(uint32_t mtu0_prd, uint32_t mtu0_cmp_init, uint32_t mtu0_cmp_cap)
{
    /*
     * TGRA的值为周期值、计数清零源
     * TGRC作为TGRA的缓存值，在周期比较时进行映射传输
     *
     * TRGB作为触发delta-sigma采样计数器清零
     *
     * TGRD作为触发delta-sigma采样结果捕获
     *·
     * TGRE和TGRF暂时保留
     *
     */
    /* 停止定时器MTU0 */
    R_MTU->TSTRA_b.CST0 = 0;

    /* MTU0定时器计数时钟为 PCLKH/1, 计数边沿为时钟的上升沿, 定时器计数器TCNT清零在TGRB比较匹配时 */
    R_MTU0->TCR_b.TPSC = 0x00;
    R_MTU0->TCR_b.CKEG = 0x00;
    R_MTU0->TCR_b.CCLR = 0x01;
    
    R_MTU0->TCR2 = 0x00;
    
    /* 定时器计数器设置为0 */
    R_MTU0->TCNT = 0x0000;
    R_MTU->TSYRA |= 0x00;   /* MTU1.TCNT operates independently */
    
    /* 缓存传输模式：0-在比较匹配时进行映射传输; 1-在TCN被清零时进行映射传输(在互补输出时不生效) */
    R_MTU0->TBTM_b.TTSA = 0x00;
    R_MTU0->TBTM_b.TTSB = 0x00;
    R_MTU0->TBTM_b.TTSE = 0x00;

    /* 0x02: PWM模式1 */
    R_MTU0->TMDR1_b.MD = 0x0;
    /* TGRC和TGRA同时使用，TGRC作为TGRA的映射寄存器 1-使能映射， 0-禁止映射模式 */
    R_MTU0->TMDR1_b.BFA = 1;
    /* TGRD和TGRB同时使用，TGRD作为TGRB的映射寄存器 1-使能映射， 0-禁止映射模式 */
    R_MTU0->TMDR1_b.BFB = 0;
    /* TGRE和TGRF同时使用，TGRD作为TGRB的映射寄存器 1-使能映射， 0-禁止映射模式 */
    R_MTU0->TMDR1_b.BFE = 1;

    /* Output prohibited */
    R_MTU0->TIORH_b.IOA = 0;
    R_MTU0->TIORH_b.IOB = 0;
    R_MTU0->TIORL_b.IOC = 0;
    R_MTU0->TIORL_b.IOD = 0;     

    /* 定时器通用寄存器(用于设定通道比较值) */

    /* 周期设定值 */
    R_MTU0->TGRA = (uint16_t)(mtu0_prd - 1);
    R_MTU0->TGRC = (uint16_t)(mtu0_prd - 1);

    /* 触发对delta-sigma采样计数器进行清零 */
    R_MTU0->TGRB = (uint16_t)(mtu0_cmp_init);

    /* 触发对delta-sigma采样结果值进行捕获，同时产生中断 */
    R_MTU0->TGRD = (uint16_t)(mtu0_cmp_cap);
    
    /* 保留 */
    R_MTU0->TGRE = 0;

    /* 保留 */
    R_MTU0->TGRF = 0;    
    
    /* 计数值和TGC匹配时产生中断 */
    R_MTU0->TIER_b.TGIED = 1;
    
    /* 触发ADC采样 */
    R_MTU0->TIER_b.TTGE = 1;    
}

/***********************************************************************************************************************
 * Function Name: PWM初始化
 * @param mtu0_prd  - mtu0周期值(周期时的计数个数，增计数模式，程序内部自动对该值进行了减1处理)
 * @param mtu0_cmp_init - mtu0的TGRB寄存器设定值，即用来触发delta-sigma采样计数器清零
 * @param mtu0_cmp_cap - mtu0的TGRD寄存器设定值，用来触发pwm中断
 * @param mtu3_prd  - mtu3/mtu6周期值(波峰时的计数个数)
 * @param dead      - 死区设定值(计数个数)
***********************************************************************************************************************/
void bsp_pwm_init(uint32_t mtu0_prd, uint32_t mtu0_cmp_init, uint32_t mtu0_cmp_cap, uint16_t mtu3_prd, uint16_t dead)
{
    volatile unsigned long dummy;

    /* Cancel MTU stop state in LPC */
    dummy = 1u;
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_MTU3, dummy);
    dummy = BSP_MSTP_REG_FSP_IP_MTU3(dummy);
    /* Dummy-read for the module-stop state(2) */
    dummy = R_MTU5->TSTR;
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);

    for (dummy = 0; dummy < 5; dummy++)
    {
        __asm volatile ("nop");
    }

    /* TRWERA_b控制MTU3/MTU4定时器相关受写保护功能保护的寄存器 */
    R_MTU->TRWERA_b.RWE = 1U;
    /* TRWERB_b控制MTU6/MTU7定时器相关受写保护功能保护的寄存器 */
    R_MTU->TRWERB_b.RWE = 1U;

    /* 停止MTU通道 */
    R_MTU->TSTRA = 0x00;
    R_MTU5->TSTR = 0x00;
    R_MTU->TSTRB = 0x00;

    /* MTU0初始化 */
    bsp_pwm_mtu0_init(mtu0_prd, mtu0_cmp_init, mtu0_cmp_cap);    
    
    /* 1轴PWM输出初始化 */
    bsp_pwm_mtu34_init(mtu3_prd, dead);

    /* 2轴PWM输出初始化 */
    bsp_pwm_mtu67_init(mtu3_prd, dead);

    /* POE初始化 */
//    bsp_poe_init();
    
    /*使能输出端口*/
//    bsp_pwm_output_enable(0);

    /* 配置事件连接器用于同步触发电流采样 */
    R_ELC->ELC_SSEL_b[7].ELC_SEL0 = ELC_EVENT_TGIB0;    /* ADC1 A */
    R_ELC->ELC_SSEL_b[7].ELC_SEL2 = ELC_EVENT_TGID0;    /* DSMIF0 CAP_TRG0(结果值捕获事件) */
    R_ELC->ELC_SSEL_b[9].ELC_SEL2 = ELC_EVENT_TGIB0;    /* DSMIF0 CDCNT_INT_TRG0(计数值清零事件) */
    R_ELC->ELC_SSEL_b[10].ELC_SEL2 = ELC_EVENT_TGID0;   /* DSMIF1 CAP_TRG0(结果值捕获事件) */
    R_ELC->ELC_SSEL_b[12].ELC_SEL2 = ELC_EVENT_TGIB0;   /* DSMIF1 CDCNT_INT_TRG0(计数值清零事件) */

    /* GIC级中断配置 - MTU0产生中断(目前禁止了MTU3/MTU4中断生成) */
#if 0
    /* MTU3周期中断(PWM波峰比较中断) */
    R_BSP_IrqDisable(VECTOR_NUMBER_TGIA3);
    R_BSP_IrqCfg(VECTOR_NUMBER_TGIA3, 0UL, (NULL));
    R_BSP_IrqEnable(VECTOR_NUMBER_TGIA3);
#endif    
#if 0
    /* MTU4下溢中断(PWM波谷中断) */
    R_BSP_IrqDisable(VECTOR_NUMBER_TCIV4);
    R_BSP_IrqCfg(VECTOR_NUMBER_TCIV4, 0UL, (NULL));
    R_BSP_IrqEnable(VECTOR_NUMBER_TCIV4);
#endif    
    
     /* MTU0.TGRD产生中断 */
    R_BSP_IrqDisable(VECTOR_NUMBER_TGID0);
    R_BSP_IrqCfg(VECTOR_NUMBER_TGID0, _MTU_PRIORITY_LEVEL2, (NULL));
    R_BSP_IrqEnable(VECTOR_NUMBER_TGID0);   
    
    
    /* 使能软件中断0 作为TASKB中断 */
    R_BSP_IrqDisable(VECTOR_NUMBER_INTCPU0);
    R_BSP_IrqCfg(VECTOR_NUMBER_INTCPU0, _MTU_PRIORITY_LEVEL3, (NULL));
    R_BSP_IrqEnable(VECTOR_NUMBER_INTCPU0);   
    
    /* 使能软件中断1 作为TASKC中断 */
    R_BSP_IrqDisable(VECTOR_NUMBER_INTCPU1);
    R_BSP_IrqCfg(VECTOR_NUMBER_INTCPU1, _MTU_PRIORITY_LEVEL4, (NULL));
    R_BSP_IrqEnable(VECTOR_NUMBER_INTCPU1);       
}
/***********************************************************************************************************************
* Function Name: R_MTU3_C3/C4 Timer Output Master Enable 
* Description  : This function Enable output pins MTIOC4D, MTIOC4C, MTIOC3D, MTIOC4B, MTIOC4A,and MTIOC3B.
* Arguments    : AxisID : Axis id 
                 ch[ bit 0 ]: u phase up brige    ch[ bit 1 ]: u phase low brige
                 ch[ bit 2 ]: v phase up brige    ch[ bit 3 ]: v phase low brige
                 ch[ bit 4 ]: w phase up brige    ch[ bit 5 ]: w phase low brige
* Return Value : None
***********************************************************************************************************************/
void bsp_pwm_output_enable(uint16_t AxisID, uint16_t Ch)
{   
  if(AxisID == 0)
  {
    /* MTU3/MTU4: PWM端口输出使能 */
        /* TRWERA_b控制MTU3/MTU4定时器相关受写保护功能保护的寄存器 */
    R_MTU->TRWERA_b.RWE = 1U;    
    R_MTU->TOERA = 0xC0| Ch;
  }
  else if (AxisID == 1)
  {
     /* TRWERB_b控制MTU6/MTU7定时器相关受写保护功能保护的寄存器 */
    R_MTU->TRWERB_b.RWE = 1U;       
    /* MTU6/MTU7: PWM端口输出使能 */
    R_MTU->TOERB = 0xC0| Ch;
  }
  
  /* clear poe flag */ 
  bsp_poe3_stop();
}
/***********************************************************************************************************************
* Function Name: R_MTU3_C6/C7 Timer Output Master Disable 
* Description  : This function Disable output pins MTIOC4D, MTIOC4C, MTIOC3D, MTIOC4B, MTIOC4A,and MTIOC3B.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/

void bsp_pwm_output_disable(uint16_t AxisID)
{
  
  if(AxisID == 0)
  {
    /* MTU3/MTU4: PWM端口输出禁止(PWM引脚设置为无效-具体引脚电平由其他寄存器设置) */
        /* TRWERA_b控制MTU3/MTU4定时器相关受写保护功能保护的寄存器 */
    R_MTU->TRWERA_b.RWE = 1U;
    R_MTU->TOERA = 0xC0;
  }
  else if (AxisID == 1)
  {
     /* TRWERB_b控制MTU6/MTU7定时器相关受写保护功能保护的寄存器 */
    R_MTU->TRWERB_b.RWE = 1U;   
   /* MTU6/MTU7: PWM端口输出禁止(PWM引脚设置为无效-具体引脚电平由其他寄存器设置) */
    R_MTU->TOERB = 0xC0;
  }  
}

/***********************************************************************************************************************
* Function Name: bsp_pwm_start
* Description  : This function stops MTU0 channel 8 counter.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/

void bsp_pwm_start(void)
{
    /* 同步启动MTU3/MTU4/MTU6/MTU7/MTU0定时器 */
    R_MTU->TCSYSTR |= (0x1B | 0x80);
}

/***********************************************************************************************************************
* Function Name: bsp_pwm_stop
* Description  : This function stops MTU0 channel 8 counter.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_pwm_stop(void)
{
    bsp_pwm_output_disable(0);
    bsp_pwm_output_disable(1);

    R_MTU->TSTRA_b.CST0 = 0u;
    R_MTU->TSTRA_b.CST3 = 0u;
    R_MTU->TSTRA_b.CST4 = 0u;
    R_MTU->TSTRB_b.CST6 = 0u;
    R_MTU->TSTRB_b.CST7 = 0u;

    /* MTU3/MTU4/MTU6/MTU7在停止后自动设置了TCNT数值 */

    /* MTU0需要清零，防止再次启动时出现不同步 */
    R_MTU0->TCNT = 0x0000;
}

/***********************************************************************************************************************
* Function Name: bsp_getmtu3_state
* Description  : 
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
uint8_t bsp_getpwm_state(void)
{
   return R_MTU4->TSR_b.TCFV;
}

/***********************************************************************************************************************
* Function Name: bsp_pwmtimer_state
* Description  : T
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_clrpwm_state(void)
{
   R_MTU4->TSR_b.TCFV  = 0;
}

/***********************************************************************************************************************
* Function Name: bsp_getmtu3_state
* Description  : 更新pwm中断周期（mtu0 周期）和pwm周期
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_setpwm_cycle(uint16_t cycle,uint16_t mtu0_cmp_init)
{
    R_MTU0->TGRC = cycle - 1;
    R_MTU0->TGRB = cycle - mtu0_cmp_init;
    
    
    R_MTU->TCBRA = cycle;
    R_MTU->TCBRB = cycle;
    R_MTU3->TGRC = cycle + R_MTU->TDDRA;
    R_MTU6->TGRC = cycle + R_MTU->TDDRB;
}

/***********************************************************************************************************************
* Function Name: bsp_getpwm_cnt
* Description  : 获得pwm计数值
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
uint16_t bsp_getpwm_cnt(void)
{
   return R_MTU4->TCNT;
}

/***********************************************************************************************************************
* Function Name: R_POE3
* Description  : This functio  the POE3 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_poe_init(void)
{
    /* POE有些寄存器只能写入一次，再次写入必须复位 */
   R_POE3_Type POE3 = {0};

    /* 1轴IPM过流输入引脚是P01_4, 对应的POE功能引脚是POE0# */
    /* 2轴IPM过流输入引脚是P22_1, 对应的POE功能引脚是POE4# */
    /* 母线过流输入引脚是P14_5, 对应的POE功能引脚是POE8# */

    /* 1轴伺服PWM引脚
     * PWMU+ - P00_6
     * PWMU- - P01_1
     * PWMV+ - P00_7
     * PWMV- - P01_0
     * PWMW+ - P01_2
     * PWMW- - P01_3
     */

    /* 选择需要将哪些引脚设置为高阻态 */
    POE3.M3SELR = R_POE3->M3SELR;
    /* 0-P17_6 / 1-P00_6 / 2-P03_6 */
    POE3.M3SELR_b.M3BSEL = 0x01;
    /* 0-P18_1 / 1-P01_1 / 2-P04_0 */
    POE3.M3SELR_b.M3DSEL = 0x01;
    R_POE3->M3SELR = POE3.M3SELR;

    POE3.M4SELR1 = R_POE3->M4SELR1;
    /* 0-P17_7 / 1-P00_7 / 2-P05_0 / P18_0 */
    POE3.M4SELR1_b.M4ASEL = 0x01;
    /* 0-P18_0 / 1-P01_0 / 2-P05_2 / P17_7 */
    POE3.M4SELR1_b.M4CSEL = 0x01;
    R_POE3->M4SELR1 = POE3.M4SELR1;

    POE3.M4SELR2 = R_POE3->M4SELR2;
    /* 0-P18_2 / 1-P01_2 / 2-P05_1 / P18_3 */
    POE3.M4SELR2_b.M4BSEL = 0x01;
    /* 0-P18_3 / 1-P01_3 / 2-P05_3 / P18_2 */
    POE3.M4SELR2_b.M4DSEL = 0x01;
    R_POE3->M4SELR2 = POE3.M4SELR2;

    /* 2轴伺服PWM引脚
     * PWMU+ - P21_2
     * PWMU- - P21_4
     * PWMV+ - P21_5
     * PWMV- - P21_7
     * PWMW+ - P21_6
     * PWMW- - P22_0
     */

    /* 选择需要将哪些引脚设置为高阻态 */
    POE3.M6SELR = R_POE3->M6SELR;
    /* 1-P21_2 / 2-P08_5 / 其他-禁止 */
    POE3.M6SELR_b.M6BSEL = 0x01;
    /* 1-P21_4 / 2-P08_7 / 其他-禁止 */
    POE3.M6SELR_b.M6DSEL = 0x01;
    R_POE3->M6SELR = POE3.M6SELR;

    POE3.M7SELR1 = R_POE3->M7SELR1;
    /* 1-P21_5 / 2-P09_0 / 其他-禁止 */
    POE3.M7SELR1_b.M7ASEL = 0x01;
    /* 1-P21_7 / 2-P09_2 / 其他-禁止 */
    POE3.M7SELR1_b.M7CSEL = 0x01;
    R_POE3->M7SELR1 = POE3.M7SELR1;

    POE3.M7SELR2 = R_POE3->M7SELR2;
    /* 1-P21_6 / 2-P09_1 / 其他-禁止 */
    POE3.M7SELR2_b.M7BSEL = 0x01;
    /* 1-P22_0 / 2-P09_3 / 其他-禁止 */
    POE3.M7SELR2_b.M7DSEL = 0x01;
    R_POE3->M7SELR2 = POE3.M7SELR2;

    /* 高阻态输出使能，只有这个使能了，才能够根据条件输出高阻态，这是必须条件 */
    POE3.POECR2 = R_POE3->POECR2;
    POE3.POECR2_b.MTU4BDZE = 1;
    POE3.POECR2_b.MTU4ACZE = 1;
    POE3.POECR2_b.MTU3BDZE = 1;
    POE3.POECR2_b.MTU7BDZE = 1;
    POE3.POECR2_b.MTU7ACZE = 1;
    POE3.POECR2_b.MTU6BDZE = 1;
    R_POE3->POECR2 = POE3.POECR2;
    
    /* 添加POE8#标志到MTU3/MTU4输出高阻态控制条件 */
    /* 添加POE8#标志到MTU6/MTU7输出高阻态控制条件 */
    POE3.POECR4 = R_POE3->POECR4;
    POE3.POECR4_b.IC3ADDMT34ZE = 1;
    POE3.POECR4_b.IC3ADDMT67ZE = 1;
    R_POE3->POECR4 = POE3.POECR4;
   
    /* 配置POE1模式 */
    POE3.ICSR1 = R_POE3->ICSR1;
    /* 0-下降沿检测， 1-16次采样在PCLKH/8时钟，2-16次采样在PCLKH/16时钟， 3-16次采样在PCLKH/128时钟*/
    POE3.ICSR1_b.POE0M = 1; 
    //POE3.ICSR1_b.
    /* 1-使能中断产生 */
    POE3.ICSR1_b.PIE1 = 0;
    R_POE3->ICSR1 = POE3.ICSR1;
#if 0 
    /* 配置POE4模式 */
    POE3.ICSR2 = R_POE3->ICSR2;
    /* 0-下降沿检测， 1-16次采样在PCLKH/8时钟，2-16次采样在PCLKH/16时钟， 3-16次采样在PCLKH/128时钟*/
    POE3.ICSR2_b.POE4M = 1;
    /* 1-使能中断产生 */
    POE3.ICSR2_b.PIE2 = 0;
    R_POE3->ICSR2 = POE3.ICSR2;
#endif
    
    /* 配置POE8模式 */
    POE3.ICSR3 = R_POE3->ICSR3;
    /* 0-下降沿检测， 1-16次采样在PCLKH/8时钟，2-16次采样在PCLKH/16时钟， 3-16次采样在PCLKH/128时钟*/
    POE3.ICSR3_b.POE8M = 2;
    /* 1-POE8F为1时封锁PWM引脚 */
    POE3.ICSR3_b.POE8E = 1;
    /* 1-使能中断产生 */
    POE3.ICSR3_b.PIE3 = 0;
    R_POE3->ICSR3 = POE3.ICSR3;

#if 0    
    /* 配置MTU3/MTU4 PWM输出短路高阻态检测/使能 */

    /* 配置PWM输出引脚短路功能使能/中断产生 */
    POE3.OCSR1 = R_POE3->OCSR1;
    /* 1-使能输出短路时中断产生 */
    POE3.OCSR1_b.OIE1 = 0;
    /* 1-使能输出短路时控制高阻态功能 */
    POE3.OCSR1_b.OCE1 = 0;
    R_POE3->OCSR1 = POE3.OCSR1;


    /* 配置MTU6/MTU7 PWM输出短路高阻态检测/使能 */

    /* 配置PWM输出引脚短路功能使能/中断产生 */
    POE3.OCSR2 = R_POE3->OCSR2;
    /* 1-使能输出短路时中断产生 */
    POE3.OCSR2_b.OIE2 = 0;
    /* 1-使能输出短路时控制高阻态功能 */
    POE3.OCSR2_b.OCE2 = 0;
    R_POE3->OCSR2 = POE3.OCSR2;

    /* 配置PWM输出引脚短路检测电平 */
    POE3.ALR1 = R_POE3->ALR1;
    /* 0-PWM有效电平是低电平， 1-PWM有效电平是高电平 */
    POE3.ALR1_b.OLSG0A = 1;     // MTIOC3B
    POE3.ALR1_b.OLSG0B = 1;     // MTIOC3D
    POE3.ALR1_b.OLSG1A = 1;     // MTIOC4A
    POE3.ALR1_b.OLSG1B = 1;     // MTIOC4C
    POE3.ALR1_b.OLSG2A = 1;     // MTIOC4B
    POE3.ALR1_b.OLSG2B = 1;     // MTIOC4D
    /* 1-使能上述有效电平设置值， 0-禁止上述有效电平设置值(禁止后由MTU.MOCR1A或者MTU.MOCR1B中的有效电平设定) */
    POE3.ALR1_b.OLSEN = 0;
    R_POE3->ALR1 = POE3.ALR1;
#endif    
    
//    bsp_poe3_enable();
//
    /* 初始化时清除POExF标志位 */
    bsp_poe3_stop();
 
}



/***********************************************************************************************************************
* Function Name: R_POE3_Set_HiZ_MTU3_4
* Description  : This function places MTU3 and MTU4 or GPT0 to GPT2 pins in high-impedance.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
/* 将MTU3/MTU4的输出引脚设置为高阻态 */
void bsp_poe3_set_hiz_pwm(void)
{
    /* 软件设置MTU3.MTU4输出进入高阻态 */
    R_POE3->SPOER_b.MTUCH34HIZ = 1U;

    /* 软件设置MTU3.MTU4输出进入高阻态 */
    R_POE3->SPOER_b.MTUCH67HIZ = 1U;
}


/***********************************************************************************************************************
* Function Name: R_POE3_Clear_HiZ_MTU3_4
* Description  : This function places MTU3 and MTU4 or GPT0 to GPT2 pins not in high-impedance.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
/* 将MTU3/MTU4的输出引脚设置为正常受控状态，如果MTU3/MTU4输出使能，则正常输出PWM信号，如果没有使能输出，则没有PWM信号输出 */
void bsp_poe3_clear_hiz_pwm(void)
{
    /* 清除MTU3.MTU4输出高阻态强制 */
    if (1U == R_POE3->SPOER_b.MTUCH34HIZ)
    {
        R_POE3->SPOER_b.MTUCH34HIZ = 0U;
    }

    if (1U == R_POE3->SPOER_b.MTUCH67HIZ)
    {
        R_POE3->SPOER_b.MTUCH67HIZ = 0U;
    }
}

/***********************************************************************************************************************
* Function Name: R_POE3_Start
* Description  : This function starts the POE3 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
 void bsp_poe3_enable(void)
{
   /* 使能POE1中断 */
  R_POE3->ICSR1_b.PIE1 = 1;

   /* 使能POE2中断 */
  R_POE3->ICSR2_b.PIE2 = 1;  
  
   /* 使能POE2中断 */
//  R_POE3->ICSR3_b.PIE3 = 1;    
  
  
}


/***********************************************************************************************************************
* Function Name: R_POE3_Start
* Description  : This function starts the POE3 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
 void bsp_poe3_disable(void)
{
   /* 使能POE1中断 */
  R_POE3->ICSR1_b.PIE1 = 0;

   /* 使能POE2中断 */
  R_POE3->ICSR2_b.PIE2 = 0;  
  
   /* 使能POE2中断 */
//  R_POE3->ICSR3_b.PIE3 = 0;    
  
  
}

/***********************************************************************************************************************
* Function Name: R_POE3_Stop
* Description  : This function stops the POE3 module.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_poe3_stop(void)
{
    /* Clear POE0F flag */
    if (1U == R_POE3->ICSR1_b.POE0F)
    {
        R_POE3->ICSR1_b.POE0F = 0U;
    }

    /* Clear POE4F flag */
    if (1U ==R_POE3->ICSR2_b.POE4F)
    {
        R_POE3->ICSR2_b.POE4F = 0U;
    }

    /* Clear POE8F flag */
    if (1U == R_POE3->ICSR3_b.POE8F)
    {
        R_POE3->ICSR3_b.POE8F = 0U;
    }

    /* Clear POE10F flag */
    if (1U == R_POE3->ICSR4_b.POE10F)
    {
        R_POE3->ICSR4_b.POE10F = 0U;
    }

    if (1U == R_POE3->ICSR5_b.POE11F)
    {
        R_POE3->ICSR5_b.POE11F = 0U;
    }

    /* Clear output short flag 1 */
    if (1U == R_POE3->OCSR1_b.OSF1)
    {
        R_POE3->OCSR1_b.OSF1 = 0U;
    }

    /* Clear output short flag 2 */
    if (1U == R_POE3->OCSR2_b.OSF2)
    {
        R_POE3->OCSR2_b.OSF2 = 0U;
    }

    /* Clear OSTST High-Impedance flag */
    if (1U == R_POE3->ICSR6_b.OSTSTF)
    {
        R_POE3->ICSR6_b.OSTSTF = 0U;
    }
}
/***********************************************************************************************************************
* Function Name: r_mtu0_tgid_interrupt -- taskA interrupt service routine
* Description  : This function is MTU0 TGID[0] interrupt service routine.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
unsigned short isr_startup_cnt=0;
void r_mtu0_tgid_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
  
  if(R_MTU3->TSR_b.TGFD) R_MTU3->TSR_b.TGFD = 0;
   
  if(isr_startup_cnt++ >= 100)
  {
      IntTaskAExec();
      isr_startup_cnt = 100;
  } 
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
}

/***********************************************************************************************************************
* Function Name: r_intcpu0_isr -- taskB interrupt service routine
* Description  : This function is software interrupt 0 interrupt service routine.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void r_intcpu0_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
  
  IntTaskBExec();
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
}
/***********************************************************************************************************************
* Function Name: r_intcpu1_isr -- taskc interrupt service routine
* Description  : This function is software interrupt 1 interrupt service routine.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void r_intcpu1_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
  
  IntTaskCExec();
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
}

/***********************************************************************************************************************
* Function Name: r_peo_isr -- ipm fualt detect
* Description  : 
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void r_mtu3_tgia_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  

//  R_BSP_PinToggle(BSP_IO_REGION_SAFE,BSP_IO_PORT_21_PIN_3);
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
}


/***********************************************************************************************************************
* Function Name: r_peo_isr -- ipm fualt detect
* Description  : 
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void r_peo0_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
  
  // 禁止POE中断
//  bsp_poe3_disable();
//  
//  if(R_POE3->ICSR1_b.POE0F)
//  {
//    // 1轴IPM 故障
//    
//  }
//  
//  if(R_POE3->ICSR2_b.POE4F)
//  {
//    // 2轴IPM 故障
//    
//  }
//  
//  if(R_POE3->ICSR3_b.POE8F)
//  {
//    // todo 
//  }
//  
//  bsp_poe3_stop();
//  
//  bsp_poe3_enable();
  
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
}


/***********************************************************************************************************************
* Function Name: r_intcpu1_isr -- taskc interrupt service routine
* Description  : This function is software interrupt 1 interrupt service routine.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
