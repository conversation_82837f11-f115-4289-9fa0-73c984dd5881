/***********************************************************************************************************************
 * Copyright [2020-2022] Renesas Electronics Corporation and/or its affiliates.  All Rights Reserved.
 *
 * This software and documentation are supplied by Renesas Electronics Corporation and/or its affiliates and may only
 * be used with products of Renesas Electronics Corp. and its affiliates ("Renesas").  No other uses are authorized.
 * Renesas products are sold pursuant to Renesas terms and conditions of sale.  Purchasers are solely responsible for
 * the selection and use of Renesas products and Renesas assumes no liability.  No license, express or implied, to any
 * intellectual property right is granted by Renesas.  This software is protected under all applicable laws, including
 * copyright laws. Renesas reserves the right to change or discontinue this software and/or this documentation.
 * THE SOFTWARE AND DOCUMENTATION IS DELIVERED TO YOU "AS IS," AND <PERSON><PERSON><PERSON>AS MAKES NO REPRESENTATIONS OR WARRANTIES, AND
 * TO THE FULLEST EXTENT PERMISSIBLE UNDER APPLICABLE LAW, DISCLAIMS ALL WARRANTIES, WHETHER EXPLICITLY OR IMPLICITLY,
 * INCLUDING WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NONINFRINGEMENT, WITH RESPECT TO THE
 * SOFTWARE OR DOCUMENTATION.  RENESAS SHALL HAVE NO LIABILITY ARISING OUT OF ANY SECURITY VULNERABILITY OR BREACH.
 * TO THE MAXIMUM EXTENT PERMITTED BY LAW, IN NO EVENT WILL RENESAS BE LIABLE TO YOU IN CONNECTION WITH THE SOFTWARE OR
 * DOCUMENTATION (OR ANY PERSON OR ENTITY CLAIMING RIGHTS DERIVED FROM YOU) FOR ANY LOSS, DAMAGES, OR CLAIMS WHATSOEVER,
 * INCLUDING, WITHOUT LIMITATION, ANY DIRECT, CONSEQUENTIAL, SPECIAL, INDIRECT, PUNITIVE, OR INCIDENTAL DAMAGES; ANY
 * LOST PROFITS, OTHER ECONOMIC DAMAGE, PROPERTY DAMAGE, OR PERSONAL INJURY; AND EVEN IF RENESAS HAS BEEN ADVISED OF THE
 * POSSIBILITY OF SUCH LOSS, DAMAGES, CLAIMS OR COSTS.
 **********************************************************************************************************************/
#ifndef __FLASH_H__
#define __FLASH_H__

/******************************************************************************
 * Macro definitions
 ******************************************************************************/
/* xSPI0 flash area address */
#define QSPI0_START_ADDR          ((uint32_t)0x60000000UL)
#define QSPI0_END_ADDR            ((uint32_t)0x67FFFFFFUL)

/* xSPI0 flash area address */
#define QSPI1_START_ADDR          ((uint32_t)0x68000000UL)
#define QSPI1_END_ADDR            ((uint32_t)0x6FFFFFFFUL)

/* NOR flash area address (CS0 space) */
#define NOR_CS0_START_ADDR        ((uint32_t)0x70000000UL)
#define NOR_CS0_END_ADDR          ((uint32_t)0x73FFFFFFUL)

/* Sector size */
#define QSPI_SECTOR_SIZE          (0x1000U)
#define NOR_SECTOR_SIZE           (0x20000U)

/* Mask value for erace */
#define QSPI_ERASE_MASK           ((uint32_t)0xFFFFF000UL)
#define NOR_ERASE_MASK            ((uint32_t)0xFFFE0000UL)

/* Flash error code */
typedef enum e_flash_err
{
    FLASH_SUCCESS           = 0,
    FLASH_ERR_PARAMETER     = 1,
    FLASH_ERR_MODULE        = 2,
    FLASH_ERR_VERIFY_FAILED = 3,
} flash_err_t;

/******************************************************************************
 * Exported global functions (to be accessed by other files)
 ******************************************************************************/
flash_err_t write_to_qspi_area(uint32_t addr, uint8_t * const p_data, uint32_t data_len);
flash_err_t write_to_nor_area(uint32_t addr, uint8_t * const p_data, uint32_t data_len);
#endif /* __FLASH_H__ */
