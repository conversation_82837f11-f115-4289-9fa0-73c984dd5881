/****************************************************************************************************
 *
 * FILE NAME:  TaskCMain.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.11
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	11-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _TASKC_MAIN_H_
#define	_TASKC_MAIN_H_
	
#include "BaseDef.h"

#define MOTSTP_SPDLVL     50000   // 16777216 x 12rpm/3900rpm = 51622 -> 50000

PUBLIC void TskCInitServoSequence( AXIS_HANDLE *AxisC );
PUBLIC void TskcInputSeqStatus( AXIS_HANDLE *AxisRscC );
PUBLIC void TskcAlarmDetections( AXIS_HANDLE *AxisC );
PUBLIC void TskcServoOnCtrl( AXIS_HANDLE *AxisC );
PUBLIC void TskcBaseDriveSequence( AXIS_HANDLE *AxisC );
PUBLIC void TskcSequenceMain( AXIS_HANDLE *AxisC );
PUBLIC void MonitorParameter(AXIS_HANDLE *AxisC);
PUBLIC void LedGlobleSet(AXIS_HANDLE *AxisC, UINT16 ax_id);

PUBLIC void SysBaseDIDOSlow( AXIS_HANDLE *AxisRscC );



PUBLIC void SysGetDOState( AXIS_HANDLE *AxisC );
#endif // _TASKC_MAIN_H_

