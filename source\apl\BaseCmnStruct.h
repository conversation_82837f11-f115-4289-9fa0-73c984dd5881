/****************************************************************************************************
 *
 * FILE NAME:  BaseCmnStruct.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _BASE_CMN_STRUCT_H_
#define _BASE_CMN_STRUCT_H_

/*--------------------------------------------------------------------------------------------------*/
/*		Basic Loops Stop Mode Definition															*/
/*--------------------------------------------------------------------------------------------------*/


/*--------------------------------------------------------------------------------------------------*/
/*		Basic Loops Mode Definition															        */
/*--------------------------------------------------------------------------------------------------*/
#define  BASE_MODE_TRQ           0x0000
#define  BASE_MODE_SPD           0x0001
#define  BASE_MODE_POS           0x0002
#define  BASE_MODE_JOG           0x0003
#define  BASE_MODE_ZSRCH		 0x0004	
#define  BASE_MODE_VF            0x0005
#define  BASE_MODE_IPD           0x0006

/*--------------------------------------------------------------------------------------------------*/
/*		Basic Loops Stop Mode Definition															*/
/*--------------------------------------------------------------------------------------------------*/
#define  BASE_MODE_ZNONE		0x00			// none zero control		
#define  BASE_MODE_ZSTOP		0x01			// Zero speed stop mode			
#define  BASE_MODE_LDSTOP		0x02			// Deceleration stop mode by linear deceleration
#define  BASE_MODE_ZCLMP		0x03			// Zero clamp mode


/*--------------------------------------------------------------------------------------------------*/
/*		Ethercat Control Mode Definition															*/
/*--------------------------------------------------------------------------------------------------*/
#define  CTRL_MODE_PP            1
#define  CTRL_MODE_PV            3
#define  CTRL_MODE_PT            4
#define  CTRL_MODE_HOME          6
#define  CTRL_MODE_CSP           8
#define  CTRL_MODE_CSV           9
#define  CTRL_MODE_CST           10
#define  MOTOR_MODE_IPD			 11

#define  CTRL_MODE_FUN_POS       ((INT8)(-1))
#define  CTRL_MODE_FUN_SPD       ((INT8)(-3))
#define  CTRL_MODE_FUN_TRQ       ((INT8)(-4))

#define  CTRL_MODE_JOG           ((INT8)(-2))
#define  CTRL_MODE_PJOG          ((INT8)(-5))

#define  CTRL_MODE_INERTIALDENT  ((INT8)(-6))
#define  CTRL_MODE_MOTORIdent    ((INT8)(-7))
#define  CTRL_MODE_PULSE_SPD     ((INT8)(-8))
#define  CTRL_MODE_PULSE_POS     ((INT8)(-9))
#define  CTRL_MODE_ANALOG_SPD    ((INT8)(-10))
#define  CTRL_MODE_ANALOG_TRQ     ((INT8)(-11))
#define  CTRL_MODE_CLAMPINGJAWINIT    ((INT8)(-12))
#define  CTRL_MODE_CLAMPINGJAW    ((INT8)(-13))

#define  CTRL_MODE_NOCMD		 0x0					// No control mode specified (TaskC only)


/*--------------------------------------------------------------------------------------------------*/
/*		Ethercat control stop mode definition										                */
/*--------------------------------------------------------------------------------------------------*/

/*
#define  ZCTRL_MODE_NONE        		0
#define  ZCTRL_MODE_SLOW_STOP    		1
#define  ZCTRL_MODE_QUICK_STOP   		2
#define  ZCTRL_MODE_CUR_STOP     		3
#define  ZCTRL_MODE_VOLT_STOP		    4
*/

/*--------------------------------------------------------------------------------------------------*/
/*		Function switch  mode definition										                */
/*--------------------------------------------------------------------------------------------------*/
#define  FUNC_DYNAMUC_PID       	(1 << 0)
#define  FUNC_ATUOID_SET  			(1 << 1)
#define  FUNC_VIRAXI_SET 			(1 << 2)
#define  FUNC_FUN_TRQ_SET     		(1 << 3)
#define  FUNC_DC_SET	            (1 << 4)
#define  FUNC_CURLOOPTI_SET	        (1 << 5)   // 0:Auto cal, 1:User input 
#define  FUNC_VOLT_FF_SET	        (1 << 6)   // 0:Auto cal, 1:User input 
#define  FUNC_SPDFDB_HR	            (1 << 7)   // 0:monitor spdfdb , 1:high resolution monitor spdfdb [1ms cal cycle]
#define  FUNC_CUR_LIMIT_SET	        (1 << 8)   // 0:enable current limit, 1:disenable current limit
#define  FUNC_ENC_INCABNORMAL_COMP  (1 << 9)   // 0: disable enc increase abnormal compensation, 1: enable enc increase abnormal compensation

/*--------------------------------------------------------------------------------------------------*/
/*		Base Control mode definition																*/
/*--------------------------------------------------------------------------------------------------*/
typedef union CTRLMD {					
	struct	
	{								
		INT8	cm;							// Basic control mode		
		UINT8	cx;							// Extended control mode		
		UINT8	zm;							// Zero control mode
		UINT8	ot;							// OT generation flag	
	} b;

	UINT32	dw;								// Control mode edit(Copy,etc)		
	
	struct 
	{
		UINT16	uwd_l;						// Control mode edit(Copy,etc)	
		UINT16	uwd_h;						// Control mode edit(Copy,etc)	
	} us;
} CTRLMD;


/*--------------------------------------------------------------------------------------------------*/
/*		Zero control mode definition																*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{					
	UINT8	zm;								// Zero control mode						
	UINT8	zmchg;							// Zero control mode change information				
	UINT8	spare0;							// Reserve											
	UINT8	spare1;							// Reserve								
} ZCTRMD;


/*--------------------------------------------------------------------------------------------------*/
/*		Control mode related definitions															*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{					
	CTRLMD			CtrlMode;				// Control Mode								
	CTRLMD			CtrlModeLst;			// Last Control Mode							
	CTRLMD			CtrlModeChg;			// Control Mode Change Info (Created with XOR)			
	ZCTRMD			ZctrlMode;				// Zero Control Mode						
} CTRLMD_SET;

/*--------------------------------------------------------------------------------------------------*/
/*		 Control loop output data structure	            								            */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	INT32			MotPosErr;				// Position amplifier deviation[Pulse]			
	INT32			SpdRefo;				// Speed command output[2^24/OvrSpd]	
	INT32			SpdFFC;					// Speed FF compensation[2^24/OvrSpd]	
	INT32			MotSpd_a;				// Motor speed average[2^24/OvrSpd]		
	INT32			TrqRefo;				// Torque command output[2^24/MaxTrq]		
	INT32			TrqRefo_a;				// Torque command output (average value)[2^24/MaxTrq]	
	INT32			TrqRefMon;				// Torque command monitor[2^24/MaxTrq]		
	INT32			TrqRefMon_a;			// Torque command monitor (average value)[2^24/MaxTrq]		
	REAL32			IdRefMon;				// d-axis current command monitor[A]		
	REAL32			IqRefMon;				// q-axis current command monitor[A]		
	INT16			IqRefMon_a;				// q-axis current command monitor (average value)[A]		
	INT16			IqRefMon_l;				// q-axis current command monitor (previous value)[A]		
	REAL32			IqFbMon;				// q-axis current FB monitor[A]										
	REAL32			IdFbMon;				// d-axis current FB monitor[A]										
	INT16			IqRefFilMon;			// q Axis current command monitor (after filter)					
	BOOL			SvonSpdLmtFlag;			// Speed limit flag during servo ON							
	BOOL			TrqClamp;				// Flag during torque limitation									
} CTRL_LOOP_OUT;

/*--------------------------------------------------------------------------------------------------*/
/*		 Base control output data structure	            								            */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	CTRLMD			CtrlModeOut;			// Control mode(TaskB output)
	INT32			TrqCtrlRef;				// Torque command during torque control[2^24/MaxTrq
	INT32			SpdCtrlRef;				// Speed command at speed control[2^24/OvrSpd]	
	INT32			OutputSpdRef;			// Speed command output value(TaskB->TaskC)[2^24/OvrSpd]	
	INT32			MotPos;					// Motor position (TaskB integrated value)[32bitRingPulse]
	INT32			PcmdCntr;				// Command pulse count value
	INT32			MotSpdSum;				// Motor speed addition value (for calculating TaskC average speed)
	INT32			MotSpdCnt;				// TaskB counter for TaskC average speed calculation		
	INT32			PositionError;			// Position error		
	INT32			SvonSpdLmtReq;			// Speed limit request at servo ON
	INT32			CmdSeqBit;				// Sequence control command(TaskB->TaskC)						
	REAL32			IdRefMon;				// d-axis current command monitor[A]		
	REAL32			IqRefMon;				// q-axis current command monitor[A]		
	INT32			TrqRefMon;				// Torque command monitor[2^24/MaxTrq]	
	BOOL			TblZclamp;				// back to zero	
        INT32                   PosTragetErr;                           // PosTragetErr = target postion - actual target postion  
        INT32                   SpdTragetErr;
        INT32                   TrqTragetErr;        
} BASE_CTRL_OUT;


/*--------------------------------------------------------------------------------------------------*/
/*		 Base sequence output data structure	            								        */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
/* Request commands to the Control module */
	CTRLMD			CtrlModeReq;			/* TaskC Control Mode request							*/
	BOOL			BaseEnableReq;			/* TaskC creation base enable request					*/
	BOOL			BeComplete;				/* Base enable completed status							*/
	BOOL			UdlBeStatus;			/* Base enable completed status (UDL side status monitor)  */
	BOOL			BBSvcRefClrReq;			/* Servo Control Ref. Clear Request Flag				*/
	BOOL			TrqLimitReq;			/* OT torque limit request (OT sequence processing output)  */
	INT32			ModeSwAcc;				/* Motor Acceleration fow ModeSw [NorSpd/TaskC]			*/

/* Sequence Status */
	BOOL			HwbbSts;				/* HWBB status											*/
	BOOL			FstpSts;				/* FSTP status											*/ 
	BOOL			MainPowerOn;			/* Main circuit ON state								*/
	BOOL			MotStop;				/* Motor stopped state									*/
	BOOL			OverTrvlSts;			/* OT status (OT sequence processing output)			*/
	BOOL			SvRdyPhaseIgnore;		/* Servo Ready (Ignore Phase)							*/

	BOOL			PotSigSts;				/* Forward drive prohibited state						*/
	BOOL			NotSigSts;				/* Reverse drive prohibited state						*/
	BOOL			BrkSigSts;				/* Brake released state									*/
	BOOL			EstpSigSts;				/* E-STOP status										*/

	BOOL			TgOnSts;				/* Motor rotation status								*/
	BOOL			VcmpSts;				/* Speed matching state									*/
	BOOL            MotSuspLvl;             /*Motor Suspend Level Flag  							*/

	INT32			RspSeqBits;				/* Sequence control status(ALM/WRN/SVON/SENON/PDET/BK/SRDY/MRDY)*/
	INT32			RspSigBits;				/* Input signal status(HWBB/P-OT/N-OT)					*/
	INT32			RspCtrlBits;			/* Control state(HBB/TGON/VCMP)							*/

	struct 
	{								
		UINT32	Vrefon              :1;		/* 0x10 : Speed  Reference Input On						*/
		UINT32	Trefon              :1;		/* 0x11 : Torque Reference Input On						*/
		UINT32	Prefon              :1;		/* 0x12 : Pulse  Reference Input On						*/
		UINT32	PerrClr             :1;		/* 0x13 : PosErr Clear     Input On						*/
		UINT32	PrefSign            :1;		/* 0x14 : Pulse  Reference Sign  (plus:1, minus:0)		*/
	}f;

	UINT32	AlmRstLast;						/* Alarm reset signal Previous value 					*/

} SEQ_CTRL_OUT;


/*--------------------------------------------------------------------------------------------------*/
/*		Fn function common servo control data														*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	volatile BOOL	FnSvControl;			/* ROUND driving right									*/
	INT32			FnCtrlMcmd;				/* Control mode in ROUND operation						*/
	BOOL			FnSvonReq;				/* Servo ON request in ROUND operation					*/
	BOOL			FnSensOnReq;			/* Servo ON request in ROUND operation					*/
	BOOL			PdetReq;				/* Magnetic pole detection request (energization request)  */
	BOOL			forcedAlmReady;			/* Forced AlmReady flag									*/
	INT32			JogSpeed;				/* JOG speed command +: Forward, -: Reverse				*/
	UINT32          JogTimer;               /* Jog Time conuter [1ms]*/    
	UINT16			SafeStopTime;			/* JOG safety stop time									*/
	INT16			OperatorSts;			/* Operator display status								*/
	BOOL			SvOnPdetReq;			/* Request for servo-on magnetic pole detection		 	*/

	UINT16			ForceOutSignals;		/* Forced signal output command							*/
	UINT16			ForceOutSignalsHW;		/* Forced signal output command (reflected on HW output signal)*/	
	UINT8			OutModeFlg;				/* Forced output mode flag								*/	

	UINT32			LightIntervalTime;		/* 7 SegLed lighting time[100ms]						*/	
	UINT8			PbAndLedChkTestFlag;	/* Push button & 7SegLed confirmation flag				*/	
	UINT16			AnlgLedChkInitFlag;		/* AP push button & 7SegLed confirmation start flag		*/
	UINT32			AnlgLedChkCnt;			/* AP push button & 7SegLed confirmation counter		*/
	UINT32			AnlgLedChkCnt2;			/* AP type push button & 7SegLed confirmation counter (LED on the left bridge)*/
	UINT32			AnlgLedChkLstPnlBtn;	/* AP type push button & 7SegLed confirmation Previous push button*/
	INT8			AnlgMonVoltTstFlag;		/* Analog monitor output voltage confirmation test flag	*/
	UINT16			AnlgMonVoltTstChSel;	/* Analog monitor output voltage confirmation test Ch selection   */
	INT16			OutputVoltVal_Ch0;		/* Analog monitor output voltage confirmation test Output voltage setting(Ch0)*/
	INT16			OutputVoltVal_Ch1;		/* Analog monitor output voltage confirmation test Output voltage setting(Ch1)*/


	struct 
	{
		UINT16	ZsrchState;					/* Origin search completed								*/
	} OpeReg;

	struct
	{
		BOOL		FnSvCtrlDisable;		/* Fn operation not possible							*/
		BOOL		LockNormalOpeDrive;		/* After Fn operation, normal operation prohibition flag  */

		BOOL		TuningRun;				/* Servo tuning in progress								*/
		BOOL		TuningPctrlRun;			/* Servo tuning P control in progress					*/
	} State;

	struct
	{						
		UINT8 PrmCopy;						/* Lcd Operator Parameter copy flag */
		UINT8 Reserved0;					/* reserved */
		UINT8 Reserved1;					/* reserved */
		UINT8 Reserved2;					/* reserved */
	} f;
} FUN_CMN_CONTROL;



#endif

