/****************************************************************************************************
 *
 * FILE NAME:  OpeRegCal.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.12
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	12-08-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Global.h"
#include "RegAccessIf.h"
#include "FunManager.h"


/****************************************************************************************************/
/*		FnF000 : Operating mode								                            			*/
/****************************************************************************************************/
PRM_RSLT fnCalcOprationMode1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
	PRM_RSLT ErrRes;
	if(Cmd == PRM_ACCCMD_READ)
	{
		*pValue = FunGetOpeModeRegister(((AXIS_HANDLE*)Axis)->FnManager,FUN_CH1);
		ErrRes = PRM_RSLT_SUCCESS;
	}
	else
	{
		ErrRes = FunSetOpeModeRegister(((AXIS_HANDLE*)Axis)->FnManager,
									FUN_CH1, (UINT32)*pValue, Axis);
	}

	return ErrRes;

}


/****************************************************************************************************/
/*		FnF001 : Operating setting								                            		*/
/****************************************************************************************************/
PRM_RSLT fnCalcOprationCmd1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
	PRM_RSLT ErrRes;
	ErrRes = PRM_RSLT_SUCCESS;

	if(Cmd == PRM_ACCCMD_READ)
	{
		*pValue = (USHORT)(((AXIS_HANDLE*)Axis)->FnManager)->FunExeState[0].ErrorResult;
	}
	else
	{
		ErrRes = FunSetOpeCommandRegister(((AXIS_HANDLE*)Axis)->FnManager,
										FUN_CH1, (UINT32)*pValue);
	}

	return ErrRes;

}

/****************************************************************************************************/
/*		FnF002 : Operating end								                            			*/
/****************************************************************************************************/
PRM_RSLT fnCalcOperationFinish(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
	PRM_RSLT ErrRes;
	if(Cmd == PRM_ACCCMD_READ)
	{
		ErrRes = PRM_RSLT_PRMNO_ERR;
	}
	else
	{
		FunSetOpeModeRegister(((AXIS_HANDLE*)Axis)->FnManager, FUN_CH1, 0, Axis);
		FunSetOpeModeRegister(((AXIS_HANDLE*)Axis)->FnManager, FUN_CH2, 0, Axis);
		DtrcWriteOpeModeTrcRegister( ((AXIS_HANDLE*)Axis)->TrcHndl, 0 );
		ErrRes = PRM_RSLT_SUCCESS;
	}
	return ErrRes;

}

/****************************************************************************************************/
/*		FnF003 : Access level								                            			*/
/****************************************************************************************************/
PRM_RSLT fnCalcAccessLevel(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
#if 0
	INT32			level; 
	USHORT			passwd;

	FUN_AXCOMMON	*AxCommon;
	PRMDATA 		*Prm;
	REGIF_CONFIG_T	*RegCfg;

	AxCommon = ((AXIS_HANDLE*)Axis)->FnManager->AxCommon;
	Prm = Axis->Prm;
	RegCfg = Axis->RegManager->hPnReg;


	if(Cmd == PRM_ACCCMD_READ)
	{
		level = (INT32)(AxCommon->AccessLevel - 2);		
		*pValue = (level >= 0) ? level : 0; 			
	}
	else
	{
		passwd = (USHORT)*pValue;

		switch(passwd)
		{
		case WRITE_OK_SET_VAL:	/* Write permission (no change in access level) */
				fnCalcPrmAccessLock(Cmd, ArrayIdx, Axis, pValue);
			break;

		case WRITE_NG_SET_VAL:	/*Write protected (no change in access level) */
				fnCalcPrmAccessLock(Cmd, ArrayIdx, Axis, pValue);
			break;

		case ACCLVL_USER1_SET_VAL:	/* User level 1 setting */
				AxCommon->AccessLevel = ACCLVL_USER1;

				Prm->syssw3 = Prm->syssw3 & (~0x0002);
				PrmStoreValueToEeprom( RegCfg, &pndef_syssw3, 0 );
			break;

		case ACCLVL_USER2_SET_VAL:	/* User level 2 setting */
				AxCommon->AccessLevel = ACCLVL_USER2;

			break;

		case ACCLVL_USER3_SET_VAL:	/* User level 3 setting */
				AxCommon->AccessLevel = ACCLVL_USER3;

				Prm->syssw3 = Prm->syssw3 | 0x0002; 								
				PrmStoreValueToEeprom( RegCfg, &pndef_syssw3, 0 );					
			break;

		case ACCLVL_SYSTEM_SET_VAL: /* System level setting */
				AxCommon->AccessLevel = ACCLVL_SYSTEM;
			break;

		default:
			/* Do Nothing */
			break;
		}
		
	}

#endif 

	return PRM_RSLT_SUCCESS;


}


/****************************************************************************************************/
/*		FnF004 : Second operating mode								                            	*/
/****************************************************************************************************/
PRM_RSLT fnCalcOprationMode2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
	PRM_RSLT ErrRes;
	if(Cmd == PRM_ACCCMD_READ)
	{
		*pValue = (INT32)FunGetOpeModeRegister(((AXIS_HANDLE*)Axis)->FnManager,FUN_CH2);
		ErrRes = PRM_RSLT_SUCCESS;
	}
	else
	{
		ErrRes = FunSetOpeModeRegister(((AXIS_HANDLE*)Axis)->FnManager,
									FUN_CH2, (UINT32)*pValue, Axis);
	}

	return ErrRes;


}

/****************************************************************************************************/
/*		FnF005 : Second operating setting								                            */
/****************************************************************************************************/
PRM_RSLT fnCalcOprationCmd2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
	PRM_RSLT ErrRes;
	ErrRes = PRM_RSLT_SUCCESS;

	if(Cmd == PRM_ACCCMD_READ)
	{
		*pValue = (USHORT)(((AXIS_HANDLE*)Axis)->FnManager)->FunExeState[1].ErrorResult;
	}
	else
	{
		ErrRes = FunSetOpeCommandRegister(((AXIS_HANDLE*)Axis)->FnManager,
										FUN_CH2, (UINT32)*pValue);
	}
	return ErrRes;

}


/****************************************************************************************************/
/*		FnF095 : Advanced AT filter type								                            */
/****************************************************************************************************/
PRM_RSLT FnAdatFilType( PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue )
{
	#if NO_ADVFUNC	
	ONEPRMTUNE	*OnePrmTune;
	OnePrmTune = ((AXIS_HANDLE*)Axis)->OnePrmTune;

	if( (*pValue >= 1) && (*pValue <= 3) )
	{
		OnePrmTune->OpeReg.OnePrmFilType = (UINT16)*pValue;
	}
	else
	{
		return( PRM_RSLT_LIMIT_ERR );
	}
	return( PRM_RSLT_SUCCESS );
	#endif
        return( PRM_RSLT_SUCCESS );
}

