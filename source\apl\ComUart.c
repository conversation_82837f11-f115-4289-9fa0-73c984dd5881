/****************************************************************************************************
 *
 * FILE NAME:  ComUart.c
 *
 * DESCRIPTION:  
 *
 * CREATED ON:  2019.06.27
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	27-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "string.h"
#include "ComUart.h"
#include "HardApi.h"
#include "Mlib.h"
#include "RegMngr.h"
#include "RegAccessIF.h"
#include "objdef.h"
//#include "menu.h"
#include "data.h"
#include "DataCache.h"

#include "bsp_uart.h"

#pragma data_alignment=32
ComUartStruct  CommandRecv ;
ComUartStruct  CommandSend ;
ComUartStruct  CommandSendLast  ;
#pragma data_alignment=4

PUBLIC UINT16 EnableMark;
UINT16 SendMark;
UINT16 ReSendMark;
UINT16 RevMark;
PRIVATE UINT16 UartDeviceAddr;

UINT8 UpgradeMark;

extern CO_Data Object_Data;

// ----------servo version variable start---------------------//
CHAR Devicename[]           = DEVICE_NAME;
UINT16 Devicename_Len       = sizeof(Devicename);

//#ifndef _ECAT_CAN_COM_ 
CHAR Softwareversion[]      = MCU_SOFTWARE_VERSION;
CHAR Hardwareversion[]      = DEVICE_HARDWARE_VERSION_CHAR;
VersionStruct VersionTbl[] =
{
  { 0x1008, &Devicename[0],      sizeof(Devicename)},
  { 0x1009, &Hardwareversion[0], sizeof(Hardwareversion)},
  { 0x100A, &Softwareversion[0], sizeof(Softwareversion)},
};

const UINT32 VersionTblNum = sizeof(VersionTbl) / sizeof(VersionTbl[0]);
//#endif
// ----------servo version variable end---------------------//

UINT8  DeviceAddr = 0;
UINT8  AxisId     = 0;
UINT32 PramAddr   = 0;
unsigned char  ComAxisId  = 0;


HmiStruct  gHmiHandle;


/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
PUBLIC void UartAppInit(void)
{
    ;
}

/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
#pragma optimize=none 
PUBLIC void DataCollectSendLoop(AXIS_HANDLE *AxisA,HmiStruct *pHmiHandle)
{
    UINT16 CRC_temp = 0,i = 0,AxisIndex = 0;
    INT16 PointErr = 0;
       
    AxisIndex = AxisA[0].TrcHndl->TrcUpload.AxisIndex;
    AxisIndex = 0;
    
    UINT16 length = 0;
    UINT16 crc_location = 0;
    UINT16 end_location = 0;
    UINT16 crc_calsize  = 0;
#if(USEUSB)    
    ComUartStruct  *Send     = &(pHmiHandle->CommandSend);
    ComUartStruct  *SendLast = &(pHmiHandle->CommandSendLast);
#else
    ComUartStruct  *Send     = &CommandSend;
    ComUartStruct  *SendLast = &CommandSendLast;    
#endif    
    
    if(AxisA[0].BaseLoops->BaseCtrls->BaseEnable == TRUE  )
    {
        EnableMark = 1;
    }
    else
    {
        EnableMark =0;
    }
    
    if(AxisA[AxisIndex].TrcHndl->TrcUpload.bSendEn == 1 )
    {
               
        if(AxisA[AxisIndex].TrcHndl->TrcUpload.FrameCNT == 0)
        {

            if(AxisA[AxisIndex].TrcHndl->TrcExeV.TrcPrelss == 1)
            {
                PointErr = 0;
            }
            else
            {
                PointErr = ((INT16)AxisA[AxisIndex].TrcHndl->TrcReg.TrgPoint -(INT16)AxisA[AxisIndex].TrcHndl->TrcReg.PtrgDnum)*2;
            
            if(AxisA[AxisIndex].TrcHndl->TrcExeV.TrcRepeat == 1 && PointErr < 0)
            {
                PointErr = (INT16)AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize + PointErr;
                }
            }
            
            AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum = AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize/2.0f;
            AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum = (AxisA[AxisIndex].TrcHndl->TrcUpload.ChannelAddr + PointErr) /2.0f;
        }       
        
        if(AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum > 240)
        {
            AxisA[AxisIndex].TrcHndl->TrcUpload.FrameCNT ++;
            
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = 0x03;
            *(UINT16 *)&(Send->Buf[2]) = AxisA[AxisIndex].TrcHndl->TrcUpload.FrameCNT;
            UINT16 Tmp = AxisA[AxisIndex].TrcHndl->TrcUpload.ChannelAddr + AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize 
                                                                - AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum  *2;
            if(Tmp>480)
            {
                *(UINT16 *)&(Send->CommandLenLow) = 960 + 10;

                for(int i=0;i<480;i++)
                {
                    *(INT16 *)&(Send->Buf[4 + 2*i]) = AxisA[AxisIndex].TraceBuffer[2*AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum + i];
                }
                
                AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum  += 240;
                AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum  -= 240;
                
                Send->StartFlag = 0xAA;
                Send->DeviceAddr = AxisA[AxisIndex].TrcHndl->TrcUpload.DeviceAddr;
                Send->FunctionCode = 0x01;

                length =  *(UINT16 *)&(Send->CommandLenLow);
                crc_location = length - 6;
                end_location = length - 4;
                crc_calsize  = length - 2;
                
                // CRC  
                CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize) ; 
                *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
                // EndFlag
                *(UINT16 *)&(Send->Buf[end_location]) = 0x55;
                
                 //UART_TO_PC_STOP();                                 //delete by jrh 2021/11/4
                 SendMark = 1;
            }
             else
            {
                
                *(UINT16 *)&(Send->CommandLenLow) = (Tmp)*2 + 10;
                
                for(int i=0;i<Tmp;i++)
                {
                    *(INT16 *)&(Send->Buf[4 + 2*i])  = AxisA[AxisIndex].TraceBuffer[2*AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum  + i];
                }
                
                 AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum  = AxisA[AxisIndex].TrcHndl->TrcUpload.ChannelAddr / 2.0f;
                 AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum  -= Tmp/2.0f;
                
                 Send->StartFlag = 0xAA;
                 Send->DeviceAddr = AxisA[AxisIndex].TrcHndl->TrcUpload.DeviceAddr;
                 Send->FunctionCode = 0x01;
                 

                length =  *(UINT16 *)&(Send->CommandLenLow);
                crc_location = length - 6;
                end_location = length - 4;
                crc_calsize  = length - 2;
                
                // CRC  
                CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
                *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
                // EndFlag
                *(UINT16 *)&(Send->Buf[end_location]) = 0x55;
                
                 //UART_TO_PC_STOP();
                 SendMark = 1;
            }

            
        }
        else
        {             
            UINT16 Tmp = AxisA[AxisIndex].TrcHndl->TrcUpload.ChannelAddr + AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize 
                                                                - AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum  *2;
            
            AxisA[AxisIndex].TrcHndl->TrcUpload.FrameCNT = 0xFFFF;
            
            if(Tmp > (AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum *2))
            {               
                *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
                *(UINT16 *)&(Send->Buf[1]) = 0x03;
                *(UINT16 *)&(Send->Buf[2]) = AxisA[AxisIndex].TrcHndl->TrcUpload.FrameCNT;
                *(UINT16 *)&(Send->CommandLenLow) = AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum *4 + 10;
                
                for(int i=0;i<AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum*2;i++)
                {
                    *(INT16 *)&(Send->Buf[4 + 2*i]) = AxisA[AxisIndex].TraceBuffer[2*AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum  + i];
                }
                              
                Send->StartFlag = 0xAA;
                Send->DeviceAddr = AxisA[AxisIndex].TrcHndl->TrcUpload.DeviceAddr;
                Send->FunctionCode = 0x01;

                length =  *(UINT16 *)&(Send->CommandLenLow);
                crc_location = length - 6;
                end_location = length - 4;
                crc_calsize  = length - 2;
                
                // CRC  
                CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
                *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
                // EndFlag
                *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

                 //UART_TO_PC_STOP();
                 SendMark = 1;

            }
            else
            {
                *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
                *(UINT16 *)&(Send->Buf[1]) = 0x03;
                *(UINT16 *)&(Send->Buf[2]) = 0xFFFF;
                *(UINT16 *)&(Send->CommandLenLow) = AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum * 4 + 10;
                
                for(int i=0;i<Tmp;i++)
                {
                    *(INT16 *)&(Send->Buf[4 + 2*i]) = AxisA[AxisIndex].TraceBuffer[2*AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum + i];
                }
                
                AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum = AxisA[AxisIndex].TrcHndl->TrcUpload.ChannelAddr / 2.0f;
                AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum -= (Tmp/2.0f);
                
                for(int i=0;i<AxisA[AxisIndex].TrcHndl->TrcUpload.SaveDnum*2;i++)
                {
                    *(INT16 *)&(Send->Buf[4 + 2*Tmp + 2*i]) = AxisA[AxisIndex].TraceBuffer[2*AxisA[AxisIndex].TrcHndl->TrcUpload.WriteDnum  + i];
                }
                
                Send->StartFlag = 0xAA;
                Send->DeviceAddr = AxisA[AxisIndex].TrcHndl->TrcUpload.DeviceAddr;
                Send->FunctionCode = 0x01;
                 
                length =  *(UINT16 *)&(Send->CommandLenLow);
                crc_location = length - 6;
                end_location = length - 4;
                crc_calsize  = length - 2;
                
                // CRC  
                CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
                *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
                // EndFlag
                *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

                 //UART_TO_PC_STOP();
                 SendMark = 1;
                
              
            }
            
            if(AxisA[AxisIndex].TrcHndl->TrcUpload.ChannelAddr + AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize 
                                                                            < AxisA[AxisIndex].TrcHndl->TrcReg.AreaSize)
            {
                AxisA[AxisIndex].TrcHndl->TrcUpload.ChannelAddr += AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize;
                AxisA[AxisIndex].TrcHndl->TrcUpload.FrameCNT = 0;
            }
            									
        }
        
       SendLast->StartFlag = Send->StartFlag;
       *(UINT16 *)&(SendLast->CommandLenLow) = *(UINT16 *)&(Send->CommandLenLow);
       SendLast->DeviceAddr   = Send->DeviceAddr;
       SendLast->FunctionCode = Send->FunctionCode;
       
       for(UINT16 i= 0;i < (*(UINT16 *)&(SendLast->CommandLenLow)-3); i++)
       {
         SendLast->Buf[i] = Send->Buf[i];
       }     
                      
        AxisA[AxisIndex].TrcHndl->TrcUpload.bSendEn = 0;       
    }
    else if(AxisA[AxisIndex].TrcHndl->TrcUpload.bReSendEn)
    {
        //UART_TO_PC_STOP(); 
        
        SendMark = 2;    
        
        AxisA[AxisIndex].TrcHndl->TrcUpload.bReSendEn = 0;      
    }
    else if(AxisA[AxisIndex].TvcLoop->V.TrcUpload.bSendEn == 1 )
    {
               
        if(AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT == 0)
        {      
            if( AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId  < 8)
            {
              AxisA[AxisIndex].TvcLoop->V.TrcUpload.SaveDnum = CacheDL;    //size of uint8_t
              AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex = AxisA[AxisIndex].TvcLoop->V.Mnum;
            }
        }
        
        
        if(AxisA[AxisIndex].TvcLoop->V.TrcUpload.SaveDnum > 240)
        {
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT ++;
            
          *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
          *(UINT16 *)&(Send->Buf[1]) = 0x03;
          *(UINT16 *)&(Send->Buf[2]) = AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT;

           *(UINT16 *)&(Send->CommandLenLow) = 960 + 10;
           
            for(int i=0;i<240;i++)
            {
                AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex++;
                if(AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex >= CacheDL)
                {
                  AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex = 0;
                }
                *(INT32 *)&(Send->Buf[4 + 4*i]) = AxisA[AxisIndex].TvcLoop->V.Data[AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId][AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex];
            }               
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.SaveDnum  = AxisA[AxisIndex].TvcLoop->V.TrcUpload.SaveDnum  - 240;
             
                
            Send->StartFlag = 0xAA;
            Send->DeviceAddr = AxisA[AxisIndex].TvcLoop->V.TrcUpload.DeviceAddr;
            Send->FunctionCode = 0x03;
            
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;
            
             //UART_TO_PC_STOP();
             SendMark = 1;               
        }
        else if(AxisA[AxisIndex].TvcLoop->V.TrcUpload.SaveDnum > 0)
        {                         
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = 0x03;
            
            *(UINT16 *)&(Send->CommandLenLow) = 64*4 + 10;
             AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT = 0xFFFF;
            *(UINT16 *)&(Send->Buf[2]) = AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT;
         
             UINT16 Tmp2 = 64;
         
            for(int i=0;i< Tmp2;i++)
            {
                AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex++;
                
                if(AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex >= CacheDL)
                {
                  AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex = 0;
                }                    
                
                *(INT32 *)&(Send->Buf[4 + 4*i]) = AxisA[AxisIndex].TvcLoop->V.Data[AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId][AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex];
            }    
            
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.SaveDnum  = 0; 
            
            Send->StartFlag = 0xAA;
            Send->DeviceAddr = AxisA[AxisIndex].TvcLoop->V.TrcUpload.DeviceAddr;
            Send->FunctionCode = 0x03;
             
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

             AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId ++;

             AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT = 0;
             
             //UART_TO_PC_STOP();
             SendMark = 1;            									
        }
        else if( AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId  > 7)
        {       
              AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId  = 0;
              AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT = 0;
              
              Send->StartFlag = 0xAA;
              *(UINT16 *)&(Send->CommandLenLow) = 10;
              Send->DeviceAddr = AxisA[AxisIndex].TvcLoop->V.TrcUpload.DeviceAddr;
              Send->FunctionCode = 0x03;          
              *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
              *(UINT16 *)&(Send->Buf[1]) = 0x03;
              *(UINT16 *)&(Send->Buf[2]) = 0xFFFF;  
              
              length =  *(UINT16 *)&(Send->CommandLenLow);
              crc_location = length - 6;
              end_location = length - 4;
              crc_calsize  = length - 2;
              
              // CRC  
              CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
              *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
              // EndFlag
              *(UINT16 *)&(Send->Buf[end_location]) = 0x55;
              

             // UART_TO_PC_STOP(); 
              SendMark = 1;                    
        }
        
       SendLast->StartFlag = Send->StartFlag;
       *(UINT16 *)&(SendLast->CommandLenLow) = *(UINT16 *)&(Send->CommandLenLow);
       SendLast->DeviceAddr   = Send->DeviceAddr;
       SendLast->FunctionCode = Send->FunctionCode;
       
       for(UINT16 i= 0;i < (*(UINT16 *)&(SendLast->CommandLenLow)-3); i++)
       {
         SendLast->Buf[i] = Send->Buf[i];
       }

        AxisA[AxisIndex].TvcLoop->V.TrcUpload.bSendEn = 0;       
    }
    else if(AxisA[AxisIndex].TvcLoop->V.TrcUpload.bReSendEn == 1)
    {
        //UART_TO_PC_STOP(); 
        
        SendMark = 2;    
        
        AxisA[AxisIndex].TvcLoop->V.TrcUpload.bReSendEn = 0;
    }
    
}

/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
PRIVATE void PcWriteParam(ComUartStruct *Receive,ComUartStruct *Send)
{
    UINT16 CRC_temp = 0;
    UINT16 ParamEntry = 0,ParamLen = 0,ParamNum = 0,Index = 0;
    UINT8  Subindex = 0;
    UINT8  WriteState = 0, WriteStateLock = 0;
    UINT32 AddrLock = 0;
    UINT32 ErrorCnt = 0;
    AXIS_HANDLE *AxisA = NULL;
    
    AxisA = (AXIS_HANDLE*)GetAxisHandle(0);
    
    ParamNum = *(UINT16 *)&(Receive->Buf[0]);
        
    UINT16 length = 0;
    UINT16 crc_location = 0;
    UINT16 end_location = 0;  
    UINT16 crc_calsize  = 0;       
    
	ParamEntry = 2;

	while(ParamNum)
	{

//        ControlSource = 1;
        
        ParamLen = *(UINT32 *)&(Receive->Buf[ParamEntry]) & 0xFF;  
        
        if(ParamLen & 0x80)
        {
           ParamLen =  (ParamLen-0x80) /8 + 0x80;
        }
        else
        {
           ParamLen = ParamLen /8;
        }
				
				 
        Subindex =  (*(UINT32 *)&(Receive->Buf[ParamEntry]) & 0xFF00)>>8;
        Index = ((*(UINT32 *)&(Receive->Buf[ParamEntry]) & 0xFFFF0000)>>16);     
        
        
//        if(((0x6000 == (Index & 0xFF00))||(0x6800 == (Index & 0xFF00))) 
//                &&(AxisA[0].BaseCtrls->DcSyncSig))
//        {
//            WriteState = ABORTIDX_DATA_CANNOT_BE_ACCESSED_BECAUSE_OF_LOCAL_CONTROL;
//            break;
//        }
//	else
//        {
        WriteState = PrmUartObjWrite( Index, Subindex, ParamLen, 
                            (UINT16 *)&(Receive->Buf[ParamEntry+4]),FALSE);
        
//        }
        
        if(WriteState > 0)
        {
           WriteStateLock = WriteState;   
           
//          *(UINT32 *)&(Send->Buf[ErrorCnt*5]) = *(UINT32 *)&Receive->Buf[ParamEntry]; // 写错误的参数地址
//          *(UINT8 *)&(Send->Buf[ErrorCnt*5 + 4])  = WriteState;                           // 写错误状态
//          ErrorCnt++;
           
          *(UINT32 *)&(Send->Buf[0]) = *(UINT32 *)&(Receive->Buf[ParamEntry]);
          *(UINT8 *)&(Send->Buf[4]) = WriteState;    
        
          ParamLen = ParamLen & 0x7F;
          ParamEntry = ParamEntry + 4 + ParamLen;
          ParamNum--;            
        }
        else
        {
            ParamLen = ParamLen & 0x7F;
            ParamEntry = ParamEntry + 4 + ParamLen;
            ParamNum--;
        }       
	}

    if(WriteStateLock > 0)
    {    
        *(UINT16 *)&(Send->CommandLenLow) = 11;       
        Send->StartFlag = Receive->StartFlag;
        Send->DeviceAddr = Receive->DeviceAddr;
        Send->FunctionCode = Receive->FunctionCode + 0x80;
        
 //       *(UINT32 *)&(Send->Buf[0]) = *(UINT32 *)&(Receive->Buf[ParamEntry]);
 //       *(UINT8 *)&(Send->Buf[4]) = WriteState;
                    
        length = *(UINT16 *)&(Send->CommandLenLow);
        crc_location = length -6;
        end_location = length -4;  
        crc_calsize  = length -2;                     
        
        // CRC    
        CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
        *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
        // EndFlag
        *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

        // UART_TO_PC_STOP();
         SendMark = 1;             
    }
    else
    {
        *(UINT16 *)&(Send->Buf[0]) = *(UINT16 *)&(Receive->Buf[0]);
        
        *(UINT16 *)&(Send->CommandLenLow) = 8;       
        Send->StartFlag = Receive->StartFlag;
        Send->DeviceAddr = Receive->DeviceAddr;
        Send->FunctionCode = Receive->FunctionCode;
        
        length = *(UINT16 *)&(Send->CommandLenLow);
        crc_location = length -6;
        end_location = length -4;  
        crc_calsize  = length -2;                     
        
        // CRC    
        CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
        *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
        // EndFlag
        *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

         //UART_TO_PC_STOP();
         SendMark = 1;       
    }        
}

/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
#pragma optimize=none
PRIVATE void PcReadParam(ComUartStruct *Receive,ComUartStruct *Send)
{
    UINT16  CRC_temp = 0, ParamEntry= 0, ParamLen = 0, ParamNum = 0;
    UINT16  Index = 0,TotalNum = 0;
    UINT8   Subindex = 0;
    UINT8   ReadState = 0, ReadStateLock = 0;
    INT8    VerIndex = 0;
    OBJCONST TOBJECT OBJMEM * pObject = NULL;
    OBJCONST TSDOINFOENTRYDESC OBJMEM * pEntryDesc = NULL;
    UINT16 *pVarPtr = 0;
    UINT8 dataType = 0;
   
    ParamNum = *(UINT16 *)&(Receive->Buf[0]);
    TotalNum = *(UINT16 *)&(Receive->Buf[0]);
     
    UINT16 length = 0;
    UINT16 crc_location = 0;
    UINT16 end_location = 0;  
    UINT16 crc_calsize  = 0;   
    
	ParamEntry = 6;

    while(ParamNum)
    {
        ParamLen = (*(UINT32 *)&(Receive->Buf[2+4*(TotalNum-ParamNum)]) & 0xFF)/8;
        Index = ((*(UINT32 *)&(Receive->Buf[2+4*(TotalNum-ParamNum)]) & 0xFFFF0000)>>16) ;
        Subindex = (*(UINT32 *)&(Receive->Buf[2+4*(TotalNum-ParamNum)]) & 0xFF00)>>8;
                 
        if(Index >= 0x2000)
        {             
            ReadState = PrmEcatObjRead( Index, Subindex, ParamLen, 
                                    (UINT16 *)&(Send->Buf[ParamEntry]),FALSE);
            *(UINT32 *)&(Send->Buf[ParamEntry-4]) = *(UINT32 *)&(Receive->Buf[2+4*(TotalNum-ParamNum)]);     
        }       
        else
        {
#if (_ECAT_CAN_COM_ == 0)
            VerIndex = -1;
            for(UINT8 i = 0 ; i< VersionTblNum; i++)
            {
              if(Index == VersionTbl[i].Index)
              {
                VerIndex = i;
                break;
              }
            }
            
//            if(VerIndex == -1)
//            {
//                ReadState = 0x08;
//                *(UINT32 *)&(Send->Buf[ParamEntry-4]) = *(UINT32 *)&(Receive->Buf[2+4*(TotalNum-ParamNum)]);    
//            }
//            else
            {               
                ParamLen =  VersionTbl[VerIndex].Length - 1;
                
                for(UINT8 i = 0;i< ParamLen;i++)
                {
                  (Send->Buf[ParamEntry + i]) =  *(VersionTbl[VerIndex].pName + i);
                }
                *(UINT32 *)&(Send->Buf[ParamEntry-4]) = *(UINT32 *)&(Receive->Buf[2+4*(TotalNum-ParamNum)]);  
                *(UINT8 *)&(Send->Buf[ParamEntry-4]) =  ParamLen;       
            } 
#elif (_ECAT_COM == 1)
            pObject = OBJ_GetObjectHandle(Index);            
            if(pObject == 0)
            {
                ReadState = 0x08;
                *(UINT32 *)&(Send->Buf[ParamEntry-4]) = *(UINT32 *)&(Receive->Buf[2+4*(TotalNum-ParamNum)]);    
            }
            else
            {               
            ParamLen = OBJ_GetObjectLength( Index, Subindex, pObject, FALSE);
            ReadState = OBJ_Read( Index, Subindex, ParamLen, pObject, (UINT16 *)&(Send->Buf[ParamEntry]), FALSE );
            *(UINT32 *)&(Send->Buf[ParamEntry-4]) = *(UINT32 *)&(Receive->Buf[2+4*(TotalNum-ParamNum)]);  
            *(UINT8 *)&(Send->Buf[ParamEntry-4]) =  ParamLen;       
            }    
#else
            if(ParamLen == 0x00) // string
            {										
                    ParamLen = CAN_GetObjectLength(&Object_Data, Index, Subindex);
            }
        
            ReadState = getODentry(&Object_Data, Index, Subindex, (void *)(UINT16 *)&(Send->Buf[ParamEntry],(UINT32 *)&ParamLen,&dataType,1);
            *(UINT32 *)&(Send->Buf[ParamEntry-4] = *(UINT32 *)&Receive->Buf[2+4*(TotalNum-ParamNum)] ;  
            *(UINT8 *)&(Send->Buf[ParamEntry-4] =  ParamLen;   	           
#endif            
        }
       
        if(ReadState > 0)
        {
//            break;
            if(ParamLen == 1)
            {
              *(UINT8 *)&(Send->Buf[ParamEntry]) = ReadState;
            }
            else if(ParamLen ==2 )
            {
              *(UINT16 *)&(Send->Buf[ParamEntry]) = (UINT16)(0x00FF & ReadState);
            }
            else if(ParamLen == 4)
            {
              *(UINT32 *)&(Send->Buf[ParamEntry]) = (UINT32)(0x000000FF & ReadState);
            }
            else
            {
              ParamLen = 2;
              *(UINT16 *)&(Send->Buf[ParamEntry]) = (UINT16)(0x00FF & ReadState);
            }
            
            *(UINT32 *)&(Send->Buf[ParamEntry-4]) |=  0x00001000;  
            
            ReadStateLock = ReadState;
            
            ParamEntry = ParamEntry + 4 + ParamLen;
            ParamNum--;
        }
        else
        {
            ParamEntry = ParamEntry + 4 + ParamLen;
            ParamNum--;
        }
    }

    if(ReadStateLock > 0)
    {    
        *(UINT16 *)&(Send->Buf[0]) = *(UINT16 *)&(Receive->Buf[0]);
        
        *(UINT16 *)&(Send->CommandLenLow) = ParamEntry + 6 -4 ;     // ReadState   
        Send->StartFlag = Receive->StartFlag;
        Send->DeviceAddr = Receive->DeviceAddr;
        Send->FunctionCode = Receive->FunctionCode;
        
        length = *(UINT16 *)&(Send->CommandLenLow) ;
        crc_location = length - 6;
        end_location = length - 4;
        crc_calsize =  length - 2;
        // CRC    
        CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
        
        *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
        // EndFlag
        *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

         SendMark = 1;       
    }
    else
    {
        *(UINT16 *)&(Send->Buf[0]) = *(UINT16 *)&(Receive->Buf[0]);
        
        *(UINT16 *)&(Send->CommandLenLow) = ParamEntry + 6 -4;
        Send->StartFlag = Receive->StartFlag;
        Send->DeviceAddr = Receive->DeviceAddr;
        Send->FunctionCode = Receive->FunctionCode;
        
        length = *(UINT16 *)&(Send->CommandLenLow) ;
        crc_location = length - 6;
        end_location = length - 4;
        crc_calsize =  length - 2;
        
        // CRC    
        CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
        
        *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
        // EndFlag
        *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

         SendMark = 1;
    }        

}

/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
PRIVATE void PcDataCollect(ComUartStruct *Receive,ComUartStruct *Send)
{
    UINT16 ParamEntry = 0,CRC_temp = 0;
    UINT8 AxisIndex = 0,ExeCode = 0;
    UINT16 result = NO_ERROR;  
   
    UINT16 length = 0;
    UINT16 crc_location = 0;
    UINT16 end_location = 0;
    UINT16 crc_calsize  = 0;    
    
    AXIS_HANDLE *AxisA = GetAxisHandle(0);
    
//    Receive->Buf[0] = 0;
    AxisIndex = *(UINT8 *)&(Receive->Buf[0]);
    ExeCode = *(UINT8 *)&(Receive->Buf[1]);
    
    AxisIndex = 0;
    
    switch(ExeCode)
    {
        case 1:
            AxisA[AxisIndex].TrcHndl->TrcReg.DataSize = 4;
            AxisA[AxisIndex].TrcHndl->TrcExeP.TrcDataType = DATATRACE_LONG;
            AxisA[AxisIndex].TrcHndl->TrcReg.OpeModeTrc = OPEMODE_DATATRACE;  

            AxisA[AxisIndex].TrcHndl->TrcReg.TrcMode = *(UINT8 *)&(Receive->Buf[2]);
            AxisA[AxisIndex].TrcHndl->TrcReg.SampIntv = *(UINT16 *)&(Receive->Buf[3]);
            
            ParamEntry = 15;
            for(int i=0;i < AxisA[AxisIndex].TrcHndl->TrcReg.TrcMode;i++)
            {
               AxisA[AxisIndex].TrcHndl->TrcIncivPrm.TrcData[i] = *(UINT8 *)&(Receive->Buf[ParamEntry]);
               ParamEntry = ParamEntry+1;
            }

            // TrcBuff is UINT16 ,TrcBuffData is INT32, TrcBuffSize = 2* TrcBufNumber       
            AxisA[AxisIndex].TrcHndl->TrcReg.MaxAreaSize = MAX_COLLECT_BUFFER_NUM; 
            AxisA[AxisIndex].TrcHndl->TrcReg.MaxTrcBuffSize = (UINT16)MAX_COLLECT_BUFFER_NUM 
                                                                / AxisA[AxisIndex].TrcHndl->TrcReg.TrcMode;      
            AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize = (*(UINT16 *)&(Receive->Buf[5])) * 2 ;

            if(AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize > AxisA[AxisIndex].TrcHndl->TrcReg.MaxTrcBuffSize)
            {
               AxisA[AxisIndex].TrcHndl->TrcReg.TrcBuffSize = AxisA[AxisIndex].TrcHndl->TrcReg.MaxTrcBuffSize; 
            }
                    
            AxisA[AxisIndex].TrcHndl->TrcReg.TrgSel = *(UINT16 *)&(Receive->Buf[7]);     
            AxisA[AxisIndex].TrcHndl->TrcReg.TrgLevel =  *(INT32 *)&(Receive->Buf[9]);
            AxisA[AxisIndex].TrcHndl->TrcReg.PtrgDnum = *(UINT16 *)&(Receive->Buf[13]);
            
            
            AxisA[AxisIndex].TrcHndl->TrcUpload.FrameCNT = 0;
            AxisA[AxisIndex].TrcHndl->TrcUpload.ChannelAddr = 0;
            AxisA[AxisIndex].TrcHndl->TrcUpload.bSendEn = 0;

            AxisA[AxisIndex].TrcHndl->AxisPtr = &AxisA[AxisIndex];        
            DtrcInitDtrcManager( AxisA[AxisIndex].TrcHndl, AxisA[AxisIndex].TraceBuffer);
            DtrcWriteOpeCmdTrcRegister( AxisA[AxisIndex].TrcHndl,  0x0001 );

            
            Send->StartFlag = Receive->StartFlag;
            *(UINT16 *)&(Send->CommandLenLow) = 10;
            Send->DeviceAddr = Receive->DeviceAddr;
            Send->FunctionCode = Receive->FunctionCode;

            
            // feedback
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = ExeCode;
                                     
            *(UINT16 *)&(Send->Buf[2]) = result;
            
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize) ; 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

            //UART_TO_PC_STOP(); 
             SendMark = 1;            
            break;
            
        case 2:
            if(AxisA[AxisIndex].TrcHndl->TrcReg.TrcState == 1 
                || AxisA[AxisIndex].TrcHndl->TrcReg.TrcState == 2 )
            {
                result = 1;
            }
            else if(AxisA[AxisIndex].TrcHndl->TrcReg.TrcState == 3 )
            {
                result = 2;
            }
            else
            {
                result = 3;
            }
            
            Send->StartFlag = Receive->StartFlag;
            *(UINT16 *)&(Send->CommandLenLow) = 10;
            Send->DeviceAddr = Receive->DeviceAddr;
            Send->FunctionCode = Receive->FunctionCode;

            // feedback
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = ExeCode;
                                     
            *(UINT16 *)&(Send->Buf[2]) = result;
            
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize) ; 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

            // UART_TO_PC_STOP();
             SendMark = 1;           
            break;        
        case 3:
            AxisA[0].TrcHndl->TrcUpload.AxisIndex = AxisIndex;
            AxisA[AxisIndex].TrcHndl->TrcUpload.bSendEn = 1;
            AxisA[AxisIndex].TrcHndl->TrcUpload.DeviceAddr = Receive->DeviceAddr;
            
            break;                
        case 4:
            DtrcWriteOpeCmdTrcRegister( AxisA[AxisIndex].TrcHndl,  0x0000 );
            AxisA[AxisIndex].TrcHndl->TrcReg.TrcState = 0;
            AxisA[AxisIndex].TrcHndl->TrcUpload.bSendEn = 0;
        
            Send->StartFlag = Receive->StartFlag;
            *(UINT16 *)&(Send->CommandLenLow) = 10;
            Send->DeviceAddr = Receive->DeviceAddr;
            Send->FunctionCode = Receive->FunctionCode;

            // feedback
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = ExeCode;
                                     
            *(UINT16 *)&(Send->Buf[2]) = result;
            
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

            //UART_TO_PC_STOP(); 
             SendMark = 1;           
            break;
        default :
            Send->StartFlag = Receive->StartFlag;
            *(UINT16 *)&(Send->CommandLenLow) = 10;
            Send->DeviceAddr = Receive->DeviceAddr;
            Send->FunctionCode = Receive->FunctionCode;

            // feedback
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = ExeCode;
                                     
            *(UINT16 *)&(Send->Buf[2]) = result;
            
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

             //UART_TO_PC_STOP(); 
             SendMark = 1;      
        
            break;
        
    }

}
/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
PRIVATE void FaultDataCollect(ComUartStruct *Receive, ComUartStruct *Send)
{
    UINT16 ParamEntry,CRC_temp;
    UINT8 AxisIndex,ExeCode;
    UINT16 result = NO_ERROR;  
    AXIS_HANDLE *AxisA = GetAxisHandle(0);
    
    AxisIndex = *(UINT8 *)&(Receive->Buf[0]);
    ExeCode = *(UINT8 *)&(Receive->Buf[1]);
    
    UINT16 length = 0;
    UINT16 crc_location = 0;
    UINT16 end_location = 0;  
    UINT16 crc_calsize  = 0;       
    
    switch(ExeCode)
    {
        case 1:
        {
            Send->StartFlag = Receive->StartFlag;
            *(UINT16 *)&(Send->CommandLenLow) = 10;
            Send->DeviceAddr = Receive->DeviceAddr;
            Send->FunctionCode = Receive->FunctionCode;
            
            // feedback
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = ExeCode;
        
            *(UINT16 *)&(Send->Buf[2]) = result;
            
            
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

            // UART_TO_PC_STOP(); 
             SendMark = 1;           
            break;
        }      
        case 2:
        {

            if(AxisA[AxisIndex].TvcLoop->V.FinishFlay == 1)
            {
                result = 1;   //ÒÑ´¥·¢¹ÊÕÏ±£´æ
                AxisA[AxisIndex].TvcLoop->V.TrcUpload.bSendEn = 0;
                AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId = 0;
                AxisA[AxisIndex].TvcLoop->V.TrcUpload.DataIndex = 0;
                AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT = 0;
                AxisA[AxisIndex].TvcLoop->V.TrcUpload.SaveDnum = CacheDL * 8 *4;    //size of uint8_t
                AxisA[AxisIndex].TvcLoop->V.TrcUpload.WriteDnum = 0;
            }
            else
            {
                result = 0;   //Î´´¥·¢¹ÊÕÏ±£´æ
            }
        
            Send->StartFlag = Receive->StartFlag;
            *(UINT16 *)&(Send->CommandLenLow) = 10;
            Send->DeviceAddr = Receive->DeviceAddr;
            Send->FunctionCode = Receive->FunctionCode;

            // feedback
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = ExeCode;
        
            *(UINT16 *)&(Send->Buf[2]) = result;
    
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

            // UART_TO_PC_STOP(); 
             SendMark = 1;           
            break;
        }
        case 3:
        {
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.bReSendEn   = 0;
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.bSendEn   = 1;
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.DeviceAddr = Receive->DeviceAddr;
            break;
        }
        case 4:
        {
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.bSendEn = 0;
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT = 0;
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId = 0;  
          
            AxisA[AxisIndex].TvcLoop->V.ReStare = 1;
        
            Send->StartFlag = Receive->StartFlag;
            *(UINT16 *)&(Send->CommandLenLow) = 10;
            Send->DeviceAddr = Receive->DeviceAddr;
            Send->FunctionCode = Receive->FunctionCode;

            // feedback
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = ExeCode;
                                     
            *(UINT16 *)&(Send->Buf[2]) = NO_ERROR;
                                     
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;
            // UART_TO_PC_STOP(); 
             SendMark = 1;           
            break;
        }
        case 5:
        {
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.bSendEn = 0;
            
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.FrameCNT = 0;
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.ChannelId = 0; 
      
            Send->StartFlag = Receive->StartFlag;
            *(UINT16 *)&(Send->CommandLenLow) = 10;
            Send->DeviceAddr = Receive->DeviceAddr;
            Send->FunctionCode = Receive->FunctionCode;

            // feedback
            *(UINT16 *)&(Send->Buf[0]) = AxisIndex;
            *(UINT16 *)&(Send->Buf[1]) = ExeCode;
        
            *(UINT16 *)&(Send->Buf[2]) = NO_ERROR;
                         
            length =  *(UINT16 *)&(Send->CommandLenLow);
            crc_location = length - 6;
            end_location = length - 4;
            crc_calsize  = length - 2;
            
            // CRC  
            CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
            *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
            // EndFlag
            *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

             //UART_TO_PC_STOP(); 
             SendMark = 1;   
            
            break;
        }
        case 6:
        {
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.bReSendEn   = 1;
            AxisA[AxisIndex].TvcLoop->V.TrcUpload.bSendEn = 0;
            break;
        }        
    }


}
/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
PRIVATE void PcFeedback(ComUartStruct *Receive,ComUartStruct *Send)
{
    UINT16  CRC_temp = 0,CommCNT=0;
    
    UINT16 length = 0;
    UINT16 crc_location = 0;
    UINT16 end_location = 0;
    UINT16 crc_calsize  = 0;    
              	    
    Send->StartFlag = Receive->StartFlag;
    *(UINT16 *)&(Send->CommandLenLow) = *(UINT16 *)&(Receive->CommandLenLow);
    Send->DeviceAddr = Receive->DeviceAddr;
    Send->FunctionCode = Receive->FunctionCode;
    
	// feedback    
    while(CommCNT < *(UINT16 *)&(Send->CommandLenLow) - 6)
    {
       *(UINT8 *)&(Send->Buf[CommCNT]) = *(UINT8 *)&(Receive->Buf[CommCNT]);
        CommCNT++ ;
    }
    
    length =  *(UINT16 *)&(Send->CommandLenLow);
    crc_location = length - 6;
    end_location = length - 4;
    crc_calsize  = length - 2;
    
    // CRC  
    CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
    *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
    // EndFlag
    *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

//     UART_TO_PC_STOP();
     SendMark = 1;
}

/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
PRIVATE void PcReadErrorLog(ComUartStruct *Receive,ComUartStruct *Send)
{
    UINT16 CRC_temp = 0;
    UINT8 AxisIndex = 0;
    UINT16 result = NO_ERROR;  
    AXIS_HANDLE *AxisA = GetAxisHandle(0);
    
    AxisIndex = *(UINT8 *)&Receive->Buf[0];
    
    UINT16 length = 0;
    UINT16 crc_location = 0;
    UINT16 end_location = 0;
    UINT16 crc_calsize  = 0;    
    
    AxisIndex = 0;
   
    Send->StartFlag = Receive->StartFlag;
    *(UINT16 *)&(Send->CommandLenLow) = 60 + 7 ;
    Send->DeviceAddr = Receive->DeviceAddr;
    Send->FunctionCode = Receive->FunctionCode;

    Send->Buf[0] = AxisIndex;
    
    for(int i=0;i<10;i++)
    {
        *(INT16 *)&(Send->Buf[1 + 6*i]) = AxisA[AxisIndex].AlmMngr->Log.AlmLog[i].AlmCode;
        *(INT32 *)&(Send->Buf[3 + 6*i]) = AxisA[AxisIndex].AlmMngr->Log.AlmLog[i].TmStamp;       
    }
    
    length =  *(UINT16 *)&(Send->CommandLenLow);
    crc_location = length - 6;
    end_location = length - 4;
    crc_calsize  = length - 2;
    
    // CRC  
    CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
    *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
    // EndFlag
    *(UINT16 *)&(Send->Buf[end_location]) = 0x55;

     //UART_TO_PC_STOP();
    if(Receive->DeviceAddr != 0xFF)
    {
      SendMark = 1;
    }
     
    
    
}
/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
PRIVATE void OnlineUpgrade(ComUartStruct *Receive,ComUartStruct *Send)
{
    UINT16 CRC_temp = 0;
    UINT8  ExeCode = 0;
    UINT16 result = NO_ERROR;  

    UINT16 length = 0;
    UINT16 crc_location = 0;
    UINT16 end_location = 0;
    UINT16 crc_calsize  = 0;      
    
    AXIS_HANDLE *AxisA = GetAxisHandle(0);
    
    ExeCode = *(UINT8 *)&(Receive->Buf[0]);    
    
    if(EnableMark == 0)
    {
      Main_Menu();       // delete  by jrh 2021/11/4
    }
    else
    {   
        Send->StartFlag = Receive->StartFlag;
        Send->DeviceAddr = Receive->DeviceAddr;
        Send->FunctionCode = Receive->FunctionCode + 0x80;      
        *(UINT16 *)&(Send->CommandLenLow) = *(UINT16 *)&(Receive->CommandLenLow) ;
        *(UINT8 *)&(Send->Buf[0]) = 0xFF;
        
        length =  *(UINT16 *)&(Send->CommandLenLow);
        crc_location = length - 6;
        end_location = length - 4;
        crc_calsize  = length - 2;
        
        // CRC  
        CRC_temp = GetCRC16((UINT8*)&(Send->CommandLenLow), crc_calsize); 
        *(UINT16 *)&(Send->Buf[crc_location]) = CRC_temp;
        // EndFlag
        *(UINT16 *)&(Send->Buf[end_location]) = 0x55;
        //UART_TO_PC_STOP();         
        SendMark = 1;      
    }
           
}
/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
PRIVATE void FdbUartError(UINT16 ErrorCode)
{


}

/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
#pragma optimize=none 
BOOL RoserBusTransmit(ComUartStruct *Receive, ComUartStruct *Send,UINT32 DevCID)
{
    UINT16 wk = 0;

    UINT16 length = *(UINT16 *)&(Receive->CommandLenLow);
    UINT16 crc_location = length - 6;
    UINT16 end_location = length - 4;
    UINT16 crc_calsize  = length - 2;
    
    if((Receive->DeviceAddr!=DevCID) && (Receive->DeviceAddr!= 0xFF)&&(Receive->DeviceAddr!= 0xFE))
    {    
      // todo   
      return FALSE;
        
    }
    
    UartDeviceAddr = Receive->DeviceAddr;
       
      
    UINT16 crc_tmp = GetCRC16((UINT8*)&(Receive->CommandLenLow),crc_calsize);    
    
    UINT16 crc_rev = *(UINT16 *)&Receive->Buf[crc_location] ;   
    
    if((crc_tmp != crc_rev) || (Receive->Buf[end_location] != 0x55 ) )
    {
        // todo 
        return FALSE;
    }
    switch(Receive->FunctionCode)  
    {
    case 0x01 :   // data collection
        PcDataCollect(Receive, Send);
        break;
    case 0x02 :   
        PcReadErrorLog(Receive,Send);
        break;
    case 0x03 :    
        FaultDataCollect(Receive,Send);
        break;
    case 0x04 :   

        break;
    case 0x05 :   // 

        break;
    case 0x06 :   //

        break;
    case 0x07 :   // 

        break;
    case 0x08 :   // 
        PcFeedback(Receive,Send);
        break; 
    case 0x09 :   // 

        break;
    case 0x0A :   // 

        break;    
    case 0x0B :   // 

        break;    
    case 0x0C :   // 

        break;
    case 0x0D :   // 
        PcReadParam(Receive,Send);
        break;                
    case 0x0E :   // 
        PcWriteParam(Receive,Send);
        break; 
    case 0x0F :   // 
                        
        break;   
    case 0x10 :   // 
        OnlineUpgrade(Receive,Send);                                
        break;             
    default :
        break;
    }

    return TRUE;
        

}

/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
void UartSendData(void)
{
  
#if(USEUSB)

#else
    if(SendMark == 1)
    {
        if(UartDeviceAddr == 0xFF)
        {
              R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_LOW);
              bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);  
              SendMark = 0;
        }
        else
        {
              UART_TO_PC_STOP();        
              R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_HIGH);
              sci_sci4_dma_tx((UINT8 *)&CommandSend,*(UINT16 *)&CommandSend.CommandLenLow + 2);   

              SendMark = 0;
        }
    }
    else if(SendMark == 2)
    {
        if(UartDeviceAddr == 0xFF)
        {
           R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_LOW);
           bsp_sci4_receive((uint8_t *)&CommandRecv,UART_RECV_MAX_NUM);  
           SendMark = 0;
        }
        else
        {
          UART_TO_PC_STOP();        
          R_BSP_PinWrite(PORT_485SZ_DE,PIN_LEVEL_HIGH);
          sci_sci4_dma_tx((UINT8 *)&CommandSendLast,*(UINT16 *)&CommandSendLast.CommandLenLow + 2);

          SendMark = 0;
        }      
    }
#endif    
  
}


/*********************************************************************************************************
 CRC16
*********************************************************************************************************/
#define CRC_SEED 0xFFFF
#define POLY16 0x1021

PRIVATE UINT8 auchCRCHi[] =
{
	0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,0x01,0xC0,
	0x80,0x41,0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,
	0x00,0xC1,0x81,0x40,0x00,0xC1,0x81,0x40,0x01,0xC0,
	0x80,0x41,0x01,0xC0,0x80,0x41,0x00,0xC1,0x81,0x40,
	0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,0x00,0xC1,
	0x81,0x40,0x01,0xC0,0x80,0x41,0x01,0xC0,0x80,0x41,
	0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,0x00,0xC1,
	0x81,0x40,0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,
	0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,0x01,0xC0,
	0x80,0x41,0x00,0xC1,0x81,0x40,0x00,0xC1,0x81,0x40,
	0x01,0xC0,0x80,0x41,0x01,0xC0,0x80,0x41,0x00,0xC1,
	0x81,0x40,0x01,0xC0,0x80,0x41,0x00,0xC1,0x81,0x40,
	0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,0x01,0xC0,
	0x80,0x41,0x00,0xC1,0x81,0x40,0x00,0xC1,0x81,0x40,
	0x01,0xC0,0x80,0x41,0x00,0xC1,0x81,0x40,0x01,0xC0,
	0x80,0x41,0x01,0xC0,0x80,0x41,0x00,0xC1,0x81,0x40,
	0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,0x01,0xC0,
	0x80,0x41,0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,
	0x00,0xC1,0x81,0x40,0x00,0xC1,0x81,0x40,0x01,0xC0,
	0x80,0x41,0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,
	0x01,0xC0,0x80,0x41,0x00,0xC1,0x81,0x40,0x01,0xC0,
	0x80,0x41,0x00,0xC1,0x81,0x40,0x00,0xC1,0x81,0x40,
	0x01,0xC0,0x80,0x41,0x01,0xC0,0x80,0x41,0x00,0xC1,
	0x81,0x40,0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,
	0x00,0xC1,0x81,0x40,0x01,0xC0,0x80,0x41,0x01,0xC0,
	0x80,0x41,0x00,0xC1,0x81,0x40
};

PRIVATE UINT8 auchCRCLo[] =
{
	0x00,0xC0,0xC1,0x01,0xC3,0x03,0x02,0xC2,0xC6,0x06,
	0x07,0xC7,0x05,0xC5,0xC4,0x04,0xCC,0x0C,0x0D,0xCD,
	0x0F,0xCF,0xCE,0x0E,0x0A,0xCA,0xCB,0x0B,0xC9,0x09,
	0x08,0xC8,0xD8,0x18,0x19,0xD9,0x1B,0xDB,0xDA,0x1A,
	0x1E,0xDE,0xDF,0x1F,0xDD,0x1D,0x1C,0xDC,0x14,0xD4,
	0xD5,0x15,0xD7,0x17,0x16,0xD6,0xD2,0x12,0x13,0xD3,
	0x11,0xD1,0xD0,0x10,0xF0,0x30,0x31,0xF1,0x33,0xF3,
	0xF2,0x32,0x36,0xF6,0xF7,0x37,0xF5,0x35,0x34,0xF4,
	0x3C,0xFC,0xFD,0x3D,0xFF,0x3F,0x3E,0xFE,0xFA,0x3A,
	0x3B,0xFB,0x39,0xF9,0xF8,0x38,0x28,0xE8,0xE9,0x29,
	0xEB,0x2B,0x2A,0xEA,0xEE,0x2E,0x2F,0xEF,0x2D,0xED,
	0xEC,0x2C,0xE4,0x24,0x25,0xE5,0x27,0xE7,0xE6,0x26,
	0x22,0xE2,0xE3,0x23,0xE1,0x21,0x20,0xE0,0xA0,0x60,
	0x61,0xA1,0x63,0xA3,0xA2,0x62,0x66,0xA6,0xA7,0x67,
	0xA5,0x65,0x64,0xA4,0x6C,0xAC,0xAD,0x6D,0xAF,0x6F,
	0x6E,0xAE,0xAA,0x6A,0x6B,0xAB,0x69,0xA9,0xA8,0x68,
	0x78,0xB8,0xB9,0x79,0xBB,0x7B,0x7A,0xBA,0xBE,0x7E,
	0x7F,0xBF,0x7D,0xBD,0xBC,0x7C,0xB4,0x74,0x75,0xB5,
	0x77,0xB7,0xB6,0x76,0x72,0xB2,0xB3,0x73,0xB1,0x71,
	0x70,0xB0,0x50,0x90,0x91,0x51,0x93,0x53,0x52,0x92,
	0x96,0x56,0x57,0x97,0x55,0x95,0x94,0x54,0x9C,0x5C,
	0x5D,0x9D,0x5F,0x9F,0x9E,0x5E,0x5A,0x9A,0x9B,0x5B,
	0x99,0x59,0x58,0x98,0x88,0x48,0x49,0x89,0x4B,0x8B,
	0x8A,0x4A,0x4E,0x8E,0x8F,0x4F,0x8D,0x4D,0x4C,0x8C,
	0x44,0x84,0x85,0x45,0x87,0x47,0x46,0x86,0x82,0x42,
	0x43,0x83,0x41,0x81,0x80,0x40
};

/***********************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
***********************************************************************/
//PUBLIC UINT16 GetCRC16(UINT8 *buf, UINT16 length)
UINT16 GetCRC16(UINT8 *buf, UINT16 length)
{
	UINT8  uchCRCHi = 0xFF ;
	UINT8  uchCRCLo = 0xFF ;
	UINT16 uIndex ;
	while (length--)
	{
		uIndex = uchCRCHi ^ *buf++ ;
		uchCRCHi = uchCRCLo ^ auchCRCHi[uIndex];
		uchCRCLo = auchCRCLo[uIndex] ;
	}
	return ((uchCRCHi << 8) | uchCRCLo);
}
