@echo off
REM Designed by <PERSON><PERSON>J
REM Adds a CRC32 checksum to the end of a binary file.
REM Leverages srec_cat.exe for CRC32 calculation and appending.

SETLOCAL EnableDelayedExpansion

REM Get the script directory and parent directory
SET "SCRIPT_DIR=%~dp0"
SET "PARENT_DIR=%SCRIPT_DIR%..\.."

REM Get current date in yyyy_mm_dd format using WMIC (more reliable across locales)
FOR /F "tokens=2 delims==" %%a IN ('wmic os get LocalDateTime /VALUE') DO SET "DATETIME=%%a"
SET "YYYY=%DATETIME:~0,4%"
SET "MM=%DATETIME:~4,2%"
SET "DD=%DATETIME:~6,2%"
SET "DATE_STR=%YYYY%%MM%%DD%"

REM Define input and output paths
SET "INPUT_FILE=%PARENT_DIR%\RF200.bin"
SET "BINARY_DIR=%PARENT_DIR%\Binary"
SET "OUTPUT_FILE=%BINARY_DIR%\MeteoriteX01_%DATE_STR%.bin"

REM Create Binary directory if it doesn't exist
IF NOT EXIST "%BINARY_DIR%" (
    ECHO Creating Binary directory...
    MKDIR "%BINARY_DIR%"
)

IF "%~1" NEQ "" SET "INPUT_FILE=%~1"
IF "%~2" NEQ "" SET "OUTPUT_FILE=%~2"

ECHO -------------------------------------
ECHO Adding CRC32 checksum to %INPUT_FILE%
ECHO Output will be saved as %OUTPUT_FILE%
ECHO -------------------------------------

REM **1. Configuration & Error Handling:**

REM Define the path to srec_cat.exe.  Crucially, we check if it exists.
REM Here, the path is set to the script directory.

SET "SREC_PATH=%SCRIPT_DIR%"
SET "SREC_CAT_EXE=%SREC_PATH%srec_cat.exe"

ECHO Looking for srec_cat.exe at: %SREC_CAT_EXE%

IF NOT EXIST "%SREC_CAT_EXE%" (
    ECHO ERROR: srec_cat.exe not found at "%SREC_CAT_EXE%"
    ECHO Please ensure srec_cat is in the script directory or the path is correct.
    EXIT /B 1  REM Exit with an error code
)

REM **2. Robust File Existence Check:**

IF NOT EXIST "%INPUT_FILE%" (
    ECHO ERROR: Input file "%INPUT_FILE%" not found.
    EXIT /B 2 REM Exit with an error code
)

REM **3.Determine File Size (Safely and Accurately):**

REM We use a FOR loop to get the file size.  This is the standard method in batch scripting.
FOR %%i IN ("%INPUT_FILE%") DO (
  SET "FILE_SIZE=%%~zi"
)

ECHO File size of "%INPUT_FILE%": %FILE_SIZE% bytes

REM **4. Execute srec_cat with Enhanced Error Handling and Logging:**

ECHO Calculating and appending CRC32 checksum...

REM Construct the srec_cat command.  The crucial part is `-crc32-l-e %FILE_SIZE%`.
REM   - `-crc32-l-e`:  Calculate a 32-bit CRC, using little-endian byte order for the result, and append it to the end of the input.
REM   - `%FILE_SIZE%`: Specifies the end address for the CRC calculation (the end of the file).

SET "SREC_COMMAND="%SREC_CAT_EXE%" "%INPUT_FILE%" -Binary -crop 0 %FILE_SIZE% -crc32-l-e %FILE_SIZE% -o "%OUTPUT_FILE%" -Binary"

ECHO Executing: %SREC_COMMAND%

REM Execute the command and capture the error code.  This is vital for detecting failures.
REM Capture both stdout and stderr to a log file for detailed debugging.
REM We'll also print the stdout to the console.

%SREC_COMMAND% > "%SCRIPT_DIR%srec_cat.log" 2>&1

IF %ERRORLEVEL% NEQ 0 (
    ECHO ERROR: srec_cat command failed with error code %ERRORLEVEL%.
    ECHO Check the srec_cat documentation and your command syntax.
    ECHO See srec_cat.log for detailed error information.
    TYPE "%SCRIPT_DIR%srec_cat.log"  REM Display the log contents in the console
    EXIT /B 3 REM Exit with an error code
)

ECHO CRC32 checksum successfully added to "%OUTPUT_FILE%".
ECHO.
ECHO Done.

ENDLOCAL
EXIT /B 0
