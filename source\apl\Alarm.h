/****************************************************************************************************
 *
 * FILE NAME:  Alarm.h
 *
 * DESCRIPTION: 
 *
 * CREATED ON:  2019.06.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _ALARM_H_
#define _ALARM_H_

#include "BaseDef.h"
#include "EepromIF.h"

#define  ALM_WARNING_AUTO_CLEAR          TRUE


#define  ALM_BIT_TBL_NUM		    4	
#define  ALM_TBL_MAX	            32								
#define  ALM_BIT_NUM                (32 * ALM_BIT_TBL_NUM)	
#define  WRN_BIT_NUM	            (32 * ALM_BIT_TBL_NUM)	

#define  ALM_LOG_NUM                10

/*--------------------------------------------------------------------------------------------------*/
/*		 Alarm attribute definitions			    	            					            */
/*--------------------------------------------------------------------------------------------------*/
#define ALMDEF_ATTR_WARNING	    	0x01		// warning (1), alarm (0)
#define ALMDEF_ATTR_NOEEPROM	    0x02		// EEPROM non storage (1), EEPROM storage (0)
#define ALMDEF_ATTR_COMALARM	    0x04		// Communications alarm (1), amplifier alarm (0)
#define ALMDEF_ATTR_NOTRESET    	0x08		// Reset disable (1), reset enable (0)
//#define ALMDEF_ATTR_MENCDANG	    0x10		// Disable motor encoder data (1)
//#define ALMDEF_ATTR_POSNG		    0x20		// position data disable(1)

/*--------------------------------------------------------------------------------------------------*/
/*		OL alarm information definition												                */
/*--------------------------------------------------------------------------------------------------*/
#define ALMDEF_OLINFO_OLF			0x01		// Overload						
#define ALMDEF_OLINFO_RGOL			0x02		// Regenerative overload					
#define ALMDEF_OLINFO_DBOL			0x04		// DB overload					

typedef enum 
{
	NO_ALARM		= 0,		    //{ 0x0000, 0x02, 0x0F },No Alarm 
	ALM_EEPROM,				        //{ 0x5530, 0x08, 0x0F },Eeprom error  
    ALM_EEPROM_VER,				    //{ 0x5531, 0x08, 0x0F },Eeprom Version error  
	ALM_OS,                         //{ 0x8400, 0x00, 0x0F },over speed   
	ALM_ORUN,                       //{ 0x0FFF, 0x00, 0x0F },over run
	ALM_OV, 					    //{ 0x3210, 0x00, 0x0F },over voltage	
	ALM_OC,                         //{ 0x2320, 0x00, 0x0F },over current
	ALM_PUV,                        //{ 0x3220, 0x00, 0x01 },under voltage
	ALM_WIR,                        //{ 0x3321, 0x00, 0x0F },Main circuit wiring error
	ALM_VDC,					    //{ 0x7303, 0x00, 0x0F },Abnormal main circuit detector
	ALM_PRM,					    //{ 0x6320, 0x00, 0x0F },Parameter setting error 		
	ALM_CONVIO,                     //{ 0xFF03, 0x00, 0x0F },Power supply related input signal error
	ALM_PWPHASE,				    //{ 0x3130, 0x00, 0x01 },Power failure phase	
	ALM_RG,                         //{ 0x7111, 0x00, 0x0F },Regenerative abnormality
	ALM_RGOL,                       //{ 0x7112, 0x00, 0x0F },Regenerative over load
	ALM_AD,                         //{ 0x5210, 0x00, 0x0F },Abnormal AD sampling circuit
    ALM_ECAT_INIT,                  //{ 0x0E12, 0x00, 0x01 },Ethercat initialization error
    ALM_ISR_TIME,                   //{ 0xFF82, 0x00, 0x0F },Scheduler error
    ALM_IPM,                        //{ 0x4230, 0x00, 0x0F },IPM Fault
    ALM_OF,							//{ 0x8611, 0x00, 0x01 },over position error
	ALM_BEOF,						//{ 0x8612, 0x00, 0x0F },over position error at servo on
	ALM_BEVLMTOF,					//{ 0x8613, 0x00, 0x01 },over position error at servo on when speed limit
	ALM_PHSFND,                     //{ 0x0FFE, 0x00, 0x0F },Phase find alarm
	ALM_ENCBRK,                     //{ 0x7380, 0x00, 0x0F },encoder communication break
	ALM_ENCCRC,                     //{ 0x738A, 0x00, 0x0F },encoder CRC error
	ALM_ENCDATA, 					//{ 0xFF04, 0x00, 0x0F },encoder data error
	ALM_ENCOS, 					    //{ 0x7387, 0x00, 0x0F },encoder over speed
	ALM_ENCCE, 					    //{ 0x7385, 0x00, 0x0F },encoder counter error
	ALM_ENCOF, 					    //{ 0x7389, 0x00, 0x0F },encoder counter overflow
	ALM_ENCOH, 					    //{ 0x7384, 0x08, 0x01 },encoder over heat
	ALM_ENCME, 					    //{ 0x7383, 0x00, 0x0F },encoder multi-turn error
	ALM_ENCBE, 					    //{ 0x7382, 0x00, 0x01 },encoder battery error
	ALM_OLF1,              		    //{ 0xFF25, 0x00, 0x01 },Overload (instantaneous maximum load)
	ALM_OLF2,         				//{ 0x3230, 0x00, 0x01 },Overload (continuous maximum load)
	ALM_MIDT,					    //{ 0xFFFE, 0x00, 0x0F },motor parameter identification error
    ALM_FAN_TEMP,                   //{ 0x4210, 0x00, 0x01 },Over FAN temperation error
	ALM_ECAT_DC,					//{ 0xFF84, 0x00, 0x01 },ethercat synchronization error
    ALM_STO1,                       //{ 0xFF1D, 0x00, 0x0F },STO1 error
    ALM_STO2,                       //{ 0xFF1C, 0x00, 0x0F },STO2 error
    ALM_FPGA_VER,                   //{ 0xFF30, 0x00, 0x0F },fpga software version errror
	ALM_STALL,              	    //{ 0x7121, 0x00, 0x01 },Motro stall error
	ALM_VIB,						//{ 0xF0FF, 0x00, 0x01 },Vibration alarm			
	ALM_AT,							//{ 0xF0FE, 0x00, 0x0F },Auto tuning alarm
	ALM_PRMUNMATCH,				    //{ 0xF0FD, 0x00, 0x0F },Parameter combination error	
	ALM_PHASEANGLE,					//{ 0xF0FC, 0x00, 0x0F },Phase information mismatch								
	ALM_POLE,						//{ 0xF0FB, 0x00, 0x0F },Magnetic pole detection failure						
	ALM_PFINDSTOP,					//{ 0xF0FA, 0x00, 0x0F },Overtravel detection at magnetic pole detection	
	ALM_PDET,						//{ 0xF0F9, 0x00, 0x0F },Magnetic pole detection incomplete			
	ALM_PDET_MOVEOVER,				//{ 0xF0F8, 0x00, 0x0F },Excessive range of magnetic pole detection		
	ALM_PDET_NG,					//{ 0xF0F7, 0x00, 0x0F },Magnetic pole detection failure 2	
	ALM_AIX1_GL,					//{ 0xFFF1, 0x00, 0x0F },Device Global Alarm for Aix_1
	ALM_VIRCOM,						//{ 0xFF90, 0x00, 0x01 },virtual serial communication Error
    ALM_HARDWARE_VER,				//{ 0x5532, 0x00, 0x0F },Hardware Version error 
    ALM_ECAT_Break,				    //{ 0xFF91, 0x00, 0x01 },ethercat break error   
    ALM_DRIVER,				        //{ 0xFF11, 0x00, 0x0F },Driver error
    ALM_LOWVOL,                     //{ 0xFF12, 0x00, 0x0F },24V error
    ALM_PWMClose,                   //{ 0xFF13, 0x00, 0x0F },PWM Close
	ALM_RLY,          				//{ 0xFF92, 0x00, 0x0F },relay error
    ALM_PARMERR,          			//{ 0xFA01, 0x00, 0x0F },Parameter Err
    ALM_IDENTERR,          			//{ 0xFA02, 0x00, 0x0F },Motor Identification
    ALM_RSVD3,          			//{ 0xFA03, 0x00, 0x0F },reserved
    ALM_RSVD4,						//{ 0xFA04, 0x00, 0x0F },reserved
    ALM_RSVD5,			//{ 0xFA05, 0x00, 0x0F },reserved         [bit64]
    ALM_RSVD6,			//{ 0xFA06, 0x00, 0x0F },reserved
    ALM_UID,			//{ 0xFA07, 0x00, 0x01 },reserved
    ALM_DICFG,			//{ 0xFA08, 0x00, 0x01 },DI Duplicate allocation
    ALM_POWIDERR,		//{ 0xFA09, 0x00, 0x0F },reserved
    ALM_FSIN,			//{ 0xFA10, 0x00, 0x01 },DI fast stop in vliad
    ALM_CUR_SMP,		//{ 0xFA11, 0x00, 0x0F },Phase Current initail failed 
    ALM_HPOSLMT,		//{ 0xFA12, 0x00, 0x0F },Hardware Positive Position limit
    ALM_HNEGLMT,		//{ 0xFA13, 0x00, 0x0F },Hardware Negative Position limit
    ALM_IPM_OTM,		//{ 0xFA14, 0x00, 0x01 },IPM OTM
    ALM_ECAT_EEPROM,	        //{ 0xFA15, 0x00, 0x0F },ECAT_EEPROM write eeror
    ALM_ENC_WEEPROM,		//{ 0xFA16, 0x00, 0x0F },ENC_EEPROM Write Error
    ALM_BUSOC,		        //{ 0xFA17, 0x08, 0x0F },reserved
    ALM_PHALACK,	        //{ 0xFA18, 0x00, 0x01 },
    ALM_ENC_REEPROM,		//{ 0xFA19, 0x00, 0x01 },ENC_EEPROM Read Error
    ALM_ENC_NEEPROM,		//{ 0xFA20, 0x00, 0x01 },ENC_EEPROM Empty Error
    ALM_EncoderNone,		//{ 0xFA21, 0x00, 0x01 },reserved
    ALM_RSVD22,			//{ 0xFA22, 0x00, 0x01 },reserved         [bit79]
    ALM_RSVD23,			//{ 0xFA23, 0x00, 0x01 },reserved         [bit80]
    ALM_RSVD24,			//{ 0xFA24, 0x00, 0x01 },reserved
    ALM_RSVD25, 		//{ 0xFA25, 0x00, 0x01 },reserved
    ALM_RSVD26, 		//{ 0xFA26, 0x00, 0x01 },reserved
    ALM_RSVD27, 		//{ 0xFA27, 0x00, 0x01 },reserved
    ALM_RSVD28, 		//{ 0xFA28, 0x00, 0x01 },reserved
    ALM_RSVD29, 		//{ 0xFA29, 0x00, 0x01 },reserved
    ALM_RSVD30, 		//{ 0xFA30, 0x00, 0x01 },reserved
    ALM_RSVD31, 		//{ 0xFA31, 0x00, 0x01 },reserved
    ALM_RSVD32, 		//{ 0xFA32, 0x00, 0x01 },reserved
    ALM_RSVD33, 		//{ 0xFA33, 0x00, 0x01 },reserved
    ALM_RSVD34, 		//{ 0xFA34, 0x00, 0x01 },reserved
    ALM_RSVD35, 		//{ 0xFA35, 0x00, 0x01 },reserved
    ALM_RSVD36, 		//{ 0xFA36, 0x00, 0x01 },reserved
    ALM_RSVD37, 		//{ 0xFA37, 0x00, 0x01 },reserved 		
    ALM_RSVD38, 		//{ 0xFA38, 0x00, 0x01 },reserved 		[bit95]     
    
    WRN_OF,			//{ 0xFFFD, 0x01, 0x00 } over position error
    WRN_BEOF,			//{ 0xFFFC, 0x01, 0x00 } over position error at servo on
    WRN_BOOT,                   //{ 0xFFFB, 0x01, 0x00 } need reboot (Parameter change)
    WRN_UV,		        //{ 0xFFFA, 0x01, 0x00 } Undervoltage
    WRN_EXREG,                  //{ 0xFFF9, 0x01, 0x00 } Regenerative warning
    WRN_RGOLF,                  //{ 0xFFF8, 0x01, 0x00 } Regenerative over load warning
    WRN_ENCBA,                  //{ 0x7381, 0x01, 0x00 } encoder battery alarm
    WRN_OLF,                	//{ 0xFF31, 0x01, 0x00 } Overload
    WRN_MIDT,			//{ 0xFFF7, 0x01, 0x00 } motor parameter identification warning
    WRN_VIB,			//{ 0xF00F, 0x01, 0x00 } Vibration warning
    WRN_POS,                    //{ 0x8600, 0x01, 0x00 } Following position warning
    WRN_POSLIMIT,               //{ 0x8601, 0x01, 0x00 } position postive over limit warning
    WRN_FAN,                    //{ 0xFFF6, 0x01, 0x00 } FAN warning
    WRN_RSVD1,          	//{ 0xFC01, 0x01, 0x00 } reserved
    WRN_RSVD2,          	//{ 0xFC02, 0x01, 0x00 } reserved
    WRN_PolePair,          	//{ 0xFC03, 0x01, 0x00 } reserved
    WRN_NEG_POSLIMIT,       //{ 0xFC04, 0x01, 0x00 } position negative over limit warning                   [bit112]
    WRN_OLF1,                //{ 0xFF31, 0x01, 0x00 } Overload,          	//{ 0xFC05, 0x01, 0x00 } reserved
    WRN_RSVD6,          	//{ 0xFC06, 0x01, 0x00 } reserved
    WRN_RSVD7,          	//{ 0xFC07, 0x01, 0x00 } reserved
    WRN_RSVD8,          	//{ 0xFC08, 0x01, 0x00 } reserved
    WRN_RSVD9,          	//{ 0xFC09, 0x01, 0x00 } reserved
    WRN_RSVD10,          	//{ 0xFC10, 0x01, 0x00 } reserved
    WRN_RSVD11,          	//{ 0xFC11, 0x01, 0x00 } reserved
    WRN_RSVD12,          	//{ 0xFC12, 0x01, 0x00 } reserved
    WRN_RSVD13,          	//{ 0xFC13, 0x01, 0x00 } reserved
    WRN_RSVD14,          	//{ 0xFC14, 0x01, 0x00 } reserved
    WRN_RSVD15,          	//{ 0xFC15, 0x01, 0x00 } reserved
    WRN_RSVD16,          	//{ 0xFC16, 0x01, 0x00 } reserved
    WRN_RSVD17,          	//{ 0xFC17, 0x01, 0x00 } reserved
    WRN_RSVD18,          	//{ 0xFC18, 0x01, 0x00 } reserved    
    WRN_RSVD19,          	//{ 0xFC19, 0x01, 0x00 } reserved                   [bit127]    
}ALMID_T;
	
/*--------------------------------------------------------------------------------------------------*/
/*		 Alarm table Struct 		    	            								            */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	UINT16	AlmCode;				// alarm code
	UCHAR	AlmAttr;				// alarm attribute
	UCHAR	StopMode;				// method of stopping when alarm occurs
} ALM_TBL_DEF;

typedef struct
{
	ALMID_T             AlmId;
	ALM_TBL_DEF*        AlmDef;
	UINT32              TmStamp;
} ALM_TBL;

/* Alarm Trace Header */
typedef union 
{
	UINT16			w;
	struct
	{
		UINT8		Idx;
		UINT8		Cnt;
	} b;
} ALM_LOG_HDR;

/* Alarm Trace Data Table */  
typedef struct 
{
	UINT16		     AlmCode;				
	UINT32		     TmStamp;			
} ALM_LOG_DATA;

typedef	struct 
{
	UINT32			  AlmCnt;					// Alarm count(Occurring)			
	UINT32			  LstAlmCnt;				// History registration number			

	ALM_TBL			  AlmTbl[ALM_TBL_MAX];	    // Alarm registration table

	ALM_LOG_HDR	      AlmLogHdr;				// Alarm log: Index&Count	
	ALM_LOG_DATA	  AlmLog[ALM_LOG_NUM];	    // Alarm log table	
	ALM_LOG_DATA*     AlmLogSp;				    // Alarm log last pointer

	UINT32			  EepAlmOLInfo;			    // Alarm Log overload information 

} ALM_LOG;


/*--------------------------------------------------------------------------------------------------*/
/*		 Alarm status Struct 		    	            								            */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	BOOL	        AlmFlag        :1;
	BOOL	        WrnFlag        :1;
	BOOL            MencDataNG     :1;
	BOOL            DevAlmFlag     :1;
	BOOL            DevWrnFlag     :1;
	UINT16	        StopMode	   :4;	
	UINT16			AlmOLInfo	   :4;
	UINT16			AlmResetInfo   :1;
	UINT16          Rsvd           :2;
	
	UINT16     	    AlmCode;

}ALM_STATUS;


/*--------------------------------------------------------------------------------------------------*/
/*		 Parameter error information definition		    	            						    */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	UINT8	ErrFlag;					/* Error flag (for online)*/
	UINT8	ErrCount;					/* Error count (for offline use)*/
	UINT16	ErrPrmNo;					/* Parameter number (parameter error)*/
} PRMERR;


/*--------------------------------------------------------------------------------------------------*/
/*		 Alarm Manager Struct 		    	            								            */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{ 
	BOOL            AlmRst;                              // Alarm reset request
	BOOL            WrnRst;                              // Warning reset request
	BOOL            AlmEepRst;                           // Alarm log eeprom reset request
	BOOL			AlmClear;		                     // Clear specific alarm requests
    BOOL            AlmTmStampRst;                       // Alarm TimeStamp request
	ALMID_T			ClrAlmId;	
	BOOL			LedWarn;

	ALM_STATUS      Status;
	BOOL			AlmReady;			                 // Alarm ready		
	PRMERR			PrmErrInfo;							 // Parameter error information		
	ALM_LOG         Log;
		
	UINT32			AlmSet[ALM_BIT_TBL_NUM];		
	UINT32			AlmSetMsk[ALM_BIT_TBL_NUM];
	UINT32			AlmRstMsk[ALM_BIT_TBL_NUM];	

	UINT32			StopModeParam;		
	ALM_TBL_DEF     *DefTable;
	UINT16			TableEntryNum;		                 // Number of Table entry 

	UINT32			myAsicNo;		                     // axis number 
	
	EEP_QUE_BUFFER		EepQueBuf[32];					 // Eeprom Queue buffer 
	EEP_QUE_HANDLE		EepQue;							 // Eeprom Queue 
	
	volatile BOOL	AlmEepRstBusy;						 // EEPROM alarm log reset BusyFlag
	UINT16			LtAlmReq;						     // Alarm latch EEPROM write request
	UINT16			LtDtWtCt;							 // Alarm latch data writing counter flag
	UINT32			LtRomAdr;						     // Write EEPROM address
	UINT16			*LtMemAdr;							 // Write memory address				
	UINT16			*AlmCdAdr;							 // Alarm code storage address			
	UINT32			*TmStpAdr;							 // Alarm timestamp storage address
    BOOL            CurrntLimitFlag;
//	void			*hAlmLatchHndl;						 // Alarm latch data					

}ALARM;

PUBLIC void ALMInitGlobalHandle(ALARM **AxisTopHandle, INT32 AxisNum);
PUBLIC void ALMSetGlobalAlarm(ALMID_T alm_id);
PUBLIC void ALMClearGlobalAlarm(ALMID_T alm_id);
PUBLIC BOOL ALMCheckGlobalAlarm(ALMID_T alm_id);

PUBLIC UINT32 ALMStartupProcedure(ALARM *AlmMngr, UINT8 StopModeParam);

PUBLIC void ALMInitAlarm(ALARM *AlmMngr, ALM_TBL_DEF *AlmDefTbl, UINT16 AlmDefCnt, UINT32 AxisNumber);
PUBLIC ALM_TBL_DEF* ALMSetServoAlarm(ALARM *AlmMngr, ALMID_T alm_id);
PUBLIC void ALMLogWriteService(ALARM *AlmMngr);
PUBLIC void ALMSetMask(ALARM *AlmMngr, ALMID_T alm_id, BOOL MaskSet);
PUBLIC BOOL ALMCheckEachState(ALARM *AlmMngr, ALMID_T alm_id);
PUBLIC BOOL ALMGetOtherAlarmChk(ALARM *AlmMngr, ALMID_T alm_id);
PUBLIC BOOL ALMCheckSetMask(ALARM *AlmMngr, ALMID_T alm_id);
PUBLIC BOOL ALMCheckSetMask(ALARM *AlmMngr, ALMID_T alm_id);
PUBLIC void ALMSetPramError(ALARM *AlmMngr, UINT16 ErrPrmNo);
PUBLIC void ALMClearWarning(ALARM *AlmMngr, ALMID_T alm_id);
PUBLIC UINT32 ALMGetStatus(ALARM *AlmMngr, BOOL *AlmFlag, BOOL *WrnFlag);
PUBLIC USHORT ALMGetAlarmLog(ALARM *AlmMngr, UINT16 Index, UINT32 *TimeStamp);
PUBLIC INT32 ALMGetAlarmLogAll(ALARM *AlmMngr, UINT16 *AlmCode, UINT32 *TimeStamp, UINT16 MaxInfo);
PUBLIC void ALMSetAlarmHistResetRequest(ALARM *AlmMngr);
PUBLIC BOOL AlmClearReq( ALARM *AlmMngr, ALMID_T AlmId );
PUBLIC void ALMRuntimeProcess(ALARM *AlmMngr, UINT8 StopModeParam );

extern const ALM_TBL_DEF SvoAlmTbl[] ;	
extern const UINT32 AlmDefTblEntNum;



#endif // end #ifndef _ALARM_H_
