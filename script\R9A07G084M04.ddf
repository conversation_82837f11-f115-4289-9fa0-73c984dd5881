
;; Memory information ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;
;; Used to define address zones within the ARM address space (Memory).
;;
;;   Name      may be almost anything
;;   AdrSpace  must be Memory
;;   StartAdr  start of memory block
;;   EndAdr    end of memory block
;;   AccType   type of access, read-only (R), read-write (RW) or SFR (W)

[Memory]
;;         Name             AdrSpace    StartAdr    EndAdr      AccType   Width
Memory =  ATCM             Memory      0x00000000  0x0001FFFF  RW 
Memory =  BTCM             Memory      0x00100000  0x0011FFFF  RW 
Memory =  SystemRAM        Memory      0x10000000  0x1017FFFF  RW 
；Memory =  BootROM        Memory      0x11000000  0x11007FFF  R 
Memory =  ATCM_AXIX        Memory      0x20000000  0x2001FFFF  RW 
Memory =  BTCM_AXIX        Memory      0x20100000  0x2011FFFF  RW 
Memory =  SystemRAM_Mirror Memory      0x30000000  0x3017FFFF  RW 
Memory =  SPI0_Mirror      Memory      0x40000000  0x47FFFFFF  R  
Memory =  SPI1_Mirror      Memory      0x48000000  0x4FFFFFFF  R  
Memory =  CS_Mirror        Memory      0x50000000  0x5FFFFFFF  W  
Memory =  SPI0             Memory      0x60000000  0x67FFFFFF  R  
Memory =  SPI1             Memory      0x68000000  0x6FFFFFFF  R  
Memory =  CS               Memory      0x70000000  0x7FFFFFFF  W  
Memory =  NS_Periph        Memory      0x80000000  0x80FFFFFF  W  
Memory =  S_Periph         Memory      0x81000000  0x81FFFFFF  W  
Memory =  LLPP0_Peri       Memory      0x90000000  0x901FFFFF  W  
Memory =  GIC0             Memory      0x94000000  0x941FFFFF  W  
Memory =  DebugPrivate     Memory      0xC0000000  0xC0FFFFFF  W  

TrustedRanges = true
UseSfrFilter = true

[SfrInclude]
File = R9A07G084xxx.svd
