/****************************************************************************************************
 *
 * FILE NAME:  MotorIdentification.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _MOTOR_IDENTIFICATION_H
#define	_MOTOR_IDENTIFICATION_H

#include "BaseDef.h"
#include "Encoder.h"
#include "BaseLoops.h"
#include "BaseSetting.h"

#define   FF_STATE_INIT            0
#define   FF_STATE_STEP1           1
#define   FF_STATE_STEP2           2
#define   FF_STATE_STEP3           3
#define   FF_STATE_STEP4           4
#define   FF_STATE_STEP5           5
#define   FF_STATE_DONE            6
#define   FF_STATE_ERROR           7

#define   FF_METHOD_MANUAL         0            // phase find by manual
#define   FF_METHOD_FIRST_EN       1            // phase find by first power on or manual


#define   MOTOR_EST_NONE    	        0x00
#define   MOTOR_EST_LDLQ_INIT    	    0x10
#define   MOTOR_EST_LDLQ_BUSY    	    0x11
#define   MOTOR_EST_LDLQ_OK    	   		0x12
#define   MOTOR_EST_LDLQ_NG    	   		0x1F
#define   MOTOR_EST_UVW_INIT    	   	0x20
#define   MOTOR_EST_UVW_BUSY    	   	0x21
#define   MOTOR_EST_UVW_OK   	   		0x22
#define   MOTOR_EST_UVW_NG  	   		0x2F
#define   ENCODER_PHASE_INIT  	   		0x40
#define   ENCODER_PHASE_BUSY  	   		0x41
#define   ENCODER_PHASE_OK  	   		0x42
#define   ENCODER_PHASE_NG 	   			0x4F
#define   ENCODER_POLEPAIR_INIT  	   		0x80
#define   ENCODER_POLEPAIR_BUSY  	   		0x81
#define   ENCODER_POLEPAIR_OK  	   		0x82
#define   ENCODER_POLEPAIR_NG 	   			0x8F


//#define   C_SinUint1D3              (UINT32)(C_SinUint/3)
//#define   C_SinUint2D3              (UINT32)((C_SinUint>>1)/3)
//#define   FPSTime                   1000	
//#define   ALLOWED_EANGLE_ERR		400			//should be input by user [32768->2*pi]
//#define   AngDelta_8HZ    		    65.536f		//[32768->2*pi]
//#define	  CycNum_8Hz				500
//#define   Zero_Current				0.1f		//[A]
//#define   AngDelta_2HZ    		    16.384f		//[32768->2*pi]
//#define	  CycNum_2Hz				2000
/*--------------------------------------------------------------------------------------------------*/
/*		 phase find Struct 		    	        	       				 			                */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	struct
	{
		UINT16    FFMethod;                     // phase find method
		REAL32    FFCur;                        // phase find current                 [A]
		REAL32    FFVolt;						// phase find voltage                 [Norm V]
		UINT32    FFStableTime;                 // phase fine stable time             [TaskB]
		REAL32    FFRampDelta;                  // phase fine ramp delta              [A/Norm V]
	}P; 
	
	struct
	{
		BOOL      FFStart;
		BOOL      FFStartFlg;
        BOOL      MechOfstSave;
		UINT16    FFCnt;
		UINT16    FFState;

		INT32     FFElecAngleRef;
		REAL32    FFIqRef;
		REAL32    FFVqRef;
	}V;

}PHASE_FIND;

typedef struct
{
    struct
    {
        REAL32    FPCur;                        // phase find current                 [A]
		REAL32	  MaxFPCur;						// MotParam find max current 			 [A]
        REAL32    FPVolt;                       // phase find voltage                 [Norm V]
        REAL32	  FPVoltEnc;                    
        REAL32    FPLCurRef;                    // phase find Current reference       [PeakA]
        REAL32    FPLCur;                                   
        REAL32    FPAngDelta;                   //64/ms  81.92/ms=8Hz=2rpm
        REAL32    FPHFAngDelta;
        UINT16    PnCmd;
    }P;

    struct
    {
        //OPEN LOOP Driver Parameters
        INT32     FPElecAngleRef;
        REAL32    FPElecAngleRefMid;
        REAL32    FPIqRef;
        REAL32    FPVqRef;
        REAL32    FPVdRef;

        //Switch State variable
		UINT16    PnMotIdentSts;
        
        UINT16	  FPStart;
		UINT16	  FPStep;
        UINT16    FPVoltState;
        UINT16    FPRState;
        UINT16    FPLState;
        UINT16    FPEncState;
        UINT16    FPPoleState;
		BOOL      FPhaseqFlag;
		BOOL      SaveToEep;        
        UINT16    FPElcShiftState;
      
		INT32     FPEncPos;
        INT32     FPFirPos;
        INT32     FPLosPos;
        INT32     FPEncDelta;
		INT32	  FirstIn;
		
        UINT32    FPEncPPR;	
        UINT32    FPEncBit;
		UINT32	  FPEncSum;
        INT32     FPElecOffset;				
        UINT32    FPNumPolePair;
		
        INT32     FPhaseCnt;
        UINT32    FPTime;
        UINT32    FPCnt;
		UINT32	  FPEncStime;
        INT32     Pos[2];	
        //Resistance Find variable
        REAL32    FPResistance;
        REAL32    FPRId[2];
        REAL32    FPRUd[2];

        REAL32    FPVdc[2];
        //Inductance Find
        UINT32    FPLTimeSum;
        REAL32    FPInductanceQ;
        REAL32    FPInductanceD;
		
		//shiftElecAngle Find
		REAL32	  FPIdFdb;
		REAL32	  FPIqFdb;        
 		INT32     FPFirElecAngle;
        INT32 	  FPLosElecAngle; 		

        //High Frequecy inject 
        REAL32    FPHFVdRef;
        REAL32    FPHFVqRef;
        INT32     FPHFAngRef;
        REAL32    FPHFIdqMax;
        REAL32    FPHFIdqMin;
        REAL32    FPHFCur;
        UINT16    FPHFIndix;
        UINT16    FPHFRunCnt;
        
	    REAL32    VdcSum;
		REAL32    IdFdbSum;


		REAL32    IalfaMax;
        REAL32    IalfaSum;
		REAL32    IsSumMax1;
		REAL32    IsSumMin1;
		REAL32 	  UdRef1;
		REAL32    IsSumMax2;
		REAL32    IsSumMin2;
		REAL32 	  UdRef2;

		REAL32    IsSumMaxTmp;
		REAL32    IsSumMinTmp;        
    }V;
}MOTORPARAM_FIND;


typedef struct
{
	struct
	{
        UINT32    SpeedMax;     //cnt/s
        UINT32	  Acc;          //cnt/s^2
        UINT32    AcceleTime;   //ms
        INT32     Position;     //cnt
		REAL32    Kt;           //[0.01N.A/m]
        REAL32	  MotInertia;   //[10^-6 kgm^2]
        
	}P; 
	struct
	{
        UINT16    StartFlag;
        UINT32    RunTime;   
        UINT16    Direction;
        UINT32    RunCNT;
        REAL32    IntegrUp;
        REAL32    IntegrDown;
        REAL32    Iq;
        REAL32    ForwardInertia;
        REAL32    NegativeInertia;  
		REAL32    Inertia;
        UINT16    LoadInertiaRatio;     //%
        UINT16    LastLoadInertiaRatio;     //%
        INT32     Speed;     //cnt/s
        INT32     InitPos;
        INT32     PosValue;
        INT32     IntegralPos;
        INT32     OutputPos;
        INT32     OutputVel;
        INT32	  RmdPos;
        INT32     RmdVel;
	}V;
}Inertia_FIND;


typedef struct
{
	struct
	{
        UINT32	  TimeThreshold;        // ms*TASKB_FRQ*0.001
        REAL32    Kt;                   //[0.01N.A/m]
        REAL32	  MotInertia;           // [10^-6 kgm^2]
        REAL32	  ForgetFactor;         // forgetting factor
	}P; 
	struct
	{
        UINT16    StartFlag;
        UINT16    IdentState;  
        UINT32    IdentCNT;
        
        REAL32    Iq;
        REAL32    Tl;                   // load torque
        REAL32    Friction;             // Friction Force[N.m.S/rad]
        
        REAL32    SpeedRef;             // [2^24/OvrSpd]	
        REAL32    SpeedFdb;             // [2^24/OvrSpd]	
        
        REAL32    Wm0;                  // k time real Speed[rad/s]
        REAL32    Wm1;                  // k-1 time real Speed(rad/s)
        REAL32    Te0;                  // k time real torque[N/m]
        REAL32    Te1;                  // k-1 time real torque[N/m]
        REAL32    theta1[2];            // k-1 time Inertia[kgm^2]
        REAL32    Pn1[4];               // k-1 time Covariance matrix[kgm^2]    
                       
        REAL32    Inertia;              // [10^-6 kgm^2]
        UINT16    LoadInertiaRatio;     // [%]
	}V;
}Inertia_FIND_OL;

typedef struct
{
	struct
	{
          UINT16        PnCmd;
          UINT32	TargetVel;
          UINT32        Acc;         
	}P; 
	struct
	{
          INT32		PosForwardLimit;
          INT32         PosReverseLimit;
          UINT16        CompleteFlag;  
          UINT16        FPStart;
          UINT16        FPStep;
          UINT16        InitSts;  
          BOOL        SaveToEep;
          UINT32        InitCNT;
          UINT16	UpDownAct;
          INT32		RmdVel;
          INT32         OutputVel;
          INT32         InitPos;
          INT32		RmdPos;
          INT32         IntegralPos;
          INT32         OutputPos;
          INT16         TrqOutFilter;
          INT16         TrqOut;
          INT16         TrqOut_l;
          INT16         TrqOut_ll;
          INT16         TrqOut_lll;          
          INT32        ReachCNT;          
	}V;
}CLAMPINGJAW_INIT;


typedef struct
{
	PHASE_FIND         FhaseFind;
	MOTORPARAM_FIND    MotorParamIdent;
    Inertia_FIND       InertiaIdent;
    Inertia_FIND_OL    InertiaIdentOL;
    	CLAMPINGJAW_INIT    ClampingJawInit;
}MOTOR_IDENTIFY;

PUBLIC void PhaseFindExec(PHASE_FIND *FhaseFind, ENCODER *Enc);
PUBLIC void FindMotorParameter(MOTORPARAM_FIND* FhaseFind, BASE_LOOP *P);
PUBLIC void FindMotorParameter(MOTORPARAM_FIND* FhaseFind, BASE_LOOP *P);
PUBLIC void MotorParaComplete(MOTORPARAM_FIND* MotorParamIdent, PRMDATA *Prm);
PUBLIC void InertiaIdentCal(Inertia_FIND* InertiaIdent, BASE_LOOP *P);	
PUBLIC void InertiaIdentOLCal(Inertia_FIND_OL* InertiaIdentOL, BASE_LOOP *P);	

PUBLIC  void OffSetElcAngle(MOTORPARAM_FIND *MotorParamIdent, ENCODER *pEnc);
PUBLIC  void ExChangeCurSample(BASE_LOOP *BaseLoops, CUR_LOOP *pCur, BOOL VfContrl);
PUBLIC  void ExChangePWMOut(BASE_LOOP *BaseLoops, CUR_LOOP *pCur, BOOL VfContrl);
#endif  //_MOTOR_IDENTIFICATION_H
