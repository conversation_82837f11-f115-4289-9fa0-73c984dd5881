/****************************************************************************************************
 *
 * FILE NAME:  DataTraceCal.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.30
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	30-10-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "bsp.h"
#include "Global.h"
#include "DataTraceCal.h"
#include "DataTraceManager.h"
#include "HardApi.h"
#include "canfd_slave.h"

extern UINT16 gAdIu,gAdIv;
extern UINT16 gAdVdc;
/****************************************************************************************************/
/*			Trace variable acquisition function (numeric trace table)								*/
/*			Returns numeric data to be stored in the trace buffer[�|]								*/
/****************************************************************************************************/

/*--------------------------------------------------------------------------------------------------*/
/*		0x01:Bus Voltage																			*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_Voltage( void *Axis )
{
	return (((AXIS_HANDLE*)Axis)->PowMngr->PowerSts.DcVolt);
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x02:IPM current																			*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_IPMCurrent( void *Axis )
{ 
//    return (((AXIS_HANDLE*)Axis)->PowMngr->PowerSts.Acon);
  
   return (((AXIS_HANDLE*)Axis)->BaseLoops->BaseCtrls->Cia402Axis.Objects->PositionActualValue0x6064);
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x03:Positon Ref																			*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_PosRef( void *Axis )
{
	return (((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.IpdPosTargetRad)*1000;
	// if(((AXIS_HANDLE*)Axis)->Prm->PnCtrlCfgPrm.CtrlSource == 1)
	// {
	// 	return (((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.IpdPosTargetRad);
	// }
	// else
	// {
	// 	return (((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.PosRef);
	// }
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x04:Positon Feedback																		*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_PosFdb( void *Axis )
{
	// return (((AXIS_HANDLE*)Axis)->BaseLoops->IpdPosFdbRad)*1000;	
	if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
	{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->IpdPosFdbRad)*1000;	
	}
	else
	{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->PosFdb);
	}
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x05:Positon Err																		*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_PosErr( void *Axis )
{

	if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
	{
		return (((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.IpdPosRadErr);
	}
	else
	{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->PosLoop.PosErr);
	}	

}

/*--------------------------------------------------------------------------------------------------*/
/*		0x06:Positon SpdRef																		*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_PosOutput( void *Axis )
{
	if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
	{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->IpdSpringTrqNm)*100;
	}
	else
	{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->PosLoop.SpdRef) 
                        /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmotspd));
	}
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x07:Speed Ref																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_SpdRef( void *Axis )
{
	if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
	{
		return (((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.IpdSpdTargetRads);
	}
	else
	{
		return ((((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.SpdRef)
                        /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmotspd));
	}
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x08:Speed Feedback																            */
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_SpdFdb( void *Axis )
{
      if(((AXIS_HANDLE*)Axis)->BaseLoops->MotSpdHr)  
      {
          return (((AXIS_HANDLE*)Axis)->Prm->PnMoniPrm.SpdFdb ); 
      }
      else
      {
		if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
		{
			return (((AXIS_HANDLE*)Axis)->BaseLoops->IpdSpdFdbRads);	
		}
		else
		{
			return ((((AXIS_HANDLE*)Axis)->BaseLoops->SpdFdb)
                        /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmotspd));
		}
      }            
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x09:Speed Err																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_SpdErr( void *Axis )
{
	if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
	{
		return (((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.IpdSpdRadsErr);
	}
	else
	{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->SpdLoop.V.SpdErr)
                        /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmotspd));
	}
	
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x0A:Speed TrqRef																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_SpdOutput( void *Axis )
{
	if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
	{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->IpdDampTrqNm)*100;
	}
	else
	{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->SpdLoop.V.TrqRef)
                        /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmottrq));
	}
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x0B:Torque Ref																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_TrqRef( void *Axis )
{
	if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
	{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->IpdTrqRef)*100;
	}
	else
	{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->TrqRefo)
						/(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmottrq));	
	}
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x0C:Torque Feedback																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_TrqFdb( void *Axis )
{
	if(((AXIS_HANDLE*)Axis)->Prm->PnCia402Prm.ModesOfOperation0x6060 == 11)	
	{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->TrqFdbBNm)*100;
	}
	else
	{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.IqFdb 
                                /((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->MaxCur * NOR_MAXVAL_FLOAT 
                                /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmottrq));
        }
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x0D:Current U																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_CurrentIa( void *Axis )
{
	return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.Ia)*1000.0f;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x0E:Current V																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_CurrentIb( void *Axis )
{
    return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.Ib)*1000.0f;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x0F:Current W																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_CurrentIc( void *Axis )
{
	return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.Ic)*1000.0f;

}

/*--------------------------------------------------------------------------------------------------*/
/*		0x10:Current IdRef																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_CurrentIdRef( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.IdRefLit)*1000.0f;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x11:Current IqRef																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_CurrentIqRef( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.IqRefLit)*1000.0f;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x12:Current IdFdb																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_CurrentIdFbd( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.IdFdb)*1000.0f;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x13:Current IqFdb																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_CurrentIqFbd( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.IqFdb)*1000.0f;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x14:Enc MechAngle																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_EncMechAngle( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc->V.MechAngle);
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x15:Enc ElecAngle																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_EncElecAngle( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc->V.ElecAngle);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x16:speed feedforward compensation															*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_SpeedFFC( void *Axis )
{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->SpdFFC)
                     /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmotspd));
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x17:torque feedforward compensation													*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_TrqFFC( void *Axis )
{
	if(((AXIS_HANDLE*)Axis)->Prm->PnCtrlCfgPrm.CtrlSource == 1)
	{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->IpdTrqFeedforwardNm))*100;
	}
	else
	{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->TrqFFC)
                    /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmottrq));
	}
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x18: speed Encoder													*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_SpeedEnc( void *Axis )
{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->dMotPos)
                    *(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->KmotspdF)
                        /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmotspd));
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x19: speed feedback observation value										*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_SpeedObs( void *Axis )
{
		return ((((AXIS_HANDLE*)Axis)->BaseLoops->SpdObsFdb)
                        /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmotspd));
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x1A: PosRef alfer filter										*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_PosFilter( void *Axis )
{
	return (((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.PosRefFilo);
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x1B: speedRef + feedforward										*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_SpdFeedForward( void *Axis )
{
    return (((((AXIS_HANDLE*)Axis)->BaseLoops->SpdLoop.V.SpdErr) 
                +(((AXIS_HANDLE*)Axis)->BaseLoops->SpdLoop.V.SpdFdbFilo))
                        /(((AXIS_HANDLE*)Axis)->BaseLoops->Bprm->Kmotspd));	
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x1C: Iq after Notch filter										*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_IqNotchFilter( void *Axis )
{
	return (((AXIS_HANDLE*)Axis)->BaseLoops->CurLoop.V.IqRefNotFil)*1000.0f;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x1D:SingleTurn													*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_EncSingleTurn( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc->V.SingleTurn);
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x1E:MultiTurn																*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_EncMultiTurn( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc->V.MultiTurn);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x1F:AlmStatus															*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_AlmStatus( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->AlmMngr->Status.AlmFlag);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x20:wrnStatus															*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_WrnStatus( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->AlmMngr->Status.WrnFlag);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x21:BrakeStatus													*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_BrakeStatus( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseSeq->BkSeqData.V.Brake);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x22:Cia402ControlW													*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_Cia402ControlW( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseCtrls->Cia402Axis.Objects->Controlword0x6040);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x23:Cia402StatusW												*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_Cia402StatusW( void *Axis )
{
		return (((AXIS_HANDLE*)Axis)->BaseCtrls->Cia402Axis.Objects->Statusword0x6041);
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x24:TargetPosition0x607A											*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_TargetPosition0x607A	( void *Axis )
{
		// return (((AXIS_HANDLE*)Axis)->BaseCtrls->Cia402Axis.Objects->TargetPosition0x607A);
	return (((AXIS_HANDLE*)Axis)->BaseLoops->TrqFdbBNm)*100;

}

/*--------------------------------------------------------------------------------------------------*/
/*		0x25:PositionInc										*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_PositionInc	( void *Axis )
{
		// return (((AXIS_HANDLE*)Axis)->BaseCtrls->CtrlCmdMngr.dPosRef);
	return (((AXIS_HANDLE*)Axis)->BaseLoops->TrqFdbANm)*100;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x26:DebugVar1										*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_DebugVar1	( void *Axis )
{
	// return (HumanRotCtrl.controlData.targetPosition);
	return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc->V.DeltaSingleTurn);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x27:DebugVar2												*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_DebugVar2		( void *Axis )
{
	// return (HumanRotCtrl.controlData.targetVelocity);
	return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc->V.DeltaAccTurn);
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x28:DebugVar3												*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_DebugVar3		( void *Axis )
{
	// return (HumanRotCtrl.controlData.torqueFeedforward);
//		return (((AXIS_HANDLE*)Axis)->BaseLoops->IpdTrqRef)*100;
  	return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc->V.SfSingleTurn);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x29:0x28:DebugVar4											*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_DebugVar4	( void *Axis )
{
	// return (HumanRotCtrl.statusData.torqueQ);

	// return (((AXIS_HANDLE*)Axis)->BaseCtrls->Cia402Axis.FG_Ctrl.V.OutputRef);
	return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc2->V.SingleTurn);
}
/*--------------------------------------------------------------------------------------------------*/
/*		0x7F:Real addressing																		*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_AdrShortData( void *Axis )
{
//	return (*(((AXIS_HANDLE*)Axis)->TrcHndl->TrcExeP.TrcObj->Adr.ShortAdrData));
  return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc2->V.SingleTurn); 
}

INT32 GetVarData_AdrLongData( void *Axis )
{
//	return (*(((AXIS_HANDLE*)Axis)->TrcHndl->TrcExeP.TrcObj->Adr.LongAdrData));
  return (((AXIS_HANDLE*)Axis)->BaseLoops->Enc2->V.SingleTurn);
}

/*--------------------------------------------------------------------------------------------------*/
/*		Dummy variable																			    */
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarData_Dummy( void *Axis )
{
	return 0;
}



/*--------------------------------------------------------------------------------------------------*/
/*		0x01:Servo-on command(/S-ON)																*/
/*--------------------------------------------------------------------------------------------------*/
BOOL GetBitData_SvonReq( void *Axis )
{
	return (((AXIS_HANDLE*)Axis)->BaseSeq->SvonReq);
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x02:P control command(/P-CON)																*/
/*--------------------------------------------------------------------------------------------------*/
BOOL GetBitData_Pcon( void *Axis )
{
//	return ((((AXIS_HANDLE*)Axis)->BaseLoops->CmdCtrlBit & ENBPCTRL_BIT) ? TRUE : FALSE);
  return 0;
}

/*--------------------------------------------------------------------------------------------------*/
/*		0x03:No forward rotation(P-OT)																*/
/*--------------------------------------------------------------------------------------------------*/
BOOL GetBitData_Pot( void *Axis )
{
//	return (((AXIS_HANDLE*)Axis)->SeqCtrlOut->PotSigSts);
  return 0;

}
/*--------------------------------------------------------------------------------------------------*/
/*		0x04:No reverse(N-OT)																		*/
/*--------------------------------------------------------------------------------------------------*/
BOOL GetBitData_Not( void *Axis )
{

//	return (((AXIS_HANDLE*)Axis)->SeqCtrlOut->NotSigSts);
  return 0;

}

/*--------------------------------------------------------------------------------------------------*/
/*		0x05:Alarm reset(/ALMRST)															    	*/
/*--------------------------------------------------------------------------------------------------*/
BOOL GetBitData_AlmRst( void *Axis )
{
//	return( 0 != (((AXIS_HANDLE*)Axis)->BaseCtrlOut->CmdSeqBit & ALM_RESET_BIT ));	
  return 0;
}

/*--------------------------------------------------------------------------------------------------*/
/*		CurrentGain mA																		*/
/*--------------------------------------------------------------------------------------------------*/
INT32 GetVarGain_Current( void *Axis )
{
	return 1000;
}
