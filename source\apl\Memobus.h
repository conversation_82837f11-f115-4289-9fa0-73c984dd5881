/****************************************************************************************************
 *
 * FILE NAME:  Memobus.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.11.01
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	01-11-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef	_MEMOBUS_H 
#define _MEMOBUS_H 

#include "Basedef.h"

/****************************************************************************************************/
/*																									*/
/*		Macro Definition																			*/
/*																									*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		MEMOBUS message Function code												                */
/*--------------------------------------------------------------------------------------------------*/
#define	MBUS_LBAK_08H			0x08			// Loopback command						
#define	MBUS_FC_42H				0x42			// MEMOBUS Message function code			
#define	MBUS_SYSDL_50H			0x50			// MEMOBUS system download code		
#define	MBUS_RELAY_51H 			0x51			// Relay command							


/*--------------------------------------------------------------------------------------------------*/
/*		MEMOBUS Message Sub function code		                                                    */
/*--------------------------------------------------------------------------------------------------*/
#define	MBUS_RD_CMEM_42H		0x01			// Read memory contents (continuous)				
#define	MBUS_WR_CMEM_42H		0x02			// Write to memory (continuous)					
#define	MBUS_RD_NCMEM_42H		0x03			// Read memory contents (non-continuous)					
#define	MBUS_WR_NCMEM_42H		0x04			// Write to memory (non-continuous)					
#define	MBUS_RD_MAXSIZE_42H		0x11			// Maximum size readout								
#define	MBUS_RD_INFO_42H		0x7f			// Read product information	

/*--------------------------------------------------------------------------------------------------*/
/*		Error code definition																		*/
/*--------------------------------------------------------------------------------------------------*/
#define	FUNCCODE_ERROR			0x01			// Function code error						
#define	MEMADRES_ERROR			0x02			// Memory address error								
#define	MODETYPE_ERROR			0x03			// Mode / data type error							
#define	DATANUMB_ERROR			0x04			// Bad number											
#define	LMITACCS_ERROR			0x05			// Access restriction error								
#define	RANGOUTS_ERROR			0x06			// Setting value out of range error								
#define	DATAADJT_ERROR			0x07			// Data consistency error									
#define	CONDTION_ERROR			0x08			// Condition error										
#define	COMPETON_ERROR			0x09			// Processing conflict error									
#define	SELAXIS_ERROR			0x10			// Axis specification error	
/*--------------------------------------------------------------------------------------------------*/
#define	MBUS_HEAD_BASE_SIZE		0x0C			// MEMOBUS header basic size (Byte) address included		
#define	MBUS_HEAD_FIX_SIZE		0x08			// MEMOBUS header fixed size (Byte)	
/*--------------------------------------------------------------------------------------------------*/
#define	MBUS_RLY_FIX_SIZE		0x0E			// Relay command fixed size							
#define	MBUS_RLY_PASS_DATA_SIZE	0x08			// Relay command variable size (changes in multiples of this size)
#define	MBUS_RLY_MAX_ROUTENUM	0x0A			// Maximum number of relay stages								
/*--------------------------------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------*/
/*		MEMOBUS message Read product information Address information							    */
/*--------------------------------------------------------------------------------------------------*/
#define	MBUS_IDRD_HEAD_BASE_SIZE		0x0C		// MEMOBUS (Read product information) Header basic size (Byte)
#define	MBUS_IDRD_RSP_HEAD_BASE_SIZE	0x0D		// MEMOBUS (Read product information) Response header basic size (Byte)
#define	MBUS_IDRD_SLOT_NUM				6			// MEMOBUS (Read product information) Number of slots		
#define	MBUS_IDRD_ID_NUM				7			// MEMOBUS (Product information readout) Number of IDs			

/*----------------------------------------------------------------------------------------------------------*/
/*		Confirmation of operator parameter display selection												*/
/*----------------------------------------------------------------------------------------------------------*/
#define	OPEDSP_SETUPPRM			0x0000			// Only user-defined parameters for SETUP						
#define	OPEDSP_ALLPRM			0x0001			// All user-defined parameters										
#define	SETUP_PRM_MASK			0x80			// Setup parameter mask	

#define	WRITE_NG				0x0000			// Write NG													
#define	WRITE_OK				0x0001			// Write OK													


#endif  // _MEMOBUS_H

