/****************************************************************************************************
 *
 * FILE NAME:  PowerManager.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.08.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-08-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _POWER_MANAGER_H_
#define	_POWER_MANAGER_H_

#include "BaseDef.h"
#include "BaseLoops.h"

/*--------------------------------------------------------------------------------------------------*/
/*		For AC power phase loss detection processing												*/
/*--------------------------------------------------------------------------------------------------*/
#define	ACOPEN_DETECT_WAIT			(1000)		   // Phase loss detection time[ms]				

/*--------------------------------------------------------------------------------------------------*/
/*		For DC power supply monitoring																*/
/*--------------------------------------------------------------------------------------------------*/
#define CONVINSIGDISCHERRCHK_TIME	(1000)	       // (Power monitoring input signal error) Rapid discharge error detection time: 1 s
#define DCOFF_FILTER				   (100)	   // DC instantaneous power failure retention time filter: 100 ms

/*--------------------------------------------------------------------------------------------------*/
/*		Initial value ratio selection definition for regenerative overload detection				*/
/*--------------------------------------------------------------------------------------------------*/
#define REGPOWSUM_RATE95PER			0x00					   /* 95% of regenerative overload alarm level */
#define REGPOWSUM_RATE50PER			0x01					   /* 50% of regenerative overload alarm level */

#define CONVERTER_POWER             1500                        // todo: converter power [W]
#define REGEN_ON_LEVEL              55                         // regeneration on level [V]
#define REGEN_OFF_LEVEL             50                         // regeneration off level [V]

/*--------------------------------------------------------------------------------------------------*/
/*		For DC power supply rapid discharge treatment								                */
/*--------------------------------------------------------------------------------------------------*/
#if(1 == HIGH_VOLTAGE)
#define DCVOLT60V					60							// DcVolt exceeds 60V					
#define DCVOLT10V					10				
#else
#define DCVOLT60V					50							// DcVolt exceeds 60V					
#define DCVOLT10V					5	
#endif

#define DISCH_CHK_TIME				20							// Rapid discharge confirmation time[ms](x3 times)
#define DISCH_CHK_VOLT				10							// Rapid discharge confirmation threshold voltage [V] (x3 times)

enum
{
	DISCH_INIT = 0,		// Rapid discharge sequence initial state									
	DISCH_CHK1ST,		// Rapid discharge confirmation 1st													
	DISCH_CHK2ND,		// Rapid discharge confirmation 2nd													
	DISCH_CHK3RD,		// Rapid discharge confirmation 3rd 													
	DISCH_ACTIVE,		// During rapid discharge 													
	DISCH_FINISH,		// Rapid discharge done		 													
};


typedef struct
{
	struct	
	{						
		UINT32	AcoffDetctTime; 			// AC Power Off Detect Time 						
		BOOL	DcbusWay;					// DC power input selection
        BOOL    Ac3Phase;                   // Power input: 3 phase
		UINT32	PowerChargeWaitTime;		// DC charging completion waiting time
		UINT32	PowerReadyWaitTime; 		// Power ready waiting time	
		UINT32	DischCheckVlt;				// Rapid discharge confirmation threshold voltage
		UINT32	DischCompVlt;				// Rapid discharge duration threshold voltage	
		UINT32	MainDischActTime;			// Main rapid discharge time (same as charging time)
		UINT32	UvFilter;					// UV detection filter time constant
		INT32	OvdetAlmFilter;				// Overvoltage alarm filter(1=250us)
		UINT16	UvLevel;					// Undervoltage (UV) detection level
		UINT16	UvWrnLevel; 				// Undervoltage (UV) warning detection level		
		UINT16	OvLevel;					// Overvoltage (OV) detection level during regenerative use	
		UINT16	NonRegOvLevel;				// Overvoltage (OV) detection level when not using regeneration
		BOOL	DcDischarge;				// Rapid discharge selection at DC power input
		BOOL	AcoffDischarge;				// Rapid discharge selection at AC Power Off
	} P;

	struct	
	{							
		UINT32	AconWaitTimer;				// Initial Acon Wait Timer							
		UINT32	AcoffChkTimer;				// AC Power Off Check Timer 							
		UINT32	DconChkTimer;				// DC Power On Check Timer 						
		UINT32	DischChkTimer;				// Discharge Check Timer							
		UINT32	DischSeqNo; 				// Discharge Sequence Number							
		UINT32	DischActTimer;				// Discharge Active Time Counter					
		UINT32	DischStartVolt; 			// Discharge Start Timing Voltage					
		UINT32	OpenChkTimer[2];			// Open Phase Check Timer							
		UINT32	UvChkTimer; 				// Under Voltage Check Timer							
		UINT32	WireAlmDetWait; 			// Remain DC Power Check Timer						
		BOOL	DetOpenAcOn1;				// ACON1 signal (Used by Detect Open-Phase) 	
		BOOL	DetOpenAcOn2;				// ACON2 signal (Used by Detect Open-Phase) 	
		INT32	AcOn1OffCount;				// ACON1 Off Count Timer(Used by Detect Open-Phase) 	
		INT32	AcOn2OffCount;				// ACON2 Off Conut Timer(Used by Detect Open-Phase) 	
		INT32	AconDetectCount;			// Acon Active Detect Count in DC mode								
		INT32	DcVoltOld;					// DC Volt Before Discharge 	
		BOOL	AlmWIR_Flg;					// Main circuit wiring error Flag
		UINT32	ConvInSigDischErrChkTimer;	// Converter I/O Signal Discharge Error Check Timer
		BOOL	WrnExRegInhibit;			// WRN_EXREG Inhibit Flag								

	} V;

}CHECK_MAIN_POWER;

typedef struct
{
	struct	
	{						
		UINT8	VdetMode;					// Main circuit bus voltage detection method(PnE0C.8,9)				
		UINT16	VdetMaxVolt;				// Amp Volt detect at max input[data/MaxV]		
		INT32	VdetAdNorm;					// Main circuit detection AD conversion result normalization constant
		INT32	VdetAdFil;					// Main circuit detection AD filter gain						
		INT32	VdcdetAlmFilter;			// Main circuit detection error alarm filter(1=250us)
		BOOL	VdetPrmOk;					// Main circuit detection related parameter setting(1:OK, 0:NG)			
		INT32	VdetAdadj;					// P-N voltage detection level adjustment
		INT32	Kdcvolt;					// Main circuit detection value conversion gain[Admax] => [V]		
	} P;
	
	INT32	DcAdFili;						// DC Volt Ad read filter output				
	INT32	DcAdFilo[3];					// DC Volt Ad read filter output					
	INT32	DcVoltx;						// DC Volt											
	UINT32	DcvErrCnt;						// DC Volt Error Check Counter						
	UINT32	DcvWaitCnt;						// DC Volt Error Check Wait Counter					
	INT32	VdcdetAlmCnt;					// Main circuit detection alarm detection filter(1=250us)		
	INT32	OvdetAlmCnt;					// Overvoltage alarm detection filter(1=250us)		

}DET_DC_VOLT;

typedef struct
{
	BOOL			Acon;					// AC power detection status	
	BOOL			AconSig;				// Converter output signal state 
	BOOL			AconSig_l;				// Converter output signal status (previous value)	
	BOOL			ChargeOk;				// DC charging complete state	
	BOOL			Discharge;				// DC rapid discharge requirement	
	BOOL			PowerOn;				// Main power ON state (considering power ready waiting time)		
	BOOL			PowerOnEep;				// Main power ON state (for EEPROM management)				
	INT32			DcVolt;					// P-N Voltage detection value	
    INT32			DcVoltFilo;					// P-N Voltage detection value	
	BOOL			NegPumpReq;		        // Negative Pump On Control Request to Kernel
	BOOL			AconDetectReady;	    // AC ON Signal Detect Ready Flag(FALSE:Not Ready, TRUE:Ready)
    BOOL            PowerReset;
    UINT16          PowerResetTime;
}POWER_STATUS;

/*--------------------------------------------------------------------------------------------------*/
/*		Structure definition for regenerative overload check										*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	struct	
	{								
		INT32	Krgpower;					// Regenerative overload: Power calculation gain
		INT32	RgolPowBase;				// Regenerative overload: Electric power base			
		INT32	RgolAlmLevel;				// Regenerative overload: Alarm level		
		INT32	RgSumSel;					// Initial value ratio selection for regenerative overload detection
	} P;

	struct	
	{							
		INT32	RgolPowSum;					// Regenerative overload: Integrated power value
		BOOL	LastRgolDetect;				// Regenerative overload: Regenerative overload detection flag at the previous start
		BOOL	RgPowUp;					// Regenerative overload: Regenerative power increase flag
		INT32	RgPowerSum;					// Regenerative average value: Integrated power value			
		INT32	RgMeanCycle;				// Regenerative average value: Power accumulation count
	} V;
} CHECK_REGOL;


/*--------------------------------------------------------------------------------------------------*/
/*		Structure definition for regenerative abnormality check										*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	INT32	RegeneErrCnt;					// Regenerative abnormality check count					
	BOOL	RegeAlmSignal;					// Regenerative abnormality alarm flag						
} CHECK_REGENE;


typedef struct
{
	struct	
	{								
		UINT32			ResistorCapa;			// Regenerative resistance capacity
		BOOL			RegRxIn;				// Built-in regenerative resistance
		INT32			RegStartFilter; 		// Regenerative operation start filter(1=250us)		
		INT32			RegStopFilter;			// Regenerative operation stop filter(1=250us)
		UINT16			RegOnLevel; 			// Regenerative Tr ON level							
		UINT16			RegOffLevel;			// Regenerative Tr Off level							
		UINT16			RegSyn; 				// Regenerative synchronization function selection								
		UINT16			RegSynWaitTime; 		// Regenerative ON wait time during regenerative synchronization		
	} P;

	struct	
	{	
		INT32			RegStartCnt;			// Regenerative operation start wait counter 							
		INT32			RegStopCnt; 			// Regeneration operation end wait counter 			
		INT32			RegOnCount[2];			// Regenerative transistor ON time 0: Update taskB, 1: Update taskC
		INT32			dRegOnCount;			// Differential regeneration transistor ON time [Count / taskC]
		BOOL			RegOn;					// Regenerative transistor ON request 						
		BOOL			RegAlmSig;				// Regenerative abnormality detection signal 								
		BOOL			RegAlmMotStop;			// Motor stop detection flag after regenerative overload alarm
		CHECK_REGOL 	RegOL;					// regenerative overload check
		CHECK_REGENE	RegChk; 				// regenerative abnormality check
		INT32			UnMeanRgPower;			// Regenerative power consumption average value (common to units: for monitoring) [%]
	}V;

}REGENE_CTRL;

/*--------------------------------------------------------------------------------------------------*/
/*		Parameter structure definition for regenerative control setting								*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	UINT16			RegenPow;				// Regenerative resistance capacity [10W]									
	UINT16			ExtRegenR;				// extern Regenerative resistance value [10mOhm]			
	UINT16			RegenR;					// Regenerative resistance value (default) [mOhm]		
	UINT16			ReW;					// Regenerative resistance capacity (default) [0.1%]		
	UINT16			RegenLvl;				// Regenerative on level [V]				
	UINT16			ConvW;					// Converter capacity (system) [W]		
} REGCFG_PRM;


typedef struct
{
	CHECK_MAIN_POWER      MainPowChecker;
	DET_DC_VOLT           DetDcVolt;
	POWER_STATUS          PowerSts;
	REGENE_CTRL           RegeneCtrl;

}POWER_MNG;


/*--------------------------------------------------------------------------------------------------*/
/*		Definition for DC power supply voltage detection											*/
/*--------------------------------------------------------------------------------------------------*/
#define	DETECT_DC_NO_OPERATION		0x00

/*--------------------------------------------------------------------------------------------------*/
/*		Main circuit bus voltage detection method													*/
/*--------------------------------------------------------------------------------------------------*/
#define	VDET_NONE					0x00		// No detection by software
#define	VDET_PWM_FIX_FOLLOW			0x01		// PWM-comparator method: Tracking by fixed value (1)
#define	VDET_PWM_VAR_FOLLOW			0x02		// PWM-comparator method: Tracking by variable value (1-3)
#define	VDET_AD						0x03		// A / D conversion method

/*--------------------------------------------------------------------------------------------------*/
/*		Definition for regenerative abnormality detection																			*/
/*--------------------------------------------------------------------------------------------------*/
#define	CHK_REGENE_NO_OPERATION		0x0000			// No alarm (no processing required)		
#define	CHK_REGENE_SET_ALARM		0x0001			// All axis alarm output					

/*--------------------------------------------------------------------------------------------------*/
/*		Definition for regenerative overload detection												*/
/*--------------------------------------------------------------------------------------------------*/
#define	CHK_REGOL_NO_OPERATION		0x0000			// No alarm (no processing required)		
#define	CHK_REGOL_SET_ALARM			0x0001			// All axis alarm output					
#define	CHK_REGOL_SET_WARNING		0x0010			// All axis warning output				
#define	CHK_REGOL_CLR_WARNING		0x0100			// All axes warning clear				

#define	REGSYNOFF					0U				// Does not use the regeneration synchronization function
#define	PNREGSYN					1U				// Use regenerative synchronization function in PN bus common system
#define	PNEXREG						2U				// Use external regeneration function in PN bus common system

PUBLIC void PcmInitPowerManager( POWER_MNG *hPowMngr, UINT32 AlmOLInfo );
PUBLIC void PcmPowerManager( POWER_MNG *hPowMngr, BOOL MotStopSts, BOOL BeSts , BOOL SvonReq)	;
PUBLIC void PcmCountRegeneOnTime( POWER_MNG *hPowMngr );
PUBLIC INT32 PcmCheckRegeneOverLoad( REGENE_CTRL *pRegCtrl,UINT32 *AlmOLInfo);
PUBLIC void IprmcalRegenePowerGain( REGENE_CTRL *RegeneCtrl, BPRMDAT *Bprm, REGCFG_PRM *RegPrm );
PUBLIC BOOL PcalRegeneOverLoadLevel( REGENE_CTRL *RegeneCtrl,UINT16 RegenPow, UINT16 ReW, UINT16 ConvW );
PUBLIC BOOL PcmCheckRegeneAlarm( REGENE_CTRL *pRegCtrl, BOOL PowerOn, BOOL RegCtrlSts );
PUBLIC void PcmRlyControlProcess( POWER_MNG *hPowMngr, BOOL DbOn, BOOL BaseEnable, UINT16 AxisID );

PUBLIC INT32 PcmDetectDcVolt( DET_DC_VOLT *pDetectDc, POWER_STATUS *PowSts );
PUBLIC void PcmRegeneControl( REGENE_CTRL *pRegCtrl, POWER_STATUS *PowSts, BOOL MotStop );
PUBLIC INT32 PcmCheckOverVoltage( CHECK_MAIN_POWER *PowerChecker, DET_DC_VOLT *pDetectDc,
														POWER_STATUS *PowSts, REGENE_CTRL *RegCtrl );
PUBLIC void PcmInputPowerStatus( POWER_MNG *hPowMngr, BOOL Acon, BOOL RegeneAlm );
PUBLIC void PowerSysReset( POWER_MNG *hPowMngr);
#endif  // _POWER_MANAGER_H_


