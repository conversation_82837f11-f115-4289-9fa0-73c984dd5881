 /****************************************************************************************************
 *
 * FILE NAME:  GainChange.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.24
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	24-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseDef.h"
#include "GainChange.h"
#include "Mlib.h"


/****************************************************************************************************
 * DESCRIPTION:
 *		 Servo gain switching variable initialization
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void GselInitServoGainChange( GAIN_CHNG_HNDL *hGainChg )
{
	ATGSEL *pAtGsel;									

	/* Set 1st gain at initialization */
	hGainChg->GselGains[0] = hGainChg->GselGains[1];
	hGainChg->GselDobs[0] = hGainChg->GselDobs[1];				
	hGainChg->AmonActGain = GAIN1;
	hGainChg->ActGain = 1;

	/* Initialize gain switching timer */
	hGainChg->AtGselA.var.Timer = 0;
	hGainChg->AtGselB.var.Timer = 0;

/*--------------------------------------------------------------------------------------------------*/
/*  Auto Gain Change																				*/
/*--------------------------------------------------------------------------------------------------*/
	GselInitAutoGainChange( &(hGainChg->AtGselA),				/* condition A */
							&(hGainChg->GselGains[1]),			/* prevGain */
							&(hGainChg->GselGains[2]),			/* nextGain */
							&(hGainChg->GselDobs[1]),			/* prevGain(Dobs) */
							&(hGainChg->GselDobs[2]) );			/* nextGain(Dobs) */
	GselInitAutoGainChange( &(hGainChg->AtGselB),				/* condition B */
							&(hGainChg->GselGains[2]),			/* prevGain */
							&(hGainChg->GselGains[1]),			/* nextGain */
							&(hGainChg->GselDobs[2]),			/* prevGain(Dobs) */
							&(hGainChg->GselDobs[1]) );			/* nextGain(Dobs) */
/*--------------------------------------------------------------------------------------------------*/
/*	Gain parameter recalculation (just in case)														*/
/*--------------------------------------------------------------------------------------------------*/
	pAtGsel = &(hGainChg->AtGselA);								/* condition A */
	PcalAtGseldKpSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Kp */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Kv, Kvi */
	PcalAtGseldKlpfSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Klpf */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate delta Ks */
	pAtGsel = &(hGainChg->AtGselB);								/* condition B */
	PcalAtGseldKpSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Kp */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Kv, Kvi */
	PcalAtGseldKlpfSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Klpf */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate delta Ks */

}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Gain switching condition judgment processing
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 GselDetGainChngTiming( BOOL CoinSignal, BOOL NearSignal, BOOL RefZSignal, INT32 Condition )
{
	INT32	triger;

	/* Second gain transition or second gain is being selected when condition A is met */
	switch( Condition )
	{
	case 0x00:
		triger = CoinSignal;
		break;
	case 0x01:
		triger = CoinSignal ^ 0x01;
		break;
	case 0x02:
		triger = NearSignal;
		break;
	case 0x03:
		triger = NearSignal ^ 0x01;
		break;
	case 0x04:
		triger = RefZSignal;
		break;
	case 0x05:
		triger = RefZSignal ^ 0x01;
		break;
	default:
		triger = 0;
		break;
	}
	return triger;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		 Manual gain switching
 * RETURNS:
 *
****************************************************************************************************/
void GselManualGainChange( BASE_CTRL *BaseControls )				
{
	GAIN_CHNG_HNDL *hGainChg	= &(BaseControls->GainChange);				
	MFCTRL *pMfcData			= &(BaseControls->MFControl);				
	INT32 GselNum				= BaseControls->GselNum;					
	BOOL TuningLess				= BaseControls->TuneLessCtrl.var.TuneLessAct;	
	CTRL_CMD_PRM *CtrlCmdPrm	= &(BaseControls->CtrlCmdPrm);					

	if( TuningLess != FALSE )
	{ /*TuningLess enable -> Disable gain switching and use TuningLess gain*/

		hGainChg->GselGains[0] = hGainChg->GselGains[5];
		hGainChg->GselDobs[0] = hGainChg->GselDobs[1];
		
		pMfcData->conf.MfcPrm[0] = pMfcData->conf.MfcPrm[1];

		hGainChg->ActGain = 1;
		hGainChg->AmonActGain = GAIN1;
		
		CtrlCmdPrm->OverTrqLoopGain[0] = CtrlCmdPrm->OverTrqLoopGain[1];
		CtrlCmdPrm->OverTrqIntegGain[0] = CtrlCmdPrm->OverTrqIntegGain[1];
	}
	else
	{
		if( GselNum < SVCDEF_NETGSEL_SETNUM )
		{ /* When 1st gain and 2nd gain are selected */
			hGainChg->GselGains[0] = hGainChg->GselGains[GselNum + 1];
			hGainChg->GselDobs[0] = hGainChg->GselDobs[GselNum + 1];			

			pMfcData->conf.MfcPrm[0] = pMfcData->conf.MfcPrm[GselNum + 1];

			hGainChg->ActGain = GselNum + 1;
			hGainChg->AmonActGain = GAIN1 << GselNum;

			CtrlCmdPrm->OverTrqLoopGain[0] = CtrlCmdPrm->OverTrqLoopGain[GselNum+1];
			CtrlCmdPrm->OverTrqIntegGain[0] = CtrlCmdPrm->OverTrqIntegGain[GselNum+1];
		}
		else
		{ /* Fixed 1st gain when 3rd gain or 4th gain is selected */
			pMfcData->conf.MfcPrm[0] = pMfcData->conf.MfcPrm[1];
			hGainChg->GselDobs[0] = hGainChg->GselDobs[1];		

			hGainChg->ActGain = 1;
			hGainChg->AmonActGain = GAIN1;

			CtrlCmdPrm->OverTrqLoopGain[0] = CtrlCmdPrm->OverTrqLoopGain[1];
			CtrlCmdPrm->OverTrqIntegGain[0] = CtrlCmdPrm->OverTrqIntegGain[1];
		}
	}

	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Automatic gain switching parameter calculation
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void GselCalculatePrm( GAIN_CHNG_HNDL *hGainChg, UINT16 gnswwait1, UINT16 gnswwait2, INT32 ScanTime )
{
	/* Gain switching waiting time 1[ms] */
	hGainChg->AtGselA.conf.Wait = gnswwait1 * 1000 / ScanTime;

	/* Gain switching waiting time 2 [ms] */
	hGainChg->AtGselB.conf.Wait = gnswwait2 * 1000 / ScanTime;

}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Automatic gain switching pointer acquisition API
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL GselGet_AtGselStatus( GAIN_CHNG_HNDL *hGainChg )
{
	return hGainChg->AtGsel;
}


