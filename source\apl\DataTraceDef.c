/****************************************************************************************************
 *
 * FILE NAME:  DataTraceDef.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.30
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	30-10-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "DataTraceDef.h"
#include "Global.h"
#include "DataTraceCal.h"


/****************************************************************************************************/
/*		Numeric trace definition table																*/
/****************************************************************************************************/
const	TVXDEF	TrcVarTbl[] = {

  { 0x00, 0,  0,   FALSE,	NULL,						NULL                      },
  { 0x01, 0,  0,   FALSE,	&GetVarData_Voltage,		NULL                      },
  { 0x02, 0,  0,   FALSE,	&GetVarData_IPMCurrent,		NULL                      }, 
  { 0x03, 0,  0,   FALSE,	&GetVarData_PosRef,		    NULL                      },
  { 0x04, 0,  0,   FALSE,	&GetVarData_PosFdb,		    NULL                      },
  { 0x05, 0,  0,   FALSE,	&GetVarData_PosErr,		    NULL	                  },
  { 0x06, 0,  0,   FALSE,	&GetVarData_PosOutput,		NULL                      },
  { 0x07, 0,  0,   FALSE,	&GetVarData_SpdRef,	        NULL                      },
  { 0x08, 0,  0,   FALSE,	&GetVarData_SpdFdb,		    NULL                      },
  { 0x09, 0,  0,   FALSE,	&GetVarData_SpdErr,		    NULL                      },
  { 0x0A, 0,  0,   FALSE,	&GetVarData_SpdOutput,		NULL	                  },
  { 0x0B, 0,  0,   FALSE,	&GetVarData_TrqRef,		    NULL                      },
  { 0x0C, 0,  0,   FALSE,	&GetVarData_TrqFdb,	        NULL                      },
  { 0x0D, 0,  0,   FALSE,	&GetVarData_CurrentIa,	    NULL                      }, 
  { 0x0E, 0,  0,   FALSE,	&GetVarData_CurrentIb,		NULL                      }, 
  { 0x0F, 0,  0,   FALSE,	&GetVarData_CurrentIc,		NULL                      },
  { 0x10, 0,  0,   FALSE,	&GetVarData_CurrentIdRef,	NULL                      }, 
  { 0x11, 0,  0,   FALSE,	&GetVarData_CurrentIqRef,	NULL                      }, 
  { 0x12, 0,  0,   FALSE,	&GetVarData_CurrentIdFbd,	NULL                      },
  { 0x13, 0,  0,   FALSE,	&GetVarData_CurrentIqFbd,	NULL                      },
  { 0x14, 0,  0,   FALSE,	&GetVarData_EncMechAngle,	NULL                      },
  { 0x15, 0,  0,   FALSE,	&GetVarData_EncElecAngle,	NULL                      },
  { 0x16, 0,  0,   FALSE,	&GetVarData_SpeedFFC,	    NULL                      },
  { 0x17, 0,  0,   FALSE,	&GetVarData_TrqFFC,	        NULL                      },
  { 0x18, 0,  0,   FALSE,	&GetVarData_SpeedEnc,	    NULL                      },
  { 0x19, 0,  0,   FALSE,	&GetVarData_SpeedObs,	    NULL                      },
  { 0x1A, 0,  0,   FALSE,	&GetVarData_PosFilter,	    NULL                      },
  { 0x1B, 0,  0,   FALSE,	&GetVarData_SpdFeedForward,	NULL                      },
  { 0x1C, 0,  0,   FALSE,	&GetVarData_IqNotchFilter,	NULL                      },
  { 0x1D, 0,  0,   FALSE,	&GetVarData_EncSingleTurn,	NULL                      },
  { 0x1E, 0,  0,   FALSE,	&GetVarData_EncMultiTurn,	NULL                      },
  { 0x1F, 0,  0,   FALSE,	&GetVarData_AlmStatus,		NULL                      },
  { 0x20, 0,  0,   FALSE,	&GetVarData_WrnStatus,		NULL                      },
  { 0x21, 0,  0,   FALSE,	&GetVarData_BrakeStatus,	NULL                      },
  { 0x22, 0,  0,   FALSE,	&GetVarData_Cia402ControlW,	NULL                      },
  { 0x23, 0,  0,   FALSE,	&GetVarData_Cia402StatusW,	NULL                      },
  { 0x24, 0,  0,   FALSE,	&GetVarData_TargetPosition0x607A,	NULL              },
  { 0x25, 0,  0,   FALSE,	&GetVarData_PositionInc,	NULL                      },  
  { 0x26, 0,  0,   FALSE,	&GetVarData_DebugVar1,	NULL                          },  
  { 0x27, 0,  0,   FALSE,	&GetVarData_DebugVar2,	NULL                          },  
  { 0x28, 0,  0,   FALSE,	&GetVarData_DebugVar3,	NULL                          },    
  { 0x29, 0,  0,   FALSE,	&GetVarData_DebugVar4,	NULL                          },      
  
};

const UINT16 TrcVarTblEntNum = sizeof(TrcVarTbl)/sizeof(TrcVarTbl[0]);


/****************************************************************************************************/
/*		Bit trace definition table																	*/
/****************************************************************************************************/
const	TBXDEF	TrcBitTbl[] = {
  { 0x00,  NULL						},	
  { 0x01,  &GetBitData_SvonReq		},	
  { 0x02,  &GetBitData_Pcon			},	
  { 0x03,  &GetBitData_Pot			},	
  { 0x04,  &GetBitData_Not			},	
  { 0x05,  &GetBitData_AlmRst		},

};

const UINT16 TrcBitTblEntNum = sizeof(TrcBitTbl)/sizeof(TrcBitTbl[0]);






