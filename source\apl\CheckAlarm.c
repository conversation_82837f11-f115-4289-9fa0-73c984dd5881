/****************************************************************************************************
 *
 * FILE NAME:  CheckAlarm.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseLoops.h"
#include "CheckAlarm.h"
#include "Mlib.h"
#include "HardApi.h"
#include "LostPhaseCheck.h"

/*--------------------------------------------------------------------------------------------------*/
/*			 Overload calculation section selection													*/
/*--------------------------------------------------------------------------------------------------*/
#define NORMAL_AREA			1U
#define LOWSPD_AREA			2U
#define LOCK_AREA			3U
//#define UID_CHECK_TIME      (30*60000) // 30 min
#define UID_CHECK_TIME      (1*60000) // 1 min

#if 0
/****************************************************************************************************
* DESCRIPTION:
*			 Calculation of amplifier low speed overload level (online)
* RETURNS:
*
****************************************************************************************************/
PRIVATE BOOL AdetPcalOverLoadLevelAmpLow( CHECK_ALARM *CheckAlarm, INT32 AbsMotSpd)
{
	INT32	Ix;
	INT32	Iy;
	INT32	Ibsqr;
	INT32	Ixsqr;
	INT32	Iysqr;
	INT32	Izsqr;
	INT32	wk;
	BOOL	ret;

	OLPRM *AmpLow;
	CHECK_OLP *AmpLowOLP;
	CHECK_OL *MotOLP;
	CHECK_OL *AmpOLP;
	CHECK_LOWOL *LowOL;
	LowOL = &(CheckAlarm->AmpLowOL);
	MotOLP = &(CheckAlarm->MotOL);
	AmpOLP = &(CheckAlarm->AmpOL);
	AmpLow = &(LowOL->LowOLPrm.AmpLow);
	AmpLowOLP = &(AmpOLP->conf.LowSpdArea);
	ret = TRUE;


	/* Calculation of low-speed section middle OL current and low-speed section maximum OL time */
	wk = MlibMulgain( (AbsMotSpd - LowOL->LowOLChkP.conf.LockSpdOLchg), LowOL->LowOLChkP.conf.TransOLImid );
	Ix = LowOL->LowOLChkP.conf.LowSpdOffsetImid + wk;

	wk = (INT32)MlibSqrtu32( AbsMotSpd - LowOL->LowOLChkP.conf.LockSpdOLchg );
	wk = MlibMulgain( wk, LowOL->LowOLChkP.conf.TransOLTmax );
	Iy = LowOL->LowOLChkP.conf.LowSpdOffsetTmax + wk;

	Ibsqr = AmpOLP->conf.NormalArea.Ibsqrmid;
	AmpLowOLP->Ibsqrmid = Ibsqr;				

	/********************************************************************************************/
	/*																							*/
	/*		Ibsqr = Ibse * Ibse																	*/
	/*																							*/
	/*				  (Imid^2 - Ibse^2) * Tmid													*/
	/*		AlmLvl = ----------------------------												*/
	/*						  Cycle																*/
	/*																							*/
	/********************************************************************************************/
	Ix = (Ix > 0x7FFF)? 0x7FFF : Ix;
	Ixsqr = (Ix * Ix) >> 16;						// Intermediate OL current^2


	Izsqr = Ixsqr - Ibsqr;
	AmpLowOLP->AlmLvlmid = MlibScalKxgain( Izsqr, 100*AmpLow->Tmid, TASKC_CYCLEMS, NULL, -30 );
	AmpLowOLP->OlLvlmidGain = MlibScalKxgain( AmpOLP->conf.NormalArea.AlmLvlmid, 1, AmpLowOLP->AlmLvlmid, NULL, 24 );
	if( AmpLowOLP->AlmLvlmid >= 0x40000000 )
	{
		ret = FALSE;
	}

	/********************************************************************************************/
	/*		Calculation of instantaneous overload level in low speed section					*/
	/********************************************************************************************/
	/*																							*/
	/*				 (Imid^2 * Tmid) - (Imax^2 * Tmax)											*/
	/*		Ibsqr = -----------------------------------											*/
	/*						 (Tmid - Tmax)														*/
	/*																							*/
	/*				  (Imax^2 - Ibsqr) * Tmax													*/
	/*		AlmLvl = ----------------------------												*/
	/*						   Cycle															*/
	/*																							*/
	/********************************************************************************************/
	if( AmpLow->Imax > 0x7FFF )						
	{
		AmpLowOLP->Ibsqrmax = MotOLP->conf.NormalArea.Ibsqrmax;		
		AmpLowOLP->AlmLvlmax = AmpOLP->conf.NormalArea.AlmLvlmax;	
		AmpLowOLP->WrnLvlmax = AmpOLP->conf.NormalArea.WrnLvlmax;	
	}
	else
	{
		Ix = AmpLow->Imax;

		Iysqr = (Ix * Ix) >> 16;					

		wk = (Ixsqr * AmpLow->Tmid) - (Iysqr * Iy);		
		Ibsqr = ( wk ) / ( AmpLow->Tmid - Iy);			


		if( wk > 0 )
		{

			if( Ibsqr < 0 )									
			{
				AmpLowOLP->Ibsqrmax = MotOLP->conf.NormalArea.Ibsqrmax;		
			}

			if( Ibsqr < AmpLowOLP->Ibsqrmid )				
			{
				AmpLowOLP->Ibsqrmax  = AmpLowOLP->Ibsqrmid;		
				AmpLowOLP->OlLvlmaxGain = AmpLowOLP->OlLvlmidGain;
			}
			else
			{
				Izsqr = Iysqr - Ibsqr;
				AmpLowOLP->Ibsqrmax  = Ibsqr;
				AmpLowOLP->AlmLvlmax = MlibScalKxgain( Izsqr, (100*Iy), TASKC_CYCLEMS, NULL, -30 );
				AmpLowOLP->OlLvlmaxGain = MlibScalKxgain( AmpOLP->conf.NormalArea.AlmLvlmax, 1, AmpLowOLP->AlmLvlmax, NULL, 24 );
				if( AmpLowOLP->AlmLvlmax >= 0x40000000 )
				{
					ret = FALSE;
				}
			}
		}

		AmpLowOLP->AlmLvlmid = AmpOLP->conf.NormalArea.AlmLvlmid;		
		AmpLowOLP->AlmLvlmax = AmpOLP->conf.NormalArea.AlmLvlmax;		
		AmpLowOLP->WrnLvlmid = AmpOLP->conf.NormalArea.WrnLvlmid;		
		AmpLowOLP->WrnLvlmax = AmpOLP->conf.NormalArea.WrnLvlmax;	
	}
	return	ret;
}
#endif

/****************************************************************************************************
 * DESCRIPTION:
 *           Amplifier & motor overload check processing execution function
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void AdetExeCheckOLAmpMot( INT32 AbsMotSpd, REAL32 Ixsqr, CHECK_OL *ol_ptr, ALARM *AlmMngr, UINT8 Mode )	
{
	CHECK_OLP	*CheckOLP;		
	REAL32	Sumx = 0;
	REAL32	Ixsqr_s = 0;
	REAL32	reduceLoad=0;
	REAL32	ltmp = 0;

	Ixsqr_s = Ixsqr;

    /* Overload calculation section selection */
	if( Mode == NORMAL_AREA )
	{
		CheckOLP = &(ol_ptr->conf.NormalArea);
	}
	else if( Mode == LOWSPD_AREA )
	{
		CheckOLP = &(ol_ptr->conf.LowSpdArea);
	}
	else if( Mode == LOCK_AREA )
	{
		CheckOLP = &(ol_ptr->conf.LowSpdArea);
	}
	else
	{
		CheckOLP = &(ol_ptr->conf.NormalArea);
	}

//	reduceLoad = 16384 - (INT32)( (float)AbsMotSpd * CheckOLP->CoolRateGain);  //  Self-running cooling rate

	ol_ptr->var.WrnInfo = 0x00;			

	/* Check for instantaneous overload */
	if( CheckOLP->CoolRateGain == 0 )
	{
		ltmp = (Ixsqr_s - CheckOLP->Ibsqrmax) * CheckOLP->OlLvlmaxGain ;
	}
	else
	{
		ltmp = ((((INT16)Ixsqr_s * (INT16)reduceLoad)>>14) - CheckOLP->Ibsqrmax) * CheckOLP->OlLvlmaxGain ;
	}

	Sumx = ol_ptr->var.SumImax + ltmp ;

	if( Sumx >= CheckOLP->AlmLvlmax*0.9)			
	{
		ol_ptr->var.SumImax = Sumx;
		ALMSetServoAlarm( AlmMngr, WRN_OLF1 );	
		AlmMngr->CurrntLimitFlag =TRUE;	
	}
	else
	{
		ALMClearWarning( AlmMngr, WRN_OLF1 );		
		AlmMngr->CurrntLimitFlag =FALSE;
	}

	if( Sumx >= CheckOLP->WrnLvlmax )				
	{
		ol_ptr->var.SumImax = Sumx;
		ol_ptr->var.WrnInfo |= 0x01;			
	}
	else if( Sumx >= 0 )						
	{
		ol_ptr->var.SumImax = Sumx;
	}
	else									
	{
		ol_ptr->var.SumImax = 0;
	}

	/* Check for continuous overload */
	if( CheckOLP->CoolRateGain == 0 )
	{
		ltmp = (Ixsqr_s - CheckOLP->Ibsqrmid) * CheckOLP->OlLvlmidGain ;
	}
	else
	{
		ltmp = ((((INT16)Ixsqr_s * (INT16)reduceLoad)>>14) - CheckOLP->Ibsqrmid) * CheckOLP->OlLvlmidGain ;
	}

	Sumx = ol_ptr->var.SumImid + ltmp;

	if( Sumx >= CheckOLP->AlmLvlmid*0.9)				
	{
		ol_ptr->var.SumImid = Sumx;
		ALMSetServoAlarm( AlmMngr, WRN_OLF );	
		AlmMngr->CurrntLimitFlag =TRUE;	
	}
	else
	{
		ol_ptr->var.SumImid = Sumx;
		ALMClearWarning( AlmMngr, WRN_OLF );	
		AlmMngr->CurrntLimitFlag =FALSE;
	}

	if( Sumx >= CheckOLP->WrnLvlmid )				
	{
		ol_ptr->var.SumImid = Sumx;
		ol_ptr->var.WrnInfo |= 0x02;			
	}
	else if( Sumx >= 0 )						
	{
		ol_ptr->var.SumImid = Sumx;
	}
	else										
	{
		ol_ptr->var.SumImid = 0;
	}
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL AdetCheckOverLoadAmpMot( ALARM *AlmMngr, CHECK_ALARM *CheckAlarm,
								 REAL32 IdRefMon, REAL32 IqRefMon, REAL32 AbsMotSpd, BOOL EncConnect )	
{
	REAL32		Ixsqr;
	CHECK_OL	*MotOL;
	CHECK_OL	*AmpOL;
	CHECK_LOWOL *LowOL;		
	LOWOLCHKP	*LowOLChkP;	
	BOOL		ret;		


	Ixsqr =	(IdRefMon * IdRefMon) + (IqRefMon * IqRefMon);
	MotOL = &(CheckAlarm->MotOL);
	AmpOL = &(CheckAlarm->AmpOL);
	LowOL = &(CheckAlarm->AmpLowOL);	
	LowOLChkP = &(LowOL->LowOLChkP);	
	ret = TRUE;							

	/* Motor normal section OL check */
	AdetExeCheckOLAmpMot( AbsMotSpd, Ixsqr, MotOL, AlmMngr, NORMAL_AREA );	

	/* Amplifier normal section OL check: Low speed OL cannot be executed, or motor speed is normal section */
	if( (LowOLChkP->conf.LowSpdOLChkEnable == (UINT8)FALSE) || (AbsMotSpd > LowOLChkP->conf.LowSpdOLchg) )
	{
		AdetExeCheckOLAmpMot( AbsMotSpd, Ixsqr, AmpOL, AlmMngr, NORMAL_AREA);
	}
	/* Amplifier low speed section OL check: Motor speed is low speed section */
	else if( (AbsMotSpd <= LowOLChkP->conf.LowSpdOLchg) && (LowOLChkP->conf.LockSpdOLchg <= AbsMotSpd) )
	{
//		ret = AdetPcalOverLoadLevelAmpLow( CheckAlarm, AbsMotSpd);
		AdetExeCheckOLAmpMot( AbsMotSpd, Ixsqr, AmpOL, AlmMngr, LOWSPD_AREA);
	}
	/* Amplifier lock section OL check: Motor speed is locked section */
	else
	{
		AdetExeCheckOLAmpMot( AbsMotSpd, Ixsqr, AmpOL, AlmMngr,LOCK_AREA);
	}

	/* Overload warning check */
	if( EncConnect == TRUE )/* Warning not detected when encoder is not connected */
	{
		if( MotOL->var.WrnInfo || AmpOL->var.WrnInfo )
		{
			// ALMSetServoAlarm( AlmMngr, WRN_OLF );			
		}
		else
		{
			// ALMClearWarning( AlmMngr, WRN_OLF );			
		}
	}
	return ret;		
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckOverSpeed( ALARM *AlmMngr, CHECK_OVRSPD *pChkOs, INT32 MotSpd, BPRMDAT *Bprm )
{
	INT32	AbsMotSpd;

	AbsMotSpd = MlibABS( MotSpd );

	if( AbsMotSpd >= pChkOs->OsAlmLvl )
	{
		pChkOs->OSCnt++;
		if(pChkOs->OSCnt >= pChkOs->OSCountMax )
		{
			pChkOs->OSCnt = pChkOs->OSCountMax;
			ALMSetServoAlarm( AlmMngr, ALM_OS );			
		}
	}
	else
	{
		pChkOs->OSCnt = 0;
	}
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckMotorOverrun( ALARM *AlmMngr, CHECK_OVRRUN *OvrRun,INT32 MotSpd,
				INT32 TrqRef, INT32 BaseEnableSts, UINT16 CtrlMode)
{
	INT32	Condition0;
	INT32	Condition1;
	INT32	DetectTime;
	INT32	AbsTrqRef;
	INT32	AbsMotSpd;
	UINT32  OvrTrqLevel = OvrRun->OvrTrqLevel;

	AbsTrqRef = MlibABS( TrqRef );
	AbsMotSpd = MlibABS( MotSpd );

	if(CtrlMode == BASE_MODE_TRQ)
	{
		OvrTrqLevel  = TrqRef;
	}
	
	if( BaseEnableSts == 1)
	{
	 	if(OvrRun->OvrChkCnt < 500 )    // 500ms
			Condition0 = TRUE;
	}
	else
	{
		Condition0 = FALSE;
		OvrRun->OvrChkCnt = 0;
		OvrRun->OvrPeakMotSpd  = 0;
		OvrRun->OvrAbsMotSpd = 0;
		OvrRun->OvrTrqRef = 0;
	}
	/* speed >= over speed * 1/10? */
	if( (OvrRun->OvrChkCnt != 0) || (AbsMotSpd >= MOTSPD_OS_10) )
	{
		Condition1 = TRUE;
	}
	else
	{
		Condition1 = FALSE;
	}
	DetectTime = 20;  // 20ms

	if( Condition0 )
	{
		if( Condition1 )
		{
			OvrRun->OvrChkCnt++;
		}
		
		if( ((MotSpd ^ TrqRef) < 0)					
			&& ((TrqRef ^ OvrRun->OvrTrqRef) >= 0)		
			&& (AbsTrqRef >= OvrTrqLevel)		
			&& (AbsMotSpd >= OvrRun->OvrSpdLevel)			
			&& (TrqRef != 0) )						
		{
			if( AbsMotSpd >= OvrRun->OvrAbsMotSpd )
			{ 
				if( AbsMotSpd >= OvrRun->OvrPeakMotSpd )
				{ 
					if( OvrRun->OvrAlmCnt >= DetectTime )
					{ 
						ALMSetServoAlarm( AlmMngr, ALM_ORUN );
					}
					else
					{
						if(AbsTrqRef >= 0x01000000)
						{
							OvrRun->OvrAlmCnt = OvrRun->OvrAlmCnt + 2;
						}
						else
						{
							OvrRun->OvrAlmCnt++;
						}
					}
				}
				else
				{
					OvrRun->OvrAlmCnt = 0;
				}
			}
			else
			{
				if( OvrRun->OvrAbsMotSpd > OvrRun->OvrPeakMotSpd )
				{ 
					OvrRun->OvrPeakMotSpd = OvrRun->OvrAbsMotSpd;
				}
				OvrRun->OvrAlmCnt = 0;
			}
		}
		else
		{
			OvrRun->OvrAlmCnt = 0;
		}
	}

	/* Last torque update */
	OvrRun->OvrTrqRef = TrqRef;
	/* Last motor speed update */
	OvrRun->OvrAbsMotSpd = AbsMotSpd;
}

																  
/****************************************************************************************************
* DESCRIPTION: Position error check processing at servo-on
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC void AdetCheckSvonPerrOverFlow( ALARM *AlmMngr, CHECK_OVRPOSERR *OvrPosErr, INT32 PositionError )
{
	INT32	AbsPosError;

	AbsPosError = MlibABS( PositionError );


	/* Position error check (with base enable request & & Servo ON not completed)*/
	if( AbsPosError >= OvrPosErr->SvonPerAlmLevel )
	{
		ALMSetServoAlarm( AlmMngr, ALM_BEOF );			
	}

	if( AbsPosError >= OvrPosErr->SvonPerWrnLevel )
	{
		ALMSetServoAlarm( AlmMngr, WRN_BEOF );			
	}
	else
	{
		ALMClearWarning( AlmMngr, WRN_BEOF );			
	}
}


/****************************************************************************************************
* DESCRIPTION: Position error check processing
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC void AdetCheckPerrOverFlow( ALARM *AlmMngr, CHECK_OVRPOSERR *OvrPosErr,
										BOOL PosCtrlMode, INT32 PositionError, BOOL SvonSpdLmt )
{
	INT32	AbsPosError;

	AbsPosError = MlibABS( PositionError );


	if( FALSE == PosCtrlMode )
	{
		ALMClearWarning( AlmMngr, WRN_OF );
		return;
	}

	if( AbsPosError >= OvrPosErr->PerAlmLevel )
	{
		if( SvonSpdLmt )
		{
			ALMSetServoAlarm( AlmMngr, ALM_BEVLMTOF );
		}
		else
		{
			ALMSetServoAlarm( AlmMngr, ALM_OF );
		}
	}
	/*----------------------------------------------------------------------------------------------*/
	if( AbsPosError >= OvrPosErr->PerWrnLevel )
	{
		ALMSetServoAlarm( AlmMngr, WRN_OF );
	}
	else
	{
		ALMClearWarning( AlmMngr, WRN_OF );
	}
}
										

/****************************************************************************************************
* DESCRIPTION: Following Position error check processing
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC void AdetCheckPosFollow( ALARM *AlmMngr, CHECK_FOLLPOSERR *PosFollowErr,
										BOOL PosCtrlMode, INT32 PositionError,CiA402_PRM *Objects)
{
	INT32	AbsPosError;
    CIA402_STATUS_WORD      StatusWord = {0};
    StatusWord.all = Objects->Statusword0x6041;

	AbsPosError = MlibABS( PositionError );
    if(StatusWord.bit.InLimitActive == 1)
    {
       ALMSetServoAlarm( AlmMngr, WRN_POSLIMIT ); 
    }
    else
    {
       ALMClearWarning( AlmMngr, WRN_POSLIMIT ); 
    }


	if( FALSE == PosCtrlMode )
	{
		ALMClearWarning( AlmMngr, WRN_POS );
		return;
	}

    if(PosFollowErr->FollowAlmTime )		// Position Following Alarm
	{
		if( (- PosFollowErr->FollowAlmLevel >  AbsPosError ) 
								|| (PosFollowErr->FollowAlmLevel <  AbsPosError ) ) 	
		{
			if(PosFollowErr->FollowAlmCnt++ >= PosFollowErr->FollowAlmTime)
			{
                StatusWord.bit.OperSpecific_b13 = 1;
				ALMSetServoAlarm( AlmMngr, WRN_POS );
			}		
		}
		else
		{
            ALMClearWarning( AlmMngr, WRN_POS );
            StatusWord.bit.OperSpecific_b13 = 0;
			PosFollowErr->FollowAlmCnt = 0;
		}
	}
    Objects->Statusword0x6041 = StatusWord.all;

	
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckAdOffset( ALARM *AlmMngr, UINT16 AxisID )
{
    if(hApi_GetAdcInitState(AxisID))
        ALMSetServoAlarm( AlmMngr, ALM_AD );
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckSwOc(  ALARM *AlmMngr, CHECK_ALARM *CheckAlarm, BASE_LOOP *BaseLoops)
{
    REAL32 TmpIa,
	TmpIb,TmpIc,TmpI;
	
	TmpIa = MlibABS(BaseLoops->CurLoop.V.Ia);
	TmpIb = MlibABS(BaseLoops->CurLoop.V.Ib);
	TmpIc = MlibABS(BaseLoops->CurLoop.V.Ic);

	TmpI = MlibMAX(TmpIa, TmpIb);
	TmpI = MlibMAX(TmpI, TmpIc);
    
    if(BaseLoops->BaseCtrls->BaseEnable && TmpI > (CheckAlarm->SwOc.SwOcCur) )
	{
		if(CheckAlarm->SwOc.SwOcCnt >= 4)
		{
			ALMSetServoAlarm( AlmMngr, ALM_OC );
		}
		else
		{
			CheckAlarm->SwOc.SwOcCnt++;
		}
	}
	else
	{
		CheckAlarm->SwOc.SwOcCnt = 0;
	}
        
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckMotStall(ALARM *AlmMngr, CHECK_STALL *MotStall, REAL32 Iq, INT32 MotSpd )
{
	REAL32 TmpI = MlibABS(Iq);
	UINT32 AbsMotSpd = MlibABS(MotSpd);
	
	if(TmpI > MotStall->StallCur && AbsMotSpd < MotStall->StallSpdL) 
	{
        MotStall->StallCnt++;	
        if(MotStall->StallCnt >= MotStall->StallCountMax)
        {
            ALMSetServoAlarm( AlmMngr, ALM_STALL );
            MotStall->StallCnt = MotStall->StallCountMax;
        }				
	}
	else
	{
		MotStall->StallCnt = 0;
	}	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckUid(ALARM *AlmMngr, CHECK_UID *Uid_Alm, UINT32 Uid_Check_Result)
{
    UINT32 Uid_Checkfalg = Uid_Check_Result;
    if(Uid_Checkfalg == 1)
    {
        Uid_Alm->CheckCnt++;
        if(Uid_Alm->CheckCnt >= UID_CHECK_TIME)
        {
            ALMSetGlobalAlarm(ALM_UID);
            Uid_Alm->CheckCnt = UID_CHECK_TIME;
        }
    }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckBusOC(BOOL BaseEnable )
{
  if(BaseEnable && (TRUE == hApi_GetBusOcSignal()))
  {
     ALMSetGlobalAlarm(ALM_BUSOC);
  }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckPhaseLack(ALARM *AlmMngr, CHECK_PHALACK *PhaseChk, BASE_LOOP *BaseLoops)
{
    REAL32 TmpIa,TmpIb,TmpIc;
    REAL32 fw = 0;
    UINT32 PLCheckTime = PhaseChk->conf.PLCheckTime;
    
    if( BaseLoops->BaseCtrls->CmdEnable == FALSE  || BaseLoops->CurLoop.V.WeakFieldFlag)   // WeakField will not check Phase lack 
    {
      MlibResetLongMemory( &(PhaseChk->var), sizeof(PhaseChk->var)/4 );
      return;
    }
    
    if(BaseLoops->CtrlMode == BASE_MODE_TRQ)
    {
      fw = MlibABS(BaseLoops->CurLoop.V.IqRef);
      
      if(fw <= PhaseChk->conf.TrqPhaLackCur)
      {
        return;
      }
    }
    else
    {
      fw = PhaseChk->conf.PhaLackCur;
    }
    
    TmpIa = MlibABS(BaseLoops->CurLoop.V.IaRef);
    TmpIb = MlibABS(BaseLoops->CurLoop.V.IbRef); 
    TmpIc = MlibABS(BaseLoops->CurLoop.V.IcRef);    
    

    if((TmpIa >= fw)|| (TmpIb >= fw)||  (TmpIc >= fw))
    {  
        PhaseChk->var.CheckCnt++;
        
        if(PhaseChk->var.CheckCnt <= PLCheckTime)
        {
          PhaseChk->var.IaRefSum += MlibABS(BaseLoops->CurLoop.V.IaRef);
          PhaseChk->var.IbRefSum += MlibABS(BaseLoops->CurLoop.V.IbRef);  
          PhaseChk->var.IcRefSum += MlibABS(BaseLoops->CurLoop.V.IcRef); 

          PhaseChk->var.IaFdbSum += MlibABS(BaseLoops->CurLoop.V.Ia);
          PhaseChk->var.IbFdbSum += MlibABS(BaseLoops->CurLoop.V.Ib);   
          PhaseChk->var.IcFdbSum += MlibABS(BaseLoops->CurLoop.V.Ic);
        }
        else
        {
          PhaseChk->var.CheckCnt = PLCheckTime + 1;
          PhaseChk->var.IaRefSum = PhaseChk->var.IaRefSum/PLCheckTime;
          PhaseChk->var.IbRefSum = PhaseChk->var.IbRefSum/PLCheckTime;
          PhaseChk->var.IcRefSum = PhaseChk->var.IcRefSum/PLCheckTime;
          
          PhaseChk->var.IaFdbSum = PhaseChk->var.IaFdbSum/PLCheckTime;
          PhaseChk->var.IbFdbSum = PhaseChk->var.IbFdbSum/PLCheckTime;
          PhaseChk->var.IcFdbSum = PhaseChk->var.IcFdbSum/PLCheckTime;
          
          if((PhaseChk->var.IaRefSum > fw) && (PhaseChk->var.IaFdbSum <= 0.1* PhaseChk->var.IaRefSum))
          {
            ALMSetServoAlarm( AlmMngr, ALM_PHALACK);
          }
          if((PhaseChk->var.IbRefSum > fw) && (PhaseChk->var.IbFdbSum <= 0.1* PhaseChk->var.IbRefSum))
          {
            ALMSetServoAlarm( AlmMngr, ALM_PHALACK);
          }          

          if((PhaseChk->var.IcRefSum > fw) && (PhaseChk->var.IcFdbSum <= 0.1* PhaseChk->var.IcRefSum))
          {
           ALMSetServoAlarm( AlmMngr, ALM_PHALACK);
          }  
          MlibResetLongMemory( &(PhaseChk->var), sizeof(PhaseChk->var)/4 );   
        }    
    }
    else
    {
      MlibResetLongMemory( &(PhaseChk->var), sizeof(PhaseChk->var)/4 );  
      return;
    }
    
}


PUBLIC void LosaPhaseCheckALM( ALARM *AlmMngr, UINT16 AxisID )
{
    if(hApi_GetPhaseState(AxisID))
    {
        ALMSetServoAlarm( AlmMngr, ALM_PHALACK );
        LostPhaseFlagRest(AxisID);
    }
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void AdetCheckOverTemp(ALARM *AlmMngr, CHECK_OVTEMP *OverTemp)
{
	
	if(OverTemp->var.MotTemp > OverTemp->conf.MaxMotTemp)
	{
		OverTemp->var.MotVtCnt++;
	}
	else
	{
		OverTemp->var.MotVtCnt = 0;
	}

	if(OverTemp->var.IPMTemp > OverTemp->conf.MaxIPMTemp)
	{
		OverTemp->var.IPMVtCnt++;
	}
	else
	{
		OverTemp->var.IPMVtCnt = 0;
	}	
        
        // motor over tempareture  
	if(OverTemp->var.MotVtCnt >= OverTemp->conf.MotVtTime)	
	{
		ALMSetServoAlarm( AlmMngr, ALM_FAN_TEMP );
		OverTemp->var.MotVtCnt = 0;
	}
        
        // Ipm over tempareture  
	if(OverTemp->var.IPMVtCnt >= OverTemp->conf.IPMVtTime)
	{
		ALMSetServoAlarm( AlmMngr, ALM_IPM_OTM );
		OverTemp->var.IPMVtCnt = 0;
	}
}




/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
UINT16  gIPDPosNLmit = 0;
UINT16  gIPDPosPLmit = 0;
PUBLIC void AdetCheckIPDPosLimit( ALARM *AlmMngr, CHECK_POS_LMT *OverLimit, REAL32 IPDPosFdbRad, BOOL RefDir )
{
	#if 1
    if((IPDPosFdbRad < OverLimit->conf.IPDPosNLmit))
    {
		OverLimit->var.IPDPosLmtCnt++;
		// if(OverLimit->var.IPDPosLmtCnt >= IPD_POS_LMT_TIME)
		{
			ALMSetServoAlarm( AlmMngr, WRN_POSLIMIT );
			OverLimit->var.IPDPosLmtCnt = 0;
		}
    }
    else if((IPDPosFdbRad > OverLimit->conf.IPDPosPLmit))
    {
		OverLimit->var.IPDPosLmtCnt++;	
		// if(OverLimit->var.IPDPosLmtCnt >= IPD_POS_LMT_TIME)
		{
			ALMSetServoAlarm( AlmMngr, WRN_POSLIMIT );
			OverLimit->var.IPDPosLmtCnt = 0;
		}
    }
	else
	{
		ALMClearWarning( AlmMngr, WRN_POSLIMIT ); 
		OverLimit->var.IPDPosLmtCnt = 0;
	}
	#else
    if((IPDPosFdbRad < OverLimit->conf.IPDPosNLmit) && (RefDir == 1))
    {
		OverLimit->var.IPDPosLmtCnt++;
		if(OverLimit->var.IPDPosLmtCnt >= IPD_POS_LMT_TIME)
		{
			ALMSetServoAlarm( AlmMngr, WRN_POSLIMIT );
			OverLimit->var.IPDPosLmtCnt = 0;
                        gIPDPosNLmit = 1;
		}
    }
    else if((IPDPosFdbRad > OverLimit->conf.IPDPosPLmit) && (RefDir == 0))
    {
		OverLimit->var.IPDPosLmtCnt++;	
		if(OverLimit->var.IPDPosLmtCnt >= IPD_POS_LMT_TIME)
		{
			ALMSetServoAlarm( AlmMngr, WRN_POSLIMIT );
			OverLimit->var.IPDPosLmtCnt = 0;
                        gIPDPosPLmit = 1;
		}
    }
	else
	{
		OverLimit->var.IPDPosLmtCnt = 0;
		ALMClearWarning( AlmMngr, WRN_POSLIMIT ); 
	}
	#endif
}