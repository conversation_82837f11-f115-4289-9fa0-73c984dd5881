#ifndef __MM_DEFS
	#define __MM_DEFS

#define         MM_MAX_NAME                     8

/* Define constants local to this component.  */
#define         MM_DYNAMIC_ID          	0x12345678UL
#define         MM_OVERHEAD     ((sizeof(MM_HEADER) + sizeof(unsigned) \
                                        - 1)/sizeof(unsigned)) *    \
                                        sizeof(unsigned)

/* Define the Dynamic Pool Control Block data type.  */
typedef struct MM_PCB_STRUCT 
{   
    
    unsigned            dm_id;                 /* Internal PCB ID        */
    char                dm_name[MM_MAX_NAME];  /* Dynamic Pool name      */
    void               *dm_start_address;      /* Starting pool address  */
    unsigned            dm_pool_size;          /* Size of pool           */
    unsigned            dm_min_allocation;     /* Minimum allocate size  */
    unsigned            dm_available;          /* Total available bytes  */
    struct MM_HEADER_STRUCT    
                       *dm_memory_list;        /* Memory list            */
    struct MM_HEADER_STRUCT
                       *dm_search_ptr;         /* Search pointer         */
    unsigned char        dm_fifo_suspend;       /* Suspension type flag   */

    unsigned            dm_tasks_waiting;      /* Number of waiting tasks*/
    
} MM_PCB;    



/* Define the header structure that is in front of each memory block.  */

typedef struct MM_HEADER_STRUCT
{
    struct MM_HEADER_STRUCT
                       *dm_next_memory,        /* Next memory block      */
                       *dm_previous_memory;    /* Previous memory block  */
    unsigned char        dm_memory_free;        /* Memory block free flag */
    MM_PCB             *dm_memory_pool;        /* Dynamic pool pointer   */
} MM_HEADER;


typedef MM_PCB      MM_POOL;

#endif
