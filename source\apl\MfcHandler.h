/****************************************************************************************************
 *
 * FILE NAME:  <PERSON><PERSON>cHandler.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.01
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	01-08-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _MFC_HANDLER_H_
#define _MFC_HANDLER_H_

#include "BaseDef.h"

/*--------------------------------------------------------------------------------------------------*/
#define	    FLOAT_USE			TRUE

#define	    LLY_MODIFY			FALSE
/*--------------------------------------------------------------------------------------------------*/
#if (FLOAT_USE==TRUE)
typedef	float					KSGAIN;				/* Floating Data								*/
#endif
#if (FLOAT_USE==FALSE)
typedef	INT32					KSGAIN;				/* {k,s} Type Floating Data						*/
#endif /* FLOAT_USE */
/****************************************************************************************************/
/*		STRUCT DEFINITION																			*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Constant definition for MFC operation														*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{	
#if 1
	REAL32	Fsft;							// DTR High-order filter resolution shift amount	
	REAL32	InvFsft;						// DTR High-order filter Reciprocal of resolution shift amount	
	REAL32	Kf1[2];							// Second-order filter gain 1		
	INT32	Kf11[2];							// Second-order filter gain 1		        
	REAL32	Kf2;							// 2nd order filter gain 2 (for 1st order differential filter)
	REAL32	Kf3;							// 2nd order filter gain 3 (for 1st order differential filter)
	REAL32	Kj1;							// Mechanical coefficient gain 1						
	REAL32	Kj2;							// Mechanical coefficient gain 2						
	REAL32	Kj2_sft;						// Mechanical coefficient gain 2 (without shift for resolution increase)
	REAL32	Kj3;							// Mechanical coefficient gain 3							
	REAL32	Kvff;							// Velocity feedforward gain			
	REAL32	Ktff1;							// Torque feedforward gain 1 (for forward rotation)
	REAL32	Ktff2;							// Torque feed forward gain 2 (for reverse rotation)
	REAL32	Cfric;							// Coulomb friction compensation torque						
	REAL32	Kfcf;							// Coulomb friction compensation filter gain		
	REAL32	Fccflag;						// 2-mass && Full-Closed Control Flag	
	REAL32	PSFccflag;						// 2-mass && PS Type Full-Closed Control Flag	
	INT32	MFCModel;						// Machine model									
	REAL32	MFCType;						// MFC processing type	
#else
	INT32	Fsft;							// DTR High-order filter resolution shift amount	
	INT32	InvFsft;						// DTR High-order filter Reciprocal of resolution shift amount	
	INT32	Kf1[2];							// Second-order filter gain 1				
	INT32	Kf2;							// 2nd order filter gain 2 (for 1st order differential filter)
	INT32	Kf3;							// 2nd order filter gain 3 (for 1st order differential filter)
	INT32	Kj1;							// Mechanical coefficient gain 1						
	INT32	Kj2;							// Mechanical coefficient gain 2						
	INT32	Kj2_sft;						// Mechanical coefficient gain 2 (without shift for resolution increase)
	INT32	Kj3;							// Mechanical coefficient gain 3							
	INT32	Kvff;							// Velocity feedforward gain			
	INT32	Ktff1;							// Torque feedforward gain 1 (for forward rotation)
	INT32	Ktff2;							// Torque feed forward gain 2 (for reverse rotation)
	INT32	Cfric;							// Coulomb friction compensation torque						
	INT32	Kfcf;							// Coulomb friction compensation filter gain		
	INT32	Fccflag;						// 2-mass && Full-Closed Control Flag	
	INT32	PSFccflag;						// 2-mass && PS Type Full-Closed Control Flag	
	INT32	MFCModel;						// Machine model									
	INT32	MFCType;						// MFC processing type	
#endif
} MFCPRM;



/*--------------------------------------------------------------------------------------------------*/
/*		Variable definition for MFC operation														*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{							
	struct
	{									
		MFCPRM		MfcPrm[3];						// Parameters for MFC				
		MFCPRM		MfcPexe;						// Parameters for MFC for execution					
		INT32		MFCTypeSetting;					// MFC processing type (parameter setting value)			
		INT32		MFCModelSetting;				// Machine model (parameter setting value)	
		BOOL		MFCFF;							// External FF (speed, torque) valid/invalid setting in MFC
	} conf;

	struct
	{									
		INT32		CurMFCModel;					// Machine model (current set value)
		INT32		execnt;							// Execute Count for MFC Execute Control
		INT32		MfcPosErr;						// Model position deviation (for calculation)[pulse]			
		INT32		PosErr;							// Model position deviation (for monitor)[pulse]		
		INT32		Per64[2];						// Model position deviation (for calculation)[pulse]	
		INT32		Vref;							// Model speed command		[2^24/OvrSpd]	
		INT32		Pfbk;							// Model position FB		[pulse/Scan]	
	/*----------------------------------------------------------------------------------------------*/
		REAL32		Fwk1[2];						// For secondary filter calculation				
		REAL32		Fwk2[2];						// For secondary filter calculation						
		REAL32		Fwk3[2];						// For secondary filter calculation						
		REAL32		Fwk4[2];						// For secondary filter calculation	
		INT32		Fwk11[2];						// For secondary filter calculation				
		INT32		Fwk21[2];						// For secondary filter calculation		                
		INT32		Filo;							// Position command after 1st order differential 2nd filter
		INT32		FCmdF;							// Resolution UP Position command after secondary filter				
		INT32		dFilwk;							// For first-order differential second-order filter calculation				
		INT32		Fwkrem1;						// For first-order differential second-order filter calculation			
		INT32		Fwkrem2;						// For first-order differential second-order filter calculation				
	/*----------------------------------------------------------------------------------------------*/
		REAL32		PCmdsub;						// Position command input				
		REAL32		FCmd[3];						// Position command after high-order filter			
		INT32		dFCmd[2];						// Higher-order filtered position command first-order differential value				
		INT32		d2FCmd[2];						// Higher-order filtered position command second-order differential value				
		INT32		d3FCmd;							// Higher-order filtered position command third-order differential value				
		INT32		wkrem1;							// For position command calculation							
		INT32		wkrem2;							// For position command calculation								
		INT32		PosSum[2];						// Position command addition integrated value							
		INT32		TrqRef;							// Model torque command (monitorable)			
		INT32		Ktffcmdx;						// Torque FF gain							
		INT32		FricCmpTrq;						// Coulomb friction compensation torque					
		INT32		FCmdFilo[3];					// Position command after high-order filter (resolution UP calculation)		
	/*----------------------------------------------------------------------------------------------*/
		INT32		PffCmd;							// Position FF command (monitorable)				
		INT32		VffCmd;							// Speed FF command 						
		INT32		TffCmd;							// Torque FF command (monitorable)					
	/*----------------------------------------------------------------------------------------------*/
		BOOL		MfcStopReq;						// MFC stop request (request from Fn)	
	} var;
} MFCTRL;






#endif

