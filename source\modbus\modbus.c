/****************************************************************************************************
 *
 * FILE NAME:  BaseLoops.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.13
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	13-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseDef.h"
#include "bsp.h"
#include "modbus.h"
#include "modbusdrv.h"
#include "RegMngr.h"
#include "RegAccessIF.h"
#include "Mlib.h"


const UINT32 ModbusBaudRate[7]=    
{
    4800,            // 0, 4800bps
    9600,            // 1, 9600bps
    19200,           // 2, 19200bps
    38400,           // 3, 38400bps
    57600,           // 4, 57600bps
	115200,          // 5, 115200bps
    2500000,         // 6, 2500000
};

//const UINT16 FrameEndTime[7][6]=     	// 帧之间间隔，3.5个字节时间  6.25Mhz
//{
//	/*	8_N_1		8_N_2		8_E_1		8_E_2		8_O_1		8_O_2	*/
//    { 	7292, 		8021, 		8021, 		8750, 		8021, 		8750 }, 	// 4800   -
//    { 	3646, 		4011, 		4011, 		4375, 		4011, 		4375 },    	// 9600
//    { 	1823, 		2006, 		2006, 		2188, 		2006, 		2188 },    	// 19200
//    { 	1750, 		1750, 		1750, 		1750, 		1750, 		1750 },   	// 38400  -1750us
//    { 	1750, 		1750, 		1750, 		1750, 		1750, 		1750 },   	// 38400  -1750us
//    { 	1750, 		1750, 		1750, 		1750, 		1750, 		1750 },   	// 38400  -1750us
//    { 	1750, 		1750, 		1750, 		1750, 		1750, 		1750 },   	// 38400  -1750us    
//};
//const UINT16 FrameBreakTime[7][6]=     	// 帧中断时间，1.5个字节时间  us
//{
//	/*	8_N_1		8_N_2		8_E_1		8_E_2		8_O_1		8_O_2	*/
//	{ 	3125, 		3438, 		3438, 		3750, 		3438, 		3750 }, 	// 4800
//	{ 	1563, 		1719, 		1719, 		1875, 		1719, 		1875 },    	// 9600
//	{ 	782, 		860, 		860, 		938, 		860, 		938 },    	// 19200
//    { 	1750, 		1750, 		1750, 		1750, 		1750, 		1750 },   	// 38400  -1750us
//    { 	1750, 		1750, 		1750, 		1750, 		1750, 		1750 },   	// 38400  -1750us
//    { 	1750, 		1750, 		1750, 		1750, 		1750, 		1750 },   	// 38400  -1750us
//    { 	1750, 		1750, 		1750, 		1750, 		1750, 		1750 },   	// 38400  -1750us    
//};


const UINT16 FrameEndTime[7][6]=     	// 帧之间间隔，3.5个字节时间  6.25Mhz
{
	/*	8_N_1		8_N_2		8_E_1		8_E_2		8_O_1		8_O_2	*/
    { 	7292, 		8021, 		8021, 		8750, 		8021, 		8750 }, 	// 4800   -
    { 	3646, 		4011, 		4011, 		4375, 		4011, 		4375 },    	// 9600
    { 	1823, 		2006, 		2006, 		2188, 		2006, 		2188 },    	// 19200
    { 	911, 		1003, 		1003, 		1094, 		1003, 		1094 },   	// 38400  -1750us
    { 	608, 		668, 		668, 		729, 		668, 		729 },   	// 57600  -1750us
    { 	304, 		334, 		334, 		365, 		334, 		365 },   	// 115200  -1750us
    { 	11, 		15, 		15, 		16, 		15, 		16 },   	// 2500000  -1750us    
};
const UINT16 FrameBreakTime[7][6]=     	// 帧中断时间，1.5个字节时间  us
{
	/*	8_N_1		8_N_2		8_E_1		8_E_2		8_O_1		8_O_2	*/
	{ 	3125, 		3438, 		3438, 		3750, 		3438, 		3750 }, 	// 4800
	{ 	1563, 		1719, 		1719, 		1875, 		1719, 		1875 },    	// 9600
	{ 	782, 		860, 		860, 		938, 		860, 		938 },    	// 19200
    { 	390, 		429, 		429, 		468, 		429, 		468 },   	// 38400  -1750us
    { 	195, 		215, 		214, 		234, 		214, 		234 },   	// 38400  -1750us
    { 	98, 		107, 		107, 		117, 		107, 		117 },   	// 38400  -1750us
    { 	49, 		54, 		54, 		59, 		54, 		59 },   	// 38400  -1750us    
};


MODBUS_ST ModbusStruct = {0}; 

PRIVATE void IndexResolve (UINT16 ModBusIndex, UINT16 *Index, UINT8 *SubIndex);

UINT8 txbuffer[8] = {1,2,3,4,5,6,7,8};
/****************************************************************************************************
 * DESCRIPTION: LED_LOOP
 *
 * RETURNS:
 *
****************************************************************************************************/
void ModbusInit(MODBUS_ST *Modbus,UINT8 BuadIndex, UINT8 Format, BOOL Endian)
{
    
  if(BuadIndex > 6) BuadIndex = 0;

  if(Format > 5) Format = 0;

  
  UINT16 Buadrate = ModbusBaudRate[BuadIndex];
  
  bsp_sci4_init_zx(Buadrate,Format);
  
  MlibResetLongMemory(&ModbusStruct, sizeof(MODBUS_ST)/4);
  
  Modbus->P.C3P5threshold = (FrameEndTime[BuadIndex][Format])* CMTW1_PPR_US;
  Modbus->P.C1P5threshold = (FrameBreakTime[BuadIndex][Format])* CMTW1_PPR_US;
  Modbus->P.Endian = Endian;
}
/****************************************************************************************************
 * DESCRIPTION: LED_LOOP
 *
 * RETURNS:
 *
****************************************************************************************************/
void ModbusSchedual(MODBUS_ST *Modbus)
{
  UINT16 RxDatanum = 0;
  UINT16 dummy = 0;
  
  switch (Modbus->State)
  {
  case MODBUS_STATE_INIT:
    // 清除标志位和fifo数据 --初始化数据
  
    SciReadEnable();
    Modbus->State = MODBUS_STATE_IDLE;
    break;
  case MODBUS_STATE_IDLE:
    Modbus->RxLength  = 0;
    Modbus->RxEndFlag = 0;
    Modbus->TxState = 0;
    if(MODBUS_RX_ISEMPTY != CheckRxState())
    {
      // 读取数据
      RxDatanum = ReadRxDataNum();

      while(RxDatanum != 0)
      { 
        Modbus->RxData[Modbus->RxLength] = ReadRxData();
        RxDatanum--;
        Modbus->RxLength++;
      }
      
      Modbus->C3P5Cnt = bsp_gettimercnt();
      Modbus->C1P5Cnt = bsp_gettimercnt();
      Modbus->State = MODBUS_STATE_RECEIVE;   
    } 
    break;
  case MODBUS_STATE_RECEIVE: 
    if(MODBUS_RX_ISEMPTY != CheckRxState())
    {
      // 读取数据
      RxDatanum = ReadRxDataNum();

      while(RxDatanum != 0)
      { 
        Modbus->RxData[Modbus->RxLength] = ReadRxData();
        RxDatanum--;
        Modbus->RxLength++;
      }
      Modbus->C3P5Cnt = bsp_gettimercnt();
      Modbus->C1P5Cnt = bsp_gettimercnt();
    }
    else
    {
      if(bsp_gettimercnt() - Modbus->C1P5Cnt >= Modbus->P.C1P5threshold)
      {
        Modbus->State = MODBUS_STATE_CTRL;  //超时t1.5 达成
      }
    }
    
    break;
  case MODBUS_STATE_CTRL:   //判断是帧中断还是帧结束
    if(bsp_gettimercnt() - Modbus->C3P5Cnt >= Modbus->P.C3P5threshold)
    {
      Modbus->RxEndFlag = 1;
      SciReadDisEnable();
      //todo 判断数据的有效性 --有效进入到发送（包括数据解析和数据打包），无效进入到空闲
      Modbus->State = MODBUS_STATE_SEND;  //超时t3.5 达成
    } 
    else
    {
      // 帧中断处理
      if(MODBUS_RX_ISEMPTY != CheckRxState())  
      {
        Modbus->RxLength = 0;
        // 读取数据
        RxDatanum = ReadRxDataNum();

        while(RxDatanum != 0)
        { 
          Modbus->RxData[Modbus->RxLength] = ReadRxData();
          RxDatanum--;
          Modbus->RxLength++;
        }
        Modbus->C3P5Cnt = bsp_gettimercnt();
        Modbus->C1P5Cnt = bsp_gettimercnt();
        Modbus->State = MODBUS_STATE_RECEIVE;  
      } 
    }     
    break;
  case MODBUS_STATE_SEND:  // 不考虑发送错误的情况
    if(Modbus->TxState == 1)  // 可进行t3.5判断，表示发送完成
    {
      if(1 == SciGetTendFlag()) // 获取发送完成标志位
      {
        // 清除发送完成标志
        Modbus->C3P5Cnt = bsp_gettimercnt();
        Modbus->TxState = 2;
      }
    }
    else if(Modbus->TxState == 2) 
    {
      if(bsp_gettimercnt() - Modbus->C3P5Cnt >= Modbus->P.C3P5threshold)
      {
        Modbus->State = MODBUS_STATE_IDLE;        
      }
    }
    else if(Modbus->TxState == 3) 
    {
        Modbus->State = MODBUS_STATE_IDLE;
    } 
    break;   
  default:
    break;    
  }
    
  // 清除错误保证通讯正常-由串口fifo溢出、奇偶校验和帧错误导致的数据错误，统一由CRC去校验    
  if(MODBUS_STATE_SEND != Modbus->State)
  {
    SciReadEnable();
  }  
}

/****************************************************************************************************
 * DESCRIPTION: Modbus CRC  Calc
 *
 * RETURNS:
 *
****************************************************************************************************/
UINT16 ModbusCrcCalc(UINT8 *data, UINT16 length)
{
    UINT16 crcValue = 0xffff;
    UINT16 i;
	UINT16 Temp1;
   
    while (length--)
    {
        Temp1 = (UINT16)(*data);
		crcValue = crcValue ^ Temp1;
		
		
		//crcValue ^= (UINT16)*data++;

        for (i = 0; i < 8; i++)
        {
            if (crcValue & 0x0001)
            {
                crcValue = (crcValue >> 1) ^ 0xA001;
            }
            else
            {
                crcValue = crcValue >> 1;
            }
        }

		data++;
    }

    return (crcValue);
}

/****************************************************************************************************
 * DESCRIPTION: Index recover 
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void IndexResolve (UINT16 ModBusIndex, UINT16 *Index, UINT8 *SubIndex)
{
   UINT8 IndexH = 0;
 
   
   IndexH = (UINT8)((ModBusIndex & 0xFF00) >> 8);
   
   if(IndexH != 0x0F)
   {
     *Index = 0x2000 + IndexH;
     *SubIndex = (UINT8)(ModBusIndex & 0x00FF);
   }
   else
   {
//     *Index = 0x6000 + IndexH;
     
   }   
   
   
}
/****************************************************************************************************
 * DESCRIPTION: Modbus Err Check
 *
 * RETURNS:
 *
****************************************************************************************************/
void ModbusRead (MODBUS_ST *modbus,UINT8 SlaveID)
{
  UINT16  CRC_temp = 0, ParamEntry= 0;  
  UINT16  ModBusIndex = 0;
  UINT16  Index = 0;
  UINT8   Subindex = 0;
  UINT8   ReadState = 0;
  UINT16  ReadRegNum  = 0;
  UINT16  ReadByteNum = 0, TotalByteNum = 0;
  UINT8 DataBuffer[4] = {0};
  
  
 UINT32  DataLenght = 0;
 REG_ACC_T  AccessPrm;


  if(modbus->P.Endian)
  {
    ModBusIndex = ((UINT16)modbus->RxData[INEDXL]<<8) | modbus->RxData[INEDXH];
    ReadRegNum = ((UINT16)modbus->RxData[DATALL]<<8) | modbus->RxData[DATALH];
  }
  else
  {
    ModBusIndex = ((UINT16)modbus->RxData[INEDXH]<<8) | modbus->RxData[INEDXL];
    ReadRegNum = ((UINT16)modbus->RxData[DATALH]<<8) | modbus->RxData[DATALL];   
  }
  
  ReadByteNum  = ReadRegNum * 2;  // 按16位寄存器个数读取
//  ReadByteNum  = ReadRegNum;        // 按字节个数读取
  TotalByteNum = ReadByteNum;
  ParamEntry = 3;
  
//  IndexResolve(ModBusIndex, &Index, &Subindex);
  
  while(ReadByteNum)
  {   
    
      ReadState =  PrmModbusObjLenGet(ModBusIndex,&DataLenght,&AccessPrm);

      if(0 == ReadState)
      {
        if(DataLenght > ReadByteNum)
        {
          ReadState = MODBUS_PRM_ERROR;
        }
        else
        {
          ReadState = PrmModBusObjRead((UINT16*)&(DataBuffer[0]),DataLenght,&AccessPrm); 
        }
        
        if(0 == ReadState)
        {
          if(modbus->P.Endian)
          {
              switch (DataLenght)
              {
              case 1:           
                modbus->TxData[ParamEntry]  = DataBuffer[0];
                modbus->TxData[ParamEntry+1]    = 0;
                break;
              case 2:
                modbus->TxData[ParamEntry]    = DataBuffer[0];
                modbus->TxData[ParamEntry+1]  = DataBuffer[1];
                break;  
              case 4:
                modbus->TxData[ParamEntry]    = DataBuffer[0];      
                modbus->TxData[ParamEntry+1]  = DataBuffer[1];
                
                modbus->TxData[ParamEntry+2]  = DataBuffer[2];
                modbus->TxData[ParamEntry+3]  = DataBuffer[3];  
                break;                       
              default:
                //error todo 
                break;
              } 
          }else
          {  
              switch (DataLenght)
              {          
              case 1:           
                modbus->TxData[ParamEntry+1]  = DataBuffer[0];
                modbus->TxData[ParamEntry]    = 0;
                break;
              case 2:
                modbus->TxData[ParamEntry]    = DataBuffer[1];
                modbus->TxData[ParamEntry+1]  = DataBuffer[0];
                break;  
              case 4:
                modbus->TxData[ParamEntry]    = DataBuffer[1];      
                modbus->TxData[ParamEntry+1]  = DataBuffer[0];
                
                modbus->TxData[ParamEntry+2]  = DataBuffer[3];
                modbus->TxData[ParamEntry+3]  = DataBuffer[2];  
                break;             
              default:
                //error todo 
                break;
              }             
          }
        }
        
      }
 
    
    if(ReadState > 0)
    {
        break;
    }
    else
    {  
      if(DataLenght == 1)
      {
        DataLenght = 2;
      }
      
      ReadByteNum = ReadByteNum -  DataLenght;
      ParamEntry = ParamEntry + DataLenght ; 
      
      if(0 != ReadByteNum) // 只能在同一组内读连续或单个读
      {
        ModBusIndex = ModBusIndex + 1;
      }          
    }
  }
  
  
  if(ReadState > 0)
  {
    modbus->TxData[0] = modbus->RxData[0];
    modbus->TxData[1] = modbus->RxData[1] | 0x80;
    modbus->TxData[2] = ReadState;
    
    // CRC 
    CRC_temp = ModbusCrcCalc((UINT8*)&modbus->TxData[0], 3); 
    
    modbus->TxData[3] =  (CRC_temp & 0x00ff);
    modbus->TxData[4]  = (CRC_temp >> 8);  
    SciSendEnable( &(modbus->TxData[0]),5);  
    modbus->TxState = 1;        
  }
  else
  {
    modbus->TxData[0] = modbus->RxData[0];
    modbus->TxData[1] = modbus->RxData[1];    
    modbus->TxData[2] = TotalByteNum;
    
    // CRC    
    CRC_temp = ModbusCrcCalc((UINT8*)&modbus->TxData[0],modbus->TxData[2] + 3);   
    
    modbus->TxData[ParamEntry]  = (CRC_temp & 0x00ff);   
    modbus->TxData[ParamEntry+1] =  (CRC_temp >> 8);
    SciSendEnable( &(modbus->TxData[0]),modbus->TxData[2]+5);
    modbus->TxState = 1; 
  }

}


/****************************************************************************************************
 * DESCRIPTION: Modbus Err Check
 *
 * RETURNS:
 *
****************************************************************************************************/
void ModbusWriteOne (MODBUS_ST *modbus,UINT8 SlaveID)
{
  UINT16  CRC_temp = 0, ParamEntry= 0;  
  UINT16  ModBusIndex = 0;
  UINT16  Index = 0;
  UINT8   Subindex = 0;
  UINT8   ReadState = 0;
  UINT16  WriteRegNum  = 0;
  UINT16  WriteByteNum = 0;
  UINT32  DataLenght = 0;
  REG_ACC_T  AccessPrm;
  
  UINT8  DataBuffer[4] = {0};
  
  if(modbus->P.Endian)
  {
      ModBusIndex = ((UINT16)modbus->RxData[INEDXL]<<8) | modbus->RxData[INEDXH];
  }
  else
  {
     ModBusIndex = ((UINT16)modbus->RxData[INEDXH]<<8) | modbus->RxData[INEDXL];
  }
  WriteRegNum = 1;
  
   WriteByteNum = WriteRegNum *2; // 按寄存器数量读取的字节数
//  WriteByteNum = 2;                 // 按字节数量读取

  ParamEntry = 4;

  IndexResolve(ModBusIndex, &Index, &Subindex);   // 获取起始地址
  
  AXIS_HANDLE *AxisA;
  
  AxisA = (AXIS_HANDLE*)GetAxisHandle(0);
  
  if(((0x0F00 == (ModBusIndex & 0xFF00))||(0x6F00 == (ModBusIndex & 0xFF00))) 
              &&((AxisA[0].Prm->PnCtrlCfgPrm.CtrlSource != BUSTpyeCtrl)))
  {

      ReadState = MODBUS_PRM_ERROR;
  }
  else
  {    
     ReadState =  PrmModbusObjLenGet(ModBusIndex,&DataLenght,&AccessPrm);
     
     if(0 == ReadState)
     {
       if(DataLenght != WriteByteNum && DataLenght != 1)
       {
         ReadState = MODBUS_PRM_ERROR; // 写入的数据长度错误
       }
       else
       {
          if(modbus->P.Endian)
          {
              DataBuffer[0] = modbus->RxData[ParamEntry];
              DataBuffer[1] = modbus->RxData[ParamEntry+1];
           }
          else  
          {
              DataBuffer[0] = modbus->RxData[ParamEntry+1];
              DataBuffer[1] = modbus->RxData[ParamEntry];
          }
          ReadState = PrmModbusObjWrite((UINT16*)&(DataBuffer[0]),DataLenght,&AccessPrm);
       }

    }
  }
  
  if(ReadState > 0)
  {
    modbus->TxData[0] = modbus->RxData[0];
    modbus->TxData[1] = modbus->RxData[1] | 0x80;
    modbus->TxData[2] = ReadState;
    
    // CRC 
    CRC_temp = ModbusCrcCalc((UINT8*)&modbus->TxData[0], 3); 
    modbus->TxData[3]  = (CRC_temp & 0x00ff); 
    modbus->TxData[4] =  (CRC_temp >> 8);

    SciSendEnable( &(modbus->TxData[0]),5);  
    modbus->TxState = 1;    
  }
  else
  {
    modbus->TxData[0] = modbus->RxData[0];
    modbus->TxData[1] = modbus->RxData[1];    
    modbus->TxData[2] = modbus->RxData[2];
    modbus->TxData[3] = modbus->RxData[3];
    modbus->TxData[4] = modbus->RxData[4];
    modbus->TxData[5] = modbus->RxData[5];   
    // CRC    
    CRC_temp = ModbusCrcCalc((UINT8*)&modbus->TxData[0],6);   
    modbus->TxData[6]  =(CRC_temp & 0x00ff);   
    modbus->TxData[7] = (CRC_temp >> 8);
    
    
   SciSendEnable( &(modbus->TxData[0]),8); 
   modbus->TxState = 1;
  }  
  
}

/****************************************************************************************************
 * DESCRIPTION: Modbus Err Check
 *
 * RETURNS:
 *
****************************************************************************************************/
void ModbusWrite (MODBUS_ST *modbus,UINT8 SlaveID)
{
  UINT16  CRC_temp = 0, ParamEntry= 0;  
  UINT16  ModBusIndex = 0;
  UINT16  Index = 0;
  UINT8   Subindex = 0;
  UINT8   ReadState = 0;
  UINT16  WriteRegNum  = 0;
  UINT16  WriteByteNum = 0;
  UINT32  DataLenght = 0;
  REG_ACC_T  AccessPrm;
  
  UINT8  DataBuffer[4] = {0};
  
  if(modbus->P.Endian)
  {
    ModBusIndex = ((UINT16)modbus->RxData[INEDXL]<<8) | modbus->RxData[INEDXH];
    WriteRegNum = ((UINT16)modbus->RxData[DATALL]<<8) | modbus->RxData[DATALH];    
  }
  else
  {
    ModBusIndex = ((UINT16)modbus->RxData[INEDXH]<<8) | modbus->RxData[INEDXL];
    WriteRegNum = ((UINT16)modbus->RxData[DATALH]<<8) | modbus->RxData[DATALL];
  }
  
   WriteByteNum = WriteRegNum *2; // 按寄存器数量读取的字节数
//  WriteByteNum = WriteRegNum;       // 按字节数量读取

  ParamEntry = 7;
  
//  IndexResolve(ModBusIndex, &Index, &Subindex);   // 获取起始地址
  
  while(WriteByteNum)
  {
    
    AXIS_HANDLE *AxisA;
    
    AxisA = (AXIS_HANDLE*)GetAxisHandle(0);
    
    if(((0x0F00 == (ModBusIndex & 0xFF00))||(0x6F00 == (ModBusIndex & 0xFF00))) 
                &&((AxisA[0].Prm->PnCtrlCfgPrm.CtrlSource != BUSTpyeCtrl)))
    {

        ReadState = MODBUS_PRM_ERROR;
    }
    else
    {    
       // 
       ReadState =  PrmModbusObjLenGet(ModBusIndex,&DataLenght,&AccessPrm);
       
       if(0 == ReadState)
       {
         if(DataLenght > WriteByteNum)
         {
           ReadState = MODBUS_PRM_ERROR; // 写入的数据长度不够
         }
         else
         {
           if(modbus->P.Endian)
           {
              switch (DataLenght)
              {            
              case 1:
                DataBuffer[0] = modbus->RxData[ParamEntry];
                DataBuffer[1] = 0;
                break;
              case 2:
                DataBuffer[0] = modbus->RxData[ParamEntry];
                DataBuffer[1] = modbus->RxData[ParamEntry+1];
                break;  
              case 4:
                DataBuffer[0] = modbus->RxData[ParamEntry];
                DataBuffer[1] = modbus->RxData[ParamEntry+1];
                
                DataBuffer[2] = modbus->RxData[ParamEntry+2];
                DataBuffer[3] = modbus->RxData[ParamEntry+3];              
                break;   
                
              default:
                //error todo 
                break;
            }
           }else
           {  
              switch (DataLenght)
              {
           
              case 1:
                DataBuffer[0] = modbus->RxData[ParamEntry+1];
                DataBuffer[1] = 0;
                break;
              case 2:
                DataBuffer[0] = modbus->RxData[ParamEntry+1];
                DataBuffer[1] = modbus->RxData[ParamEntry];
                break;  
              case 4:
                DataBuffer[0] = modbus->RxData[ParamEntry+1];
                DataBuffer[1] = modbus->RxData[ParamEntry];
                
                DataBuffer[2] = modbus->RxData[ParamEntry+3];
                DataBuffer[3] = modbus->RxData[ParamEntry+2];              
                break;                 
              default:
                //error todo 
                break;                
              }
           }
           
           ReadState = PrmModbusObjWrite((UINT16*)&(DataBuffer[0]),DataLenght,&AccessPrm);
         }
       }
    }  
    
    
    if(ReadState > 0)
    {
        break;
    }
    else
    {  
      if(DataLenght == 1)
      {
        DataLenght = 2;
      }      
      
      ParamEntry   =   ParamEntry + DataLenght;  
   
      WriteByteNum = WriteByteNum - DataLenght;
      
      if(0 != WriteByteNum) // 只能在同一组内读连续或单个读，同一组内部连续的无法读取
      {
        ModBusIndex = ModBusIndex + 1;
      }      
    }
  }  
  
  if(ReadState > 0)
  {
    modbus->TxData[0] = modbus->RxData[0];
    modbus->TxData[1] = modbus->RxData[1] | 0x80;
    modbus->TxData[2] = ReadState;
    
    // CRC 
    CRC_temp = ModbusCrcCalc((UINT8*)&modbus->TxData[0], 3); 
    
    modbus->TxData[3]  = (CRC_temp & 0x00ff);   
    modbus->TxData[4] =  (CRC_temp >> 8);
   
    
    SciSendEnable(&(modbus->TxData[0]),5); 
    modbus->TxState = 1;
  }
  else
  {
    modbus->TxData[0] = modbus->RxData[0];
    modbus->TxData[1] = modbus->RxData[1];    
    modbus->TxData[2] = modbus->RxData[2];
    modbus->TxData[3] = modbus->RxData[3];
    modbus->TxData[4] = modbus->RxData[4];
    modbus->TxData[5] = modbus->RxData[5];   
    // CRC    
    CRC_temp = ModbusCrcCalc((UINT8*)&modbus->TxData[0],6); 
    
    modbus->TxData[6]  = (CRC_temp & 0x00ff);  
    modbus->TxData[7]  = (CRC_temp >> 8);
    
    SciSendEnable( &(modbus->TxData[0]),8);  
    modbus->TxState = 1;
  }
  
}
/****************************************************************************************************
 * DESCRIPTION: Modbus Err Check
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE BOOL ModbusFrameValid(MODBUS_ST *modbus,UINT8 SlaveID)
{
  BOOL ret = 0;
  UINT16 FrameLength = 0;
  if((modbus->RxData[0] != SlaveID) && (modbus->RxData[0] == 0))  // 从站Id不匹配和广播帧
  {
     // 不响应
     ret = 1;
  }
  
  if(modbus->RxData[1] != 0x10)
  {
    if(modbus->RxLength != 8)
    {
      // 不响应
      ret = 1;
    }
  }
  else 
  {
    if(modbus->RxData[6] != 0)
    {
      FrameLength =9 +modbus->RxData[6];
      if(FrameLength != modbus->RxLength)
      {
        ret = 1;
      }
    }
  }
  
  return ret;
}
/****************************************************************************************************
 * DESCRIPTION: Modbus 接收发送处理函数
 *
 * RETURNS:
 *
****************************************************************************************************/
#pragma optimize=none  
BOOL ModBusTransmit(MODBUS_ST *modbus, UINT8 SlaveID)
{
  UINT16  RxLength  = 0;
  UINT16  CRC_temp  = 0;
  UINT16  CRC_Value = 0;
  
  if(1 != modbus->RxEndFlag)  // 帧结束标志位
  {
     return 0;
  }
  
  if(0 != ModbusFrameValid(modbus,SlaveID))
  {
    modbus->TxState = 2; // 无需响应--重新进入Idel状态开启接收
    modbus->RxEndFlag = 0;
    return 0;
  }
  
  
  RxLength = modbus->RxLength;

//  temp = (modbus->RxData[RxLength-1]) << 8;
  
  CRC_Value = ((UINT16)modbus->RxData[RxLength-2]) + ((UINT16)modbus->RxData[RxLength-1] << 8);
  
  CRC_temp = ModbusCrcCalc((UINT8*)&(modbus->RxData[0]), RxLength - 2);
  
  if(CRC_Value != CRC_temp)
  {
    modbus->ErrBit.CrcChkErr = 1;
    
    modbus->TxData[0] = modbus->RxData[0];
    modbus->TxData[1] = modbus->RxData[1] | 0x80;
    modbus->TxData[2] = MODBUS_CRC_ERROR;  
    
    // CRC 
    CRC_temp = ModbusCrcCalc((UINT8*)&modbus->TxData[0], 3); 
    
    modbus->TxData[3]  = (CRC_temp & 0x00ff);   
    modbus->TxData[4] =  (CRC_temp >> 8);
    
    
    SciSendEnable(&(modbus->TxData[0]),5);   
    modbus->TxState = 1;
    modbus->RxEndFlag = 0;
    return 0;
  }
  
  switch (modbus->RxData[1])
  {
  case 0x03:
    ModbusRead(modbus,SlaveID);
    
    break;

  case 0x06:
    ModbusWriteOne(modbus,SlaveID);
    break; 
   
  case 0x10:
    ModbusWrite(modbus,SlaveID);

    break;   
    
//  case 0x2B:  // read 软硬件版本号  todo
//
//    break; 
    
  default:
    // cmd eeror
    modbus->TxData[0] = modbus->RxData[0];
    modbus->TxData[1] = modbus->RxData[1] | 0x80;
    modbus->TxData[2] = 0x1;   //
    
    // CRC 
    CRC_temp = ModbusCrcCalc((UINT8*)&modbus->TxData[0], 3); 
    
    modbus->TxData[3] =  (CRC_temp & 0x00ff);
    modbus->TxData[4]  = (CRC_temp >> 8);   
    
    
    SciSendEnable(&(modbus->TxData[0]),5);   
    modbus->TxState = 1;    
    break;

  } 
  modbus->RxEndFlag = 0;
  return  0;
}