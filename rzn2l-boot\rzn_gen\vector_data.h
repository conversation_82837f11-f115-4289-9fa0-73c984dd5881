/* generated vector header file - do not edit */
        #ifndef VECTOR_DATA_H
        #define VECTOR_DATA_H
        #include "bsp_api.h"
                /* Number of interrupts allocated */
        #ifndef VECTOR_DATA_IRQ_COUNT
        #define VECTOR_DATA_IRQ_COUNT    (3)
        #endif
        /* ISR prototypes */
        void usbfs_interrupt_handler(void);
        void r_usb_dmaca_intDMAC0I_isr(void);
        void r_usb_dmaca_intDMAC1I_isr(void);

        /* Vector table allocations */
        #define VECTOR_NUMBER_USB_FI ((IRQn_Type) 285) /* USB_FI (USB (Function) interrupt) */
        #define VECTOR_NUMBER_USB_FDMA0 ((IRQn_Type) 286) /* USB_FDMA0 (USB (Function) DMA 0 transmit completion) */
        #define VECTOR_NUMBER_USB_FDMA1 ((IRQn_Type) 287) /* USB_FDMA1 (USB (Function) DMA 1 transmit completion) */
        typedef enum IRQn {
            SoftwareGeneratedInt0 = -32,
            SoftwareGeneratedInt1 = -31,
            SoftwareGeneratedInt2 = -30,
            SoftwareGeneratedInt3 = -29,
            SoftwareGeneratedInt4 = -28,
            SoftwareGeneratedInt5 = -27,
            SoftwareGeneratedInt6 = -26,
            SoftwareGeneratedInt7 = -25,
            SoftwareGeneratedInt8 = -24,
            SoftwareGeneratedInt9 = -23,
            SoftwareGeneratedInt10 = -22,
            SoftwareGeneratedInt11 = -21,
            SoftwareGeneratedInt12 = -20,
            SoftwareGeneratedInt13 = -19,
            SoftwareGeneratedInt14 = -18,
            SoftwareGeneratedInt15 = -17,
            DebugCommunicationsChannelInt = -10,
            PerformanceMonitorCounterOverflowInt = -9,
            CrossTriggerInterfaceInt = -8,
            VritualCPUInterfaceMaintenanceInt = -7,
            HypervisorTimerInt = -6,
            VirtualTimerInt = -5,
            NonSecurePhysicalTimerInt = -2,
            USB_FI_IRQn = 285, /* USB_FI (USB (Function) interrupt) */
            USB_FDMA0_IRQn = 286, /* USB_FDMA0 (USB (Function) DMA 0 transmit completion) */
            USB_FDMA1_IRQn = 287, /* USB_FDMA1 (USB (Function) DMA 1 transmit completion) */
            SHARED_PERIPHERAL_INTERRUPTS_MAX_ENTRIES = BSP_VECTOR_TABLE_MAX_ENTRIES
        } IRQn_Type;
        #endif /* VECTOR_DATA_H */