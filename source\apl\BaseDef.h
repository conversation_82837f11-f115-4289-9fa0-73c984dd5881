/****************************************************************************************************
 *
 * FILE NAME:  BaseDef.h
 *
 * DESCRIPTION:  1.Basic Data Type Definition
 *              2.Basic Constant Definition
 *              3.Basic Macro Definition
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _BASE_DEF_H
#define	_BASE_DEF_H

#include <stdint.h>

#ifndef _ECAT_COM
  #define _ECAT_COM 0
#endif

#ifndef _CAN_COM
  #define _CAN_COM 0
#endif

#if (( _ECAT_COM == 1) || (_CAN_COM == 1))
    #define  _ECAT_CAN_COM_  1
#else
    #define  _ECAT_CAN_COM_  0 
#endif


#ifndef gUSE
    #define  gUSE       0
#endif

#ifndef PRIVATE
    #define PRIVATE static
#endif

#ifndef PUBLIC
    #define PUBLIC
#endif
    
#ifndef NULL
	#define NULL          0
#endif

/**
FALSE: Will be used for variables from type BOOL */
#ifndef FALSE
#define FALSE                                     0
#endif

/**
TRUE: Will be used for variables from type BOOL  */
#ifndef TRUE
#define TRUE                                      1
#endif

/**
BOOL: Should be adapted to the boolean type of the microcontroller */
#ifndef BOOL
#define BOOL                                      uint8_t
#endif

/**
UINT8: Should be adapted to the unsigned8 type of the microcontroller  */
#ifndef UINT8
#define UINT8                                     uint8_t
#endif

/**
UINT16: Should be adapted to the unsigned16 type of the microcontroller  */
#ifndef UINT16
#define UINT16                                    uint16_t
#endif

/**
UINT32: Should be adapted to the unsigned32 type of the microcontroller  */
#ifndef UINT32
#define UINT32                                    uint32_t
#endif

/**
UINT64: Should be adapted to the unsigned64 type of the microcontroller  */
#ifndef UINT64
#define UINT64                                    uint64_t
#endif

/**
USHORT: Should be adapted to the unsigned16 type of the microcontroller */
#ifndef USHORT
#define USHORT                                    uint16_t
#endif

/**
INT8: Should be adapted to the integer8 type of the microcontroller */
#ifndef INT8
#define INT8                                      int8_t
#endif

/**
INT16: Should be adapted to the integer16 type of the microcontroller  */
#ifndef INT16
#define INT16                                     int16_t
#endif

/**
INT32: Should be adapted to the integer32 type of the microcontroller */
#ifndef INT32
#define INT32                                     int32_t
#endif

/**
INT64: Should be adapted to the integer64 type of the microcontroller */
#ifndef INT64
#define INT64                                     int64_t
#endif

/**
CHAR: Should be adapted to the character type of the microcontroller */
#ifndef CHAR
#define CHAR                                      int8_t
#endif

/**
UCHAR: Should be adapted to the unsigned character type of the microcontroller */
#ifndef UCHAR
#define UCHAR                                     uint8_t
#endif

/**
//BYTE: Should be adapted to the character type of the microcontroller */
#ifndef BYTE
#define BYTE                                      uint8_t
#endif

/**
WORD: Should be adapted to the WORDacter type of the microcontroller */
#ifndef WORD
#define WORD                                      uint16_t
#endif

/**
DWORD: Should be adapted to the character type of the microcontroller */
#ifndef DWORD
#define DWORD                                     uint32_t
#endif

/**
REAL32: Should be adapted to the character type of the microcontroller */
#ifndef	REAL32
#define	REAL32                                     float
#endif

//__INLINE: Should be adapted to the character type of the microcontroller */
//#ifndef   __INLINE
//  #define __INLINE                               __inline
//#endif

#define		OK				 0
#define		NG				 -1							

/*--------------------------------------------------------------------------------------------------*/
/*		Double Byte Data Type Definition															*/
/*--------------------------------------------------------------------------------------------------*/
typedef union DBYTEX
{
    UINT16	w;										/* Word											*/
    struct { 
		UINT8 l; 
		UINT8 h;
	} b;											/* Low Byte / High Byte							*/
} DBYTEX;

/*--------------------------------------------------------------------------------------------------*/
/*		Double Word Data Type Definition															*/
/*--------------------------------------------------------------------------------------------------*/
typedef	union DWORDX
{
	UINT32	dw;										/* Double Word									*/
	INT32	Long;									/* signed   long								*/
	UINT32	Ulong;									/* unsigned long								*/
	struct {
		UINT16 l; 
		UINT16 h;
	} w;											/* Low Word / High Word							*/
    struct {
		UINT8 ll; 
		UINT8 lh; 
		UINT8 hl; 
		UINT8 hh;
	} b;											/* Low Byte / High Byte							*/
} DWORDX;

/*--------------------------------------------------------------------------------------------------*/
/*		Unsigned Double Word Data Type Definition													*/
/*--------------------------------------------------------------------------------------------------*/
typedef	union UDWORD
{
	UINT32	w;										/* Double Word									*/
	struct { 
		USHORT l; 
		USHORT h;
	} hw;											/* Low Word / High Word							*/
    struct { 
		UCHAR l; 
		UCHAR h;
	} b;											/* Low Byte / High Byte							*/
} UDWORD;

/*--------------------------------------------------------------------------------------------------*/
/*		INT64	 64bit Integer									*/
/*--------------------------------------------------------------------------------------------------*/
typedef union {									/* Double Word Register								*/
		INT64	dl;								/* 64bit Register									*/
		INT32	l[2];							/* Long Register									*/
		UINT32	ul[2];							/* Long Register									*/
		INT16	s[4];							/* Short Register									*/
		UINT16	us[4];							/* Short Register									*/
} DLREG;


typedef union {									/* Double Word Register								*/
		INT32	l;								/* Long Register									*/
		UINT32	ul;								/* Long Register									*/
		INT16	s[2];							/* Short Register									*/
		UINT16	us[2];							/* Short Register									*/
} DWREG;


/*--------------------------------------------------------------------------------------------------*/
/*		Bit Data Definition																            */
/*--------------------------------------------------------------------------------------------------*/
//#define	BIT0	0x0001
//#define	BIT1	0x0002
//#define	BIT2	0x0004
//#define	BIT3	0x0008
//#define	BIT4	0x0010
//#define	BIT5	0x0020
//#define	BIT6	0x0040
//#define	BIT7	0x0080
//#define	BIT8	0x0100
//#define	BIT9	0x0200
//#define	BIT10	0x0400
//#define	BIT11	0x0800
//#define	BIT12	0x1000
//#define	BIT13	0x2000
//#define	BIT14	0x4000
//#define	BIT15	0x8000

/*--------------------------------------------------------------------------------------------------*/
/*		Basic Constant Definition																    */
/*--------------------------------------------------------------------------------------------------*/
#define  ON                      1
#define  OFF                     0

#define  PI                      3.14159265f
#define  C2_PI                   (PI*2)
#define  C_FSqrt2   	         1.41421356f		        //Sqrt(2)
#define  C_FSqrt3   	         1.732050807568877f		    //Sqrt(3)
#define  C_FSqrt3Inv   	         (1.0f/C_FSqrt3)		    //1/Sqrt(3)
#define  C_FSqrt3D2              0.8660254037844386f		//Sqrt(3)/2
#define  C_F1D2  	             0.5f
#define  C_Tonmin                0


#define  C_SinUint 		         (32768L)
#define  C_SinUint1D8            (C_SinUint>>3)
#define  C_SinUint1D4            (C_SinUint>>2)
#define  C_SinUint1D2            (C_SinUint>>1)
#define  C_SinUint3D4            ((C_SinUint>>2)*3)
#define  C_2SinUint 		     (65536L)

#define  C2PAI                   6.2831853f
#define	 C10POW9			     1000000000				    // 10^9	
#define	 C10POW7			     10000000				    // 10^7		
#define	 C10POW5			     100000				        // 10^5	
#define	 C10POW3			     1000				        // 10^3
#define	 C2PAIE7			     62831853				    // (2*PAI) * 10^7


#define	 INTGRAL_NORMAXVAL   	 0x01000000
#define  NOR_MAXVAL_FLOAT        16777216.0f

#define	 NORMAXVALUE			 0x01000000				    // Normalized Maximum Value					
#define	 NORMINVALUE			 -0x01000000				// Normalized Minimum Value					


#define CUSTOM_QUCIK_DATA_PLACE_IN_SECTION    BSP_PLACE_IN_SECTION(".customdata.quick")


#include "BaseSetting.h"

#include "fundefine.h"

#endif	/* _BASE_DEF_H */

