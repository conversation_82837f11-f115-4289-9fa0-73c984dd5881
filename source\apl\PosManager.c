/****************************************************************************************************
 *
 * FILE NAME:  PosManager.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.05.28
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	28-05-2020 Version 1.00 : Created by Denny
****************************************************************************************************/

#include "PosManager.h"
#include "ModelControl.h"

#define MAFIL_BUFNUM	256							/* Number of moving average filter buffers     */

PRIVATE void posMngCtrlPosSignal( POS_MNG_HNDL *PosManager, POS_CTRL_STS *PosCtrlSts
									, PCMDFIL *pPcmdFil , INT32 PosErr );
PRIVATE void BpxPerClrSignalProcedure( BASE_LOOP *BaseLoops, BPRMDAT *Bprm );
PRIVATE void BpxEdgeClrSvcPositionManager( BASE_LOOP *BaseLoops ); 	

#if 1

/****************************************************************************************************
 * DESCRIPTION:
 *	 	Servo control Position control variable initialization
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PosMngInitPositionManager( BASE_CTRL *BaseControls )
{

//	MlibResetLongMemory( &(BaseControls->PosManager.var), sizeof(BaseControls->PosManager.var)/4 );
//
//	BaseControls->PosManager.Egear = &(BaseControls->PosManager.conf.Egear);
//
//	BaseControls->PosManager.dPosRefi = 0;				
//	BaseControls->PosManager.CompdPosRefi = 0;			
//	BaseControls->PosManager.CompdPcmda = 0;	
//	BaseControls->PosManager.var.dPcmda = 0;	

	if( BaseControls->CtrlCmdMngr.CtrlMode == BASE_MODE_POS )
	{
		BaseControls->PosCtrlSts.CoinSignal = TRUE;
		BaseControls->PosCtrlSts.NearSignal = TRUE;
	}
	else
	{
		BaseControls->PosCtrlSts.CoinSignal = FALSE;
		BaseControls->PosCtrlSts.NearSignal = FALSE;
	}

	PcmdFilInitialize( &(BaseControls->PcmdFil), &(BaseControls->VibSupFil) );

	// MfcInitModelControl( &(BaseControls->MFControl) );

	BaseControls->CtrlCmdMngr.SpdffFilo = 0;
//	BaseControls->CtrlCmdMngr.PicvClrCmd = FALSE;

	BaseControls->PosManager.conf.CoinOutSel = POSERR_REFIN;    // todo todo

}


/****************************************************************************************************
 * DESCRIPTION:
 *	 	Servo communication position command management
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PosMngClrPosCmd( BASE_LOOP *BaseLoops)
{
	BaseLoops->BaseCtrls->CtrlCmdMngr.dPosRef = 0;
	BaseLoops->BaseCtrls->CtrlCmdMngr.dPosRefFilo = 0;
	BaseLoops->BaseCtrls->CtrlCmdMngr.dPosRefo = BaseLoops->dPosFdb;
	BaseLoops->BaseCtrls->PosManager.var.dPcmda = BaseLoops->dPosFdb;
        if(BaseLoops->UseEncoder2)
        {
          BaseLoops->BaseCtrls->CtrlCmdMngr.PosRef     =  BaseLoops->BaseCtrls->Cia402Axis.Objects->PositionActualValue0x6064;
          BaseLoops->BaseCtrls->CtrlCmdMngr.PosRefFilo = BaseLoops->BaseCtrls->Cia402Axis.Objects->PositionActualValue0x6064; 
        }
        else
        {
        BaseLoops->BaseCtrls->CtrlCmdMngr.PosRef     =  Cia402_GearRatioCal(&BaseLoops->BaseCtrls->Cia402Axis,
                                                     BaseLoops->BaseCtrls->Cia402Axis.Objects->PositionActualValue0x6064);
        
	BaseLoops->BaseCtrls->CtrlCmdMngr.PosRefFilo = Cia402_GearRatioCal(&BaseLoops->BaseCtrls->Cia402Axis,
                                                             BaseLoops->BaseCtrls->Cia402Axis.Objects->PositionActualValue0x6064);   
        }
        BaseLoops->BaseCtrls->CtrlCmdMngr.LdStpPosRef =  BaseLoops->BaseCtrls->CtrlCmdMngr.PosRef ;  
        BaseLoops->BaseCtrls->CtrlCmdMngr.dPosRefFiloRem = 0;
	BaseLoops->PosLoop.Per64[0] = 0;
	BaseLoops->PosLoop.Per64[1] = 0;
	BaseLoops->PosLoop.Per64a[0] = 0;
	BaseLoops->PosLoop.Per64a[1] = 0;
	BaseLoops->PosLoop.PosErra = 0;
	// MfcInitModelControl( &(BaseLoops->BaseCtrls->MFControl) );
}

/****************************************************************************************************
 * DESCRIPTION:
 *	 	Servo communication position command management
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 PosMngNetPosCmdManager( BASE_LOOP *BaseLoops, BPRMDAT *Bprm )
{
	BASE_CTRL		*BaseControls;
	POS_MNG_HNDL	*PosManager;
	MFCTRL			*MFControl;
	BOOL			den;
	INT32			dPosRefo;
	INT32			rc;

	BaseControls = BaseLoops->BaseCtrls;	
	PosManager	= &(BaseControls->PosManager);
	MFControl	= &(BaseControls->MFControl);

/*--------------------------------------------------------------------------------------------------*/
/*	Position command input process (communication, PJOG, etc): PosManager->var.dPcmda [command unit/Scan]    */
/*--------------------------------------------------------------------------------------------------*/
	if( BaseControls->CtrlModeSet.CtrlMode.b.cm == CTRL_MODE_PJOG )
	{ /* In program JOG mode */
//		BaseControls->CtrlCmdMngr.PicvClrCmd = FALSE;			/* Position control integration clear command reset*/
		BaseControls->PosCtrlSts.DenSignal  = FALSE;			/* Den Signal							*/

		if( BaseControls->CtrlModeSet.CtrlModeLst.b.cm != CTRL_MODE_PJOG )
		{												
			PrgJogReset( &(BaseControls->PJogHdl) );	
		}

		if( BaseControls->BaseEnable == TRUE )
		{ /* Position control execution */

			/* Position command creation processing [command unit/Scan] */
			rc = PrgJogMakePositionReference( &BaseControls->PJogHdl, &dPosRefo );

			if( rc == TRUE )
			{/* Program JOG operation end */
				BaseControls->PosCtrlSts.DenSignal  = TRUE;
			}
			else
			{/* During program JOG calculation */
				BaseControls->PosCtrlSts.DenSignal  = FALSE;
				BaseControls->PosCtrlSts.RefZSignal = FALSE;
			}
			BaseControls->CtrlCmdMngr.PerrClrReq = FALSE;
		}
		else
		{ /* End of position control */
			PosMngInitPositionManager( BaseControls );
			BaseControls->CtrlCmdMngr.PerrClrReq = TRUE;
			return( 0 );							
		}

		BaseControls->CtrlCmdMngr.dPosRef = dPosRefo;
		PosManager->var.dPcmda = BaseControls->CtrlCmdMngr.dPosRef;	
		BaseControls->CtrlCmdMngr.PosRef = BaseControls->CtrlCmdMngr.PosRef + BaseControls->CtrlCmdMngr.dPosRef;
	}
	else
	{ /* During normal position control */

		BaseControls->CtrlCmdMngr.dPosRef = BaseControls->CtrlCmdMngr.PosRef - BaseControls->CtrlCmdMngr.PosRef_l;

                        
        if(BaseControls->CtrlCmdMngr.dPosRef != 0)
        {
          BaseControls->CtrlCmdMngr.LatchdPosRef = BaseControls->CtrlCmdMngr.dPosRef;
        }
	
		PosManager->var.dPcmda = BaseControls->CtrlCmdMngr.dPosRef;			

#if 0

		if( ((SvControlIf->CmdCtrl.CmdCtrlBit) >> CLRPOSERR_BITNO) & 1 )
		{
			PosMngInitPositionManager( BaseControls );	
//			BaseControls->CtrlCmdMngr.PerClrEdge = FALSE;	
			BaseControls->CtrlCmdMngr.PerrClrReq = TRUE;	
			return( 0 );								
		}
#endif


		BaseControls->CtrlCmdMngr.PerrClrReq = FALSE;

#if 0
	/*----------------------------------------------------------------------------------------------*/
	/*	Position deviation edge clear processing													*/
	/*----------------------------------------------------------------------------------------------*/
		if( BaseControls->PerClrEdge )
		{
			BpxPerClrSignalProcedure( BaseLoops, Bprm ); 
			BaseControls->CtrlCmdMngr.PerClrEdge = TRUE;
			dPosRefo = 0;
		}
		else
		{
			BaseControls->CtrlCmdMngr.PerClrEdge = FALSE; 
		}
#endif
		
	}

/*--------------------------------------------------------------------------------------------------*/
/*	Position command filter calculation (exponential acceleration/deceleration, moving average):   */
/*                       BaseControls->CtrlCmdMngr.dPcmdFilo [Pulse/Scan] */
/*--------------------------------------------------------------------------------------------------*/
	/* Payout completion signal processing (payment incomplete module sets to FALSE)*/
	PcmdFilSetRefZStatus( &(BaseControls->PcmdFil), TRUE );
	/* Position command filtering */
	BaseControls->CtrlCmdMngr.dPosRefFilo = PcmdFilRuntimeService( &(BaseControls->PcmdFil),
                                                                               BaseControls->CtrlCmdMngr.dPosRef,
                                                                               BaseLoops->PosLoop.PosErr,
                                                                               PosManager->var.PcmdFilStop );
        

        
          BaseControls->CtrlCmdMngr.dPosRefFilo1 = MlibLpfilter12(BaseControls->CtrlCmdMngr.dPosRefFilo,
                                                                BaseControls->CtrlCmdPrm.PosRefKlpf,
                                                                BaseControls->CtrlCmdMngr.dPosRefFilo,	
                                                                (&BaseControls->CtrlCmdMngr.dPosRefFiloRem));
          

          
	/* Acquisition of payout completion status */
	BaseControls->PosCtrlSts.DenSignal = PcmdFilGetRefZStatus( &(BaseControls->PcmdFil) );
	BaseControls->PosCtrlSts.RefZSignal = BaseControls->PosCtrlSts.DenSignal;

	/*----------------------------------------------------------------------------------------------*/
	/*		Vibration suppression filtering															*/
	/*----------------------------------------------------------------------------------------------*/
	/*Vibration suppression filter forced invalidation processing (invalid except for position control)*/
	if( BaseControls->CtrlCmdMngr.LastCtrlMode != BASE_MODE_POS  )
	{
		BaseControls->VibSupFil.conf.Pexe.enable = 0;
	}
	/* Vibration suppression filter calculation */
	// BaseControls->CtrlCmdMngr.dPosRefFilo = PcmdFilVibSupFilter(
	// 										&(BaseControls->VibSupFil),
	// 										BaseControls->CtrlCmdMngr.dPosRefFilo,		
	// 										&(BaseControls->PosCtrlSts.RefZSignal) );

	

	/*----------------------------------------------------------------------------------------------*/
	/*		MFC processing																			*/
	/*----------------------------------------------------------------------------------------------*/
	/* MFC forced invalidation processing (invalid except for position control)*/
	if( BaseControls->CtrlCmdMngr.LastCtrlMode != BASE_MODE_POS )
	{ 
		MFControl->conf.MfcPexe.MFCModel = 0;
	}
	/* MFC operation */
	// BaseControls->CtrlCmdMngr.dPosRefo = MfcControlMain( &(BaseControls->MFControl),
	// 						   							BaseControls->CtrlCmdMngr.dPosRefFilo1,
	// 						   							&(BaseControls->CtrlCmdMngr.SpdFFC),
	// 						   							&(BaseControls->CtrlCmdMngr.TrqFFC) );

	BaseControls->CtrlCmdMngr.dPosRefo = BaseControls->CtrlCmdMngr.dPosRefFilo1;
	BaseControls->CtrlCmdMngr.PosRefFilo += BaseControls->CtrlCmdMngr.dPosRefo;

/*--------------------------------------------------------------------------------------------------*/
/*		Control position signal processing															*/
/*--------------------------------------------------------------------------------------------------*/
	/* Make Coin and Near Status */
	posMngCtrlPosSignal( PosManager, &(BaseControls->PosCtrlSts),
	                     &(BaseControls->PcmdFil) , BaseLoops->PosLoop.PosErra);

	return (BaseControls->CtrlCmdMngr.dPosRefo);
}

#endif

#if 0

/****************************************************************************************************/
/*																									*/
/*		Absolute position recalculation when SEN is ON													*/
/*																									*/
/****************************************************************************************************/
void	PosMngSenOnReCalcAbsoScalePos(MENCV *MencV, APOSRG *EncPos, EGEAR *Egear, BOOL RvsDir)
{
	INT32	IniPos[2];
	UCHAR	Sftx;

	Sftx	= FSX_FPOS_SFT;

	if( MencV->AbsoEncType == MENC_INCRE_TYPE )
	{
		MlibResetLongMemory(EncPos, sizeof(APOSRG)/4);
	}
	else
	{
		IniPos[1] = (INT32)((INT16)MencV->RxPosH[0] >> Sftx);	
		IniPos[0] = (INT32)( ((UINT32)MencV->RxPosH[0] << (32-Sftx))
						+ ((UINT32)MencV->RxPosL[0].dw >> Sftx) );	

		if( MencV->RevAsmMotor ^ RvsDir )
		{
			IniPos[1] = (~IniPos[1]);		
			IniPos[0] = (~IniPos[0]) + 1;
			if( IniPos[0] == 0 )
			{	
				++IniPos[1];
			}
		}

		MlibAposRg64iv( IniPos[0], IniPos[1], Egear, EncPos );
	}
}

/****************************************************************************************************/
/*																									*/
/*		Absolute position recalculation when SEN is ON														*/
/*																									*/
/****************************************************************************************************/
void	PosMngSenOnReCalcAbsoPos( MENCV *MencV, APOSRG *EncPos,
								  EGEAR *Egear, DBYTEX bit_dp, UINT16 limmlt, BOOL RvsDir )
{
	SencMakeAbsPosition(MencV, limmlt, bit_dp, RvsDir);
	MlibAposRg64iv( MencV->SenOnScalePosL, MencV->SenOnScalePosH, Egear, EncPos );
}

#endif

#if 0

/****************************************************************************************************
 * DESCRIPTION:
 *	 	Position FB latch processing 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PosMngLatchNetFbkPos( BASE_CTRL *BaseControls, SERVO_CONTROL_IF *SvControlIf, BPRMDAT *Bprm )
{
	POS_MNG_HNDL	*PosManager;
	INT32			RspLtBit = 0;
	INT32			dPosCmp;
	INT32			motLpos, motPos;																
	INT32			posSftR;																	
	INT32			rvsDir; 																	

	PosManager = &(BaseControls->PosManager);
	motPos = BaseControls->MencV->MotPosX[0];													
	rvsDir = 1; 																				

	if(Bprm->FencUse != FALSE)
	{
		posSftR = BaseControls->FencV->MposSftR;
	}
	else
	{
		posSftR = BaseControls->MencV->MposSftR;
		if(Bprm->RvsDir)
		{
			rvsDir = -1;
		}
	}

/*--------------------------------------------------------------------------------------------------*/
/*		C phase latch processing																				*/
/*--------------------------------------------------------------------------------------------------*/
	if( SvControlIf->LtReqCphs != FALSE )
	{ /* Latch required */
		if((Bprm->FencUse != FALSE)&&(BaseControls->MotSts.FencCphPass != FALSE)
			||(Bprm->FencUse == FALSE)&&(BaseControls->MotSts.MotCphPass != FALSE))
		{/* Latch complete */
			dPosCmp = BaseControls->CtrlCmdMngr.FbkPosFromCpos;

			/* Absolute position update calculation */
			MlibAposRg64dp( -dPosCmp,
							&PosManager->conf.Egear,
							&PosManager->PgPos,
							SvControlIf->MonCphaLpos );

			SvControlIf->LtReqCphs = FALSE;
			RspLtBit |= ((SvControlIf->LtReqCphs^1) * C_PHASE_COMP_BIT);
		}
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Ext1 latch processing 																			*/
/*--------------------------------------------------------------------------------------------------*/
	if( SvControlIf->LtReqExt1 != FALSE )
	{ /* Latch required */
		if((SvControlIf->ExtLtSts[0]).Complete != FALSE)
		{/* Latch complete  */
			motLpos = (INT32)SvControlIf->ExtLtSts[0].LatchPosition << posSftR;	
			dPosCmp = rvsDir * ((motPos - motLpos) >> posSftR); 			
			/* Absolute position update calculation */
			MlibAposRg64dp( -dPosCmp,
							&PosManager->conf.Egear,
							&PosManager->PgPos,
							SvControlIf->MonExt1Lpos);

			SvControlIf->LtReqExt1 = FALSE;
			RspLtBit |= ((SvControlIf->LtReqExt1^1) * EXT_SIG1_COMP_BIT);
		}
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Ext2 latch processing 																			*/
/*--------------------------------------------------------------------------------------------------*/
	if( SvControlIf->LtReqExt2 != FALSE )
	{ /* Latch required */
		if((SvControlIf->ExtLtSts[1]).Complete != FALSE)
		{/* Latch complete  */
			motLpos = (INT32)SvControlIf->ExtLtSts[1].LatchPosition << posSftR;	
			dPosCmp = rvsDir * ((motPos - motLpos) >> posSftR); 			
			/* Absolute position update calculation */
			MlibAposRg64dp( -dPosCmp,
							&PosManager->conf.Egear,
							&PosManager->PgPos,
							SvControlIf->MonExt2Lpos);

			SvControlIf->LtReqExt2 = FALSE;
			RspLtBit |= ((SvControlIf->LtReqExt2^1) * EXT_SIG2_COMP_BIT);
		}
	}
/*--------------------------------------------------------------------------------------------------*/
/*		Ext3 latch processing 																			*/
/*--------------------------------------------------------------------------------------------------*/
	if( SvControlIf->LtReqExt3 != FALSE )
	{ /* Latch required */
		if(SvControlIf->ExtLtSts[2].Complete != FALSE)
		{/* Latch complete */
			motLpos = (INT32)SvControlIf->ExtLtSts[2].LatchPosition << posSftR;	
			dPosCmp = rvsDir * ((motPos - motLpos) >> posSftR); 			
			/* Absolute position update calculation */
			MlibAposRg64dp( -dPosCmp,
							&PosManager->conf.Egear,
							&PosManager->PgPos,
							SvControlIf->MonExt3Lpos);

			SvControlIf->LtReqExt3 = FALSE;
			RspLtBit |= ((SvControlIf->LtReqExt3^1) * EXT_SIG3_COMP_BIT);
		}
	}

	/* Latch request level setting */
	SvControlIf->RspLtBit = RspLtBit | (SvControlIf->CmdLtBit & 0xF);
}
#endif

#if 1
/****************************************************************************************************
 * DESCRIPTION:
 *	 	Control position signal processing
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void posMngCtrlPosSignal( POS_MNG_HNDL *PosManager, POS_CTRL_STS *PosCtrlSts,
									PCMDFIL *pPcmdFil, INT32 PosErr )
{
	INT32	abs_poserr;

	abs_poserr = MlibABS(PosErr);

/*--------------------------------------------------------------------------------------------------*/
/*		Positioning completion signal creation																		*/
/*--------------------------------------------------------------------------------------------------*/
	if( abs_poserr <= PosManager->conf.CoinLevel )
	{
		switch( PosManager->conf.CoinOutSel )
		{
		case POSERR:		/*Position deviation / COIN output*/
			PosCtrlSts->CoinSignal = TRUE;
			break;

		case POSERR_REFOUT: /* Position deviation and position command filter output / COIN output*/
			PosCtrlSts->CoinSignal = PcmdFilGetRefZStatus( pPcmdFil );	/* <S081> */
			break;

		case POSERR_REFIN:	/* Position deviation and position command input / COIN output*/
			PosCtrlSts->CoinSignal = (PosManager->var.dPcmda == 0) ? TRUE : FALSE;
			break;

		default:
			break;
		}
	}
	else
	{
		PosCtrlSts->CoinSignal = FALSE;
	}

/*--------------------------------------------------------------------------------------------------*/
/*		Positioning near signal creation															*/
/*--------------------------------------------------------------------------------------------------*/
	if( abs_poserr <= PosManager->conf.NearLevel )
	{
		PosCtrlSts->NearSignal = TRUE;
	}
	else
	{
		PosCtrlSts->NearSignal = FALSE;
	}
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Electronic gear function enable/disable switching process
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BpiEgearFuncSwitch( POS_MNG_HNDL *PosManager, BOOL Switch )
{
	if( Switch == TRUE )
	{ /* When requesting activation of electronic gear function */
		PosManager->var.EgearDisable = FALSE;
		PosManager->Egear = &(PosManager->conf.Egear);
	}
	else
	{ 
		PosManager->var.EgearDisable = TRUE;
		PosManager->Egear = &(PosManager->conf.InvalidEgear);
	}
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Position command filter valid/invalid switching processing
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BpiPcmdFilSwitch( POS_MNG_HNDL *PosManager, BOOL Switch )
{
	if( Switch == TRUE )
	{ /* When requesting position command filter activation */
		PosManager->var.PcmdFilStop = FALSE;
	}
	else
	{ 
		PosManager->var.PcmdFilStop = TRUE;
	}
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Variable initialization processing for settling time related processing
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void StlgInitSettlingTime( SETTLINGTIME *SettlingTime, INT32 CycleTimeUs )
{
	/* Reset Settling Time Variable */
	MlibResetLongMemory( SettlingTime, sizeof(*SettlingTime)/4 );

	SettlingTime->UnitChngGain = CycleTimeUs;

}

/****************************************************************************************************
 * DESCRIPTION:
 *		Settling time calculation function 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void StlgCalculateSettlingTime( SETTLINGTIME *SettlingTime, INT32 dPcmda, BOOL CoinSts )
{
	/*----------------------------------------------------------------------------------------------*/
	/*	Settling time calculation Creating flag during calculation									*/
	/*----------------------------------------------------------------------------------------------*/
	if( dPcmda != 0 )
	{ 
		if( SettlingTime->StlgLastdPcmd == 0 )
		{ 
			/* Substitute the settling time held in the output variable */
			SettlingTime->RsltStlgTime = SettlingTime->TmpStlgTime;
		}

		/*------------------------------------------------------------------------------------------*/
		/*	Clear variables during operation														*/
		/*------------------------------------------------------------------------------------------*/
		SettlingTime->StlgCalcReq = FALSE;				/* Settling time calculation request flag OFF   */
		SettlingTime->StlgLastCoinSts = FALSE;			
		SettlingTime->StlgTimeCntr = 0; 			
	}
	else if( SettlingTime->StlgLastdPcmd != 0 )
	{ /* When the position command is issued (first time only)*/
		SettlingTime->StlgCalcReq = TRUE;				/* Settling time calculation request flag ON */
	}

	/*----------------------------------------------------------------------------------------------*/
	/*	Calculation of settling time																			*/
	/*----------------------------------------------------------------------------------------------*/
	if( SettlingTime->StlgCalcReq == TRUE )
	{
		/*------------------------------------------------------------------------------------------*/
		/*	When SettlingTime->StlgTimeCntr = 65535, settling time is assigned to the output variable (about 8[s])*/
		/*------------------------------------------------------------------------------------------*/
		if( SettlingTime->StlgTimeCntr == 65535 )
		{
			SettlingTime->RsltStlgTime = SettlingTime->TmpStlgTime;
		}

		/*------------------------------------------------------------------------------------------*/
		/*	Buffer the settling time only when the positioning complete signal turns ON									*/
		/*------------------------------------------------------------------------------------------*/
		if( ( CoinSts == TRUE ) && ( SettlingTime->StlgLastCoinSts == FALSE ) )
		{
			SettlingTime->TmpStlgTime = SettlingTime->StlgTimeCntr;
		}

		/*------------------------------------------------------------------------------------------*/
		/*	Increment counter (but limit at 65535)							*/
		/*------------------------------------------------------------------------------------------*/
		if( SettlingTime->StlgTimeCntr != 65535 )
		{
			SettlingTime->StlgTimeCntr++;
		}
	}

	/*----------------------------------------------------------------------------------------------*/
	/*	Keep previous value																				*/
	/*----------------------------------------------------------------------------------------------*/
	SettlingTime->StlgLastdPcmd = dPcmda;					
	SettlingTime->StlgLastCoinSts = CoinSts;				

//	BoutV.OutSettlingTime = SettlingTime->RsltStlgTime;

}

/****************************************************************************************************
 * DESCRIPTION:
 *		Create maximum overshoot amount 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void StlgMakeMaxOverShoot( SETTLINGTIME *SettlingTime, POS_MNG_HNDL *PosManager, BOOL PosCtrlMode, INT32 PosErr )
{

	if( (PosCtrlMode == TRUE) && (PosManager->var.dPcmda == 0) )
	{
/*--------------------------------------------------------------------------------------------------*/
/*		Positioning direction discrimination														*/
/*--------------------------------------------------------------------------------------------------*/
		if( SettlingTime->LastdPcmda > 0 )		/* Positioning in the forward direction Command issuing first time */
		{
			SettlingTime->UnMaxOverShoot = 0;		/* Maximum overshoot amount clear					*/
			SettlingTime->OverErrDir = -1;			/* Sign of deviation to overshoot (-)*/
		}
		else if( SettlingTime->LastdPcmda < 0 ) /* Positioning in the negative direction Command issuing first time*/
		{
			SettlingTime->UnMaxOverShoot = 0;		/* Maximum overshoot amount clear					*/
			SettlingTime->OverErrDir = 1;			/* Sign of deviation to overshoot (+)*/
		}

		SettlingTime->OverShoot = SettlingTime->OverErrDir * PosErr;

/*--------------------------------------------------------------------------------------------------*/
/*		Overshoot amount judgment																	*/
/*--------------------------------------------------------------------------------------------------*/
		if( SettlingTime->UnMaxOverShoot < SettlingTime->OverShoot )
		{
			SettlingTime->UnMaxOverShoot = SettlingTime->OverShoot;
		}
	}
	else
	{
		BpiRsetMaxOverShoot( SettlingTime );			/* Overshoot amount clear processing		*/
	}
	SettlingTime->LastdPcmda = PosManager->var.dPcmda;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Positioning completion failure cumulative time creation
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void StlgMakeCoinOffTime( SETTLINGTIME *SettlingTime, INT32 dPcmda, BOOL CoinSts, BOOL PosCtrlMode )
{

	if( (PosCtrlMode == TRUE) && (dPcmda == 0) )
	{
		if( CoinSts == TRUE )
		{
			SettlingTime->StlgCoinLatch = TRUE;
		}
	}
	else
	{
		SettlingTime->StlgCoinLatch = FALSE;
	}

/*--------------------------------------------------------------------------------------------------*/
/*	COIN crack detection --> Positioning completion failure cumulative time creation				*/
/*--------------------------------------------------------------------------------------------------*/
	if( (SettlingTime->StlgCoinLatch == TRUE) && (CoinSts == FALSE) )
	{
		if( SettlingTime->UnCoinOffTimer < 0x1000000 )
		{
			SettlingTime->UnCoinOffTimer++;
		}
	}

	if( PosCtrlMode == FALSE )
	{
		BpiRsetCoinOffTime( SettlingTime );
	}

}


/****************************************************************************************************
 * DESCRIPTION:
 *		Allowable overshoot parameter calculation
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void StlgCalculateOverShootErrLevel( SETTLINGTIME *SettlingTime, INT32 CoinLevel, INT32 ovserrdetlvl )
{
	INT32	sx;
	sx = 0;
	SettlingTime->OvsErrDetLvl = MlibScalKxgain( CoinLevel,ovserrdetlvl, 100, &sx, -24 );
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Overshoot amount clear processing
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BpiRsetMaxOverShoot( SETTLINGTIME *SettlingTime )
{
	SettlingTime->UnMaxOverShoot = 0;
	SettlingTime->OverErrDir = 0;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Positioning completion failure accumulated time clear processing 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BpiRsetCoinOffTime( SETTLINGTIME *SettlingTime )
{
	SettlingTime->UnCoinOffTimer = 0;
	SettlingTime->StlgCoinLatch = FALSE;
	SettlingTime->StlgCoinLatch = FALSE;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Positioning completion failure cumulative time acquisition processing
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 BpiGetCoinOffTime( SETTLINGTIME *SettlingTime )
{
	return ( MlibMulgain( SettlingTime->UnCoinOffTimer, SettlingTime->UnitChngGain) );

}

/****************************************************************************************************
 * DESCRIPTION:
 *		Maximum overshoot amount [command unit] acquisition processing
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 BpiGetMaxOverShoot( SETTLINGTIME *SettlingTime )
{
	return (SettlingTime->UnMaxOverShoot);
}

/****************************************************************************************************
 * DESCRIPTION:
 *		COIN latch status acquisition processing 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 BpiGetCoinLatchSts( SETTLINGTIME *SettlingTime )
{
	return (SettlingTime->StlgCoinLatch);
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Settling time calculation result acquisition processing
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 BpiGetSettlingTime( SETTLINGTIME *SettlingTime )
{
	return (SettlingTime->RsltStlgTime);
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Servo control net position response management
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PosMngResponseManager( BASE_CTRL *BaseControls )
{

	if( BaseControls->CtrlCmdMngr.CtrlMode != BASE_MODE_POS  )
	{
		PosMngInitPositionManager( BaseControls );
	}
//	else if( BaseControls->CtrlModeSet.ZctrlMode.zm == CTRLMODE_ZSTOP )
//	{
//		;
//	}

}

#endif

#if 0
/****************************************************************************************************
 * DESCRIPTION:
 *		Servo control position deviation edge clear Variable initialization
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BpxPerClrSignalProcedure( BASE_LOOP *BaseLoops, BPRMDAT *Bprm )
{
	BASE_CTRL *BaseControls;
	INT32	lwk;

	BaseControls = BaseLoops->BaseCtrls;	

	BpxEdgeClrSvcPositionManager( BaseLoops );

	BaseControls->CtrlCmdMngr.ClrFbPos = BaseControls->ClrFbPos;	
	if(Bprm->FencUse != FALSE)
	{
		lwk = BaseControls->MotSts.FencPos - BaseControls->ClrFbPos;
	}
	else
	{
		lwk = BaseControls->MotSts.MotPos - BaseControls->ClrFbPos; 
	}
	MlibAposRg64dp(
				-lwk,
				&(BaseControls->PosManager.conf.Egear), 
				&(BaseControls->PosManager.PgPos), 
				&(BaseControls->MonPerrLpos[0]) );

	return;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Servo control position deviation edge clear Variable initialization
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BpxEdgeClrSvcPositionManager( BASE_LOOP *BaseLoops )
{
	BASE_CTRL	*BaseControls;
	POSCTRL 		*PosCtrl;
	POS_MNG_HNDL	*PosMngV;
	
	BaseControls = BaseLoops->BaseCtrls;	
	PosCtrl = &(BaseLoops->PosCtrl);
	PosMngV = &(BaseControls->PosManager);

	MlibResetLongMemory( &(PosMngV->var.Per64),sizeof(PosMngV->var.Per64)/4); 
	MlibResetLongMemory( &(PosMngV->var.PosErrA), sizeof(PERRA)/4 );
	PosMngV->var.dPcmdEgear = 0;								
	PosMngV->var.dPcmdEgrem = 0;							
	PosMngV->var.dPcmda = 0;									

	BaseControls->SettlingTime.LastdPcmda = 0;		
	
	MlibResetLongMemory( &(PosMngV->BlshCmp.var), sizeof(PosMngV->BlshCmp.var)/4 ); 

	PosCtrl->V.PerIvar64[0] = 0;						
	PosCtrl->V.PerIvar64[1] = 0;						
	PosCtrl->V.LastPacOut = 0;						
	PosCtrl->V.dPFfFilo = 0;							

	BaseControls->CtrlCmdMngr.dPFfFilo = 0; 			/* Position differentiation Previous value clear*/

	BaseControls->PosManager.dPosRefi = 0;				/* Position command difference input [Pulse/Scan]*/
	BaseControls->PosManager.CompdPosRefi = 0;			/* Position command difference input(TaskB->TaskA)*/
	BaseControls->PosManager.CompdPcmda = 0;			/* Position command increment value input (TaskB->TaskA)*/

	if( BaseControls->CtrlModeSet.CtrlMode.b.cm == BASE_MODE_POS )
	{
		BaseControls->PosCtrlSts.CoinSignal = TRUE; 	
		BaseControls->PosCtrlSts.NearSignal = TRUE; 		
	}
	else
	{
		BaseControls->PosCtrlSts.CoinSignal = FALSE;		
		BaseControls->PosCtrlSts.NearSignal = FALSE;	
	}
/*--------------------------------------------------------------------------------------------------*/
/*	Position command related variable initialization: Position command filter, MFC, etc.			*/
/*--------------------------------------------------------------------------------------------------*/
	/* Position command filter initialization processing */
	PcmdFilInitialize( &(BaseControls->PcmdFil), &(BaseControls->VibSupFil) );
	/* MFC variable initialization */
	MfcInitModelControl( &(BaseControls->MFControl) );

	return;

}



#endif

