/****************************************************************************************************
 *
 * FILE NAME:  IniPrmCal.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.03.04
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	04-03-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Global.h"
#include "ExCtrlPrmCal.h"
#include "PnPrmListTbl.h"

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 LpxPcalOverLoadLevelAmpMot( OLPRM *OlPrm, CHECK_OLP *ChkOLP1, CHECK_OLP *ChkOLP2,
											CHECK_OLP *ChkOLP3, PRMDATA *Prm, INT32	LinearCoolRate100PerGain )
{
	REAL32	Ix;
	REAL32	Ibsqr;
	REAL32	Ixsqr;
	REAL32	Iysqr;
	REAL32	Izsqr;
	INT32	rc;
	REAL32	wk;
	INT32	cflg;

	rc = TRUE;

	if( ChkOLP1 != ChkOLP2 )
	{
		cflg = TRUE;
	}
	else
	{
		cflg = FALSE;
	}

	/********************************************************************************************/
	/*		Calculation of continuous overload													*/
	/********************************************************************************************/
	/*																							*/
	/*		Ibsqr = Ibse * Ibse																	*/
	/*																							*/
	/*				  (Imid^2 - Ibse^2) * Tmid[0.1s]											*/
	/*		AlmLvl = --------------------------------											*/
	/*						  Cycle[ms]															*/
	/*																							*/
	/********************************************************************************************/
	if( OlPrm->Ibse > 15000 )					
	{
		if( cflg == TRUE )
		{
			ChkOLP1->Ibsqrmid      = ChkOLP2->Ibsqrmid;
			ChkOLP1->AlmLvlmid     = ChkOLP2->AlmLvlmid;
			ChkOLP1->WrnLvlmid     = ChkOLP2->WrnLvlmid;
			ChkOLP1->OlLvlmidGain  = ChkOLP2->OlLvlmidGain;
			ChkOLP1->Ibsqrmax      = ChkOLP2->Ibsqrmax;
			ChkOLP1->AlmLvlmax     = ChkOLP2->AlmLvlmax;
			ChkOLP1->WrnLvlmax     = ChkOLP2->WrnLvlmax;
			ChkOLP1->OlLvlmaxGain  = ChkOLP2->OlLvlmaxGain;


			ChkOLP1->CoolRateGain = LinearCoolRate100PerGain;		

			return( TRUE );								
		}
		else
		{
			return( FALSE );							
		}
	}
	else
	{
		Ix = OlPrm->Ibse;
	}
	Ibsqr = Ix * Ix;			

	Ixsqr = OlPrm->Imid * OlPrm->Imid;						

	Izsqr = Ixsqr - Ibsqr;

	ChkOLP1->Ibsqrmid  = Ibsqr;

	ChkOLP1->AlmLvlmid = ( (REAL32)Izsqr * (REAL32)(100*OlPrm->Tmid) / (REAL32)TASKC_CYCLEMS );
	ChkOLP1->WrnLvlmid = ( (REAL32)ChkOLP1->AlmLvlmid * (REAL32)Prm->PnCfgPrm.OvrLoadWrnLvl/ (REAL32)100 );
	ChkOLP1->OlLvlmidGain = (REAL32)ChkOLP3->AlmLvlmid / (REAL32)ChkOLP1->AlmLvlmid;

	if( (ChkOLP1->AlmLvlmid >= 0x40000000) || (ChkOLP1->AlmLvlmid <= 0) )		
	{
		rc = FALSE;
	}

	/********************************************************************************************/
	/*		Calculation of instantaneous overload												*/
	/********************************************************************************************/
	/*																							*/
	/*				 (Imid^2 * Tmid) - (Imax^2 * Tmax)											*/
	/*		Ibsqr = -----------------------------------											*/
	/*						 (Tmid - Tmax)														*/
	/*																							*/
	/*				  (Imax^2 - Imsqr) * Tmax													*/
	/*		AlmLvl = ----------------------------												*/
	/*						   Cycle															*/
	/*																							*/
	/********************************************************************************************/
	if( OlPrm->Imax > 0x7FFF)						
	{
		if( cflg == TRUE )
		{
			ChkOLP1->Ibsqrmax = ChkOLP2->Ibsqrmax;
			ChkOLP1->AlmLvlmax = ChkOLP2->AlmLvlmax;
			ChkOLP1->WrnLvlmax = ChkOLP2->WrnLvlmax;
			ChkOLP1->OlLvlmaxGain = ChkOLP2->OlLvlmaxGain;

			ChkOLP1->CoolRateGain = LinearCoolRate100PerGain;	

			return( TRUE );							
		}
		else
		{
			return( FALSE );					
		}
	}
	else
	{
		Ix = OlPrm->Imax;
	}
	Iysqr = Ix * Ix;				

	wk = ( Ixsqr * OlPrm->Tmid ) - ( Iysqr * OlPrm->Tmax );	
	Ibsqr = (INT32)( (REAL32)wk / (REAL32)(OlPrm->Tmid - OlPrm->Tmax) );

	if( wk < 0 )
	{
		ChkOLP1->Ibsqrmax		= 0;	
		ChkOLP1->AlmLvlmax		= 0;		
		ChkOLP1->WrnLvlmax		= 0;			
		ChkOLP1->OlLvlmaxGain	= 0;		

		return( FALSE );				
	}
	else
	{

		if( Ibsqr < ChkOLP1->Ibsqrmid )				
		{
			ChkOLP1->Ibsqrmax  = ChkOLP1->Ibsqrmid;
			ChkOLP1->AlmLvlmax = ChkOLP1->AlmLvlmid;
			ChkOLP1->WrnLvlmax = ChkOLP1->WrnLvlmid;

			ChkOLP1->OlLvlmaxGain =
					(REAL32)ChkOLP3->AlmLvlmax / (REAL32)ChkOLP1->AlmLvlmax;


		}
		else
		{
			Izsqr = Iysqr - Ibsqr;

			ChkOLP1->Ibsqrmax  = Ibsqr;

			ChkOLP1->AlmLvlmax = ( (REAL32)Izsqr * (float)(100*OlPrm->Tmax) / (REAL32)TASKC_CYCLEMS );
			ChkOLP1->WrnLvlmax = ( (REAL32)ChkOLP1->AlmLvlmax * (REAL32)Prm->PnCfgPrm.OvrLoadWrnLvl / (REAL32)100 );
			ChkOLP1->OlLvlmaxGain = (REAL32)ChkOLP3->AlmLvlmax / (REAL32)ChkOLP1->AlmLvlmax;

			if(( ChkOLP1->AlmLvlmax >= 0x40000000 ) ||(ChkOLP1->AlmLvlmax <= 0))
			{
				rc = FALSE;
			}
		}
	}
	return( rc );
}

/****************************************************************************************************
* DESCRIPTION:
*
* RETURNS:
*
****************************************************************************************************/
PRIVATE void IprmcalOverLoadLevelAmpMot( AXIS_HANDLE *Axis )
{
	OLPRM		Mot, Amp;
	BOOL		prmchk;
	PRMDATA 	*Prm;
	BPRMDAT 	*Bprm;
	CHECK_OL	*pMotOL;
	CHECK_OL	*pAmpOL;
	INT32		ks, ks2;
	INT32		kx, sx;
	INT32		LinearCoolRate100PerGain = 0;

	CHECK_LOWOL *pLowOL;
	INT32	wk;
	INT32	s;
	INT32	calcwk;
	REAL32  fw;
	OLPRM	AmpLock,AmpLow;
	CHECK_OLP	*AmpLockOLP;
	LOWOLPRM	*LowOLPrm;
	LOWOLCHKP	*LowOLChkP;
	pLowOL	  = &(Axis->CheckAlm->AmpLowOL);
	AmpLockOLP = &(Axis->CheckAlm->AmpOL.conf.LockArea);
	LowOLPrm = &(pLowOL->LowOLPrm);
	LowOLChkP = &(pLowOL->LowOLChkP);

	Prm 	  = Axis->Prm; 
	Bprm	  = Axis->BaseLoops->Bprm;		
	pMotOL	  = &(Axis->CheckAlm->MotOL);
	pAmpOL	  = &(Axis->CheckAlm->AmpOL);


	Mot.Tmid = 100;                        // 10s
	Mot.Tmax = 30;                         // 3s
	Mot.Ibse = 1.1f * Bprm->RatCur;        // 110%
	Mot.Imid = 2.2f * Bprm->RatCur;        // 220%
    
    if(Bprm->MaxCur >= 3.0f *Bprm->RatCur)
    {
      Mot.Imax = 3.0f *Bprm->RatCur;               // 300%
    }
    else
    {
      Mot.Imax = Bprm->MaxCur;               // 300%
    }
    
	if(Mot.Ibse >= Mot.Imax)
	{
		Mot.Imax = 1.1f * Mot.Ibse ;
		ALMSetPramError( Axis->AlmMngr, pndef_RatCur.Number );
	}

	if(Mot.Imid >= Mot.Imax)
	{
		fw = (Mot.Imax/Mot.Ibse - 1.0f);
		fw = fw/2 + 1.0f;
		Mot.Imid = fw * Mot.Ibse;
	}

        if(Axis->Prm->PnCfgPrm.DevRatCur == RATED_CUR_50A*10.0f)
        {
			Amp.Tmid = 100;                        // 10s
			Amp.Tmax = 30;                         // 3s
			Amp.Ibse = 1.05f * (REAL32)Prm->PnCfgPrm.DevRatCur*C_FSqrt2/10.0f;    // 115%
			Amp.Imid = 1.5f * (REAL32)Prm->PnCfgPrm.DevRatCur*C_FSqrt2/10.0f;     // 230%;
			Amp.Imax = 2.0f * (REAL32)Prm->PnCfgPrm.DevRatCur*C_FSqrt2/10.0f;        // 300% 
        }
        else
        {
			Amp.Tmid = 100;                        // 10s
			Amp.Tmax = 30;                         // 3s
			Amp.Ibse = 1.15f * (REAL32)Prm->PnCfgPrm.DevRatCur*C_FSqrt2/10.0f;    // 115%
			Amp.Imid = 2.3f * (REAL32)Prm->PnCfgPrm.DevRatCur*C_FSqrt2/10.0f;     // 230%;
			Amp.Imax = (REAL32)Prm->PnCfgPrm.DevMaxCur*C_FSqrt2/10.0f;        // 300% 
        }

	prmchk = TRUE;							

	pAmpOL->conf.NormalArea.CoolRateGain = 0;							
	LinearCoolRate100PerGain = 0;
	
	if( Amp.Imax > Mot.Imax )
	{

		if( LpxPcalOverLoadLevelAmpMot( &Mot, &(pMotOL->conf.NormalArea), &(pMotOL->conf.NormalArea),&(pMotOL->conf.NormalArea),
										Prm, LinearCoolRate100PerGain ) != TRUE )		
		{
			ALMSetPramError( Axis->AlmMngr, pndef_RatCur.Number );
		}

		if( LpxPcalOverLoadLevelAmpMot( &Amp, &(pAmpOL->conf.NormalArea), &(pMotOL->conf.NormalArea), &(pAmpOL->conf.NormalArea),
										Prm, LinearCoolRate100PerGain ) != TRUE )		

		{
			ALMSetPramError( Axis->AlmMngr, pndef_DevRatCur.Number );
		}
	}
	else
	{
		if( LpxPcalOverLoadLevelAmpMot( &Amp, &(pAmpOL->conf.NormalArea), &(pAmpOL->conf.NormalArea), &(pAmpOL->conf.NormalArea),
										Prm, LinearCoolRate100PerGain ) != TRUE )	
		{
			ALMSetPramError( Axis->AlmMngr, pndef_DevRatCur.Number );
		}

		if( LpxPcalOverLoadLevelAmpMot( &Mot, &(pMotOL->conf.NormalArea), &(pAmpOL->conf.NormalArea), &(pMotOL->conf.NormalArea),
										Prm, LinearCoolRate100PerGain ) != TRUE )	
		{
			ALMSetPramError( Axis->AlmMngr, pndef_RatCur.Number );
		}
	}

	LowOLChkP->conf.LowSpdOLChkEnable = (UINT8)FALSE;		
		
}




/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void IprmcalMotorOverrun( AXIS_HANDLE *Axis )
{
	UINT32			work1;
	PRMDATA			*Prm;
	BPRMDAT			*Bprm;
	CHECK_OVRRUN	*pOvrRun;

	Prm = Axis->Prm;		
	Bprm = Axis->BaseLoops->Bprm;		
	pOvrRun = &(Axis->CheckAlm->OvrRun);
	work1 = Prm->PnAlmCfgPrm.OvrRunTrq;
	
	
	if ( work1 > (UINT32)Bprm->PerMaxTrq )
	{
		pOvrRun->OvrTrqLevel = 0x01000000;
	}
	else
	{

		pOvrRun->OvrTrqLevel = work1 * 0x01000000 / Bprm->PerMaxTrq;

	}
	

	pOvrRun->OvrSpdLevel = 0x01000000 / 60;

}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void IprmcalMotStall( AXIS_HANDLE *Axis )
{
	BPRMDAT		*Bprm;
	REAL32      fw ;
	Bprm = Axis->BaseLoops->Bprm;

	fw = 1.5*Bprm->RatCur;
	if(fw > Bprm->MaxCur) fw = Bprm->MaxCur;

	Axis->CheckAlm->MotStall.StallCur = fw;
	Axis->CheckAlm->MotStall.StallCountMax = 200;					//300ms
	Axis->CheckAlm->MotStall.StallSpdL = 5 * Bprm->Kspdrpm;
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void IprmcalOverTemp( AXIS_HANDLE *Axis )
{
	CHECK_OVTEMP	*OverTemp;

	OverTemp = &(Axis->CheckAlm->OverTemp);

	OverTemp->conf.MaxMotTemp = MAX_MOTOR_TEMP;
	OverTemp->conf.MaxIPMTemp = MAX_IPM_TEMP;	
	OverTemp->conf.MotVtTime = 1000;
	OverTemp->conf.IPMVtTime = 1000;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void IprmcalOverCur( AXIS_HANDLE *Axis )
{
	BPRMDAT		*Bprm = Axis->BaseLoops->Bprm;
    REAL32      work1;


	// Axis->CheckAlm->SwOc.SwOcTime = 4;

	// if(Axis->Prm->PnAlmCfgPrm.OverCurLvl == 0)
	// {
	// 	work1 = Bprm->MaxCur;
	// }
	// else
	// {
	// 	work1 = Axis->Prm->PnAlmCfgPrm.OverCurLvl*0.01*Bprm->RatCur;
	// }

    // if(work1 >= Bprm->MaxCur)
    // {
    //     Axis->CheckAlm->SwOc.SwOcCur = Bprm->MaxCur;
    // }
    // else
    // {
    //     Axis->CheckAlm->SwOc.SwOcCur = work1;
    // }
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void IprmcalPhaseLack( AXIS_HANDLE *Axis )
{
	BPRMDAT		*Bprm;
	Bprm = Axis->BaseLoops->Bprm;
    CHECK_PHALACK *PhaseChk = &(Axis->CheckAlm->PhaseChk);
    
    MlibResetLongMemory( &(PhaseChk->var), sizeof(PhaseChk->var)/4 );
    
	Axis->CheckAlm->PhaseChk.conf.PhaLackCur = Bprm->RatCur*PHASELACK_BASE_CURRENT;
    
    Axis->CheckAlm->PhaseChk.conf.TrqPhaLackCur =  Bprm->RatCur*0.05;
    Axis->CheckAlm->PhaseChk.conf.PLCheckTime   =   4000;  // 250 ms
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void IprmcalMainAxis( AXIS_HANDLE *Axis)
{
	REGCFG_PRM		RegPrm; 
	TUNELESS_CFGPRM	TuneLessCfgPrm;
 	BPRMDAT         *Bprm = Axis->BaseLoops->Bprm; 
	MFC_CFG_PRM     MfcCfgPrm;

	/* Calculation of parameters related to the friction torque correction function */
	Axis->BaseCtrls->FrictionM.InitEnable = TRUE;

	IprmcalMotorOverrun(Axis);
	IprmcalOverLoadLevelAmpMot(Axis);
	IprmcalMotStall( Axis );
    IprmcalOverCur(Axis);
	IprmcalPhaseLack(Axis);
	IprmcalOverTemp(Axis);

	#if NO_ADVFUNC
	DetVibObsCalculateInitPrm( &(Axis->DetVib->DetVibObs),
							   Axis->BaseLoops->Bprm->Kvx,
							   Axis->Prm->PnCtrlCfgPrm.Jrat,
							   PS_CYCLENS );

	TuneLessCfgPrm.gnmode = Axis->Prm->PnCtrlCfgPrm.ModeSw;
	TuneLessCfgPrm.avibsw = Axis->Prm->PnAdvPrm.AvibOpt;
	TuneLessCfgPrm.DatLevel = Axis->Prm->PnAdvPrm.TlsCfg;
	TuneLessCfgPrm.DobsEn =(Axis->Prm->PnAdvPrm.AdvAppSW>>4) & 0xF;
	TuneLessCfgPrm.MotSilent = 0;

	TuneLessCalculateInitPrm( Axis->BaseCtrls,
							  Axis->BaseLoops->Bprm,
							  Axis->Prm,
							  &TuneLessCfgPrm );
    
    /* Torque filter selection after torque compensation for tuneless*/
	Axis->BaseCtrls->CtrlCmdPrm.LpassFil3 = TuneLessSetTrqFil3(
													Axis->BaseCtrls->TuneLessCtrl.conf.TuningLessUse,
													Axis->BaseCtrls->TuneLessCtrl.conf.TuningLessEx,

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculateInitPrm( &(Axis->BaseCtrls->MFControl), &MfcCfgPrm,
						  				Axis->Prm->PnCtrlCfgPrm.Jrat, Axis->BaseLoops->Bprm, 0x0021) ;
    
    	/* Analysis and initial setting of vibration cycle number */
	FftCalculateInitPrm( Axis->FftAna, TASKB_CYCLEUS );

	/* Automatic notch settings */
	AutoNotchCalculateInitPrm( Axis->ANotchSeq,
							   Axis->ANotch,
							   Axis->Prm,
							   Axis->BaseLoops->Bprm );
	#endif
	/* Program JOG initial parameter calculation*/
	IprmcalPrgJog( &Axis->BaseCtrls->PJogHdl, Axis->BaseLoops->Bprm );
	
}



