/******************************************************************************
* DISCLAIMER
* This software is supplied by Renesas Electronics Corporation and is only
* intended for use with Renesas products. No other uses are authorized. This
* software is owned by Renesas Electronics Corporation and is protected under
* all applicable laws, including copyright laws.
* THIS SOFTWARE IS PROVIDED "AS IS" AND RENESAS MAKES NO WARRANTIES REGARDING
* THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT
* LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE
* AND NON-INFRINGEMENT. ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED.
* TO THE MAXIMUM EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS
* ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES SHALL BE LIABLE
* FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR
* ANY REASON RELATED TO THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE
* BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
* Renesas reserves the right, without notice, to make changes to this software
* and to discontinue the availability of this software. By using this software,
* you agree to the additional terms and conditions found by accessing the
* following link:
* http://www.renesas.com/disclaimer
*
* Copyright (C) 2019 Renesas Electronics Corporation. All rights reserved.
******************************************************************************/
/******************************************************************************
* System Name  : RZ/T1 Sample program
* File Name    : user_prog_info_tbl.h
* Version      : 1.00
* Description  : User Program Infomation Table for RZ/T1.
******************************************************************************/
/******************************************************************************
* History      : DD.MM.YYYY Version  Description
*              : 30.09.2019 1.0      First Release
******************************************************************************/

#ifndef _USER_PRG_INFO_TBL_HEADER_
#define _USER_PRG_INFO_TBL_HEADER_
/***********************************************************************************************************************
Macro definitions
***********************************************************************************************************************/
#include "stdint.h"
/***********************************************************************************************************************
Typedef definitions
***********************************************************************************************************************/
/* loader_table definition */
typedef struct {
    uint32_t   prgsrc;
    uint32_t   prgdst;
    uint32_t   prgsize;
    uint32_t   datasrc;
    uint32_t   datadst;
    uint32_t   datasize;   
}loader_table;


extern const loader_table User_Loader_Table;

/***********************************************************************************************************************
Exported global variables
***********************************************************************************************************************/

/***********************************************************************************************************************
Exported global functions (to be accessed by other files)
***********************************************************************************************************************/

#endif /* _USER_PRG_INFO_TBL_HEADER_ */

/* End of File */
