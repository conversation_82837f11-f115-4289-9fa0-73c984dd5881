/****************************************************************************************************
 *
 * FILE NAME:  MLib.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _MLIB_H_
#define	_MLIB_H_

#include "BaseDef.h"

#define	SRH_FROM_LSB			0
#define	SRH_FROM_MSB			1

//#define	PI						3.1415926535		/* PI										    */
#define	DEG2RAD					0.0174532925194		/* PI / 180										*/
#define	FOURTEENBIT2RAD			        0.0003834951969604	/* (PI / 180) * (360 / 16384)					*/

/*--------------------------------------------------------------------------------------------------*/
/*		Friction														                            */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{	
	INT32	GrvTrq;							// Gravity Torque          
	INT32	PosCTrq;						// Positive Coulomb Torque 
	INT32	NegCTrq;						// Negative Coulomb Torque 
	INT32	VisTrq;					        // Viscous Torque          
	INT32	MotFricEnable;	                // Motor Friction Enable
	INT32   TrqCLast;						// Torque Coulomb Last      
	INT32   TrqGrvLast;						// Torque Gravity Last      
	INT32   SpdHys;				        	// offset speed            
	INT32   RatSpeed;				    	// rate speed            
	BOOL    InitEnable; 					// Motor Friction Parameters Initialization Enable 
}FRICTION;

/*--------------------------------------------------------------------------------------------------*/
/*		Position command moving average filter														*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{				
	INT16	idx;			// Buffer index									
	INT16	ksub;			// Subtraction amount calculation gain(0/1)				
	INT32	sumx;			// Position command total										
	INT32	remx;			// Position command remainder						
	UINT16	zcntx;			// completion check(0:Complete)							
	UINT16	spare;			// Reserve									
} PMAFV;

/*--------------------------------------------------------------------------------------------------*/
/*		Position command moving average filter 2											        */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{			
	INT16	idxSec;			// Buffer index										
	INT16	ksubSec;		// Subtraction amount calculation gain(0/1)				
	INT32	sumxSec;		// Position command total									
	INT32	remxSec;		// Position command remainder							
	UINT16	zcntxSec;		// completion check(0:Complete)						
	UINT16	spareSec;		// Reserve		                                 
} HIGHFV;

/*--------------------------------------------------------------------------------------------------*/
/*		Position command interpolation Moving average filter										*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct
{			
	INT32	sumx;			// Position command total										
	INT32	remx;			// Position command remainder								
	INT32	newpcmd;		// Position command input										
	INT32	oldpcmd;		// Position command input previous value
	UINT16	zcntx;			// completion check(0:Complete)					
	UINT16	spare;			// Reserve												
} PIMFV;

/*--------------------------------------------------------------------------------------------------*/
/*		Position command interpolation Moving average filter 2										*/
/*--------------------------------------------------------------------------------------------------*/ 
typedef	struct	
{				
	INT32	sumxSec;		// Position command total									
	INT32	remxSec;		// Position command remainder							
	INT32	newpcmdSec;		// Position command input								
	INT32	oldpcmdSec;		// Position command input previous value				
	UINT16	zcntxSec;		// completion check(0:Complete)						
	UINT16	spareSec;		// Reserve		                           
} HIMFV;

/*--------------------------------------------------------------------------------------------------*/
/*		Position command index acceleration / deceleration filter									*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{			
	INT32	sumx;			// Position command total									
	INT32	remx;			// Position command remainder (unused)								
} PEXFV;

/*--------------------------------------------------------------------------------------------------*/
/*		Electronic gear & position deviation structure												*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct
{			
	INT32	a;				// Electronic gear A													
	INT32	b;				// Electronic gear B														
	INT32	k1;				// Electronic gear gain: Higher bit of b / a								
	INT32	k2;				// Electronic gear gain: Lower bit of b / a							
	INT32	g1;				// Electronic gear gain: Higher bits of a / b								
	INT32	g2;				// Electronic gear gain: Lower bits of a / b							

} EGEAR;

typedef	struct
{			
	INT32	pfbkb;			// Position FB (command unit) calculation result							
	INT32	pfbkrem;		// Position FB (command unit) calculation result remainder									
	INT32	per64[2];		// For position deviation 64-bit calculation												
	INT32	per32s;			// Position deviation output (rounded)											
	INT32	per32a;			// Same as above Absolute value (rounded)												
	INT32	per32sx;		// Position deviation output (rounded up)										
	INT32	per32ax;		// Same as above Absolute value (rounded up)										
	INT32	per32mx;		// For position deviation monitor (rounding up / zero at 0.5 or less)			
} PERRA;

/*--------------------------------------------------------------------------------------------------*/
/*		Absolute position calculation (reverse electronic gear: FB unit-> command unit)				*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{
	INT32	dposix;			// Position difference [command unit]					
	INT32	aposrem;		// Position remainder [command unit]					
	INT32	apos[2];		// Absolute position (64bit) [Command unit]					
} APOSRG;


PUBLIC INT32 MlibMulkxBiasrem( INT32 u, INT32 k, INT32 *rem, INT32 bias );
PUBLIC INT32 MlibPcmdMafil( INT32 pcmdin, INT32 pmafnum, PMAFV *pmafvar, INT32 *pmafbuf );
PUBLIC INT32 MlibPcmdMafilSec( INT32 pcmdin, INT32 pmafnum, HIGHFV *pmafvarSec, INT32 *pmafbufSec )  ;
PUBLIC INT32 MlibPcmdImafil( INT32 pcmdin, INT32 shift, INT32 index, PIMFV *pimfvar );
PUBLIC INT32 MlibPcmdImafilSec( INT32 pcmdin, INT32 shift, INT32 indexSec, HIMFV *pimfvarSec ) ;
PUBLIC INT32 MlibPcmdExpfil( INT32 pcmdin, INT32 kexp, INT32 pbias, PEXFV *pexfvar );

#define MlibABS( u )			(((u)>=0) ? (u) : -(u))
#define MlibSIGN( u )			((u < 0) ? -1 : 1)
#define MlibMAX( a, b )			(((a)>(b)) ? (a) : (b))
#define MlibMIN( a, b )			(((a)<(b)) ? (a) : (b))

#define MUL3232( a, b, xx )		*(INT64*)xx = ((INT64)(a))*((INT64)(b))
#define MULU32U32( a, b, xx )	*(INT64*)xx = ((INT64)(UINT32)(a))*((INT64)(UINT32)(b))

#define ROUND_TO_UINT16(x)   ((UINT16)(x)+0.5)>(x)? ((UINT16)(x)):((UINT16)(x)+1) 
/*--------------------------------------------------------------------------------------------------*/
/*		Basic math macro																			*/
/*--------------------------------------------------------------------------------------------------*/
extern	const INT16 MlibFastSinTbl[1024];					/* High-speed Sin operation: Input: [360deg/1024]*/
#define	MlibFASTSINS16( a )	(MlibFastSinTbl[(a)&0x3FF])		/* High-speed Sin operation: Output: [1.000/16384]*/
#define	MlibFASTCOSS16( a ) (MlibFastSinTbl[(a+256)&0x3FF])	/* High-speed Cos operation: Output: [1.000/16384]*/

/*--------------------------------------------------------------------------------------------------*/
/*		Gain manipulation macro																		*/
/*--------------------------------------------------------------------------------------------------*/
#define	MlibGAINKX( k )		((INT32)((k)<<8)>>8)
#define	MlibGAINSX( k )		(((INT8*)&(k))[3])
/*--------------------------------------------------------------------------------------------------*/
#define	MlibGAINRD( k )		(MlibGAINKX( k ) >> ((INT32) ((UINT32) (k)>>24)))
/*--------------------------------------------------------------------------------------------------*/


PUBLIC INT32	MlibPcalKxgain( INT32 a, INT32 b, INT32 c, INT32 *psx, INT32 smax );
PUBLIC INT32	MlibScalKxgain( INT32 a, INT32 b, INT32 c, INT32 *psx, INT32 smax );
PUBLIC INT32    MlibPcalKxaddx( INT32 ka, INT32 sa, INT32 kb, INT32 sb, INT32 *psx );
PUBLIC INT32	MlibPcalKxsubx( INT32 ka, INT32 sa, INT32 kb, INT32 sb, INT32 *psx );
PUBLIC INT32	 MlibPcalKxmulx( INT32 a, INT32 b, INT32 c, INT32 *psx );
PUBLIC INT32 	MlibPcalKxdivx( INT32 ka, INT32 sa, INT32 kb, INT32 sb, INT32 smax );
PUBLIC INT32    MlibPcalKxkskx( INT32 a, INT32 ksb, INT32 c, INT32 *psx, INT32 smax );
PUBLIC INT32    MlibPcalKf1gain(INT32	tx, INT32 ts, INT32 insel );
PUBLIC UINT32    MlibPcalKf1gain1(UINT32	tx, UINT32 ts, UINT32 insel );
PUBLIC INT32    MlibPcalKxksks( INT32 a, INT32 ksb, INT32 ksc, INT32 *psx, INT32 smax );
PUBLIC INT32    MlibScalKskxkx( INT32 ksa, INT32 b, INT32 c, INT32 *psx, INT32 smax );
PUBLIC INT32	MlibScalKxksks( INT32 a, INT32 ksb, INT32 ksc, INT32 *psx, INT32 smax );
PUBLIC INT32    MlibScalKxkskx( INT32 a, INT32 ksb, INT32 c, INT32 *psx, INT32 smax );
PUBLIC INT32    MlibScalKskskx( INT32 ksa, INT32 ksb, INT32 c, INT32 *psx, INT32 smax );
PUBLIC INT32 	MlibKsgain2Long( INT32 Input );

PUBLIC INT32 MlibMulgain( INT32 u, INT32 k );
PUBLIC INT32 MlibMulgain29( INT32 u, INT32 k );
PUBLIC INT32 MlibMulgain30( INT32 u, INT32 k );
PUBLIC INT32 MlibMulgain32( INT32 u, INT32 k );
PUBLIC INT32 MlibMulgainNolim( INT32 u, INT32 k );
PUBLIC REAL32  FlibLimitul( REAL32 u, REAL32 ulim, REAL32 llim );
PUBLIC INT32   MlibLimitul( INT32 u, INT32 ulim, INT32 llim );
PUBLIC INT32   MlibLimitS32( INT32 u, INT32 lim );
PUBLIC UINT32  MlibLimitu32( UINT32 u, UINT32 lim );
PUBLIC INT32 MlibMulhigh32( INT32 a, INT32 b );
PUBLIC INT32 MlibMulhighu32( INT32 a, INT32 b );
PUBLIC REAL32  FlibIntegral( REAL32 u, REAL32 k, REAL32 *iu );
PUBLIC INT32 MlibIntegral( INT32 u, INT32 k, INT32 iu[2] );
PUBLIC INT32   MlibLpfilter1( INT32 u, INT32 k, INT32 x );
PUBLIC INT32   MlibLpfilter12( INT32 u, INT32 k,INT32 x, INT32* rem );
PUBLIC REAL32  FlibLpfilter1( REAL32 u, REAL32 k, REAL32 x );
PUBLIC INT32   MlibHpfilter1( INT32 u, INT32 k, INT32 *z );
PUBLIC REAL32 FlibHpfilter1( REAL32 u, REAL32 k, REAL32 *z );
PUBLIC REAL32 FlibLowPassfilter2( float u, float k[4], float z[4] );
PUBLIC INT32 MlibLowPassfilter2( INT32 u, INT32 k[4], INT32 z[4] );
PUBLIC REAL32 FlibPcalKf1gain(INT32 tx, INT32 ts, INT32 insel );

PUBLIC BOOL MlibRampCal (INT32 u, INT32 *iu, INT32 Delta);

PUBLIC BOOL    FlibRampCal(REAL32 u, REAL32 *iu, REAL32 Delta);
PUBLIC void FilbPcalNotchFilter2(REAL32 Fc, REAL32 Qx ,REAL32 Dx ,REAL32 a[4] , REAL32 Fs);
PUBLIC REAL32 FlibNotchFilter2(REAL32 In, REAL32 Y[3], REAL32 X[3], REAL32 a[4]);

PUBLIC void MlibResetByteMemory( void *mempx, INT32 xbyte );
PUBLIC void MlibResetLongMemory( void *mempx, INT32 xlwd );
PUBLIC void MlibCopyByteMemory(void *dstMem, const void *srcMem, INT32 nbyte );
PUBLIC void MlibCopyLongMemory( void *dstMem, void *srcMem, INT32 nbyte );

PUBLIC UINT16 MlibSqrtu32( UINT32 a );
PUBLIC UINT32 MlibSqrtu64( UINT32 a0, UINT32 a1 );

PUBLIC INT32 MlibSrhbiton( UINT32 data, UINT32 dir );

PUBLIC void  MlibSetCRC16MB( UINT8 *MsgBuf, INT32 MsgLen );
PUBLIC BOOL MlibChkCRC16MB( UINT8 *MsgBuf, INT32 MsgLen );
PUBLIC INT32 MlibPerrcalx( INT32 pcmdx, INT32 pfbkx, INT32 per64[2] );
PUBLIC INT32 FlibPerrcalx( INT32 pcmdx, INT32 pfbkx, INT32 per64[2] );
PUBLIC INT32 MlibPfbkxremNolim( INT32 u, INT32 k, INT32 *pfbrem );

PUBLIC void MlibPcalKf2gain(
		INT32	hz,			/* Filter frequency			[0.1Hz]										*/
		INT32	dx,			/* Filter damping coefficient(&)[0.001]									*/
		INT32	ts,			/* Scan time			[us/ns] (Y=0:us, Y=1:ns)						*/
		INT32	kf[2],		/* Gain calculation result  [--]										*/
		INT32	insel	);	/* Input specification(0xYX)[X:not used, Y=us/ns sel.]					*/
PUBLIC void FlibPcalKf2gain(
		float	hz, 		/* Filter frequency			[Hz]									   */
		float	dx, 		/* Filter damping coefficient(&) 	[-] 							   */
		float	ts, 		/* Scan time			[sec]									       */
		float	kf[2]);

PUBLIC INT32 MlibMulkxBiasrem( INT32 u, INT32 k, INT32 *rem, INT32 bias );
PUBLIC INT32 MlibPcmdMafil( INT32 pcmdin, INT32 pmafnum, PMAFV *pmafvar, INT32 *pmafbuf );
PUBLIC INT32 MlibPcmdMafilSec( INT32 pcmdin, INT32 pmafnum, HIGHFV *pmafvarSec, INT32 *pmafbufSec )  ;
PUBLIC INT32 MlibPcmdImafil( INT32 pcmdin, INT32 shift, INT32 index, PIMFV *pimfvar );
PUBLIC INT32 MlibPcmdImafilSec( INT32 pcmdin, INT32 shift, INT32 indexSec, HIMFV *pimfvarSec ) ;
PUBLIC INT32 MlibPcmdExpfil( INT32 pcmdin, INT32 kexp, INT32 pbias, PEXFV *pexfvar );
PUBLIC INT32 MlibPcmdLpfil2( INT32 pcmdin, INT32 kf[2], INT32 z[2] );
PUBLIC float FlibPcmdLpfil2( float pcmdin, float kf[2], float z[2] );
PUBLIC INT32 MlibPcmdEgear( INT32 pcmda, EGEAR *egear, INT32 *pcmdrem );

/****************************************************************************************************/
/*																									*/
/*		Position command generator function															*/
/*																									*/
/****************************************************************************************************/
/*																									*/
/*   1) MlibPcmdMaker()     : Position command generator(64bit):rv=vp, vp=vp+vpacc/vp=vp-vpdec		*/
/*   2) MlibIpTposLimit()   : Same as above Interpolation position command limit:rv=TRUE(Limit)/FALSE(NotLimit)*/
/*   3) MlibRstPcmdMaker()  : Same as above Reset processing:pcmdout[]=pcmdset0,pcmdset1,etc		*/
/*   4) MlibIpcalPcmdMaker  : Same as above Initial parameter calculation:OUT: pcmkprm.osvpm, maxvpm, pshlx, etc*/
/*   5) MlibPcalaPcmdMaker  : Same as above Positioning parameter calculation A:OUT: pcmkprm.vpacc, vpdec,  vpamx, etc*/
/*   6) MlibPcalaPcmdMkrIP  : Same as above Interpolation calculation Parameter calculation A:OUT: pcmkprm.ipmaxspd, ipmaxacc,   etc*/
/*   7) MlibPcalbPcmdMaker  : Same as above Positioning parameter calculation B:OUT: pcmkprm.vpacc, vpdec,  vpamx, etc*/
/*   8) MlibPcalbPcmdMkrIP  : Same as above Interpolation calculation Parameter calculation B:OUT: pcmkprm.ipmaxspd, ipmaxacc,   etc*/
/*																									*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Position command generator																	*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{					
	INT32	osvpm;				/* OS motor position command speed[xpulse/scan]						*/
	INT32	maxvpm;				/* Maximum motor position command speed[xpulse/scan]				*/
	INT32	maxipv;				/* Interpolation mode Maximum position command speed[xpulse/scan]	*/
	UCHAR	pshlx;				/* Position command calculation magnification (shift)[-]			*/
	UCHAR	pcmd64f;			/* Position command 64-bit flag[TRUE/FALSE]							*/
	USHORT	iptimes;			/* Interpolation count[-]											*/

	INT32	vpacc;				/* Position command acceleration[xpulse/scan/scan]					*/
	INT32	vpdec;				/* Position command deceleration[xpulse/scan/scan]					*/
	INT32	vpamx;				/* quotient(vpacc/vpdec)	[-]										*/
	INT32	vparx;				/* remainder(vpacc%vpdec)	[-]										*/
	INT32	vpapx;				/* Acceleration deceleration stop pulse amount[xpulse]				*/
	INT32	maxspd;				/* Maximum position command speed[2^24/OvrSpd]						*/

	INT32	ipmaxspd;			/* Interpolation mode Maximum speed[ipulse/IPcycle]					*/
	INT32	ipmaxacc;			/* Interpolation mode Maximum acceleration (unused)[ipulse/IPcycle^2]	*/
	INT32	ipstpdec;			/* Interpolation mode Emergency stop deceleration[ipulse/IPcycle^2]	*/
} PCMKP;

typedef	struct	
{				
	UCHAR	calendf;			/* Computation end flag (during computation: 0, computation end: 1)	*/
	UCHAR	cmderrf;			/* Command error flag (interpolation mode position command error, etc)      */
	UCHAR	pcmkmode;			/* Position command creation mode									*/
	UCHAR	modechgf;			/* Position command creation mode change flag						*/

	INT32	vpx;				/* Position command speed[xpulse/scan]								*/
	INT32	vpxrem;				/* Position command speed output remainder[xpulse/scan]				*/
	INT32	pcmdout[2];			/* Position command output absolute value[ipulse]					*/
	INT32	avp;				/* Work:Position command speed absolute value[xpulse/scan]			*/
	INT32	n;					/* Work:quotient(avp/vpdec)		[-]									*/
	INT32	rem;				/* Work:remainder(avp%vpdec)	[-]									*/
	INT32	maxvp;				/* Work:Maximum position command speed[xpulse/scan]					*/

	INT32	ipvpi;				/* Interpolate position command speed[ipulse/IPcycle]				*/
	INT32	ipvpx;				/* Interpolate position command speed[ipulse/scan]					*/
	INT32	ipremi;				/* Interpolate Position command speed surplus[ipulse/scan]			*/
	INT32	ipremx;				/* Interpolate Position command speed surplus[ipulse/scan]			*/
	INT32	ipstopx[2];			/* IpWork:Deceleration stop position calculated value[ipulse]		*/
} PCMKV;

typedef	struct	
{					
	PCMKP	P;				
	PCMKV	V;					
} PCMKPV;
/*--------------------------------------------------------------------------------------------------*/
/*		Position command generator calculation execution function									*/
/*--------------------------------------------------------------------------------------------------*/
INT32	MlibPcmdMaker(		
		INT32	pcmdin0,			/* Position command input (lower 32 bits)[pulse]				*/
		INT32	pcmdin1,			/* Position command input (upper 32bit)[pulse]					*/
		INT32	pcmdspd,			/* Position command speed (max/feed)[2^24/OvrSpd]				*/
		PCMKPV	*pcmdmkr,			/* Position command generator calculation variables(P&V)[-]		*/
		UINT32	pcmkmode	);		/* Position command creation mode		[-]						*/
/*--------------------------------------------------------------------------------------------------*/
/*		Position command creation mode definition: Basic mode should be defined by lower byte		*/
/*--------------------------------------------------------------------------------------------------*/
#define	PCMKMODE_NONE   0x0000		/* Position command creation mode: None							*/
#define	PCMKMODE_IPA	0x0001		/* Position command creation mode: Interpolation (with input update) */
#define	PCMKMODE_IPB	0x0101		/* Position command creation mode: Interpolation (without input update) */
#define	PCMKMODE_POS	0x0002		/* Position command creation mode: Positioning					*/
#define	PCMKMODE_FEED	0x0003		/* Position command creation mode: constant speed feed			*/
#define	PCMKMODE_STOP	0x0004		/* Position command creation mode: Deceleration stop			*/
/*--------------------------------------------------------------------------------------------------*/
INT32	MlibIpTposLimit(			
		INT32	*tpos,				/* Target position(64bit)[pulse]								*/
		INT32	*lmtpos,			/* Limit position(64bit)[pulse]									*/
		INT32	lmtdir,				/* Limit direction (positive side/negative side)[-]				*/
		PCMKPV	*pcmdmkr	);		/* Position command generator calculation variables(P&V)[-]		*/
/*--------------------------------------------------------------------------------------------------*/
#define	IPTPOSLMT_PSIDE		1		/* Limit direction: Positive side								*/
#define	IPTPOSLMT_NSIDE		0		/* Limit direction: negative side								*/
/*--------------------------------------------------------------------------------------------------*/
void	MlibRstPcmdMaker(		
		INT32	pcmdset0,			/* Position command initial value (lower 32 bits)[pulse]		*/
		INT32	pcmdset1,			/* Position command initial value (upper 32 bits)[pulse]		*/
		PCMKPV	*pcmdmkr	);		/* Position command generator calculation variables(P&V)[-]		*/
/*--------------------------------------------------------------------------------------------------*/
INT32	MlibIpcalPcmdMaker(			/* Position command generator Initial parameter calculation		*/
		INT32	ksosvp,				/* OS pulse speed						[xpulse/scan]			*/
		INT32	maxspdm,			/* Maximum motor speed					[2^24/OvrSpd]			*/
		INT32	maxspdi,			/* Interpolation mode Maximum speed		[2^24/OvrSpd]			*/
		INT32	pcmd64f,			/* Position command 64-bit flag			[TRUE/FALSE]			*/
		PCMKP	*pcmkprm	);		/* Calculation result output structure pointer[-]				*/
/*--------------------------------------------------------------------------------------------------*/
INT32	MlibPcalaPcmdMaker(			/* Position command generator Positioning parameter calculation A     */
		INT32	maxspd,				/* Maximum speed					[2^24/OvrSpd]				*/
		INT32	acctime,			/* Acceleration time				[xs]						*/
		INT32	dectime,			/* Deceleration time				[xs]						*/
		INT32	scantime,			/* Scan time						[xs]						*/
		PCMKP	*pcmkprm	);		/* Calculation result output structure pointer[-]				*/
/*--------------------------------------------------------------------------------------------------*/
INT32	MlibPcalaPcmdMkrIP(			/* Position command generator Interpolation calculation Parameter calculation A						*/
		INT32	iptimes,			/* Number of interpolation				[-]						*/
		INT32	maxspd,				/* Maximum speed						[2^24/OvrSpd]			*/
		INT32	ipacctm,			/* Minimum acceleration/deceleration time (unused)[xs]			*/
		INT32	ipdectm,			/* Emergency stop deceleration time		[xs]					*/
		INT32	scantime,			/* Scan time						[xs]						*/
		PCMKP	*pcmkprm	);		/* Calculation result output structure pointer[-]				*/
/*--------------------------------------------------------------------------------------------------*/
INT32	MlibPcalbPcmdMaker(			/* Position command generator Positioning parameter calculation B  */
		INT32	accrate,			/* Acceleration rate		[10000pulse/s/s]					*/
		INT32	decrate,			/* Deceleration rate		[10000pulse/s/s]					*/
		INT32	scantime,			/* Scan time(stm)			[us(Y=0)/ns(Y=1)]					*/
		PCMKP	*pcmkprm,			/* Calculation result output structure pointer[-]				*/
		INT32	insel		);		/* Input selection(0xYX)	[X:Not used, Y:us/nsselection]		*/
/*--------------------------------------------------------------------------------------------------*/
INT32	MlibPcalbPcmdMkrIP(			/* Position command generator Interpolation calculation Parameter calculation B*/
		INT32	iptimes,			/* Number of interpolation				[-]						*/
		INT32	ipaccrt,			/* Maximum acceleration/deceleration rate (unused)[10000pulse/s/s]*/
		INT32	ipdecrt,			/* Emergency stop deceleration rate		[10000pulse/s/s]		*/
		INT32	scantime,			/* Scan time(stm)						[us(Y=0)/ns(Y=1)]		*/
		PCMKP	*pcmkprm,			/* Calculation result output structure pointer[-]				*/
		INT32	insel		);		/* Input selection(0xYX)	[X:Not used, Y:us/nsselection]		*/
/*--------------------------------------------------------------------------------------------------*/

PUBLIC INT32 MlibLaufilter(INT32 u, INT32 z, INT32 dz );

PUBLIC double Mlibpow(double x, int y);
#endif  // _MLIB_H_
