/* generated vector source file - do not edit */
        #include "bsp_api.h"
        /* Do not build these data structures if no interrupts are currently allocated because IAR will have build errors. */
        #if VECTOR_DATA_IRQ_COUNT > 0
        BSP_DONT_REMOVE const fsp_vector_t g_vector_table[BSP_ICU_VECTOR_MAX_ENTRIES] =
        {
                        [285] = usbfs_interrupt_handler, /* USB_FI (USB (Function) interrupt) */
            [286] = r_usb_dmaca_intDMAC0I_isr, /* USB_FDMA0 (USB (Function) DMA 0 transmit completion) */
            [287] = r_usb_dmaca_intDMAC1I_isr, /* USB_FDMA1 (USB (Function) DMA 1 transmit completion) */
        };
        #endif