/****************************************************************************************************
 *
 * FILE NAME:  OpeRegTable.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.12
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	12-08-2020 Version 1.00 : Created by Denny
****************************************************************************************************/

#include "Global.h"
#include "OpeRegFunc.h"


/***************************************************************************************************/
/*  Attribute of 0xF000 : Operation mode*/
/***************************************************************************************************/
const PRM_ATTR opedef_OpeMode = {
    0x000,                                             /* Parameter Number */
    ACCLVL_USER1,                                      /* Access Level*/
    FALSE,                                             /* Axis common */
    FALSE,                                             /* Need Reboot */
    FALSE,                                             /* With Sign */
    FALSE,                                             /* Read Only */
    PRMDEF_BASEDEC,                                    /* Display format */
    PRMDEF_NO_EEPROM,                                  /* EEPROM device */
    2,                                                 /* Data Length */
    0,                                                 /* Number of Array */
    0,                                                 /* EEPROM address */
    0x0000,                                            /* Lower Limit */
    0xFFFF,                                            /* Upper Limit */
    0x0000,                                            /* Default value */
    NULL,                           				   /* Pointer to Parameter varialble*/
    &fnCalcOprationMode1,                              /* Parameter Read/Write Callback Function */
    };
	

/***************************************************************************************************/
/*	Attribute of 0xF001 : Operation settings*/
/***************************************************************************************************/
const PRM_ATTR opedef_OpeCmd = {
	0x000,											   /* Parameter Number */
	ACCLVL_USER1,									   /* Access Level*/
	FALSE,											   /* Axis common */
	FALSE,											   /* Need Reboot */
	FALSE,											   /* With Sign */
	FALSE,											   /* Read Only */
	PRMDEF_BASEDEC, 								   /* Display format */
	PRMDEF_NO_EEPROM,								   /* EEPROM device */
	2,												   /* Data Length */
	0,												   /* Number of Array */
	0,												   /* EEPROM address */
	0x0000, 										   /* Lower Limit */
	0xFFFF, 										   /* Upper Limit */
	0x0000, 										   /* Default value */
	NULL,											   /* Pointer to Parameter varialble*/
	&fnCalcOprationCmd1,							   /* Parameter Read/Write Callback Function */
	};


/***************************************************************************************************/
/*	Attribute of 0xF002 : Operation end*/
/***************************************************************************************************/
const PRM_ATTR opedef_OpeEnd = {
	0x000,											   /* Parameter Number */
	ACCLVL_USER1,									   /* Access Level*/
	FALSE,											   /* Axis common */
	FALSE,											   /* Need Reboot */
	FALSE,											   /* With Sign */
	FALSE,											   /* Read Only */
	PRMDEF_BASEDEC, 								   /* Display format */
	PRMDEF_NO_EEPROM,								   /* EEPROM device */
	2,												   /* Data Length */
	0,												   /* Number of Array */
	0,												   /* EEPROM address */
	0x0000, 										   /* Lower Limit */
	0xFFFF, 										   /* Upper Limit */
	0x0000, 										   /* Default value */
	NULL,											   /* Pointer to Parameter varialble*/
	&fnCalcOperationFinish,							   /* Parameter Read/Write Callback Function */
	};	


/***************************************************************************************************/
/*	Attribute of 0xF003 : Access level*/
/***************************************************************************************************/
const PRM_ATTR opedef_AccLevel = {
	0x000,											   /* Parameter Number */
	ACCLVL_USER1,									   /* Access Level*/
	FALSE,											   /* Axis common */
	FALSE,											   /* Need Reboot */
	FALSE,											   /* With Sign */
	FALSE,											   /* Read Only */
	PRMDEF_BASEDEC, 								   /* Display format */
	PRMDEF_NO_EEPROM,								   /* EEPROM device */
	2,												   /* Data Length */
	0,												   /* Number of Array */
	0,												   /* EEPROM address */
	0x0000, 										   /* Lower Limit */
	0xFFFF, 										   /* Upper Limit */
	0x0000, 										   /* Default value */
	NULL,											   /* Pointer to Parameter varialble*/
	&fnCalcAccessLevel, 						   	   /* Parameter Read/Write Callback Function */
	};	


/***************************************************************************************************/
/*	Attribute of 0xF004 : Second operation mode*/
/***************************************************************************************************/
const PRM_ATTR opedef_OpeMode2 = {
	0x000,											   /* Parameter Number */
	ACCLVL_USER1,									   /* Access Level*/
	FALSE,											   /* Axis common */
	FALSE,											   /* Need Reboot */
	FALSE,											   /* With Sign */
	FALSE,											   /* Read Only */
	PRMDEF_BASEDEC, 								   /* Display format */
	PRMDEF_NO_EEPROM,								   /* EEPROM device */
	2,												   /* Data Length */
	0,												   /* Number of Array */
	0,												   /* EEPROM address */
	0x0000, 										   /* Lower Limit */
	0xFFFF, 										   /* Upper Limit */
	0x0000, 										   /* Default value */
	NULL,											   /* Pointer to Parameter varialble*/
	&fnCalcOprationMode2, 						       /* Parameter Read/Write Callback Function */
	};	


/***************************************************************************************************/
/*	Attribute of 0xF005 : Second operation settings*/
/***************************************************************************************************/
const PRM_ATTR opedef_OpeCmd2 = {
	0xFF05,											   /* Parameter Number */
	ACCLVL_USER1,									   /* Access Level*/
	FALSE,											   /* Axis common */
	FALSE,											   /* Need Reboot */
	FALSE,											   /* With Sign */
	FALSE,											   /* Read Only */
	PRMDEF_BASEDEC, 								   /* Display format */
	PRMDEF_NO_EEPROM,								   /* EEPROM device */
	2,												   /* Data Length */
	0,												   /* Number of Array */
	0,												   /* EEPROM address */
	0x0000, 										   /* Lower Limit */
	0xFFFF, 										   /* Upper Limit */
	0x0000, 										   /* Default value */
	NULL,											   /* Pointer to Parameter varialble*/
	&fnCalcOprationCmd2,							   /* Parameter Read/Write Callback Function */
	};


/***************************************************************************************************/
/*	  Parameter(Register) List Table	  */
/***************************************************************************************************/

const PRM_TBL OpeRegTable[] = {
    {0xF0000000   ,0xF000   ,&opedef_OpeMode           },  /*0xF000:Operation mode*/
    {0xF0000001   ,0xF001   ,&opedef_OpeCmd            },  /*0xF001:Operation settings*/
    {0xF0000002   ,0xF002   ,&opedef_OpeEnd            },  /*0xF002:Operation End*/
	{0xF0000003   ,0xF003	,&opedef_AccLevel		   },  /*0xF003:Access Level*/
	{0xF0000004   ,0xF004	,&opedef_OpeMode2 		   },  /*0xF004:Second operation mode*/
	{0xF0000005   ,0xF005	,&opedef_OpeCmd2 		   },  /*0xF005:Second operation settings*/
	{0xFFFFFFFF   ,0xFFFF	,NULL					   },  /* 0xFFFF: End of Table List */

};
	
const UINT32 OpeRegTableEntNum = sizeof(OpeRegTable) / sizeof(OpeRegTable[0]);


