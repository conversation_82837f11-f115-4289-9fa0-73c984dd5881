/***********************************************************************************************************************
 * Copyright [2020-2022] Renesas Electronics Corporation and/or its affiliates.  All Rights Reserved.
 *
 * This software and documentation are supplied by Renesas Electronics Corporation and/or its affiliates and may only
 * be used with products of Renesas Electronics Corp. and its affiliates ("Renesas").  No other uses are authorized.
 * Renesas products are sold pursuant to Renesas terms and conditions of sale.  Purchasers are solely responsible for
 * the selection and use of Renesas products and Renesas assumes no liability.  No license, express or implied, to any
 * intellectual property right is granted by Renesas.  This software is protected under all applicable laws, including
 * copyright laws. Renesas reserves the right to change or discontinue this software and/or this documentation.
 * THE SOFTWARE AND DOCUMENTATION IS DELIVERED TO YOU "AS IS," AND <PERSON><PERSON><PERSON>AS MAKES NO REPRESENTATIONS OR WARRANTIES, AND
 * TO THE FULLEST EXTENT PERMISSIBLE UNDER APPLICABLE LAW, DISCLAIMS ALL WARRANTIES, WHETHER EXPLICITLY OR IMPLICITLY,
 * INCLUDING WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NONINFRINGEMENT, WITH RESPECT TO THE
 * SOFTWARE OR DOCUMENTATION.  RENESAS SHALL HAVE NO LIABILITY ARISING OUT OF ANY SECURITY VULNERABILITY OR BREACH.
 * TO THE MAXIMUM EXTENT PERMITTED BY LAW, IN NO EVENT WILL RENESAS BE LIABLE TO YOU IN CONNECTION WITH THE SOFTWARE OR
 * DOCUMENTATION (OR ANY PERSON OR ENTITY CLAIMING RIGHTS DERIVED FROM YOU) FOR ANY LOSS, DAMAGES, OR CLAIMS WHATSOEVER,
 * INCLUDING, WITHOUT LIMITATION, ANY DIRECT, CONSEQUENTIAL, SPECIAL, INDIRECT, PUNITIVE, OR INCIDENTAL DAMAGES; ANY
 * LOST PROFITS, OTHER ECONOMIC DAMAGE, PROPERTY DAMAGE, OR PERSONAL INJURY; AND EVEN IF RENESAS HAS BEEN ADVISED OF THE
 * POSSIBILITY OF SUCH LOSS, DAMAGES, CLAIMS OR COSTS.
 **********************************************************************************************************************/

/******************************************************************************
 * Includes   <System Includes> , "Project Includes"
 ******************************************************************************/
#include "hal_data.h"
#include "flash.h"

/******************************************************************************
 * Macro definitions
 ******************************************************************************/
#define QSPI_WRITE_SIZE           (0x40U)
#define QSPI_WRITE_MASK           ((uint32_t)0xFFFFFFC0UL)
#define FLASH_MIRROR_OFFSET       ((uint32_t)0x20000000UL)
#define NOR_WRITE_SIZE            (0x100U)
#define CS0_BASE                  ((uint32_t)0x50000000UL)
#define SECTOR_ERASE              (0)
#define CHIP_ERASE                (1)

/* Parallel NOR Flash command */
#define FLASH_A_SEQ_ADD1          (0x555U)
#define FLASH_A_SEQ_ADD2          (0x2AAU)
#define FLASH_CODE1               (0x00AAU)
#define FLASH_CODE2               (0x0055U)
#define FLASH_RESET               (0x00F0U)
#define WRITE_BUF_CODE            (0x0025U)
#define WRITE_BUF_CODE2           (0x0029U)
#define ERASE_SECTOR_CODE1        (0x0080U)
#define ERASE_SECTOR_CODE2        (0x0030U)
#define ERASE_SECTOR_CHIP         (0x0010U)
#define STATUS_REGISTER_Q6BIT     (1U << 6U)

/* Flash backup buffer address */
#define BACKUP_BUFFER_ADDR        ((uint32_t)0x30140000UL)


// modify by lly 20231125
uint8_t  g_flash_buff[QSPI_SECTOR_SIZE] = {0};

/******************************************************************************
 * Private global variables and functions
 ******************************************************************************/
static flash_err_t qspi_erace_flash (xspi_qspi_instance_ctrl_t * const p_ctrl, uint32_t addr, uint32_t len);
static flash_err_t qspi_write_flash(xspi_qspi_instance_ctrl_t * const p_ctrl, uint32_t src, uint32_t dest, uint32_t len);
static fsp_err_t qspi_set_4byte_address_mode(xspi_qspi_instance_ctrl_t * const p_ctrl);

/******************************************************************************
 * @brief Write to the QSPI area.
 *
 * @param[in]  addr           Write address
 * @param[in]  p_data         Write data
 * @param[in]  data_len       Write data length
 *
 * @retval FLASH_SUCCESS            Success
 * @retval FLASH_ERR_PARAMETER      Parameter error
 * @retval FLASH_ERR_MODULE         Module error
 * @retval FLASH_ERR_VERIFY_FAILED  Verify error
 ******************************************************************************/
flash_err_t write_to_qspi_area (uint32_t addr, uint8_t * const p_data, uint32_t data_len)
{
    uint32_t  write_complete_len = 0U;    // Data length for which flash writing has already been completed
    uint32_t  sector_write_len   = 0U;    // Data length to be written in one flash write operation
    flash_err_t flash_err        = FLASH_SUCCESS;
    fsp_err_t fsp_err;
    xspi_qspi_instance_ctrl_t  *p_qspi_ctrl;
    spi_flash_cfg_t const *p_qspi_cfg;
//    uint8_t *p_buf = (uint8_t *)BACKUP_BUFFER_ADDR;
     uint8_t *p_buf = &(g_flash_buff[0]);
    
    /* Check address. */
    /* RSK only implements serial flash connected to QSPI0. */
    if ((QSPI1_START_ADDR <= addr) && ((addr + data_len - 1U) <= QSPI1_END_ADDR))
    {
        /* Set QSPI1 setting information. */
        p_qspi_ctrl = &g_qspi1_ctrl;
        p_qspi_cfg  = &g_qspi1_cfg;
    }
    else
    {
        return FLASH_ERR_PARAMETER;
    }
    
    /* Initializes the QSPI module. */
    fsp_err = R_XSPI_QSPI_Open(p_qspi_ctrl, p_qspi_cfg);
    if (FSP_SUCCESS != fsp_err)
    {
        return FLASH_ERR_MODULE;
    }
 
#if 1     
    /* Set address mode. */
    fsp_err = qspi_set_4byte_address_mode(p_qspi_ctrl);
    if (FSP_SUCCESS != fsp_err)
    {
        /* Close. */
        R_XSPI_QSPI_Close(p_qspi_ctrl);
        return FLASH_ERR_MODULE;
    }
#endif
    
    
    /* Gets the maximum write size for the first sector of the write-to address. */
    sector_write_len = QSPI_SECTOR_SIZE - (addr % QSPI_SECTOR_SIZE);
    
    /* Erace processing loop. (sector unit)  */
    while (write_complete_len < data_len)
    {
        /* Check the write size for the sector. */
        if (sector_write_len > (data_len - write_complete_len))
        {
            sector_write_len = data_len - write_complete_len;
        }
        
        uint32_t src_addr = (uint32_t)p_data + write_complete_len;
        uint32_t dest_addr = addr + write_complete_len;
        
        /* Read the data of the target sector into the buffer. */
        uint32_t read_addr = (dest_addr & QSPI_ERASE_MASK) - FLASH_MIRROR_OFFSET;
        memcpy(p_buf, (void*)read_addr, QSPI_SECTOR_SIZE);
        
        /* Erase sector. */
        flash_err = qspi_erace_flash(p_qspi_ctrl, (dest_addr & QSPI_ERASE_MASK), QSPI_SECTOR_SIZE);
        if (FLASH_SUCCESS != flash_err)
        {
            break;
        }
        
        /* Update the data in the buffer. */
        uint32_t offset = dest_addr - (dest_addr & QSPI_ERASE_MASK);
        memcpy((p_buf + offset), (void *)src_addr, sector_write_len);
        
        /* Write to the sector where the erace was performed. */
        flash_err = qspi_write_flash(p_qspi_ctrl, (uint32_t)p_buf, (dest_addr & QSPI_ERASE_MASK), QSPI_SECTOR_SIZE);
        if (FLASH_SUCCESS != flash_err)
        {
            break;
        }
        
        write_complete_len += sector_write_len;
        sector_write_len    = QSPI_SECTOR_SIZE;
    }
    
    /* Check if writing is complete. */
    if (FLASH_SUCCESS == flash_err)
    {
        /* Verify Write data. (Specify the mirror area address) */
        uint32_t read_addr = addr - FLASH_MIRROR_OFFSET;
        int32_t write_check = memcmp((void*)read_addr, p_data, data_len);
            
        if (0 != write_check)
        {
            flash_err = FLASH_ERR_VERIFY_FAILED;
        }
    }
    
    /* Close. */
   // modify by lly 20231125
//    R_XSPI_QSPI_Close(p_qspi_ctrl);
    
    return flash_err;
}
/******************************************************************************
 * @brief QSPI Erases the specified area.
 *
 * @param[in]  p_ctrl         Pointer to instance control block
 * @param[in]  addr           Start address to erase
 * @param[in]  len            Length to erase
 *
 * @retval FLASH_SUCCESS      Success
 * @retval FLASH_ERR_MODULE   Failure
 ******************************************************************************/
static flash_err_t qspi_erace_flash (xspi_qspi_instance_ctrl_t * const p_ctrl, uint32_t addr, uint32_t len)
{
    flash_err_t flash_err = FLASH_SUCCESS;
    fsp_err_t fsp_err;
    
    /* Erase flash. */
    fsp_err = R_XSPI_QSPI_Erase(p_ctrl, (uint8_t *)addr, len);
    if (FSP_SUCCESS != fsp_err)
    {
        flash_err = FLASH_ERR_MODULE;
    }
    else
    {
        /* Wait for status register to update. */
        spi_flash_status_t status_erase;
        do
        {
            (void) R_XSPI_QSPI_StatusGet(p_ctrl, &status_erase);
        } while (true == status_erase.write_in_progress);
    }
    
    return flash_err;
}

/******************************************************************************
 * @brief QSPI Writes data to the specified address.
 *
 * @param[in]  p_ctrl         Pointer to instance control block
 * @param[in]  src            Source address
 * @param[in]  dest           Dest address
 * @param[in]  len            Write data length
 *
 * @retval FLASH_SUCCESS      Success
 * @retval FLASH_ERR_MODULE   Failure
 ******************************************************************************/
static flash_err_t qspi_write_flash (xspi_qspi_instance_ctrl_t * const p_ctrl, uint32_t src, uint32_t dest, uint32_t len)
{
    flash_err_t flash_err      = FLASH_SUCCESS;
    uint32_t written_len       = 0U;
    uint32_t onetime_write_len;
    fsp_err_t fsp_err;
    uint8_t src_data[QSPI_WRITE_SIZE];
    
    onetime_write_len = QSPI_WRITE_SIZE - (dest % QSPI_WRITE_SIZE);
    
    /* Write processing loop. (64-byte unit) */
    /* Since there is a limit of writing in 64-byte units within one page, */
    /* "0xFF" is written in the front and back area depending on the specified address and data size. */
    while (written_len < len)
    {
        if (onetime_write_len > (len - written_len))
        {
            onetime_write_len = len - written_len;
        }
        
        uint8_t src_offset = 0U;
        
        /* Get the offset value for the first write. (Page crossing prevention) */
        if (0U == written_len)
        {
            src_offset = dest % QSPI_WRITE_SIZE;
        }
        
        /* Clear the data buffer of the write source. */
        memset(src_data, 0xFFU, QSPI_WRITE_SIZE);
        
        /* Stores 64 bytes of data to be written at one time in a buffer. */
        for (uint32_t i = 0U; i < onetime_write_len; i++)
        {
            src_data[src_offset + i] = *(uint8_t *)(src + written_len + i);
        }
        
        /* Write data. */
        fsp_err = R_XSPI_QSPI_Write(p_ctrl, src_data, (uint8_t *)((dest + written_len) & QSPI_WRITE_MASK), QSPI_WRITE_SIZE);
        if (FSP_SUCCESS != fsp_err)
        {
            flash_err = FLASH_ERR_MODULE;
            break;
        }
        
        /* Wait for status register to update. */
        FSP_HARDWARE_REGISTER_WAIT(p_ctrl->p_reg->COMSTT_b.WRBUFNE, 0);
        FSP_HARDWARE_REGISTER_WAIT(p_ctrl->p_reg->COMSTT_b.MEMACC, 0);
        
        spi_flash_status_t status_write;
        do
        {
            (void) R_XSPI_QSPI_StatusGet(p_ctrl, &status_write);
        } while (true == status_write.write_in_progress);
        
        written_len      += onetime_write_len;
        onetime_write_len = QSPI_WRITE_SIZE;
    }
    
    return flash_err;
}

/******************************************************************************
 * @brief QSPI set 4byte address mode.
 *
 * @param[in]  p_ctrl         Pointer to instance control block
 *
 * @retval FSP_SUCCESS(0)  Success
 * @retval Other than 0    Failure
 ******************************************************************************/
#if 1 
static fsp_err_t qspi_set_4byte_address_mode (xspi_qspi_instance_ctrl_t * const p_ctrl)
{
    fsp_err_t fsp_err;
    
    /*----------------- Enter 4byte address mode(EN4B) (0xB7) ----------------*/
    spi_flash_direct_transfer_t direct_command_EN4B = {0};
    direct_command_EN4B.command        = 0xB7U;
    direct_command_EN4B.command_length = 1U;
    
    fsp_err = R_XSPI_QSPI_DirectTransfer(p_ctrl, &direct_command_EN4B, SPI_FLASH_DIRECT_TRANSFER_DIR_WRITE);
    /*--------------------------------------------------------------*/
    
    return fsp_err;
}
#endif