/**
  ******************************************************************************
  * @file    IAP_Main/Src/menu.c 
  * <AUTHOR> Application Team
  * @brief   This file provides the software which contains the main menu routine.
  *          The main menu gives the options of:
  *             - downloading a new binary file, 
  *             - uploading internal flash memory,
  *             - executing the binary file already loaded 
  *             - configuring the write protection of the Flash sectors where the 
  *               user loads his binary file.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */ 

/* Includes ------------------------------------------------------------------*/
#include "common.h"
#include "flash_if.h"
#include "menu.h"
#include "ymodem.h"
#include "bsp.h"

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
pFunction JumpToApplication;
uint32_t JumpAddress;
uint32_t FlashProtection = 0;
uint8_t aFileName[FILE_NAME_LENGTH];


uint32_t UpgradeAddr = UpgradeMark_ADDRESS_START;
uint32_t SourceAddr  = UpgradeMark_ADDRESS_END;

uint8_t  DataBuffer[9];

extern uint16_t MCU_DEVICE_ID;
extern uint8_t UpgradeMark;
extern uint8_t FlashIsEmpty;
/* Private function prototypes -----------------------------------------------*/
void SerialDownload(void);
/* Private functions ---------------------------------------------------------*/
/**
  * @brief  Download a file via serial port
  * @param  None
  * @retval None
  */
void SerialDownload(void)
{
  uint8_t  number[11] = {0};
  uint32_t size = 0;
  uint8_t  UpgradeFlag= 0;
  COM_StatusTypeDef result;

  Serial_PutString((uint8_t *)"Waiting for the file to be sent ... (press 'a' to abort)\n\r");
  result = Ymodem_Receive(&size);
  
  // upgrade success and clear upgrade flag 
  if (result == COM_OK)
  {
    // flash upload mark Set
    UpgradeFlag = *(uint8_t *)UpgradeMark_ADDRESS_START;
    

    if(UpgradeFlag != 1)
    {
      UpgradeMark = 0x01; 
      FLASH_If_Write(UpgradeAddr, &UpgradeMark ,1);       
    }
    else if(UpgradeFlag == 1)
    {
       UpgradeMark = 0xFF; 
       FLASH_If_Write(UpgradeAddr, &UpgradeMark ,1);    		//Erase the sector of flash upgrade flag      
    }
      
    Serial_PutString((uint8_t *)"\n\n\r Programming Completed Successfully!\n\r--------------------------------\r\n Name: ");
    Serial_PutString(aFileName);
    Int2Str(number, size);
    Serial_PutString((uint8_t *)"\n\r Size: ");
    Serial_PutString(number);
    Serial_PutString((uint8_t *)" Bytes\r\n");
    Serial_PutString((uint8_t *)"-------------------\n");
    
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_SystemReset();
  }
  else if (result == COM_LIMIT)
  {
    Serial_PutString((uint8_t *)"\n\n\rThe image size is higher than the allowed space memory!\n\r");
  }
  else if (result == COM_DATA)
  {
    Serial_PutString((uint8_t *)"\n\n\rVerification failed!\n\r");
  }
  else if (result == COM_ABORT)
  {
    Serial_PutString((uint8_t *)"\r\n\nAborted by user.\n\r");
  }
  else
  {
    Serial_PutString((uint8_t *)"\n\rFailed to receive the file!\n\r");
  }
}
/**
  * @brief  Display the Main Menu on HyperTerminal
  * @param  None
  * @retval None
  */
void Main_Menu(void)
{
  uint8_t key = 0;
  /* 禁能所有中断 */
  
  //hApi_DisableInterrupt();

  
  while (1)
  {
    /* Test if any sector of Flash memory where user application will be loaded is write protected */
    SerialDownload();
    break;       
  }
  
  R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
  R_BSP_SystemReset();

}
/**
  * @}
  */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
