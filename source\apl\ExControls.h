/****************************************************************************************************
 *
 * FILE NAME:  ExControl.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.3
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	3-10-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _EX_CONTROL_H_
#define _EX_CONTROL_H_
	
#include "BaseControls.h"


/****************************************************************************************************/
/*		for Disturb Observer																		*/
/****************************************************************************************************/
enum DOBS_CHANGE_STS
{
	DOBS_NOCHANGE		= (INT32)0x00L,		    // Disturbance observer disabled <=> No switching
	DOBSCHNG_INV2ACT	= (INT32)0x01L,			// Disturbance observer disabled => enabled switching
	DOBSCHNG_ACT2INV	= (INT32)0x02L,			// Disturbance observer enabled => disabled switching
};


/****************************************************************************************************/
/*		API's																						*/
/****************************************************************************************************/

/****************************************************************************************************/
/*		for Tuning Less Control																		*/
/****************************************************************************************************/
#pragma inline = forced
BOOL TuneLessGetTuneLessUse( TUNELESS_CTRL *TuneLessCtrl )
{
	return (TuneLessCtrl->conf.TuningLessUse);
}
#pragma inline = forced
BOOL TuneLessGetTuneLessSts( TUNELESS_CTRL *TuneLessCtrl )
{
	return (TuneLessCtrl->var.TuneLessAct);
}
#pragma inline = forced
INT32 TuneLessGetDisTrqCmp( TUNELESS_CTRL *TuneLessCtrl )
{
	return (TuneLessCtrl->Drcomp.var.DisTrqCmp);
}
#pragma inline = forced
INT32 TuneLessGetDisTrq( TUNELESS_CTRL *TuneLessCtrl )
{
	return (TuneLessCtrl->Drcomp.var.DisTrq);
}
#pragma inline = forced
BOOL TuneLessGetTFilClrReq( TUNELESS_CTRL *TuneLessCtrl )
{
	return (TuneLessCtrl->Drcomp.var.TrqFilClrReq);
}
#pragma inline = forced
INT32 TuneLessGetSpdObsFbk( TUNELESS_CTRL *TuneLessCtrl )
{
	return (TuneLessCtrl->EhVobs.var.EhSpdObs);
}
#pragma inline = forced
void TuneLessClearForceInvldReq( TUNELESS_CTRL *TuneLessCtrl )
{
	TuneLessCtrl->var.TuneLessInvldReq = FALSE;
}
#pragma inline = forced
void TuneLessSetForceInvldReq( TUNELESS_CTRL *TuneLessCtrl )
{
	TuneLessCtrl->var.TuneLessAct = FALSE;
	TuneLessCtrl->var.TuneLessInvldReq = TRUE;
}
#pragma inline = forced
BOOL TuneLessGetTuneLessModeEx( TUNELESS_CTRL *TuneLessCtrl )
{
	return (TuneLessCtrl->conf.TuningLessEx);
}


/****************************************************************************************************/
/*		for Disturbance Observer																	*/
/****************************************************************************************************/
#pragma inline = forced

BOOL DobsGetObserverSts( DOBS_CTRL *DobsCtrl )
{
	return (DobsCtrl->V.DobsAct);
}

#pragma inline = forced
REAL32 DobsGetDisTrqCmp( DOBS_CTRL *DobsCtrl )
{
	return (DobsCtrl->V.DisTrqCmp);
}
#pragma inline = forced
UINT8 DobsGetExchangeSts( DOBS_CTRL *DobsCtrl )
{
	return (DobsCtrl->V.ExchangeSts);
}

/****************************************************************************************************/
/*		for Eh Velocity Observer																	*/
/****************************************************************************************************/
#pragma inline = forced
BOOL EhVobsGetObserverUse( EHVOBS_CTRL *EhVobsCtrl )
{
	return (EhVobsCtrl->P.EhVobsUse);
}

/****************************************************************************************************/
/*		for Tuning Less Control																		*/
/****************************************************************************************************/

extern	BOOL	TuneLessGetTuneLessSts( TUNELESS_CTRL *TuneLessCtrl );
//extern	INT32	TuneLessGetSpdObsFbk( TUNELESS_CTRL *TuneLessCtrl );
//extern	INT32	TuneLessGetDisTrqCmp( TUNELESS_CTRL *TuneLessCtrl );
//extern	INT32	TuneLessGetDisTrq( TUNELESS_CTRL *TuneLessCtrl );
//extern	BOOL	TuneLessGetTFilClrReq( TUNELESS_CTRL *TuneLessCtrl );
//extern	void	TuneLessSetForceInvldReq( TUNELESS_CTRL *TuneLessCtrl );
//extern	void	TuneLessClearForceInvldReq( TUNELESS_CTRL *TuneLessCtrl );
extern  void	TuneLessInitRobustCompensator( 	TUNELESS_CTRL *TuneLessCtrl, 
												TUNELESS_DRCOMP	*Drcomp );	


PUBLIC void TuneLessCtrlInitialize( TUNELESS_CTRL *TuneLessCtrl );
PUBLIC void TuningLessCtrl( TUNELESS_CTRL *TuneLessCtrl, INT32 MotSpd, INT32 TrqRefi, INT32 TrqRef );	
PUBLIC void TuneLessInitRobustCompensator( TUNELESS_CTRL *TuneLessCtrl, TUNELESS_DRCOMP	*Drcomp );


/****************************************************************************************************/
/*		for Disturb Observer																		*/
/****************************************************************************************************/
PUBLIC void DisturbObserverInitialize( DOBS_CTRL *DobsCtrl, GSELDOBS *pActGains ) ;
PUBLIC void DisturbObserver( DOBS_CTRL *DobsCtrl, REAL32 MotSpd, REAL32 TrqRef )	;		

/****************************************************************************************************/
/*		for Eh Velocity Observer																	*/
/****************************************************************************************************/
PUBLIC void EhSpeedObserverInitialize( EHVOBS_CTRL *EhVobsCtrl );
PUBLIC REAL32 EhSpeedObserver( EHVOBS_CTRL *EhVobsCtrl, REAL32 MotSpd, REAL32 TrqRef )	;		

PUBLIC void InitParamFriction( BASE_LOOP *BaseLoops  );
PUBLIC INT32 LpxMotorFricComp(  BOOL BaseEnable, BASE_LOOP *BaseLoops );


#endif   //  _EX_CONTROL_H_

