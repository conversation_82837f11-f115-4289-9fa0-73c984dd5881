include "memory_regions.icf";

/* The memory information for each device is done in memory regions file.
 * The starting address and length of memory not defined in memory regions file are defined as 0. */

if (isdefinedsymbol(SYSTEM_RAM_START))
{
    define symbol SYSTEM_RAM_PRV_START = SYSTEM_RAM_START;
}
else
{
    define symbol SYSTEM_RAM_PRV_START = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_LENGTH))
{
    define symbol SYSTEM_RAM_PRV_LENGTH = SYSTEM_RAM_LENGTH;
}
else
{
    define symbol SYSTEM_RAM_PRV_LENGTH = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_MIRROR_START))
{
    define symbol SYSTEM_RAM_MIRROR_PRV_START = SYSTEM_RAM_MIRROR_START;
}
else
{
    define symbol SYSTEM_RAM_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_MIRROR_LENGTH))
{
    define symbol SYSTEM_RAM_MIRROR_PRV_LENGTH = SYSTEM_RAM_MIRROR_LENGTH;
}
else
{
    define symbol SYSTEM_RAM_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_MIRROR_START))
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_START = xSPI0_CS0_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH = xSPI0_CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_MIRROR_START))
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_START = xSPI0_CS1_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH = xSPI0_CS1_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_MIRROR_START))
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_START = xSPI1_CS0_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH = xSPI1_CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_MIRROR_START))
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_START = xSPI1_CS1_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH = xSPI1_CS1_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS0_SPACE_MIRROR_START))
{
    define symbol CS0_SPACE_MIRROR_PRV_START = CS0_SPACE_MIRROR_START;
}
else
{
    define symbol CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS0_SPACE_MIRROR_LENGTH))
{
    define symbol CS0_SPACE_MIRROR_PRV_LENGTH = CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS2_SPACE_MIRROR_START))
{
    define symbol CS2_SPACE_MIRROR_PRV_START = CS2_SPACE_MIRROR_START;
}
else
{
    define symbol CS2_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS2_SPACE_MIRROR_LENGTH))
{
    define symbol CS2_SPACE_MIRROR_PRV_LENGTH = CS2_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS2_SPACE_MIRROR_PRV_LENGTH = 0;
}


if (isdefinedsymbol(CS3_SPACE_MIRROR_START))
{
    define symbol CS3_SPACE_MIRROR_PRV_START = CS3_SPACE_MIRROR_START;
}
else
{
    define symbol CS3_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS3_SPACE_MIRROR_LENGTH))
{
    define symbol CS3_SPACE_MIRROR_PRV_LENGTH = CS3_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS3_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS5_SPACE_MIRROR_START))
{
    define symbol CS5_SPACE_MIRROR_PRV_START = CS5_SPACE_MIRROR_START;
}
else
{
    define symbol CS5_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS5_SPACE_MIRROR_LENGTH))
{
    define symbol CS5_SPACE_MIRROR_PRV_LENGTH = CS5_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS5_SPACE_MIRROR_PRV_LENGTH = 0;
}


if (isdefinedsymbol(xSPI0_CS0_SPACE_START))
{
    define symbol xSPI0_CS0_SPACE_PRV_START = xSPI0_CS0_SPACE_START;
}
else
{
    define symbol xSPI0_CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_LENGTH))
{
    define symbol xSPI0_CS0_SPACE_PRV_LENGTH = xSPI0_CS0_SPACE_LENGTH;
}
else
{
    define symbol xSPI0_CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_START))
{
    define symbol xSPI0_CS1_SPACE_PRV_START = xSPI0_CS1_SPACE_START;
}
else
{
    define symbol xSPI0_CS1_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_LENGTH))
{
    define symbol xSPI0_CS1_SPACE_PRV_LENGTH = xSPI0_CS1_SPACE_LENGTH;
}
else
{
    define symbol xSPI0_CS1_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_START))
{
    define symbol xSPI1_CS0_SPACE_PRV_START = xSPI1_CS0_SPACE_START;
}
else
{
    define symbol xSPI1_CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_LENGTH))
{
    define symbol xSPI1_CS0_SPACE_PRV_LENGTH = xSPI1_CS0_SPACE_LENGTH;
}
else
{
    define symbol xSPI1_CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_START))
{
    define symbol xSPI1_CS1_SPACE_PRV_START = xSPI1_CS1_SPACE_START;
}
else
{
    define symbol xSPI1_CS1_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_LENGTH))
{
    define symbol xSPI1_CS1_SPACE_PRV_LENGTH = xSPI1_CS1_SPACE_LENGTH;
}
else
{
    define symbol xSPI1_CS1_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS0_SPACE_START))
{
    define symbol CS0_SPACE_PRV_START = CS0_SPACE_START;
}
else
{
    define symbol CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS0_SPACE_LENGTH))
{
    define symbol CS0_SPACE_PRV_LENGTH = CS0_SPACE_LENGTH;
}
else
{
    define symbol CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS2_SPACE_START))
{
    define symbol CS2_SPACE_PRV_START = CS2_SPACE_START;
}
else
{
    define symbol CS2_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS2_SPACE_LENGTH))
{
    define symbol CS2_SPACE_PRV_LENGTH = CS2_SPACE_LENGTH;
}
else
{
    define symbol CS2_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS3_SPACE_START))
{
    define symbol CS3_SPACE_PRV_START = CS3_SPACE_START;
}
else
{
    define symbol CS3_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS3_SPACE_LENGTH))
{
    define symbol CS3_SPACE_PRV_LENGTH = CS3_SPACE_LENGTH;
}
else
{
    define symbol CS3_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS5_SPACE_START))
{
    define symbol CS5_SPACE_PRV_START = CS5_SPACE_START;
}
else
{
    define symbol CS5_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS5_SPACE_LENGTH))
{
    define symbol CS5_SPACE_PRV_LENGTH = CS5_SPACE_LENGTH;
}
else
{
    define symbol CS5_SPACE_PRV_LENGTH = 0;
}

/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\a_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = 0x00000000;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_ROM_start__ = 0x60020100;
define symbol __ICFEDIT_region_ROM_end__   = 0x6006FFFF;
define symbol __ICFEDIT_region_RAM_start__ = 0x00000100;
define symbol __ICFEDIT_region_RAM_end__   = 0x0001FFFF;
/**** End of ICF editor section. ###ICF###*/

define memory mem with size = 4G;
define region ROM_region   = mem:[from __ICFEDIT_region_ROM_start__   to __ICFEDIT_region_ROM_end__];
define region RAM_region   = mem:[from __ICFEDIT_region_RAM_start__   to __ICFEDIT_region_RAM_end__];

define symbol __region_D_LDR_DATA_start__    = 0x00100000;
define symbol __region_D_LDR_DATA_end__      = 0x00101FFF;
define symbol __region_D_LDR_PRG_start__     = 0x00102000;
define symbol __region_D_LDR_PRG_end__       = 0x0011FFFF;

define symbol __region_SHARED_NONCACHE_BUFFER_start__ = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00040000;
define symbol __region_SHARED_NONCACHE_BUFFER_end__   = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00020000 - 1;
define symbol __region_NONCACHE_BUFFER_start__        = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00020000;
define symbol __region_NONCACHE_BUFFER_end__          = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 1;

define symbol __region_SYSTEM_RAM_start__        = SYSTEM_RAM_PRV_START;
define symbol __region_SYSTEM_RAM_end__          = SYSTEM_RAM_PRV_START + SYSTEM_RAM_PRV_LENGTH - 1;
define symbol __region_SYSTEM_RAM_MIRROR_start__ = SYSTEM_RAM_MIRROR_PRV_START;
define symbol __region_SYSTEM_RAM_MIRROR_end__   = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 1;

define symbol __region_XSPI0_CS0_MIRROR_start__ = xSPI0_CS0_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI0_CS0_MIRROR_end__   = xSPI0_CS0_SPACE_MIRROR_PRV_START + xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS1_MIRROR_start__ = xSPI0_CS1_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI0_CS1_MIRROR_end__   = xSPI0_CS1_SPACE_MIRROR_PRV_START + xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS0_MIRROR_start__ = xSPI1_CS0_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI1_CS0_MIRROR_end__   = xSPI1_CS0_SPACE_MIRROR_PRV_START + xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS1_MIRROR_start__ = xSPI1_CS1_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI1_CS1_MIRROR_end__   = xSPI1_CS1_SPACE_MIRROR_PRV_START + xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS0_MIRROR_start__       = CS0_SPACE_MIRROR_PRV_START;
define symbol __region_CS0_MIRROR_end__         = CS0_SPACE_MIRROR_PRV_START + CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS2_MIRROR_start__       = CS2_SPACE_MIRROR_PRV_START;
define symbol __region_CS2_MIRROR_end__         = CS2_SPACE_MIRROR_PRV_START + CS2_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS3_MIRROR_start__       = CS3_SPACE_MIRROR_PRV_START;
define symbol __region_CS3_MIRROR_end__         = CS3_SPACE_MIRROR_PRV_START + CS3_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS5_MIRROR_start__       = CS5_SPACE_MIRROR_PRV_START;
define symbol __region_CS5_MIRROR_end__         = CS5_SPACE_MIRROR_PRV_START + CS5_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS0_start__        = xSPI0_CS0_SPACE_PRV_START;
define symbol __region_XSPI0_CS0_end__          = xSPI0_CS0_SPACE_PRV_START + xSPI0_CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS1_start__        = xSPI0_CS1_SPACE_PRV_START;
define symbol __region_XSPI0_CS1_end__          = xSPI0_CS1_SPACE_PRV_START + xSPI0_CS1_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS0_start__        = xSPI1_CS0_SPACE_PRV_START;
define symbol __region_XSPI1_CS0_end__          = xSPI1_CS0_SPACE_PRV_START + xSPI1_CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS1_start__        = xSPI1_CS1_SPACE_PRV_START;
define symbol __region_XSPI1_CS1_end__          = xSPI1_CS1_SPACE_PRV_START + xSPI1_CS1_SPACE_PRV_LENGTH - 1;
define symbol __region_CS0_start__              = CS0_SPACE_PRV_START;
define symbol __region_CS0_end__                = CS0_SPACE_PRV_START + CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_CS2_start__              = CS2_SPACE_PRV_START;
define symbol __region_CS2_end__                = CS2_SPACE_PRV_START + CS2_SPACE_PRV_LENGTH - 1;
define symbol __region_CS3_start__              = CS3_SPACE_PRV_START;
define symbol __region_CS3_end__                = CS3_SPACE_PRV_START + CS3_SPACE_PRV_LENGTH - 1;
define symbol __region_CS5_start__              = CS5_SPACE_PRV_START;
define symbol __region_CS5_end__                = CS5_SPACE_PRV_START + CS5_SPACE_PRV_LENGTH - 1;

/************** SPI boot mode setting **************/
define symbol __region_LDR_PARAM_start__     = 0x60000000;
define symbol __region_LDR_PARAM_end__       = 0x6000004B;
define symbol __region_S_LDR_PRG_start__     = 0x6000004C;
define symbol __region_S_LDR_PRG_end__       = 0x6000604B;
define symbol __region_S_LDR_DATA_start__    = 0x6000604C;
define symbol __region_S_LDR_DATA_end__      = 0x6000804B;

define symbol __region_S_intvec_start__      = 0x60020000;
define symbol __region_S_intvec_end__        = 0x600200FF;
define symbol __region_S_RAM_start__         = 0x60070000;
define symbol __region_S_RAM_end__           = 0x6007FFFF;
/****************************************************/

define region D_LDR_DATA_region = mem:[from __region_D_LDR_DATA_start__   to __region_D_LDR_DATA_end__];
define region D_LDR_PRG_region  = mem:[from __region_D_LDR_PRG_start__   to __region_D_LDR_PRG_end__];

define region LDR_PARAM_region  = mem:[from __region_LDR_PARAM_start__   to __region_LDR_PARAM_end__];
define region S_LDR_PRG_region  = mem:[from __region_S_LDR_PRG_start__   to __region_S_LDR_PRG_end__];
define region S_LDR_DATA_region  = mem:[from __region_S_LDR_DATA_start__   to __region_S_LDR_DATA_end__];

define region S_intvec_region = mem:[from __region_S_intvec_start__  to __region_S_intvec_end__];
define region S_RAM_region = mem:[from __region_S_RAM_start__  to __region_S_RAM_end__];

define region SHARED_NONCACHE_BUFFER_region  = mem:[from __region_SHARED_NONCACHE_BUFFER_start__   to __region_SHARED_NONCACHE_BUFFER_end__];
define region NONCACHE_BUFFER_region  = mem:[from __region_NONCACHE_BUFFER_start__   to __region_NONCACHE_BUFFER_end__];

define region SYSTEM_RAM_region        = mem:[from __region_SYSTEM_RAM_start__  to __region_SYSTEM_RAM_end__ ];
define region SYSTEM_RAM_MIRROR_region = mem:[from __region_SYSTEM_RAM_MIRROR_start__  to __region_SYSTEM_RAM_MIRROR_end__ ];
define region XSPI0_CS0_MIRROR_region  = mem:[from __region_XSPI0_CS0_MIRROR_start__  to __region_XSPI0_CS0_MIRROR_end__ ];
define region XSPI0_CS1_MIRROR_region  = mem:[from __region_XSPI0_CS1_MIRROR_start__  to __region_XSPI0_CS1_MIRROR_end__ ];
define region XSPI1_CS0_MIRROR_region  = mem:[from __region_XSPI1_CS0_MIRROR_start__  to __region_XSPI1_CS0_MIRROR_end__ ];
define region XSPI1_CS1_MIRROR_region  = mem:[from __region_XSPI1_CS1_MIRROR_start__  to __region_XSPI1_CS1_MIRROR_end__ ];
define region CS0_MIRROR_region        = mem:[from __region_CS0_MIRROR_start__  to __region_CS0_MIRROR_end__ ];
define region CS2_MIRROR_region        = mem:[from __region_CS2_MIRROR_start__  to __region_CS2_MIRROR_end__ ];
define region CS3_MIRROR_region        = mem:[from __region_CS3_MIRROR_start__  to __region_CS3_MIRROR_end__ ];
define region CS5_MIRROR_region        = mem:[from __region_CS5_MIRROR_start__  to __region_CS5_MIRROR_end__ ];
define region XSPI0_CS0_region         = mem:[from __region_XSPI0_CS0_start__  to __region_XSPI0_CS0_end__ ];
define region XSPI0_CS1_region         = mem:[from __region_XSPI0_CS1_start__  to __region_XSPI0_CS1_end__ ];
define region XSPI1_CS0_region         = mem:[from __region_XSPI1_CS0_start__  to __region_XSPI1_CS0_end__ ];
define region XSPI1_CS1_region         = mem:[from __region_XSPI1_CS1_start__  to __region_XSPI1_CS1_end__ ];
define region CS0_region               = mem:[from __region_CS0_start__  to __region_CS0_end__ ];
define region CS2_region               = mem:[from __region_CS2_start__  to __region_CS2_end__ ];
define region CS3_region               = mem:[from __region_CS3_start__  to __region_CS3_end__ ];
define region CS5_region               = mem:[from __region_CS5_start__  to __region_CS5_end__ ];

define block LDR_PRG_RBLOCK with fixed order
                             { ro code section .loader_text_init object startup.o,
                               ro code object startup.o,
                               ro code object system.o,
                               ro code object bsp_clocks.o,
                               ro code object bsp_irq.o,
                               ro code object bsp_register_protection.o,
                               ro code object r_ioport.o,
                               ro code section .warm_start_init }
                               except { ro code section .intvec_init};
define block LDR_PRG_WBLOCK with fixed order
                             { rw code section .loader_text object startup.o,
                               rw code object startup.o,
                               rw code object system.o,
                               rw code object bsp_clocks.o,
                               rw code object bsp_irq.o,
                               rw code object bsp_register_protection.o,
                               rw code object r_ioport.o,
                               rw code section .warm_start }
                               except { rw code section .intvec};
define block LDR_DATA_ZBLOCK { section .bss object startup.o,
                               section .bss object system.o,
                               section .bss object bsp_clocks.o,
                               section .bss object bsp_irq.o,
                               section .bss object bsp_register_protection.o,
                               section .bss object r_ioport.o,
                               section .bss object bsp_io.o };
define block LDR_DATA_RBLOCK with fixed order
                             { section .data_init object startup.o,
                               section .data_init object system.o,
                               section .data_init object bsp_clocks.o,
                               section .data_init object bsp_irq.o,
                               section .data_init object bsp_register_protection.o,
                               section .data_init object r_ioport.o };
define block LDR_DATA_WBLOCK with fixed order
                             { section .data object startup.o,
                               section .data object system.o,
                               section .data object bsp_clocks.o,
                               section .data object bsp_irq.o,
                               section .data object bsp_register_protection.o,
                               section .data object r_ioport.o };

define block VECTOR_RBLOCK with alignment = 32 { ro code section .intvec_init};
define block VECTOR_WBLOCK with alignment = 32 { rw code section .intvec};
define block USER_PRG_RBLOCK  { ro code };
define block USER_PRG_WBLOCK  { rw code };
define block USER_DATA_ZBLOCK { section .bss };
define block USER_DATA_RBLOCK with fixed order
                              { section .data_init,
                                section __DLIB_PERTHREAD_init,
                                section .rodata_init,
                                section .version_init };
define block USER_DATA_WBLOCK with fixed order
                              { section .data,
                                section __DLIB_PERTHREAD,
                                section .rodata,
                                section .version };
define block SHARED_NONCACHE_BUFFER_ZBLOCK with alignment = 32 { section .shared_noncache_buffer* };
define block NONCACHE_BUFFER_ZBLOCK with alignment = 32 { section .noncache_buffer* };

initialize manually  { ro code object startup.o,
                       ro code object system.o,
                       ro code object bsp_clocks.o,
                       ro code object bsp_irq.o,
                       ro code object bsp_register_protection.o,
                       ro code object r_ioport.o,
                       ro code section .intvec,
                       ro code section .warm_start,
                       ro code,
                       section .data,
                       section __DLIB_PERTHREAD,
                       section .rodata,
                       section .version };

do not initialize   { section .noinit,
                      section .bss,
                      section .shared_noncache_buffer*,
                      section .noncache_buffer*,
                      rw section HEAP,
                      rw section .stack*,
                      rw section .sys_stack,
                      rw section .svc_stack,
                      rw section .irq_stack,
                      rw section .fiq_stack,
                      rw section .und_stack,
                      rw section .abt_stack };

place at address mem: __ICFEDIT_intvec_start__ { block VECTOR_WBLOCK };
place in RAM_region        { block USER_PRG_WBLOCK };
place in RAM_region        { readwrite };
place in RAM_region        { block USER_DATA_WBLOCK,
                             block USER_DATA_ZBLOCK };
place in RAM_region        { rw section HEAP,
                             rw section .stack* };
place in D_LDR_DATA_region { section LDR_DATA_WBLOCK, block LDR_DATA_WBLOCK,
                             section LDR_DATA_ZBLOCK, block LDR_DATA_ZBLOCK };
place at start of D_LDR_PRG_region { block LDR_PRG_WBLOCK };
place in D_LDR_PRG_region  { rw section .sys_stack,
                             rw section .svc_stack,
                             rw section .irq_stack,
                             rw section .fiq_stack,
                             rw section .und_stack,
                             rw section .abt_stack };
place in LDR_PARAM_region { readonly section .loader_param };
place in S_LDR_PRG_region { block LDR_PRG_RBLOCK };
place in S_LDR_DATA_region { section LDR_DATA_RBLOCK, block LDR_DATA_RBLOCK };
place in S_intvec_region { block VECTOR_RBLOCK };
place in ROM_region   { block USER_PRG_RBLOCK, readonly };
place in S_RAM_region { block USER_DATA_RBLOCK };
place in SHARED_NONCACHE_BUFFER_region { block SHARED_NONCACHE_BUFFER_ZBLOCK };
place in NONCACHE_BUFFER_region { block NONCACHE_BUFFER_ZBLOCK };
place in SYSTEM_RAM_region { };
place in SYSTEM_RAM_MIRROR_region { };
place in XSPI0_CS0_MIRROR_region { };
place in XSPI0_CS1_MIRROR_region { };
place in XSPI1_CS0_MIRROR_region { };
place in XSPI1_CS1_MIRROR_region { };
place in CS0_MIRROR_region { };
place in CS2_MIRROR_region { };
place in CS3_MIRROR_region { };
place in CS5_MIRROR_region { };
place in XSPI0_CS0_region { };
place in XSPI0_CS1_region { };
place in XSPI1_CS0_region { };
place in XSPI1_CS1_region { };
place in CS0_region { };
place in CS2_region { };
place in CS3_region { };
place in CS5_region { };