
#ifndef _AM600_IF_H_
#define _AM600_IF_H_
#include "BaseDef.h"


#define MA600_READ_ANGLE                     0x0000             /**< read angle */
#define MA600_READ_MultiTurn                 0x00000000         /**< read multi turn */


typedef struct
{
  uint16_t singleTurn;    /* 单圈角度值，16位 */
  uint32_t multiTurn;     /* 圈数计数 */
  uint32_t AlmCode;       /* 状态信息，2位 */
} MA600_Angle_T;


uint8_t hApi_MA600_readMagAlphaAngleWithParityBitCheck(uint32_t *pdata);
uint8_t hApi_MA600_readMagAlphaRegister(uint8_t address);
uint8_t hApi_MA600_writeMagAlphaRegister(uint8_t address, uint8_t value);
uint8_t hApi_MA600_Init(void);   

uint8_t hApi_MA600_PosDecoding(ENCODER *pEnc,ENCODER *pEnc2);
#endif

