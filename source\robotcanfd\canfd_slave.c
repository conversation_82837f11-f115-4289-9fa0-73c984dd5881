/**
 * @file canfd_slave.c
 * @brief CANFD从站功能模块实现
 */

#include <string.h>
#include "canfd_slave.h"
#include "firmware.h"
#include "r_can_api.h"
#include "RegMngr.h"    
#include "bsp_timer.h"  
#include "ComUart.h"



#define DUPLICATE_PACKET_TIME_MS 100

extern uint8_t Canfd_Ota_Buffer[CANFD_OTA_BUFFER_SIZE];
extern uint8_t Canfd_Ota_RxBuffer[CANFD_OAT_MAX_RDATE];

/**
 * @brief 从站内部状态
 */
typedef struct {
    uint8_t initialized;                     ///< 初始化标志
    uint8_t slaveAddress;                    ///< 从站地址
    uint8_t seq;                             ///< 当前包序号
    void *canfdInterface;                    ///< CANFD接口句柄
    
    CANFD_MotorControlCallback_t controlCallback;   ///< 电机控制回调函数
    
    uint32_t lastRxTime;                     ///< 最后一次接收时间
    uint32_t lastTxTime;                     ///< 最后一次发送时间
    uint8_t  ComStatus;                      ///< 通信状态  
    
    /* 重复包检测相关 */
    CANFD_Frame lastRxFrame;
    CANFD_Frame lastTxFrame;
    uint32_t timestamp;
} CANFD_SlaveState;


static void PrepareFrameHeader(CANFD_Frame *frame, CANFD_Frame *rxframe);
/* 从站内部状态变量 */
static CANFD_SlaveState gState = {0};

/* 默认回调函数 */
static void DefaultControlCallback(CANFD_MotorControl *controlData) {
    /* 默认实现为空 */
    (void)controlData;
}


/**
 * @brief 获取当前系统时间（毫秒）
 * 
 * @return uint32_t 当前时间戳
 */
float CanFdTimeMs;
uint32_t Canfd_GetTimeMs(void) {
    /* 这里应该调用实际平台的时间获取函数，例如：
     * return HAL_GetTick(); // STM32 HAL库
     * 或者
     * return xTaskGetTickCount() * portTICK_PERIOD_MS; // FreeRTOS
     */
    CanFdTimeMs = (float)bsp_gettimercnt()/CMTW1_PPR_MS;
    return (uint32_t)CanFdTimeMs; /* 占位符，实际实现需要替换 */
}

/**
 * @brief 超时检测
 * 
 * @return 
 */
uint8_t Canfd_TimeoutCheck(uint32_t timeout, uint32_t lastTime) {

    if(Canfd_GetTimeMs() - lastTime > timeout)
    {
        return 1;
    }   
    
    return 0; /* 占位符，实际实现需要替换 */
}

/**
 * @brief 初始化从站
 * 
 * @param config 从站配置信息
 * @return CANFD_Error 错误码
 */
CANFD_Error CANFD_SlaveInit(CANFD_SlaveConfig *config) {
    if (config == NULL) {
        return CANFD_INVALID_PARAM;
    }
    
    if (config->slaveAddress == 0 || config->slaveAddress > 31) {
        return CANFD_INVALID_PARAM;
    }
    
    /* 初始化内部状态 */
    memset(&gState, 0, sizeof(CANFD_SlaveState));
    gState.slaveAddress = config->slaveAddress;
    gState.canfdInterface = config->canfdInterface;
    gState.controlCallback = DefaultControlCallback;
    gState.seq = 0;
    gState.initialized = 1;
    
    return CANFD_OK;
}

/**
 * @brief 设置从站通信状态
 * 
 * @param config 从站配置信息
 * @return CANFD_Error 错误码
 */
CANFD_Error CANFD_SlaveStatus(CANFD_SlaveConfig *config) {

    gState.ComStatus = config->ComStatus;
    return CANFD_OK;
}


/**
 * @brief 注册电机控制回调函数
 * 
 * @param callback 回调函数指针
 * @return CANFD_Error 错误码
 */
CANFD_Error CANFD_RegisterControlCallback(CANFD_MotorControlCallback_t callback) {
    if (!gState.initialized) {
        return CANFD_NOT_INITIALIZED;
    }
    
    if (callback != NULL) {
        gState.controlCallback = callback;
    } else {
        gState.controlCallback = DefaultControlCallback;
    }
    
    return CANFD_OK;
}


/**
 * @brief 解析CANFD帧ID获取相关信息
 * 
 * @param id CAN ID
 * @param direction 输出：方向
 * @param isolation 输出：隔离码
 * @param idCode 输出：ID子码
 */
static void ParseCanID(uint32_t id, uint8_t *direction, uint8_t *isolation, uint8_t *idCode) {
    CANFD_StandardID *stdId = (CANFD_StandardID *)&id;
    
    if (direction != NULL) {
        *direction = stdId->direction;
    }
    
    if (isolation != NULL) {
        *isolation = stdId->isolation;
    }
    
    if (idCode != NULL) {
        *idCode = stdId->id_code;
    }
}

/**
 * @brief 构造CANFD标准帧ID
 * 
 * @param direction 方向
 * @param isolation 隔离码
 * @param idCode ID子码
 * @return uint32_t 构造的CAN ID
 */
static uint32_t BuildCanID(uint8_t direction, uint8_t isolation, uint8_t idCode) {
    CANFD_StandardID stdId = {0};
    
    stdId.direction = direction & 0x03;
    stdId.isolation = isolation & 0x07;
    stdId.id_code = idCode & 0x1F;
    stdId.reserved = 0;
    
    return *((uint32_t *)&stdId);
}

/**
 * @brief 判断接收的数据是否为重复包
 * 
 * @param seq 包序号
 * @param cmd 命令码
 * @param type 包类型
 * @param srcAddr 源地址
 * @return int 1:重复包 0:非重复包
 */
static int IsDuplicatePacket(CANFD_SlaveState *gState,CANFD_Frame *CurrentFrame) {
    uint32_t currentTime = Canfd_GetTimeMs();
    CANFD_Frame *lastRxFrame = &gState->lastRxFrame;

    /* 判断是否是100ms内的重复包 */
    if (lastRxFrame->header.seq == CurrentFrame->header.seq && 
        lastRxFrame->header.cmd == CurrentFrame->header.cmd && 
        lastRxFrame->header.type == CurrentFrame->header.type &&
        lastRxFrame->header.src == CurrentFrame->header.src &&
        (currentTime - gState->timestamp) < DUPLICATE_PACKET_TIME_MS) { 
        return 1;
    }
    
    /* 更新上一个包的信息 */
    lastRxFrame->header.seq = CurrentFrame->header.seq;
    lastRxFrame->header.cmd = CurrentFrame->header.cmd;
    lastRxFrame->header.type = CurrentFrame->header.type;
    lastRxFrame->header.src = CurrentFrame->header.src;
    memcpy(lastRxFrame->data, CurrentFrame->data, CurrentFrame->header.length);
    gState->timestamp = currentTime;
    return 0;
}

/**
 * @brief 判断是否是发给当前从站的包
 * 
 * @param destAddr 目标地址
 * @return int 1:是发给当前从站的包 0:不是发给当前从站的包
 */
static int IsForThisSlave(uint8_t destAddr) {
    /* 地址匹配或广播地址(0) */
    return (destAddr == gState.slaveAddress || destAddr == 0);
}

/**
 * @brief 从数据缓冲区读取int32_t值（小端模式）
 * 
 * @param data 数据缓冲区
 * @return int32_t 读取的值
 */
static int32_t ReadInt32(const uint8_t *data) {
    int32_t value = 0;
    value |= (int32_t)data[0];
    value |= (int32_t)data[1] << 8;
    value |= (int32_t)data[2] << 16;
    value |= (int32_t)data[3] << 24;
    return value;
}

/**
 * @brief 从数据缓冲区读取int16_t值（小端模式）
 * 
 * @param data 数据缓冲区
 * @return int16_t 读取的值
 */
static int16_t ReadInt16(const uint8_t *data) {
    int16_t value = 0;
    value |= (int16_t)data[0];
    value |= (int16_t)data[1] << 8;
    return value;
}

/**
 * @brief 从数据缓冲区读取uint16_t值（小端模式）
 * 
 * @param data 数据缓冲区
 * @return uint16_t 读取的值
 */
static uint16_t ReadUint16(const uint8_t *data) {
    uint16_t value = 0;
    value |= (uint16_t)data[0];
    value |= (uint16_t)data[1] << 8;
    return value;
}

/**
 * @brief 向数据缓冲区写入int32_t值（小端模式）
 * 
 * @param data 数据缓冲区
 * @param value 要写入的值
 */
static void WriteInt32(uint8_t *data, int32_t value) {
    data[0] = (uint8_t)(value & 0xFF);
    data[1] = (uint8_t)((value >> 8) & 0xFF);
    data[2] = (uint8_t)((value >> 16) & 0xFF);
    data[3] = (uint8_t)((value >> 24) & 0xFF);
}

/**
 * @brief 向数据缓冲区写入int16_t值（小端模式）
 * 
 * @param data 数据缓冲区
 * @param value 要写入的值
 */
static void WriteInt16(uint8_t *data, int16_t value) {
    data[0] = (uint8_t)(value & 0xFF);
    data[1] = (uint8_t)((value >> 8) & 0xFF);
}

/**
 * @brief 向数据缓冲区写入uint16_t值（小端模式）
 * 
 * @param data 数据缓冲区
 * @param value 要写入的值
 */
static void WriteUint16(uint8_t *data, uint16_t value) {
    data[0] = (uint8_t)(value & 0xFF);
    data[1] = (uint8_t)((value >> 8) & 0xFF);
}

/**
 * @brief 解析电机控制模式参数
 * 
 * @param data 数据缓冲区
 * @param offset 数据起始偏移
 * @param controlData 输出：控制参数
 * @return int 解析的数据长度
 */
#pragma optimize=none 
static int ParseMotorControlModeParams(const uint8_t *data, int offset, CANFD_MotorControl *controlData) {
    int parsedLen = 0;
    

    /* 根据控制模式解析不同的参数 */
    switch (controlData->controlMode) {
        case MOTOR_MODE_POSITION:
            /* 解析位置模式参数 */
            controlData->targetPosition = ReadUint16(&data[offset]);
            parsedLen = 2; /* 位置参数占4字节 */
            break;
            
        case MOTOR_MODE_VELOCITY:
            /* 解析速度模式参数 */
            controlData->targetVelocity = ReadUint16(&data[offset]);
            // controlData->accelerationTime = ReadUint16(&data[offset + 2]);
            parsedLen = 4; /* 速度参数占6字节(速度4字节+加速时间2字节) */
            break;
            
        case MOTOR_MODE_TORQUE:
            /* 解析转矩模式参数 */
            controlData->targetTorque = ReadInt16(&data[offset]);
            parsedLen = 2; /* 转矩参数占2字节 */
            break;
            
        case MOTOR_MODE_IMPEDANCE:
            /* 解析导纳模式参数 */
            controlData->targetPosition = ReadUint16(&data[offset]);
            controlData->targetVelocity = ReadUint16(&data[offset + 2]);
            controlData->torqueFeedforward = ReadInt16(&data[offset + 4]);
            controlData->positionKp = ReadUint16(&data[offset + 6]);
            controlData->velocityKd = ReadUint16(&data[offset + 8]);
            parsedLen = 10; /* 导纳模式参数占10字节(位置2字节+速度2字节+转矩前馈2字节+位置增益2字节+速度增益2字节) */
            break;
            
        case MOTOR_MODE_ZEROING:
            /* 解析回零模式参数 */
            controlData->zeroingMethod = data[offset];
            controlData->currentLimit = ReadUint16(&data[offset + 1]);
            controlData->highSpeed = ReadUint16(&data[offset + 3]);
            controlData->lowSpeed = ReadUint16(&data[offset + 5]);
            controlData->maxTime = ReadUint16(&data[offset + 7]);           
            parsedLen = 9; /* 回零模式参数占9字节 */
            break;
            
        default:
            /* 对于默认模式、VF模式等，暂不处理其特定参数 */
            /* 解析位置模式参数 */
            controlData->targetPosition = ReadUint16(&data[offset]);
            parsedLen = 2; /* 位置参数占2字节 */
            break;
    }
    
    if(parsedLen != 0)
    {
       controlData->TargetSetFlag = 1;
    }
    
    return parsedLen;
}


/**
 * @brief 解析电机控制命令
 * 
 * @param data 数据缓冲区
 * @param length 数据长度
 * @return CANFD_Error 错误码
 */
#pragma optimize=none 
static CANFD_Error ParseMotorControlCommand(const uint8_t *data, uint8_t length) {
    uint8_t motorCount;
    uint8_t parseMethod;
    uint8_t motorParamLen;
    uint8_t deviceAddr;
    uint8_t motorIdx;
    uint8_t runMode;
    uint8_t controlMode;
    int offset = 0;
    int i;
    uint8_t result = 1;

    
    if (length < 4) {
        return CANFD_INVALID_PARAM; /* 数据太短，无效 */
    }
    
    /* 解析Data0：获取电机控制对象数量和解析方式 */
    motorCount = data[0] & 0x07;        /* 低3位表示电机控制对象数 */
    parseMethod = (data[0] >> 6) & 0x03; /* 高2位表示解析方式 */
    
    if (parseMethod != 0) {
        return CANFD_ERROR; /* 目前只支持顺序式解析 */
    }
    
    offset = 1; /* 跳过Data0 */


    /* 逐个解析电机控制对象 */
    for (i = 0; i < motorCount; i++) {
        /* 确保还有足够的数据用于解析 */
        if (offset >= length) {
            return CANFD_ERROR;
        }
         
        /* 读取本电机相关参数所用字节数 */
        motorParamLen = data[offset];
        
        /* 确保有足够的数据用于解析当前电机的参数 */
        if (offset + motorParamLen > length) {
            return CANFD_ERROR;
        }
         
        offset++;
        /* 读取设备电机号 */
        deviceAddr = data[offset] & 0x1F; /* 低5位为设备地址 */
        motorIdx = (data[offset] >> 5) & 0x07; /* 高3位为电机序号 */
        offset++;
        
        /* 判断是否是发给当前从站的控制 */
        if (deviceAddr != gState.slaveAddress) {        
            /* 不是发给当前从站的，跳过该电机控制参数 */
            offset += motorParamLen - 2; /* -1是因为已经读取了设备电机号 */
            continue;
        }
        
        //电机控制参数长度小于3，无效,最小为3，用于设置工作模式
        if(motorParamLen < 3)
        {
            return CANFD_ERROR;
        }   

        /* 读取运行及模式 */
        runMode = data[offset++];
        
        /* 解析控制模式和运行命令 */
        controlMode = (runMode >> 4) & 0x0F; /* 高4位为控制模式 */
        runMode = runMode & 0x07; /* 低3位为运行命令 */
        
        /* 清空控制数据结构并填充基本信息 */
        HumanRotCtrl.controlData.controlMode = controlMode;
        HumanRotCtrl.controlData.runCommand = runMode;
        
        /* 只有运行命令为MOTOR_CMD_RUN时才需要解析控制模式参数 */
        if (runMode == MOTOR_CMD_RUN && motorParamLen >= 5) {
            /* 解析对应模式的参数 */
            int paramLen = ParseMotorControlModeParams(data, offset, &HumanRotCtrl.controlData);
            offset += paramLen;
        }

        //已经解析到本从站的电机控制数据
        result = 0;
        break;
    }

    
    if(result == 0)
    {
        /* 调用控制回调函数 */
        if (gState.controlCallback != NULL) {
            gState.controlCallback(&HumanRotCtrl.controlData);
        }
        return CANFD_OK;
    }
    else
    {
        return CANFD_NONEED_RESPONSE;
    }
}


/**
 * @brief 解析数据字典写入命令
 * 
 * @param rxdata 接收到的数据
 * @param txdata 发送的数据
 * @param length 数据长度
 * @return CANFD_Error 错误码
 */
static CANFD_Error ParseWriteDictionary(const uint8_t *rxdata, uint8_t *txdata, uint8_t length)
{
   uint8_t deviceAddr;
   uint16_t ParamEntry = 0,ParamLen = 0,ParamNum = 0,Index = 0;
   uint8_t SubIndex = 0;
   uint8_t WriteState = 0, WriteStateLock = 0;

   ParamNum = rxdata[1];
   ParamEntry = 2;  
   while(ParamNum)
   {
        ParamLen = *(uint32_t *)&(rxdata[ParamEntry]) & 0xFF;   
        if(ParamLen & 0x80)
        {
            ParamLen =  (ParamLen-0x80) /8 + 0x80;
        }
        else
        {
            ParamLen = ParamLen /8;
        }

        SubIndex =  (*(uint32_t *)&(rxdata[ParamEntry]) & 0xFF00)>>8;
        Index = ((*(uint32_t *)&(rxdata[ParamEntry]) & 0xFFFF0000)>>16);    

        WriteState = PrmEcatObjWrite( Index, SubIndex, ParamLen, 
                                (uint16_t *)&(rxdata[ParamEntry+4]),FALSE);   

        // memcpy(txdata[ParamEntry], rxdata[ParamEntry], ParamLen+4);
        // if(WriteState != 0)
        // {
        //     txdata[ParamEntry] = 0xFF;
        //     txdata[ParamEntry+4] = WriteState;
        // }

        ParamLen = ParamLen & 0x7F;
        ParamEntry = ParamEntry + 4 + ParamLen;
        ParamNum--;   
   } 

    memcpy(txdata, rxdata, length);

   return CANFD_OK;
}



/**
 * @brief 解析数据字典读取命令
 * 
 * @param rxdata 接收到的数据
 * @param txdata 发送的数据
 * @param length 数据长度
 * @return CANFD_Error 错误码
 */
static CANFD_Error ParseReadDictionary(const uint8_t *rxdata, uint8_t *txdata, uint8_t *length)
{
   uint8_t deviceAddr;
   uint16_t ParamEntry = 0,ParamLen = 0,ParamNum = 0,Index = 0, TotalNum = 0;
   uint8_t SubIndex = 0;
   uint8_t ReadState = 0, ReadStateLock = 0;

   ParamNum = rxdata[1];
   TotalNum = rxdata[1];

   txdata[0] = rxdata[0];
   txdata[1] = rxdata[1];

   ParamEntry = 6;  
   while(ParamNum)
   {
        ParamLen = (*(UINT32 *)&(rxdata[2+4*(TotalNum-ParamNum)]) & 0xFF)/8;  
        Index = ((*(UINT32 *)&(rxdata[2+4*(TotalNum-ParamNum)]) & 0xFFFF0000)>>16) ;
        SubIndex = (*(UINT32 *)&(rxdata[2+4*(TotalNum-ParamNum)]) & 0xFF00)>>8;

        if(Index >= 0x2000)
        {
            ReadState = PrmEcatObjRead( Index, SubIndex, ParamLen, 
                                    (UINT16 *)&(txdata[ParamEntry]),FALSE);
            *(UINT32 *)&(txdata[ParamEntry-4]) = *(UINT32 *)&(rxdata[2+4*(TotalNum-ParamNum)]);    
            // if(ReadState != 0)
            // {
            //     *(UINT8 *)&(txdata[ParamEntry-4]) =  0xFF;  //   if the len is 0xFF, the read is occur fault
            //     txdata[ParamEntry]  = ReadState;            //  and the lower byte is the error code
            // }

            ReadStateLock = ReadState;
            
            ParamEntry = ParamEntry + 4 + ParamLen;
            ParamNum--;            
        }
        else
        {
            int8_t VerIndex = -1;
            for(uint8_t i = 0 ; i< VersionTblNum; i++)
            {
              if(Index == VersionTbl[i].Index)
              {
                VerIndex = i;
                break;
              }
            }

            if(VerIndex == -1)
            {
                VerIndex = 0;
            }
           
            {               
                ParamLen =  VersionTbl[VerIndex].Length - 1;
                
                for(UINT8 i = 0;i< ParamLen;i++)
                {
                  (txdata[ParamEntry + i]) =  *(VersionTbl[VerIndex].pName + i);
                }
                *(UINT32 *)&(txdata[ParamEntry-4]) = *(UINT32 *)&(rxdata[2+4*(TotalNum-ParamNum)]);   
                *(UINT8 *)&(txdata[ParamEntry-4]) =  ParamLen;       
            } 
            ParamEntry = ParamEntry + 4 + ParamLen;
            ParamNum--;    
        }

   } 

   *length = ParamEntry-4;


   return CANFD_OK;
}

/**
 * @brief 解析固件数据
 * 
 * @param data 数据缓冲区
 * @param length 数据长度
 * @return CANFD_Error 错误码
 */
#pragma optimize=none 
static CANFD_Error ParseOtaData(const CANFD_Frame *frame) {
    uint8_t deviceAddr;
    
    if (frame->header.length < 2 ) {
        return CANFD_INVALID_PARAM; /* 数据太短，无效 */
    }

    #if 0
    deviceNum = frame->data[0] & 0x03; /* 低2位为设备地址 */ 

    if(deviceNum != s_update_state.var.device_id)
    {
        return CANFD_INVALID_PARAM;
    }
    #endif

    uint8_t parket_id = (frame->data[0] >> 6) & 0x03;
    uint8_t chunk_size = frame->header.length -1;

    if((parket_id != s_update_state.var.packet_id)|| (chunk_size != s_update_state.var.chunk_size))
    {
        return CANFD_INVALID_PARAM;
    }

    return CANFD_OK;
}
/**
 * @brief 处理接收到的CANFD帧
 * 
 * @param frame 接收到的CANFD帧
 * @return CANFD_Error 错误码
 */
CANFD_Error CANFD_ProcessRxFrame(CANFD_Frame *frame) {
    uint8_t direction, isolation, idCode;
    
    uint8_t OpCode;
    if (!gState.initialized) {
        return CANFD_NOT_INITIALIZED;
    }
    
    if (frame == NULL) {
        return CANFD_INVALID_PARAM;
    }
    
    /* 解析CAN ID */
    ParseCanID(frame->can_id, &direction, &isolation, &idCode);
    
    /* 检查方向，只处理主站到从站的包 */
    if (direction != CANFD_DIR_MASTER_TO_SLAVE && direction != CANFD_DIR_POINT_TO_POINT) {
        return CANFD_NONEED_RESPONSE; /* 不是主到从的包，忽略 */
    }

    /* 判断是否是发给当前从站的包 */
    if (!IsForThisSlave(idCode)) {
        return CANFD_NONEED_RESPONSE; /* 不是发给当前从站的包，忽略 */
    }
    
    /* 更新最后接收时间 */
    gState.lastRxTime = Canfd_GetTimeMs();
    
    #if 0
    /* 检查是否是重复包 */
    if (IsDuplicatePacket(&gState,frame)) {
        /* 根据协议，设置1类命令包仅处理一次，其他类型继续处理 */
        if (frame->header.type == PACKET_TYPE_SET1) {
            return CANFD_NONEED_RESPONSE; /* 对设置1类命令包，是重复包则忽略 */
        }
    }
    #endif

    /* 处理不同类型的包 */
    switch (frame->header.type) {
        case PACKET_TYPE_READ:
            /* 处理读取命令 -不需要解析*/
            switch (frame->header.cmd)
            {
            case CMD_DEVICE_STATUS:   /* 该设备为从站 ：主站查询该设备状态 */
            case CMD_DEVICE_VERSION:  /*该设备为从站 ：主站查询该设备版本 */
                /* code */
                break;
            case CMD_FIRMWARE_UPDATE: /* 主站查询该设备更新状态*/
                /* code */
                break;
            default:
                break;
            }
            break;
        case PACKET_TYPE_SET1:  
            /* 处理设置1命令 */
            switch (frame->header.cmd)
            {
            case CMD_DEVICE_STATUS:  /*该设备为主站 ：主动上传设备状态到该设备 */
            case CMD_DEVICE_VERSION: /*该设备为主站 ：主动上传版本到该设备 */     
                /* code */  
                break;
            case CMD_FIRMWARE_UPDATE: /* 该设备为主站 ：设备固件写入和启动更新 */
                /* code */
                break;
            case CMD_MOTOR_CONTROL: /* 电机控制命令 */
                /* code */
                break;
            default:
                break;
            }
            break;
        case PACKET_TYPE_SET2:
            /* 处理设置2命令 */
            switch (frame->header.cmd)
            {
            case CMD_DEVICE_STATUS: /* 查询设备状态 */
                /* code */
                break;
            case CMD_DEVICE_VERSION: /* 查询设备版本 */     
                /* code */  
                break;
            case CMD_FIRMWARE_UPDATE: /* 设备备固件写入和启动更新 */
                /* code */
                break;
            case CMD_MOTOR_CONTROL: /* 电机控制命令 */
                /* code */
                 return ParseMotorControlCommand(frame->data, frame->header.length);
                break;
            default:
                break;
            }            
            break;
        case PACKET_TYPE_RESP:
            /* 处理应答命令 */
            switch (frame->header.cmd)
            {
            case CMD_DEVICE_STATUS: /* 查询设备状态 */
                /* code */
                break;
            case CMD_DEVICE_VERSION: /* 查询设备版本 */     
                /* code */  
                break;
            case CMD_FIRMWARE_UPDATE: /* 设备备固件写入和启动更新 */
                /* code */
                OpCode = (frame->data[0] >> 2) & 0x03;

                switch(OpCode)
                {
                    case FIRMWARE_OP_INFO:
                        break;
                    case FIRMWARE_OP_START_UPDATE:
                        break;
                    case FIRMWARE_OP_DATA:
                        // todo 处理数据 判断数据对不对
                        if(ParseOtaData(frame) == CANFD_OK)
                        {
                            s_update_state.var.waiting_for_response = false;
                            memcpy(&Canfd_Ota_RxBuffer[0], &frame->data[1], s_update_state.var.chunk_size);
                        }    
                        return CANFD_NONEED_RESPONSE; /* 不需要回复*/
                        break;
                    case FIRMWARE_OP_STATUS:
                        break;
                    default:
                        break;
                }                

                break;
            case CMD_MOTOR_CONTROL: /* 电机控制命令 */
                /* code */
                break;
            default:
                break;
            }            
            break;          
        default:
            break;
    }
    return CANFD_OK;
}

/**
 * @brief 准备CANFD帧头部
 * 
 * @param frame 输出：CANFD帧
 * @param cmd 命令码
 * @param type 包类型
 * @param destAddr 目标地址
 * @param dataLen 数据长度
 */
static void PrepareFrameHeader(CANFD_Frame *frame, CANFD_Frame *rxframe) {
    /* 初始化帧数据 */
    memset(frame, 0, sizeof(CANFD_Frame));
    
    /* 设置CAN ID，从站到主站方向 */
    if(rxframe->header.src != 0x01)
    {
        frame->can_id = BuildCanID(CANFD_DIR_POINT_TO_POINT, 0x07, gState.slaveAddress);
    }
    else
    {
        frame->can_id = BuildCanID(CANFD_DIR_SLAVE_TO_MASTER, 0x07, gState.slaveAddress);
    }

    if(rxframe->header.type == PACKET_TYPE_READ || rxframe->header.type == PACKET_TYPE_SET1)
    {
        frame->header.type = PACKET_TYPE_RESP;
    }
    else
    {
        frame->header.type = rxframe->header.type;
    }
    
    /* 设置帧头 */
    frame->header.seq = gState.seq;  
    gState.seq = (gState.seq + 1) & 0x3F;  /* 自增序号，范围0-63（6位） */
    frame->header.src = gState.slaveAddress;
    frame->header.dest = rxframe->header.dest;
    frame->header.cmd = rxframe->header.cmd;
    frame->header.res = 0;
    /* 设置帧属性 */
    frame->id_mode = 0; /* 标准帧 */
    
    /* 更新最后发送时间 */
    gState.lastTxTime = Canfd_GetTimeMs();
}




/**
 * @brief 发送CANFD帧
 * 
 * @param frame 要发送的帧
 * @return CANFD_Error 错误码
 */
static CANFD_Error SendFrame(CANFD_Frame *frame) {
    /* 这里需要调用具体平台的CANFD发送函数，例如：
     * HAL_FDCAN_AddMessageToTxFifoQ(gState.canfdInterface, &txHeader, frame->data); // STM32 HAL库
     */
    
    /* 返回默认成功，实际实现中需替换 */
    return CANFD_OK;
}

/**
 * @brief 打包电机状态信息
 * 
 * @param frame 输出：CANFD帧
 * @param status 电机状态信息
 * @return uint8_t 打包的数据长度
 */
static uint8_t PackMotorStatus(CANFD_Frame *frame, CANFD_MotorStatus *status) {
    uint8_t *data = frame->data;
    uint8_t deviceAddr = gState.slaveAddress;
    uint8_t motorIdx = 0; /* 默认电机索引为0 */
    uint8_t dataLen = 0;
    
    /* Data0: 电机数量和解析方式 */
    data[dataLen++] = 0x01; /* 1个电机，顺序解析方式 */
    
    /* Data1: 电机状态参数长度 */
    data[dataLen++] = 15; /* 自身1字节 + 电机号1字节 + 状态1字节 + 校零状态1字节 + 报警3字节 + 电流2字节 + 电压2字节 + 速度2字节 + 位置2字节 = 15字节 */
    
    /* Data2: 设备电机号 = 设备地址 + 电机序号 */
    data[dataLen++] = (deviceAddr & 0x1F) | ((motorIdx & 0x07) << 5);
    
    /* Data3: 电机运行状态及模式 */
    data[dataLen++] = ((uint8_t)status->runningState & 0x0F) | ((status->controlMode & 0x0F) << 4);
    
    /* Data4: 校零状态 */
    data[dataLen++] = status->zeroingState & 0x03;
    
    /* Data5-7: 电机报警标志(3字节) */
    data[dataLen++] = status->alarmFlags.alarm1;
    data[dataLen++] = status->alarmFlags.alarm2;
    data[dataLen++] = status->alarmFlags.alarm3;
    
    /* Data8-9: 电机有效电流(2字节) */
    WriteInt16(&data[dataLen], status->currentQ);
    dataLen += 2;
    
    /* Data10-11: 电机母线电压(2字节) */
    WriteUint16(&data[dataLen], status->voltageQ);
    dataLen += 2;
    
    /* Data12-13: 电机当前速度(2字节) */
    WriteUint16(&data[dataLen], status->velocityQ);
    dataLen += 2;
    
    /* Data14-15: 电机当前位置(2字节) */
    WriteUint16(&data[dataLen], status->positionQ);
    dataLen += 2;

    /* Data16-17: 电机当前扭矩(2字节) */
    WriteUint16(&data[dataLen], status->torqueQ);
    dataLen += 2;
    
    /* Data18-19: 电机扭矩指令(2字节) */   
    WriteUint16(&data[dataLen], status->torquefdbQ);
    dataLen += 2;   
    
    return dataLen;
}


/**
 * @brief 打包设备信息
 * 
 * @param frame 输出：CANFD帧
 * @param status 设备信息
 * @return uint8_t 打包的数据长度
 */
static uint8_t PackDeviceStatus(CANFD_Frame *frame, CANFD_MotorStatus *status) {
    uint8_t *data = frame->data;
    uint8_t dataLen = 0;
    
    /* Data0: app工程 或boot 工程 */
    data[dataLen++] = status->AppOrBoot; /*0：app  1：boot */
    
    /* Data1: 设备是否就绪 */
    data[dataLen++] = status->DevIsRready; /* 0：not ready  1：ready */
    
    return dataLen;
}

/**
 * @brief 打包设备版本信息
 * 
 * @param frame 输出：CANFD帧
 * @param status 设备信息
 * @return uint8_t 打包的数据长度
 */
static uint8_t PackFirmwareInfo(CANFD_Frame *frame, CANFD_MotorStatus *status) {
    uint8_t *data = frame->data;
    uint8_t dataLen = 0;
    
    /* Data0: app工程 或boot 工程 */
    data[dataLen++] = status->DevHardVersion; /*0：app  1：boot */
    
    /* Data1: 设备是否就绪 */
    data[dataLen++] = status->DevBootVersion; /* 0：not ready  1：ready */

    /* Data2: 设备是否就绪 */
    data[dataLen++] = status->DevAppVersion & 0xFF; /* BIT[9:0]  app version */   

    /* Data2: 设备是否就绪 */
    data[dataLen++] = (status->FirmwareCode << 2) | (status->DevAppVersion & 0x3); /*BIT[15:10]  frameware  code*/

    return dataLen;
}

/**
 * @brief 打包设备更新状态
 * 
 * @param frame 输出：CANFD帧
 * @param status 设备信息
 * @return uint8_t 打包的数据长度
 */
static uint8_t PackFwUpdateInfo(CANFD_Frame *frame, CANFD_Frame *rxframe) {
    uint8_t *data = frame->data;
    uint8_t dataLen = 0;
    
    /* Data0: app工程 或boot 工程 */
    data[dataLen] = rxframe->data[dataLen]; /*0：app  1：boot */
    dataLen ++;
    
    /* Data2: 设备更新状态  0 ：非更新，1：正在更新 2：更新成功 3：更新失败*/
    data[dataLen++] = s_update_state.var.status;    // todo

    return dataLen;
}


/**
 * @brief Process firmware information request/response
 *
 * @param rx_frame Pointer to received frame
 * @param tx_frame Pointer to response frame to fill
 * @return uint8_t 打包的数据长度
 */
static uint8_t process_firmware_info(CANFD_Frame *rx_frame, CANFD_Frame *tx_frame)
{
    uint8_t *data = rx_frame->data;
    uint8_t dataLen = 0;
    /* Prepare response */
    
    data[dataLen++] = rx_frame->data[0] & 0x03 | (FIRMWARE_OP_INFO << 2);
    data[dataLen++] = ARM_FIRMWARE_CODE;

    return dataLen;
}



/**
 * @brief Process firmware information request/response
 *
 * @param rx_frame Pointer to received frame
 * @param tx_frame Pointer to response frame to fill
 * @return uint8_t 打包的数据长度
 */
static uint8_t process_firmware_start(CANFD_Frame *rx_frame, CANFD_Frame *tx_frame)
{
    uint8_t *data = rx_frame->data;
    uint8_t dataLen = 0;
    FirmwareStartResponse_t response_code;
    /* Prepare response */
    

    /* Extract firmware information from request */
    FirmwareInfo_t info;
    info.firmware_type = (rx_frame->data[4] >> 2) & 0x3F;
    info.firmware_version = ((uint16_t)(rx_frame->data[4] & 0x03) << 8) | rx_frame->data[3];
    info.firmware_size = ((uint32_t)rx_frame->data[2] << 16) |
                        ((uint32_t)rx_frame->data[1] << 8) |
                        ((uint32_t)rx_frame->data[0] >> 4);   

    if((info.firmware_type != ARM_FIRMWARE_CODE && info.firmware_type != ARM_FIRMWARE_CODE) || 
        info.firmware_size == 0 || info.firmware_size > 0x80000)  // 512KB
    {
        response_code =  FIRMWARE_START_ERROR;   
    }
    else if(s_update_state.var.update_in_progress == false)
    {
        response_code =  FIRMWARE_START_STARTED; 
        s_update_state.var.update_in_progress = true;
        s_update_state.var.current_offset = 0;
        s_update_state.var.packet_id = 0;
        s_update_state.var.allrxdatenum = 0;    
        s_update_state.var.buffrxdatenum = 0;
        s_update_state.var.status = FIRMWARE_STATUS_UPDATING;     

        s_update_state.conf.firmware_type = info.firmware_type;
        s_update_state.conf.device_id = info.device_id;
        s_update_state.conf.firmware_size = info.firmware_size;
        s_update_state.conf.max_chunk_size = 59;
        s_update_state.conf.max_retries = 5;
        s_update_state.conf.timeout_ms = 1000;
    }
    else
    {
        response_code =  FIRMWARE_START_ERROR;
    }
    

    data[dataLen++] = rx_frame->data[0] & 0x03 | (FIRMWARE_OP_INFO << 2);
    data[dataLen++] = (response_code& 0xFF) >> 2;

    return dataLen;
}
/**
 * @brief 发送电机状态信息
 * 
 * @return CANFD_Error 错误码
 */
#pragma optimize=none
CANFD_Error CANFD_ParkResponseData(CANFD_Frame *txframe,CANFD_Frame *rxframe, uint8_t type) {
    uint8_t dataLen = 0;
    
    if (!gState.initialized) {
        return CANFD_NOT_INITIALIZED;
    }
    PrepareFrameHeader(txframe, rxframe); 
    
    switch(rxframe->header.type)
    {
       
        case PACKET_TYPE_READ:
            switch (rxframe->header.cmd)
            {
                case CMD_DEVICE_STATUS:
                    /* code */
                    dataLen =  PackDeviceStatus(txframe, &HumanRotCtrl.statusData);
                    break;
                case CMD_DEVICE_VERSION:
                    /* code */
                    dataLen = PackFirmwareInfo(txframe, &HumanRotCtrl.statusData);
                    break;   
                case CMD_OP_DICTIONARY:
                    /* code */
                    ParseReadDictionary(rxframe->data, txframe->data, &dataLen);
                    break;
                case CMD_FIRMWARE_UPDATE:
                    /* code */
                    dataLen =  PackFwUpdateInfo(txframe, rxframe);
                break;
                case CMD_MOTOR_CONTROL:
                    /* code */
                    dataLen = PackMotorStatus(txframe, &HumanRotCtrl.statusData);
                    break;
                default:
                    return CANFD_NONEED_RESPONSE;  
                    break;
            }
            break;
        case PACKET_TYPE_SET1:
            if(rxframe->header.cmd == CMD_FIRMWARE_UPDATE)
            {
                /* Extract operation code and device ID from the first byte */
                uint8_t device_id = rxframe->data[0] & 0x03;
                uint8_t op_code = (rxframe->data[0] >> 2) & 0x03;
                /* Process based on operation code */
                switch(op_code)
                {
                    case FIRMWARE_OP_INFO:
                        dataLen = process_firmware_info(rxframe, txframe);
                        break;
                    case FIRMWARE_OP_START_UPDATE:
                        dataLen = process_firmware_start(rxframe, txframe);
                        break;
                    default:
                        return CANFD_NONEED_RESPONSE;  
                        break;
                }
            }
            else if(rxframe->header.cmd == CMD_OP_DICTIONARY)
            {
                 ParseWriteDictionary(rxframe->data, txframe->data, rxframe->header.length);
                 dataLen = rxframe->header.length;
            }
            else
            {
                return CANFD_NONEED_RESPONSE;   
            } 
            break;
        case PACKET_TYPE_SET2:
            /* 打包电机状态信息 */
            dataLen = PackMotorStatus(txframe, &HumanRotCtrl.statusData);
            break;
        case PACKET_TYPE_RESP:
            switch (rxframe->header.cmd)
            {
                case CMD_FIRMWARE_UPDATE:
                    /* code */
                    dataLen =  PackDeviceStatus(txframe, &HumanRotCtrl.statusData);
                    break;
                default:
                    return CANFD_NONEED_RESPONSE;  
                    break;
            }
            break;
        default:
            break;
    }
  /* 更新数据长度 */
  txframe->header.length = dataLen;
  txframe->dlc = 4 + dataLen; /* 帧头(4字节) + 数据长度 */
  
   return CANFD_OK;  
  
  // /* 发送帧 */
  // return SendFrame(&frame);    
    
}

/**
 * @brief 打包CANFD帧头部
 * 
 * @param frame 输出：CANFD帧

 */
void PackFrameHeader(CANFD_Frame *frame, uint8_t *buffer) {
    /* 初始化帧数据 */
    CANFD_FrameHeader *header = &frame->header;

    /* Pack byte 0: seq (6 bits) + src (2 bits) */
    buffer[0] = header->seq & 0x3F;
    buffer[0] |= ((header->src & 0x03) << 6);
    
    /* Pack byte 1: src (3 bits) + dest (5 bits) */
    buffer[1] = ((header->src >> 2) & 0x07);
    buffer[1] |= ((header->dest & 0x1F) << 3);
    
    /* Pack byte 2: cmd (6 bits) + type (2 bits) */
    buffer[2] = header->cmd & 0x3F;
    buffer[2] |= ((header->type & 0x03) << 6);
    
    /* Pack byte 3: reserved (2 bits) + length (6 bits) */
    buffer[3] = (header->length & 0x3F) << 2;
    buffer[3] |= buffer[3] & 0xFC;
}



/**
 * @brief 解包CANFD帧头部
 * 
 * @param frame 输出：CANFD帧

 */
void UnPackFrameHeader(CANFD_Frame *frame, can_frame_t * p_frame) 
{
    /* 初始化帧数据 */
    CANFD_FrameHeader *header = &frame->header;
    
    frame->can_id = p_frame->id;
    frame->id_mode = p_frame->id_mode;
    frame->type = p_frame->type;
    frame->dlc = p_frame->data_length_code;
    frame->options = p_frame->options;

    // 从第1个字节提取seq (6位)
    frame->header.seq = p_frame->data[0] & 0x3F;
    
    // 从第1个字节的高2位和第2个字节的低3位提取src (5位)
    frame->header.src = (( p_frame->data[0] >> 6) & 0x03) | (( p_frame->data[1] & 0x07) << 2);
    
    // 从第2个字节的高5位提取dest (5位)
    frame->header.dest = ( p_frame->data[1] >> 3) & 0x1F;
    
    // 从第3个字节提取cmd (6位)
    frame->header.cmd =  p_frame->data[2] & 0x3F;
    
    // 从第3个字节的高2位提取type (2位)
    frame->header.type = ( p_frame->data[2] >> 6) & 0x03;
    
    // 从第4个字节的低2位提取res (2位)
    frame->header.res =  p_frame->data[3] & 0x03;
    
    // 从第4个字节的高6位提取length (6位)
    frame->header.length = ( p_frame->data[3] >> 2) & 0x3F;        
          
}



/* Firmware update state */
Updatestate_t s_update_state = {0};

/**
 * @brief Initialize the firmware update module
 *
 * @return Status_t Status code
 */
void firmware_init(void)
{
   /* Initialize update state */
     memset(&s_update_state, 0, sizeof(Updatestate_t));
}


void firmware_pack_data_request(CANFD_Frame *frame)
{
    uint8_t *data = frame->data;
    uint8_t dataLen = 0;
    /* 设置CAN ID，从站到主站方向 */
    frame->can_id = BuildCanID(CANFD_DIR_SLAVE_TO_MASTER, 0x07, gState.slaveAddress);
    frame->header.type = PACKET_TYPE_READ;
    /* 设置帧头 */
    frame->header.seq = gState.seq;  
    gState.seq = (gState.seq + 1) & 0x3F;  /* 自增序号，范围0-63（6位） */
    frame->header.src = gState.slaveAddress;
    frame->header.dest = CANFD_MASTER_ID;
    frame->header.cmd = CMD_FIRMWARE_UPDATE;
    frame->header.res = 0;
    /* 设置帧属性 */
    frame->id_mode = 0; /* 标准帧 */


    uint32_t offset = s_update_state.var.current_offset;
    uint8_t packet_id = s_update_state.var.packet_id;
    uint8_t chunk_size = s_update_state.var.chunk_size;

    data[dataLen++] =  ((offset & 0x0f) << 4) | (FIRMWARE_OP_DATA << 2)|(s_update_state.conf.device_id & 0x3);
    data[dataLen++] =  (offset >> 4) & 0xff;
    data[dataLen++] =  (offset >> 12) & 0xff;
    data[dataLen++] =  (packet_id << 2)|(chunk_size & 0x3f);  

    /* 更新数据长度 */
    frame->header.length = dataLen;
    frame->dlc = 4 + dataLen; /* 帧头(4字节) + 数据长度 */    
}


void firmware_status_response(CANFD_Frame *frame)   
{
    uint8_t *data = frame->data;
    uint8_t dataLen = 0;
    /* 设置CAN ID，从站到主站方向 */
    frame->can_id = BuildCanID(CANFD_DIR_SLAVE_TO_MASTER, 0x07, gState.slaveAddress);
    frame->header.type = PACKET_TYPE_READ;
    /* 设置帧头 */
    frame->header.seq = gState.seq;  
    gState.seq = (gState.seq + 1) & 0x3F;  /* 自增序号，范围0-63（6位） */
    frame->header.src = gState.slaveAddress;
    frame->header.dest = CANFD_MASTER_ID;
    frame->header.cmd = CMD_FIRMWARE_UPDATE;
    frame->header.res = 0;
    /* 设置帧属性 */
    frame->id_mode = 0; /* 标准帧 */

    data[dataLen++] =  (FIRMWARE_OP_STATUS << 2)|(s_update_state.conf.device_id & 0x3);
    data[dataLen++] =  s_update_state.var.status;
    /* 更新数据长度 */
    frame->header.length = dataLen;
    frame->dlc = 4 + dataLen; /* 帧头(4字节) + 数据长度 */    
}

