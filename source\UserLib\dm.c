#include        "mm_defs.h"                 

#ifndef		TRUE
#define     TRUE                         1
#endif

#ifndef		FALSE
#define		FALSE                        0
#endif

#ifndef		NULL
#define		NULL                         0
#endif

static int  MmCreatePool(MM_POOL *pool_ptr, char *name,void *start_address, unsigned pool_size, unsigned min_allocation)
{

	register MM_PCB      *pool;                       /* Pool control block ptr    */
	int             i;                          /* Working index variable    */
	MM_HEADER      *header_ptr;                 /* Partition block header ptr*/
    /* Move input pool pointer into internal pointer.  */
    pool =  (MM_PCB *) pool_ptr;
    /* First, clear the partition pool ID just in case it is an old 
       pool control block.  */
    pool -> dm_id =             0;    
    /* Fill in the partition pool name.  */
    for (i = 0; i < MM_MAX_NAME; i++)
        pool -> dm_name[i] =  name[i];    

    /* Convert the pool's size into something that is evenly divisible by
       the sizeof an UNSIGNED data element.  */
    pool_size =  (pool_size/sizeof(unsigned)) * sizeof(unsigned);

    /* Save the starting address and size parameters in the dynamic memory 
       control block.  */
    pool -> dm_start_address =   start_address;
    pool -> dm_pool_size =       pool_size;
    pool -> dm_min_allocation =    
                ((min_allocation + sizeof(unsigned) - 1)/sizeof(unsigned)) *
                                                              sizeof(unsigned);    
    
    
    /* Clear the number of tasks waiting on the dynamic memory pool.  */
    pool -> dm_tasks_waiting =  0;    
    /* Build a single block that has all of the memory.  */
    header_ptr =  (MM_HEADER *) start_address;
    /* Initialize the memory parameters.  */
    pool -> dm_available =       pool_size - (2 * MM_OVERHEAD);
    pool -> dm_memory_list =     header_ptr;
    pool -> dm_search_ptr =      header_ptr;    
    /* Build the block header.  */
    header_ptr -> dm_memory_pool =  pool;
    header_ptr -> dm_memory_free =  TRUE;
    header_ptr -> dm_next_memory =  (MM_HEADER *) 
           (((unsigned char *) header_ptr) + pool -> dm_available + MM_OVERHEAD);     
    header_ptr -> dm_previous_memory =  header_ptr -> dm_next_memory;
    /* Build the small trailer block that prevents block merging when the
       pool wraps around.  Note that the list is circular so searching can
       wrap across the physical end of the memory pool.  */
    header_ptr =  header_ptr -> dm_next_memory;
    header_ptr -> dm_next_memory =  (MM_HEADER *) start_address;
    header_ptr -> dm_previous_memory =  (MM_HEADER *) start_address;
    header_ptr -> dm_memory_pool =  pool;
    header_ptr -> dm_memory_free =  FALSE;
    
    /* At this point the dynamic memory pool is completely built.  The ID can 
       now be set and it can be linked into the created dynamic memory
       pool list. */
    pool -> dm_id =  MM_DYNAMIC_ID;    
        
    /* Return successful completion.  */
    return(0);
}

static int  MmAllocate(MM_POOL *pool_ptr, void **return_pointer, unsigned size)
{

	register MM_PCB      *pool;                       /* Pool control block ptr    */
	register MM_HEADER   *memory_ptr;                 /* Pointer to memory         */
	register MM_HEADER   *new_ptr;                    /* New split block pointer   */
	unsigned        free_size;                  /* Size of block found       */
	int          	status;                     /* Completion status         */


    /* Move input pool pointer into internal pointer.  */
    pool =  (MM_PCB *) pool_ptr;


    /* Initialize the status as successful.  */
    status =  -1;

    /* Adjust the request to a size evenly divisible by the number of bytes
       in an UNSIGNED data element.  Also, check to make sure it is of the
       minimum size.  */
    if (size < pool -> dm_min_allocation)
    
        /* Change size to the minimum allocation.  */
        size =  pool -> dm_min_allocation;
    else
    
        /* Insure that size is a multiple of the UNSIGNED size.  */
        size = 
           ((size + sizeof(unsigned) - 1)/sizeof(unsigned)) * sizeof(unsigned);

    
    
    /* Search the memory list for the first available block of memory that
       satisfies the request.  Note that blocks are merged during the 
       deallocation function.  */
    memory_ptr =  pool -> dm_search_ptr;
    do
    {
    
        /* Determine if the block is free and if it can satisfy the request. */
        if (memory_ptr -> dm_memory_free)
        
            /* Calculate the free block size.  */
            free_size =  (((unsigned char *) (memory_ptr -> dm_next_memory)) -
                           ((unsigned char *) memory_ptr)) - MM_OVERHEAD;
        else
        
            /* There are no free bytes available.  */
            free_size =  0;
            
        /* Determine if the search should continue.  */
        if (free_size < size)
        
            /* Large enough block has not been found.  Move the search 
               pointer to the next block.  */
            memory_ptr =  memory_ptr -> dm_next_memory;
    } while((free_size < size) && (memory_ptr != pool -> dm_search_ptr));
    
    /* Determine if the memory is available.  */
    if (free_size >= size)
    {
    
        /* A block that satisfies the request has been found.  */
        
        /* Determine if the block needs to be split.  */
        if (free_size >= (size + MM_OVERHEAD + pool -> dm_min_allocation))
        {
        
            /* Yes, split the block.  */
            new_ptr =  (MM_HEADER *) (((unsigned char *) memory_ptr) + size +
                                                MM_OVERHEAD);

            /* Mark the new block as free.  */
            new_ptr -> dm_memory_free =  TRUE;
            
            /* Put the pool pointer into the new block.  */
            new_ptr -> dm_memory_pool =  pool;

            /* Build the necessary pointers.  */
            new_ptr -> dm_previous_memory =  memory_ptr;
            new_ptr -> dm_next_memory =      memory_ptr -> dm_next_memory;
            (new_ptr -> dm_next_memory) -> dm_previous_memory =  new_ptr;
            memory_ptr -> dm_next_memory =   new_ptr;
            
            /* Decrement the available byte count.  */
            pool -> dm_available =  pool -> dm_available - size - MM_OVERHEAD;
        }
        else
        
            /* Decrement the entire free size from the available bytes 
               count.  */
            pool -> dm_available =  pool -> dm_available - free_size;

        /* Mark the allocated block as not available.  */
        memory_ptr -> dm_memory_free =  FALSE;

        /* Should the search pointer be moved?   */
        if (pool -> dm_search_ptr == memory_ptr)
        
            /* Move the search pointer to the next free memory slot.  */
            pool -> dm_search_ptr =  memory_ptr -> dm_next_memory;
        
        /* Return a memory address to the caller.  */
        *return_pointer =  (void *) (((unsigned char *) memory_ptr) + MM_OVERHEAD);

	status=0;
    }
    
       

    /* Return the completion status.  */
    return(status);
}



static int  MmFree(void *memory)
{

	register MM_PCB      *pool;                       /* Pool pointer              */
	register MM_HEADER   *header_ptr;                 /* Pointer to memory hdr     */
	register MM_HEADER   *new_ptr;                    /* New memory block pointer  */
//unsigned        size;                       /* Suspended task request    */
//unsigned        free_size;                  /* Amount of free bytes      */
	int          status;                     /* Completion status         */

    /* Initialize the status as successful.  */
    status =  -1;

    /* Pickup the associated pool's pointer.  It is inside the header of
       each memory.  */
    header_ptr =  (MM_HEADER *) (((unsigned char *) memory) - MM_OVERHEAD);
    pool =        header_ptr -> dm_memory_pool;
    
       
    /* Mark the memory as available.  */
    header_ptr -> dm_memory_free =  TRUE;

    /* Adjust the available number of bytes.  */
    pool -> dm_available =  pool -> dm_available +
                        (((unsigned char *) (header_ptr -> dm_next_memory)) -
                           ((unsigned char *) header_ptr)) - MM_OVERHEAD;

    /* Determine if the block can be merged with the previous neighbor.  */
    if ((header_ptr -> dm_previous_memory) -> dm_memory_free)
    {
    
        /* Adjust the available number of bytes.  */
        pool -> dm_available =  pool -> dm_available + MM_OVERHEAD;

        /* Yes, merge block with previous neighbor.  */
        (header_ptr -> dm_previous_memory) -> dm_next_memory =  
                                header_ptr -> dm_next_memory;
        (header_ptr -> dm_next_memory) -> dm_previous_memory =
                                header_ptr -> dm_previous_memory;
                                
        /* Move header pointer to previous.  */
        header_ptr =  header_ptr -> dm_previous_memory;
        
        /* Adjust the search pointer to the new merged block.  */
        pool -> dm_search_ptr =  header_ptr;
    }
    
    /* Determine if the block can be merged with the next neighbor.  */
    if ((header_ptr -> dm_next_memory) -> dm_memory_free)
    {
    
        /* Adjust the available number of bytes.  */
        pool -> dm_available =  pool -> dm_available + MM_OVERHEAD;

        /* Yes, merge block with next neighbor.  */
        new_ptr =  header_ptr -> dm_next_memory;
        (new_ptr -> dm_next_memory) -> dm_previous_memory =
                                                header_ptr;
        header_ptr -> dm_next_memory = new_ptr -> dm_next_memory;

        /* Adjust the search pointer to the new merged block.  */
        pool -> dm_search_ptr =  header_ptr;
    }

    

    /* Return the completion status.  */
    return(status);
}


static MM_POOL MemoryPool;	

void InitMm(unsigned long firstavail, unsigned long  endavail)
{
	MmCreatePool(&MemoryPool,"SdramPool",(unsigned char*)firstavail,endavail-firstavail,64);
}

void *MyMalloc(int size)
{
	void * result=(void * )NULL;		
	MmAllocate(&MemoryPool,&result,size);
	return result;
}

void MyFree(void *where)
{
	MmFree(where);
}
