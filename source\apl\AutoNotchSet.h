/****************************************************************************************************
 *
 * FILE NAME:  AutoNotchSet.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.25
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	25-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _AUTO_NOTCH_SET_H_
#define _AUTO_NOTCH_SET_H_

#include "BaseDef.h"
#include "TuneLib.h"
#include "ResVibCtrl.h"
#include "RegAccessIf.h"



/****************************************************************************************************/
/*																									*/
/*		Constant Definition																			*/
/*																									*/
/****************************************************************************************************/
#define		ANOTCH_MIN_SET_FREQ			400		/* Automatic notch minimum setting frequency[Hz]	*/
#define		ANOTCH_MIN_SET_FREQ2		200		/* Automatic notch minimum setting frequency during forced detection[Hz]*/
#define		ANOTCH_NOTCH_SET_CHK_TIME	3000	/* Wait time after setting notch[ms]				*/
/*--------------------------------------------------------------------------------------------------*/
#define		NOTCH_COMP_MAX_FREQ			800		/* Notch frequency correction maximum frequency[Hz]	*/
#define		NOTCH_COMP_RATIO			170		/* Notch frequency correction ratio[%]				*/
/*--------------------------------------------------------------------------------------------------*/
#define		NOTCH_BASIC_VIB_LEVEL		600		/* Vibration detection level (rotation)[0.1min-1]	*/
#define		NOTCH_BASIC_VIB_LEVEL_L		200		/* Vibration detection level (Linear)[0.1mm/s]		*/
#define		NOTCH_FILTYPE				2		/* Filter type Type 2 fixed in automatic notch		*/
#define		NOTCH_FILTYPE2				3		/* Filter type Type 3 with automatic notch above 2000Hz */
#define		FREQDETUNIT					20		/* Frequency analysis Detection frequency resolution  */
#define		ANOTCH_FRAT_106( data )		(((data)*17)>>4)	/* 106.25%								*/




/****************************************************************************************************/
/*																									*/
/*		Struct Definition																			*/
/*																									*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Constant/variable data definition for automatic notch sequence								*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	struct
	{								
		UINT16		SpatANotchState;			/* Automatic notch status							*/
	} OpeReg;
	
	struct
	{								
		UINT16		kv;							/* Velocity loop gain for automatic notch calculation[0.1Hz] */
		UINT16		jrate;						/* Load inertia moment/mass ratio for automatic notch calculation[%]*/
		BOOL		EepromWriteProtect;			/* EEPROM write protect flag						*/
		BOOL		BaseEnableSts;				/* Servo ON/OFF status flag							*/
		BOOL		TuneLessSts;				/* Tune-less valid/invalid flag						*/
		BOOL		OverTravelSts;				/* P-OT/N-OT status flag							*/
		INT32		VibCheckLevelForTunLess ;	/* Vibration detection level for automatic notch[2^24/OS]   */
	} conf;

	struct
	{								
		BOOL		FftReq;						/* Frequency analysis request						*/
		BOOL		NotchChk;					/* Notch checking									*/
		UINT32		NotchChkTimer;				/* Notch check timer (waiting for vibration detection) */
		INT16		FftStat;					/* Frequency analysis status						*/
		INT16		ANotchSetStat;				/* Automatic notch setting result status			*/
		UINT8		ANotchStat;					/* Execution status of automatic notch setting		*/
		UINT8		ANotch1Stat;				/* Automatic notch 1st stage notch setting result	*/
		UINT8		ANotch2Stat;				/* Automatic notch 2nd stage notch setting result	*/
		UINT8		ANotchPreStat;				/* Automatic notch immediately before notch setting	*/
	} var;
} ANOTCHSEQ;



/*--------------------------------------------------------------------------------------------------*/
/*		Pointer structure definition for automatic notch sequence call								*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	ANOTCHSEQ			*ANotchSeq;			/* Structure for automatic notch sequence				*/
	AUTONOTCH			*ANotch;			/* Structure for automatic notch setting				*/
	FFTANALYZE			*FftAna;			/* Structure for vibration frequency analysis (Online FFT) */
	DETVIB				*DetVib;			/* Vibration detection structure						*/
	PRMDATA				*Prm;				/* Parameter data structure								*/
	REGIF_CONFIG_T		*RegCfg;			/* Register IF configuration structure					*/
} ANOTCHSEQCTRL;



/****************************************************************************************************/
/*																									*/
/*		Function Prototype Definition																*/
/*																									*/
/****************************************************************************************************/
PUBLIC void AutoNotchFilterSetting( ANOTCHSEQCTRL *ANotchCtrl );
PUBLIC void AutoNotchInitialize( ANOTCHSEQ *ANotchSeq );
PUBLIC void AutoNotchCalculatePrm( ANOTCHSEQ *ANotchSeq, UINT16 kv, UINT16 jrate);
PUBLIC void AutoNotchCalculateInitPrm( ANOTCHSEQ *ANotchSeq, AUTONOTCH *ANotch, 
																	PRMDATA *PnPrm, BPRMDAT *Bprm );
PUBLIC void RpiResetStatusANotch1( ANOTCHSEQ *ANotchSeq );
PUBLIC void RpiResetStatusANotch2( ANOTCHSEQ *ANotchSeq );
PUBLIC UINT8 RpiGetANotchPreStat( ANOTCHSEQ *ANotchSeq );
PUBLIC UINT16 RpiGetANotchSetStat( ANOTCHSEQ *ANotchSeq );
PUBLIC void RpiSetRegSpatANotchState( ANOTCHSEQ *ANotchSeq );
PUBLIC void RpiANotchFftReqSet( ANOTCHSEQ *ANotchSeq, FFTANALYZE *FftAna );
PUBLIC void RpiANotchTrqFilCalc( ANOTCHSEQ *ANotchSeq, AUTONOTCH *ANotch, REGIF_CONFIG_T *RegCfg, PRMDATA *Prm );
PUBLIC void RpiANotchDisableReqSet( ANOTCHSEQ *ANotchSeq );
PUBLIC void AutoNotchSetCondition( ANOTCHSEQ *ANotchSeq, BOOL EepromWriteProtect, 
										BOOL BaseEnableSts, BOOL TuneLessSts, BOOL OverTravelSts );


#endif

