/*******************************************************************************
								Copyright 2022 IAR Systems. All rights reserved.
Function:RZ/N2L(Cortex-R52) SPI Flashloader.

Note:MX25UR51245G SPI mode

History:
 No  Rev  Date       Name			Note
---+-----+----------+-------------+--------------------------------------------
000 01.00 2022/07/29 S.Tonoshita	New Development
*******************************************************************************/

/******************************************************************************/
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <intrinsics.h>
#include "flash_loader.h"
#include "flash_loader_extra.h"
#include "MX25UR51245G.h"

/******************************************************************************/
#pragma section = ".intvec"										// For setting the vector base register(VBAR)


/*******************************************************************************
1. Function: Initialize the flash memory

2.Caution.

3.Argument
I/O|Name					   |Description.
---+---------------------------+------------------------------------------------
I/	base_of_flash				Points to the first byte of the flash memory.
I/	image_size					Specifies the size of the image, in bytes, to be written to flash memory.
I/	link_address				Specifies the original link address of the first byte of the
								image, before any offsets, or if there are multiple passes,
								the first byte of the subset of the image used for this pass.
								Not all flash loaders need this parameter.
I/	flags						Specifies optional flags.

4.return:						RESULT_OK
								RESULT_ERROR
*******************************************************************************/
uint32_t FlashInit(void *base_of_flash, uint32_t image_size,uint32_t link_address,uint32_t flags)
{
	uint32_t ret;
	uint32_t flash_id;

	__MCR(15,0,(uint32_t)__section_begin(".intvec"),12,0,0);	// Write to vector-based register (VBAR)

	ret = flash_init(&flash_id);								// Initialize the flash memory.
	if(ret!=RESULT_OK){
		return( ret );
	}
//	if( flash_id!=FLASH_ID){
//		return( RESULT_ERROR );
//	}
	return( ret );
}

/*******************************************************************************
1. Function: Write the flash memory

2.Caution.

3.Argument
I/O|Name					   |Description.
---+---------------------------+------------------------------------------------
I/	block_start					Points to the first byte of the block into which this operation writes.
I/	offset_into_block			Specifies how far into the current block that this write
								operation shall start. The absolute address of the first byte
								to write is block_start + offset_into_block.
								
I/	count						Specifies the number of bytes to write.
I/	buffer						A pointer to the buffer that contains the bytes to write.

4.return:						RESULT_OK
								RESULT_ERROR
*******************************************************************************/
uint32_t FlashWrite(void *block_start,uint32_t offset_into_block,uint32_t count,char const *buffer)
{
	return( flash_write((uint32_t)block_start + offset_into_block,count,(const uint8_t *)buffer) );
}

/*******************************************************************************
1. Function:Flash Erase

2.Caution.

3.Argument
I/O|Name					   |Description.
---+---------------------------+------------------------------------------------
I/	block_start					Points to the first byte of the block to erase.
I/	block_size					Specifies the size, in bytes, of the block to erase.

4.return:						RESULT_OK
								RESULT_ERROR
*******************************************************************************/
uint32_t FlashErase(void *block_start,uint32_t block_size)
{
	return( flash_sector_erase((uint32_t)block_start) );
}
