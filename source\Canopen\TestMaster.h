
/* File generated by gen_cfile.py. Should not be modified. */

#ifndef TESTMASTER_H
#define TESTMASTER_H

#include "data.h"

/* Prototypes of function provided by object dictionnary */
UNS32 TestMaster_valueRangeTest (UNS8 typeValue, void * value);
const indextable * TestMaster_scanIndexOD (UNS16 wIndex, UNS32 * errorCode, ODCallback_t **callbacks);

/* Master node data struct */
extern CO_Data TestMaster_Data;
extern UNS8 MasterMap1;		/* Mapped at index 0x2000, subindex 0x00*/
extern UNS8 MasterMap2;		/* Mapped at index 0x2001, subindex 0x00*/
extern UNS8 MasterMap3;		/* Mapped at index 0x2002, subindex 0x00*/
extern UNS8 MasterMap4;		/* Mapped at index 0x2003, subindex 0x00*/
extern UNS8 MasterMap5;		/* Mapped at index 0x2004, subindex 0x00*/
extern UNS8 MasterMap6;		/* Mapped at index 0x2005, subindex 0x00*/
extern UNS8 MasterMap7;		/* Mapped at index 0x2006, subindex 0x00*/
extern UNS8 MasterMap8;		/* Mapped at index 0x2007, subindex 0x00*/
extern UNS8 MasterMap9;		/* Mapped at index 0x2008, subindex 0x00*/
extern UNS32 MasterMap10;		/* Mapped at index 0x2009, subindex 0x00*/
extern UNS16 MasterMap11;		/* Mapped at index 0x200A, subindex 0x00*/
extern INTEGER16 MasterMap12;		/* Mapped at index 0x200B, subindex 0x00*/
extern INTEGER16 MasterMap13;		/* Mapped at index 0x200C, subindex 0x00*/

#endif // TESTMASTER_H
