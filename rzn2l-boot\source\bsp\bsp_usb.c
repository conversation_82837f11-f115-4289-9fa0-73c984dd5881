/****************************************************************************************************
 *
 * FILE NAME:  ComUart.h
 *
 * DESCRIPTION:  
 *
 * CREATED ON:  2019.06.27
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	27-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "hal_data.h"
#include "r_usb_basic.h"
#include "r_usb_basic_cfg.h"
#include "r_usb_pcdc_api.h"
#include "r_usb_typedef.h"
#include "r_usb_extern.h"
#include "bsp_timer.h"
#include "bsp_usb.h"

extern uint16_t SendMark;

extern uint16_t g_usb_complete;
/***********************************************************************************************************************
 * Exported global variables (to be accessed by other files)
 **********************************************************************************************************************/
/* USB descriptor */
extern uint8_t g_apl_device[];
extern uint8_t g_apl_configuration[];
extern uint8_t g_apl_hs_configuration[];
extern uint8_t g_apl_qualifier_descriptor[];
extern uint8_t *g_apl_string_table[];




usb_descriptor_t g_usb_descriptor =
{
    g_apl_device,                   /* Pointer to the device descriptor */
    g_apl_configuration,            /* Pointer to the configuration descriptor for Full-speed */
    g_apl_hs_configuration,         /* Pointer to the configuration descriptor for Hi-speed */
    g_apl_qualifier_descriptor,     /* Pointer to the qualifier descriptor */
    g_apl_string_table,             /* Pointer to the string descriptor table */
    NUM_STRING_DESCRIPTOR
};


static usb_pcdc_linecoding_t s_g_line_coding;




/***********************************************************************************************************************
* Function Name: bsp_usb_init 
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
// 初始化usb
void bsp_usb_init(void)
{
    /* Additional processing to enable USB communication after USB boot. */

    /* Remove write protection. */
    R_RWP_NS->PRCRN = (uint32_t)0x0000A507;
    R_RWP_S->PRCRS  = (uint32_t)0x0000A50F;

    /* Reset the USB module. */
    uint32_t mrctle_val = R_SYSC_NS->MRCTLE;
    R_SYSC_NS->MRCTLE = mrctle_val | 0x00002000UL;

    /* Delay. */
    R_BSP_SoftwareDelay(5, BSP_DELAY_UNITS_MICROSECONDS);

    R_SYSC_NS->MRCTLE = mrctle_val;

    /* Enable write protection */
    R_RWP_NS->PRCRN = (uint32_t)0x0000A500;
    R_RWP_S->PRCRS  = (uint32_t)0x0000A500;  
    
    g_usb_on_usb.open(&g_basic0_ctrl, &g_basic0_cfg);
}

/***********************************************************************************************************************
* Function Name: bsp_usb_cfg_detect
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : Non
* if timeout_us is zero timeout will not detect 
***********************************************************************************************************************/
uint16_t bsp_usb_cfg_detect(uint32_t timeout_ms)
{
    usb_event_info_t   event_info;
    usb_status_t       event;
    fsp_err_t          fsp_err = FSP_SUCCESS;
    
    uint32_t           starttimer = 0;
    uint32_t           TimeoutElapse = 0;
    
    usb_info_t info;
    uint32_t   timeout =  (uint32_t)(((uint64_t)COUNTER_PPR_MS * (uint64_t)timeout_ms));

    starttimer = bsp_gettimercnt(); 
    
    do
    {
      g_usb_on_usb.eventGet(&event_info, &event);
      
      R_USB_InfoGet(&g_basic0_ctrl,&info,USB_CLASS_PCDC);
      
      switch (event)
      {
        case USB_STATUS_REQUEST:   /* Receive Class Request */
            if (USB_PCDC_SET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Configure virtual UART settings */
                g_usb_on_usb.periControlDataGet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else if (USB_PCDC_GET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Send virtual UART settings back to host */
                g_usb_on_usb.periControlDataSet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else
            {
                /* ACK all other status requests */
                g_usb_on_usb.periControlStatusSet(&g_basic0_ctrl, USB_SETUP_STATUS_ACK);
            }
            break;
        case USB_STATUS_SUSPEND:
        case USB_STATUS_DETACH:
            break;
        default:           
            break;
      }   


      TimeoutElapse = bsp_gettimercnt() - starttimer;
      
      if(TimeoutElapse >= timeout)
      {
        fsp_err = FSP_ERR_USB_FAILED;
        break;
      }
                  
    }while((info.device_status != USB_STATUS_CONFIGURED) || (event != USB_STATUS_NONE));  
  

   if(fsp_err != 0)
   {
      return USER_USB_ERROR;
   }
   else
   {
      return USB_OK;
   }     

}
/***********************************************************************************************************************
* Function Name: bsp_usb_receive
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : Non
* if timeout_us is zero timeout will not detect 
***********************************************************************************************************************/
uint16_t bsp_usb_send(uint8_t *pbuff, uint16_t len,uint32_t timeout_ms)
{
    usb_event_info_t   event_info;
    usb_status_t       event;
    fsp_err_t          fsp_err = FSP_SUCCESS;
    uint32_t           starttimer = 0;
    uint32_t           TimeoutElapse = 0;

    usb_info_t info;
    uint32_t   timeout =  (uint32_t)(((uint64_t)COUNTER_PPR_MS * (uint64_t)timeout_ms));
   
    
    do
    {
      g_usb_on_usb.eventGet(&event_info, &event);
      
      R_USB_InfoGet(&g_basic0_ctrl,&info,USB_CLASS_PCDC);
      
      switch (event)
      {
        case USB_STATUS_REQUEST:   /* Receive Class Request */
            if (USB_PCDC_SET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Configure virtual UART settings */
                g_usb_on_usb.periControlDataGet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else if (USB_PCDC_GET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Send virtual UART settings back to host */
                g_usb_on_usb.periControlDataSet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else
            {
                /* ACK all other status requests */
                g_usb_on_usb.periControlStatusSet(&g_basic0_ctrl, USB_SETUP_STATUS_ACK);
            }
            break;
        case USB_STATUS_SUSPEND:
        case USB_STATUS_DETACH:
            break;
        default:           
            break;
      }         
      
    }while((info.device_status != USB_STATUS_CONFIGURED) || (event != USB_STATUS_NONE));

      
    if(g_p_usb_pstd_pipe[1] == (usb_utr_t *) USB_NULL)
    {
      g_usb_on_usb.write(&g_basic0_ctrl, (pbuff), len, USB_CLASS_PCDC); 
    }
      

    
    starttimer = bsp_gettimercnt(); 
      
    
    do{
      g_usb_on_usb.eventGet(&event_info, &event);
      
      switch (event)
      {
        case USB_STATUS_REQUEST:   /* Receive Class Request */
            if (USB_PCDC_SET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Configure virtual UART settings */
                g_usb_on_usb.periControlDataGet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else if (USB_PCDC_GET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Send virtual UART settings back to host */
                g_usb_on_usb.periControlDataSet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else
            {
                /* ACK all other status requests */
                g_usb_on_usb.periControlStatusSet(&g_basic0_ctrl, USB_SETUP_STATUS_ACK);
            }
            break;
        case USB_STATUS_SUSPEND:
        case USB_STATUS_DETACH:
            break;
        default:           
            break;
      }      
      
      TimeoutElapse = bsp_gettimercnt() - starttimer;
      
      if(TimeoutElapse >= timeout)
      {
        break;
      }
            
      
    }while(event != USB_STATUS_WRITE_COMPLETE);
    

   
   if(fsp_err != 0)
   {
     return USER_USB_ERROR;
   }
   else 
   if(event == USB_STATUS_WRITE_COMPLETE)
   {
      return USB_OK;
   }
   else
   {
      return USER_USB_TIMEOUT;
   } 
 
}


/***********************************************************************************************************************
* Function Name: bsp_usb_receive
* Description  : This function initializes every macro.
* Arguments    : None
* Return Value : Non
* if timeout_us is zero timeout will not detect 
***********************************************************************************************************************/





uint16_t bsp_usb_receive(uint8_t *pbuff, uint16_t len,uint32_t timeout_ms)
{
    usb_event_info_t   event_info;
    usb_status_t       event;
    fsp_err_t          fsp_err = FSP_SUCCESS;
    
    uint32_t           starttimer = 0;
    uint32_t           timeout =  (uint32_t)(((uint64_t)COUNTER_PPR_MS * (uint64_t)timeout_ms));
    uint32_t           TimeoutElapse = 0;
    
    usb_info_t info;

#if 1
    
    do
    {
      g_usb_on_usb.eventGet(&event_info, &event);
      
      R_USB_InfoGet(&g_basic0_ctrl,&info,USB_CLASS_PCDC);
      
      switch (event)
      {
        case USB_STATUS_REQUEST:   /* Receive Class Request */
            if (USB_PCDC_SET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Configure virtual UART settings */
                g_usb_on_usb.periControlDataGet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else if (USB_PCDC_GET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Send virtual UART settings back to host */
                g_usb_on_usb.periControlDataSet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else
            {
                /* ACK all other status requests */
                g_usb_on_usb.periControlStatusSet(&g_basic0_ctrl, USB_SETUP_STATUS_ACK);
            }
            break;
        case USB_STATUS_SUSPEND:
        case USB_STATUS_DETACH:
            break;
        default:           
            break; 
      }  
    }while((info.device_status != USB_STATUS_CONFIGURED) || (event != USB_STATUS_NONE));

#endif    
    
    
    TimeoutElapse = 0;
    starttimer = bsp_gettimercnt();  

    if(g_p_usb_pstd_pipe[2] == (usb_utr_t *) USB_NULL)
    {
      fsp_err =  g_usb_on_usb.read(&g_basic0_ctrl, (pbuff), len, USB_CLASS_PCDC); 
    }

    do{
      g_usb_on_usb.eventGet(&event_info, &event);
      
      switch (event)
      {
        case USB_STATUS_REQUEST:   /* Receive Class Request */
            if (USB_PCDC_SET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Configure virtual UART settings */
                g_usb_on_usb.periControlDataGet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else if (USB_PCDC_GET_LINE_CODING == (event_info.setup.request_type & USB_BREQUEST))
            {
                /* Send virtual UART settings back to host */
                g_usb_on_usb.periControlDataSet(&g_basic0_ctrl, (uint8_t *) &s_g_line_coding, LINE_CODING_LENGTH);
            }
            else
            {
                /* ACK all other status requests */
                g_usb_on_usb.periControlStatusSet(&g_basic0_ctrl, USB_SETUP_STATUS_ACK);
            }
            break;
        case USB_STATUS_SUSPEND:
        case USB_STATUS_DETACH:
            break;
        default:           
            break;
      }      
      
      TimeoutElapse = bsp_gettimercnt() - starttimer;
      
      if(TimeoutElapse >= timeout)
      {
        break;
      }
            
      
    }while(event != USB_STATUS_READ_COMPLETE);
     
    

   if(fsp_err != 0)
   {

    return USER_USB_ERROR;
   }
   else 
     if(event == USB_STATUS_READ_COMPLETE)
   {
      return USB_OK;
   }
   else
   {
      return USER_USB_TIMEOUT;
   } 
}
