/**
 * @file canfd_protocol.h
 * @brief CANFD协议相关数据结构定义
 * @details 基于"人形机器人CAN及CANFD总线通信协议-V2.3"实现
 */

#ifndef CANFD_PROTOCOL_H
#define CANFD_PROTOCOL_H

#include <stdint.h>
#include <stdbool.h>


#define CANFD_SLAVE_ADDRESS 0x02
#define CANFD_MASTER_ADDRESS 0x01



/**
 * @brief CANFD帧头部结构体
 */
typedef struct {
    uint8_t seq:6;          ///< 包序号(6 bits, 0-63)
    uint8_t src:5;          ///< 源设备地址(5 bits, 0-31)
    uint8_t dest:5;         ///< 目标设备地址(5 bits, 0-31)
    uint8_t cmd:6;          ///< 命令码(6 bits, 1-63)
    uint8_t type:2;         ///< 包类型(2 bits, 0-3)
    uint8_t res:2;          ///< 预留位(2 bits, 默认0)
    uint8_t length:6;       ///< 数据净荷区长度(6 bits, 0-60)
} CANFD_FrameHeader;

/**
 * @brief 包类型定义
 */
typedef enum {
    PACKET_TYPE_READ = 0,   ///< 读取命令(发送方读取、查询接收方相关信息)
    PACKET_TYPE_SET1 = 1,   ///< 设置1命令(发送方设置、修改、通知接收方，接收方需应答)
    PACKET_TYPE_SET2 = 2,   ///< 设置2命令(主要用于周期性过程数据、实时数据、透传等，接收方无需应答)
    PACKET_TYPE_RESP = 3    ///< 应答(接收方对0和1的包类型应答)
} CANFD_PacketType;

/**
 * @brief CANFD标准帧ID组成部分
 */
typedef struct {
    uint16_t id_code:5;     ///< ID子码(5 bits)
    uint16_t isolation:3;   ///< 隔离码(3 bits, 默认0x07)
    uint16_t direction:2;   ///< 方向位(2 bits, 0:主到从; 1:从到主; 2:点对点; 3:未定义)
    uint16_t reserved:1;    ///< 预留位(1 bit, 默认0)
} CANFD_StandardID;

/**
 * @brief CANFD方向定义
 */
typedef enum {
    CANFD_DIR_MASTER_TO_SLAVE = 0,  ///< 主站到从站
    CANFD_DIR_SLAVE_TO_MASTER = 1,  ///< 从站到主站
    CANFD_DIR_POINT_TO_POINT = 2,   ///< 点对点
    CANFD_DIR_UNDEFINED = 3         ///< 未定义
} CANFD_Direction;



/**
 * @brief CANFD帧结构体
 */
typedef struct {
    uint32_t can_id;                        ///< CAN ID
    uint8_t  id_mode;                       ///< 标准帧或扩展帧
    uint8_t  type;                          ///< 帧类型
    uint8_t  dlc;                            ///< 数据长度码
    uint32_t options;                       ///< 实现相关选项
    CANFD_FrameHeader header;               ///< 帧头部结构
    uint8_t data[64 - sizeof(CANFD_FrameHeader)]; ///< 净荷数据区(最大60字节)
} CANFD_Frame;



/**
 * @brief CANFD帧结构体
 */
typedef struct {
    uint8_t windex;                        ///< 写入索引
    uint8_t rindex;                        ///< 读取索引
    uint8_t tx_data_num;                   ///< 发送FIFO中数据个数
    uint8_t tx_datamax_num;
    CANFD_Frame tx_data[16];               ///< 发送FIFO中数据
} CANFD_Frame_FIFO;


/**
 * @brief 电机控制模式定义
 */
typedef enum {
    MOTOR_MODE_DEFAULT = 0,     ///< 默认模式
    MOTOR_MODE_VF = 1,          ///< VF模式
    MOTOR_MODE_TORQUE = 2,      ///< 转矩模式
    MOTOR_MODE_VELOCITY = 3,    ///< 速度模式
    MOTOR_MODE_POSITION = 4,    ///< 位置模式
    MOTOR_MODE_FORCE = 5,       ///< 力控模式
    MOTOR_MODE_IMPEDANCE = 8,   ///< 导纳模式
    MOTOR_MODE_ZEROING = 9      ///< 回零模式
} CANFD_MotorMode;

/**
 * @brief 电机运行命令定义
 */
typedef enum {
    MOTOR_CMD_STOP = 0,         ///< 停止
    MOTOR_CMD_RUN = 1           ///< 运行
} CANFD_MotorRunCmd;

/**
 * @brief 电机运行状态定义
 */
typedef enum {
    MOTOR_STATE_STOPPED = 0,    ///< 已停止
    MOTOR_STATE_STOPPING = 1,   ///< 停止中
    MOTOR_STATE_RUNNING = 2,    ///< 运行中
    MOTOR_STATE_FAULT = 9       ///< 故障中
} CANFD_MotorState;

/**
 * @brief 校零状态定义
 */
typedef enum {
    ZEROING_STATE_NONE = 0,     ///< 未校零
    ZEROING_STATE_ZEROING = 1,  ///< 校零中
    ZEROING_STATE_DONE = 2,     ///< 已校零
    ZEROING_STATE_FAILED = 3    ///< 校零失败
} CANFD_ZeroingState;

/**
 * @brief 电机报警标志位定义
 */
typedef enum {
    ALARM_OVER_VOLTAGE = (1 << 0),      ///< 过压
    ALARM_UNDER_VOLTAGE = (1 << 1),     ///< 欠压
    ALARM_OVER_CURRENT = (1 << 2),      ///< 过流
    ALARM_PHASE_LOSS = (1 << 3),        ///< 缺相
    ALARM_OVERLOAD = (1 << 4),          ///< 过载
    ALARM_OVER_TEMP = (1 << 5),         ///< 过温
    ALARM_STALL = (1 << 6),             ///< 堵转
    ALARM_RUNAWAY = (1 << 7),           ///< 失控/飞车
    ALARM_HALL = (1 << 8),              ///< 霍尔
    ALARM_ENCODER1 = (1 << 9),          ///< 编码器1
    ALARM_ENCODER2 = (1 << 10),         ///< 编码器2
    ALARM_POSITIVE_LIMIT = (1 << 11),   ///< 正超程
    ALARM_NEGATIVE_LIMIT = (1 << 12),   ///< 负超程
    ALARM_SETTING_ERROR = (1 << 13),    ///< 设置错误
    ALARM_TRACKING_ERROR = (1 << 14),   ///< 跟随错误
    ALARM_OTHER = (1 << 15)             ///< 其它
} CANFD_AlarmFlags;

/**
 * @brief Firmware update operation codes
 */
typedef enum {
    FIRMWARE_OP_INFO = 0,        /**< Read firmware information */
    FIRMWARE_OP_START_UPDATE = 1, /**< Start firmware update */
    FIRMWARE_OP_DATA = 2,         /**< Firmware data transfer */
    FIRMWARE_OP_STATUS = 3        /**< Query update status */
} FirmwareOpCode_t;

/**
 * @brief Firmware update status codes
 */
typedef enum {
    FIRMWARE_STATUS_NONE = 0,     /**< Not in update mode */
    FIRMWARE_STATUS_UPDATING = 1, /**< Update in progress */
    FIRMWARE_STATUS_SUCCESS = 2,  /**< Update successful */
    FIRMWARE_STATUS_FAILED = 3,    /**< Update failed */
    FIRMWARE_STATUS_CRC_ERROR = 4,   /**< CRC error */
    FIRMWARE_STATUS_LENGTH_ERROR = 5,    /**< Length error */
    FIRMWARE_STATUS_TIMEOUT = 6,    /**< Timeout */
    FIRMWARE_STATUS_FLASH_WRITE_ERROR = 7    /**< Flash write error */
} FirmwareStatus_t;


/**
 * @brief Firmware update status codes
 */
typedef enum {
    FIRMWARE_DATA_REQUEST = 0,     /**< Data request */
    FIRMWARE_DATA_RESPONSE = 1,    /**< Data response */
    FIRMWARE_WRITE_FLASH = 2,       /**< Data write */
    FIRMWARE_CHECKSUM = 3,    /**< Data checksum */
    FIRMWARE_UPDATE_END = 4,    /**< Update end */
} FirmUpdateStep_t;

/**
 * @brief Firmware update start response codes
 */
typedef enum {
    FIRMWARE_START_NO_NEED = 0,   /**< No need to update */
    FIRMWARE_START_STARTED = 1,   /**< Update started */
    FIRMWARE_START_ERROR = 2,     /**< Firmware error */
    FIRMWARE_START_PAUSED = 3     /**< Update paused */
} FirmwareStartResponse_t;


/**
 * @brief 位置模式参数
 */
typedef struct {
    uint16_t targetPosition;     ///< 目标位置   
} CANFD_PositionMode;

/**
 * @brief 速度模式参数
 */
typedef struct {
    uint16_t targetVelocity;     ///< 目标速度(单位：0.01RPM)
    uint16_t accelerationTime;  ///< 加速时间(单位：毫秒)
} CANFD_VelocityMode;

/**
 * @brief 转矩模式参数
 */
typedef struct {
    uint16_t targetTorque;       ///< 目标转矩  [-32768->NegMaxTrq,32767-> PosMaxTrq]    
} CANFD_TorqueMode;

/**
 * @brief 导纳模式参数
 */
typedef struct {
    uint16_t targetPosition;     ///< 目标位置  enc2 cnt 
    uint16_t targetVelocity;     ///< 目标速度 0.01rpm
    uint16_t torqueFeedforward;  ///< 转矩前馈 [-32768->NegMaxTrq,32767-> PosMaxTrq]    
    uint16_t positionKp;        ///< 位置增益Kp(Q15标幺化)
    uint16_t velocityKd;        ///< 速度增益Kd(Q15标幺化)
} CANFD_ImpedanceMode;

/**
 * @brief 回零模式参数
 */
typedef struct {
    uint8_t zeroingMethod;      ///< 回零方式(0:默认方式 1:正向堵转回零 2:反向堵转回零)
    uint16_t currentLimit;      ///< 回零电流限制(单位：10毫安)
    uint16_t highSpeed;         ///< 回零高速(单位：RPM)
    uint16_t lowSpeed;          ///< 回零低速(单位：RPM)
    uint16_t maxTime;           ///< 回零最大时间(单位：0.1s)
    int32_t maxTravel;          ///< 回零最大行程
} CANFD_ZeroingMode;

/**
 * @brief 电机控制参数结构体
 */
typedef struct {
    uint8_t controlMode;        ///< 控制模式(参见CANFD_MotorMode)
    uint8_t runCommand;         ///< 运行命令(参见CANFD_MotorRunCmd)

    uint16_t targetPosition;     ///< 目标位置  enc2 cnt 
    uint16_t targetVelocity;     ///< 目标速度 0.01rpm
    uint16_t targetTorque;       ///< 目标转矩  [-32768->NegMaxTrq,32767-> PosMaxTrq]    
    uint16_t torqueFeedforward;  ///< 转矩前馈 [-32768->NegMaxTrq,32767-> PosMaxTrq]    
    uint16_t positionKp;        ///< 位置增益Kp(Q15标幺化)
    uint16_t velocityKd;        ///< 速度增益Kd(Q15标幺化)

    uint8_t  zeroingMethod;      ///< 回零方式(0:默认方式 1:正向堵转回零 2:反向堵转回零)
    uint16_t currentLimit;      ///< 回零电流限制(单位：10毫安)
    uint16_t highSpeed;         ///< 回零高速(单位：RPM)
    uint16_t lowSpeed;          ///< 回零低速(单位：RPM)
    uint16_t maxTime;           ///< 回零最大时间(单位：0.1s)
    int32_t  maxTravel;          ///< 回零最大行程    
    uint8_t  TargetSetFlag;      ///< 指令设置标志
      
} CANFD_MotorControl;

/**
 * @brief 电机状态信息结构体
 */
typedef struct {
    uint16_t runningState;       ///< 运行状态(参见CANFD_MotorState)
    uint8_t controlMode;        ///< 控制模式(参见CANFD_MotorMode)
    uint8_t zeroingState;       ///< 校零状态(参见CANFD_ZeroingState)

    struct {
        union {
            uint8_t alarm1;
            struct {
                uint8_t OverVoltage:1;
                uint8_t UnderVoltage:1;
                uint8_t OverCurrent:1;
                uint8_t PhaseLoss:1;
                uint8_t Overload:1;
                uint8_t OverTemp:1;
                uint8_t Stall:1;
                uint8_t Runaway:1;
            } alarm1_b;
        };
 
        union {
            uint8_t alarm2;
            struct {
                uint8_t Hall:1;
                uint8_t Encoder1:1;              
                uint8_t Encoder2:1;
                uint8_t PositiveLimit:1;
                uint8_t NegativeLimit:1;
                uint8_t SettingError:1;
                uint8_t TrackingError:1;
                uint8_t CanBreak:1;
            } alarm2_b;
        };

        union {
            uint8_t alarm3;
            struct {
                uint8_t AlmIpm:1;
                uint8_t OverloadWrn:1;
                uint8_t IPMOvertTemp:1;
                uint8_t PosLimitWrn:1;
                uint8_t res5:1;
                uint8_t res6:1;
                uint8_t res7:1;
                uint8_t res8:1;
            } alarm3_b;
        } ;
    } alarmFlags;


    uint16_t  currentQ;          ///< 电流(标幺化 -300.0f->300.0fA)
    uint16_t  voltageQ;          ///< 电压(标幺化 0.0f->100.0fV)
    uint16_t  velocityQ;         ///< 速度(标幺化)
    uint16_t  positionQ;         ///< 位置(标幺化)
    uint16_t  torqueQ;           ///< 转矩(标幺化 -32768->NegMaxTrq,32767-> PosMaxTrq)
    uint16_t  torquefdbQ;         ///< 转矩反馈(标幺化 -32768->NegMaxTrq,32767-> PosMaxTrq)


    float velocityrads;        ///< 速度(rad/s)
    float positionrad;         ///< 位置 rad

    uint8_t AppOrBoot;         //0 : app 1:boot
    uint8_t DevIsRready;       //0 : not ready 1:ready
    uint8_t DevHardVersion;
    uint8_t DevBootVersion;
    uint8_t DevAppVersion;
    uint8_t FirmwareCode;    
} CANFD_MotorStatus;




/**
 * @brief 回调函数类型定义
 */
typedef void (*CANFD_MotorControlCallback_t)(CANFD_MotorControl *controlData);



/**
 * @brief 初始化CANFD从机
 */
void Canfd_Slave_Init(void);

/**
 * @brief 监控CANFD状态
 */
void Canfd_State_Monitor(void);

/**
 * @brief 电机使能
 * 
 * @param Control 电机使能状态
 * @return uint8_t 电机使能状态
 */
uint8_t Canfd_ServoCtrl(uint8_t Control);

/**
 * @brief 获取电机报警状态
 * 
 * @param void 
 * @return void 
 */
void Canfd_GetServoAlarm(void);

/**
 * @brief 发送CANFD消息
 * 
 * @param void 
 * @return void 
 */
uint8_t canfd_send(void);

/**
 * @brief 电机控制回调函数
 * 
 * @param controlData 电机控制数据
 * @return void 
 */
void MotorControlCallback(CANFD_MotorControl *controlData);

/**
 * @brief 固件处理
 * 
 * @param void 
 * @return void 
 */
void firmware_process(void);

/**
 * @brief 控制器校准
 * 
 * @param void 
 * @return void 
 */
void Canfd_CtrlCoff_Cal(void);

/**
 * @brief 发送CANFD消息
 * 
 * @param void 
 * @return void 
 */
uint8_t CanFd_Send(void) ;

/**
 * @brief 获取IPD位置目标
 * 
 * @param void 
 * @return void 
 */
float Canfd_GetIpdPosTarget(void);

/**
 * @brief 获取IPD速度目标
 * 
 * @param void 
 * @return void 
 */
float Canfd_GetIpdSpdTarget(void);   

/**
 * @brief 获取IPD转矩目标
 * 
 * @param void 
 * @return void 
 */
float Canfd_GetIpdTrqFf(void);     

/**
 * @brief 更新IPD位置目标
 * 
 * @param void 
 * @return void 
 */
void Canfd_SetIpdPosTarget(float pos);  

/**
 * @brief 更新IPD速度目标
 * 
 * @param void 
 * @return void 
 */
void Canfd_SetIpdSpdTarget(float spd);  

/**
 * @brief 更新IPD转矩前馈
 * 
 * @param void 
 * @return void 
 */
void Canfd_SetIpdTrqFf(float trq);      

/**
 * @brief 获取IPD位置目标下限
 * 
 * @param void 
 * @return float 
 */

float Canfd_GetIpdPosNLmit  (void);

/**
 * @brief 获取IPD位置目标上限
 * 
 * @param void 
 * @return float 
 */

float Canfd_GetIpdPosPLmit  (void) ;     



/**
 * @brief   获取IPD转矩目标上限
 * 
 * @param void 
 * @return float 
 */
float Canfd_GetIpdTrqLimit(void);


/**
 * @brief 设置CAN断线保护使能
 * 
 * @param enable 使能状态
 */
void Canfd_SetCanBreakEnable(uint8_t enable);
    

/**
 * @brief 获取电机超时状态
 * 
 * @param void 
 * @return uint8_t 
 */
uint8_t Canfd_GetTimeoutFlag(void);

/**
 * @brief 重置电机超时状态
 * 
 * @param void 
 * @return void 
 */
void Canfd_ResetTimeoutFlag(void);


#endif /* CANFD_PROTOCOL_H */ 