/****************************************************************************************************
 *
 * FILE NAME:  BaseSequence.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.08.13
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	13-08-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseSequence.h"
#include "HardApi.h"
#include "LostPhaseCheck.h"

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BeSeqMakeServoReadySignal(BE_SEQ_HNDL *BeSeq, BOOL ready_cond, INT32 PhaseReady )
{
	if(BeSeq->SvonEnable && ready_cond)
	{
		/* Servo Ready flag set (Ignore Phase) */
		BeSeq->SvReadyPhaseIgnore = TRUE;
		/* Servo Ready flag set */
		if( PhaseReady )
		{
			BeSeq->ServoReady = TRUE;
		}
		else
		{
			BeSeq->ServoReady = FALSE;
		}
	}
	else
	{
		/* Servo Ready flag set (Ignore Phase) */
		BeSeq->SvReadyPhaseIgnore = FALSE;
		/* Servo Ready flag set */
		BeSeq->ServoReady = FALSE;
	}
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BeSeqMakeServoOnRequest( BE_SEQ_HNDL *BeSeq, BOOL ComSvonReq, 
                                                                       FUN_CMN_CONTROL *FnCmnCtrl,
                                                                        ALARM *AlmMngr)
{
	BOOL ServoOnReq,PdetReq;

	if(FnCmnCtrl->FnSvControl != FALSE  && !AlmMngr->Status.AlmFlag)
	{
		ServoOnReq = FnCmnCtrl->FnSvonReq;
		PdetReq = FnCmnCtrl->SvOnPdetReq;				
		FnCmnCtrl->State.LockNormalOpeDrive = TRUE;
	}
	else 
	{
		ServoOnReq = ComSvonReq;
	}
	
	ServoOnReq = ComSvonReq;
	
	BeSeq->SvonReq = ServoOnReq;

//	if(BeSeq->SvonReq == TRUE && BeSeq->ServoReady == FALSE)
//	{
//		ALMSetServoAlarm( AlmMngr, ALM_PUV ); 
//	}
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL BeSeqMechaBrakeSequence( BE_SEQ_HNDL *BeSeq, UINT16 CtrlMode,
                                                          INT32 AbsMotSpd, BOOL HwbbStatus)
{
	BK_SEQ_DATA 	*BkSeqData;
	ALM_STOP		*AlmStop; 
	FORCE_STOP		*ForceStop;  

	BkSeqData = &BeSeq->BkSeqData;

	AlmStop = &BeSeq->AlmStop;	
	ForceStop = &BeSeq->ForceStop;	

	switch( BkSeqData->V.BkStep )
	{

	case 0: /* Initial Step */
		BkSeqData->V.Brake = TRUE;  /* Brake command output */
		BkSeqData->V.BkZeroStop = FALSE;			
		BkSeqData->V.BkLinDecStop = FALSE;			
		BkSeqData->V.BkWait = 0;
		BkSeqData->V.BkStep = 1;
		break;

	case 1:  /* Servo off and waiting for servo on */
		if( BeSeq->SvonReq && BeSeq->SvReadyPhaseIgnore )
		{
			/* Base Enable Request ON */
			BkSeqData->V.BkBeReq = TRUE;
			BeSeq->CmdEnable = FALSE;

			if( BeSeq->BeReqSeqOut)
			{
				/* Brake Release */
				BkSeqData->V.Brake = FALSE;
                BkSeqData->V.BkStep = 2;
				BkSeqData->V.BkWait = 0;
			}
			
		}
		else
		{
			/* Base Enable Request OFF */
			BkSeqData->V.BkBeReq = FALSE;
            BkSeqData->V.BkLinDecStop = FALSE;
		}
		break;

	case 2: /* Servo is on and waiting for servo off or servo ready off  */
		if( BeSeq->SvonReq && BeSeq->SvReadyPhaseIgnore )
		{
			BkSeqData->V.BkWait ++;
			if(BkSeqData->V.BkWait>= BkSeqData->P.BkoffDelayTime)
			{
				BeSeq->CmdEnable = TRUE;
				BkSeqData->V.BkWait = BkSeqData->P.BkoffDelayTime;
			}
		}
		else if( (AlmStop->V.Gr2ZeroStop == TRUE) 	
			|| (ForceStop->V.FstpZeroStop == TRUE))	
		{ 
			;										
		}
		else if( (AlmStop->V.Gr2LinDecStop == TRUE)
			|| (ForceStop->V.FstpLinDecStop == TRUE) )
		{
			BkSeqData->V.BkStep = 3;
		}
		else if( AbsMotSpd >= BkSeqData->P.BkonSpdLevel )
		{ 
			BkSeqData->V.BkBeReq = FALSE; 	
			BkSeqData->V.BkStep = 4;		
			BkSeqData->V.BkWait = 0;
		}
		else if( (AlmStop->V.AlmStopMode >= STOPMODE_DBSTOPDB) 
			  || (ForceStop->V.FstpMode >= STOPMODE_DBSTOPDB)  
				|| (BkSeqData->P.BkonDelayTime == 0)
				|| (HwbbStatus == TRUE) )
		{												
			BkSeqData->V.BkBeReq = FALSE; 			
			BkSeqData->V.Brake = TRUE;				
			BkSeqData->V.BkStep = 1;				
		}
		else										
		{											
			BkSeqData->V.Brake = TRUE;				
			if( CtrlMode == BASE_MODE_TRQ ) 		
			{											
				BkSeqData->V.BkZeroStop = FALSE;
			}
			else
			{											
				BkSeqData->V.BkZeroStop = TRUE;
			}
			BkSeqData->V.BkStep = 5;		
			BkSeqData->V.BkWait = 0;
		}
		break;

	case 3: /* Servo is on and waiting for servo off or servo ready off */
		if( BeSeq->SvonReq && BeSeq->SvReadyPhaseIgnore )
		{ 
			;											
		}
		else if( (AlmStop->V.Gr2LinDecStop == TRUE)
			|| (ForceStop->V.FstpLinDecStop == TRUE))
		{ 
			;										
		}
		else if( (AlmStop->V.Gr2ZeroStop == TRUE)
			|| (ForceStop->V.FstpZeroStop == TRUE) )
		{
			BkSeqData->V.BkStep = 2;
			BkSeqData->V.BkWait = 0;
		}
		else if( AbsMotSpd >= BkSeqData->P.BkonSpdLevel )
		{ 
			BkSeqData->V.BkBeReq = FALSE; 			
			BkSeqData->V.BkStep = 4;			
			BkSeqData->V.BkWait = 0;
		}
		else if( (AlmStop->V.AlmStopMode >= STOPMODE_DBSTOPDB)
				|| (ForceStop->V.FstpMode >= STOPMODE_DBSTOPDB)
				|| (BkSeqData->P.BkonDelayTime == 0)
				|| (HwbbStatus == TRUE) )
		{												
			BkSeqData->V.BkBeReq = FALSE; 		
			BkSeqData->V.Brake = TRUE;			
			BkSeqData->V.BkStep = 1;					
		}
		else											
		{								
			BkSeqData->V.Brake = TRUE;				
			if( CtrlMode == BASE_MODE_TRQ ) 	
			{											
				BkSeqData->V.BkLinDecStop = FALSE;
			}
			else
			{										
				BkSeqData->V.BkLinDecStop = TRUE;
			}
			BkSeqData->V.BkStep = 5;
			BkSeqData->V.BkWait = 0;
		}
		break;

	case 4: /* Waiting for motor speed to drop due to servo-off during motor operation  */
		BkSeqData->V.BkWait ++;
		if( (BkSeqData->V.BkWait >= BkSeqData->P.BkonWaitTime)
			|| (AbsMotSpd <= BkSeqData->P.BkonSpdLevel) )
		{												
			BkSeqData->V.Brake = TRUE;				
			BkSeqData->V.BkStep = 1;
			BkSeqData->V.BkWait = 0;
		}
		break;

	case 5: /* Waiting for base bolck until the brake is applied with the servo off while the motor is stopped */
		BkSeqData->V.BkWait ++;
		if( (BkSeqData->V.BkWait >= BkSeqData->P.BkonDelayTime)
			|| (HwbbStatus == TRUE) )
		{											
			BkSeqData->V.BkBeReq = FALSE; 			
			BkSeqData->V.BkZeroStop = FALSE;		
			BkSeqData->V.BkStep = 1;
			BkSeqData->V.BkWait = 0;
		}
		break;

	default: 
		break;
	}

	return BkSeqData->V.BkBeReq;

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 BeSeqStopModeManager( BE_SEQ_HNDL *BeSeq)
{
	INT32	  StopMode;
	BOOL	  ZeroStopReq;
	BOOL	  LinDecStopReq;	
	INT32	  Gr2DbopReq;	
	INT32	  FstpDbopReq;	 
	INT32	  OtDbopReq;

	StopMode = BeSeq->StopMode;
	Gr2DbopReq = BeSeq->AlmStop.V.Gr2DbopReq;		  
	FstpDbopReq = BeSeq->ForceStop.V.FstpDbopReq;	  
	
	OtDbopReq = BeSeq->OtSeqData.var.OtDbopReq;

	ZeroStopReq = BeSeq->BkSeqData.V.BkZeroStop
			    | BeSeq->AlmStop.V.Gr2ZeroStop 
			    | BeSeq->ForceStop.V.FstpZeroStop
				| BeSeq->OtSeqData.var.OtZeroStop;

	LinDecStopReq = BeSeq->AlmStop.V.Gr2LinDecStop
		          | BeSeq->BkSeqData.V.BkLinDecStop
			      | BeSeq->ForceStop.V.FstpLinDecStop
			      | BeSeq->OtSeqData.var.OtLinDecStop;


	if( (BeSeq->AlmStop.V.AlmStopMode >= STOPMODE_DBSTOPDB)   
	|| (BeSeq->ForceStop.V.FstpMode >= STOPMODE_DBSTOPDB))   
	{
		StopMode = BeSeq->AlmStop.P.StopMode;				  
	}
	else if(( BeSeq->BkSeqData.V.BkBeReq == FALSE ) 			
	   &&(BeSeq->AlmStop.V.AlmStopMode == STOPMODE_NOTSTOP )	  
	   &&(BeSeq->ForceStop.V.FstpMode == STOPMODE_NOTSTOP ))	 
	{
		StopMode = BeSeq->AlmStop.P.StopMode;				 
	}
	else if( (BeSeq->OtSeqData.var.OtBeReq == FALSE) && (OtDbopReq == OTDBOP_DEF) )
	{ //OT
	   if( BeSeq->AlmStop.P.StopMode == STOPMODE_DBSTOPDB )
	   { 
		   StopMode = STOPMODE_DBSTOPFR;
	   }
	   else
	   {
		   //
		   StopMode = BeSeq->AlmStop.P.StopMode;
	   }
	}
	else if(((BeSeq->OtSeqData.var.OtBeReq == FALSE) && (OtDbopReq == OTDBOP_FREE) )
	   || ((BeSeq->BkSeqData.V.BkBeReq == FALSE) && (Gr2DbopReq == GR2DBOP_FREE))	
	   || ((BeSeq->BkSeqData.V.BkBeReq == FALSE) && (FstpDbopReq == FSTPDBOP_FREE)) )  
	{ 												 
		StopMode = STOPMODE_FREERUN;				
	}
	else if( ZeroStopReq )		
	{
		StopMode = STOPMODE_ZEROSTOP; 				
	}
	else if( LinDecStopReq )		
	{
		StopMode = STOPMODE_LDECSTOP; 				 
	}
	else												
	{
	  StopMode = STOPMODE_NOTSTOP;				
	}

	BeSeq->StopMode = StopMode;
	return StopMode;

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT32 BeSeqControlModeManager( BE_SEQ_HNDL *BeSeq, FUN_CMN_CONTROL *FnCmnCtrl )
{

	CTRLMD	CtrlMcmd;

	if(FnCmnCtrl->FnSvControl != FALSE)
	{/* Driving in Fn mode*/
		/* TaskC Control Mode <= ROUND request mode*/
		CtrlMcmd.dw = (UINT32)FnCmnCtrl->FnCtrlMcmd;
	}
	else
	{	/* ScanC Control Mode <= NO CMD */
		CtrlMcmd.dw = CTRL_MODE_NOCMD;
	}

	/* TaskC: Zero control mode command */
	if( BeSeq->StopMode == STOPMODE_ZEROSTOP )			
	{
		CtrlMcmd.b.zm = BASE_MODE_ZSTOP; 			
	}
	else if( BeSeq->StopMode == STOPMODE_LDECSTOP ) 
	{
		CtrlMcmd.b.zm = BASE_MODE_LDSTOP;			
	}
	else if( BeSeq->OtSeqData.var.OtZeroClamp ) 		
	{
		CtrlMcmd.b.zm = BASE_MODE_ZCLMP; 				
	}
//	else if( BeSeq->ZcCtrlData.ZcZeroClamp )			
//	{
//		CtrlMcmd.b.zm = BASE_MODE_ZCLMP; 				
//	}
	else										
	{
		CtrlMcmd.b.zm = BASE_MODE_ZNONE; 			
	}

	return CtrlMcmd.dw;

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BeSeqInputAlmStopMode( BE_SEQ_HNDL *BeSeq, INT32 AlmStopMode,   
                            						UINT16 CtrlMode, BOOL PhaseReady, BOOL MotStop)  
{
	ALM_STOP *AlmStop;

	AlmStop = &(BeSeq->AlmStop);

	if( AlmStopMode == STOPMODE_ZEROSTOP )					
	{
		if( (CtrlMode == BASE_MODE_TRQ)					// torque control mode?		
		||	(AlmStop->P.G2AlmStopMode == GR2STOPMODE_DEFAULT)
		||(PhaseReady == FALSE) )								// During magnetic pole detection?
		{
			AlmStopMode = AlmStop->P.StopMode;
		}
		else
		{	
			AlmStopMode = AlmStop->P.G2AlmStopMode;
		}
	}
	AlmStop->V.AlmStopMode = AlmStopMode;

    /* Alarm stop sequence */
	if( BeSeq->LdstpTimeZero == TRUE )
	{	
		if( AlmStop->V.AlmStopMode == GR2STOPMODE_LDSTOPDB )
		{
			AlmStop->V.AlmStopMode = GR2STOPMODE_ZSTOPDB;
		}
		else if( AlmStop->V.AlmStopMode == GR2STOPMODE_LDSTOPFR )
		{
			AlmStop->V.AlmStopMode = GR2STOPMODE_ZSTOPFR;
		}
		else
		{/* do nothing */
			;
		}
	}
	
	switch( AlmStop->V.Gr2StopStep )
	{
	case 0:	/* Initial Step */
		AlmStop->V.Gr2TrqLimit = FALSE;
		AlmStop->V.Gr2ZeroStop = FALSE;
		AlmStop->V.Gr2LinDecStop = FALSE;
		AlmStop->V.Gr2DbopReq = GR2DBOP_NONE;
		AlmStop->V.Gr2StopStep = 1;
		break;

	case 1:	/* Branch by Alm stop mode */

		switch( AlmStop->V.AlmStopMode )
		{
			case GR2STOPMODE_ZSTOPDB:			
			case GR2STOPMODE_ZSTOPFR:			
				AlmStop->V.Gr2TrqLimit = TRUE;
				AlmStop->V.Gr2ZeroStop = TRUE;
				AlmStop->V.Gr2DbopReq = GR2DBOP_NONE;
				AlmStop->V.Gr2StopStep = 2;
				break;
			case GR2STOPMODE_LDSTOPDB:			
			case GR2STOPMODE_LDSTOPFR:			
				AlmStop->V.Gr2LinDecStop = TRUE;
				AlmStop->V.Gr2DbopReq = GR2DBOP_NONE;
				AlmStop->V.Gr2StopStep = 3;
				break;
			default:
				break;
		}
		break;

	case 2:	/* During zero speed stop, cancel alarm stop / waiting for motor stop  */
		if( (AlmStop->V.AlmStopMode != GR2STOPMODE_ZSTOPDB)
		 && (AlmStop->V.AlmStopMode != GR2STOPMODE_ZSTOPFR) )
		{
			AlmStop->V.Gr2TrqLimit = FALSE;
			AlmStop->V.Gr2ZeroStop = FALSE;
			AlmStop->V.Gr2DbopReq = GR2DBOP_NONE;
			AlmStop->V.Gr2StopStep = 1;
		}
		else if( MotStop )
		{
			AlmStop->V.Gr2ZeroStop = FALSE;
			if( AlmStop->V.AlmStopMode == GR2STOPMODE_ZSTOPFR )
			{
				AlmStop->V.Gr2DbopReq = GR2DBOP_FREE;
			}
		}
		else
		{/* do nothing */
			;
		}
		break;

	case 3:	/* During linear deceleration stop, cancel alarm stop / waiting for motor stop */
		if( (AlmStop->V.AlmStopMode != GR2STOPMODE_LDSTOPDB)
		 && (AlmStop->V.AlmStopMode != GR2STOPMODE_LDSTOPFR) )
		{
			AlmStop->V.Gr2LinDecStop = FALSE;
			AlmStop->V.Gr2DbopReq = GR2DBOP_NONE;
			AlmStop->V.Gr2StopStep = 1;
		}
		else if( MotStop )
		{
			AlmStop->V.Gr2LinDecStop = FALSE;
			if( AlmStop->V.AlmStopMode == GR2STOPMODE_LDSTOPFR )
			{
				AlmStop->V.Gr2DbopReq = GR2DBOP_FREE;
			}
		}
		else
		{/* do nothing */
			;
		}
		break;
	default:
		break;
	}
/* <S1F5> End */
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BeSeqForceStopMode( BE_SEQ_HNDL *BeSeq, UINT16 CtrlMode, BOOL MotStop, BOOL FstpSts )
{
	FORCE_STOP     *ForceStop;
	ALM_STOP       *AlmStop;

	ForceStop = &( BeSeq->ForceStop );
	AlmStop = &( BeSeq->AlmStop );

	switch( ForceStop->V.FstpStopStep )
	{
	case 0:	/* Initial Step */
		ForceStop->V.FstpTrqLimit = FALSE;
		ForceStop->V.FstpZeroStop = FALSE;
		ForceStop->V.FstpLinDecStop = FALSE;
		ForceStop->V.FstpDbopReq = FSTPDBOP_NONE;
		ForceStop->V.FstpMode = STOPMODE_NOTSTOP;
		ForceStop->V.FstpStopStep = 1;
		break;

	case 1:	/* Forced stop monitoring (non-forced stop state) */
		if( FstpSts == TRUE )
		{
			if( (CtrlMode == BASE_MODE_TRQ)
			 || (ForceStop->P.ForceStopMode == FSTOPMODE_DEFAULT) )
			{
				ForceStop->V.FstpMode = AlmStop->P.StopMode;

				break;
			}
			else
			{
				ForceStop->V.FstpMode = ForceStop->P.ForceStopMode;

				if( BeSeq->LdstpTimeZero == TRUE )
				{	
					if( ForceStop->V.FstpMode == FSTOPMODE_LDSTOPDB )
					{
						ForceStop->V.FstpMode = FSTOPMODE_ZSTOPDB;
					}
					else if( ForceStop->V.FstpMode == FSTOPMODE_LDSTOPFR )
					{
						ForceStop->V.FstpMode = FSTOPMODE_ZSTOPFR;
					}
					else
					{
						;
					}
				}
			}
			
			switch( ForceStop->V.FstpMode )
			{
			case FSTOPMODE_ZSTOPDB:
			case FSTOPMODE_ZSTOPFR:
				ForceStop->V.FstpTrqLimit = TRUE;
				ForceStop->V.FstpZeroStop = TRUE;
				ForceStop->V.FstpDbopReq = FSTPDBOP_NONE;
				ForceStop->V.FstpStopStep = 2;
				break;
			case FSTOPMODE_LDSTOPDB:
			case FSTOPMODE_LDSTOPFR:
				ForceStop->V.FstpLinDecStop = TRUE;
				ForceStop->V.FstpDbopReq = FSTPDBOP_NONE;
				ForceStop->V.FstpStopStep = 3;
				break;
			default:
				break;
			}
		}
		else
		{
			ForceStop->V.FstpMode = STOPMODE_NOTSTOP;
		}
		break;

	case 2:	/* During zero speed stop, forced stop release / motor stop wait */
		if( FstpSts == FALSE )
		{
			ForceStop->V.FstpTrqLimit = FALSE;
			ForceStop->V.FstpZeroStop = FALSE;
			ForceStop->V.FstpDbopReq = FSTPDBOP_NONE;
			ForceStop->V.FstpStopStep = 1;
		}
		else if( MotStop )
		{
			ForceStop->V.FstpZeroStop = FALSE;
			if( ForceStop->V.FstpMode == FSTOPMODE_ZSTOPFR )		
			{
				ForceStop->V.FstpDbopReq = FSTPDBOP_FREE;
			}
		}
		else
		{/* do nothing */
			;
		}
		break;

	case 3:	/* During linear deceleration stop, forced stop release / motor stop wait */
		if( FstpSts == FALSE )
		{
			ForceStop->V.FstpLinDecStop = FALSE;
			ForceStop->V.FstpDbopReq = FSTPDBOP_NONE;
			ForceStop->V.FstpStopStep = 1;
		}
		else if( MotStop )
		{
			ForceStop->V.FstpLinDecStop = FALSE;
			if( ForceStop->V.FstpMode == FSTOPMODE_LDSTOPFR )		
			{
				ForceStop->V.FstpDbopReq = FSTPDBOP_FREE;
			}
		}
		else
		{/* do nothing */
			;
		}
		break;
	default:
		break;
	}
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void BeSeqChargePumpControl( PUMP_CTR_STR *PumpCtrl, BOOL OCState, BOOL NegPumpReq, UINT16 AxisID )
{
	/* Current detection: AD zero adjustment */
	if( PumpCtrl->AdjPumponReq == TRUE ) /* Zero adjustment pump ON request check */
	{
		if( ( PumpCtrl->SvonPumpCnt & 0x0001 ) == 0 ) /* ON / OFF in taskB cycle */
		{
			hApi_ChargePumpOff(AxisID);			
		}
		else
		{
			hApi_ChargePumpOn(AxisID);				
		}
		PumpCtrl->SvonPumpCnt++;
		PumpCtrl->PumpOnComp = FALSE;					/* Charge pump incomplete */
	}
	/* taskC : Charge Pump Control Request */
	else if( PumpCtrl->PumpOnReq == TRUE ) /* taskC pump ON request check */
	{
		if( OCState != FALSE )
		{
			hApi_ChargePumpOff(AxisID);
			return;
		}
	
		switch(PumpCtrl->PumpOnSeq)
		{
		case 0: /* Wait for Relay ON */
			if( (PumpCtrl->TimerCnt++) > 50 )
			{
				/* Perform alarm latch reset before base enable */
			    // todo :reset alarm
                              PumpCtrl->TimerCnt = 0;
                              PumpCtrl->SvonPumpCnt = 0;
                              PumpCtrl->PumpOnSeq = 1;
                              LostPhaseCheckParaInit(AxisID);
			}
			break;
                 case 1: /* Phase Lost Check */

			if(LostPhaseCheck1Ms(AxisID))
			{
                              PumpCtrl->TimerCnt = 0;
                              PumpCtrl->SvonPumpCnt = 0;
                              PumpCtrl->PumpOnSeq = 2;
			}
			break;                        

		case 2: /* Charg Pump ON/OFF */
//			if( ( PumpCtrl->SvonPumpCnt & 0x0001 ) == 0 )
//			{
//				hApi_ChargePumpOff(AxisID);
//			}
//			else
			{
                              hApi_ChargePumpOn(AxisID);
			}
			
			if(PumpCtrl->SvonPumpCnt > 23) /* 24ms */
			{
                            hApi_ChargePumpOff(AxisID);
                            PumpCtrl->PumpOnComp = TRUE; 	/* Charge pump complete */
                            PumpCtrl->PumpOnSeq = 3;
			}

			PumpCtrl->SvonPumpCnt++;
			break;
	
			default: /* Complete state */
				break;
		}
	}
	/* ScanC : The Charge pump ON/OF Switch Control */	
	else if( NegPumpReq == TRUE ) /* SCAN C: N-Arm ON required to checking */
	{
		if( PumpCtrl->NegPumpCnt <=128 )/* Hardware Requirements */
		 {
			if( ( PumpCtrl->NegPumpCnt & 0x0001 ) == 0 )/* Bit computing */
			 {
				 hApi_ChargePumpOff(AxisID);/* ChargePumpOFF */
			 }
			 else
			 {
				 hApi_ChargePumpOn(AxisID);/* ChargePumpON */
			 }
			 PumpCtrl->NegPumpCnt++;/* Bit computing */
		 }
		 else
		 {
			  hApi_ChargePumpOn(AxisID);/* ChargePumpON */
		 }
	}
	/* No request */
	else
	{
		PumpCtrl->TimerCnt = 0;
		PumpCtrl->PumpOnSeq = 0;
		PumpCtrl->NegPumpCnt = 0;  
		PumpCtrl->PumpOnComp = FALSE;		
		hApi_ChargePumpOff(AxisID);	
	}
	return;

}


/****************************************************************************************************
* DESCRIPTION:
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC BOOL BeSeqDynamicBrakeSequence( BE_SEQ_HNDL *BeSeq, INT32 MotStop, BOOL EncDataNG, UINT16 AxisID )	
{

	BOOL				DbOnRequest;
	DB_SEQ_DATA 		*DbSeqData;
	PUMP_CTR_STR 		*PumpOnCtrl;

	DbSeqData = &BeSeq->DbSeqData;
	PumpOnCtrl = &BeSeq->ChargePump;
	DbOnRequest = BeSeq->DbOn;

    /* Creating a base enable request */
	DbSeqData->DbBeReq = BeSeq->BkSeqData.V.BkBeReq & BeSeq->OtSeqData.var.OtBeReq;

	switch( DbSeqData->DbStep )
	{

		case 0: /* Initial Step */
			/* BaseBlock */
			hApi_BaseBlock(AxisID); 

			PumpOnCtrl->PumpOnReq = FALSE;
			BeSeq->BeReqSeqOut = FALSE;

			DbSeqData->DbWait = 0;

			if( BeSeq->StopMode == STOPMODE_FREERUN )
			{
				DbOnRequest = FALSE;
				DbSeqData->DbStep = 1;
			}
			else if( BeSeq->StopMode == STOPMODE_DBSTOPFR )
			{
				if( EncDataNG == TRUE )
				{ /* Turn DB on when encoder data is invalid */
					DbOnRequest = TRUE;
				}
				else
				{
					DbOnRequest = FALSE;
				}
				DbSeqData->DbStep = 1;
			}
			else
			{
				DbOnRequest = TRUE;
				DbSeqData->DbStep = 6;
			}
			break;

		case 1: /* Waiting for motor to stop: Turn off DB after motor stops */
			if(( BeSeq->StopMode == STOPMODE_DBSTOPFR )&&( EncDataNG == TRUE ))								
			{																
				DbOnRequest = TRUE;				
				PumpOnCtrl->PumpOnReq = FALSE;									
			}								
			else if( MotStop ) /* Motor stop check only when encoder data is reliable */
			{
				DbOnRequest = FALSE;
				PumpOnCtrl->PumpOnReq = FALSE; 	/* Charge pump drive request off  */

				DbSeqData->DbWait = 0;
				DbSeqData->DbStep = 2;
			}
			break;

		case 2: /* Wait for base enable request */
			/* StopMode changes (due to processing of BK.Seq), DB stops, DB continues state change */
			if( BeSeq->StopMode == STOPMODE_DBSTOPDB )
			{
				DbOnRequest = TRUE;
				DbSeqData->DbWait = 0;
				DbSeqData->DbStep = 6;
			}
			else
			{
				/* Check base enable */
				if( DbSeqData->DbBeReq )
				{
					/* Charge pump drive request on */
					PumpOnCtrl->PumpOnReq = TRUE;
					DbSeqData->DbWait = 0;
					DbSeqData->DbStep = 3;
				}
				/* If encoder data is invalid while waiting for a base enable request, set the sequence to 1 */
				else if(( BeSeq->StopMode == STOPMODE_DBSTOPFR )&&( EncDataNG == TRUE ))
				{
					DbSeqData->DbStep = 1;
				}
			}
			break;

		case 3: /* Charge pump control */
		    /* Control charge pump while checking if base enable request continues */
			if( DbSeqData->DbBeReq == FALSE )
			{
				PumpOnCtrl->PumpOnReq = FALSE;
				DbSeqData->DbWait = 0;
				DbSeqData->DbStep = 0;
			}
			else if( PumpOnCtrl->PumpOnComp == TRUE ) /* Completion of charge pump control */
			{
				PumpOnCtrl->PumpOnReq = FALSE;

				// todo : reset alarm
				hApi_BaseEnable(AxisID);
				
				BeSeq->BeReqSeqOut = TRUE;
				DbSeqData->DbWait = 0;
				DbSeqData->DbStep = 4;
			}
			break;

		case 4: /* Wait for base block request�BStay here during base enable command */
			if( DbSeqData->DbBeReq == FALSE )
			{
				/* BaseBlock */
			    hApi_BaseBlock(AxisID); 

				BeSeq->BeReqSeqOut = FALSE;

				if( BeSeq->StopMode == STOPMODE_FREERUN )
				{
					DbOnRequest = FALSE;
					DbSeqData->DbWait = 0;
					DbSeqData->DbStep = 2;
				}
				else if( BeSeq->StopMode == STOPMODE_DBSTOPFR )
				{
					if( EncDataNG == TRUE )
					{ 
						DbSeqData->DbWait = 0;
						DbSeqData->DbStep = 5;
					}
					else if( MotStop )
					{
						DbOnRequest = FALSE;
						DbSeqData->DbWait = 0;
						DbSeqData->DbStep = 1;
					}
					else
					{
						DbSeqData->DbWait = 0;
						DbSeqData->DbStep = 5;
					}
				}
				else
				{
					DbSeqData->DbWait = 0;
					DbSeqData->DbStep = 5;
				}
			}
			break;

		case 5: /* DB on processing by base block request�BTurn on DB after 1 cycle from base block request */
			if( BeSeq->StopMode == STOPMODE_DBSTOPFR )
			{
				if( EncDataNG == TRUE )
				{ 
					DbOnRequest = TRUE;			
				}
				else if( MotStop )
				{
					DbOnRequest = FALSE;
					DbSeqData->DbWait = 0;
					DbSeqData->DbStep = 1;
				}
				else
				{
					DbOnRequest = TRUE;			
					DbSeqData->DbWait = 0;
					DbSeqData->DbStep = 1;
				}
			}
			else if( BeSeq->StopMode == STOPMODE_FREERUN )
			{
				DbOnRequest = FALSE;
				DbSeqData->DbWait = 0;
				DbSeqData->DbStep = 1;
			}
			else
			{
				DbOnRequest = TRUE;
				DbSeqData->DbWait = 0;
				DbSeqData->DbStep = 6;
			}
			break;

		case 6: 
			if(((( EncDataNG == FALSE ) && MotStop ) && DbSeqData->DbBeReq )
			   || ( BeSeq->StopMode == STOPMODE_FREERUN )	
			   || ( BeSeq->StopMode == STOPMODE_DBSTOPFR ))	
			{
				DbOnRequest = FALSE;
				DbSeqData->DbWait = 0;
				DbSeqData->DbStep = 2;
			}
			break;

		default: 
			break;
	}

	BeSeq->DbOn = DbOnRequest;
	return DbOnRequest;
	
}

/****************************************************************************************************/
/*																									*/
/*																	*/
/*																									*/
/****************************************************************************************************/
BOOL BeSeqDetectOTStatus( OT_SEQ_DATA *OverTravelSeq, BASE_CTRL_OUT *BaseCtrlOut, BOOL PotSig, BOOL NotSig )
{
	CTRLMD	*CtrlMode;
	INT32	lwk, SpdRef;

	CtrlMode = &BaseCtrlOut->CtrlModeOut;

/*--------------------------------------------------------------------------------------------------*/
/*																				*/
/*--------------------------------------------------------------------------------------------------*/
// Mask OT on return to zero     --todo
//	if( BaseCtrlOut->TblZclamp == TRUE )
//	{
//		/* OT*/
//		OverTravelSeq->var.OverTrvlSts = FALSE;
//		return FALSE;
//	}

/*--------------------------------------------------------------------------------------------------*/
/*																				*/
/*--------------------------------------------------------------------------------------------------*/
	switch( CtrlMode->b.cm )
	{
/*--------------------------------------------------------------------------------------------------*/
	case BASE_MODE_SPD:	/* 														*/
/*--------------------------------------------------------------------------------------------------*/
		/*  */
		SpdRef = BaseCtrlOut->OutputSpdRef;

		if( (SpdRef >= 0) && PotSig )
		{
			OverTravelSeq->var.OverTrvlSts = TRUE;
		}
		else if( (SpdRef <= 0) && NotSig )
		{ 
			OverTravelSeq->var.OverTrvlSts = TRUE;
		}
		else
		{ 
			OverTravelSeq->var.OverTrvlSts = FALSE;
		}
		break;
/*--------------------------------------------------------------------------------------------------*/
	case BASE_MODE_POS:	
/*--------------------------------------------------------------------------------------------------*/
		lwk = BaseCtrlOut->PositionError;
		if( lwk == 0 )
		{ 
			if( PotSig || NotSig )
			{ 
				OverTravelSeq->var.OverTrvlSts = TRUE;
			}
			else
			{ 
				OverTravelSeq->var.OverTrvlSts = FALSE;
			}
		}
		else
		{ 

			SpdRef = BaseCtrlOut->OutputSpdRef;
			if( (((lwk > 0) || (SpdRef >= 0)) && PotSig) )
			{ 
				OverTravelSeq->var.OverTrvlSts = TRUE;
			}
			else if( (((lwk < 0) || (SpdRef <= 0)) && NotSig) )
			{ 
				OverTravelSeq->var.OverTrvlSts = TRUE;
			}
			else
			{ 
				OverTravelSeq->var.OverTrvlSts = FALSE;
			}
		}
		break;
/*--------------------------------------------------------------------------------------------------*/
	case BASE_MODE_TRQ:	
/*--------------------------------------------------------------------------------------------------*/
		lwk = BaseCtrlOut->TrqCtrlRef;
		if( ((lwk >= 0) && PotSig) || ((lwk <= 0) && NotSig) )
		{ 
			OverTravelSeq->var.OtOffCnt = 0;
			if( OverTravelSeq->var.OtOnCnt >=10 )  //10ms
			{
				OverTravelSeq->var.OverTrvlSts = TRUE;
			}
			else
			{
				OverTravelSeq->var.OtOnCnt++;
//				OverTravelSeq->var.OverTrvlSts = FALSE; /* <S0B7> */
			}
		}
		else
		{
			OverTravelSeq->var.OtOnCnt = 0;
			if( OverTravelSeq->var.OtOffCnt >= 10 )  //10ms
			{
				OverTravelSeq->var.OverTrvlSts = FALSE;
			}
			else
			{
				OverTravelSeq->var.OtOffCnt++;
//				OverTravelSeq->var.OverTrvlSts = TRUE; /* <S0B7> */
			}
		}
		break;
/*--------------------------------------------------------------------------------------------------*/
	default:	
/*--------------------------------------------------------------------------------------------------*/
		OverTravelSeq->var.OverTrvlSts = FALSE;
		break;
	}
	return OverTravelSeq->var.OverTrvlSts;
}



/****************************************************************************************************/
/*																									*/
/*																									*/
/****************************************************************************************************/
// BOOL BeSeqOverTravelSequence( OT_SEQ_DATA *OverTravelSeq, LONG CtrlMode, BOOL MotStop, BOOL BeComplete )  /* <S1F5> */
BOOL BeSeqOverTravelSequence( OT_SEQ_DATA *OverTravelSeq, INT32 CtrlMode, BOOL MotStop,     /* <S1F5> */
	                                                      BOOL BeComplete, BOOL LdstpTimeZero )    /* <S1F5> */
{
	INT32	CtrlMode2Lst;
	INT32	OtStopMode;
/*--------------------------------------------------------------------------------------------------*/
/*		*/
/*--------------------------------------------------------------------------------------------------*/
	OtStopMode = OverTravelSeq->conf.OtStopMode;
	CtrlMode2Lst = OverTravelSeq->var.CtrlMode2;
	OverTravelSeq->var.CtrlMode2 = CtrlMode;

/*--------------------------------------------------------------------------------------------------*/
/*																*/
/*--------------------------------------------------------------------------------------------------*/
	switch( OverTravelSeq->var.OtStep )
	{
/*----------------------------------------------------------------------------------------------*/
	case 0: /* Initial Step																		*/
/*----------------------------------------------------------------------------------------------*/
		OverTravelSeq->var.OtBeReq = TRUE;
		OverTravelSeq->var.OtTrqLimit = FALSE;
		OverTravelSeq->var.OtZeroStop = FALSE;
		OverTravelSeq->var.OtLinDecStop = FALSE;   /* <S1F5> */
		OverTravelSeq->var.OtZeroClamp = FALSE;
		OverTravelSeq->var.OtDbopReq = OTDBOP_NONE;
		OverTravelSeq->var.OtWait = 0;
		OverTravelSeq->var.OtStep = 1;
		break;
/*----------------------------------------------------------------------------------------------*/
	case 1:
/*----------------------------------------------------------------------------------------------*/
		if( OverTravelSeq->var.OverTrvlSts == TRUE )	
		{ /* */
			/* 2011.04.04 Y.Oka <V680> */
			if( (BeComplete == TRUE) && (OverTravelSeq->conf.OtWarningSw == TRUE) )
			{ 
				OverTravelSeq->var.OtWarningDet = TRUE;
				OverTravelSeq->var.OtWarngWaitCnt = 0;
			}
		/*--------------------------------------------------------------------------------------*/
			
			if( CtrlMode == BASE_MODE_TRQ )
			{ 
				OverTravelSeq->var.OtBeReq = FALSE;		
				OverTravelSeq->var.OtDbopReq = OTDBOP_DEF;
				OverTravelSeq->var.OtStep = 2;
				break;
			}
		/*--------------------------------------------------------------------------------------*/
			switch( OtStopMode )						
			{
			case OTSTOPMODE_DEFAULT:					
				OverTravelSeq->var.OtBeReq = FALSE;
				OverTravelSeq->var.OtDbopReq = OTDBOP_DEF;
				OverTravelSeq->var.OtStep = 2;
				break;
			case OTSTOPMODE_ZSTOPZC:					
				OverTravelSeq->var.OtTrqLimit = TRUE;
				OverTravelSeq->var.OtZeroStop = TRUE;
				OverTravelSeq->var.OtDbopReq = OTDBOP_NONE;
				OverTravelSeq->var.OtStep = 3;
				break;
			case OTSTOPMODE_ZSTOPFR:					
				OverTravelSeq->var.OtTrqLimit = TRUE;
				OverTravelSeq->var.OtZeroStop = TRUE;
/* <S1F5> Start */
				OverTravelSeq->var.OtDbopReq = OTDBOP_NONE;
				OverTravelSeq->var.OtStep = 5;
				break;
			case OTSTOPMODE_LDSTOPZC:					
				if( LdstpTimeZero == TRUE )
				{	
					OverTravelSeq->var.OtTrqLimit = TRUE;
					OverTravelSeq->var.OtZeroStop = TRUE;
				}
				else
				{
					OverTravelSeq->var.OtLinDecStop = TRUE;
				}
				OverTravelSeq->var.OtDbopReq = OTDBOP_NONE;
				OverTravelSeq->var.OtStep = 3;
				break;
			case OTSTOPMODE_LDSTOPFR:					
				if( LdstpTimeZero == TRUE )
				{	
					OverTravelSeq->var.OtTrqLimit = TRUE;
					OverTravelSeq->var.OtZeroStop = TRUE;
				}
				else
				{
					OverTravelSeq->var.OtLinDecStop = TRUE;
				}
/* <S1F5> End */
				OverTravelSeq->var.OtDbopReq = OTDBOP_NONE;
				OverTravelSeq->var.OtStep = 5;
				break;
			default:
				 break;
			}
		}
		/* 2011.04.04 Y.Oka <V680> */
		else
		{ 
			if( OverTravelSeq->var.OtWarngWaitCnt > 1000)   //KPI_SCANC_MS(1000)   1000ms
			{
				OverTravelSeq->var.OtWarningDet = FALSE;
			}
			else
			{
				OverTravelSeq->var.OtWarngWaitCnt++;
			}
		}
		break;
/*----------------------------------------------------------------------------------------------*/
	case 2: 
/*----------------------------------------------------------------------------------------------*/
		if( OverTravelSeq->var.OverTrvlSts == FALSE )		
		{
			OverTravelSeq->var.OtWait++;
			if( OverTravelSeq->var.OtWait >= 20 )   //20ms  todo  KPI_SCANC_MS
			{ 
				OverTravelSeq->var.OtBeReq = TRUE;			
				OverTravelSeq->var.OtDbopReq = OTDBOP_NONE;	
				OverTravelSeq->var.OtWait = 0;
				OverTravelSeq->var.OtStep = 1;
			}
		}
//	<2010.11.17>	
//		else if((( OverTravelSeq->var.CtrlMode2 == POS_CTRL_MODE ) || ( OverTravelSeq->var.CtrlMode2 == SPD_CTRL_MODE ))
//				&& ( CtrlMode2Lst == NO_CTRL_MODE ) 
//				&& ( OtStopMode == OTSTOPMODE_ZSTOPZC ))
		else if((CtrlMode != BASE_MODE_TRQ)
				&& ( CtrlMode2Lst == BASE_MODE_TRQ ) 
				&& ( OtStopMode == OTSTOPMODE_ZSTOPZC ))
		{													
			OverTravelSeq->var.OtBeReq = TRUE;				
			OverTravelSeq->var.OtTrqLimit = TRUE;
			OverTravelSeq->var.OtZeroStop = TRUE;
			OverTravelSeq->var.OtDbopReq = OTDBOP_NONE;
			OverTravelSeq->var.OtStep = 3;
		}
		else
		{
			OverTravelSeq->var.OtWait = 0;
		}
		break;
/*----------------------------------------------------------------------------------------------*/
	case 3:
/*----------------------------------------------------------------------------------------------*/
		if( OverTravelSeq->var.OverTrvlSts == FALSE )		
		{
			OverTravelSeq->var.OtTrqLimit = FALSE;			
			OverTravelSeq->var.OtZeroStop = FALSE;
			OverTravelSeq->var.OtLinDecStop = FALSE;   /* <S1F5> */
			OverTravelSeq->var.OtStep = 1;
		}
		else if( MotStop )									
		{
			OverTravelSeq->var.OtZeroStop = FALSE;			
			OverTravelSeq->var.OtLinDecStop = FALSE;    /* <S1F5> */
			OverTravelSeq->var.OtZeroClamp = TRUE;
			OverTravelSeq->var.OtStep = 4;
		}
		break;
/*----------------------------------------------------------------------------------------------*/
	case 4: 
/*----------------------------------------------------------------------------------------------*/
		if( OverTravelSeq->var.OverTrvlSts == FALSE )		
		{
			OverTravelSeq->var.OtTrqLimit = FALSE;			
			OverTravelSeq->var.OtZeroClamp = FALSE;			
			OverTravelSeq->var.OtStep = 1;					
		}
		break;
/*----------------------------------------------------------------------------------------------*/
	case 5: 
/*----------------------------------------------------------------------------------------------*/
		if( OverTravelSeq->var.OverTrvlSts == FALSE )		
		{
			OverTravelSeq->var.OtTrqLimit = FALSE;			
			OverTravelSeq->var.OtZeroStop = FALSE;
			OverTravelSeq->var.OtLinDecStop = FALSE;     /* <S1F5> */
			OverTravelSeq->var.OtStep = 1;
		}
		else if( MotStop )									
		{
			OverTravelSeq->var.OtBeReq = FALSE;				
			OverTravelSeq->var.OtTrqLimit = FALSE;
			OverTravelSeq->var.OtZeroStop = FALSE;
			OverTravelSeq->var.OtLinDecStop = FALSE;    /* <S1F5> */
			OverTravelSeq->var.OtDbopReq = OTDBOP_FREE;
			OverTravelSeq->var.OtStep = 6;
		}
		break;
/*----------------------------------------------------------------------------------------------*/
	case 6: /* 											*/
/*----------------------------------------------------------------------------------------------*/
		if( OverTravelSeq->var.OverTrvlSts == FALSE )		/* 					*/
		{
			
			OverTravelSeq->var.OtWait++;
			if( OverTravelSeq->var.OtWait >= 20 )    //20ms
			{												/* 	*/
				OverTravelSeq->var.OtBeReq = TRUE;			/* 	*/
				OverTravelSeq->var.OtDbopReq = OTDBOP_NONE;	/* 		*/
				OverTravelSeq->var.OtWait = 0;				/* 				*/
				OverTravelSeq->var.OtStep = 1;
			}
		}
		else
		{
			OverTravelSeq->var.OtWait = 0;
		}
		break;
/*----------------------------------------------------------------------------------------------*/
	default:												
/*----------------------------------------------------------------------------------------------*/
	//	KpiSystemError( );
		break;
	}
	
	return OverTravelSeq->var.OtBeReq;
}


