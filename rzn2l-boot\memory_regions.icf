
            /* generated memory regions file - do not edit */
                            define symbol ATCM_START = 0x00000000;
                define symbol ATCM_LENGTH   = 0x20000;
                define symbol BTCM_START = 0x00100000;
                define symbol BTCM_LENGTH   = 0x20000;
                define symbol SYSTEM_RAM_START = 0x10000000;
                define symbol SYSTEM_RAM_LENGTH   = 0x180000;
                define symbol SYSTEM_RAM_MIRROR_START = 0x30000000;
                define symbol SYSTEM_RAM_MIRROR_LENGTH   = 0x180000;
                define symbol xSPI0_CS0_SPACE_MIRROR_START = 0x40000000;
                define symbol xSPI0_CS0_SPACE_MIRROR_LENGTH   = 0x4000000;
                define symbol xSPI0_CS1_SPACE_MIRROR_START = 0x44000000;
                define symbol xSPI0_CS1_SPACE_MIRROR_LENGTH   = 0x4000000;
                define symbol xSPI1_CS0_SPACE_MIRROR_START = 0x48000000;
                define symbol xSPI1_CS0_SPACE_MIRROR_LENGTH   = 0x4000000;
                define symbol CS0_SPACE_MIRROR_START = 0x50000000;
                define symbol CS0_SPACE_MIRROR_LENGTH   = 0x4000000;
                define symbol CS2_SPACE_MIRROR_START = 0x54000000;
                define symbol CS2_SPACE_MIRROR_LENGTH   = 0x4000000;
                define symbol CS3_SPACE_MIRROR_START = 0x58000000;
                define symbol CS3_SPACE_MIRROR_LENGTH   = 0x4000000;
                define symbol CS5_SPACE_MIRROR_START = 0x5C000000;
                define symbol CS5_SPACE_MIRROR_LENGTH   = 0x4000000;
                define symbol xSPI0_CS0_SPACE_START = 0x60000000;
                define symbol xSPI0_CS0_SPACE_LENGTH   = 0x4000000;
                define symbol xSPI0_CS1_SPACE_START = 0x64000000;
                define symbol xSPI0_CS1_SPACE_LENGTH   = 0x4000000;
                define symbol xSPI1_CS0_SPACE_START = 0x68000000;
                define symbol xSPI1_CS0_SPACE_LENGTH   = 0x4000000;
                define symbol CS0_SPACE_START = 0x70000000;
                define symbol CS0_SPACE_LENGTH   = 0x4000000;
                define symbol CS2_SPACE_START = 0x74000000;
                define symbol CS2_SPACE_LENGTH   = 0x4000000;
                define symbol CS3_SPACE_START = 0x78000000;
                define symbol CS3_SPACE_LENGTH   = 0x4000000;
                define symbol CS5_SPACE_START = 0x7C000000;
                define symbol CS5_SPACE_LENGTH   = 0x4000000;
