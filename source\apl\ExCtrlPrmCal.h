/****************************************************************************************************
 *
 * FILE NAME:  ExCtrlPrmCal.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.6
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	6-10-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _EX_CTRL_PRM_CAL_H_
#define _EX_CTRL_PRM_CAL_H_
		
#include "BaseLoops.h"
	

/*--------------------------------------------------------------------------------------------------*/
/*		TuningLess level definition																	*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16		Kv;				// Speed loop gain									[0.1Hz]		
	UINT16		F1rate;			// Observer gain ratio								[%]			
} TUNELESS_GAIN_TBL;

typedef struct 
{
	UINT16		Jrate;			// Load moment of inertia/mass ratio				[%]			
	UINT16		dummy;			// for alignment												
} TUNELESS_JRATE_TBL;
/*--------------------------------------------------------------------------------------------------*/



/****************************************************************************************************/
/*																									*/
/*		DEFINES																						*/
/*																									*/
/****************************************************************************************************/
#define		PRM_SETTING_OK			  0			// For parameter setting judgment (normal)
#define		PRM_SETTING_NG			  -1		// For parameter setting judgment (abnormal)					
#define		PRMERR_TUNELESS_LVL		  0x01		// Pnxxx TuningLevel Setting error								
#define		PRMERR_TUNELESS_TYPE	0x02		// Pnxxx TuningType Setting error				

/* For tuneless gain calculation */
#define		TULESCYCLE			TASKB_CYCLENS		// Tuneless calculation cycle time (taskB)[ns]
#define		TULESCYCLEUS		TASKB_CYCLEUS   	// Tuneless calculation cycle time (taskB)[us]
/*--------------------------------------------------------------------------------------------------*/
#define		TLSPDDET13BIT		(KPI_TACOUNTMS*600000/8192)			// 13bit encoder speed detection resolution
#define		TLSPDDET200UM		(KPI_TACOUNTMS*10000/256*200/1000)	// 	Speed detection resolution of scale pitch 200um
/*--------------------------------------------------------------------------------------------------*/
#define		TLESSGLVLMAX		10				// Tuneless maximum gain level
#define		TLESSJLVLMAX		5				// Tuneless inertia level maximum value				
/*--------------------------------------------------------------------------------------------------*/
#define		TUNELESSJRAT		30				// Tuneless JL target stable range [times]
#define		DISOBSFSTD			4000//6000			// Tuneless Robust compensator frequency 1: (reference) [0.1Hz]
#define		TUNELESS_STIFFSTD	80//100				// Tuneless Robust strength 100% (standard)		
/*--------------------------------------------------------------------------------------------------*/
#define		DISOBSF12			3000			// Tuneless Robust compensator frequency 1: Robustness regulation (noise suppression) [0.1Hz]
#define		DISOBSF21			8000			// Tuneless Robust compensator frequency 2: For noise cut (standard) [0.1Hz]
#define		DISOBSF22			8000			// Tuneless Robust compensator frequency 2: For noise cutting (noise suppression) [0.1Hz]
#define		DISOBSF1MIN			1500			// Tuneless Robust compensator frequency 1 minimum value [0.1Hz]	
/*--------------------------------------------------------------------------------------------------*/
#define		VOBSLPFGNHZ			(DISOBSFSTD/TUNELESSJRAT)	// Tuneless speed observer LPF calculation frequency [0.1Hz]
/*--------------------------------------------------------------------------------------------------*/
#define		DISOBSKJ			85//100				// Tuneless disturbance observer coefficient gain [%]
#define		DISOBSKD			85//99				// Tuneless disturbance observer coefficient [%]
#define		VOBSKS				60				// Tuneless speed observer gain [Hz]
#define		VOBSKJ				85//100				// Tuneless speed observer coefficient gain [%]
#define		VPHASEF2			10000			// Tuneless phase compensation filter frequency 2 [0.1Hz]
#define		VPHASEFULIM			1				// Tuneless phase compensation filter frequency lower limit value [0.1Hz]
/*--------------------------------------------------------------------------------------------------*/
#define		EHVOBSTFULIM		25//4				// Tuneless phase compensation speed OBS LpFil time constant lower limit value [10us]
/*--------------------------------------------------------------------------------------------------*/
#define		TUNE_IP_KPTI		250000			// IP balanced Kp/Ti ??=1.0, Kp[1/s] = 0.25 / Ti[s]	
#define		TUNLESS_TREFFIL		50//25				// Time constant of torque command filter after torque compensation[0.01ms]
/*--------------------------------------------------------------------------------------------------*/
#define		TUNELESS_STIFF2		50				// Tuneless robustness strength 2.50% (stability emphasis)
#define		TUNELESS_STIFF3		30				// Tuneless robustness strength 3 30% (stability emphasis)
/*--------------------------------------------------------------------------------------------------*/
#define		VIBOBS_KS			800				// Vibration detection observer Ks gain [0.1Hz]	
/*--------------------------------------------------------------------------------------------------*/
#define		DAT_GAINLEVEL_MAX	7				// Tuneless maximum gain level
#define		DAT_GAINLEVEL_MIN	0				// Tuneless minimum gain level		
#define		DAT_JRATLEVEL_MAX	2				// Tuneless inertia level maximum value					
#define		DAT_JRATLEVEL_MIN	0				// Tuneless inertia level minimum value			
/*--------------------------------------------------------------------------------------------------*/
#define		TLEX_DISOBSFRQ		2000//4000			// New tuneless disturbance observer frequency
#define		TLEX_DISOBSKD		85//99				// New tuneless disturbance observer coefficient	
#define		TLEX_VOBSLPFGAIN	160//200				// New tuneless phase compensation speed observer gain			
#define		TLEX_VPHASEF1		300				// New tuneless phase compensation filter frequency 1				
#define		TLEX_VPHASEF2		600				// New tuneless phase compensation filter frequency 2				
#define		TLEX_VOBSKS			80//120				// New tuneless phase compensation speed observer gain			
#define		TLEX_TRQLPFIL		25				// New tuneless torque filter time constant					
#define		TLEX_BNDPASSFILZN	10000			// New tuneless bandpass filter attenuation coefficient					
#define		TLEX_RESSUPFIL		2000			// New tuneless machine resonance suppression filter time constant	

/* For tuneless mode setting */
enum 
{
	TLMODE_NORMAL		= (INT32)0x00L,			// TuningLessMode NORMAL							
	TLMODE_CORELESS		= (INT32)0x01L,		    // TuningLessMode for CoreLess Linear				
	TLMODE_STABLE_L		= (INT32)0x10L,		    // TuningLessMode Stable Form for Linear			
	TLMODE_STABLE_R		= (INT32)0x20L,		    // TuningLessMode Stable Form for Rotary			
	TLMODE_SILENT		= (INT32)0x40L,		    // TuningLessMode Silent Form						
	TLMODE_SPDDETCHNG	= (INT32)0x100L,		// TuningLessMode Speed Detect Change Request		
};


/****************************************************************************************************/
/*		for Disturb Observer																		*/
/****************************************************************************************************/
/* Disturbance observer calculation cycle time (same period as velocity loop) [ns]*/
#define	DOBSCYCLE		PS_CYCLENS


/****************************************************************************************************/
/*		for Eh Velocity Observer																	*/
/****************************************************************************************************/
/* Phase compensation speed observer calculation cycle time (same period as speed loop) [us]  */
#define	EHSOBSCYCLE		PS_CYCLEUS

/****************************************************************************************************/
/*																									*/
/*		STRUCTS																						*/
/*																									*/
/****************************************************************************************************/
/****************************************************************************************************/
/*		Tuning Less Structures for API function Parameters											*/
/****************************************************************************************************/
typedef struct 
{
	UINT16		gnmode;					// Pn20A : Gain related application switch				
//	UINT16		mfctype;				// Pnxxx : Control related switch									
	UINT16		avibsw;					// Pnxxx : Vibration suppression control related switch								
	UINT16		DatLevel;				// Pnxxx : Dynamic auto tuning related switches		
	UINT16		tfuncsw;				// Pnxxx : Torque related function switch							
//	UINT16		flg_wf;					// Pnxxx : flag	
	BOOL        DobsEn;                 // Disturbance observer enable flag
	BOOL	    MotSilent;              // Motor silent enable flag

} TUNELESS_CFGPRM;

typedef struct 
{
	UINT16		DatLevel;				// Pnxxx : Dynamic auto tuning related switches
	UINT16		trqfil11;				// Pnxxx : 1st stage 1st torque command filter time constant
	UINT16		ampw;					// Pnxxx : Servo capacity						
	UINT16		ignq;					// Pnxxx : Current loop gain(Q)	
	UINT16		kiq;					// Pnxxx : Current loop integration time constant(Q)	
	UINT16		fbfil2;					// Pnxxx : Second speed F/B filter time constant
	UINT16		TuneLessGain;			// Pnxxx : Tuneless disturbance observer gain	
	UINT16		TuneLessStiff;			// Pnxxx : Tuneless inertia compensation gain	
	UINT16		TuneLessTrqLpf;			// Pnxxx : Tuneless disturbance torque low-pass filter time constant			
	UINT16		TuneLessVfbLpf;			// Pnxxx : Tuneless speed FB low-pass filter time constant				
//	MENCPRM		*MencP;					// Pnxxx - Pnxxx : Encoder parameters					
	DETVIB		*DetVib;				// Oscillation monitoring data definition				
//	ANOTCHSEQ 	*ANotchSeq;				// Data definition for automatic notch				
} TUNELESS_SETPRM;


/****************************************************************************************************/
/*		Disturb Observer Structures for API function Parameters										*/
/****************************************************************************************************/
typedef struct 
{
	UINT16		jrate;					// Pn205 : Inertia Ratio						
	UINT16		loophz;					// Pn203 : Speed loop gain									
	UINT16		loophz2;				// Pn206 : Speed loop gain 2							
	UINT16		dobgn;					// Pn309 : Disturbance observer gain									
	UINT16		dobgn2;					// Pn30A : Second Disturbance observer gain								
	UINT16		dtrqgn;					// Pn30B : Disturbance observer coefficient										
	INT16		dlpfil;					// Pn30C : Disturbance observer frequency correction							
	UINT16		dobjgn;					// Pn30D : Disturbance observer gain correction						
} DOBS_CFGPRM;


/****************************************************************************************************/
/*		Eh Velocity Observer Structures for API function Parameters									*/
/****************************************************************************************************/
typedef struct 
{
	UINT16		jrate;					// Pn205 : Inertia Ratio									
	UINT16		evobgn;					// Pn30E : Speed observer gain								
	UINT16		evobjgn;				// Pn30F : Speed observer position compensation gain						
	UINT16		trqfil11;				// Pn218 : First torque command filter time constant					
} EHVOBS_CFGPRM;



/****************************************************************************************************/
/*																									*/
/*		PROTOTYPE																					*/
/*																									*/
/****************************************************************************************************/
/****************************************************************************************************/
/*		for Tuning Less Control																		*/
/****************************************************************************************************/
PUBLIC INT32 TuneLessCalculateInitPrm( BASE_CTRL *BaseCtrlData, BPRMDAT *Bprm, 
								                        PRMDATA *PnPrm,  TUNELESS_CFGPRM *CfgPrm );
PUBLIC BOOL TuneLessSetTrqFil3( BOOL TuningLess, BOOL TuningLessEx, UINT32 MotSilent);
PUBLIC BOOL TuneLessCalculatePrm( BASE_LOOP *BaseLoops, BPRMDAT *Bprm,
														   TUNELESS_SETPRM *Prm, PRMDATA *PnPrm );


/****************************************************************************************************/
/*		for Disturb Observer																		*/
/****************************************************************************************************/
PUBLIC INT32 DobsCalculatePrmSW( BOOL *DobsAct, BOOL TuningLess, UINT16 DobsSel );
PUBLIC void DobsCalculatePrm( DOBS_PRM *DobsPrm, BPRMDAT *Bprm, 
	                             DOBS_CFGPRM *CfgPrm, INT32 GselNo );

/****************************************************************************************************/
/*		for Eh Velocity Observer																	*/
/****************************************************************************************************/
PUBLIC INT32 EhVobsCalculatePrmSW( BOOL *EhVobsUse, BOOL TuningLess, UINT16 EhVobsSel );					  
PUBLIC BOOL EhVobsCalculatePrm( EHVOBS_CTRL *EhVobsCtrl, BPRMDAT *Bprm, EHVOBS_CFGPRM *CfgPrm);

	
#endif   //  _EX_CTRL_PRM_CAL_H_






