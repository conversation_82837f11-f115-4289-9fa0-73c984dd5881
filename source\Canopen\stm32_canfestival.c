#include "hal_data.h"
 #include "bsp_api.h"
#include "canfestival.h"
#include "r_canfd.h"
#include "Alarm.h"

unsigned int TimeCNT=0;                         //Time Count
unsigned int NextTime=0;                        //Next trigger time count
unsigned int TIMER_MAX_COUNT=70000;             //Time Count Max
TIMEVAL last_time_set = TIMEVAL_MAX;     				//Last time count

extern CO_Data Object_Data;
SERVO_CAN_ERR_DEF  gServoCanErr = {0};

extern can_frame_t g_can_tx0_frame;
extern can_frame_t g_can_rx0_frame;


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
void setTimer(TIMEVAL value)
{
    NextTime=(TimeCNT+value)%TIMER_MAX_COUNT;
}

TIMEVAL getElapsedTime(void)
{
    int ret=0;
    ret = TimeCNT> last_time_set ? TimeCNT - last_time_set : TimeCNT + TIMER_MAX_COUNT - last_time_set;
//    last_time_set = TimeCNT;
    return ret;
}

//1ms run
void timerForCan(void)
{
    TimeCNT++;
    if (TimeCNT>=TIMER_MAX_COUNT)
    {
                    TimeCNT=0;
    }
    if (TimeCNT==NextTime)
    {
            TimeDispatch();	
    } 
    

    // send sdo break detect
    gServoCanErr.CanSalveState =  Object_Data.nodeState;
}

unsigned char canSend(CAN_PORT notused, Message *m)
{
    g_can_tx0_frame.id = (uint32_t)(m->cob_id);
    
    if(m->rtr)
    {
        g_can_tx0_frame.type = (uint32_t)CAN_FRAME_TYPE_REMOTE;
    }
    else
    {
        g_can_tx0_frame.type = (uint32_t)CAN_FRAME_TYPE_DATA;
    }
    
    g_can_tx0_frame.id_mode = (uint32_t)CAN_ID_MODE_STANDARD;
    

     if(m->len<=8)
     {
        g_can_tx0_frame.data_length_code = (uint32_t)(m->len);
     }
     else
     {
      	if(m->len<= 12)
        {
                g_can_tx0_frame.data_length_code = CANFD_MINIMUM_DLC_12;
        }
        else if(m->len<= 16)
        {
                g_can_tx0_frame.data_length_code = CANFD_MINIMUM_DLC_16;
        }
        else if(m->len<= 20)
        {
                g_can_tx0_frame.data_length_code = CANFD_MINIMUM_DLC_20;
        }
        else if(m->len<= 24)
        {
                g_can_tx0_frame.data_length_code = CANFD_MINIMUM_DLC_24;
        }
        else if(m->len<= 32)
        {
                g_can_tx0_frame.data_length_code = CANFD_MINIMUM_DLC_32;
        }
        else if(m->len<= 48)
        {
                g_can_tx0_frame.data_length_code = CANFD_MINIMUM_DLC_48;
        }
        else if(m->len<= 64)
        {
                g_can_tx0_frame.data_length_code = CANFD_MINIMUM_DLC_64;
        }
       
     }

    
   
    for(uint8_t i = 0; i < m->len; i++)
    {
        g_can_tx0_frame.data[i] = m->data[i];
    }    
  
    if(R_CANFD_Write(&g_canfd0_ctrl, CANFD_TX_MB_0, &g_can_tx0_frame)!=FSP_SUCCESS) return 1;
                
    
    return 0;
}


void canfd0_callback (can_callback_args_t * p_args)
{
    int i=0;
    Message m; 
    
    if(p_args->event ==  CAN_EVENT_RX_COMPLETE)   /* Receive complete event. */
    {           
      
       memcpy(&g_can_rx0_frame, p_args->p_frame, sizeof(can_frame_t));
//      if(R_CANFD_Read(&g_canfd0_ctrl, CANFD_TX_MB_0,&g_can_rx0_frame) == FSP_SUCCESS)
      {
          m.cob_id=g_can_rx0_frame.id;

          if(g_can_rx0_frame.type == CAN_FRAME_TYPE_REMOTE)
          {
              m.rtr=1;
          }
          else if(g_can_rx0_frame.type == CAN_FRAME_TYPE_DATA)
          {
              m.rtr=0;
          }
          
         if(g_can_rx0_frame.data_length_code <= 8)
         {          
            m.len = g_can_rx0_frame.data_length_code;
         }
         else
         {
           if(g_can_rx0_frame.data_length_code <= CANFD_MINIMUM_DLC_12)
            {
                    m.len = 12;
            }
            else if(g_can_rx0_frame.data_length_code <= CANFD_MINIMUM_DLC_16)
            {
                    m.len = 16;
            }
            else if(g_can_rx0_frame.data_length_code <= CANFD_MINIMUM_DLC_20)
            {
                    m.len = 20;
            }
            else if(g_can_rx0_frame.data_length_code <= CANFD_MINIMUM_DLC_24)
            {
                    m.len = 24;
            }
            else if(g_can_rx0_frame.data_length_code <= CANFD_MINIMUM_DLC_32)
            {
                    m.len = 32;
            }
            else if(g_can_rx0_frame.data_length_code <= CANFD_MINIMUM_DLC_48)
            {
                    m.len = 48;
            }
            else if(g_can_rx0_frame.data_length_code <= CANFD_MINIMUM_DLC_64)
            {
                    m.len = 64;
            }
           
         }

          for(i=0; i<m.len; i++)
          {
              m.data[i]=g_can_rx0_frame.data[i];		
          }

          //Canopen processes a frame of data
          canDispatch(&Object_Data, &m); 
          

      }
    }
    	
}
