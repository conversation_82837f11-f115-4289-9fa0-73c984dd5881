/**
  ******************************************************************************
  * @file    IAP_Main/Inc/flash_if.h 
  * <AUTHOR> Application Team
  * @brief   This file provides all the headers of the flash_if functions.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __FLASH_IF_H
#define __FLASH_IF_H

/* Includes ------------------------------------------------------------------*/
#include "stdint.h"
#include "flash.h"

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Base address of the Flash sectors */
/* Error code */
enum 
{
  FLASHIF_OK = 0,
  FLASHIF_ERASEKO,
  FLASHIF_WRITINGCTRL_ERROR,
  FLASHIF_WRITING_ERROR
};

#define ERROR_CODE_SUCCESS            (0x00u)
#define ERROR_CODE_FILE_TRANSFER      (0xFFu)
#define ERROR_CODE_PARAM_ERROR        (0xFEu)
#define ERROR_CODE_VERIFY             (0xFCu)
#define ERROR_CODE_NO_CORRESPOND      (0xFBu)
#define ERROR_CODE_TIMEOUT            (0xFAu)
#define ERROR_CODE_HW_ERROR           (0xF8u)

  
enum{
  FLASHIF_PROTECTION_NONE         = 0,
  FLASHIF_PROTECTION_PCROPENABLED = 0x1,
  FLASHIF_PROTECTION_WRPENABLED   = 0x2,
  FLASHIF_PROTECTION_RDPENABLED   = 0x4,
};

#define ERASE_BLOCK_SIZE        (QSPI_SECTOR_SIZE) // 4 Kbety

/* Define the address from where user application will be loaded.
   Note: the 1st sector 0x08000000-0x0801FFFF is reserved for the IAP code */
#define BOOTLOADER_ADDRESS1_START    (uint32_t)0x68000000
#define BOOTLOADER_ADDRESS1_END      (uint32_t)0x680FFFFF
#define APPLICATION_ADDRESS1_START   (uint32_t)0x68100000
#define APPLICATION_ADDRESS1_END     (uint32_t)0x681FFFFF
#define APPLICATION_ADDRESS2_START   (uint32_t)0x68200000
#define APPLICATION_ADDRESS2_END     (uint32_t)0x682FFFFF
#define UpgradeMark_ADDRESS_START    (uint32_t)0x68300000
#define UpgradeMark_ADDRESS_END      (uint32_t)0x68301000   

/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
void              FLASH_If_Init(void);
uint32_t          FLASH_If_Erase(uint32_t Startaddr, uint32_t Endaddr);
uint32_t          FLASH_If_Erase_Sector(void);
uint32_t          FLASH_If_Write(uint32_t FlashAddress, uint8_t* Data, uint32_t DataLength);
uint16_t          FLASH_If_GetWriteProtectionStatus(void);

#endif  /* __FLASH_IF_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
