/****************************************************************************************************
 *
 * FILE NAME:  PnPrmListTbl.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.26
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	26-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _PN_PRM_LIST_TBL_H_
#define _PN_PRM_LIST_TBL_H_

#include "RegAccessIf.h"

extern const PRM_ATTR pndef_RatCur;

extern const PRM_ATTR pndef_ClampingJawForLit;
extern const PRM_ATTR pndef_ClampingJawRevLit;
extern const PRM_ATTR pndef_MotPolePairs;
extern const PRM_ATTR pndef_MotR;
extern const PRM_ATTR pndef_MotLd;
extern const PRM_ATTR pndef_MotLq;
extern const PRM_ATTR pndef_AbsEncOffset;

extern const PRM_ATTR pndef_UWVSeq;


extern const PRM_ATTR pndef_DevType;
extern const PRM_ATTR pndef_DevVolt;
extern const PRM_ATTR pndef_DevRatCur;
extern const PRM_ATTR pndef_ExtRegenPow;

extern const PRM_ATTR pndef_CtrlSource;
extern const PRM_ATTR pndef_PosLoopHz;
extern const PRM_ATTR pndef_SpdLoopHz;
extern const PRM_ATTR pndef_SpdLoopTi;
extern const PRM_ATTR pndef_Jrat;
extern const PRM_ATTR pndef_SpdLoopHz2;
extern const PRM_ATTR pndef_SpdLoopTi2;
extern const PRM_ATTR pndef_ModeSw;

extern const PRM_ATTR pndef_SpdffCfg;

extern const PRM_ATTR pndef_SpdffFil;
extern const PRM_ATTR pndef_SpdffGain;
extern const PRM_ATTR pndef_TrqFilTime;
extern const PRM_ATTR pndef_NotFilCfg;
extern const PRM_ATTR pndef_NotFilFc1;
extern const PRM_ATTR pndef_NotFilQ1;
extern const PRM_ATTR pndef_NotFilDep1;
extern const PRM_ATTR pndef_NotFilFc2;
extern const PRM_ATTR pndef_NotFilQ2;
extern const PRM_ATTR pndef_NotFilDep2;

extern const PRM_ATTR pndef_PjogSw;
extern const PRM_ATTR pndef_PjogMovDist;
extern const PRM_ATTR pndef_PjogMovSpd;
extern const PRM_ATTR pndef_PjogAccTm;
extern const PRM_ATTR pndef_PjogWaitTm;
extern const PRM_ATTR pndef_PjogNum;
extern const PRM_ATTR pndef_GnChgSw;

extern const PRM_ATTR pndef_AvibOpt;
extern const PRM_ATTR pndef_AvibFrq;
extern const PRM_ATTR pndef_AvibGn;
extern const PRM_ATTR pndef_AvibDamp;
extern const PRM_ATTR pndef_AvibFil1;
extern const PRM_ATTR pndef_AvibFil2;
extern const PRM_ATTR pndef_AvibDamp2;
extern const PRM_ATTR pndef_AvibFrq2;
extern const PRM_ATTR pndef_DobsGn;
extern const PRM_ATTR pndef_DobsGn2;
extern const PRM_ATTR pndef_DobsCoff;
extern const PRM_ATTR pndef_DobsFreqComp;
extern const PRM_ATTR pndef_DobsGnComp;
extern const PRM_ATTR pndef_AdvAppSW;
extern const PRM_ATTR pndef_TlsCfg;

extern const PRM_ATTR pndef_MfcSw;
extern const PRM_ATTR pndef_MfcGn;
extern const PRM_ATTR pndef_MfcGnComp;
extern const PRM_ATTR pndef_MfcBiasFw;
extern const PRM_ATTR pndef_MfcBiasRv; 
extern const PRM_ATTR pndef_VibSupFrq1A;
extern const PRM_ATTR pndef_VibSupFrq1B;
extern const PRM_ATTR pndef_MfcVffComp;
extern const PRM_ATTR pndef_MfcGn2;
extern const PRM_ATTR pndef_MfcGnComp2;
extern const PRM_ATTR pndef_VibSupFrq2;              	 // Pn325
extern const PRM_ATTR pndef_VibSupFrq2Comp;              // Pn326

extern const PRM_ATTR pndef_VibDetSens;
extern const PRM_ATTR pndef_VibDetSpd;
extern const PRM_ATTR pndef_JstVibDetSpd;
extern const PRM_ATTR pndef_VibDetSw;
extern const PRM_ATTR pndef_RemVibDetW;

extern const PRM_ATTR pndef_MaxPrfVel;
extern const PRM_ATTR pndef_MaxDec;
extern const PRM_ATTR pndef_MaxAcc;
extern const PRM_ATTR pndef_OFAlmLvl;
extern const PRM_ATTR pndef_SvonOFAlmLvl;


#endif

