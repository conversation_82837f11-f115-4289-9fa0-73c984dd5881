/****************************************************************************************************
 *
 * FILE NAME:  DataTraceCal.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.30
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	30-10-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _DATA_TRACE_CAL_H_
#define	_DATA_TRACE_CAL_H_

#include "BaseDef.h"
//#include "PrmAccessIf.h"

INT32   GetVarData_Voltage( void *Axis );
INT32   GetVarData_IPMCurrent( void *Axis );
INT32	GetVarData_PosRef( void *Axis );				
INT32	GetVarData_PosFdb( void *Axis );					
INT32	GetVarData_PosErr( void *Axis );				
INT32	GetVarData_PosOutput( void *Axis );				
INT32	GetVarData_SpdRef( void *Axis );	
INT32   GetVarData_SpdFdb( void *Axis );
INT32   GetVarData_SpdErr( void *Axis );
INT32   GetVarData_SpdOutput( void *Axis );
INT32   GetVarData_TrqRef( void *Axis );
INT32   GetVarData_TrqFdb( void *Axis );
INT32   GetVarData_CurrentIa( void *Axis );
INT32   GetVarData_CurrentIb( void *Axis );
INT32   GetVarData_CurrentIc( void *Axis );
INT32   GetVarData_CurrentIdRef( void *Axis );
INT32   GetVarData_CurrentIqRef( void *Axis );
INT32   GetVarData_CurrentIdFbd( void *Axis );
INT32   GetVarData_CurrentIqFbd( void *Axis );
INT32   GetVarData_EncMechAngle( void *Axis );
INT32   GetVarData_EncElecAngle( void *Axis );
INT32   GetVarData_SpeedFFC( void *Axis );
INT32   GetVarData_TrqFFC( void *Axis );
INT32   GetVarData_SpeedEnc( void *Axis );
INT32   GetVarData_SpeedObs( void *Axis );
INT32 GetVarData_PosFilter( void *Axis );
INT32 GetVarData_SpdFeedForward( void *Axis );
INT32 GetVarData_IqNotchFilter( void *Axis );
INT32 GetVarData_EncSingleTurn( void *Axis );
INT32 GetVarData_EncMultiTurn( void *Axis );
INT32 GetVarData_AlmStatus( void *Axis );
INT32 GetVarData_WrnStatus( void *Axis );
INT32 GetVarData_BrakeStatus( void *Axis );
INT32 GetVarData_Cia402StatusW( void *Axis );
INT32 GetVarData_Cia402ControlW( void *Axis );
INT32 GetVarData_TargetPosition0x607A( void *Axis );

INT32 GetVarData_PositionInc( void *Axis );
INT32 GetVarData_DebugVar1( void *Axis );
INT32 GetVarData_DebugVar2( void *Axis );
INT32 GetVarData_DebugVar3( void *Axis );
INT32 GetVarData_DebugVar4( void *Axis );

INT32   GetVarData_AdrShortData( void *Axis );
INT32   GetVarData_AdrLongData( void *Axis );
INT32   GetVarData_Dummy( void *Axis );

BOOL	GetBitData_SvonReq( void *Axis );					
BOOL	GetBitData_Pcon( void *Axis );						
BOOL	GetBitData_Pot( void *Axis );					
BOOL	GetBitData_Not( void *Axis );					
BOOL	GetBitData_AlmRst( void *Axis );	

INT32   GetVarGain_Current( void *Axis );


#endif   // _DATA_TRACE_CAL_H_

