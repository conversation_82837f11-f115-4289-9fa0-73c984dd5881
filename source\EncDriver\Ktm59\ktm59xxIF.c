/**************************************************
** Copyright (c) 2016-202X 昆泰芯微电子科技有限公司
** 文件名: ktm59xx.c
** 作者: liujunbo
** 日期: 2023.12.5
** 描述: KTM59xx芯片相关文件，存放用来操作KTM59xx芯片的函数
**
**************************************************/
#include "hal_data.h"
#include "bsp_spi.h"
#include "bsp_timer.h"
#include "ktm59xxIF.h"
#include "common_ktm.h"
#include "bsp.h"
#include "Encoder.h"

#define KTM_ENCPPR  (0x18)  // 编码器位数
#define KTM59_ENABLE_ABZ  0
#define KTM59_ENABLE_ONLINECALIB  0
/**
 * @brief 角度相关数据全局变量
 * @details 存储从KTM59xx芯片读取的角度值、圈数和状态信息
 */
Angle_T KTM59_Angle =
{
  .Angle = 0,            /* 角度值，24位 */
  .RevolutionCount = 0,  /* 圈数计数 */
  .AngleStatus = 0,      /* 状态信息，2位 */
};

/**
 * @brief SPI数据发送函数
 * @param[in] ptxdata 发送数据缓冲区指针
 * @param[in] txsize 发送数据大小
 * @param[in] rxsize 接收数据大小
 * @return None
 * @note 调用底层SPI驱动发送数据，使用通道(0 通道）
 */
void KTM59_SPI_Transmit(uint32_t *ptxdata, uint16_t txsize, uint16_t rxsize)
{
  /* 调用底层SPI驱动发送数据 */
  bsp_spi_transfer(ptxdata, rxsize, txsize, 0);
}

/**
 * @brief SPI数据接收函数
 * @param[out] prxdata 接收数据缓冲区指针
 * @param[in] size 接收数据大小
 * @return uint8_t 接收状态（0=成功，1=失败）
 * @note 调用底层SPI驱动接收数据，使用通道(0 通道）
 */
uint32_t Spi_rxbuffer[2] = {0};
#pragma optimize=none
uint8_t KTM59_SPI_Receive(uint32_t *prxdata, uint16_t size)
{
    /* 调用底层SPI驱动接收数据并返回状态 */
    uint8_t result = 0;
    
    result = bsp_spi_receive(&Spi_rxbuffer[0], size, 0);
    
    *prxdata =  Spi_rxbuffer[1];
    *(prxdata+1) = Spi_rxbuffer[0];
    
    
//   return result;
    return result;
}


/**
 * @brief SPI数据发送和接收函数
 * @param[in] ptxdata 发送数据缓冲区指针
 * @param[out] prxdata 接收数据缓冲区指针
 * @param[in] txsize 发送数据大小
 * @param[in] rxsize 接收数据大小
 * @return 0:ok 1;:fail
 * @note 该函数首先发送数据，然后等待接收缓冲区有数据后再读取
 */
uint8_t KTM59_SPI_TransmitReceive(uint32_t* ptxdata, uint32_t *prxdata, uint16_t txsize, uint16_t rxsize)
{
  uint8_t ret = 0;
  /* 通过SPI发送数据 */
  bsp_spi_transfer(ptxdata, rxsize, txsize, 0);

  uint32_t starttimer = bsp_gettimercnt();

  /* 等待接收缓冲区有数据（接收缓冲区满标志为1） */
  while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*100)
  {
    // do nothing
  }

  /* 从SPI接收缓冲区读取数据 */
  ret = bsp_spi_receive(prxdata, 1, 0);
  return  ret;
}

/**
 * @brief 写KTM59xx寄存器
 * @param[in] addr 寄存器地址
 * @param[in] data 写入的数据
 * @return uint8_t 写入状态（1=成功，0=失败）
 * @note 写入后会校验返回值是否与写入的数据一致
 */
uint8_t KTM59_WriteReg(uint16_t addr, uint16_t data)
{
  /* 构造发送数据包：命令码 + 地址 + 数据 */
  uint32_t sendData = KTM59_WRITE_REG | ((uint32_t)addr << 8) | data;
  uint64_t sendData_64 = (uint64_t)((uint64_t)sendData << 32) ;
  uint32_t spitxbuf[2] = {0};
  uint32_t spirxbuf[2] = {0};

  /* 将数据转换为32位数组格式 */
  DataToDwordArr(&spitxbuf[0], sendData_64, 0, 2);

    /* 发送读取角度命令 */
  KTM59_SPI_Transmit(&spitxbuf[0], 2, 2);
  
  uint32_t  starttimer = bsp_gettimercnt();
  while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*100)
  {
    // do nothing
  }  
  
  KTM59_SPI_Receive(spirxbuf, 2);
  
  return 0;
}

/**
 * @brief 读KTM59xx寄存器
 * @param[in] addr 寄存器地址
 * @return uint8_t 读取的寄存器值（8位）
 * @note 返回值为接收数据的低8位
 */
uint8_t KTM59_ReadReg(uint16_t addr)
{
  /* 构造发送数据包：命令码 + 地址 */
  uint32_t sendData = KTM59_READ_REG | ((uint32_t)addr << 8);
  
  uint64_t sendData_64 = (uint64_t)((uint64_t)sendData << 32) ;

  uint32_t spitxbuf[2] = {0};
  uint32_t spirxbuf[2] = {0};

  /* 将数据转换为32位数组格式 */
   DataToDwordArr(&spitxbuf[0], sendData_64, 0, 2);

  /* 发送并接收数据 */
//  KTM59_SPI_TransmitReceive(&spitxbuf, &spirxbuf, 1, 1);
   
  /* 发送读取角度命令 */
  KTM59_SPI_Transmit(&spitxbuf[0], 2, 2);
  
  uint32_t  starttimer = bsp_gettimercnt();
  while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*2)
  {
    // do nothing
  }  
  
  KTM59_SPI_Transmit(&spitxbuf[0], 2, 2);
  
  starttimer = bsp_gettimercnt();
  while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*100)
  {
    // do nothing
  }
  
  KTM59_SPI_Receive(spirxbuf, 2);

  /* 返回接收数据的低8位 */
  return ((spirxbuf[1] >> 24) & 0xFF);
}

/**
 * @brief 发送读取角度命令
 * @return None
 * @note 只发送读取角度命令，需要后续调用KTM59_ReadAngle来获取数据
 */
void KTM59_ReadAngleCmd(void)
{
  uint32_t spitxbuf[2];

  /* 将读取角度命令转换为32位数组格式 */
  DataToDwordArr(&spitxbuf[0], KTM59_READ_ANGLE, 0, 2);

  /* 发送读取角度命令 */
  KTM59_SPI_Transmit(&spitxbuf[0], 2, 2);
}

/**
 * @brief 接收角度数据
 * @param[out] spirxbuf 接收数据缓冲区指针，用于存储64位角度数据
 * @return uint8_t 接收状态（0=成功，1=失败）
 * @note 需要先调用KTM59_ReadAngleCmd发送读取命令，然后调用此函数接收数据
 *       接收的数据包含角度值、圈数和状态信息
 */
uint8_t KTM59_ReadAngle(uint32_t *spirxbuf)
{
  /* 接收角度数据（2个32位数据，共计64位） */
  return KTM59_SPI_Receive(spirxbuf, 2);
}

/**
 * @brief 写入齿轮箱参数
 * @param[in] cycCount 循环计数值，用于多圈计数
 * @param[in] revCount 圈数计数值
 * @return None
 * @note 该函数用于配置芯片的齿轮箱参数，影响多圈计数功能
 */
void KTM59_WriteGearBox(uint16_t cycCount, uint32_t revCount)
{
  /* 构造发送数据包：命令码 + 循环计数 + 圈数计数 */
  uint64_t sendData = KTM59_WRITE_GEARBOX | ((uint64_t)cycCount << 40) | ((uint64_t)revCount << 8);
  uint32_t spitxbuf[2];
  uint32_t spirxbuf[2];

  /* 将数据转换为32位数组格式（2个32位数据，共计64位） */
  DataToDwordArr(&spitxbuf[0], sendData, 0, 2);

  /* 发送并接收数据 */
  KTM59_SPI_TransmitReceive(spitxbuf, spirxbuf, 2, 2);
}


/**
 * @brief 读取角度值并进行CRC校验
 * @return 1:校验成功 0:校验失败
 * @note 读取的数据将更新到KTM59_Angle全局结构体中
 *       数据格式: RC(0bit) + Angle(24bit) + status(2bit)
 */
/**
 * @brief 位置数据全局变量
 * @details 存储从KTM59xx芯片读取的原始位置数据
 */
uint64_t g_PosData = 0;
/**
 * @brief 读取角度值并进行CRC校验和数据解析
 * @param[in] input 输入数据缓冲区指针，包含从KTM59xx读取的原始数据
 * @param[out] singleTrun 单圈角度值指针（未使用）
 * @param[out] muiltTrun 多圈角度值指针（未使用）
 * @param[out] status 状态信息指针（未使用）
 * @return 1:校验成功 0:校验失败
 * @note 读取的数据将更新到KTM59_Angle全局结构体中
 *       数据格式: RC(0bit) + Angle(24bit) + status(2bit) + CRC(8bit)
 */
uint8_t KTM59_ReadAngleDecoding(uint32_t *input, int32_t *singleTrun, int32_t *muiltTrun, uint16_t *status)
{
  uint64_t data = 0;
  uint8_t rcbw = 0;          /* 圈数位宽 */
  uint8_t anglebw = KTM_ENCPPR;      /* 角度位宽 */
  uint8_t statusbw = 2;      /* 状态位宽 */
  uint8_t alldatalen = 0;    /* 总数据长度（不含CRC） */
  uint8_t crcflag = 0;       /* CRC校验标志 */

  /* 计算总数据长度（不含CRC） */
  alldatalen += anglebw + rcbw + statusbw;

  /* 将输入数据转换为64位数据 */
  g_PosData = *(uint64_t*)input;

  data = g_PosData;
  /* 进行CRC8校验 */
  crcflag = IsCheckCRC8OK(data, alldatalen);

  if (crcflag == 1)
  {
    /* CRC校验成功，解析数据 */
    /* 提取角度值（24位） */
    KTM59_Angle.Angle = (uint32_t)((data << rcbw) >> (KTM59_ANGLE_DATA_MAX_LENGTH - anglebw));
    /* 提取圈数值 */
    KTM59_Angle.RevolutionCount = (uint32_t)(data >> (KTM59_ANGLE_DATA_MAX_LENGTH - rcbw));
    /* 提取状态信息（2位） */
    KTM59_Angle.AngleStatus = (uint8_t)((data << (rcbw + anglebw)) >> (KTM59_ANGLE_DATA_MAX_LENGTH - statusbw));
    
    *singleTrun = KTM59_Angle.Angle;
    *muiltTrun = 0;
    *status = KTM59_Angle.AngleStatus;    
  }
  else
  {
    /* CRC校验失败 */
    crcflag = 0;
  }
  

  
  return crcflag;
}

/**
 * @brief 出厂初始化编码器-只在初始化伺服的时候写一次
 * @return None
 */
static uint8_t KTM59_Save_MTP(void)
{
  uint8_t ret = 0;
  uint32_t  WriteCnt = 0;
  KTM59_WriteReg(0x148, 0x0A);  

  while(KTM59_ReadReg(0x148) != 0x0A)
  {
    KTM59_WriteReg(0x148, 0x0A);  
    if(WriteCnt > 10)
    {
      ret = 1;
      return ret;
    }
    WriteCnt++;
  }
  
 uint32_t  starttimer = bsp_gettimercnt();
  while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*2)
  {
    // do nothing
  } 
  
  
  KTM59_WriteReg(0x140, 0xc5);

  starttimer = bsp_gettimercnt();
  do
  {
    if(bsp_gettimercnt() - starttimer > CMTW1_PPR_MS*50)
    {
      ret = 2;
      return ret;
    }

  }while(KTM59_ReadReg(0x140) != 0x01);  

  KTM59_WriteReg(0x141, 0x02);

  starttimer = bsp_gettimercnt();
  do
  {
    if(bsp_gettimercnt() - starttimer > CMTW1_PPR_MS*2000)
    {
      ret = 3;
      return ret;
    }
  }while(KTM59_ReadReg(0x141) != 0x00);  

  return ret;

}



/**
 * @brief 出厂初始化编码器-只在初始化伺服的时候写一次
 * @return None
 */
uint8_t FactoryInit_Reg[6] = {0};
uint8_t  hApi_KTM59_Factory_Init(void)
{
  uint8_t ret = 0;
  // 设置编码器位数为24
  KTM59_WriteReg(0xE1, KTM_ENCPPR);

  #if KTM59_ENABLE_ONLINECALIB
  // 开启自校准
  KTM59_WriteReg(0x30,0x05);
  KTM59_WriteReg(0x31,0x00);
  KTM59_WriteReg(0x32,0x00);
  KTM59_WriteReg(0xA0,0x02);
  #else
  // 关闭自校准
  KTM59_WriteReg(0x30,0x00);
  KTM59_WriteReg(0x31,0x00);
  KTM59_WriteReg(0x32,0x00);
  KTM59_WriteReg(0xA0,0x00);
  #endif

  // 清除P6 所有功能
  KTM59_WriteReg(0x11,0x40);
  
  // 清除所存故障
  KTM59_WriteReg(0x129,0x00);
  KTM59_WriteReg(0x12A,0x00);
   
  // 设置 avdd 为5V， 并保存mtp
  KTM59_WriteReg(0x23, 0x00);
  
  // 设置方向为4CCW----默认值为CW 0
  KTM59_WriteReg(0x28, 0x04); 

  ret = KTM59_Save_MTP();

  FactoryInit_Reg[0] = KTM59_ReadReg(0xA0);
  FactoryInit_Reg[1] = KTM59_ReadReg(0x30);
  FactoryInit_Reg[2] = KTM59_ReadReg(0x23);
  FactoryInit_Reg[3] = KTM59_ReadReg(0x31);  
  FactoryInit_Reg[4] = KTM59_ReadReg(0x32); 

  FactoryInit_Reg[5] = KTM59_ReadReg(0x28); 
  return ret;
}


/**
 * @brief 出厂自动256点校准
 * @return 0:成功 1:失败
 */
uint8_t AutoCalib_Reg[10] = {0};
uint8_t hApi_KTM59_Factory_AutoCalib(void)
{
    uint8_t ret = 0;
    uint8_t  RegVal = 0;
    RegVal = KTM59_ReadReg(0x16);
    RegVal = RegVal | (1<< 0) | (1 << 2) | (1 << 7);
    KTM59_WriteReg(0x16,RegVal);

    RegVal = KTM59_ReadReg(0x11);
    RegVal = (RegVal & 0xF8) | 0x04;
    KTM59_WriteReg(0x11,RegVal);

    RegVal = KTM59_ReadReg(0xa0);
    RegVal = (RegVal | (1 <<1));
    KTM59_WriteReg(0xa0,RegVal); 

    R_BSP_PinWrite(BSP_IO_PORT_21_PIN_3,BSP_IO_LEVEL_HIGH);

    uint32_t  starttimer = bsp_gettimercnt();
    while(bsp_gettimercnt() - starttimer < CMTW1_PPR_S*5)
    {
      // do nothing
    }       
    R_BSP_PinWrite(BSP_IO_PORT_21_PIN_3,BSP_IO_LEVEL_LOW);


    return ret;
}


/**
 * @brief 出厂手动线性校准
 * @return 0:成功 1:失败
 */
uint8_t ManualCalib_Reg[5] = {0};
uint8_t hApi_KTM59_Factory_ManualCalib(void)
{
    uint8_t ret = 0;
    /* code */
    KTM59_WriteReg(0x30,0x05);
    uint32_t  starttimer = bsp_gettimercnt();

    ManualCalib_Reg[0] = KTM59_ReadReg(0x30);

    while(bsp_gettimercnt() - starttimer < CMTW1_PPR_S*100)
    {
      // do nothing
    }    
    
    KTM59_WriteReg(0x30,0x00);
    ManualCalib_Reg[1] = KTM59_ReadReg(0x30);
    starttimer = bsp_gettimercnt();
    while(bsp_gettimercnt() - starttimer < CMTW1_PPR_S*1)
    {
      // do nothing
    }   

    KTM59_WriteReg(0x38,0xFF);
    ManualCalib_Reg[2] = KTM59_ReadReg(0x38);
    starttimer = bsp_gettimercnt();
    while(bsp_gettimercnt() - starttimer < CMTW1_PPR_S*1)
    {
      // do nothing
    }   

    KTM59_WriteReg(0x3A,0x00);
    ManualCalib_Reg[3] = KTM59_ReadReg(0x3A);
    starttimer = bsp_gettimercnt();
    while(bsp_gettimercnt() - starttimer < CMTW1_PPR_S*1)
    {
      // do nothing
    }       

    KTM59_WriteReg(0x31,0x02);
    ManualCalib_Reg[4] = KTM59_ReadReg(0x31);
    
    
        starttimer = bsp_gettimercnt();
     do
    {
      if(bsp_gettimercnt() - starttimer > CMTW1_PPR_MS*1000)
      {
        ret = 1;
        return ret;
      }
    }while(KTM59_ReadReg(0x31) != 0x00);  
    
    return ret;
}

/**
 * @brief 初始化编码器-只在初始化伺服的时候写一次
 * @return 0:成功 1:失败
 */
uint8_t Ktm59_Reg[10] = {0};
uint8_t KTM59_Init(void)
{
  uint8_t ret = 0;
  // 读取故障锁存
  Ktm59_Reg[1] = KTM59_ReadReg(0x129);
  Ktm59_Reg[2] = KTM59_ReadReg(0x12a);
  if(Ktm59_Reg[1] != 0)
  {
          // 清除所存故障
      KTM59_WriteReg(0x129,0x00);
  }
  if(Ktm59_Reg[2] != 0)
  {
          // 清除所存故障
      KTM59_WriteReg(0x12A,0x00);
  }  
  

  // 读取编码器位数
  Ktm59_Reg[0] = KTM59_ReadReg(0xE1);

  if(Ktm59_Reg[0] != KTM_ENCPPR)
  {
    // 设置编码器位数为24
    KTM59_WriteReg(0xE1, KTM_ENCPPR);    
  }

  // 读取初始化状态
  Ktm59_Reg[3] = KTM59_ReadReg(0x12); 

  #if KTM59_ENABLE_ABZ  //  apply as  abz encoder
  KTM59_WriteReg(0x10,0x06); 
  Ktm59_Reg[4]  = KTM59_ReadReg(0x10);  
  
  KTM59_WriteReg(0xf0,0x00); 
  Ktm59_Reg[5]  = KTM59_ReadReg(0xf2);    
  Ktm59_Reg[6]  = KTM59_ReadReg(0xf3); 
  Ktm59_Reg[7]  = KTM59_ReadReg(0xf4);  
  
  KTM59_WriteReg(0xf0,0x01); 
  Ktm59_Reg[8]  = KTM59_ReadReg(0xf0);  
  #endif

  if((Ktm59_Reg[3] & 0x0F) != 0x0F)
  {
    ret = 1;  // 初始化失败
  }
  
  
  // 读取初始化状态
  Ktm59_Reg[4] = KTM59_ReadReg(0x28); 
  return ret;
}

/**
 * @brief 初始化编码器读写-只在初始化伺服的时候写一次
 * @return 0:成功 1:失败
 */ 

uint8_t hApi_IniKtm59() 
{
  uint8_t ret = 0;
  // spi device init
  bsp_spi3_init(SPI3_KTM59XX_MODE);
  
  // init encoder register
  if(0 ==  KTM59_Init())
  {
    // reduce spi communication error 
    KTM59_WriteReg(0xE4,0x01);

    // read one time position
    KTM59_ReadAngleCmd();
    
    uint32_t starttimer = bsp_gettimercnt();
    
    while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*20)
    {
      // do nothing
    }       
    KTM59_ReadAngleCmd();
  }
  else
  {
    // todo alarm 
    ret= 1;
  }  

  return ret;
}


/**
 * @brief 初始化编码器读写-只在初始化伺服的时候写一次
 * @return 0:成功 1:失败
 */ 

uint8_t hApi_ReIniKtm59() 
{
  uint8_t ret = 0;

  bsp_spi3_close();
  // spi device init
  ret = hApi_IniKtm59();

  return ret;
}
/**
 * @brief 读取编码器数据
 * @return 0:成功 1:失败  
 * 失败，接收到的数据长度小于预期，认为断线 
 */ 
uint8_t hApi_KTM59_GetPos(uint32_t *pdata)
{
  uint8_t ret = 0;
  // 读取编码器数据 
  ret = KTM59_ReadAngle(pdata);
    
  KTM59_ReadAngleCmd();

  return ret;
}

/**
 * @brief 编码器数据解码
 * @return 0:成功 1:失败
 */ 
uint8_t hApi_KTM59_PosDecoding(ENCODER *pEnc,ENCODER *pEnc2)
{
  uint32_t *input     = (uint32_t *)&pEnc->V.AbsRecvBuf[0];
  int32_t *singleTrun = &pEnc->V.SingleTurn;
  int32_t *muiltTrun  = &pEnc->V.MultiTurn;
  uint16_t *status    = &pEnc->V.AbsAlmCode;

  return KTM59_ReadAngleDecoding(input,singleTrun,muiltTrun,status);
}

