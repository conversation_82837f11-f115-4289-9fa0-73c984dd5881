/****************************************************************************************************
 *
 * FILE NAME:  CheckMotSts.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.14
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	14-08-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseDef.h"
#include "CheckMotSts.h"
#include "Mlib.h"

PRIVATE BOOL LpxCheckMotorRun( SEQ_MOT_STS *MotSts, INT32 LstTgonSts );
PRIVATE BOOL LpxCheckMotorStop( INT32 AbsMotSpd );
PRIVATE BOOL LpxCheckMotorVcmp( SEQ_MOT_STS *MotSts, INT32 SpdRef, BOOL SpdCtrlMode );
PRIVATE BOOL  LpxCheckMotorSuspendLevel( SEQ_MOT_STS *MotSts,INT32 NorSuspSpd);


/****************************************************************************************************
* DESCRIPTION:
*		Motor state detection processing
* RETURNS:
*
****************************************************************************************************/
PUBLIC void SMotSts_InputMotorStatus( SEQ_MOT_STS *MotSts, BASE_CTRL_OUT *BaseCtrlOut,
                               							 SEQ_CTRL_OUT *SeqCtrlOut, BPRMDAT *Bprm )
{
	INT32				lwk0;

/*--------------------------------------------------------------------------------------------------*/
/*		Input Motor Pulse Data																		*/
/*--------------------------------------------------------------------------------------------------*/
	lwk0 = BaseCtrlOut->MotPos - MotSts->var.MotPos;			/* Cal. Delta Position				*/
	MotSts->var.MotPos  = BaseCtrlOut->MotPos;					/* Update Motor Position			*/
	MotSts->var.dMotPos = lwk0;									/* Update Delta Motor Position		*/

/*--------------------------------------------------------------------------------------------------*/
/*		Input Pulse Command Data																	*/
/*--------------------------------------------------------------------------------------------------*/
	lwk0 = BaseCtrlOut->PcmdCntr - MotSts->var.PcmdCntr;		/* Cal. Delta Pulse					*/
	MotSts->var.PcmdCntr = BaseCtrlOut->PcmdCntr;				/* Update Pulse Command Counter		*/
	MotSts->var.dPcmdCnt = lwk0;								/* Update Delta Pulse Command		*/

/*--------------------------------------------------------------------------------------------------*/
/*		Calculate Motor Speed [2^24/OvrSpd]															*/
/*--------------------------------------------------------------------------------------------------*/
	MotSts->var.LstMotSpd = MotSts->var.MotSpd;
	if( BaseCtrlOut->MotSpdCnt > 0 )
	{
		MotSts->var.MotSpd = BaseCtrlOut->MotSpdSum / BaseCtrlOut->MotSpdCnt;
	}

	/* Absolute Motor Speed */
	MotSts->var.AbsMotSpd = MlibABS( MotSts->var.MotSpd );

	/* Make Acceleration for Mode Switch */
	SeqCtrlOut->ModeSwAcc = MotSts->var.MotSpd - MotSts->var.LstMotSpd;

/*--------------------------------------------------------------------------------------------------*/
/*		Detect Motor Status																			*/
/*--------------------------------------------------------------------------------------------------*/
	/* Motor rotation detection processing(TGON) */
	SeqCtrlOut->TgOnSts = LpxCheckMotorRun( MotSts, SeqCtrlOut->TgOnSts );

	/* Motor stop detection processing(MOTSTOP) */
	SeqCtrlOut->MotStop = LpxCheckMotorStop( MotSts->var.AbsMotSpd );

	/* Speed match detection processing(VCMP) */
	SeqCtrlOut->VcmpSts = LpxCheckMotorVcmp( MotSts,
											    BaseCtrlOut->SpdCtrlRef,
										        (( BASE_MODE_SPD == BaseCtrlOut->CtrlModeOut.b.cm ) ||
										        ( BASE_MODE_JOG == BaseCtrlOut->CtrlModeOut.b.cm ) ));
      /*Motor Suspend Level Flag */
	SeqCtrlOut->MotSuspLvl = LpxCheckMotorSuspendLevel(MotSts,Bprm->NorSuspSpd);
/*--------------------------------------------------------------------------------------------------*/
/*	Motor speed, command pulse speed																*/
/*--------------------------------------------------------------------------------------------------*/
	MotSts->var.Pfbk64Sum += MotSts->var.dMotPos;
	MotSts->var.Pcmd64Sum += MotSts->var.dPcmdCnt;
	MotSts->var.MotSpd64Sum += MotSts->var.MotSpd;

	MotSts->var.TrqRefMon = BaseCtrlOut->TrqRefMon;


}


 /****************************************************************************************************
 * DESCRIPTION:
 *		 Motor rotation detection processing
 * RETURNS:
 *
 ****************************************************************************************************/
PRIVATE BOOL LpxCheckMotorRun( SEQ_MOT_STS *MotSts, INT32 LstTgonSts )
{
	BOOL	TgOn;
	TgOn = LstTgonSts;

	if( MotSts->var.AbsMotSpd >= MotSts->conf.TgonSpdLevel )
	{ /* Motor speed >= rotation detection level*/
		TgOn = TRUE;
	}
	else
	{ 
		if( MotSts->conf.TgonSpdLevel > MOTSPD_TGMGN )
		{ /* Rotation detection level > 5[min-1] */
			if( MotSts->var.AbsMotSpd < (MotSts->conf.TgonSpdLevel - MOTSPD_TGMGN) )
			{ /* Motor speed < (Rotation detection level - 5[min-1]) */
				TgOn = FALSE;
			}
		}
		else
		{ /* Rotation detection level <= 5[min-1] */
			TgOn = TRUE;
		}
	}
	return TgOn;
}


/****************************************************************************************************
* DESCRIPTION:
*		Motor stop detection processing
* RETURNS:
*
****************************************************************************************************/
PRIVATE BOOL LpxCheckMotorStop( INT32 AbsMotSpd )
{
	BOOL	MotStop;

	if( AbsMotSpd < MOTSPD_DBLVL )
	{
		MotStop = TRUE;								/* Motor stop detection signal setting(ON)		*/
	}
	else
	{
		MotStop = FALSE;							/* Motor stop detection signal setting(OFF)		*/
	}

	return	MotStop;
}



/****************************************************************************************************
* DESCRIPTION:
*		Speed match detection processing
* RETURNS:
*
****************************************************************************************************/
PRIVATE BOOL LpxCheckMotorVcmp( SEQ_MOT_STS *MotSts, INT32 SpdRef, BOOL SpdCtrlMode )
{
	BOOL	Vcmp;

	if( TRUE == SpdCtrlMode )
	{
		if( MlibABS(SpdRef - MotSts->var.MotSpd) <= MotSts->conf.VcmpSpdLevel )
		{
			Vcmp = TRUE;
		}
		else
		{
			Vcmp = FALSE;
		}
	}
	else
	{
		Vcmp = FALSE;
	}
	return Vcmp;
}


/****************************************************************************************************
* DESCRIPTION:
*		Interruption speed level detection processing
* RETURNS:
*
****************************************************************************************************/
PRIVATE BOOL  LpxCheckMotorSuspendLevel( SEQ_MOT_STS *MotSts,INT32 NorSuspSpd)
{
	BOOL MotSuspLvl;
	
	if( MotSts->var.AbsMotSpd > NorSuspSpd)
	{
		MotSuspLvl = TRUE;							/* Interruption speed detection signal setting(TRUE)*/
	}
	else
	{
		MotSuspLvl = FALSE; 						/* Interruption speed detection signal setting(FALSE)*/
	}
	return MotSuspLvl;

}



