<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<iarProjectConnection version="1.8" name="Flex Software">
    <device>
        <name>R9A07G084M04</name>
    </device>
    <includePath>
        <path>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</path>
        <path>$PROJ_DIR$/rzn/fsp/inc</path>
        <path>$PROJ_DIR$/rzn/fsp/inc/api</path>
        <path>$PROJ_DIR$/rzn/fsp/inc/instances</path>
        <path>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</path>
        <path>$PROJ_DIR$/rzn_cfg/driver</path>
        <path>$PROJ_DIR$/rzn_cfg/fsp_cfg</path>
        <path>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</path>
        <path>$PROJ_DIR$/rzn_gen</path>
        <path>$PROJ_DIR$/src</path>
    </includePath>
    <defines>
        <define>_RZN_CORE=CR52_0</define>
    </defines>
    <asmIncludePath>
        <path>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</path>
        <path>$PROJ_DIR$/rzn/fsp/inc</path>
        <path>$PROJ_DIR$/rzn/fsp/inc/api</path>
        <path>$PROJ_DIR$/rzn/fsp/inc/instances</path>
        <path>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</path>
        <path>$PROJ_DIR$/rzn_cfg/driver</path>
        <path>$PROJ_DIR$/rzn_cfg/fsp_cfg</path>
        <path>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</path>
        <path>$PROJ_DIR$/rzn_gen</path>
        <path>$PROJ_DIR$/src</path>
    </asmIncludePath>
    <asmDefines>
        <define>_RZN_CORE=CR52_0</define>
    </asmDefines>
    <linkerFile>
        <override>true</override>
        <path>$PROJ_DIR$/script/fsp_xspi1_boot_SRAM.icf</path>
    </linkerFile>
    <linkerExtraOptions>
        <arg>--config_search "$PROJ_DIR$"</arg>
    </linkerExtraOptions>
    <customArgVars>
        <group name="RA Smart Configurator">
            <argVar>
                <name>RASC_EXE_PATH</name>
                <value>C:\Renesas\rzn\sc_v2023-07_fsp_v1.3.0\eclipse\rasc.exe</value>
            </argVar>
        </group>
    </customArgVars>
    <files>
        <group name="Components">
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_compiler.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_cp15.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_gcc.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_iccarm.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/cmsis_version.h</path>
            <path>rzn/arm/CMSIS_5/CMSIS/Core_R/Include/core_cr52.h</path>
            <path>rzn/arm/CMSIS_5/LICENSE.txt</path>
            <path>rzn/board/custom/board.h</path>
            <path>rzn/fsp/inc/api/bsp_api.h</path>
            <path>rzn/fsp/inc/api/rm_ethercat_ssc_port_api.h</path>
            <path>rzn/fsp/inc/api/r_adc_api.h</path>
            <path>rzn/fsp/inc/api/r_elc_api.h</path>
            <path>rzn/fsp/inc/api/r_ether_phy_api.h</path>
            <path>rzn/fsp/inc/api/r_ether_selector_api.h</path>
            <path>rzn/fsp/inc/api/r_ioport_api.h</path>
            <path>rzn/fsp/inc/api/r_spi_flash_api.h</path>
            <path>rzn/fsp/inc/api/r_timer_api.h</path>
            <path>rzn/fsp/inc/api/r_transfer_api.h</path>
            <path>rzn/fsp/inc/fsp_common_api.h</path>
            <path>rzn/fsp/inc/fsp_features.h</path>
            <path>rzn/fsp/inc/fsp_version.h</path>
            <path>rzn/fsp/inc/instances/rm_ethercat_ssc_port.h</path>
            <path>rzn/fsp/inc/instances/r_cmt.h</path>
            <path>rzn/fsp/inc/instances/r_dmac.h</path>
            <path>rzn/fsp/inc/instances/r_dsmif.h</path>
            <path>rzn/fsp/inc/instances/r_ether_phy.h</path>
            <path>rzn/fsp/inc/instances/r_ether_selector.h</path>
            <path>rzn/fsp/inc/instances/r_ioport.h</path>
            <path>rzn/fsp/inc/instances/r_xspi_qspi.h</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Include/R9A07G084.h</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Include/renesas.h</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Include/system.h</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c</path>
            <path>rzn/fsp/src/bsp/cmsis/Device/RENESAS/SVD/RA.svd</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_clocks.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_clocks.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_common.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_common.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_compiler_support.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_delay.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_delay.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_io.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_io.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_irq.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_irq.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_mcu_api.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_module_stop.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_register_protection.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_register_protection.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_reset.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_reset.h</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_sbrk.c</path>
            <path>rzn/fsp/src/bsp/mcu/all/bsp_tfu.h</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_cache.c</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_cache.h</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_elc.h</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_feature.h</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_loader_param.c</path>
            <path>rzn/fsp/src/bsp/mcu/rzn2l/bsp_mcu_info.h</path>
            <path>rzn/fsp/src/rm_ethercat_ssc_port/renesashw.h</path>
            <path>rzn/fsp/src/rm_ethercat_ssc_port/rm_ethercat_ssc_port.c</path>
            <path>rzn/fsp/src/r_cmt/r_cmt.c</path>
            <path>rzn/fsp/src/r_dmac/r_dmac.c</path>
            <path>rzn/fsp/src/r_dsmif/r_dsmif.c</path>
            <path>rzn/fsp/src/r_ether_phy/r_ether_phy.c</path>
            <path>rzn/fsp/src/r_ether_selector/r_ether_selector.c</path>
            <path>rzn/fsp/src/r_ioport/r_ioport.c</path>
            <path>rzn/fsp/src/r_spi/r_spi.c</path>			
       	   <path>rzn/fsp/src/r_canfd/r_canfd.c</path>			
            <path>rzn/fsp/src/r_xspi_qspi/r_xspi_qspi.c</path>
        </group>
        <group name="Build Configuration">
            <path>rzn_cfg/driver/r_xspi_qspi_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/board_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_mcu_device_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_mcu_device_pn_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_mcu_family_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/bsp/bsp_pin_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/rm_ethercat_ssc_port_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_cmt_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_dmac_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_dsmif_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_ether_phy_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_ether_selector_cfg.h</path>
            <path>rzn_cfg/fsp_cfg/r_ioport_cfg.h</path>
        </group>
        <group name="Generated Data">
            <path>rzn_gen/bsp_clock_cfg.h</path>
            <path>rzn_gen/common_data.c</path>
            <path>rzn_gen/common_data.h</path>
            <path>rzn_gen/hal_data.c</path>
            <path>rzn_gen/hal_data.h</path>
            <path>rzn_gen/main.c</path>
            <path>rzn_gen/pin_data.c</path>
            <path>rzn_gen/vector_data.c</path>
            <path>rzn_gen/vector_data.h</path>
        </group>
        <group name="Program Entry">
            <path>src/hal_entry.c</path>
        </group>
    </files>
</iarProjectConnection>
