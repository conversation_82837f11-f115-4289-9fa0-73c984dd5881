/****************************************************************************************************
 *
 * FILE NAME:  Global.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.17
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	17-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _GLOBAL_H_
#define _GLOBAL_H_
	
#include "BaseLoops.h"
#include "BaseCmnStruct.h"
#include "PnPrmStruct.h"
#include "MotorIdentification.h"
#include "Cia402Appl.h"
#include "CheckAlarm.h"
#include "RegAccessIF.h"
#include "FunManager.h"
#include "BaseSequence.h"
#include "PowerManager.h"
#include "DataTraceManager.h"
#include "RegMngr.h"
#include "AdvancedAutoTuning.h"
#include "AutoNotchSet.h"
#include "CheckMotSts.h"
#include "ModelControl.h"
#include "FnOnePrmTuning.h"
#include "FnAtypeVibTune.h"
#include "FnMfcTuning.h"
#include "FnEasyFFT.h"
#include "HardApi.h"
#include "DataCache.h"
 
/*--------------------------------------------------------------------------------------------------*/
/*		 Task measure Struct 		    		            								    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT32	SA_maxTime;		
	UINT32	SA_crtTime;		
	UINT32	SA_sumTime;		
	UINT32	SAinB_exeTime;	
	UINT32	SAinC_exeTime;		
	UINT32	SB_maxTime;		
	UINT32	SB_crtTime;		
	UINT32	SB_sumTime;		
	UINT32	SB_exeflg;		
	UINT32	SBinC_exeTime;		
	UINT32	SC_maxTime;		
	UINT32	SC_crtTime;	
	UINT32	SC_exeflg;		
	UINT32	crtTimePer1ms;	
	UINT32	maxTimePer1ms;	
	UINT32	ScanSumTime;	
	
	UINT32	SCtask_wakeup_time;
	UINT32	SBcountInMS;
	UINT32	SCCount;
	UINT32	SBCount;
	UINT32	SACount;
        UINT32	SACountCnt;
        UINT32	SACountSub;
    UINT16      Slot;    
} TASK_MEASURE_TIME;


typedef	struct 
{
	INT32                  AxisID;
    UINT16                 AxisCobID;
    UINT16                 HardwareVer;    

	PRMDATA                *Prm;
	BE_SEQ_HNDL            *BaseSeq;
	BASE_CTRL              *BaseCtrls;                      // base control
	BASE_CTRL_OUT		   *BaseCtrlOut;					// TaskB output data
	BASE_LOOP              *BaseLoops;
	MOTSPDMAFIL			   *MotSpdMafil;
	CTRL_LOOP_OUT		   *CtrlLoopOut;		            // TaskA output data
	SEQ_CTRL_OUT		   *SeqCtrlOut;
	HW_STATUS_REG          *HwIputSignals;
	SEQ_MOT_STS			   *SeqMotSts;	
	#if NO_ADVFUNC
	REMVIBFREQ			   *RemVibFreq;
	#endif
	POWER_MNG              *PowMngr;
	#if NO_ADVFUNC
	DETVIB				   *DetVib;					
	#endif	
	ALARM                  *AlmMngr;
	FUN_CMN_CONTROL		   *FnCmnCtrl;	
	FUN_MANAGER			   *FnManager;		
	FUN_AXCOMMON		   *FnAxCommons;	
	REG_MANAGER_HANDLE	   *RegManager;	
	CHECK_ALARM            *CheckAlm;
	MOTOR_IDENTIFY         *MotorIdentify;
	TASK_MEASURE_TIME      *TaskProcessTime;

	TRCHNDL				   *TrcHndl;						// Data trace handle 
	UINT16				   *TraceBuffer;					// Pointer to data trace buffer 
    
    TVCLOOP                *TvcLoop;                        // Alarm data cache handle

	REG_HNDL			   *RegHndl;						// REG Manager handle 

	#if NO_ADVFUNC
	/* Utility Functions */
	ANOTCHSEQ				*ANotchSeq;			// Structure for automatic notch sequence
	AUTONOTCH				*ANotch;			// Structure for automatic notch setting
	FFTANALYZE				*FftAna;			// Structure for vibration frequency analysis (Online FFT)
	MFCTUNE					*MfcTune;			// Model gain tuning structure
//	VIBMON					*VibMon;			// Structure for online vibration monitor
//	INIVIBDET				*IniVibDet;			// Vibration detection level initialization structure
	ADATHNDL				*AdatHndl;			// Advanced Auto Tuning Handle
	ONEPRMTUNE				*OnePrmTune;		// One-parameter tuning structure
	AVIBTUNE				*AvibTune;			// A type damping control tuning structure
	EASYFFT					*EasyFFT;		    // Easy FFT structure	
//	OPTCLR					*OptClr;		    // Option card detection clear structure
	#endif

//	ALMLATCH_HNDL			*hAlmLatchHndl;						// Alarm latch data		
	#if NO_ETHERCAT	
    BASE_ECAT			    *EcatDate;
	#endif
}AXIS_HANDLE;



PUBLIC AXIS_HANDLE* GetAxisHandle(UINT16 AxisID);
PUBLIC void AxisGlobalsInitMain( void );
PUBLIC void SysResetAxisParameters( AXIS_HANDLE *Axis );

PUBLIC void IprmcalMainAxis( AXIS_HANDLE *Axis);
PUBLIC void PcalCopyMFCCfgParam(MFC_CFG_PRM *MfcCfgPrm, const PRMDATA *Prm);

   
#endif //_GLOBAL_H_

