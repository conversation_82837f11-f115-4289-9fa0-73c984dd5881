/**************************************************
** Copyright (c) 2016-202X 昆泰芯微电子科技有限公司
** 文件名: common_ktm.c
** 作者: liujunbo
** 日期: 2023.12.5
** 描述: KTM59xx芯片通用工具函数，提供数据转换和CRC校验功能
**
**************************************************/
#include <stdint.h>
#include "common_ktm.h"

/**
 * @brief 校验CRC8
 * @param[in] input 输入数据，包含数据和CRC校验码EcatDcSync.h
 * @param[in] len 数据位长度（不含CRC位）
 * @return uint8_t 校验结果（1=校验成功，0=校验失败）
 * @note 使用多项式0x83进行CRC8校验
 */
uint8_t IsCheckCRC8OK(uint64_t input, int32_t len)
{
  /* 将数据右移到合适位置 */
  input >>= 56 - len;
  uint64_t data = input;  /* 保存原始数据（包含CRC） */

  /* 清除CRC位（最低8位） */
  input &= 0xFFFFFFFFFFFFFF00;
  uint64_t inputTemp = input;

  /* 初始化CRC计算参数 */
  uint64_t mark = 0x8000000000000000;  /* 位掩码，用于检查最高位 */
  uint64_t ploy = 0x8380000000000000;  /* CRC多项式（0x83左移） */

  /* 调整多项式和掩码位置 */
  ploy >>= 56 - len;
  mark >>= 56 - len;

  /* CRC计算循环 */
  for (int32_t i = 0; i < len; i++)
  {
    /* 如果最高位为1，则异或多项式 */
    if ((input & mark) != 0)
    {
      input ^= ploy;
    }

    /* 右移掩码和多项式，处理下一位 */
    mark >>= 1;
    ploy >>= 1;
  }

  /* 计算最终CRC值并与原始数据比较 */
  inputTemp += (input ^ 0xFF) & 0xFF;  /* 添加取反的CRC值 */
  return (inputTemp == data) ? 1 : 0;  /* 如果计算结果与原始数据相同，则校验成功 */
}

/**
 * @brief 将64位数据转换为32位数组
 * @param[out] pbuffer 输出缓冲区指针
 * @param[in] data 要转换的64位数据
 * @param[in] startindex 输出缓冲区的起始索引
 * @param[in] length 要转换的32位数据个数
 * @return None
 * @note 从高位到低位依次填充，每32位填充一个数组元素
 */
void DataToDwordArr(uint32_t* pbuffer, uint64_t data, uint16_t startindex, uint8_t length)
{
  /* 计算结束索引 */
  uint16_t total = startindex + length;

  /* 从低位到高位依次填充数组 */
  for (int32_t i = total - 1; i >= startindex; i--)
  {
    pbuffer[i] = data;  /* 取低32位填充到数组 */
    data >>= 32;        /* 右移32位，处理下一个32位数据 */
  }
}

/**
 * @brief 将32位数组转换为64位数据
 * @param[in] pbuffer 输入缓冲区指针
 * @param[in] startindex 输入缓冲区的起始索引
 * @param[in] length 要转换的32位数据个数
 * @return uint64_t 转换后的64位数据
 * @note 从高位到低位依次读取，每个数组元素作为32位数据
 */
uint64_t DwordArrToData(uint32_t* pbuffer, uint16_t startindex, uint8_t length)
{
  /* 计算结束索引 */
  uint16_t total = startindex + length;
  uint64_t data = 0;

  /* 从高位到低位依次读取数组元素 */
  for (int32_t i = startindex; i < total; i++)
  {
    data <<= 32;        /* 左移32位，为新数据腾出空间 */
    data |= pbuffer[i];  /* 将当前32位数据合并到结果中 */
  }

  return data;
}

