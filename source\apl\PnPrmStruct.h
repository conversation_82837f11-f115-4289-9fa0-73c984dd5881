/****************************************************************************************************
 *
 * FILE NAME:  PnPrmStruct.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.16
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	16-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _PNPRMSTRUCT_H_
#define _PNPRMSTRUCT_H_
	
#include "BaseDef.h"


/*--------------------------------------------------------------------------------------------------*/
/*		 motor & encoder parameter Struct 		    	    	       							    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
    UINT16	MotType;				// motor type
    UINT16  RatFreq;                // motor rated Frequency        		     [0.1Hz]
    UINT16  RatPow;                 // motor rated power                         [0.01kw]
    UINT16  RatV;                   // motor rated voltage                       0: 220V  1:380V
    UINT16	RatCur;					// rated current                             [0.1A]
    UINT16	RatTrq;					// rated torque                              [0.01Nm]  
    UINT16  RatSpd;                 // motor rated speed                         [rpm]
    UINT16	MaxCur;					// max current                               [0.1A]	
    UINT16	MaxTrq;					// max torque                                [0.01Nm]  
    UINT16  MaxSpd;                 // motor max speed                           [rpm]
    UINT16  MotPolePairs;           // motor pole pairs  
    UINT16  MotR;                   // motor resistance                          [0.001ohm]	
    UINT16  MotL;                   // motor inductance                          [0.01mH]
    UINT16  Jmot;                   // motor rotor inertia                       [10^-6 kgm^2]
    UINT16  MotEmf;                 // motor EMF constat                         [0.01mV/rpm]
    UINT16  MotKt;                  // motor torque constant                     [0.1N..m/A]
    UINT16  MotTs;                  // motor mechanical constant                 [0.01ms]
    UINT16	EncType;			    // encoder type
    UINT16	AbsEncBitM;			    // absolute encoder multi-turn bit number
    UINT16	AbsEncBitS;			    // absolute encoder single-turn bit number
    UINT32  AbsEncOffset;           // absolute encoder offset
    UINT32  AbzEncPPR;              // abz encoder pulses per revolution
    UINT16	UseEncoder2;			// Use encoder2
    UINT16	EncType2;			    // encoder 2 type
    UINT16	AbsEncBitM2;			    // absolute encoder 2 multi-turn bit number
    UINT16	AbsEncBitS2;			    // absolute encoder 2 single-turn bit number
    UINT16  BisscDLen2;              // Bissc 2 data filed length     
    UINT32  AbzEncPPR2;              // abz encoder 2 pulses per revolution
    UINT16  MotLd;                  // motor inductance Ld                       [0.01mH].
    UINT16  MotLq;                  // motor inductance Ld                       [0.01mH]
    UINT16	MotorPitch;	            // Linear Moter Pitch [mm]
    UINT16  BisscDLen;              // Bissc data filed length    
    UINT32  EncConfig;
    UINT32  EncConfig2;
} MOTOR_PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		 base config parameter Struct 		    	    	       				         			*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16	DevType;				// device type  
	UINT16	DevVolt;				// device voltage grade                      [V]
	UINT16  DevRatW;                // device rated power                        [0.01kw]
	UINT16  DevMaxW;                // device max power                          [0.01kw]
	UINT16  DevRatCur;              // device rated current                      [0.1A]
	UINT16  DevMaxCur;              // device max current                        [0.1A]
	UINT16  ResDir;                 // reverse rotate direction              
	UINT16  OvrTravStpMode;         // over travel stop mode
	UINT16  ForceStpMode;           // force stop mode
	UINT16  AlmStpMode;             // alarm stop mode
	UINT16  FaultNO1StpMode;        // fault NO.1 stop mode
	UINT16  BrkRlsTim;              // brake release delay time                  [ms]
	UINT16  BrkTim;                 // brake command servo-off delay time        [ms]
	UINT16  BrkSpd;                 // brake command output speed level          [rpm]
	UINT16  BrkDelay;               // servo off brake command delaytime         [ms]
    UINT16  BrakeEn;                // brake enable
	UINT16  LedWrnUse;              // LED warn Select
	UINT16	MinRegenR;				// Regen Resistor Min Value                  [ohm]
	UINT16	RegenRType;				// Regen Resistor Type
	UINT16	RegenDisCoff;		    // Res Heat Dissipation Coeff                [1%]
	UINT16	ExtRegenPow;			// Ext Regen Resistor Power                  [0.01kw]
	UINT16  ExtRegenR;			    // Ext Regen Resistor Value                  [ohm]
	UINT16  SysInit;			    // System Init
	UINT16	PowInputSel;			// Power Input Select                        [0: 3P, 1: 1P, 2:DC]
	UINT16	AcOffTim;				// Ac Off Detect Time                        [ms] 
	UINT16  PowChgWaitTim;          // DC charging completion waiting time       [ms]
	UINT16  PowRdyWaitTim;          // Power ready waiting time	                 [ms]
	UINT16  OvrLoadWrnLvl;          // Over load warning level                   [1%]
    UINT16  MotOlBasTrq_drt;
    UINT16  UWVSeq;                 // Motor line UWVSeq sequence                0:UVW     1:UWV
	UINT16	AcoffDischarge;         // Ac Off Discharge switch
	UINT32  SvoVer;                 // servo version
	INT16   IuOffset;
	INT16   IvOffset;   
    UINT32  DevID;                   // device type  [8-16 bits]0x00 device id    [0-8bits]0x00 aixs
    UINT16  BusFrame;                // [0-7 bit] buadrate set  [8-15bit] bus frame type set 
    UINT16  OpenLoopT;               // current openloop type 
    INT16   OpenLoopVa;              // current openloop alfa phase voltage
    INT16   OpenLoopVb;              // current openloop beta phase voltage 
    UINT16  OpenLoopA;               // current openloop angle inc 
    UINT16  BusType;                 // Bus type select bit0：ethercat bit 1:modbus  bit 2：can 
    UINT16  PluseInMode;             // Pulse Input Mode [0-7 bit] Counter Mode set 0:  up_down counting 
    UINT16  MotorPrmSelect;          // motor prm init select 0:servo eeprom 1:encoder eeprom
    UINT16  FanCtrlDuty;             // motor prm init select 0:servo eeprom 1:encoder eeprom
    INT32   EncZeroOffset;
    UINT16  Analog1ZeroPoint;
    UINT16  Analog2ZeroPoint;
    UINT16  PwmTestDuty;             // Bootstrap Test Pwm duty Cycle  [1%]
    UINT16  PwmDeadTime;             // Pwm dead time [10ns]
}BASE_CFG_PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		 control config parameter Struct 		    	    	       				 			    */
/*--------------------------------------------------------------------------------------------------*/



typedef struct 
{
	UINT16  CtrlSource;             // Control Source Option
        
	UINT16 PosLoopHz;				// position loop gain	                     [0.1Hz]
	UINT16 SpdLoopHz;				// speed loop gain	                         [0.1Hz]
	UINT16 SpdLoopTi;				// speed loop integral time constant         [0.01ms]     
        
	UINT16  Jrat;                   // motor inertia ratio                       [%]
	UINT16	SpdLoopHz2;				// speed loop gain 2	                     [0.1Hz]
	UINT16	SpdLoopTi2;				// speed loop integral time constant 2       [0.01ms]
	UINT16  PosLoopHz2;             // position loop gain 2          			 [0.1Hz]
    UINT16  CurLoopHz;              // current loop gain                         [1Hz]
	UINT16  ModeSw;                 // speed mode switch
	UINT16  ModeSwTrq;              // mode switch torque value                  [1%]
	UINT16  ModeSwSpd;              // mode switch speed value                   [rpm]
	UINT16  ModeSwAcc;              // mode switch acc value                     [rpm/s]
	UINT16  ModeSwPE;               // mode switch pos error value               [1]
	UINT16  TrqffCfg;               // torque feedforward configure
	UINT16  TrqffFil;               // torque feedforward filter time constant   [0.01ms] 
	UINT16  TrqffGain;              // torque feedforward gain                   [1%]
	UINT16  SpdffCfg;               // speed feedforward configure
	UINT16  SpdffFil;               // speed feedforward filter time constant    [0.01ms] 
	UINT16  SpdffGain;              // speed feedforward gain                    [1%]
	UINT16  SvoOnSpdLim;            // servo on speed limit level                [rpm]
	UINT16  TconSpdLim;             // torque control speed limit level          [rpm]
	UINT16  SpdMaFilCfg;            // speed moving average filter configure
	UINT16  TrqFilTime;             // first torque filter time                  [0.01ms]
	UINT16  TrqFil2Freq;            // second torque filter frequency            [hz]
	UINT16  TrqFil2Q;               // second torque filter Q value              [0.01]
	UINT16  FwIntTrqLmt;            // forward internal torque limit             [1%]
	UINT16  RvIntTrqLmt;            // reverse internal torque limit             [1%]
	UINT16  FwExtTrqLmt;            // forward external torque limit             [1%]
	UINT16  RvExtTrqLmt;            // reverse external torque limit             [1%]
	UINT16  EstopTrqLmt;            // emgency stop torque limit                 [1%]
	UINT16  FFMethod;               // phase find method
	UINT16  FFStableTime;		    // phase find stable time	                 [ms]
	UINT16  FFRampTime;             // phase find ramp time                      [ms]
	UINT16  FFCur;                  // Fhase find current                        [0.1A]
	UINT16  FunGenType;		        // Funcation Generator Type                  1:sin 2:squart 3:step
    UINT16  FunGenNum;	            // Funcation Generator Number    
	UINT16  FunGenFrq;	            // Funcation Generator Frequency             [0.1Hz]
	UINT32  FunGenAmp;	            // Funcation Generator Amplitude             [UU]
    UINT32  FunGenSlope;            // Funcation Generator Slope                 [UU/s]
	UINT16  SpdRefLpfTime;          // Speed reference low pass filter time      [0.01ms]
	UINT32  NotchFilterCfg;         // Notch filter config
	UINT16  NotchFilterFc1;         // Notch filter frequency 1                  [Hz]
	UINT16  NotchFilterQ1;	        // Notch filter Q factor 1                   [0.01]
	UINT16  NotchFilterDepth1;	    // Notch filter Depth 1                      [0.001]
	UINT16  NotchFilterFc2;         // Notch filter frequency 2                  [Hz]
	UINT16  NotchFilterQ2;	        // Notch filter Q factor 2                   [0.01]
	UINT16  NotchFilterDepth2;	    // Notch filter Depth 2                      [0.001]
	UINT16  NotchFilterFc3;         // Notch filter frequency 3                  [Hz]
	UINT16  NotchFilterQ3;	        // Notch filter Q factor 3                   [0.01]
	UINT16  NotchFilterDepth3;	    // Notch filter Depth 3                      [0.001]
	UINT16  NotchFilterFc4;         // Notch filter frequency 4                  [Hz]
	UINT16  NotchFilterQ4;	        // Notch filter Q factor 4                   [0.01]
	UINT16  NotchFilterDepth4;	    // Notch filter Depth 4                      [0.001]
	UINT16  SpdFdbLpfTime;          // Speed feedback low pass filter time       [0.01ms]
    UINT16  InertiaAccTime;         // Inertia Identification Accelerate Time    [ms]
    UINT32  InertiaPosition;        // Inertia Identification Positon            [CNT]
    UINT16	CurLoopTi;				// current loop integral time constant       [0.01ms]
    UINT16  PosRefMaFil;            // position reference moving average filter time  [0.1ms]
	UINT16  SpdffMaFil;             // speed ff moving average filter time       [0.1ms]
	UINT16  TrqffMaFil;             // torque ff moving average filter time      [0.1ms]
	UINT16  PosRefHFRad;            // position Reference high filter ratio      [%]
	UINT16  PosRefExpFil;           // position reference acc/dec time           [0.1ms]
	UINT16  PjogSW;                 // Program Jog switch
	UINT32  PjogDist;    			// Program Jog moving distance         		 [CNT]
	UINT16  PjogSpd;    			// Program Jog moving speed    				 [rpm]
	UINT16  PjogAccT;   			// Program Jog Acc/Dec time   			     [1ms]
	UINT16  PjogWaitT;   			// Program Jog wait time      				 [1ms]
	UINT16  PjogNum;                // Program Jog moving number                 [1]
	INT16   JogSpd;                 // Jog Speed           						 [rpm]
	UINT16  JogAccT;                // Jog Acceleration time          			 [ms]
	UINT16  JogDecT;                // Jog Deceleration time           			 [ms]
	UINT16  GnChgT1;                // Gain Change Time 1           		 	 [ms]
	UINT16  GnChgT2;                // Gain Change Time 2           		 	 [ms]
	UINT16  GnChgWait1;             // Gain Change Wait Time 1           		 [ms]
	UINT16  GnChgWait2;             // Gain Change Wait Time 2           		 [ms]
	UINT16  GnChgSw;                // Gain Change Switch
	UINT16	SpdKpdff;				// speed PDFF coff          				 [%]
	UINT16	TrqFilTime2; 			// first torque filter time 2				 [0.01ms]
	UINT16	GravTrq;            	// Gravity Torque                   		 [0.1%]
	UINT16	PosClombTrq;        	// Static Friction Positive Torque  		 [0.1%]
	UINT16	NegClombTrq;      		// Static Friction Negetive Torque 			 [0.1%]
	UINT16	ViscTrq;        		// Viscosity Torque   						 [0.1%]
	UINT16  FricSpd;      		    // Friction speed  							 [rpm]
	UINT32  PosDisturComp;
    UINT16  FunctionSw;             // Function Switch  [bit 0] : Dynamic PID switch     
    UINT16  PulseSpdMode;      		   
	UINT16  JogTime;                // Jog Time :AccT+VelT     					 [0.1s]    
    UINT16  MotIdentCur;                 //  
    INT16   PluseInCnt;             // pluse input counting
        UINT16  PosRefFil;
    UINT16  PosStiff;                 // 0.01f
    UINT16  SpdStiff;                 // 0.01f
    
}CTRL_CFG_PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		 advanced config parameter Struct 		    	        	       				 	    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16  AvibOpt;                // A type vibration suppression option
	UINT16	AvibFrq;				// A type damping frequency			         [0.1HZ]		
	UINT16	AvibGn;					// A type damping gain correction			 [1%]		
	UINT16	AvibDamp;				// A type damping damping gain				 [1%]
	INT16	AvibFil1;				// A type damping filter time constant 1 correction   [0.01ms] 	
	INT16	AvibFil2;				// A type damping filter time constant 2 correction   [0.01ms] 			
	UINT16	AvibDamp2;				// Inertia damping damping gain			     [1%]
	UINT16	AvibFrq2;				// Inertia damping frequency		         [0.1HZ]
	UINT16  DobsGn;                 // Disturbance observer gain                 [1%]
	UINT16  DobsGn2;                // Disturbance observer gain 2               [1%]
	UINT16  DobsCoff;               // Disturbance observer coefficient          [1%]
	INT16   DobsFreqComp;           // Disturbance observer freq correction      [0.1HZ]
	UINT16  DobsGnComp;             // Disturbance observer gain correction      [1%]
	UINT16  VobsGn;                 // Speed observer gain                       [HZ]
	UINT16  VobsPosCompGn;          // Speed observer pos compensation gain      [1%]
	UINT16  AdvAppSW;               // Advanced Application Switch
	UINT16  EVibOpt;                // Vibration suppression option              [1]
	UINT16  EVibFrq;                // Vibration suppression frequency           [0.1HZ]
	UINT16  EVibFil;                // Vibration suppression compensation        [1%]
	UINT16  TlsCfg;                 // Tuneless setting                          [1]
	UINT16  JstVal;         	    // Inertia Identification start value        [1%]
	UINT16  AnotchSw;               // Automatic notch filter switch             [1]
    UINT16  MfcSw;                  // Model following control switch            [1] 
    UINT16  MfcGn;                  // Model following control gain              [0.1/s]
    UINT16  MfcGnComp;              // MFC gain correction                       [0.1%]
    UINT16  MfcBiasFw;              // MFC forward bias                          [0.1%]
    UINT16  MfcBiasRv;              // MFC reverse bias                          [0.1%]
    UINT16  VibSupFrq1A;            // Vibration suppression 1 frequency A       [0.1HZ]
    UINT16  VibSupFrq1B;            // Vibration suppression 1 frequency B       [0.1HZ]
    UINT16  MfcVffComp;             // MFC Velocity feedforward compensation     [0.1%]
    UINT16  MfcGn2;                 // MFC gain 2                                [0.1/s]
    UINT16  MfcGnComp2;             // MFC gain correction 2                     [0.1%]
    UINT16  WfbKp;          	    // Voltage feedback loop proportional gain   [HZ]
	UINT16  WfbTi;                  // Voltage feedback integration time constant  [us]
    UINT16  WfIdSet;                // max speed corresponding to IdRef          [0.1%]
    UINT16  WfSwitch;               // weak filed control switch                 [1]
    UINT16  VibSupFrq2;             // Vibration suppression 2 frequency         [0.1HZ]
	UINT16	VibSupFrq2Comp;         // Vibration suppression 2 frequency correction [1%]

    UINT16  MotPrmEstSts;           // motor parameter estimation state [Pn32C]
    UINT16  ClampingJawSts;
    INT32  ClampingJawForLit;
    INT32  ClampingJawRevLit;
    UINT32 PTPStpTime;
    UINT16 ClampingJawPosVal;
	    UINT16 ClampingJawIniState;
    UINT16  AdatMaxSpd;             // Advanced AT MaxSpeed [rpm]	
}ADVD_PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		 general input config parameter Struct 		    	        	       				 	    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16  FuncDI1;                // DI1 function select       
	UINT16  LogicDI1;               // DI1 logic select
	UINT16  FuncDI2;                // DI2 function select       
	UINT16  LogicDI2;               // DI2 logic select
	UINT16  FuncDI3;                // DI3 function select       
	UINT16  LogicDI3;               // DI3 logic select
	UINT16  FuncDI4;                // DI4 function select       
	UINT16  LogicDI4;               // DI4 logic select
	UINT16  FuncDI5;                // DI5 function select       
	UINT16  LogicDI5;               // DI5 logic select
	UINT16  FuncDI6;                // DI6 function select       
	UINT16  LogicDI6;               // DI6 logic select
}INPUT_PRM;

/*--------------------------------------------------------------------------------------------------*/
/*		 general Output config parameter Struct 		    	        	       				 	*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16  FuncDO1;                // DO1 function select       
	UINT16  LogicDO1;               // DO1 logic select
	UINT16  FuncDO2;                // DO2 function select       
	UINT16  LogicDO2;               // DO2 logic select
	UINT16  FuncDO3;                // DO3 function select       
	UINT16  LogicDO3;               // DO3 logic select
	UINT16  FuncDO4;                // DO4 function select       
	UINT16  LogicDO4;               // DO4 logic select
	UINT16  FuncDO5;                // DO5 function select       
	UINT16  LogicDO5;               // DO5 logic select
	UINT16  FuncDO6;                // DO6 function select       
	UINT16  LogicDO6;               // DO6 logic select

}OUTPUT_PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		 alarm config parameter Struct 		    	        	       				 			    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16  OverVoltValue;          // Overvoltage (OV) detection level          [V]
	UINT16	NonRegOvLevel;			// NonRegen OV level 		                 [V]
	UINT16  UnderVoltValue;         // Undervoltage (UV) detection level         [V]
	UINT16  UnderVoltFil;           // UV detection filter time constant         [ms]
	UINT16  UvWrnValue;             // Undervoltage (UV) warning detection level
	UINT16  OvrSpdValue;            // Over speed value                          [rpm]
	UINT32  OvrPerWrnLevel;         // Over position error warning level         [Pulse unit]
	UINT32  OvrPerAlmLevel;         // Over position error alarm level           [Pulse unit]
	UINT32  SvonOvrPerWrnLevel;     // Over position error warning level when servo on    [Pulse unit]
	UINT32  SvonOvrPerAlmLevel;     // Over position error alarm level when servo on       [Pulse unit]
	UINT16  OvrRunTrq;              // Over run detection torque                  [%]
    UINT32  TimeStamp;              // Time stamp                                [s]
	UINT16	AlmMask;				// Alarm Mask Array
	UINT16  VibChkSens;             // Vibration detection sensitivity           [%]
	UINT16  VibChkSpd;              // Vibration detection level (rotation)      [rpm]
	UINT16  JstVibChkSpd;           // Vibration detection level (rotation) during inertia identification [rpm]
	UINT16  VibDetSw;               // Vibration detection switch 
	UINT16  RemVibDetW;         	// Remain vibration detection width        	 [0.1%]
	UINT32  PosCoinLvl;             // Position completion width                 [Pulse unit]
	UINT32  PosNearLvl;				// Position near signal range                [Pulse unit]
    UINT32  AlmCacheCh1;            // Alarm Cache chennal 1 setting
    UINT32  AlmCacheCh2;            // Alarm Cache chennal 2 setting
    UINT32  AlmCacheChTime;         // Alarm Cache chennal Time setting    
    UINT16  OverCurLvl;
    UINT16  BusBreakTime;
    UINT16  BusBreakEnable;
    UINT16  EncAbnormalValue;
}ALM_CFG_PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		 Auxiliary parameters Struct 		    	        	       				 			    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{    
    UINT16  SysReboot;
    UINT16  SysPrmInit;
    UINT16  FaultRst;
    UINT16  ErrLogClr;
    UINT16  MotIdent;
    UINT16  SavePrm;
    UINT16  LoadPrm;
	UINT16  FunGenStart;
	UINT16  FnSvoOff;
	UINT16  PrgJogCmd;
	UINT16  JogCmd;    
    UINT16  MIIOLStart;
	UINT16  EncCmd;
    UINT16  AATCmd;
    UINT16  DevIdConfig;
    UINT16  EscWrite;
    UINT16  MotEncZero;
    UINT16  ClampingJawInit;
    UINT16  PTPStart;
    UINT16  AnalogZeroCal;
}AUX_PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		 Auxiliary parameters Struct 		    	        	       				 			    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{    
    UINT32  Voltage;
    UINT32  IPMCurrent;
    INT32   PosRef;
    INT32   PosFdb;
    INT32   PosErr;
    INT32   PosOutput;
    INT32   SpdRef;
    INT32   SpdFdb;
    INT32   SpdFdb1ms;
    INT32   SpdErr;
    INT32   SpdOutput;
    INT32   TrqRef;
    INT32   TrqFdb;
    INT32   CurrentIa;
    INT32   CurrentIb;
    INT32   CurrentIc;
    INT32   CurrentIdRef;
    INT32   CurrentIqRef;
    INT32   CurrentIdFbd;
    INT32   CurrentIqFbd;
    INT32   EncMechAngle;
    INT32   EncElecAngle;   
    INT16   IPMTemp;
    INT16   MototTemp;
	UINT32	AlmSet0;
	UINT32	AlmSet1;
	UINT32	AlmSet2;
	UINT32	AlmSet3;
    UINT16  ActualEnable;
    UINT16  MotorIdentState;
    UINT32  EcatCycleTime;
    UINT32  EcatSync0CycleTime;
    UINT16  EcatSmEventMissedCnt;
    UINT16  EcatSyncType;    
    UINT8  Ecat0InFCnt ;    
    UINT8  Ecat0RxErrCnt;    
    UINT8  Ecat0FwdErrCnt;      
    UINT8  Ecat0LostLinkCnt;    
    UINT8  Ecat1InFCnt ;    
    UINT8  Ecat1RxErrCnt;    
    UINT8  Ecat1FwdErrCnt;    
    UINT8  Ecat1LostLinkCnt;     
    UINT8  EcatPuErrCnt;    
    UINT8  EcatPDIErr ;   
    UINT8 EscStateTrans;
    UINT16 AlStatus;
    UINT16 IRQCNT;
    UINT16 Sync0CNT;
    UINT32 DLControl;
    UINT32 DLStatus;
    UINT16 DiPinFunStatus;
    UINT16 DoPinFunStatus;
    UINT32 CanFdCh0SR;
    UINT32 CanFdCh0ER;
    UINT32 CanFdGlSR;
    UINT32 CanFdGlER;
    UINT32 CanFdTQ0SR; 
    UINT32 Ch0CanFdSR;    
}MONITOR_PRM;
/*--------------------------------------------------------------------------------------------------*/
/*		 cia-402 parameters Struct 		    	        	       				 			        */
/*--------------------------------------------------------------------------------------------------*/

typedef struct
{
    UINT16   PhyAddr;
    UINT16   PhyRegAddr;
    UINT16   Phy0RegValue;
    UINT16   Phy1RegValue; 
}ECAT_PRM;
typedef struct
{
    INT16    AbortConnectionOptionCode0x6007;
    UINT16   ErrorCode0x603F;
    UINT16   Controlword0x6040;
    UINT16   Statusword0x6041;
    INT16    QuickstopOptionCode0x605A;
    INT16    ShutdownOptionCode0x605B;
    INT16    DisableOperationOptionCode0x605C;
    INT16    HaltOptionCode0x605D;
    INT16    FaultReactionCode0x605E;
    INT8     ModesOfOperation0x6060;
    INT8     ModesOfOperationDisplay0x6061;
    INT32    PositionDemandValue0x6062;
    INT32    PositionActualInternalvalue0x6063;
    INT32    PositionActualValue0x6064;
    UINT32   FollowingErrorWindow0x6065;
    UINT16   FollowingErrorTimeout0x6066;
    UINT32   PositionWindow0x6067;
    UINT16   PositionWindowTime0x6068;
	INT32    VelocitySensorValue0x6069;
    INT32    VelocityDemandValue0x606B;
    INT32    VelocityActualValue0x606C;
    UINT16   VelocityWindow0x606D;
    UINT16   VelocityWindowTime0x606E;
    UINT16   VelocityThreshold0x606F;
    UINT16   VelocityThresholdTime0x6070;
    INT16    TargetTorque0x6071;
    INT16    TorqueDemand0x6074;
    INT16    TorqueActualValue0x6077;
	INT16    CurrentActualValue0x6078;
    INT32    TargetPosition0x607A;
    INT32    HomeOffset0x607C;
	INT32    MinSoftwarePositionLimit0x607D;
	INT32    MaxSoftwarePositionLimit0x607D;
    UINT32   Max_profile_velocity0x607F;
    UINT32   ProfileVelocity0x6081;
    UINT32   EndVelocity0x6082;
    UINT32   ProfileAcceleration0x6083;
    UINT32   ProfileDeceleration0x6084;
    UINT32   QuickstopDeceleration0x6085;
    INT16    MotionProfileType0x6086;
    UINT32   TorqueSlope0x6087;
    INT16    TorqueProfileType0x6088;

	UINT32   MotorRevolutions0x6091; 
	UINT32   LoadShaftRevolutions0x6091; 
    UINT32   Feed0x6092; 
	UINT32   ShaftRevolutions0x6092; 

    INT16    HomingMethod0x6098;
	UINT32   FastHomingSpeed0x6099;
	UINT32   SlowHomingSpeed0x6099; 
    UINT32   HomingAcceleration0x609A;
    INT32    ProfileJerk10x60A4;
    INT32    ProfileJerk20x60A4;
    INT32    ProfileJerk30x60A4;
    INT32    ProfileJerk40x60A4;
    INT32    ProfileJerk50x60A4;
    INT32    ProfileJerk60x60A4;
    INT32    PositionOffset0x60B0;
    INT32    VelocityOffset0x60B1;
    INT16    TorqueOffset0x60B2;
    UINT32   Max_acceleration0x60C5;
    UINT32   Max_deceleration0x60C6;
    UINT16   PositiveTorqueLimitValue0x60E0;
    UINT16   NegativeTorqueLimitValue0x60E1;
    INT32    FollowingErrorActualValue0x60F4;
    INT32    PositionDemandInternalvalue0x60FC;
    UINT32   DigitalInputs0x60FD;
    UINT32   DigitalOutputsControl0x60FE;
    UINT32   DigitalOutputsMask0x60FE;  
    INT32    TargetVelocity0x60FF;    
    UINT32   SupDriveModes0x6052;
}CiA402_PRM;


typedef struct
{
    UINT16   AutoEnable;       // 自动使能 0：自动功能关闭 1：自动功能使能
    INT16    Max_Postion;      // 标幺最大位置
    INT16    Min_Postion;      // 标幺最小位置
    UINT16   Max_Velocity;     // 标幺最大速度
    UINT16   Max_Torque;       // 标幺最大转矩
    UINT16   RefMaxVol;        // 标幺最大电压  
    UINT16   RefMaxCur;        // 标幺最大电流
    UINT16   HjLimitEnble;     // 限位使能 0：限位功能关闭 1：限位功能使能
    UINT16   HjLimitActMode;   // 限位动作模式 0：转矩指令置0 1：位置指令限制
    UINT16   HjVelLimit;       // 限位速度
    UINT16   HjTrqLimit;       // 限位转矩
    UINT16   HjLimitKp;        // 限位Kp
    UINT16   HjLimitKv;        // 限位Kv
    INT16   DyHjMaxTqrLimit;  // 动态限位最大转矩  
    INT16   DyHjMinTqrLimit;  // 动态限位最小转矩  
    INT16   DyHjMaxVelLimit;  // 动态限位最大速度
    INT16   DyHjMinVelLimit;  // 动态限位最小速度
    UINT16  IpdKpMax;         // 导纳控制最大Kp
    UINT16  IpdKdMax;         // 导纳控制最大Kd
}CUSTOM_PRM;
/*--------------------------------------------------------------------------------------------------*/
/*		 parameter data Struct 		    	    	            								    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	MOTOR_PRM           PnMotPrm;
	BASE_CFG_PRM        PnCfgPrm;
	CTRL_CFG_PRM        PnCtrlCfgPrm;
	ADVD_PRM            PnAdvPrm;
	INPUT_PRM           PnInputPrm;
	OUTPUT_PRM          PnOutputPrm;
	ALM_CFG_PRM         PnAlmCfgPrm;
	AUX_PRM             PnAuxPrm;
    MONITOR_PRM         PnMoniPrm;
    ECAT_PRM            PnEcatPrm;
	CiA402_PRM          PnCia402Prm;
    CUSTOM_PRM          PnCustomPrm;
}PRMDATA;

#endif //_PNPRMSTRUCT_H_
