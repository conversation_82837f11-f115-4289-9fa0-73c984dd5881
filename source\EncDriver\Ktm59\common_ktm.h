#ifndef __COMMON_H
#define __COMMON_H

/**
 * @file common_ktm.h
 * @brief KTM59xx芯片通用工具函数头文件
 * @details 定义了数据转换和CRC校验相关的函数接口
 */

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/**
 * @brief 校验CRC8
 * @param[in] input 输入数据，包含数据和CRC校验码
 * @param[in] len 数据位长度（不含CRC位）
 * @return uint8_t 校验结果（1=校验成功，0=校验失败）
 */
uint8_t IsCheckCRC8OK(uint64_t input, int32_t len);

/**
 * @brief 将64位数据转换为32位数组
 * @param[out] pbuffer 输出缓冲区指针
 * @param[in] data 要转换的64位数据
 * @param[in] startindex 输出缓冲区的起始索引
 * @param[in] length 要转换的32位数据个数
 * @return None
 */
void DataToDwordArr(uint32_t* pbuffer, uint64_t data, uint16_t startindex, uint8_t length);

/**
 * @brief 将32位数组转换为64位数据
 * @param[in] pbuffer 输入缓冲区指针
 * @param[in] startindex 输入缓冲区的起始索引
 * @param[in] length 要转换的32位数据个数
 * @return uint64_t 转换后的64位数据
 */
uint64_t DwordArrToData(uint32_t* pbuffer, uint16_t startindex, uint8_t length);

#ifdef __cplusplus
}
#endif

#endif /* __COMMON_H */
