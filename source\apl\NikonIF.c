/****************************************************************************************************
 *
 * FILE NAME:  NikonIF.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.09.09
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	09-09-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "NikonIF.h"

PRIVATE UNI_NKABSENCCDF_REG     NKAbsEncCdf_Reg;
PRIVATE UNI_NKABSENCMDF_REG     NKAbsEncMdf_Reg;


PRIVATE UINT8 NikonCrc8Table[]=
{
	0x00, 0x64, 0xc8, 0xac, 0xe1, 0x85, 0x29, 0x4d, 0xb3, 0xd7, 0x7b, 0x1f, 0x52, 0x36, 0x9a, 0xfe,
	0x17, 0x73, 0xdf, 0xbb, 0xf6, 0x92, 0x3e, 0x5a, 0xa4, 0xc0, 0x6c, 0x08, 0x45, 0x21, 0x8d, 0xe9,
	0x2e, 0x4a, 0xe6, 0x82, 0xcf, 0xab, 0x07, 0x63, 0x9d, 0xf9, 0x55, 0x31, 0x7c, 0x18, 0xb4, 0xd0,
	0x39, 0x5d, 0xf1, 0x95, 0xd8, 0xbc, 0x10, 0x74, 0x8a, 0xee, 0x42, 0x26, 0x6b, 0x0f, 0xa3, 0xc7,
	0x5c, 0x38, 0x94, 0xf0, 0xbd, 0xd9, 0x75, 0x11, 0xef, 0x8b, 0x27, 0x43, 0x0e, 0x6a, 0xc6, 0xa2,
	0x4b, 0x2f, 0x83, 0xe7, 0xaa, 0xce, 0x62, 0x06, 0xf8, 0x9c, 0x30, 0x54, 0x19, 0x7d, 0xd1, 0xb5,
	0x72, 0x16, 0xba, 0xde, 0x93, 0xf7, 0x5b, 0x3f, 0xc1, 0xa5, 0x09, 0x6d, 0x20, 0x44, 0xe8, 0x8c,
	0x65, 0x01, 0xad, 0xc9, 0x84, 0xe0, 0x4c, 0x28, 0xd6, 0xb2, 0x1e, 0x7a, 0x37, 0x53, 0xff, 0x9b,
	0xb8, 0xdc, 0x70, 0x14, 0x59, 0x3d, 0x91, 0xf5, 0x0b, 0x6f, 0xc3, 0xa7, 0xea, 0x8e, 0x22, 0x46,
	0xaf, 0xcb, 0x67, 0x03, 0x4e, 0x2a, 0x86, 0xe2, 0x1c, 0x78, 0xd4, 0xb0, 0xfd, 0x99, 0x35, 0x51,
	0x96, 0xf2, 0x5e, 0x3a, 0x77, 0x13, 0xbf, 0xdb, 0x25, 0x41, 0xed, 0x89, 0xc4, 0xa0, 0x0c, 0x68,
	0x81, 0xe5, 0x49, 0x2d, 0x60, 0x04, 0xa8, 0xcc, 0x32, 0x56, 0xfa, 0x9e, 0xd3, 0xb7, 0x1b, 0x7f,
	0xe4, 0x80, 0x2c, 0x48, 0x05, 0x61, 0xcd, 0xa9, 0x57, 0x33, 0x9f, 0xfb, 0xb6, 0xd2, 0x7e, 0x1a,
	0xf3, 0x97, 0x3b, 0x5f, 0x12, 0x76, 0xda, 0xbe, 0x40, 0x24, 0x88, 0xec, 0xa1, 0xc5, 0x69, 0x0d,
	0xca, 0xae, 0x02, 0x66, 0x2b, 0x4f, 0xe3, 0x87, 0x79, 0x1d, 0xb1, 0xd5, 0x98, 0xfc, 0x50, 0x34,
	0xdd, 0xb9, 0x15, 0x71, 0x3c, 0x58, 0xf4, 0x90, 0x6e, 0x0a, 0xa6, 0xc2, 0x8f, 0xeb, 0x47, 0x23,
};

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE UINT8 NikonCrc8Check(UINT8 *ptr, UINT8 len)
{
	UINT8 crc = 0x00;
	for(;len > 0;len--)
	{
		crc = NikonCrc8Table[crc^*ptr];
		ptr++;
	}
	return (crc);
}


/****************************************************************************************************
 * DESCRIPTION:
 *         Reverse the binary bits of a hexadecimal number
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE UINT16 calrev(UINT16 num, UINT16 Len)
{
    UINT16 y = 0;
    for (UINT16 i = 0; i < Len; i++)
    {
        y = (y << 1) | (num & 1);
        num >>= 1;
    }
    return y;
}

/****************************************************************************************************
 * DESCRIPTION:
 *          CRC3 calculation(input data - Reverse, output data - Reverse)
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE UINT16 getFCS(UINT16 M, UINT8 p)
{
    UINT16 tmp;
    UINT8 plen = 0, Mlen = 0;
    UINT8 i;
    
    //Reverse order input data
    M = calrev(M,10);
 
    //Get the effective length of a polynomial(p)
    tmp = p;    
    while (tmp) 
    {
        tmp >>= 1;
        plen++;
    }
    
    //Get the effective length of the data(M)
    tmp = M;    
    while (tmp) 
    {
        tmp >>= 1;
        Mlen++;
    }
 
    //Get the dividend
    M <<= (plen - 1);   
 
     for (i = Mlen + plen-1 - 1; i >= plen - 1; i--)
    {
        if (M & (1 << i)) 
        { 
            M ^= p << (i - (plen - 1));
        }
    }

    //Reverse order output data
    M = calrev(M,3);
       
    return M;
}

/****************************************************************************************************
 * DESCRIPTION:
 *         config CDF and write it to TxREG1
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void NikonSetCdf(UINT16 Cmd, UINT16 EncoderID, UINT16 AxisID)
{
    UINT16 temp = 0x00;
    
    NKAbsEncCdf_Reg.bit.SyncCode     = 0x02;
    NKAbsEncCdf_Reg.bit.FrameCode    = 0x00;
    NKAbsEncCdf_Reg.bit.EA           = EncoderID;
    NKAbsEncCdf_Reg.bit.CC           = Cmd;
    NKAbsEncCdf_Reg.bit.NKCRC        = 0x00;
	
    temp = (NKAbsEncCdf_Reg.all & 0x1FF8) >> 3;   //CRC Message:FrameCode + EA + CC;
    NKAbsEncCdf_Reg.bit.NKCRC = getFCS(temp, 0x0B);  //calculation crc
    
    FPGA_Write((6 + AxisID*16), NKAbsEncCdf_Reg.all);
}

/****************************************************************************************************
 * DESCRIPTION:
 *        config MDF and write it to TxREG2, TxREG3, or TxREG4
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void NikonSetMdf(UINT16 FrameCode, UINT16 Data, UINT16 AxisID)
{
    UINT16 temp = 0x00;
    
    NKAbsEncMdf_Reg.bit.SyncCode    = 0x02;
    NKAbsEncMdf_Reg.bit.FrameCode   = FrameCode;
    NKAbsEncMdf_Reg.bit.DA          = Data;
    NKAbsEncMdf_Reg.bit.NKCRC       = 0x00;
	
    temp = (NKAbsEncMdf_Reg.all & 0x1FF8) >> 3;   //CRC Message:FrameCode + DA;
    NKAbsEncMdf_Reg.bit.NKCRC = getFCS(temp, 0x0B);  //calculation crc
    
    FPGA_Write((6 + AxisID*16), NKAbsEncMdf_Reg.all);
}

