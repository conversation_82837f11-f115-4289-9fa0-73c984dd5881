/****************************************************************************************************
 *
 * FILE NAME:  TaskCMain.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.06.11
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	11-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Global.h"
#include "TaskCMain.h"
#include "Mlib.h"
#include "HardApi.h"
#include "CheckAlarm.h"

#include "ecatslv.h"

extern PUBLIC INT32 CiA402_StopControl(TCiA402Axis *Cia402Axis, BOOL MotStop);
extern PUBLIC BOOL Cia402_StateMachine(BASE_LOOP *BaseLoops, BOOL PowerOn);
extern PUBLIC void CpxJatOfflineCalc( JATHNDL *JatHdl, INT32 UnMotSpdx, DETVIB	*DetVib, 
														BOOL PConActFlag, BOOL PerrClrReq );
PRIVATE void SyscUpdateBaseEnableStatus( BE_SEQ_HNDL *BeSeq, INT32 AxisNo,
                                       SEQ_CTRL_OUT *SeqCtrlOut, BOOL PowerOn, BOOL faultState)	;

PRIVATE void    GlobalAlmCheck(void);
extern UINT16 Sync0CNT;
extern UINT16 IRQCNT;
/****************************************************************************************************
 * DESCRIPTION:  
 *		 Initialize Servo Sequence Variables 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void TskCInitServoSequence( AXIS_HANDLE *AxisC )
{
	BE_SEQ_HNDL 	*BeSeqData;
	BeSeqData = AxisC->BaseSeq;

	MlibResetLongMemory( &(BeSeqData->AlmStop.V), sizeof( BeSeqData->AlmStop.V )/4 );
//	MlibResetLongMemory( &(BeSeqData->OtSeqData.var), sizeof( BeSeqData->OtSeqData.var )/4 );
	MlibResetLongMemory( &(BeSeqData->BkSeqData.V), sizeof( BeSeqData->BkSeqData.V )/4 );
	MlibResetLongMemory( &(BeSeqData->DbSeqData), sizeof( BeSeqData->DbSeqData )/4 );
//	MlibResetLongMemory( &(BeSeqData->ZcCtrlData.f),	
//				( sizeof( BeSeqData->ZcCtrlData ) - sizeof( BeSeqData->ZcCtrlData.P ))/4 );
	MlibResetLongMemory( &(BeSeqData->ChargePump), sizeof( BeSeqData->ChargePump )/4 );

	/* Reset Servo Sequence Special Variables */
	AxisC->SeqCtrlOut->CtrlModeReq.dw = CTRL_MODE_NOCMD;
//	AxisRscC->CheckAlm->faultState |= SHAL_FAULT_BB;	
	BeSeqData->DbOn = TRUE;
	BeSeqData->SvonEnable = TRUE;


}


/****************************************************************************************************
 * DESCRIPTION:
 *		Kernel Input Service for TaskC
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void TskcInputSeqStatus( AXIS_HANDLE *AxisRscC )
{
	BASE_CTRL_OUT		*BaseCtrlOut;
	SEQ_CTRL_OUT		*SeqCtrlOut;

	BaseCtrlOut = AxisRscC->BaseCtrlOut;
	SeqCtrlOut = AxisRscC->SeqCtrlOut;

	/* Update Motor status for Sequence modules */
	SMotSts_InputMotorStatus(AxisRscC->SeqMotSts,
	                         BaseCtrlOut,
	                         SeqCtrlOut,
	                         AxisRscC->BaseLoops->Bprm);


}


/****************************************************************************************************
 * DESCRIPTION:  Alarm detections 
 *
 * RETURNS:
 *
****************************************************************************************************/
UINT16 ClearFlagEtherCAT=0;
PRIVATE void GlobalAlmCheck(void)
{
	BOOL Almtag = 0;
	
	Almtag |=  ALMCheckGlobalAlarm( ALM_OV );
	Almtag |=  ALMCheckGlobalAlarm( ALM_PUV );
	Almtag |=  ALMCheckGlobalAlarm( ALM_WIR );
	Almtag |=  ALMCheckGlobalAlarm( ALM_VDC );
	Almtag |=	ALMCheckGlobalAlarm( ALM_CONVIO );
	Almtag |=	ALMCheckGlobalAlarm( ALM_RG );
	Almtag |=	ALMCheckGlobalAlarm( ALM_RGOL );
	
//	if(Almtag )
//	{
//		HAL_GPIO_WritePin(GPIOI,GPIO_PIN_9,GPIO_PIN_SET);
//	}
//	else
//	{
//		HAL_GPIO_WritePin(GPIOI,GPIO_PIN_9,GPIO_PIN_RESET);
//	}
}
/****************************************************************************************************
 * DESCRIPTION:  Alarm detections 
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void TskcAlarmDetections( AXIS_HANDLE *AxisC )
{
	INT32  			  SvStatus;
	ALARM 		      *AlmMngr;
	BE_SEQ_HNDL		  *BeSeqData;
	BASE_CTRL_OUT	  *BaseCtrlOut;
	CHECK_ALARM		  *CheckAlarm;
    BASE_CTRL         *BaseCtrls;
	
	AlmMngr = AxisC->AlmMngr;
	BeSeqData = AxisC->BaseSeq;
	BaseCtrlOut = AxisC->BaseCtrlOut;
	CheckAlarm = AxisC->CheckAlm;
    BaseCtrls  = AxisC->BaseCtrls;
    
    AdetCheckAdOffset(AlmMngr, AxisC->AxisID);

	LosaPhaseCheckALM(AlmMngr, AxisC->AxisID);
    
//    AdetCheckSwOc( AlmMngr, AxisC->BaseLoops->CurLoop.V.IqFdb, AxisC->BaseLoops->Bprm->MaxCur, AxisC->BaseLoops->Bprm->RatCur);

    if(BaseCtrls->CtrlCmdMngr.CtrlMode != BASE_MODE_VF)
    {
        AdetCheckOverSpeed(AlmMngr, &CheckAlarm->OvrSpd, AxisC->BaseLoops->SpdFdb, AxisC->BaseLoops->Bprm);
    }
    
    AdetCheckOverLoadAmpMot( AlmMngr,
                            CheckAlarm,
                            AxisC->CtrlLoopOut->IdFbMon,
                            AxisC->CtrlLoopOut->IqFbMon,
                            AxisC->BaseLoops->SpdObsFdb,
                            AxisC->BaseLoops->Enc->V.EncConnect );  
   
	/* Amplifier & motor overload check processing*/
	if(BeSeqData->BeReqSeqOut)
	{	
		if(BaseCtrlOut->CtrlModeOut.b.cm != BASE_MODE_TRQ 
								&& MlibABS(BaseCtrlOut->SpdCtrlRef) >= 2*CheckAlarm->MotStall.StallSpdL)
		{
			AdetCheckMotStall(AlmMngr, &CheckAlarm->MotStall, AxisC->CtrlLoopOut->IqFbMon, AxisC->BaseLoops->SpdObsFdb );
		}
		else if(BaseCtrlOut->CtrlModeOut.b.cm == BASE_MODE_TRQ)
		{
			AdetCheckMotStall(AlmMngr, &CheckAlarm->MotStall, AxisC->CtrlLoopOut->IqFbMon, AxisC->BaseLoops->SpdObsFdb );
		}

	}
    
    // if((BeSeqData->BeReqSeqOut == TRUE)
    //         &&( bEscStateTrans==OP_2_SAFEOP || bEscStateTrans==OP_2_PREOP || bEscStateTrans==OP_2_INIT) )
    // {
    //     ALMSetGlobalAlarm(ALM_ECAT_Break);                
    // }    

//          AxisC->BaseLoops->DebugVar1 = bEscStateTrans;
//        AxisC->BaseLoops->DebugVar2 = AxisC->Prm->PnMoniPrm.DLControl;
//        AxisC->BaseLoops->DebugVar3 = AxisC->Prm->PnMoniPrm.DLStatus;

	AdetCheckMotorOverrun( AlmMngr, &(CheckAlarm->OvrRun),
							  AxisC->SeqMotSts->var.MotSpd,
							  AxisC->CtrlLoopOut->TrqRefMon,
							  AxisC->BaseCtrls->BaseEnable,
									AxisC->BaseCtrls->CtrlCmdMngr.CtrlMode);
	//						  CheckAlarm->faultState & SHAL_FAULT_BB);

//	if( (BaseCtrlOut->CtrlModeOut.b.cm != BASE_MODE_POS) ||
//		((BeSeqData->DbSeqData.DbBeReq == FALSE) || (BeSeqData->BeReqSeqOut == TRUE)) )
//	{
//		ALMClearWarning( AlmMngr, WRN_BEOF );	
//	}
//	else
//	{
//		AdetCheckSvonPerrOverFlow( AlmMngr, &(CheckAlarm->OvrPosErr), BaseCtrlOut->PositionError );
//	}

	if( (BaseCtrlOut->CtrlModeOut.b.cm == BASE_MODE_POS) &&
		((BaseCtrls->LastCmdEnable == FALSE) && (BaseCtrls->CmdEnable == TRUE)) )
	{	
        AdetCheckSvonPerrOverFlow( AlmMngr, &(CheckAlarm->OvrPosErr), BaseCtrlOut->PositionError );
	}
	else
	{
        ALMClearWarning( AlmMngr, WRN_BEOF );
	}  

    BaseCtrls->LastCmdEnable =  BaseCtrls->CmdEnable; 
    

	if(BeSeqData->BeReqSeqOut == TRUE)
	{
		/* Over position error check processing */
		AdetCheckPerrOverFlow( AlmMngr,
							  &(CheckAlarm->OvrPosErr),
							  (BASE_MODE_POS == BaseCtrlOut->CtrlModeOut.b.cm),
							  BaseCtrlOut->PositionError,
							  BaseCtrlOut->SvonSpdLmtReq );
        AdetCheckPosFollow( AlmMngr,
							  &(CheckAlarm->PosFollowErr),
							  (BASE_MODE_POS == BaseCtrlOut->CtrlModeOut.b.cm),
							  BaseCtrlOut->PositionError, AxisC->BaseCtrls->Cia402Axis.Objects);
	}
	
#if 1
	#if NO_ADVFUNC
	/* Detect Vibration */
	if( BeSeqData->BeReqSeqOut == FALSE )
	{ 
		ALMClearWarning( AlmMngr, WRN_VIB );

		DetVibResetRequest( AxisC->DetVib );
		DetVibLowFreqResetRequest( AxisC->DetVib );
	}
	else
	{ 
		SvStatus = DetVibDetectVibration( AxisC->DetVib );
		if( SvStatus & DETECT_VIB_ALARM )
		{
			ALMSetServoAlarm( AlmMngr, ALM_VIB );
			ALMClearWarning( AlmMngr, WRN_VIB );
		}
		else if( SvStatus & DETECT_VIB_WARNING )
		{
			ALMSetServoAlarm( AlmMngr, WRN_VIB );
		}
		else
		{
			ALMClearWarning( AlmMngr, WRN_VIB );
		}

		SvStatus = DetVibDetectLowFreqVibration( AxisC->DetVib );
		if( (SvStatus & DETECT_VIB_ALARM)
			&& (AxisC->BaseCtrls->TuneLessCtrl.var.TuneLessAct == TRUE) )
		{ 
			ALMSetServoAlarm( AlmMngr, ALM_AT );
		}
	}
	#endif
#endif

    AlmMngr->AlmRst = AxisC->BaseCtrls->CtrlCmdBit.bits.ClrAlm;
    AxisC->BaseCtrls->CtrlCmdBit.bits.ClrAlm = 0;
    ALMRuntimeProcess( AlmMngr, BeSeqData->AlmStop.P.StopMode);

	if(AlmMngr->Status.AlmFlag && !AlmMngr->AlmRst)
	{
		Cia402_LocalError(&AxisC->BaseCtrls->Cia402Axis, AlmMngr->Status.AlmCode, AlmMngr->Status.StopMode);
	}
	
	
	if(MCU_DEVICE_ID == MCU_DEVICE_ID_0)
	{
		GlobalAlmCheck();
	}
	else
	{
//		if(HAL_GPIO_ReadPin(GPIOI,GPIO_PIN_9))
//		{
//			if(ALMCheckGlobalAlarm(ALM_AIX1_GL) == FALSE)
//			{
//				ALMSetGlobalAlarm(ALM_AIX1_GL);				
//			}		
//		}
	}
}

/****************************************************************************************************
 * DESCRIPTION:  
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PTPMotorCtrl(AXIS_HANDLE *AxisC )
{
        if(AxisC->Prm->PnAuxPrm.PTPStart == 2)
        {          
            if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 0)
            {
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 6;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 1;
                
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos1 = AxisC->Prm->PnAdvPrm.ClampingJawForLit;   
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos2 = AxisC->Prm->PnAdvPrm.ClampingJawRevLit;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos = AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos1;
            }
            else if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 1)
            {
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 7;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 2;
            }
            else if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 2)
            {
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 15;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 3;
            }
            else if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 3)
            {
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 63;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 5;
            }
            else if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 4)
            {
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Mark = 1;
                if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos == AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos1)
                {
                    AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos = AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos2;                   
                }
                else
                {
                    AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos = AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos1;
                }
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 15;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 3;
            }
        }
        else if(AxisC->Prm->PnAuxPrm.PTPStart == 1)
        {          
            if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 0)
            {
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 6;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 1;
                
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos2 = AxisC->Prm->PnAdvPrm.ClampingJawForLit;   
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos1 = AxisC->Prm->PnAdvPrm.ClampingJawRevLit;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos = AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos1;
            }
            else if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 1)
            {
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 7;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 2;
            }
            else if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 2)
            {
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 15;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 3;
            }
            else if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 3)
            {
                AxisC->Prm->PnCia402Prm.Controlword0x6040 = 63;
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 5;
            }
            else if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep == 4)
            {
                AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Mark = 1;
                if(AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos == AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos1)
                {
                    AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos = AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos2;                              
                    AxisC->Prm->PnCia402Prm.Controlword0x6040 = 15;
                    AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 3;
                }
                else
                {
                    AxisC->Prm->PnAuxPrm.PTPStart = 0;
                     AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.TargetPos = AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.Pos1;        
                    AxisC->Prm->PnCia402Prm.Controlword0x6040 = 15;
                    AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 3;
                }
            }
        }
        else if(AxisC->Prm->PnAuxPrm.PTPStart == 3)
        {
              AxisC->Prm->PnCia402Prm.Controlword0x6040 = 0;
              AxisC->BaseCtrls->Cia402Axis.PTP_Ctrl.AxisEnableStep = 0;
              AxisC->Prm->PnAuxPrm.PTPStart = 0;
        }
        
       
}

#pragma optimize=none
PUBLIC void TskcServoOnCtrl( AXIS_HANDLE *AxisC )
{
	BOOL 				servo_on = FALSE;
	PHASE_FIND		    *PhsFnd = &AxisC->MotorIdentify->FhaseFind;
        
            CIA402_EXTRA_WORD   *ExtraCtrl= &AxisC->BaseCtrls->Cia402Axis.ExtraCtrl;
            PTPMotorCtrl(AxisC);
	
	CiA402_StopControl(&AxisC->BaseCtrls->Cia402Axis, AxisC->SeqCtrlOut->MotStop);
        
        ExtraCtrl->bit.FstopIn = 1;
        ExtraCtrl->bit.FnSvonReq = AxisC->FnCmnCtrl->FnSvonReq;
        ExtraCtrl->bit.BeReqSeqoff = AxisC->BaseSeq->BeReqSeqOff;
	
	servo_on = Cia402_StateMachine(AxisC->BaseLoops, 
		                           AxisC->PowMngr->PowerSts.PowerOn );

	if(servo_on != FALSE)  // power on
	{
		if(PhsFnd->V.FFStart ||
			(!AxisC->BaseLoops->Enc->V.PhaseReady && PhsFnd->P.FFMethod == FF_METHOD_FIRST_EN))
		{
			// phase find
    		PhaseFindExec(&AxisC->MotorIdentify->FhaseFind, AxisC->BaseLoops->Enc);
		}
		else if(!AxisC->BaseLoops->Enc->V.PhaseReady)
		{
			ALMSetServoAlarm( AxisC->AlmMngr, ALM_PHSFND );// Alarm
		}
	}
	
}



/****************************************************************************************************
 * DESCRIPTION:  
 *		Update status of Base Servo Sequence
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void SyscUpdateBaseEnableStatus( BE_SEQ_HNDL *BeSeq, INT32 AxisNo,
                                       SEQ_CTRL_OUT *SeqCtrlOut, BOOL PowerOn, BOOL faultState)											
{
	BOOL	BeReqSeqOut;

/*--------------------------------------------------------------------------------------------------*/
/*		Set Main Power Status																		*/
/*--------------------------------------------------------------------------------------------------*/
	SeqCtrlOut->MainPowerOn = PowerOn;

	BeReqSeqOut = BeSeq->BeReqSeqOut;
        
        
            if(( BeSeq->BeReqSeqOut == 0) && ( BeSeq->BeReqSeqOutLast == 1))
    {
       BeSeq->BeReqSeqOff = 1;
    }else if(( BeSeq->BeReqSeqOut == 1) && ( BeSeq->BeReqSeqOutLast == 0))
    {
       BeSeq->BeReqSeqOff = 0;
    }
    BeSeq->BeReqSeqOutLast = BeSeq->BeReqSeqOut ;


	if( BeSeq->BkSeqData.V.BkBeReq == FALSE )
	{
		SeqCtrlOut->BBSvcRefClrReq = TRUE;				/* ServoOnComp = FALSE */
	}
//	else if( BeReqSeqOut || (!BeSeq->OtSeqData.var.OtBeReq) )
	else if( BeReqSeqOut )
	{
		SeqCtrlOut->BBSvcRefClrReq = FALSE;				/* ServoOnComp = TRUE */
	}

/*--------------------------------------------------------------------------------------------------*/
/*		Set Sequence Control Output Data															*/
/*--------------------------------------------------------------------------------------------------*/
	SeqCtrlOut->BaseEnableReq = BeSeq->BeReqSeqOut;

	SeqCtrlOut->BeComplete	= BeSeq->BeReqSeqOut;

	SeqCtrlOut->SvRdyPhaseIgnore = BeSeq->SvReadyPhaseIgnore;
	
//	SeqCtrlOut->UdlBeStatus = ((faultState & SHAL_FAULT_BB) == 0) ? TRUE : FALSE;
}


PRIVATE UINT16 PosLimitMask_Sort(AXIS_HANDLE *AxisC)
{
	UINT16 ret ; 
    HOME_MODE_CTRL			*pHOME_Ctrl = &AxisC->BaseCtrls->Cia402Axis.HOME_Ctrl;
	if(AxisC->BaseCtrls->CtrlModeSet.CtrlMode.b.cm != CTRL_MODE_HOME)
	{
		ret =  0;
		return ret;
	}
	
   switch(pHOME_Ctrl->Method)
   {
     case 1:
     case 11:
     case 12:
     case 13:
     case 14:
     case 17:
     case 27:
     case 28:
     case 29:
     case 30:
       ret = 1;  // negtive limit used
       break;
     case 2:
     case 7:
     case 8:
     case 9:
     case 10:
     case 18:
     case 23:
     case 24:
     case 25:
     case 26:
       ret = 2;
       break;   
	 default:
	 	ret = 0;
	 break;   
   }
	
    return ret;
}

/****************************************************************************************************
 * DESCRIPTION:  
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void TskcBaseDriveSequence( AXIS_HANDLE *AxisC )
{
	BE_SEQ_HNDL   *BaseSeq = AxisC->BaseSeq;
	POWER_MNG     *PowMngr = AxisC->PowMngr;
	BOOL          ready_cond;
	BOOL          ComSvonReq;
    BOOL          DiSvonReq;
	BOOL		  spot, snot;
	UINT16        LimitMask = 0;
	BOOL          spmask,snmask;
	
	ready_cond = (PowMngr->PowerSts.PowerOn)& (AxisC->AlmMngr->AlmReady) & ( !(AxisC->SeqCtrlOut->FstpSts))  ; 

    
//    if(AxisC->BaseCtrls->Cia402Axis.DiServoOnMask)
//    {
//      //todo
//      ComSvonReq = AxisC->BaseCtrls->Cia402Axis.Cia402BeReq & AxisC->BaseCtrls->Cia402Axis.DiServoOn;
//    }
//    else 
    {
        if(AxisC->BaseCtrls->Cia402Axis.VirtualAxisEn)
        {
            ComSvonReq = 0;
        }
        else
        {
            ComSvonReq = AxisC->BaseCtrls->Cia402Axis.Cia402BeReq;
        }
    }
      
	BeSeqMakeServoReadySignal(BaseSeq, ready_cond, AxisC->BaseLoops->Enc->V.PhaseReady);

	BeSeqMakeServoOnRequest(BaseSeq, ComSvonReq, AxisC->FnCmnCtrl ,AxisC->AlmMngr);

	BeSeqInputAlmStopMode( BaseSeq, 
							 AxisC->AlmMngr->Status.StopMode,	
							 AxisC->BaseLoops->CtrlMode,
							 AxisC->BaseLoops->Enc->V.PhaseReady,
							 AxisC->SeqCtrlOut->MotStop);


//	BeSeqForceStopMode( BaseSeq,   AxisC->BaseLoops->CtrlMode, 
//		                           AxisC->SeqCtrlOut->MotStop, AxisC->SeqCtrlOut->FstpSts );

	//todo: FALSE should be changed
	BeSeqMechaBrakeSequence(BaseSeq, AxisC->BaseLoops->CtrlMode, AxisC->SeqMotSts->var.AbsMotSpd, FALSE);

	/* Set OT Torque Limit Request */

//    LimitMask = PosLimitMask_Sort(AxisC);
//
//	if(LimitMask ==1)
//	{	
//		snot = 0;
//	}else if(LimitMask ==2)
//	{
//		 spot = 0;
//	}
//    
//	AxisC->SeqCtrlOut->OverTrvlSts = BeSeqDetectOTStatus( &BaseSeq->OtSeqData,
//	                                               AxisC->BaseCtrlOut,
//	                                               spot,		/* <S045> */
//	                                               snot);		/* <S045> */
	/* OT */
	BeSeqOverTravelSequence(&BaseSeq->OtSeqData,
							AxisC->BaseCtrlOut->CtrlModeOut.b.cm,
							AxisC->SeqCtrlOut->MotStop,
							AxisC->SeqCtrlOut->BeComplete,						/* <S1F5> */
							BaseSeq->LdstpTimeZero);     			/* <S1F5> */
	/* Set OT Torque Limit Request */
	AxisC->SeqCtrlOut->TrqLimitReq = BaseSeq->OtSeqData.var.OtTrqLimit		/* <S1F5> */
							| BaseSeq->AlmStop.V.Gr2TrqLimit		/* <S1F5> */
							| BaseSeq->ForceStop.V.FstpTrqLimit;		/* <S1F5> */
	/* Set OT Warning */
	if( BaseSeq->OtSeqData.var.OtWarningDet == TRUE )
	{	
		ALMSetServoAlarm( AxisC->AlmMngr, WRN_POSLIMIT );
	}
	else
	{
		ALMClearWarning( AxisC->AlmMngr, WRN_POSLIMIT );
	}
  //  AxisC->SeqCtrlOut->TrqLimitReq = BaseSeq->AlmStop.V.Gr2TrqLimit | BaseSeq->ForceStop.V.FstpTrqLimit;	

	BeSeqStopModeManager(BaseSeq);


	BOOL OCState = ALMCheckEachState(AxisC->AlmMngr, ALM_OC);
	BeSeqChargePumpControl(&BaseSeq->ChargePump, OCState, PowMngr->PowerSts.NegPumpReq, AxisC->AxisID );

	BeSeqDynamicBrakeSequence(BaseSeq, AxisC->SeqCtrlOut->MotStop, 
								 AxisC->AlmMngr->Status.MencDataNG, AxisC->AxisID );

//	if(MCU_DEVICE_ID_0==MCU_DEVICE_ID)
//	{
		PcmRlyControlProcess( PowMngr, BaseSeq->DbOn, BaseSeq->BkSeqData.V.BkBeReq, AxisC->AxisID );
//	}
			 
	#if NO_DIDO
    SysGetDOState(AxisC); 
	#endif

	AxisC->SeqCtrlOut->CtrlModeReq.dw = BeSeqControlModeManager(BaseSeq, AxisC->FnCmnCtrl);

	/* Sequence state update*/
	SyscUpdateBaseEnableStatus( BaseSeq,
								  AxisC->AxisID,
								  AxisC->SeqCtrlOut,
								  AxisC->PowMngr->PowerSts.PowerOn,
								  FALSE );	

}

/****************************************************************************************************
 * DESCRIPTION:  
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void TskcSequenceMain( AXIS_HANDLE *AxisC )
{
	BASE_CTRL		*BaseCtrlData;
	BE_SEQ_HNDL		*BeSequence;

#if 0
	static UINT32 LockCnt = 0;

	if(LockCnt++ > 4000*3600*2)  
	{
		ALMSetGlobalAlarm( ALM_ISR_TIME );
		hApi_BaseBlock(AxisC->AxisID);
		return;
	}
#endif

	BaseCtrlData =AxisC->BaseCtrls;
	BeSequence = AxisC->BaseSeq;

	if(BaseCtrlData->CtrlModeSet.CtrlMode.b.cm == CTRL_MODE_PJOG && BaseCtrlData->PJogHdl.PJogV.Var.PnPrgJogFlg == TRUE)
	{
		CpiPrgJogLoopExec(AxisC);
	}

    AxisC->MotorIdentify->InertiaIdent.V.PosValue = AxisC->Prm->PnCia402Prm.PositionActualValue0x6064;
	/* Program JOG sequence */
	CpxPrgJogPosSequence(     &BaseCtrlData->PJogHdl,
							  AxisC->SeqCtrlOut,
							  AxisC->AlmMngr,
							  AxisC->FnCmnCtrl->FnCtrlMcmd ); 

#if  1	
	#if NO_ADVFUNC
	/* Offline inertia identification */
	CpxJatOfflineCalc(	&BaseCtrlData->JatHdl,
						AxisC->SeqMotSts->var.MotSpd,
						AxisC->DetVib,
						AxisC->BaseLoops->SpdPctrlFlag,
						FALSE);
//						BaseCtrlData->CtrlCmdMngr.PerrClrReq);
	
	/* Advanced auto tuning */
	CpxAdvancedAutoTuning(	AxisC->AdatHndl,
							AxisC->FnCmnCtrl);  
    
	/* Check the RemVibCalculate Execution Condition */
	RemVibChkCondition( AxisC->RemVibFreq,
						(BASE_MODE_POS == BaseCtrlData->CtrlCmdMngr.CtrlMode) );
	/* Calculate RemVibration */
	RemVibCalculateFreq( AxisC->RemVibFreq,
	                     AxisC->BaseLoops->PosLoop.PosErr,
						 BaseCtrlData->PosCtrlSts.CoinSignal,
						 BaseCtrlData->PosCtrlSts.RefZSignal );
	#endif
#endif
	
}

/****************************************************************************************************
 * DESCRIPTION:  
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void MonitorParameter(AXIS_HANDLE *AxisC)
{
  REAL32 Tmp=0;
  
    AxisC->Prm->PnMoniPrm.Voltage =  AxisC->PowMngr->PowerSts.DcVolt;
    AxisC->Prm->PnMoniPrm.IPMCurrent = (UINT32)(AxisC->BaseLoops->Idc*1000.0f);
    AxisC->Prm->PnMoniPrm.PosRef = AxisC->BaseLoops->PosRef;
    AxisC->Prm->PnMoniPrm.PosFdb = AxisC->BaseLoops->PosFdb;
    AxisC->Prm->PnMoniPrm.PosErr = AxisC->BaseLoops->PosLoop.PosErr;
    AxisC->Prm->PnMoniPrm.PosOutput = (INT32) (AxisC->BaseLoops->PosLoop.SpdRef 
                                    /AxisC->BaseLoops->Bprm->Kmotspd);
    
       
    if(AxisC->BaseLoops->UseEncoder2)
    {
      Tmp = (INT32)(AxisC->BaseLoops->SpdLoop.V.SpdRef
                                      /AxisC->BaseLoops->Bprm->Kmotspd);
      AxisC->Prm->PnMoniPrm.SpdRef = GearRatioInvCal(AxisC->BaseLoops, Tmp);
      
      
      Tmp =  (INT32)(AxisC->BaseLoops->MotSpd1msFil
                                      /AxisC->BaseLoops->Bprm->Kmotspd);  
      
      AxisC->Prm->PnMoniPrm.SpdFdb =  GearRatioInvCal(AxisC->BaseLoops, Tmp);
      
      Tmp = (INT32)(AxisC->BaseLoops->SpdLoop.V.SpdErr
                                      /AxisC->BaseLoops->Bprm->Kmotspd);   
      
      AxisC->Prm->PnMoniPrm.SpdErr = GearRatioInvCal(AxisC->BaseLoops, Tmp);
    }
    else
    {
      AxisC->Prm->PnMoniPrm.SpdRef = (INT32)(AxisC->BaseLoops->SpdLoop.V.SpdRef
                                      /AxisC->BaseLoops->Bprm->Kmotspd);
      
      
      AxisC->Prm->PnMoniPrm.SpdFdb = (INT32)(AxisC->BaseLoops->MotSpd1msFil
                                      /AxisC->BaseLoops->Bprm->Kmotspd);  
        
      AxisC->Prm->PnMoniPrm.SpdErr = (INT32)(AxisC->BaseLoops->SpdLoop.V.SpdErr
                                      /AxisC->BaseLoops->Bprm->Kmotspd);
    }
  
    AxisC->Prm->PnMoniPrm.SpdOutput = (INT32)(AxisC->BaseLoops->SpdLoop.V.TrqRef
                                      /(AxisC->BaseLoops->Bprm->Kmottrq));
    AxisC->Prm->PnMoniPrm.TrqRef = (INT32)(AxisC->BaseLoops->TrqRef/(AxisC->BaseLoops->Bprm->Kmottrq));
    AxisC->Prm->PnMoniPrm.TrqFdb = (INT32)(AxisC->BaseLoops->CurLoop.V.IqFdb * AxisC->Prm->PnMotPrm.MotKt
                                    / AxisC->Prm->PnMotPrm.RatTrq *1000.0f);
    AxisC->Prm->PnMoniPrm.CurrentIa = (INT32)(AxisC->BaseLoops->CurLoop.V.Ia*1000.0f);
    AxisC->Prm->PnMoniPrm.CurrentIb = (INT32)(AxisC->BaseLoops->CurLoop.V.Ib*1000.0f);
    AxisC->Prm->PnMoniPrm.CurrentIc = (INT32)(AxisC->BaseLoops->CurLoop.V.Ic*1000.0f);
    AxisC->Prm->PnMoniPrm.CurrentIdRef = (INT32)(AxisC->BaseLoops->CurLoop.V.IdRef*1000.0f);
    AxisC->Prm->PnMoniPrm.CurrentIqRef = (INT32)(AxisC->BaseLoops->CurLoop.V.IqRef*1000.0f);
    AxisC->Prm->PnMoniPrm.CurrentIdFbd = (INT32)(AxisC->BaseLoops->CurLoop.V.IdFdb*1000.0f);
    AxisC->Prm->PnMoniPrm.CurrentIqFbd = (INT32)(AxisC->BaseLoops->CurLoop.V.IqFdb*1000.0f);
    AxisC->Prm->PnMoniPrm.EncMechAngle = AxisC->BaseLoops->Enc->V.MechAngle;
    AxisC->Prm->PnMoniPrm.EncElecAngle = AxisC->BaseLoops->Enc->V.ElecAngle;   
	AxisC->Prm->PnMoniPrm.AlmSet0 = AxisC->AlmMngr->AlmSet[0];
	AxisC->Prm->PnMoniPrm.AlmSet1 = AxisC->AlmMngr->AlmSet[1];
	AxisC->Prm->PnMoniPrm.AlmSet2 = AxisC->AlmMngr->AlmSet[2];
	AxisC->Prm->PnMoniPrm.AlmSet3 = AxisC->AlmMngr->AlmSet[3];
    AxisC->Prm->PnMoniPrm.ActualEnable = AxisC->BaseCtrls->BaseEnable;

	AxisC->Prm->PnCustomPrm.DyHjMaxTqrLimit = AxisC->BaseCtrls->TrqLimitData.V.IpdPosTrqLmtOut*100;
	AxisC->Prm->PnCustomPrm.DyHjMinTqrLimit = -AxisC->BaseCtrls->TrqLimitData.V.IpdPosTrqLmtOut*100;
	// AxisC->Prm->PnCustomPrm.DyHjMaxVelLimit = AxisC->BaseLoops->DyHjMaxVelLimit;
	// AxisC->Prm->PnCustomPrm.DyHjMinVelLimit = AxisC->BaseLoops->DyHjMinVelLimit;
    
    
    // AxisC->Prm->PnMoniPrm.EcatCycleTime        = (UINT32)(AxisC->EcatDate->EcatCycleTime*0.001);
    // AxisC->Prm->PnMoniPrm.EcatSync0CycleTime   = (UINT32)(AxisC->EcatDate->EcatSync0CycleTime*0.001);
    // AxisC->Prm->PnMoniPrm.EcatSmEventMissedCnt = AxisC->EcatDate->EcatSmEventMissedCnt;
    // AxisC->Prm->PnMoniPrm.EcatSyncType         = AxisC->EcatDate->EcatSyncType;  
    // AxisC->Prm->PnMoniPrm.Ecat0InFCnt         = hApi_ReadEcatErrReg(ECAT_INFECNT,0);      
    // AxisC->Prm->PnMoniPrm.Ecat0RxErrCnt       = hApi_ReadEcatErrReg(ECAT_RXFECNT,0);     
    // AxisC->Prm->PnMoniPrm.Ecat0FwdErrCnt      = hApi_ReadEcatErrReg(ECAT_FWDRFECNT,0);       
    // AxisC->Prm->PnMoniPrm.Ecat0LostLinkCnt    = hApi_ReadEcatErrReg(ECAT_LSOTECNT,0);    
    // AxisC->Prm->PnMoniPrm.Ecat1InFCnt         = hApi_ReadEcatErrReg(ECAT_INFECNT,1);         
    // AxisC->Prm->PnMoniPrm.Ecat1RxErrCnt       = hApi_ReadEcatErrReg(ECAT_RXFECNT,1);        
    // AxisC->Prm->PnMoniPrm.Ecat1FwdErrCnt      = hApi_ReadEcatErrReg(ECAT_FWDRFECNT,1);          
    // AxisC->Prm->PnMoniPrm.Ecat1LostLinkCnt    = hApi_ReadEcatErrReg(ECAT_LSOTECNT,1); 
    
    // AxisC->Prm->PnMoniPrm.EcatPuErrCnt       = hApi_ReadEcatErrReg(ECAT_PROCECNT,0);       
    // AxisC->Prm->PnMoniPrm.EcatPDIErr         = hApi_ReadEcatErrReg(ECAT_PIDECNT,0); 
    // AxisC->Prm->PnMoniPrm.EscStateTrans      = bEscStateTrans;
    // AxisC->Prm->PnMoniPrm.AlStatus           = ALStatusCode;
    // AxisC->Prm->PnMoniPrm.IRQCNT             =  IRQCNT;
    //  AxisC->Prm->PnMoniPrm.Sync0CNT             =  Sync0CNT;
    // AxisC->Prm->PnMoniPrm.DLControl             =   hApi_ReadEcatErrReg(ECAT_DLCONTROL,0);
}


/****************************************************************************************************
 * DESCRIPTION:  
 *
 * RETURNS:
 *
****************************************************************************************************/
#if NO_DIDO
PUBLIC void LedGlobleSet(AXIS_HANDLE *AxisC, UINT16 ax_id)
{
	
	if ((AxisC->BaseCtrls->Cia402Axis.State == STATE_NOT_READY_TO_SWITCH_ON) 
		||(AxisC->BaseCtrls->Cia402Axis.State == STATE_SWITCH_ON_DISABLED) 
		||(AxisC->BaseCtrls->Cia402Axis.State == STATE_READY_TO_SWITCH_ON))
	{
		if((AxisC->AlmMngr->Status.WrnFlag == 1) && AxisC->AlmMngr->LedWarn)
		{
			LED_SetStatus(SYS_INIT_SUCD_PRDON_WARN, ax_id);
		}
		else 
		{
			LED_SetStatus(SYS_INIT_SUCD_PRDON, ax_id);
		}
	}
	else if(AxisC->BaseCtrls->Cia402Axis.State == STATE_SWITCHED_ON)
	{
		LED_SetStatus(SYS_POWER_ON, ax_id);
	}
	else if(AxisC->BaseCtrls->Cia402Axis.State== STATE_OPERATION_ENABLED)
	{
		if((AxisC->AlmMngr->Status.WrnFlag == 1)&& AxisC->AlmMngr->LedWarn)
		{
			LED_SetStatus(SYS_OPERATION_ENABLED_WARN, ax_id);
		}
		else 
		{
			LED_SetStatus(SYS_OPERATION_ENABLED, ax_id);
		}
	}
	
	if(AxisC->AlmMngr->Status.AlmFlag == 1)
	{
		if (AxisC->AlmMngr->Status.AlmResetInfo == 1)
		{
			LED_SetStatus(SYS_FALUT_NOTRESET, ax_id);
		}
		else 
		{
                        LED_SetStatus(SYS_FALUT_RESET, ax_id);
		}
	}
}

/****************************************************************************************************
* DESCRIPTION:   be used in taskC
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC void    SysBaseDIDOSlow( AXIS_HANDLE *AxisRscC )
{  
    BASE_CTRL           *BaseCtrls = NULL;
    TCiA402Axis         *Cia402Axis;
	SEQ_IO_HNDL			*SequenceIO = NULL;
  
    BaseCtrls  = AxisRscC->BaseCtrls;
    SequenceIO = AxisRscC->SequenceIO;
    Cia402Axis = &(BaseCtrls->Cia402Axis);    
  

//    if((SequenceIO->CalDiFunSet >> DI_SVR_OFF) & 0x1)
//    {
//       Cia402Axis->DiServoOff = SequenceIO->V.ServoOff; 
//    }
//    
//    if((SequenceIO->CalDiFunSet >> DI_SVR_ON) & 0x1)
//    {
//      Cia402Axis->DiServoOn = SequenceIO->V.ServoOn; 
//    } 

//    if((SequenceIO->CalDiFunSet >> DI_FSTOP) & 0x1)
//    {
//      Cia402Axis->QkStop = SequenceIO->V.FstopIn; 
//    }
//    
//    if((SequenceIO->CalDiFunSet >> DI_FAULT_CLR) & 0x1)
//    {
//      Cia402Axis->AlmRstCmd = SequenceIO->V.FaultClr; 
//    } 
}   
/****************************************************************************************************
* DESCRIPTION:   be used in taskC
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC void SysGetDOState( AXIS_HANDLE *AxisC )
{
    CIA402_STATUS_WORD      StatusWord = {0};
 
    StatusWord.all = AxisC->BaseCtrls->Cia402Axis.Objects->Statusword0x6041;
     // DO state 
    if( AxisC->BaseSeq->BkSeqData.V.Brake)
    {
      AxisC->SequenceIO->DoOutsts.BrkOut = 0;
    }
    else
    {
       AxisC->SequenceIO->DoOutsts.BrkOut = 1;
    }
    
    if(AxisC->AlmMngr->Status.AlmFlag)
    {
       AxisC->SequenceIO->DoOutsts.FaultOut = 1;
    }
    else
    {
      AxisC->SequenceIO->DoOutsts.FaultOut = 0;
    }

    if(StatusWord.bit.TargetReached) 
    {
      AxisC->SequenceIO->DoOutsts.TgtRchOut = 1;
    }
    else
    {
      AxisC->SequenceIO->DoOutsts.TgtRchOut = 0;
    }
}
#endif