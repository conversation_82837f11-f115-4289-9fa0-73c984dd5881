/*
 * bsp_usb.h
 *
 *  Created on: Oct 7, 2022
 *      Author: xgj12
 */

#ifndef BSP_USB_H_
#define BSP_USB_H_


/******************************************************************************
 Macro definitions
 ******************************************************************************/
/* USB related */
#define NUM_STRING_DESCRIPTOR   (7U)
#define LINE_CODING_LENGTH      (0x07U)
#define USB_READ_SIZE           (1024)



/******************************************************************************
 User define macro definitions
 ******************************************************************************/
/** [USB module selection setting]
 *  USB_IP0             : Uses USB0 module
 *  USB_IP1             : Uses USB1 module
 */
#define USE_USBIP           (USB_IP0)

   
/** [USB connect speed setting]
 *  USB_HS              : Support Hi-Speed
 *  USB_FS              : Not suppor Hi-Speed(Full-Speed only)
 */
#define USB_SUPPORT_SPEED   (USB_FS)


/** [Select PCDC Demo sample Application mode]
 *  USB_ECHO              : Loop back(Echo) mode
 *  USB_UART              : USB Serial(VCOM) converter mode
 */
#define OPERATION_MODE  (USB_ECHO)


/** [Select Support Free RTOS]
 *  USB_APL_ENABLE        : Support Free RTOS
 *  USB_APL_DISABLE       : Not support Free RTOS(Support non OS)
 */
#define USB_SUPPORT_RTOS    (USB_APL_DISABLE)

/** [Select USE power saving control]
 *  USB_APL_ENABLE        : Use power saving control
 *  USB_APL_DISABLE       : Not use power saving control
 */
#define USB_SUPPORT_LPW    (USB_APL_DISABLE)


// 先暂时放在这个   
#define UART_RECV_MAX_NUM 1024

#define RX_GOING     0
#define RX_COMPLETE  1
#define RX_ERROR     2
   


#define USER_USB_OK      0
#define USER_USB_ERROR   1
#define USER_USB_BUYS    2
#define USER_USB_TIMEOUT 3

#define USER_USB_RX  1
#define USER_USB_TX  2




   
void bsp_usb_init(void);
uint16_t bsp_usb_cfg_detect(uint32_t timeout_ms);
uint16_t bsp_usb_receive(uint8_t *pbuff, uint16_t len,uint32_t timeout_ms);
uint16_t bsp_usb_send(uint8_t *pbuff, uint16_t len,uint32_t timeout_ms);

#endif /* BSP_USB_H_ */
