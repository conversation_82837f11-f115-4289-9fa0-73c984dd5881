/* generated configuration header file - do not edit */
#ifndef BSP_MCU_DEVICE_MEMORY_CFG_H_
#define BSP_MCU_DEVICE_MEMORY_CFG_H_
#define BSP_CFG_MPU0_READ0 (0)
      #define BSP_CFG_MPU0_WRITE0 (0)
      #define BSP_CFG_MPU0_STADD0 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD0 (0x00000C00)
      #define BSP_CFG_MPU0_READ1 (0)
      #define BSP_CFG_MPU0_WRITE1 (0)
      #define BSP_CFG_MPU0_STADD1 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD1 (0x00000C00)
      #define BSP_CFG_MPU0_READ2 (0)
      #define BSP_CFG_MPU0_WRITE2 (0)
      #define BSP_CFG_MPU0_STADD2 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD2 (0x00000C00)
      #define BSP_CFG_MPU0_READ3 (0)
      #define BSP_CFG_MPU0_WRITE3 (0)
      #define BSP_CFG_MPU0_STADD3 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD3 (0x00000C00)
      #define BSP_CFG_MPU0_READ4 (0)
      #define BSP_CFG_MPU0_WRITE4 (0)
      #define BSP_CFG_MPU0_STADD4 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD4 (0x00000C00)
      #define BSP_CFG_MPU0_READ5 (0)
      #define BSP_CFG_MPU0_WRITE5 (0)
      #define BSP_CFG_MPU0_STADD5 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD5 (0x00000C00)
      #define BSP_CFG_MPU0_READ6 (0)
      #define BSP_CFG_MPU0_WRITE6 (0)
      #define BSP_CFG_MPU0_STADD6 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD6 (0x00000C00)
      #define BSP_CFG_MPU0_READ7 (0)
      #define BSP_CFG_MPU0_WRITE7 (0)
      #define BSP_CFG_MPU0_STADD7 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD7 (0x00000C00)
      #define BSP_CFG_MPU1_READ0 (0)
      #define BSP_CFG_MPU1_WRITE0 (0)
      #define BSP_CFG_MPU1_STADD0 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD0 (0x00000C00)
      #define BSP_CFG_MPU1_READ1 (0)
      #define BSP_CFG_MPU1_WRITE1 (0)
      #define BSP_CFG_MPU1_STADD1 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD1 (0x00000C00)
      #define BSP_CFG_MPU1_READ2 (0)
      #define BSP_CFG_MPU1_WRITE2 (0)
      #define BSP_CFG_MPU1_STADD2 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD2 (0x00000C00)
      #define BSP_CFG_MPU1_READ3 (0)
      #define BSP_CFG_MPU1_WRITE3 (0)
      #define BSP_CFG_MPU1_STADD3 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD3 (0x00000C00)
      #define BSP_CFG_MPU1_READ4 (0)
      #define BSP_CFG_MPU1_WRITE4 (0)
      #define BSP_CFG_MPU1_STADD4 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD4 (0x00000C00)
      #define BSP_CFG_MPU1_READ5 (0)
      #define BSP_CFG_MPU1_WRITE5 (0)
      #define BSP_CFG_MPU1_STADD5 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD5 (0x00000C00)
      #define BSP_CFG_MPU1_READ6 (0)
      #define BSP_CFG_MPU1_WRITE6 (0)
      #define BSP_CFG_MPU1_STADD6 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD6 (0x00000C00)
      #define BSP_CFG_MPU1_READ7 (0)
      #define BSP_CFG_MPU1_WRITE7 (0)
      #define BSP_CFG_MPU1_STADD7 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD7 (0x00000C00)
      #define BSP_CFG_MPU2_READ0 (0)
      #define BSP_CFG_MPU2_WRITE0 (0)
      #define BSP_CFG_MPU2_STADD0 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD0 (0x00000C00)
      #define BSP_CFG_MPU2_READ1 (0)
      #define BSP_CFG_MPU2_WRITE1 (0)
      #define BSP_CFG_MPU2_STADD1 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD1 (0x00000C00)
      #define BSP_CFG_MPU2_READ2 (0)
      #define BSP_CFG_MPU2_WRITE2 (0)
      #define BSP_CFG_MPU2_STADD2 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD2 (0x00000C00)
      #define BSP_CFG_MPU2_READ3 (0)
      #define BSP_CFG_MPU2_WRITE3 (0)
      #define BSP_CFG_MPU2_STADD3 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD3 (0x00000C00)
      #define BSP_CFG_MPU2_READ4 (0)
      #define BSP_CFG_MPU2_WRITE4 (0)
      #define BSP_CFG_MPU2_STADD4 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD4 (0x00000C00)
      #define BSP_CFG_MPU2_READ5 (0)
      #define BSP_CFG_MPU2_WRITE5 (0)
      #define BSP_CFG_MPU2_STADD5 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD5 (0x00000C00)
      #define BSP_CFG_MPU2_READ6 (0)
      #define BSP_CFG_MPU2_WRITE6 (0)
      #define BSP_CFG_MPU2_STADD6 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD6 (0x00000C00)
      #define BSP_CFG_MPU2_READ7 (0)
      #define BSP_CFG_MPU2_WRITE7 (0)
      #define BSP_CFG_MPU2_STADD7 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD7 (0x00000C00)
      #define BSP_CFG_MPU3_READ0 (0)
      #define BSP_CFG_MPU3_WRITE0 (0)
      #define BSP_CFG_MPU3_STADD0 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU3_READ1 (0)
      #define BSP_CFG_MPU3_WRITE1 (0)
      #define BSP_CFG_MPU3_STADD1 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU3_READ2 (0)
      #define BSP_CFG_MPU3_WRITE2 (0)
      #define BSP_CFG_MPU3_STADD2 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU3_READ3 (0)
      #define BSP_CFG_MPU3_WRITE3 (0)
      #define BSP_CFG_MPU3_STADD3 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU3_READ4 (0)
      #define BSP_CFG_MPU3_WRITE4 (0)
      #define BSP_CFG_MPU3_STADD4 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU3_READ5 (0)
      #define BSP_CFG_MPU3_WRITE5 (0)
      #define BSP_CFG_MPU3_STADD5 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU3_READ6 (0)
      #define BSP_CFG_MPU3_WRITE6 (0)
      #define BSP_CFG_MPU3_STADD6 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU3_READ7 (0)
      #define BSP_CFG_MPU3_WRITE7 (0)
      #define BSP_CFG_MPU3_STADD7 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU4_READ0 (0)
      #define BSP_CFG_MPU4_WRITE0 (0)
      #define BSP_CFG_MPU4_STADD0 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU4_READ1 (0)
      #define BSP_CFG_MPU4_WRITE1 (0)
      #define BSP_CFG_MPU4_STADD1 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU4_READ2 (0)
      #define BSP_CFG_MPU4_WRITE2 (0)
      #define BSP_CFG_MPU4_STADD2 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU4_READ3 (0)
      #define BSP_CFG_MPU4_WRITE3 (0)
      #define BSP_CFG_MPU4_STADD3 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU4_READ4 (0)
      #define BSP_CFG_MPU4_WRITE4 (0)
      #define BSP_CFG_MPU4_STADD4 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU4_READ5 (0)
      #define BSP_CFG_MPU4_WRITE5 (0)
      #define BSP_CFG_MPU4_STADD5 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU4_READ6 (0)
      #define BSP_CFG_MPU4_WRITE6 (0)
      #define BSP_CFG_MPU4_STADD6 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU4_READ7 (0)
      #define BSP_CFG_MPU4_WRITE7 (0)
      #define BSP_CFG_MPU4_STADD7 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU6_READ0 (0)
      #define BSP_CFG_MPU6_WRITE0 (0)
      #define BSP_CFG_MPU6_STADD0 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD0 (0x00000C00)
      #define BSP_CFG_MPU6_READ1 (0)
      #define BSP_CFG_MPU6_WRITE1 (0)
      #define BSP_CFG_MPU6_STADD1 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD1 (0x00000C00)
      #define BSP_CFG_MPU6_READ2 (0)
      #define BSP_CFG_MPU6_WRITE2 (0)
      #define BSP_CFG_MPU6_STADD2 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD2 (0x00000C00)
      #define BSP_CFG_MPU6_READ3 (0)
      #define BSP_CFG_MPU6_WRITE3 (0)
      #define BSP_CFG_MPU6_STADD3 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD3 (0x00000C00)
      #define BSP_CFG_MPU6_READ4 (0)
      #define BSP_CFG_MPU6_WRITE4 (0)
      #define BSP_CFG_MPU6_STADD4 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD4 (0x00000C00)
      #define BSP_CFG_MPU6_READ5 (0)
      #define BSP_CFG_MPU6_WRITE5 (0)
      #define BSP_CFG_MPU6_STADD5 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD5 (0x00000C00)
      #define BSP_CFG_MPU6_READ6 (0)
      #define BSP_CFG_MPU6_WRITE6 (0)
      #define BSP_CFG_MPU6_STADD6 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD6 (0x00000C00)
      #define BSP_CFG_MPU6_READ7 (0)
      #define BSP_CFG_MPU6_WRITE7 (0)
      #define BSP_CFG_MPU6_STADD7 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD7 (0x00000C00)
      #define BSP_CFG_MPU7_READ0 (0)
      #define BSP_CFG_MPU7_WRITE0 (0)
      #define BSP_CFG_MPU7_STADD0 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU7_READ1 (0)
      #define BSP_CFG_MPU7_WRITE1 (0)
      #define BSP_CFG_MPU7_STADD1 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU7_READ2 (0)
      #define BSP_CFG_MPU7_WRITE2 (0)
      #define BSP_CFG_MPU7_STADD2 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU7_READ3 (0)
      #define BSP_CFG_MPU7_WRITE3 (0)
      #define BSP_CFG_MPU7_STADD3 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU7_READ4 (0)
      #define BSP_CFG_MPU7_WRITE4 (0)
      #define BSP_CFG_MPU7_STADD4 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU7_READ5 (0)
      #define BSP_CFG_MPU7_WRITE5 (0)
      #define BSP_CFG_MPU7_STADD5 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU7_READ6 (0)
      #define BSP_CFG_MPU7_WRITE6 (0)
      #define BSP_CFG_MPU7_STADD6 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU7_READ7 (0)
      #define BSP_CFG_MPU7_WRITE7 (0)
      #define BSP_CFG_MPU7_STADD7 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU8_READ0 (0)
      #define BSP_CFG_MPU8_WRITE0 (0)
      #define BSP_CFG_MPU8_STADD0 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU8_READ1 (0)
      #define BSP_CFG_MPU8_WRITE1 (0)
      #define BSP_CFG_MPU8_STADD1 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU8_READ2 (0)
      #define BSP_CFG_MPU8_WRITE2 (0)
      #define BSP_CFG_MPU8_STADD2 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU8_READ3 (0)
      #define BSP_CFG_MPU8_WRITE3 (0)
      #define BSP_CFG_MPU8_STADD3 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU8_READ4 (0)
      #define BSP_CFG_MPU8_WRITE4 (0)
      #define BSP_CFG_MPU8_STADD4 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU8_READ5 (0)
      #define BSP_CFG_MPU8_WRITE5 (0)
      #define BSP_CFG_MPU8_STADD5 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU8_READ6 (0)
      #define BSP_CFG_MPU8_WRITE6 (0)
      #define BSP_CFG_MPU8_STADD6 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU8_READ7 (0)
      #define BSP_CFG_MPU8_WRITE7 (0)
      #define BSP_CFG_MPU8_STADD7 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD7 (0x00000000)

      #define BSP_CFG_CPU_MPU_ATTR0_TYPE        (BSP_TYPE_NORMAL_MEMORY)
      #define BSP_CFG_CPU_MPU_ATTR0_INNER       (BSP_WRITE_BACK_NON_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR0_INNER_READ  (BSP_READ_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR0_INNER_WRITE (BSP_WRITE_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR0_OUTER       (BSP_WRITE_BACK_NON_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR0_OUTER_READ  (BSP_READ_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR0_OUTER_WRITE (BSP_WRITE_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR0_DEVICE_TYPE (BSP_DEVICE_NGNRNE)

      #define BSP_CFG_CPU_MPU_ATTR1_TYPE        (BSP_TYPE_NORMAL_MEMORY)
      #define BSP_CFG_CPU_MPU_ATTR1_INNER       (BSP_WRITE_NON_THROUGH)
      #define BSP_CFG_CPU_MPU_ATTR1_INNER_READ  (BSP_READ_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR1_INNER_WRITE (BSP_WRITE_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR1_OUTER       (BSP_WRITE_NON_THROUGH)
      #define BSP_CFG_CPU_MPU_ATTR1_OUTER_READ  (BSP_READ_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR1_OUTER_WRITE (BSP_WRITE_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR1_DEVICE_TYPE (BSP_DEVICE_NGNRNE)

      #define BSP_CFG_CPU_MPU_ATTR2_TYPE        (BSP_TYPE_NORMAL_MEMORY)
      #define BSP_CFG_CPU_MPU_ATTR2_INNER       (BSP_WRITE_NON_THROUGH)
      #define BSP_CFG_CPU_MPU_ATTR2_INNER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR2_INNER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR2_OUTER       (BSP_WRITE_NON_THROUGH)
      #define BSP_CFG_CPU_MPU_ATTR2_OUTER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR2_OUTER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR2_DEVICE_TYPE (BSP_DEVICE_NGNRNE)

      #define BSP_CFG_CPU_MPU_ATTR3_TYPE        (BSP_TYPE_NORMAL_MEMORY)
      #define BSP_CFG_CPU_MPU_ATTR3_INNER       (BSP_NON_CACHEABLE)
      #define BSP_CFG_CPU_MPU_ATTR3_INNER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR3_INNER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR3_OUTER       (BSP_NON_CACHEABLE)
      #define BSP_CFG_CPU_MPU_ATTR3_OUTER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR3_OUTER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR3_DEVICE_TYPE (BSP_DEVICE_NGNRNE)

      #define BSP_CFG_CPU_MPU_ATTR4_TYPE        (BSP_TYPE_DEVICE_MEMORY)
      #define BSP_CFG_CPU_MPU_ATTR4_INNER       (BSP_WRITE_THROUGH_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR4_INNER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR4_INNER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR4_OUTER       (BSP_WRITE_THROUGH_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR4_OUTER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR4_OUTER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR4_DEVICE_TYPE (BSP_DEVICE_NGNRNE)

      #define BSP_CFG_CPU_MPU_ATTR5_TYPE        (BSP_TYPE_DEVICE_MEMORY)
      #define BSP_CFG_CPU_MPU_ATTR5_INNER       (BSP_WRITE_THROUGH_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR5_INNER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR5_INNER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR5_OUTER       (BSP_WRITE_THROUGH_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR5_OUTER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR5_OUTER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR5_DEVICE_TYPE (BSP_DEVICE_NGNRE)

      #define BSP_CFG_CPU_MPU_ATTR6_TYPE        (BSP_TYPE_DEVICE_MEMORY)
      #define BSP_CFG_CPU_MPU_ATTR6_INNER       (BSP_WRITE_THROUGH_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR6_INNER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR6_INNER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR6_OUTER       (BSP_WRITE_THROUGH_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR6_OUTER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR6_OUTER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR6_DEVICE_TYPE (BSP_DEVICE_NGRE)

      #define BSP_CFG_CPU_MPU_ATTR7_TYPE        (BSP_TYPE_DEVICE_MEMORY)
      #define BSP_CFG_CPU_MPU_ATTR7_INNER       (BSP_WRITE_THROUGH_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR7_INNER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR7_INNER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR7_OUTER       (BSP_WRITE_THROUGH_TRANSIENT)
      #define BSP_CFG_CPU_MPU_ATTR7_OUTER_READ  (BSP_READ_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR7_OUTER_WRITE (BSP_WRITE_NOT_ALLOCATE)
      #define BSP_CFG_CPU_MPU_ATTR7_DEVICE_TYPE (BSP_DEVICE_GRE)

      /* Region00 : ATCM */
      #define BSP_CFG_EL1_MPU_REGION00_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION00_LIMIT     (0x0001FFFF)
      #define BSP_CFG_EL1_MPU_REGION00_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION00_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION00_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION00_ATTRINDEX (BSP_ATTRINDEX3)
      #define BSP_CFG_EL1_MPU_REGION00_ENABLE    (BSP_REGION_ENABLE)

      /* Region01 : BTCM */
      #define BSP_CFG_EL1_MPU_REGION01_BASE      (0x00100000)
      #define BSP_CFG_EL1_MPU_REGION01_LIMIT     (0x0011FFFF)
      #define BSP_CFG_EL1_MPU_REGION01_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION01_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION01_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION01_ATTRINDEX (BSP_ATTRINDEX3)
      #define BSP_CFG_EL1_MPU_REGION01_ENABLE    (BSP_REGION_ENABLE)

      /* Region02 : System RAM */
      #define BSP_CFG_EL1_MPU_REGION02_BASE      (0x10000000)
      #define BSP_CFG_EL1_MPU_REGION02_LIMIT     (0x1017FFFF)
      #define BSP_CFG_EL1_MPU_REGION02_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION02_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION02_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION02_ATTRINDEX (BSP_ATTRINDEX1)
      #define BSP_CFG_EL1_MPU_REGION02_ENABLE    (BSP_REGION_ENABLE)

      /* Region03 : Mirror area of System RAM */
      #define BSP_CFG_EL1_MPU_REGION03_BASE      (0x30000000)
      #define BSP_CFG_EL1_MPU_REGION03_LIMIT     (0x3017FFFF)
      #define BSP_CFG_EL1_MPU_REGION03_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION03_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION03_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION03_ATTRINDEX (BSP_ATTRINDEX3)
      #define BSP_CFG_EL1_MPU_REGION03_ENABLE    (BSP_REGION_ENABLE)

      /* Region04 : Mirror area of external address space */
      #define BSP_CFG_EL1_MPU_REGION04_BASE      (0x40000000)
      #define BSP_CFG_EL1_MPU_REGION04_LIMIT     (0x5FFFFFFF)
      #define BSP_CFG_EL1_MPU_REGION04_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION04_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION04_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION04_ATTRINDEX (BSP_ATTRINDEX3)
      #define BSP_CFG_EL1_MPU_REGION04_ENABLE    (BSP_REGION_ENABLE)

      /* Region05 : External address space */
      #define BSP_CFG_EL1_MPU_REGION05_BASE      (0x60000000)
      #define BSP_CFG_EL1_MPU_REGION05_LIMIT     (0x7FFFFFFF)
      #define BSP_CFG_EL1_MPU_REGION05_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION05_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION05_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION05_ATTRINDEX (BSP_ATTRINDEX1)
      #define BSP_CFG_EL1_MPU_REGION05_ENABLE    (BSP_REGION_ENABLE)

      /* Region06 : Non-Safety Peripheral */
      #define BSP_CFG_EL1_MPU_REGION06_BASE      (0x80000000)
      #define BSP_CFG_EL1_MPU_REGION06_LIMIT     (0x80FFFFFF)
      #define BSP_CFG_EL1_MPU_REGION06_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION06_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION06_XN        (BSP_EXECUTE_NEVER)
      #define BSP_CFG_EL1_MPU_REGION06_ATTRINDEX (BSP_ATTRINDEX5)
      #define BSP_CFG_EL1_MPU_REGION06_ENABLE    (BSP_REGION_ENABLE)

      /* Region07 : Safety Peripheral */
      #define BSP_CFG_EL1_MPU_REGION07_BASE      (0x81000000)
      #define BSP_CFG_EL1_MPU_REGION07_LIMIT     (0x81FFFFFF)
      #define BSP_CFG_EL1_MPU_REGION07_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION07_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION07_XN        (BSP_EXECUTE_NEVER)
      #define BSP_CFG_EL1_MPU_REGION07_ATTRINDEX (BSP_ATTRINDEX5)
      #define BSP_CFG_EL1_MPU_REGION07_ENABLE    (BSP_REGION_ENABLE)

      /* Region08 : LLPP Peripheral */
      #define BSP_CFG_EL1_MPU_REGION08_BASE      (0x90000000)
      #define BSP_CFG_EL1_MPU_REGION08_LIMIT     (0x901FFFFF)
      #define BSP_CFG_EL1_MPU_REGION08_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION08_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION08_XN        (BSP_EXECUTE_NEVER)
      #define BSP_CFG_EL1_MPU_REGION08_ATTRINDEX (BSP_ATTRINDEX5)
      #define BSP_CFG_EL1_MPU_REGION08_ENABLE    (BSP_REGION_ENABLE)

      /* Region09 : GIC0 */
      #define BSP_CFG_EL1_MPU_REGION09_BASE      (0x94000000)
      #define BSP_CFG_EL1_MPU_REGION09_LIMIT     (0x941FFFFF)
      #define BSP_CFG_EL1_MPU_REGION09_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION09_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION09_XN        (BSP_EXECUTE_NEVER)
      #define BSP_CFG_EL1_MPU_REGION09_ATTRINDEX (BSP_ATTRINDEX4)
      #define BSP_CFG_EL1_MPU_REGION09_ENABLE    (BSP_REGION_ENABLE)

      /* Region10 : Debug Private */
      #define BSP_CFG_EL1_MPU_REGION10_BASE      (0xC0000000)
      #define BSP_CFG_EL1_MPU_REGION10_LIMIT     (0xC0FFFFFF)
      #define BSP_CFG_EL1_MPU_REGION10_SH        (BSP_OUTER_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION10_AP        (BSP_EL1RW_EL0RW)
      #define BSP_CFG_EL1_MPU_REGION10_XN        (BSP_EXECUTE_NEVER)
      #define BSP_CFG_EL1_MPU_REGION10_ATTRINDEX (BSP_ATTRINDEX4)
      #define BSP_CFG_EL1_MPU_REGION10_ENABLE    (BSP_REGION_ENABLE)

      /* Region11 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION11_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION11_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION11_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION11_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION11_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION11_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION11_ENABLE    (BSP_REGION_DISABLE)

      /* Region12 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION12_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION12_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION12_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION12_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION12_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION12_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION12_ENABLE    (BSP_REGION_DISABLE)

      /* Region13 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION13_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION13_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION13_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION13_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION13_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION13_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION13_ENABLE    (BSP_REGION_DISABLE)

      /* Region14 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION14_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION14_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION14_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION14_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION14_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION14_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION14_ENABLE    (BSP_REGION_DISABLE)

      /* Region15 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION15_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION15_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION15_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION15_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION15_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION15_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION15_ENABLE    (BSP_REGION_DISABLE)

      /* Region16 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION16_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION16_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION16_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION16_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION16_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION16_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION16_ENABLE    (BSP_REGION_DISABLE)

      /* Region17 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION17_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION17_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION17_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION17_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION17_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION17_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION17_ENABLE    (BSP_REGION_DISABLE)

      /* Region18 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION18_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION18_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION18_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION18_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION18_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION18_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION18_ENABLE    (BSP_REGION_DISABLE)

      /* Region19 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION19_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION19_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION19_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION19_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION19_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION19_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION19_ENABLE    (BSP_REGION_DISABLE)

      /* Region20 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION20_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION20_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION20_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION20_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION20_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION20_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION20_ENABLE    (BSP_REGION_DISABLE)

      /* Region21 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION21_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION21_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION21_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION21_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION21_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION21_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION21_ENABLE    (BSP_REGION_DISABLE)

      /* Region22 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION22_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION22_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION22_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION22_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION22_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION22_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION22_ENABLE    (BSP_REGION_DISABLE)

      /* Region23 : Not Used */
      #define BSP_CFG_EL1_MPU_REGION23_BASE      (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION23_LIMIT     (0x00000000)
      #define BSP_CFG_EL1_MPU_REGION23_SH        (BSP_NON_SHAREABLE)
      #define BSP_CFG_EL1_MPU_REGION23_AP        (BSP_EL1RW_EL0NO)
      #define BSP_CFG_EL1_MPU_REGION23_XN        (BSP_EXECUTE_ENABLE)
      #define BSP_CFG_EL1_MPU_REGION23_ATTRINDEX (BSP_ATTRINDEX0)
      #define BSP_CFG_EL1_MPU_REGION23_ENABLE    (BSP_REGION_DISABLE)

      #define BSP_CFG_SCTLR_BR_BIT               (BSP_BG_REGION_DISABLE)
      #define BSP_CFG_SCTLR_I_BIT                (BSP_ICACHE_ENABLE)
      #define BSP_CFG_SCTLR_C_BIT                (BSP_DATACACHE_ENABLE)
#endif /* BSP_MCU_DEVICE_MEMORY_CFG_H_ */
