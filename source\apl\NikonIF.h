/****************************************************************************************************
 *
 * FILE NAME:  NikonIF.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.09.09
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	09-09-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _NIKON_IF_H_
#define _NIKON_IF_H_

#include "BaseDef.h"

#include "Basedef.h"
#include "alarm.h"
#include "Encoder.h"

#define NIKON_ABSENC_CF0          0x00       //ABS full 40 bits data request
#define NIKON_ABSENC_CF1          0x01       //ABS lower 24 bits data request
#define NIKON_ABSENC_CF2          0x02       //ABS upper 24 bits data request
#define NIKON_ABSENC_CF3          0x03       //encoder status request
#define NIKON_ABSENC_CF8          0x08       //clear status flag request
#define NIKON_ABSENC_CF9          0x09       //set encoder address request
#define NIKON_ABSENC_CF10         0x0A       //zero preset single turn data request 
#define NIKON_ABSENC_CF11         0x0B       //read eeprom request
#define NIKON_ABSENC_CF12         0x0C       //write eeprom request
#define NIKON_ABSENC_CF13         0x0D       //temperature data request
#define NIKON_ABSENC_CF14         0x0E       //read ID code I request
#define NIKON_ABSENC_CF15         0x0F       //read ID code II request
#define NIKON_ABSENC_CF16         0x10       //write ID code I request
#define NIKON_ABSENC_CF17         0x11       //write ID code II request
#define NIKON_ABSENC_CF18         0x12       //ABS lower 17 bits data request
#define NIKON_ABSENC_CF19         0x13       //ABS lower 24 bits data request + status request
#define NIKON_ABSENC_CF21         0x15       //ABS lower 24 bits data request + temperature data request
#define NIKON_ABSENC_CF27         0x1B       //ABS full 40 bits data request
#define NIKON_ABSENC_CF29         0x1D       //ABS lower 24 bits data request

#define NIKON_ABSENC_MF0          0x01       //frame code = MF0
#define NIKON_ABSENC_MF1          0x02       //frame code = MF1
#define NIKON_ABSENC_MF2          0x03       //frame code = MF2

//CDF 
typedef struct
{
    UINT16 SyncCode:3;  
    UINT16 FrameCode:2;   
    UINT16 EA:3; 
    UINT16 CC:5; 
    UINT16 NKCRC:3;    
}STR_NKABSENCCDF_BIT;


typedef union
{
    volatile UINT16                     all;
    volatile STR_NKABSENCCDF_BIT        bit;
}UNI_NKABSENCCDF_REG;

//MDF
typedef struct
{
	UINT16 SyncCode:3;      
    UINT16 FrameCode:2;        
	UINT16 DA:8;        
	UINT16 NKCRC:3;            
}STR_NKABSENCMDF_BIT;

typedef union
{
    volatile UINT16                     all;
    volatile STR_NKABSENCMDF_BIT        bit;
}UNI_NKABSENCMDF_REG;

//IF(CDF0-CDF20)
typedef struct
{   
    UINT16 SyncCode:3;        
    UINT16 EAX:3;      
    UINT16 CC:5;
    UINT16 fix:1;
	UINT16 BusyStatus:1;  //BUSY+MEMBUSY
    UINT16 BATT:1;        //BATT      
	UINT16 OSMEMOT:1;     //OVSPD+MEMERR+OVTEMP     
	UINT16 STPSMT:1;      //STERR+PSERR+MTERR                                  
}STR_NKABSENCIF_BIT;

typedef union
{
    volatile UINT16                     all;
    volatile STR_NKABSENCIF_BIT         bit;
}UNI_NKABSENCIF_REG;

//IF(CDF21-CDF22)
typedef struct
{   
    UINT16 SyncCode:3;        
    UINT16 EAX:3;      
    UINT16 ES:1;
    UINT16 Data:9;                                
}STR_NKABSENCIFO_BIT;

typedef union
{
    volatile UINT16                     all;
    volatile STR_NKABSENCIFO_BIT        bit;
}UNI_NKABSENCIFO_REG;


typedef struct
{
	UINT16 BATT :1;      
    UINT16 MTERR:1;        
	UINT16 Rsvd2:1;        
	UINT16 OVSPD:1;        

    UINT16 MEMERR:1;       
    UINT16 STERR:1;       
	UINT16 PSERR:1;
	UINT16 BUSY:1;           

	UINT16 MEMBUSY:1;
	UINT16 OVTEMP:1;
	UINT16 Rsvd10:1;      
	UINT16 Rsvd11:1;

	UINT16 Rsvd12:1;
	UINT16 Rsvd13:1;      
	UINT16 Rsvd14:1;      
	UINT16 Rsvd15:1;      
}STR_NKABSENCALM_BIT;

typedef union
{
    volatile UINT16                     all;
    volatile STR_NKABSENCALM_BIT        bit;
}UNI_NKABSENCALM_REG;


PUBLIC void NikonSetCdf(UINT16 Cmd, UINT16 EncoderID, UINT16 AxisID);
PUBLIC void NikonSetMdf(UINT16 FrameCode, UINT16 Data, UINT16 AxisID);





#endif  // end #ifndef _NIKON_IF_H_

