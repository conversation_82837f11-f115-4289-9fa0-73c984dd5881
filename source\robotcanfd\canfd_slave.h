/**
 * @file canfd_slave.h
 * @brief CANFD从站功能模块
 * @details 实现从站数据接收解析和发送打包功能
 */

#ifndef CANFD_SLAVE_H
#define CANFD_SLAVE_H

#include "canfd_protocol.h"
#include "r_can_api.h"


/**
 * @brief 从站初始化配置结构体
 */
typedef struct {
    uint8_t slaveAddress;        ///< 从站地址(1-31)
    uint8_t ComStatus;           ///< 通信状态
    void *canfdInterface;        ///< CANFD接口句柄，具体类型依赖于底层驱动
} CANFD_SlaveConfig;


/**
 * @brief 错误码定义
 */
typedef enum {
    CANFD_OK = 0,                ///< 成功
    CANFD_ERROR = -1,            ///< 一般错误
    CANFD_INVALID_PARAM = -2,    ///< 参数无效
    CANFD_NOT_INITIALIZED = -3,  ///< 未初始化
    CANFD_NO_CALLBACK = -4,      ///< 回调函数未注册
    CANFD_TX_FAILED = -5,        ///< 发送失败
    CANFD_RX_ERROR = -6,         ///< 接收错误
    CANFD_NONEED_RESPONSE = -7,  ///< 无需响应
} CANFD_Error;


/**
 * @brief Command codes
 */
#define CMD_DEVICE_STATUS     0x01 /**< Device status command */
#define CMD_DEVICE_VERSION    0x05 /**< Device version command */
#define CMD_FIRMWARE_UPDATE   0x06 /**< Firmware update command */
#define CMD_MOTOR_CONTROL     0x21 /**< Motor control command */
#define CMD_OP_DICTIONARY     0x24 /**< Operation dictionary command */


/**
 * @brief Master Id
 */

#define CANFD_MASTER_ID  0x01


/**
 * @brief 初始化从站
 * 
 * @param config 从站配置信息
 * @return CANFD_Error 错误码
 */
CANFD_Error CANFD_SlaveInit(CANFD_SlaveConfig *config);

/**
 * @brief 处理接收到的CANFD帧
 * 
 * @param frame 接收到的CANFD帧
 * @return CANFD_Error 错误码
 */
CANFD_Error CANFD_ProcessRxFrame(CANFD_Frame *frame);

/**
 * @brief 发送电机状态信息
 * 
 * @return CANFD_Error 错误码
 */
CANFD_Error CANFD_ParkResponseData(CANFD_Frame *frame, CANFD_Frame *rxframe, uint8_t type) ;

/**
 * @brief 注册电机控制回调函数
 * 
 * @param callback 回调函数指针
 * @return CANFD_Error 错误码
 */
CANFD_Error CANFD_RegisterControlCallback(CANFD_MotorControlCallback_t callback);



/**
 * @brief 解包CANFD帧头部
 * 
 * @param frame 输出：CANFD帧

 */
void UnPackFrameHeader(CANFD_Frame *frame, can_frame_t * p_frame) ;


/**
 * @brief 打包CANFD帧头部
 * 
 * @param frame 输入：CANFD帧
 * @param buffer 输出：CANFD帧头部

 */
void PackFrameHeader(CANFD_Frame *frame, uint8_t *buffer);



/**
 * @brief 获取当前时间
 * 
 * @return uint32_t 当前时间
 */
uint32_t Canfd_GetTimeMs(void); 


/**
 * @brief 检查超时
 * 
 * @return uint8_t 返回状态
 */
uint8_t Canfd_TimeoutCheck(uint32_t timeout, uint32_t lastTime);

/**
 * @brief Initialize the firmware update module
 *
 * @return Status_t Status code
 */
void firmware_init(void);


/**
 * @brief 打包固件数据请求
 * 
 * @param frame 输出：CANFD帧
 */
void firmware_pack_data_request(CANFD_Frame *frame);

/**
 * @brief 发送固件状态响应
 * 
 * @param frame 输出：CANFD帧
 */
void firmware_status_response(CANFD_Frame *frame);












#define ARM_FIRMWARE_CODE   0x0B
#define LEG_FIRMWARE_CODE   0x0C


#define CANFD_OTA_BUFFER_SIZE 1024
#define CANFD_OAT_MAX_RDATE   59



#define WAITING_FIRMWARE_INFO 1
#define WAITING_FIRMWARE_DATA 2


/**
* @brief Firmware information structure
*/
typedef struct {
   uint8_t device_id;           /**< Target device ID */
   uint8_t firmware_type;       /**< Firmware type */
   uint32_t firmware_size;      /**< Total firmware size */
   uint16_t firmware_version;   /**< Firmware version */
} FirmwareInfo_t;


/* Firmware update state */
typedef struct {
    struct
    {
        uint32_t timeout_ms;
        uint8_t  max_chunk_size;
        uint8_t  max_retries;   
        uint8_t  device_id;       
        uint32_t firmware_size;       
        uint8_t  firmware_type; 
    }conf;

    struct
    {
        uint8_t  update_in_progress;
        uint32_t current_offset;
        uint32_t buffrxdatenum;
        uint32_t allrxdatenum;
        uint8_t  bufferIndx;
        uint8_t  packet_id;
        FirmwareStatus_t status;
        uint32_t last_request_time;
        uint8_t  waiting_for_response;
        uint8_t  retry_count;
        uint8_t  chunk_size; 
        uint8_t  step;
    }var;   
} Updatestate_t;



typedef struct {
    struct {
        int32_t  targetPosCnt;   //轴端目标位置 cnt
        int32_t  targetVelCnts;  //轴端目标速度 cnt/s
        uint16_t accelerationTime; //加速度时间
        float    targetTorque;    //轴端目标转矩 Nm
        float    torqueFeedffNm;  //轴端目标转矩 Nm
        float    targetPosRad;    //轴端目标位置 rad
        float    targetVelRads;   //轴端目标速度 rad/s            
        float    positionKp;      //轴端位置比例系数
        float    velocityKd;      //轴端速度微分系数
        uint8_t  controlMode;     //控制模式
        uint16_t runCommand;      //运行命令
    } CovCtrlCmd;

    struct {
        float Max_Postion;      ///< 轴端最大位置 rad
        float Min_Postion;      ///< 轴端最小位置 rad
        float Max_Velocity;     ///< 轴端最大速度 rad/s
        float Min_Velocity;     ///< 轴端最小速度 rad/s
        float Max_Torque;       ///< 轴端最大转矩 Nm
        float Min_Torque;       ///< 轴端最小转矩 Nm
        float RefMaxCur;      ///< 轴端最大电流 A
        float RefMinCur;      ///< 轴端最小电流 A
        float RefMaxVol;      ///< 轴端最大电压 V
        float PosiontQ2Cnt;     ///< 位置转换系数 减速机端 cnt
        float VelocityQ2Cnts;    ///< 速度转换系数 减速机端 cnt/s
        float TorqueQ2Nm;       ///< 转矩转换系数 减速机端 Nm

        float TorqueCxNm;      ///< 转矩转换系数 电机端 Nm
        float PosiontQ2Rad;     ///< 位置转换系数 电机端 rad
        float VelocityQ2Rads;    ///< 速度转换系数



        uint16_t CanFdBreakTime;      ///< 超时阈值ms
        uint16_t CanFdBreakEnable;    ///< 超时使能   
        float    IpdPosNLmit;        ///< 位置负限位 rad
        float    IpdPosPLmit;        ///< 位置正限位 rad
        float    IpdKpMax;           ///< 导纳控制最大Kp
        float    IpdKdMax;           ///< 导纳控制最大Kd
    } Prm;

    
    struct {
        uint8_t  CanBreakEnable;
        uint8_t  UserEnable;
        uint8_t  Enable;
        uint8_t  CanBreakFlag;
        uint32_t Count;
    } Timeout;

    CANFD_MotorStatus statusData;
    
    CANFD_MotorControl controlData;
}HUMAN_ROBOT_CTRL;



extern Updatestate_t s_update_state;
extern CANFD_SlaveConfig config;
extern HUMAN_ROBOT_CTRL HumanRotCtrl;


// extern FirmwareStorageConfig_t s_storage_config;



#endif /* CANFD_SLAVE_H */ 