/****************************************************************************************************
 *
 * FILE NAME:  FnSetTunelessLvl.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.06.30
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	30-06-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FNSET_TUNELESS_LVL_H_
#define _FNSET_TUNELESS_LVL_H_
	
#include "BaseLoops.h"
#include "RegAccessIf.h"

/****************************************************************************************************/
/*																									*/
/*		Constant Definition																			*/
/*																									*/
/****************************************************************************************************/
#define		DAT_GAINLEVEL_MAX			7		// Tuneless maximum gain level					
#define		DAT_GAINLEVEL_MIN			0		// Tuneless minimum gain level				
#define		DAT_JRATLEVEL_MAX			2		// Tuneless inertia level maximum value		
#define		DAT_JRATLEVEL_MIN			0		// Tuneless inertia level minimum value		
/*--------------------------------------------------------------------------------------------------*/
#define		DAT_SEQ_INIT				0		// Initial processing									
#define		DAT_SEQ_TUNING				1		// Gain level tuneless processing					
#define		DAT_SEQ_END					10		// End processing						


/****************************************************************************************************/
/*																									*/
/*		Function Prototype Definition																*/
/*																									*/
/****************************************************************************************************/
PUBLIC void        RpiRegSetTuneLessLevel( UINT32 Gain, TUNELESS_CTRL *TuneLessCtrl );
PUBLIC PRM_RSLT    RpiFunSetDATGainLevel( TUNELESS_CTRL *TuneLessCtrl, UINT16 Level );
PUBLIC PRM_RSLT    RpiFunSetDATJratLevel( TUNELESS_CTRL *TuneLessCtrl, UINT16 Level );



#endif

