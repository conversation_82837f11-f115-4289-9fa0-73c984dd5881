__var val;

__param ATCM_ADDR = 0x00000000;
__param ATCM_SIZE = 0x00020000;

CR52_400MHz()
{
	__message "==== Set Clock: 400MHz ====\n";
  __writeMemory32(0x0000A50F, 0x81281A00, "Memory"); // set PRCRS
  __writeMemory32(0x0000A50F, 0x80281A10, "Memory"); // set PRCRN
  
	__writeMemory32(0x00000015, 0x81280004, "Memory");	// set SCKCR2

  __writeMemory32(0x0000A500, 0x81281A00, "Memory"); // set PRCRS
  __writeMemory32(0x0000A500, 0x80281A10, "Memory"); // set PRCRN

}

__spibus_setup()
{
  __writeMemory32(0x0000A50F, 0x81281A00, "Memory"); // set PRCRS
  __writeMemory32(0x0000A50F, 0x80281A10, "Memory"); // set PRCRN

  // Enable xSPI module
  __writeMemory32(0x00001F01,0x80280300, "Memory");  //  MSTPCRA

  // port Pin setup

  // P17_7 -> XSPI1_CKP    PFC177 = 7,PMC177 = 1,ASELP177 = 1
  // P16_7 -> XSPI1_IO0    PFC167 = 2,PMC167 = 1,ASELP167 = 1
  // P17_0 -> XSPI1_IO1    PFC170 = 2,PMC170 = 1,ASELP170 = 1
  // P17_3 -> XSPI1_IO2    PFC173 = 7,PMC173 = 1,ASELP173 = 1
  // P17_4 -> XSPI1_IO3    PFC174 = 7,PMC174 = 1,ASELP174 = 1
  // P18_2 -> XSPI1_CS0#   PFC182 = 5,PMC182 = 1,ASELP182 = 1
 __writeMemory8(0x80,       0x81030C10, "Memory"); // set ASELP16:
 __writeMemory8(0x99,       0x81030C11, "Memory"); // set ASELP17:
 __writeMemory8(0x04,       0x81030C12, "Memory"); // set ASELP18:
 __writeMemory32(0x20000000,0x800A0640, "Memory"); // set PFC16;
 __writeMemory32(0x70077002,0x800A0644, "Memory"); // set PFC17;
 __writeMemory32(0x00000500,0x800A0648, "Memory"); // set PFC18;
 __writeMemory8(0x80,       0x800A0410, "Memory"); // set PMC16;
 __writeMemory8(0x99,       0x800A0411, "Memory"); // set PMC17;
 __writeMemory8(0x04,       0x800A0412, "Memory"); // set PMC18;

  __writeMemory32(0x0000A500, 0x81281A00, "Memory"); // set PRCRS
  __writeMemory32(0x0000A500, 0x80281A10, "Memory"); // set PRCRN

}

enable_BTCM()
{
	__message "Enable BTCM\n";

	val = __jtagCP15ReadReg(9, 1, 0, 1);
	val |= BTCM_ADDR;		// Base address
	val |= 1<<1;			// Enable at EL2
	val |= 1<<0;			// Enable at EL1 and EL0
	__jtagCP15WriteReg(9, 1, 0, 1, val);
}

clear_BTCM()
{
	__message "Clear BTCM\n";

	__fillMemory32(0, BTCM_ADDR, "Memory", BTCM_SIZE/4, "Copy");
}

clear_ATCM()
{
	__message "Clear ATCM\n";

	__fillMemory32(0, ATCM_ADDR, "Memory", ATCM_SIZE/4, "Copy");
}

execUserPreload() 
{

    __message "Executing execUserPreload() function";
    __hwReset(0);

    __delay(10);

    execute_InitialFunctions();

    __message "FINISH Executing execUserPreload() function";

}

execUserReset() 
{

    __message "Executing execUserReset() function";

    // Execute boot sequence and stop at 0x00802000(start of loader program)
//    __hwResetRunToBp(2, 0x0802000, 10000);
    __hwResetRunToBp(2, 0x0102200, 10);

    __message "FINISH Executing execUserReset() function";

}


execute_InitialFunctions()
{
  CR52_400MHz();
  __spibus_setup();
}

