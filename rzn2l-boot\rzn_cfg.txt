FSP Configuration
  Board "RZN2L Custom User Board (xSPI1 x1 boot mode)"
    Parameter information for the loader: CACHE_FLG: 0x00000000
    Parameter information for the loader: WRAPCFG_V: 0x00000000
    Parameter information for the loader: COMCFG_V: 0x00000000
    Parameter information for the loader: BMCFG_V: 0x00000000
    Parameter information for the loader: xSPI_FLG: 0x00000000
    Parameter information for the loader: LDR_ADDR_NML: 0x6800004C
    Parameter information for the loader: LDR_SIZE_NML: 0x00010000
    Parameter information for the loader: DEST_ADDR_NML: 0x00102000
    Parameter information for the loader: CSSCTL_V: 0x0000003F
    Parameter information for the loader: LIOCFGCS0_V: 0x00070000
    Parameter information for the loader: ACCESS_SPEED: 0x00000600
    Parameter information for the loader: CHECK_SUM: Auto Calculate.
    
  R9A07G084M04GBG
    part_number: R9A07G084M04GBG
    atcm_size_bytes: 131072
    btcm_size_bytes: 131072
    system_ram_size_bytes: 1572864
    package_style: FBGA
    package_pins: 225
    Cortex-R52 CPU core: CPU0
    Master MPU: MPU0 : DMAC Unit0: Enable or disable read control for Region 0: Disabled
    Master MPU: MPU0 : DMAC Unit0: Enable or disable write control for Region 0: Disabled
    Master MPU: MPU0 : DMAC Unit0: Region 0 Start: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Region 0 End: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Enable or disable read control for Region 1: Disabled
    Master MPU: MPU0 : DMAC Unit0: Enable or disable write control for Region 1: Disabled
    Master MPU: MPU0 : DMAC Unit0: Region 1 Start: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Region 1 End: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Enable or disable read control for Region 2: Disabled
    Master MPU: MPU0 : DMAC Unit0: Enable or disable write control for Region 2: Disabled
    Master MPU: MPU0 : DMAC Unit0: Region 2 Start: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Region 2 End: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Enable or disable read control for Region 3: Disabled
    Master MPU: MPU0 : DMAC Unit0: Enable or disable write control for Region 3: Disabled
    Master MPU: MPU0 : DMAC Unit0: Region 3 Start: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Region 3 End: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Enable or disable read control for Region 4: Disabled
    Master MPU: MPU0 : DMAC Unit0: Enable or disable write control for Region 4: Disabled
    Master MPU: MPU0 : DMAC Unit0: Region 4 Start: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Region 4 End: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Enable or disable read control for Region 5: Disabled
    Master MPU: MPU0 : DMAC Unit0: Enable or disable write control for Region 5: Disabled
    Master MPU: MPU0 : DMAC Unit0: Region 5 Start: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Region 5 End: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Enable or disable read control for Region 6: Disabled
    Master MPU: MPU0 : DMAC Unit0: Enable or disable write control for Region 6: Disabled
    Master MPU: MPU0 : DMAC Unit0: Region 6 Start: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Region 6 End: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Enable or disable read control for Region 7: Disabled
    Master MPU: MPU0 : DMAC Unit0: Enable or disable write control for Region 7: Disabled
    Master MPU: MPU0 : DMAC Unit0: Region 7 Start: 0x00000000
    Master MPU: MPU0 : DMAC Unit0: Region 7 End: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Enable or disable read control for Region 0: Disabled
    Master MPU: MPU1 : DMAC Unit1: Enable or disable write control for Region 0: Disabled
    Master MPU: MPU1 : DMAC Unit1: Region 0 Start: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Region 0 End: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Enable or disable read control for Region 1: Disabled
    Master MPU: MPU1 : DMAC Unit1: Enable or disable write control for Region 1: Disabled
    Master MPU: MPU1 : DMAC Unit1: Region 1 Start: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Region 1 End: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Enable or disable read control for Region 2: Disabled
    Master MPU: MPU1 : DMAC Unit1: Enable or disable write control for Region 2: Disabled
    Master MPU: MPU1 : DMAC Unit1: Region 2 Start: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Region 2 End: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Enable or disable read control for Region 3: Disabled
    Master MPU: MPU1 : DMAC Unit1: Enable or disable write control for Region 3: Disabled
    Master MPU: MPU1 : DMAC Unit1: Region 3 Start: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Region 3 End: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Enable or disable read control for Region 4: Disabled
    Master MPU: MPU1 : DMAC Unit1: Enable or disable write control for Region 4: Disabled
    Master MPU: MPU1 : DMAC Unit1: Region 4 Start: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Region 4 End: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Enable or disable read control for Region 5: Disabled
    Master MPU: MPU1 : DMAC Unit1: Enable or disable write control for Region 5: Disabled
    Master MPU: MPU1 : DMAC Unit1: Region 5 Start: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Region 5 End: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Enable or disable read control for Region 6: Disabled
    Master MPU: MPU1 : DMAC Unit1: Enable or disable write control for Region 6: Disabled
    Master MPU: MPU1 : DMAC Unit1: Region 6 Start: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Region 6 End: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Enable or disable read control for Region 7: Disabled
    Master MPU: MPU1 : DMAC Unit1: Enable or disable write control for Region 7: Disabled
    Master MPU: MPU1 : DMAC Unit1: Region 7 Start: 0x00000000
    Master MPU: MPU1 : DMAC Unit1: Region 7 End: 0x00000000
    Master MPU: MPU2 : GMAC: Enable or disable read control for Region 0: Disabled
    Master MPU: MPU2 : GMAC: Enable or disable write control for Region 0: Disabled
    Master MPU: MPU2 : GMAC: Region 0 Start: 0x00000000
    Master MPU: MPU2 : GMAC: Region 0 End: 0x00000000
    Master MPU: MPU2 : GMAC: Enable or disable read control for Region 1: Disabled
    Master MPU: MPU2 : GMAC: Enable or disable write control for Region 1: Disabled
    Master MPU: MPU2 : GMAC: Region 1 Start: 0x00000000
    Master MPU: MPU2 : GMAC: Region 1 End: 0x00000000
    Master MPU: MPU2 : GMAC: Enable or disable read control for Region 2: Disabled
    Master MPU: MPU2 : GMAC: Enable or disable write control for Region 2: Disabled
    Master MPU: MPU2 : GMAC: Region 2 Start: 0x00000000
    Master MPU: MPU2 : GMAC: Region 2 End: 0x00000000
    Master MPU: MPU2 : GMAC: Enable or disable read control for Region 3: Disabled
    Master MPU: MPU2 : GMAC: Enable or disable write control for Region 3: Disabled
    Master MPU: MPU2 : GMAC: Region 3 Start: 0x00000000
    Master MPU: MPU2 : GMAC: Region 3 End: 0x00000000
    Master MPU: MPU2 : GMAC: Enable or disable read control for Region 4: Disabled
    Master MPU: MPU2 : GMAC: Enable or disable write control for Region 4: Disabled
    Master MPU: MPU2 : GMAC: Region 4 Start: 0x00000000
    Master MPU: MPU2 : GMAC: Region 4 End: 0x00000000
    Master MPU: MPU2 : GMAC: Enable or disable read control for Region 5: Disabled
    Master MPU: MPU2 : GMAC: Enable or disable write control for Region 5: Disabled
    Master MPU: MPU2 : GMAC: Region 5 Start: 0x00000000
    Master MPU: MPU2 : GMAC: Region 5 End: 0x00000000
    Master MPU: MPU2 : GMAC: Enable or disable read control for Region 6: Disabled
    Master MPU: MPU2 : GMAC: Enable or disable write control for Region 6: Disabled
    Master MPU: MPU2 : GMAC: Region 6 Start: 0x00000000
    Master MPU: MPU2 : GMAC: Region 6 End: 0x00000000
    Master MPU: MPU2 : GMAC: Enable or disable read control for Region 7: Disabled
    Master MPU: MPU2 : GMAC: Enable or disable write control for Region 7: Disabled
    Master MPU: MPU2 : GMAC: Region 7 Start: 0x00000000
    Master MPU: MPU2 : GMAC: Region 7 End: 0x00000000
    Master MPU: MPU3 : USB Host: Enable or disable read control for Region 0: Disabled
    Master MPU: MPU3 : USB Host: Enable or disable write control for Region 0: Disabled
    Master MPU: MPU3 : USB Host: Region 0 Start: 0x00000000
    Master MPU: MPU3 : USB Host: Region 0 End: 0x00000000
    Master MPU: MPU3 : USB Host: Enable or disable read control for Region 1: Disabled
    Master MPU: MPU3 : USB Host: Enable or disable write control for Region 1: Disabled
    Master MPU: MPU3 : USB Host: Region 1 Start: 0x00000000
    Master MPU: MPU3 : USB Host: Region 1 End: 0x00000000
    Master MPU: MPU3 : USB Host: Enable or disable read control for Region 2: Disabled
    Master MPU: MPU3 : USB Host: Enable or disable write control for Region 2: Disabled
    Master MPU: MPU3 : USB Host: Region 2 Start: 0x00000000
    Master MPU: MPU3 : USB Host: Region 2 End: 0x00000000
    Master MPU: MPU3 : USB Host: Enable or disable read control for Region 3: Disabled
    Master MPU: MPU3 : USB Host: Enable or disable write control for Region 3: Disabled
    Master MPU: MPU3 : USB Host: Region 3 Start: 0x00000000
    Master MPU: MPU3 : USB Host: Region 3 End: 0x00000000
    Master MPU: MPU3 : USB Host: Enable or disable read control for Region 4: Disabled
    Master MPU: MPU3 : USB Host: Enable or disable write control for Region 4: Disabled
    Master MPU: MPU3 : USB Host: Region 4 Start: 0x00000000
    Master MPU: MPU3 : USB Host: Region 4 End: 0x00000000
    Master MPU: MPU3 : USB Host: Enable or disable read control for Region 5: Disabled
    Master MPU: MPU3 : USB Host: Enable or disable write control for Region 5: Disabled
    Master MPU: MPU3 : USB Host: Region 5 Start: 0x00000000
    Master MPU: MPU3 : USB Host: Region 5 End: 0x00000000
    Master MPU: MPU3 : USB Host: Enable or disable read control for Region 6: Disabled
    Master MPU: MPU3 : USB Host: Enable or disable write control for Region 6: Disabled
    Master MPU: MPU3 : USB Host: Region 6 Start: 0x00000000
    Master MPU: MPU3 : USB Host: Region 6 End: 0x00000000
    Master MPU: MPU3 : USB Host: Enable or disable read control for Region 7: Disabled
    Master MPU: MPU3 : USB Host: Enable or disable write control for Region 7: Disabled
    Master MPU: MPU3 : USB Host: Region 7 Start: 0x00000000
    Master MPU: MPU3 : USB Host: Region 7 End: 0x00000000
    Master MPU: MPU4 : USB Function: Enable or disable read control for Region 0: Disabled
    Master MPU: MPU4 : USB Function: Enable or disable write control for Region 0: Disabled
    Master MPU: MPU4 : USB Function: Region 0 Start: 0x00000000
    Master MPU: MPU4 : USB Function: Region 0 End: 0x00000000
    Master MPU: MPU4 : USB Function: Enable or disable read control for Region 1: Disabled
    Master MPU: MPU4 : USB Function: Enable or disable write control for Region 1: Disabled
    Master MPU: MPU4 : USB Function: Region 1 Start: 0x00000000
    Master MPU: MPU4 : USB Function: Region 1 End: 0x00000000
    Master MPU: MPU4 : USB Function: Enable or disable read control for Region 2: Disabled
    Master MPU: MPU4 : USB Function: Enable or disable write control for Region 2: Disabled
    Master MPU: MPU4 : USB Function: Region 2 Start: 0x00000000
    Master MPU: MPU4 : USB Function: Region 2 End: 0x00000000
    Master MPU: MPU4 : USB Function: Enable or disable read control for Region 3: Disabled
    Master MPU: MPU4 : USB Function: Enable or disable write control for Region 3: Disabled
    Master MPU: MPU4 : USB Function: Region 3 Start: 0x00000000
    Master MPU: MPU4 : USB Function: Region 3 End: 0x00000000
    Master MPU: MPU4 : USB Function: Enable or disable read control for Region 4: Disabled
    Master MPU: MPU4 : USB Function: Enable or disable write control for Region 4: Disabled
    Master MPU: MPU4 : USB Function: Region 4 Start: 0x00000000
    Master MPU: MPU4 : USB Function: Region 4 End: 0x00000000
    Master MPU: MPU4 : USB Function: Enable or disable read control for Region 5: Disabled
    Master MPU: MPU4 : USB Function: Enable or disable write control for Region 5: Disabled
    Master MPU: MPU4 : USB Function: Region 5 Start: 0x00000000
    Master MPU: MPU4 : USB Function: Region 5 End: 0x00000000
    Master MPU: MPU4 : USB Function: Enable or disable read control for Region 6: Disabled
    Master MPU: MPU4 : USB Function: Enable or disable write control for Region 6: Disabled
    Master MPU: MPU4 : USB Function: Region 6 Start: 0x00000000
    Master MPU: MPU4 : USB Function: Region 6 End: 0x00000000
    Master MPU: MPU4 : USB Function: Enable or disable read control for Region 7: Disabled
    Master MPU: MPU4 : USB Function: Enable or disable write control for Region 7: Disabled
    Master MPU: MPU4 : USB Function: Region 7 Start: 0x00000000
    Master MPU: MPU4 : USB Function: Region 7 End: 0x00000000
    Master MPU: MPU6 : CoreSight: Enable or disable read control for Region 0: Disabled
    Master MPU: MPU6 : CoreSight: Enable or disable write control for Region 0: Disabled
    Master MPU: MPU6 : CoreSight: Region 0 Start: 0x00000000
    Master MPU: MPU6 : CoreSight: Region 0 End: 0x00000000
    Master MPU: MPU6 : CoreSight: Enable or disable read control for Region 1: Disabled
    Master MPU: MPU6 : CoreSight: Enable or disable write control for Region 1: Disabled
    Master MPU: MPU6 : CoreSight: Region 1 Start: 0x00000000
    Master MPU: MPU6 : CoreSight: Region 1 End: 0x00000000
    Master MPU: MPU6 : CoreSight: Enable or disable read control for Region 2: Disabled
    Master MPU: MPU6 : CoreSight: Enable or disable write control for Region 2: Disabled
    Master MPU: MPU6 : CoreSight: Region 2 Start: 0x00000000
    Master MPU: MPU6 : CoreSight: Region 2 End: 0x00000000
    Master MPU: MPU6 : CoreSight: Enable or disable read control for Region 3: Disabled
    Master MPU: MPU6 : CoreSight: Enable or disable write control for Region 3: Disabled
    Master MPU: MPU6 : CoreSight: Region 3 Start: 0x00000000
    Master MPU: MPU6 : CoreSight: Region 3 End: 0x00000000
    Master MPU: MPU6 : CoreSight: Enable or disable read control for Region 4: Disabled
    Master MPU: MPU6 : CoreSight: Enable or disable write control for Region 4: Disabled
    Master MPU: MPU6 : CoreSight: Region 4 Start: 0x00000000
    Master MPU: MPU6 : CoreSight: Region 4 End: 0x00000000
    Master MPU: MPU6 : CoreSight: Enable or disable read control for Region 5: Disabled
    Master MPU: MPU6 : CoreSight: Enable or disable write control for Region 5: Disabled
    Master MPU: MPU6 : CoreSight: Region 5 Start: 0x00000000
    Master MPU: MPU6 : CoreSight: Region 5 End: 0x00000000
    Master MPU: MPU6 : CoreSight: Enable or disable read control for Region 6: Disabled
    Master MPU: MPU6 : CoreSight: Enable or disable write control for Region 6: Disabled
    Master MPU: MPU6 : CoreSight: Region 6 Start: 0x00000000
    Master MPU: MPU6 : CoreSight: Region 6 End: 0x00000000
    Master MPU: MPU6 : CoreSight: Enable or disable read control for Region 7: Disabled
    Master MPU: MPU6 : CoreSight: Enable or disable write control for Region 7: Disabled
    Master MPU: MPU6 : CoreSight: Region 7 Start: 0x00000000
    Master MPU: MPU6 : CoreSight: Region 7 End: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Enable or disable read control for Region 0: Disabled
    Master MPU: MPU7 : SHOSTIF: Enable or disable write control for Region 0: Disabled
    Master MPU: MPU7 : SHOSTIF: Region 0 Start: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Region 0 End: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Enable or disable read control for Region 1: Disabled
    Master MPU: MPU7 : SHOSTIF: Enable or disable write control for Region 1: Disabled
    Master MPU: MPU7 : SHOSTIF: Region 1 Start: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Region 1 End: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Enable or disable read control for Region 2: Disabled
    Master MPU: MPU7 : SHOSTIF: Enable or disable write control for Region 2: Disabled
    Master MPU: MPU7 : SHOSTIF: Region 2 Start: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Region 2 End: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Enable or disable read control for Region 3: Disabled
    Master MPU: MPU7 : SHOSTIF: Enable or disable write control for Region 3: Disabled
    Master MPU: MPU7 : SHOSTIF: Region 3 Start: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Region 3 End: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Enable or disable read control for Region 4: Disabled
    Master MPU: MPU7 : SHOSTIF: Enable or disable write control for Region 4: Disabled
    Master MPU: MPU7 : SHOSTIF: Region 4 Start: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Region 4 End: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Enable or disable read control for Region 5: Disabled
    Master MPU: MPU7 : SHOSTIF: Enable or disable write control for Region 5: Disabled
    Master MPU: MPU7 : SHOSTIF: Region 5 Start: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Region 5 End: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Enable or disable read control for Region 6: Disabled
    Master MPU: MPU7 : SHOSTIF: Enable or disable write control for Region 6: Disabled
    Master MPU: MPU7 : SHOSTIF: Region 6 Start: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Region 6 End: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Enable or disable read control for Region 7: Disabled
    Master MPU: MPU7 : SHOSTIF: Enable or disable write control for Region 7: Disabled
    Master MPU: MPU7 : SHOSTIF: Region 7 Start: 0x00000000
    Master MPU: MPU7 : SHOSTIF: Region 7 End: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Enable or disable read control for Region 0: Disabled
    Master MPU: MPU8 : PHOSTIF: Enable or disable write control for Region 0: Disabled
    Master MPU: MPU8 : PHOSTIF: Region 0 Start: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Region 0 End: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Enable or disable read control for Region 1: Disabled
    Master MPU: MPU8 : PHOSTIF: Enable or disable write control for Region 1: Disabled
    Master MPU: MPU8 : PHOSTIF: Region 1 Start: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Region 1 End: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Enable or disable read control for Region 2: Disabled
    Master MPU: MPU8 : PHOSTIF: Enable or disable write control for Region 2: Disabled
    Master MPU: MPU8 : PHOSTIF: Region 2 Start: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Region 2 End: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Enable or disable read control for Region 3: Disabled
    Master MPU: MPU8 : PHOSTIF: Enable or disable write control for Region 3: Disabled
    Master MPU: MPU8 : PHOSTIF: Region 3 Start: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Region 3 End: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Enable or disable read control for Region 4: Disabled
    Master MPU: MPU8 : PHOSTIF: Enable or disable write control for Region 4: Disabled
    Master MPU: MPU8 : PHOSTIF: Region 4 Start: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Region 4 End: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Enable or disable read control for Region 5: Disabled
    Master MPU: MPU8 : PHOSTIF: Enable or disable write control for Region 5: Disabled
    Master MPU: MPU8 : PHOSTIF: Region 5 Start: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Region 5 End: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Enable or disable read control for Region 6: Disabled
    Master MPU: MPU8 : PHOSTIF: Enable or disable write control for Region 6: Disabled
    Master MPU: MPU8 : PHOSTIF: Region 6 Start: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Region 6 End: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Enable or disable read control for Region 7: Disabled
    Master MPU: MPU8 : PHOSTIF: Enable or disable write control for Region 7: Disabled
    Master MPU: MPU8 : PHOSTIF: Region 7 Start: 0x00000000
    Master MPU: MPU8 : PHOSTIF: Region 7 End: 0x00000000
    
  RZN2L
    stack size (bytes): FIQ stack size: 0x400
    stack size (bytes): IRQ stack size: 0x400
    stack size (bytes): ABT stack size: 0x400
    stack size (bytes): UND stack size: 0x400
    stack size (bytes): SYS stack size: 0x400
    stack size (bytes): SVC stack size: 0x400
    Heap size (bytes): 0x2000
    C Runtime Initialization : Enabled
    TFU Mathlib: Enabled
    
  RZN2L Family
    
  RZN Common
    MCU Vcc (mV): 3300
    Parameter checking: Disabled
    Assert Failures: Return FSP_ERR_ASSERTION
    Error Log: No Error Log
    Soft Reset: Disabled
    Port Protect: Enabled
    Early BSP Initialization : Disabled
    
  Clocks
    LOCO Enabled
    PLL1 is initial state
    Ethernet Clock src: Main clock oscillator
    CLMA0 Enabled
    CLMA0 error not mask
    CLMA3 error not mask
    CLMA1 error mask
    CLMA3 Enabled
    CLMA1 Enabled
    CLMA2 Enabled
    CLMA0 CMPL 1
    CLMA1 CMPL 1
    CLMA2 CMPL 1
    CLMA3 CMPL 1
    Alternative clock: LOCO
    CLMA0 CMPH 1023
    CLMA1 CMPH 1023
    CLMA2 CMPH 1023
    CLMA3 CMPH 1023
    ICLK 200MHz
    CPU0CLK Mulx1
    CKIO Div/4
    SPI0ASYNCCLK: 96MHz
    SPI1ASYNCCLK: 96MHz
    SPI2ASYNCCLK: 96MHz
    SPI3ASYNCCLK: 96MHz
    PCLKCAN 40MHz
    XSPI_CLK0 12.5MHz
    XSPI_CLK1 12.5MHz
    
  Pin Configurations
    R9A07G084M04GBG.pincfg -> g_bsp_pin_cfg
      AN000 B13 SYSTEM_AN000 - - - - - - - - I "Read only" - 
      AN001 C12 SYSTEM_AN001 - - - - - - - - I "Read only" - 
      AN002 B14 SYSTEM_AN002 - - - - - - - - I "Read only" - 
      AN003 C13 SYSTEM_AN003 - - - - - - - - I "Read only" - 
      AN100 B12 SYSTEM_AN100 - - - - - - - - I "Read only" - 
      AN101 A14 SYSTEM_AN101 - - - - - - - - I "Read only" - 
      AN102 B11 SYSTEM_AN102 - - - - - - - - I "Read only" - 
      AN103 A13 SYSTEM_AN103 - - - - - - - - I "Read only" - 
      AN104 A12 SYSTEM_AN104 - - - - - - - - I "Read only" - 
      AN105 B10 SYSTEM_AN105 - - - - - - - - I "Read only" - 
      AN106 A11 SYSTEM_AN106 - - - - - - - - I "Read only" - 
      AN107 C9 SYSTEM_AN107 - - - - - - - - I "Read only" - 
      AVCC18_TSU C14 SYSTEM_AVCC18_TSU - - - - - - - - I "Read only" - 
      AVCC18_USB P10 SYSTEM_AVCC18_USB - - - - - - - - I "Read only" - 
      AVCC18_USB R10 SYSTEM_AVCC18_USB - - - - - - - - I "Read only" - 
      BSCANP G2 SYSTEM_BSCANP - - - - - - - - I "Read only" - 
      EXTAL R7 CGC_EXTAL - - - - - - - - I "Read only" - 
      EXTCLKIN R6 CGC_EXTCLKIN - - - - - - - - I "Read only" - 
      MDX P5 SYSTEM_MDX - - - - - - - - IO "Read only" - 
      P00_0 C4 - - - - Disabled - - "BSC: D15; ETHER_ETH2: ETH2_RXD3; PHOSTIF: HD15; SCI2: DE2; SCI2: SCK2" - None - - 
      P00_1 D5 - - - - Disabled - - "BSC: A13; ETHER_ETH2: ETH2_RXDV_CRSDV_RXCTL; IRQ: IRQ0; MTU35: MTIC5U; SCI2: RXD_MISO2; SCI2: SCL2" - None - - 
      P00_2 A3 - - - - Disabled - - "BSC: RD#; ETHER_ETH2: ETH2_TXEN_TXCTL; MTU35: MTIC5V; SCI2: SDA2; SCI2: TXD_MOSI2; USB_HS: USB_OVRCUR" - None - - 
      P00_3 B3 - - - - Disabled - - "BSC: RD_WR#; ETHER_ETH2: ETH2_REFCLK; ETHER_ETH2: ETH2_RMII2_REFCLK; IRQ: IRQ1; MTU35: MTIC5W; SCI2: CTS_RTS_SS2#" - None - - 
      P00_4 A4 - - - - Disabled - - "BSC: WAIT#; DSMIF0: MCLK0; ETHER_ETH2: ETH2_RXER; GPT0: GTIOC0A; IRQ: IRQ13; MTU33: MTIOC3A; PHOSTIF: HWAIT#" - None - - 
      P00_5 B4 - - - - Disabled - - "BSC: CS0#; DSMIF0: MDAT0; ETHER_ESC: ESC_PHYLINK0; ETHER_ESC: ESC_PHYLINK2; ETHER_ETHSW: ETHSW_PHYLINK0; ETHER_ETHSW: ETHSW_PHYLINK2; GPT0: GTIOC0B; MTU33: MTIOC3C" - None - - 
      P00_6 C3 - - - - Disabled - - "BSC: CS5#; ETHER_ETH2: ETH2_TXCLK_TXC; GPT1: GTIOC1A; MTU33: MTIOC3B" - None - - 
      P00_7 D4 - - - - Disabled - - "BSC: RAS#; GPT2: GTIOC2A; IRQ: IRQ13; MTU34: MTIOC4A" - None - - 
      P01_0 A2 - - - - Disabled - - "BSC: CAS#; DSMIF1: MCLK1; ETHER_ESC: ESC_MDIO; ETHER_ETHSW: ETHSW_MDIO; ETHER_GMAC: GMAC_MDIO; GPT3: GTIOC3A; MTU34: MTIOC4C; SCI2: CTS2#" - None - - 
      P01_1 D3 - - - - Disabled - - "BSC: CKE; DSMIF1: MDAT1; ETHER_ESC: ESC_MDC; ETHER_ETHSW: ETHSW_MDC; ETHER_GMAC: GMAC_MDC; GPT1: GTIOC1B; MTU33: MTIOC3D; SCI2: DE2" - None - - 
      P01_2 B2 - - - - Disabled - - "BSC: CS2#; ETHER_ETH2: ETH2_TXD3; GPT2: GTIOC2B; IRQ: IRQ2; MTU34: MTIOC4B" - None - - 
      P01_3 C2 - - - - Disabled - - "BSC: AH#; ETHER_ETH2: ETH2_TXD2; GPT3: GTIOC3B; MTU34: MTIOC4D" - None - - 
      P01_4 E4 - - - - Disabled - - "BSC: WE1#_DQMLU; ETHER_ETH2: ETH2_TXD1; IRQ: IRQ3; MTU_POE3: POE0#" - None - - 
      P01_5 B1 - - - - Disabled - - "BSC: WE0#_DQMLL; ETHER_ETH2: ETH2_TXD0" - None - - 
      P01_6 D2 - - - - Disabled - - "BSC: A20; CANFD1: CANTXDP1; ETHER_ESC: ESC_LATCH0; ETHER_ESC: ESC_LATCH1; ETHER_GMAC: GMAC_PTPTRG1; GPT9: GTIOC9A; MTU31: MTIOC1A; PHOSTIF: HA20; SCI1: CTS1#; TRACE: TRACEDATA0" - None - - 
      P01_7 C1 - - - - Disabled - - "ADC0: ADTRG0#; BSC: A19; CANFD0: CANRX0; ETHER_ETHSW: ETHSW_LPI1; GPT9: GTIOC9B; MTU31: MTIOC1B; PHOSTIF: HA19; SCI1: SCK1; SPI3: SPI_RSPCK3; TRACE: TRACEDATA1" - None - - 
      P02_0 E3 - - - - Disabled - - "BSC: A18; CANFD1: CANTX1; ETHER_ETHSW: ETHSW_LPI2; GPT: GTADSML0; IRQ: IRQ4; PHOSTIF: HA18; SCI1: RXD_MISO1; SCI1: SCL1; SPI3: SPI_MISO3; TRACE: TRACEDATA2; USB_HS: USB_OTGID" - None - - 
      P02_1 D1 - - - - Disabled - - "BSC: A17; ETHER_ESC: ESC_SYNC0; ETHER_ESC: ESC_SYNC1; ETHER_ETHSW: ETHSW_PTPOUT1; PHOSTIF: HA17; SCI1: DE1" - None - - 
      P02_2 F3 - - - - Disabled - - "BSC: A16; CANFD0: CANTX0; ETHER_ETHSW: ETHSW_TDMAOUT0; GPT10: GTIOC10A; IRQ: IRQ14; MTU32: MTIOC2A; MTU_POE3: POE10#; PHOSTIF: HA16; RTC: RTCAT1HZ; SCI1: SDA1; SCI1: TXD_MOSI1; SPI3: SPI_MOSI3" - None - - 
      P02_3 E1 - - - - Disabled - - "BSC: A15; BSC: AH#; CANFD1: CANRX1; ETHER_ETHSW: ETHSW_TDMAOUT1; GPT10: GTIOC10B; IRQ: IRQ15; MTU32: MTIOC2B; MTU_POE3: POE11#; PHOSTIF: HA15; SCI1: CTS_RTS_SS1#; SPI3: SPI_SSL30" - None - - 
      P02_4 F4 JTAG/SWD_TDO - High - "Peripheral mode" - - "BSC: WE0#_DQMLL; JTAG/SWD: TDO; SCI1: DE1; SPI3: SPI_SSL33" - O - - 
      P02_5 F2 JTAG/SWD_TDI - Low - "Peripheral mode" - - "BSC: WE1#_DQMLU; ETHER_ETHSW: ETHSW_TDMAOUT3; JTAG/SWD: TDI; SCI5: SCK5; SPI3: SPI_SSL31" - I - - 
      P02_6 F5 JTAG/SWD_TMS_SWDIO - High - "Peripheral mode" - - "JTAG/SWD: TMS_SWDIO; SCI5: RXD_MISO5; SCI5: SCL5" - IO - - 
      P02_7 F1 JTAG/SWD_TCK_SWCLK - Low - "Peripheral mode" - - "JTAG/SWD: TCK_SWCLK; SCI5: SDA5; SCI5: TXD_MOSI5" - I - - 
      P03_0 G3 GPIO - Low - "Output mode (Low & Into Input)" - - "BSC: A14; BSC: CS5#; CANFD1: CANTXDP1; GPT: GTADSML1; IRQ: IRQ14; PHOSTIF: HA14; SCI2: SCK2; SPI3: SPI_SSL32; TRACE: TRACEDATA3" - IO - - 
      P03_5 G1 - - - - Disabled - - "BSC: A12; DSMIF2: MCLK2; ETHER_ETH2: ETH2_CRS; GPT4: GTIOC4A; IRQ: IRQ5; MTU33: MTIOC3A; PHOSTIF: HA12; SCI2: RXD_MISO2; SCI2: SCL2" - None - - 
      P03_6 G4 - - - - Disabled - - "BSC: A11; DSMIF2: MDAT2; ETHER_ETH2: ETH2_COL; GPT4: GTIOC4B; IRQ: IRQ8; MTU33: MTIOC3B; PHOSTIF: HA11; SCI2: SDA2; SCI2: TXD_MOSI2; SPI1: SPI_SSL13; TRACE: TRACEDATA4" - None - - 
      P03_7 G5 - - - - Disabled - - "BSC: A10; ETHER_ETH2: ETH2_TXER; GPT5: GTIOC5A; IRQ: IRQ9; MTU33: MTIOC3C; PHOSTIF: HA10; SCI3: SCK3; TRACE: TRACEDATA5" - None - - 
      P04_0 H1 - - - - Disabled - - "BSC: A9; GPT5: GTIOC5B; MTU33: MTIOC3D; PHOSTIF: HA9; SCI3: RXD_MISO3; SCI3: SCL3; TRACE: TRACEDATA6" - None - - 
      P04_1 H2 - - - - Disabled - - "BSC: CKIO; IIC2: IIC_SDA2; PHOSTIF: HCKIO; SCI3: SDA3; SCI3: TXD_MOSI3; SPI0: SPI_MOSI0" - None - - 
      P04_4 H4 - - - - Disabled - - "BSC: A8; GPT: GTADSMP0; IRQ: IRQ10; MTU_POE3: POE10#; PHOSTIF: HA8; SCI3: CTS3#; SPI1: SPI_RSPCK1; TRACE: TRACEDATA7" - None - - 
      P04_5 H3 - - - - Disabled - - "BSC: A7; ETHER_ESC: ESC_SYNC0; ETHER_ESC: ESC_SYNC1; ETHER_ETHSW: ETHSW_PTPOUT0; PHOSTIF: HA7; SCI3: DE3" - None - - 
      P04_6 H5 - - - - Disabled - - "BSC: A6; DMAC: DACK; ETHER_ETH1: ETH1_TXER; PHOSTIF: HA6; RTC: RTCAT1HZ" - None - - 
      P04_7 J1 - - - - Disabled - - "BSC: A5; ETHER_ETH0: ETH0_TXER; ETHER_ETH2: ETH2_TXER; PHOSTIF: HA5; SPI2: SPI_SSL21" - None - - 
      P05_0 J5 - - - - Disabled - - "BSC: A4; CANFD0: CANTXDP0; CMTW0: CMTW0_TOC0; DSMIF3: MCLK3; ETHER_ETH1: ETH1_CRS; GPT6: GTIOC6A; IRQ: IRQ12; MTU34: MTIOC4A; PHOSTIF: HA4; SCI5: CTS_RTS_SS5#; USB_HS: USB_VBUSEN" - None - - 
      P05_1 J2 - - - - Disabled - - "BSC: A3; CANFD0: CANRXDP0; CMTW0: CMTW0_TIC1; DSMIF3: MDAT3; ETHER_ETH1: ETH1_COL; GPT6: GTIOC6B; IRQ: IRQ13; MTU34: MTIOC4B; PHOSTIF: HA3; SCI5: CTS5#; USB_HS: USB_EXICEN" - None - - 
      P05_2 J4 - - - - Disabled - - "BSC: A2; CANFD0: CANRX0; CMTW0: CMTW0_TOC0; DMAC: DREQ; ETHER_ETH0: ETH0_CRS; GPT7: GTIOC7A; GPT_POEG: GTETRGSA; IIC1: IIC_SCL1; IRQ: IRQ14; MTU34: MTIOC4C; PHOSTIF: HA2; SCI5: DE5; USB_HS: USB_VBUSEN" - None - - 
      P05_3 J3 - - - - Disabled - - "BSC: A1; CANFD0: CANTX0; CMTW0: CMTW0_TIC0; ETHER_ETH0: ETH0_COL; GPT7: GTIOC7B; GPT_POEG: GTETRGSB; IIC1: IIC_SDA1; IRQ: IRQ15; MTU34: MTIOC4D; MTU_POE3: POE11#; PHOSTIF: HA1; SCI4: SCK4; USB_HS: USB_EXICEN" - None - - 
      P05_4 K1 - - - - Disabled - - "BSC: A0; CANFD0: CANTXDP0; DMAC: DACK; ETHER_ETHSW: ETHSW_LPI0; GPT14: GTIOC14A; IRQ: IRQ12; PHOSTIF: HA0; SCI4: RXD_MISO4; SCI4: SCL4; SPI0: SPI_SSL00; USB_HS: USB_OVRCUR" - None - - 
      P05_5 K2 - - - - Disabled - - "CMTW0: CMTW0_TOC1; ETHER_ESC: ESC_PHYLINK1; ETHER_ETHSW: ETHSW_PHYLINK1; GPT14: GTIOC14B; SPI2: SPI_RSPCK2" - None - - 
      P05_6 K3 - - - - Disabled - - "CMTW1: CMTW1_TIC0; ETHER_ETH1: ETH1_RXER; GPT15: GTIOC15A; IRQ: IRQ12; SPI2: SPI_SSL22" - None - - 
      P05_7 M1 - - - - Disabled - - "CMTW1: CMTW1_TOC1; ETHER_ETH1: ETH1_TXD2; GPT15: GTIOC15B; SCI4: SDA4; SCI4: TXD_MOSI4; SPI2: SPI_SSL23" - None - - 
      P06_0 L2 - - - - Disabled - - "CANFD1: CANRX1; CMTW1: CMTW1_TOC0; ETHER_ETH1: ETH1_TXD3; GPT16: GTIOC16A; SCI4: CTS_RTS_SS4#; SPI2: SPI_SSL23" - None - - 
      P06_1 L3 - - - - Disabled - - "CANFD1: CANTX1; ETHER_ETH1: ETH1_REFCLK; ETHER_ETH1: ETH1_RMII1_REFCLK; GPT16: GTIOC16B; SCI4: CTS4#; SPI2: SPI_SSL22" - None - - 
      P06_2 M2 - - - - Disabled - - "CANFD1: CANRXDP1; ETHER_ETH1: ETH1_TXD1; GPT17: GTIOC17A" - None - - 
      P06_3 K4 - - - - Disabled - - "CANFD1: CANTXDP1; CMTW1: CMTW1_TIC1; ETHER_ETH1: ETH1_TXD0; GPT17: GTIOC17B; SCI4: DE4; SPI1: SPI_MISO1" - None - - 
      P06_4 N1 - - - - Disabled - - "ETHER_ETH1: ETH1_TXCLK_TXC; GPT11: GTIOC11A; SPI1: SPI_MOSI1" - None - - 
      P06_5 N2 - - - - Disabled - - "ETHER_ETH1: ETH1_TXEN_TXCTL; GPT11: GTIOC11B" - None - - 
      P06_6 L4 - - - - Disabled - - "ETHER_ETH1: ETH1_RXD0; GPT12: GTIOC12A; SPI1: SPI_SSL10" - None - - 
      P06_7 M3 - - - - Disabled - - "ETHER_ETH1: ETH1_RXD1; GPT12: GTIOC12B; SPI1: SPI_SSL11" - None - - 
      P07_0 P1 - - - - Disabled - - "ETHER_ETH1: ETH1_RXD2; GPT13: GTIOC13A" - None - - 
      P07_1 N3 - - - - Disabled - - "ETHER_ETH1: ETH1_RXD3; GPT13: GTIOC13B" - None - - 
      P07_2 P2 - - - - Disabled - - "ETHER_ETH1: ETH1_RXDV_CRSDV_RXCTL" - None - - 
      P07_3 M4 - - - - Disabled - - "ETHER_ETH1: ETH1_RXCLK_REF_CLK_RXC" - None - - 
      P07_4 R2 USB_HS_USB_VBUSIN - - - "Peripheral mode" - - "ADC0: ADTRG0#; IRQ: IRQ1; USB_HS: USB_VBUSIN" - I - - 
      P08_4 N4 - - - - Disabled - - "ETHER_ETH0: ETH0_RXD3; MTU36: MTIOC6A" - None - - 
      P08_5 P3 - - - - Disabled - - "ETHER_ETH0: ETH0_RXDV_CRSDV_RXCTL; MTU36: MTIOC6B" - None - - 
      P08_6 M5 - - - - Disabled - - "ETHER_ETH0: ETH0_RXCLK_REF_CLK_RXC; MTU36: MTIOC6C" - None - - 
      P08_7 N5 - - - - Disabled - - "ETHER_ESC: ESC_MDC; ETHER_ETHSW: ETHSW_MDC; ETHER_GMAC: GMAC_MDC; MTU36: MTIOC6D" - None - - 
      P09_0 P4 - - - - Disabled - - "ETHER_ESC: ESC_MDIO; ETHER_ETHSW: ETHSW_MDIO; ETHER_GMAC: GMAC_MDIO; MTU37: MTIOC7A" - None - - 
      P09_1 R3 - - - - Disabled - - "ETHER_ETH0: ETH0_REFCLK; ETHER_ETH0: ETH0_RMII0_REFCLK; MTU37: MTIOC7B" - None - - 
      P09_2 N6 - - - - Disabled - - "ETHER_ETH0: ETH0_RXER; IRQ: IRQ0; MTU37: MTIOC7C" - None - - 
      P09_3 R4 - - - - Disabled - - "ETHER_ETH0: ETH0_TXD3; MTU37: MTIOC7D" - None - - 
      P09_4 M6 - - - - Disabled - - "ETHER_ETH0: ETH0_TXD2" - None - - 
      P09_5 N7 - - - - Disabled - - "ETHER_ETH0: ETH0_TXD1" - None - - 
      P09_6 M7 - - - - Disabled - - "ETHER_ETH0: ETH0_TXD0" - None - - 
      P09_7 L7 - - - - Disabled - - "ETHER_ETH0: ETH0_TXCLK_TXC" - None - - 
      P10_0 N8 - - - - Disabled - - "ETHER_ETH0: ETH0_TXEN_TXCTL" - None - - 
      P10_1 M8 - - - - Disabled - - "ETHER_ETH0: ETH0_RXD0" - None - - 
      P10_2 L8 - - - - Disabled - - "ETHER_ETH0: ETH0_RXD1" - None - - 
      P10_3 L9 - - - - Disabled - - "ETHER_ETH0: ETH0_RXD2; RTC: RTCAT1HZ" - None - - 
      P10_4 M9 - - - - Disabled - - "ETHER_ESC: ESC_PHYLINK0; ETHER_ETHSW: ETHSW_PHYLINK0; IRQ: IRQ11" - None - - 
      P12_4 N11 - - - - Disabled - - "BSC: D15; ETHER_ETH1: ETH1_CRS; GPT8: GTIOC8B; MBXSEM: MBX_HINT#; MTU38: MTIOC8B; SPI0: SPI_SSL01; TRACE: TRACEDATA0" - None - - 
      P13_2 L10 - - - - Disabled - - "BSC: A13; BSC: D9; DSMIF4: MCLK4; ETHER_ESC: ESC_I2CCLK; ETHER_ETHSW: ETHSW_PTPOUT2; GPT10: GTIOC10A; IIC0: IIC_SCL0; IRQ: IRQ5; MTU30: MTIOC0A; MTU_POE3: POE8#; SCI1: CTS_RTS_SS1#; SPI0: SPI_MISO0; TRACE: TRACEDATA6" - None - - 
      P13_3 N12 - - - - Disabled - - "BSC: D8; BSC: RD#; CMTW1: CMTW1_TOC0; DSMIF4: MDAT4; ETHER_ESC: ESC_I2CDATA; ETHER_ETHSW: ETHSW_PTPOUT3; GPT10: GTIOC10B; IIC0: IIC_SDA0; MTU30: MTIOC0B; MTU30: MTIOC0C; SCI1: CTS1#; SPI0: SPI_RSPCK0; TRACE: TRACEDATA7" - None - - 
      P13_4 L12 - - - - Disabled - - "BSC: A0; ETHER_ESC: ESC_RESETOUT#; GPT8: GTIOC8B; MTU30: MTIOC0D" - None - - 
      P13_5 M12 - - - - Disabled - - "ETHER_ESC: ESC_LATCH0; ETHER_ESC: ESC_LATCH1; ETHER_GMAC: GMAC_PTPTRG0; IIC2: IIC_SCL2; MTU3: MTCLKA; SPI1: SPI_RSPCK1; XSPI0: XSPI0_WP1#" - None - - 
      P13_6 M13 - - - - Disabled - - "ETHER_ESC: ESC_SYNC0; ETHER_ESC: ESC_SYNC1; ETHER_ETHSW: ETHSW_PTPOUT0; MTU3: MTCLKB; XSPI0: XSPI0_WP0#" - None - - 
      P13_7 M11 - - - - Disabled - - "ETHER_ESC: ESC_LATCH0; ETHER_ESC: ESC_LATCH1; ETHER_GMAC: GMAC_PTPTRG1; MBXSEM: MBX_HINT#; MTU3: MTCLKC; XSPI0: XSPI0_ECS1#" - None - - 
      P14_0 L13 - - - - Disabled - - "ETHER_ESC: ESC_SYNC0; ETHER_ESC: ESC_SYNC1; ETHER_ETHSW: ETHSW_PTPOUT1; MTU3: MTCLKD; XSPI0: XSPI0_INT0#" - None - - 
      P14_1 L14 - - - - Disabled - - "ETHER_ESC: ESC_LATCH0; ETHER_ESC: ESC_LATCH1; ETHER_ETH1: ETH1_COL; ETHER_GMAC: GMAC_PTPTRG1; GPT8: GTIOC8A; MTU38: MTIOC8A; SHOSTIF: HSPI_IO0; XSPI0: XSPI0_INT1#" - None - - 
      P14_2 K12 - - - - Disabled - - "ETHER_ETH0: ETH0_CRS; ETHER_ETH2: ETH2_CRS; GPT8: GTIOC8B; IRQ: IRQ6; MTU38: MTIOC8B; SHOSTIF: HSPI_CK; XSPI0: XSPI0_ECS0#" - None - - 
      P14_3 M14 - - - - Disabled - - "ETHER_ETH0: ETH0_COL; ETHER_ETH2: ETH2_COL; MTU30: MTIOC0A; SHOSTIF: HSPI_IO1; XSPI0: XSPI0_RSTO1#" - None - - 
      P14_4 J13 - - - - Disabled - - "BSC: BS#; ETHER_ESC: ESC_IRQ; MTU30: MTIOC0B; PHOSTIF: HBS#; XSPI0: XSPI0_DS" - None - - 
      P14_5 J12 - - - - Disabled - - "BSC: CS3#; MTU_POE3: POE8#; SHOSTIF: HSPI_INT#; XSPI0: XSPI0_CKN" - None - - 
      P14_6 K13 - - - - Disabled - - "BSC: A21; XSPI0: XSPI0_CKP" - None - - 
      P14_7 M15 - - - - Disabled - - "BSC: A22; BSC: BS#; SCI5: SCK5; SPI1: SPI_MISO1; XSPI0: XSPI0_IO0" - None - - 
      P15_0 L11 - - - - Disabled - - "BSC: A23; BSC: CKE; SCI5: RXD_MISO5; SCI5: SCL5; SPI1: SPI_MOSI1; XSPI0: XSPI0_IO1" - None - - 
      P15_1 K14 - - - - Disabled - - "BSC: A24; BSC: CAS#; MTU30: MTIOC0C; SCI5: SDA5; SCI5: TXD_MOSI5; SPI1: SPI_SSL10; XSPI0: XSPI0_IO2" - None - - 
      P15_2 K15 - - - - Disabled - - "BSC: A25; BSC: RAS#; MTU30: MTIOC0D; SCI5: CTS_RTS_SS5#; SPI1: SPI_SSL11; XSPI0: XSPI0_IO3" - None - - 
      P15_3 K11 - - - - Disabled - - "BSC: D11; DSMIF1: MCLK1; MTU38: MTIOC8C; XSPI0: XSPI0_IO4" - None - - 
      P15_4 H13 - - - - Disabled - - "BSC: D12; DSMIF1: MDAT1; MTU38: MTIOC8D; XSPI0: XSPI0_IO5" - None - - 
      P15_5 J14 - - - - Disabled - - "BSC: D13; DSMIF2: MCLK2; XSPI0: XSPI0_IO6" - None - - 
      P15_6 H12 - - - - Disabled - - "BSC: D14; DSMIF2: MDAT2; SPI1: SPI_SSL12; XSPI0: XSPI0_IO7" - None - - 
      P15_7 J15 - - - - Disabled - - "DMAC: TEND; SCI5: CTS5#; SPI1: SPI_SSL13; XSPI0: XSPI0_CS0#" - None - - 
      P16_0 G13 - - - - Disabled - - "DSMIF3: MCLK3; ETHER_ETH0: ETH0_TXER; ETHER_ETH2: ETH2_REFCLK; SCI0: SDA0; SCI0: TXD_MOSI0; SHOSTIF: HSPI_CS#; SPI3: SPI_MOSI3; XSPI0: XSPI0_CS1#" - None - - 
      P16_1 H11 - - - - Disabled - - "ADC0: ADTRG0#; BSC: CS2#; CMTW0: CMTW0_TOC1; DSMIF3: MDAT3; PHOSTIF: HCS1#; SCI0: RXD_MISO0; SCI0: SCL0; SPI3: SPI_MISO3; XSPI0: XSPI0_RESET0#" - None - - 
      P16_2 H14 - - - - Disabled - - "IRQ: NMI; PHOSTIF: HERROUT#; SCI0: CTS0#; SHOSTIF: HSPI_IO2; SPI3: SPI_RSPCK3; USB_HS: USB_EXICEN; XSPI0: XSPI0_RESET1#" - None - - 
      P16_3 G12 - - - - Disabled - - "BSC: CS3#; ETHER_ETH1: ETH1_CRS; ETHER_ETH1: ETH1_TXER; GPT: GTADSMP1; IRQ: IRQ7; SCI0: SCK0; SHOSTIF: HSPI_IO3; SPI3: SPI_SSL30; XSPI0: XSPI0_RSTO0#" - None - - 
      P16_5 H15 - - - - Disabled - - "BSC: A15; MTU35: MTIC5U; SCI0: SDA0; SCI0: TXD_MOSI0; SHOSTIF: HSPI_IO4" - None - - 
      P16_6 G11 - - - - Disabled - - "BSC: CS0#; IRQ: IRQ8; MTU35: MTIC5V; PHOSTIF: HCS0#; SCI0: RXD_MISO0; SCI0: SCL0; SHOSTIF: HSPI_IO5" - None - - 
      P16_7 G14 XSPI1_XSPI1_IO0 - High - "Peripheral mode" - - "BSC: A13; MTU35: MTIC5W; PHOSTIF: HA13; SCI0: SCK0; XSPI1: XSPI1_IO0" - IO - - 
      P17_0 F12 XSPI1_XSPI1_IO1 - High - "Peripheral mode" - - "ETHER_ESC: ESC_IRQ; SCI0: CTS_RTS_SS0#; XSPI1: XSPI1_IO1" - IO - - 
      P17_3 F14 XSPI1_XSPI1_IO2 - High - "Peripheral mode" - - "ADC1: ADTRG1#; DMAC: DREQ; GPT_POEG: GTETRGA; MTU_POE3: POE0#; SPI3: SPI_SSL31; TRACE: TRACECTL; XSPI1: XSPI1_IO2" - IO - - 
      P17_4 F13 XSPI1_XSPI1_IO3 - High - "Peripheral mode" - - "DMAC: DACK; GPT0: GTIOC0A; GPT_POEG: GTETRGB; MTU33: MTIOC3C; SCI3: CTS3#; SPI3: SPI_SSL32; TRACE: TRACECLK; XSPI1: XSPI1_IO3" - IO - - 
      P17_5 F15 - - - - Disabled - - "DMAC: TEND; GPT0: GTIOC0B; GPT_POEG: GTETRGC; MTU33: MTIOC3A; USB_HS: USB_OVRCUR" - None - - 
      P17_6 G15 - - - - Disabled - - "BSC: RD_WR#; GPT1: GTIOC1A; MTU33: MTIOC3B; PHOSTIF: HWRSTB#; SCI3: SCK3; XSPI1: XSPI1_DS" - None - - 
      P17_7 E15 XSPI1_XSPI1_CKP - High - "Peripheral mode" - - "BSC: RD#; DMAC: DACK; GPT2: GTIOC2A; GPT3: GTIOC3A; MTU34: MTIOC4A; MTU34: MTIOC4C; PHOSTIF: HRD#; SCI3: RXD_MISO3; SCI3: SCL3; XSPI1: XSPI1_CKP" - O - - 
      P18_0 E14 - - - - Disabled - - "BSC: WE0#_DQMLL; GPT2: GTIOC2A; GPT3: GTIOC3A; MTU34: MTIOC4A; MTU34: MTIOC4C; PHOSTIF: HWR0#; SCI3: SDA3; SCI3: TXD_MOSI3; SHOSTIF: HSPI_IO6" - None - - 
      P18_1 D15 - - - - Disabled - - "ADC1: ADTRG1#; BSC: WE1#_DQMLU; GPT1: GTIOC1B; IRQ: IRQ10; MTU33: MTIOC3D; PHOSTIF: HWR1#; SCI3: CTS_RTS_SS3#; SHOSTIF: HSPI_IO7" - None - - 
      P18_2 D14 XSPI1_XSPI1_CS0# - High - "Peripheral mode" - - "BSC: BS#; ETHER_ETH1: ETH1_COL; GPT2: GTIOC2B; GPT3: GTIOC3B; IIC2: IIC_SDA2; MTU34: MTIOC4B; MTU34: MTIOC4D; SCI0: SCK0; XSPI1: XSPI1_CS0#" - O - - 
      P18_3 E13 - - - - Disabled - - "BSC: CKE; CANFD1: CANRXDP1; CMTW1: CMTW1_TIC1; ETHER_ETH2: ETH2_CRS; GPT2: GTIOC2B; GPT3: GTIOC3B; IRQ: IRQ0; MTU34: MTIOC4B; MTU34: MTIOC4D; XSPI1: XSPI1_IO4" - None - - 
      P18_4 E12 - - - - Disabled - - "BSC: CAS#; CANFD0: CANTX0; ETHER_ETH1: ETH1_CRS; IRQ: IRQ1; MTU35: MTIC5U; SCI4: SDA4; SCI4: TXD_MOSI4; SPI2: SPI_RSPCK2; XSPI1: XSPI1_IO5" - None - - 
      P18_5 D13 - - - - Disabled - - "BSC: RAS#; CANFD0: CANRX0; ETHER_ETH2: ETH2_COL; MTU35: MTIC5V; SCI4: RXD_MISO4; SCI4: SCL4; SPI2: SPI_MOSI2; TRACE: TRACECTL; XSPI1: XSPI1_IO6" - None - - 
      P18_6 C15 - - - - Disabled - - "ADC0: ADTRG0#; ETHER_ETH1: ETH1_COL; IIC2: IIC_SCL2; IRQ: IRQ11; MTU35: MTIC5W; SCI4: DE4; SCI4: SCK4; SPI2: SPI_MISO2; TRACE: TRACECLK; XSPI1: XSPI1_IO7" - None - - 
      P19_0 B15 - - - - Disabled - - "USB_HS: USB_VBUSEN" - None - - 
      P20_1 B9 GPIO - Low - "Output mode (Low & Into Input)" - - "ETHER_ESC: ESC_LINKACT0; ETHER_ETHSW: ETHSW_PTPOUT3; ETHER_ETHSW: ETHSW_TDMAOUT0" - IO - - 
      P20_2 D8 GPIO - Low - "Output mode (Low & Into Input)" - - "ETHER_ESC: ESC_LEDRUN; ETHER_ESC: ESC_LEDSTER; ETHER_ETHSW: ETHSW_PTPOUT2; ETHER_ETHSW: ETHSW_TDMAOUT1; SCI3: DE3" - IO - - 
      P20_3 D9 GPIO - Low - "Output mode (Low & Into Input)" - - "ETHER_ESC: ESC_LEDERR; ETHER_ETHSW: ETHSW_PTPOUT1; ETHER_ETHSW: ETHSW_TDMAOUT2" - IO - - 
      P20_4 A9 GPIO - Low - "Output mode (Low & Into Input)" - - "ETHER_ESC: ESC_LINKACT1; ETHER_ETHSW: ETHSW_PTPOUT0; ETHER_ETHSW: ETHSW_TDMAOUT3" - IO - - 
      P21_1 B8 - - - - Disabled - - "BSC: D0; CMTW0: CMTW0_TIC0; DSMIF0: MCLK0; ETHER_ESC: ESC_SYNC0; ETHER_ESC: ESC_SYNC1; GPT14: GTIOC14A; IIC1: IIC_SCL1; MTU36: MTIOC6A; PHOSTIF: HD0; SCI5: SCK5; SHOSTIF: HSPI_INT#; SPI2: SPI_SSL20; TRACE: TRACEDATA0" - None - - 
      P21_2 C8 - - - - Disabled - - "BSC: D1; CMTW0: CMTW0_TIC1; DSMIF0: MDAT0; ETHER_ESC: ESC_SYNC0; ETHER_ESC: ESC_SYNC1; GPT14: GTIOC14B; IIC1: IIC_SDA1; MTU36: MTIOC6B; PHOSTIF: HD1; SCI5: RXD_MISO5; SCI5: SCL5; SPI2: SPI_MISO2; TRACE: TRACEDATA1" - None - - 
      P21_3 A8 - - - - Disabled - - "BSC: D2; DSMIF1: MCLK1; GPT15: GTIOC15A; IRQ: NMI; MTU36: MTIOC6C; PHOSTIF: HD2; SCI5: SDA5; SCI5: TXD_MOSI5; SPI3: SPI_SSL33; TRACE: TRACEDATA2" - None - - 
      P21_4 E7 - - - - Disabled - - "BSC: D3; DSMIF1: MDAT1; ETHER_ESC: ESC_SYNC0; ETHER_ESC: ESC_SYNC1; ETHER_ETHSW: ETHSW_PTPOUT1; GPT15: GTIOC15B; MBXSEM: MBX_HINT#; MTU36: MTIOC6D; PHOSTIF: HD3; SCI5: CTS_RTS_SS5#; SPI0: SPI_SSL02; TRACE: TRACEDATA3" - None - - 
      P21_5 C7 - - - - Disabled - - "ADC1: ADTRG1#; BSC: D4; CMTW1: CMTW1_TOC1; DSMIF2: MCLK2; GPT16: GTIOC16A; IRQ: IRQ6; MTU37: MTIOC7A; PHOSTIF: HD4; SCI5: CTS5#; SPI0: SPI_MISO0; TRACE: TRACEDATA4" - None - - 
      P21_6 D7 - - - - Disabled - - "BSC: D5; DMAC: TEND; DSMIF2: MDAT2; GPT16: GTIOC16B; IRQ: IRQ9; MTU37: MTIOC7B; PHOSTIF: HD5; SCI0: CTS0#; TRACE: TRACEDATA5" - None - - 
      P21_7 B7 - - - - Disabled - - "BSC: D6; DMAC: DREQ; DSMIF3: MCLK3; GPT17: GTIOC17A; IRQ: IRQ10; MTU37: MTIOC7C; PHOSTIF: HD6; SCI0: DE0; TRACE: TRACEDATA6" - None - - 
      P22_0 A7 - - - - Disabled - - "BSC: D7; DSMIF3: MDAT3; GPT17: GTIOC17B; IRQ: IRQ15; MTU37: MTIOC7D; PHOSTIF: HD7; SCI5: DE5; TRACE: TRACEDATA7" - None - - 
      P22_1 A6 - - - - Disabled - - "BSC: D8; ETHER_ESC: ESC_LINKACT2; GPT_POEG: GTETRGB; MTU_POE3: POE4#; PHOSTIF: HD8; SCI4: CTS_RTS_SS4#; TRACE: TRACECTL" - None - - 
      P22_2 C6 - - - - Disabled - - "BSC: D9; DSMIF1: MCLK1; GPT_POEG: GTETRGSA; IRQ: IRQ4; MTU38: MTIOC8C; PHOSTIF: HD9; SPI1: SPI_SSL12; TRACE: TRACECLK" - None - - 
      P22_3 B6 - - - - Disabled - - "BSC: D10; GPT_POEG: GTETRGSB; MTU38: MTIOC8D; PHOSTIF: HD10; SCI5: RXD_MISO5; SCI5: SCL5" - None - - 
      P23_7 D6 - - - - Disabled - - "BSC: BS#; BSC: D11; DSMIF4: MCLK4; ETHER_ETH2: ETH2_RXD0; GPT_POEG: GTETRGA; MTU30: MTIOC0A; PHOSTIF: HD11; SCI1: SCK1" - None - - 
      P24_0 A5 - - - - Disabled - - "BSC: CKE; BSC: D12; DMAC: DREQ; DSMIF4: MDAT4; ETHER_ETH2: ETH2_RXD1; GPT_POEG: GTETRGB; MTU30: MTIOC0B; PHOSTIF: HD12; SCI1: RXD_MISO1; SCI1: SCL1" - None - - 
      P24_1 B5 - - - - Disabled - - "BSC: CAS#; BSC: D13; DSMIF5: MCLK5; ETHER_ETH2: ETH2_RXCLK_REF_CLK_RXC; GPT_POEG: GTETRGC; MTU30: MTIOC0C; MTU_POE3: POE8#; PHOSTIF: HD13" - None - - 
      P24_2 C5 - - - - Disabled - - "BSC: D14; BSC: RAS#; DSMIF5: MDAT5; ETHER_ETH2: ETH2_RXD2; GPT_POEG: GTETRGD; MTU30: MTIOC0D; PHOSTIF: HD14; SCI1: SDA1; SCI1: TXD_MOSI1" - None - - 
      RES# P6 SYSTEM_RES# - - - - - - - - I "Read only" - 
      TRST# E2 SYSTEM_TRST# - - - - - - - - I "Read only" - 
      USB_DM P13 SYSTEM_USB_DM - - - - - - - - IO "Read only" - 
      USB_DP R13 SYSTEM_USB_DP - - - - - - - - IO "Read only" - 
      USB_RREF P15 SYSTEM_USB_RREF - - - - - - - - I "Read only" - 
      VCC1833_0 L6 SYSTEM_VCC1833_0 - - - - - - - - I "Read only" - 
      VCC1833_1 K5 SYSTEM_VCC1833_1 - - - - - - - - I "Read only" - 
      VCC1833_2 E5 SYSTEM_VCC1833_2 - - - - - - - - I "Read only" - 
      VCC1833_3 J11 SYSTEM_VCC1833_3 - - - - - - - - I "Read only" - 
      VCC1833_4 F11 SYSTEM_VCC1833_4 - - - - - - - - I "Read only" - 
      VCC18_ADC0 E11 SYSTEM_VCC18_ADC0 - - - - - - - - I "Read only" - 
      VCC18_ADC1 E9 SYSTEM_VCC18_ADC1 - - - - - - - - I "Read only" - 
      VCC18_PLL0 P9 SYSTEM_VCC18_PLL0 - - - - - - - - I "Read only" - 
      VCC18_PLL1 N9 SYSTEM_VCC18_PLL1 - - - - - - - - I "Read only" - 
      VCC18_USB P11 SYSTEM_VCC18_USB - - - - - - - - I "Read only" - 
      VCC33 E6 SYSTEM_VCC33 - - - - - - - - I "Read only" - 
      VCC33 E8 SYSTEM_VCC33 - - - - - - - - I "Read only" - 
      VCC33 M10 SYSTEM_VCC33 - - - - - - - - I "Read only" - 
      VCC33 L5 SYSTEM_VCC33 - - - - - - - - I "Read only" - 
      VCC33_USB R11 SYSTEM_VCC33_USB - - - - - - - - I "Read only" - 
      VDD H10 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD G10 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD F6 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD F8 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD F9 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD G6 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD F10 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD H6 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD J6 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD K6 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD K7 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD K8 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD K10 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD P7 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VDD J10 SYSTEM_VDD - - - - - - - - I "Read only" - 
      VREFH0 C11 SYSTEM_VREFH0 - - - - - - - - I "Read only" - 
      VREFH1 C10 SYSTEM_VREFH1 - - - - - - - - I "Read only" - 
      VSS A1 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS R1 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS A10 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS R5 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS A15 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS R9 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS F7 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS G7 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS G8 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS G9 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS N10 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS H7 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS N14 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS H8 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS H9 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS E10 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS J7 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS J8 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS J9 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS K9 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS D10 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS D11 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS L1 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS L15 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS P8 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS R15 SYSTEM_VSS - - - - - - - - I "Read only" - 
      VSS_ADC D12 SYSTEM_VSS_ADC - - - - - - - - I "Read only" - 
      VSS_USB P12 SYSTEM_VSS_USB - - - - - - - - I "Read only" - 
      VSS_USB P14 SYSTEM_VSS_USB - - - - - - - - I "Read only" - 
      VSS_USB N13 SYSTEM_VSS_USB - - - - - - - - I "Read only" - 
      VSS_USB N15 SYSTEM_VSS_USB - - - - - - - - I "Read only" - 
      VSS_USB R12 SYSTEM_VSS_USB - - - - - - - - I "Read only" - 
      VSS_USB R14 SYSTEM_VSS_USB - - - - - - - - I "Read only" - 
      XTAL R8 CGC_XTAL - - - - - - - - O "Read only" - 
    
  User Events
    
  User Event Links
    
  Module "I/O Port Driver on r_ioport"
    Parameter Checking: Default (BSP)
    
  Module "QSPI Driver on r_xspi_qspi"
    Parameter Checking Enable: Default (BSP)
    
  Module "USB PCDC driver on r_usb_pcdc"
    Bulk In Pipe: USB PIPE1
    Bulk Out Pipe: USB PIPE2
    Interrupt Out Pipe: USB PIPE6
    
  Module "USB Driver on r_usb_basic"
    Parameter Checking: Default (BSP)
    CPU Bus Access Wait Cycles: 9 cycles
    Power IC Shutdown Polarity: Active High
    Notifications for SET_INTERFACE/SET_FEATURE/CLEAR_FEATURE: Enabled
    Double Buffering: Enabled
    Continuous Transfer Mode: Disabled
    DMA Support: Disable
    
  HAL
    Instance "g_ioport I/O Port Driver on r_ioport"
      General: Name: g_ioport
      ELC Output Port Group 1: Trigger Source: Disabled
      ELC Output Port Group 1: Port Selection: 
      ELC Output Port Group 1: Output Operation: Low output
      ELC Output Port Group 2: Trigger Source: Disabled
      ELC Output Port Group 2: Port Selection: 
      ELC Output Port Group 2: Output Operation: Low output
      ELC Input Port Group 1: Trigger Source: Disabled
      ELC Input Port Group 1: Event Link Control: Disabled
      ELC Input Port Group 1: Port Selection: 
      ELC Input Port Group 1: Edge Detection: Rising edge
      ELC Input Port Group 1: Buffer Overwrite: Disabled
      ELC Input Port Group 1: Buffer Initial Value: P16_0: Low input
      ELC Input Port Group 1: Buffer Initial Value: P16_1: Low input
      ELC Input Port Group 1: Buffer Initial Value: P16_2: Low input
      ELC Input Port Group 1: Buffer Initial Value: P16_3: Low input
      ELC Input Port Group 1: Buffer Initial Value: P16_5: Low input
      ELC Input Port Group 1: Buffer Initial Value: P16_6: Low input
      ELC Input Port Group 1: Buffer Initial Value: P16_7: Low input
      ELC Input Port Group 2: Trigger Source: Disabled
      ELC Input Port Group 2: Event Link Control: Disabled
      ELC Input Port Group 2: Port Selection: 
      ELC Input Port Group 2: Edge Detection: Rising edge
      ELC Input Port Group 2: Buffer Overwrite: Disabled
      ELC Input Port Group 2: Buffer Initial Value: P18_0: Low input
      ELC Input Port Group 2: Buffer Initial Value: P18_1: Low input
      ELC Input Port Group 2: Buffer Initial Value: P18_2: Low input
      ELC Input Port Group 2: Buffer Initial Value: P18_3: Low input
      ELC Input Port Group 2: Buffer Initial Value: P18_4: Low input
      ELC Input Port Group 2: Buffer Initial Value: P18_5: Low input
      ELC Input Port Group 2: Buffer Initial Value: P18_6: Low input
      ELC Single Port 0: Common: Event Link Control: Disabled
      ELC Single Port 0: Common: Event Direction: Output direction
      ELC Single Port 0: Common: Port selection: P16_0
      ELC Single Port 0: Output Direction Setting: Trigger Source: Disabled
      ELC Single Port 0: Output Direction Setting: Output Operation: Low output
      ELC Single Port 0: Input Direction Setting: Edge Detection: Rising edge
      ELC Single Port 1: Common: Event Link Control: Disabled
      ELC Single Port 1: Common: Event Direction: Output direction
      ELC Single Port 1: Common: Port selection: P16_0
      ELC Single Port 1: Output Direction Setting: Trigger Source: Disabled
      ELC Single Port 1: Output Direction Setting: Output Operation: Low output
      ELC Single Port 1: Input Direction Setting: Edge Detection: Rising edge
      ELC Single Port 2: Common: Event Link Control: Disabled
      ELC Single Port 2: Common: Event Direction: Output direction
      ELC Single Port 2: Common: Port selection: P16_0
      ELC Single Port 2: Output Direction Setting: Trigger Source: Disabled
      ELC Single Port 2: Output Direction Setting: Output Operation: Low output
      ELC Single Port 2: Input Direction Setting: Edge Detection: Rising edge
      ELC Single Port 3: Common: Event Link Control: Disabled
      ELC Single Port 3: Common: Event Direction: Output direction
      ELC Single Port 3: Common: Port selection: P16_0
      ELC Single Port 3: Output Direction Setting: Trigger Source: Disabled
      ELC Single Port 3: Output Direction Setting: Output Operation: Low output
      ELC Single Port 3: Input Direction Setting: Edge Detection: Rising edge
      
    Instance "g_qspi1 QSPI Driver on r_xspi_qspi"
      General: Name: g_qspi1
      General: unit: 1
      General: Chip Select: Chip Select 0
      General: Flash Size: 16MB
      General: SPI Protocol: 1S-1S-1S
      General: Address Bytes: 3
      General: Dummy Clocks for Read: 0
      General: Prefetch Function: Enable
      Command Definitions: Page Program Command: 0x02
      Command Definitions: Read Command: 0x03
      Command Definitions: Write Enable Command: 0x06
      Command Definitions: Status Command: 0x05
      Command Definitions: Write Status Bit: 0
      Command Definitions: Sector Erase Command: 0x20
      Command Definitions: Sector Erase Size: 4096
      Command Definitions: Block Erase Command: 0xD8
      Command Definitions: Block Erase Size: 65536
      Command Definitions: Block Erase 32KB Command: 0x52
      Command Definitions: Block Erase 32KB Size: 32768
      Command Definitions: Chip Erase Command: 0xC7
      Command Definitions: XIP Enter M7-M0: 0x20
      Command Definitions: XIP Exit M7-M0: 0xFF
      Bus Timing: CS minimum idle term: 7 CYCLES
      Bus Timing: CS asserting extension: No Extension
      Bus Timing: CS negating extension: No Extension
      
    Instance "g_pcdc0 USB PCDC driver on r_usb_pcdc"
      Name: g_pcdc0
      
      Instance "g_basic0 USB Driver on r_usb_basic"
        Name: g_basic0
        USB Mode: Peri mode
        USB Speed: Hi Speed
        USB Device Class: Peripheral Communications Device Class
        USB Descriptor: g_usb_descriptor
        USB Compliance Callback: NULL
        USBFS Interrupt Priority: Priority 12
        USBFS Resume Priority: Priority 12
        USBFS D0FIFO Interrupt Priority: Priority 12
        USBFS D1FIFO Interrupt Priority: Priority 12
        USB RTOS Callback: NULL
        USB Callback Context: NULL
        
