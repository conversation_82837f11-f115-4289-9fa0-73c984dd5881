/****************************************************************************************************
 *
 * FILE NAME:  ProgramJog.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.22
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	22-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _PROGRAM_JOG_H_
#define _PROGRAM_JOG_H_

#include "Bprm.h"
#include "BaseCmnStruct.h"
#include "Alarm.h"
#include "Mlib.h"

/************************************************************************************************/
/*																								*/
/*		Data definition for program JOG operation												*/
/*																								*/
/************************************************************************************************/
/*----------------------------------------------------------------------------------------------*/
/*		Program JOG operation data structure													*/
/*----------------------------------------------------------------------------------------------*/
typedef	struct 
{
	struct
	{						
		INT32	Distance;			/* Program JOG operation target position [command unit]		*/
		INT32	MaxSpd;				/* Program JOG operation maximum speed [2^24/OvrSpd]		*/
		INT32	AccTime;			/* Program JOG operation acceleration/deceleration time [us]  */
		UINT16	WaitTime;			/* Wait time [ms]											*/
		UINT16	MTimes;				/* Number of transfers										*/
		INT8	Pattern;			/* Program JOG operation command pattern					*/
		INT8	RevDir;				/* Program JOG operation mode start direction				*/
		INT8	PrmUnMatch;			/* Parameter combination error status						*/
		INT8	PrmUnMatch2;		/* Parameter combination error status 2						*/
	} Cnst;

	struct
	{	
		INT32	AbsPosCmd;			/*Program JOG position command [command unit]				*/
		UINT32	TimeWork;			/* Data for waiting time measurement						*/
		UINT16	DispTime;			/* Paneopepe display data									*/
		UINT16	MCntr;				/* Number of repetitions									*/
		UINT16	SeqPtr;				/* Program JOG operation command creation sequence pointe	*/
		UINT8	RoutCmd;			/* Program JOG operation command	(Round  -> task C)		*/
		UINT8	CoutCmd;			/* Program JOG operation command	(task C -> task B)		*/
		UINT8	State;				/* Program JOG operation status								*/
		BOOL	TaskCRunFlg;		/* Flag that turns on with taskC and turns off with Round (interrupt substitute)*/
		UINT16  Step;   		    /* Program JOG operation step */
		UINT16  PnPrgJogCmd;        /* Pn Program JOG Command */
        BOOL	PnPrgJogFlg;        
	} Var;
} PJOGV;

/*----------------------------------------------------------------------------------------------*/
/*		Program JOG structure																		*/
/*----------------------------------------------------------------------------------------------*/
typedef	struct 
{
	PJOGV   PJogV;
	PCMKPV	PJogPcmk;
	INT16	PJogState;
}PJOGHNDL;

/*----------------------------------------------------------------------------------------------*/
/*		Program JOG operation status definition													*/
/*----------------------------------------------------------------------------------------------*/
#define	PJOG_INIT	0x00				/* Initial state										*/
#define	PJOG_ABORT	0x10				/* Suspended state										*/
#define	PJOG_START	0x01				/* Operating condition									*/
#define	PJOG_AFTER	0x11				/* Post-processing state								*/
#define	PJOG_END	0x02				/* Termination status									*/
#define	PJOG_ERROR	0x03				/* Abnormal termination									*/
/*----------------------------------------------------------------------------------------------*/
/*		Program JOG operation command definition												*/
/*----------------------------------------------------------------------------------------------*/
#define	PJOGCMD_NONE	0x00			/* No command											*/
#define	PJOGCMD_INIT	0x01			/* Initialization										*/
#define	PJOGCMD_ABORT	0x02			/* Interruption											*/
#define	PJOGCMD_START	0x03			/* operation											*/

#define	LMTLOWSPEED	10
/*----------------------------------------------------------------------------------------------*/
/*		Program JOG operation related function definition										*/
/*----------------------------------------------------------------------------------------------*/
void PrgJogReset( PJOGHNDL *PJogHdl );
void IprmcalPrgJog( PJOGHNDL *PJogHdl, BPRMDAT *Bprm );
void CpxPrgJogPosSequence( PJOGHNDL *PJogHdl, SEQ_CTRL_OUT *SeqCtrlOut, 
														ALARM *AlmManager, INT32 FnCtrlMcmd ); 
BOOL PrgJogMakePositionReference( PJOGHNDL *PJogHdl, INT32 *dPosRefo );

#endif

