/****************************************************************************************************
 *
 * FILE NAME:  Cia402Appl.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.24
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	24-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "Mlib.h"
#include "BaseLoops.h"
#include "Encoder.h"
#include "ComUart.h"
extern ComUartStruct  CommandSend;
extern UINT16 SendMark;
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void Cia402_Init(TCiA402Axis *Cia402Axis, CiA402_PRM *Cia402Prm)
{
	/*Reset Axis buffer*/
	MlibResetByteMemory( Cia402Axis, sizeof(TCiA402Axis));

	Cia402Axis->State = STATE_NOT_READY_TO_SWITCH_ON;
	Cia402Axis->DcCmdCode = 0x00;
	
	Cia402Axis->Objects = Cia402Prm;

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
#pragma optimize=none
PUBLIC BOOL Cia402_StateMachine(BASE_LOOP *BaseLoops, AUX_PRM  *AuxPrm,BOOL PowerOn)
{
	TCiA402Axis           *Cia402Axis;
        ENCODER               *pEnc;
	volatile CIA402_STATUS_WORD    StatusWord = {0};
	volatile CIA402_CTRL_WORD      ControlWord = {0};
	volatile CIA402_CTRL_WORD      LastCtrlWord = {0};

        
	Cia402Axis = &BaseLoops->BaseCtrls->Cia402Axis;
	StatusWord.all = Cia402Axis->Objects->Statusword0x6041;
	ControlWord.all = Cia402Axis->Objects->Controlword0x6040;
	LastCtrlWord.all = Cia402Axis->LastCtrlWord;

        
        pEnc = BaseLoops->Enc;
    
    
    
        BOOL  QkStop = Cia402Axis->ExtraCtrl.bit.FstopIn;
        BOOL  FnSvonReq = Cia402Axis->ExtraCtrl.bit.FnSvonReq;
        BOOL  DiSvonReq = Cia402Axis->ExtraCtrl.bit.DiServoOn;
        BOOL  BeReqoff   = Cia402Axis->ExtraCtrl.bit.BeReqSeqoff;

	ControlWord.bit.QkStop &= QkStop;
		
	switch(Cia402Axis->State)
	{
	case STATE_NOT_READY_TO_SWITCH_ON:
            StatusWord.bit.ReadySwitchOn = 0;
            StatusWord.bit.SwitchednOn = 0;
            StatusWord.bit.OperEnabled = 0;
            StatusWord.bit.Fault = 0;
            StatusWord.bit.VoltageEnabled = 0;
            StatusWord.bit.QkStop = 0;
            StatusWord.bit.SwitchednOnDisabled = 0;
            StatusWord.bit.FnCtrlMode = 0;
            Cia402Axis->Cia402BeReq = FALSE;
            Cia402Axis->State = STATE_SWITCH_ON_DISABLED;
            break;
			
	case STATE_SWITCH_ON_DISABLED:
            StatusWord.bit.ReadySwitchOn = 0;
            StatusWord.bit.SwitchednOn = 0;
            StatusWord.bit.OperEnabled = 0;
            StatusWord.bit.Fault = 0;
            StatusWord.bit.VoltageEnabled = PowerOn;
            StatusWord.bit.QkStop = 0;
            StatusWord.bit.SwitchednOnDisabled = 1;
            StatusWord.bit.FnCtrlMode = 0;
            Cia402Axis->Cia402BeReq = FALSE;
            if((ControlWord.bit.QkStop && ControlWord.bit.EnVoltage && !ControlWord.bit.SwitchON) || (FnSvonReq == TRUE) )
            {
                if(FnSvonReq == TRUE) 
                {
                        StatusWord.bit.FnCtrlMode = 1;
                }
                
                Cia402Axis->State = STATE_READY_TO_SWITCH_ON;
                Cia402Axis->DcCmdCode = DC_SHUTDOWM;
            }
		break;

	case STATE_READY_TO_SWITCH_ON:
        StatusWord.bit.ReadySwitchOn = 1;
        StatusWord.bit.SwitchednOn = 0;
        StatusWord.bit.OperEnabled = 0;
        StatusWord.bit.Fault = 0;
		StatusWord.bit.VoltageEnabled = PowerOn;
        StatusWord.bit.QkStop = 1;
        StatusWord.bit.SwitchednOnDisabled = 0;
		StatusWord.bit.FnCtrlMode = 0;
		Cia402Axis->Cia402BeReq = FALSE;
		if((ControlWord.bit.QkStop && ControlWord.bit.EnVoltage && ControlWord.bit.SwitchON )|| (FnSvonReq == TRUE)  )
		{
			if(FnSvonReq == TRUE) 
			{
				StatusWord.bit.FnCtrlMode = 1;
			}
			
			Cia402Axis->State = STATE_SWITCHED_ON;
			Cia402Axis->DcCmdCode = DC_SWITCH_ON;
		}
		else if(!ControlWord.bit.EnVoltage)
		{
			Cia402Axis->State = STATE_SWITCH_ON_DISABLED;
			Cia402Axis->DcCmdCode = DC_DISABLE_VOLTAGE;
		}
		else if(!ControlWord.bit.QkStop && ControlWord.bit.EnVoltage)
		{
			Cia402Axis->State = STATE_SWITCH_ON_DISABLED;
			Cia402Axis->DcCmdCode = DC_QUICK_STOP;
		}
		break;

	case STATE_SWITCHED_ON:
        StatusWord.bit.ReadySwitchOn = 1;
        StatusWord.bit.SwitchednOn = 1;
        StatusWord.bit.OperEnabled = 0;
        StatusWord.bit.Fault = 0;
		StatusWord.bit.VoltageEnabled = PowerOn;
        StatusWord.bit.QkStop = 1;
        StatusWord.bit.SwitchednOnDisabled = 0;
		StatusWord.bit.FnCtrlMode = 0;
		Cia402Axis->Cia402BeReq = FALSE;
		if((ControlWord.bit.EnOper && ControlWord.bit.QkStop && ControlWord.bit.EnVoltage && ControlWord.bit.SwitchON)
																	|| (FnSvonReq == TRUE) || (DiSvonReq == TRUE) )
		{
			if(FnSvonReq == TRUE) 
			{
				StatusWord.bit.FnCtrlMode = 1;
			}
			Cia402Axis->Cia402BeReq = TRUE;
            Cia402Axis->StopCode = 0;
			if(BaseLoops->BaseCtrls->BaseEnable && PowerOn)
			{
				Cia402Axis->State = STATE_OPERATION_ENABLED;
				Cia402Axis->DcCmdCode = DC_ENABLE_OPERATION;
			}
		}
		else if(ControlWord.bit.QkStop && ControlWord.bit.EnVoltage && !ControlWord.bit.SwitchON)
		{
			Cia402Axis->State = STATE_READY_TO_SWITCH_ON;
			Cia402Axis->DcCmdCode = DC_SHUTDOWM;
		}
		else if(!ControlWord.bit.EnVoltage)
		{
			Cia402Axis->State = STATE_SWITCH_ON_DISABLED;
			Cia402Axis->DcCmdCode = DC_DISABLE_VOLTAGE;
		}
		else if(!ControlWord.bit.QkStop && ControlWord.bit.EnVoltage)
		{
			Cia402Axis->State = STATE_SWITCH_ON_DISABLED;
			Cia402Axis->DcCmdCode = DC_QUICK_STOP;
		}
		break;
		
	case STATE_OPERATION_ENABLED:
        StatusWord.bit.ReadySwitchOn = 1;
        StatusWord.bit.SwitchednOn = 1;
        StatusWord.bit.OperEnabled = 1;
        StatusWord.bit.Fault = 0;
		StatusWord.bit.VoltageEnabled = PowerOn;
        StatusWord.bit.QkStop = 1;
        StatusWord.bit.SwitchednOnDisabled = 0;
		StatusWord.bit.FnCtrlMode = 0;
		Cia402Axis->Cia402BeReq = TRUE;
//        if(!ControlWord.bit.Halt)
//        {
//            Cia402Axis->StopCode = 0;
//        }
		
		if(FnSvonReq == TRUE) 
		{
			StatusWord.bit.FnCtrlMode = 1;
		}
        else if(DiSvonReq == TRUE)
        {
            // do nothing 
        }
		else if(!ControlWord.bit.EnOper && ControlWord.bit.QkStop && ControlWord.bit.EnVoltage && ControlWord.bit.SwitchON)
		{
			Cia402Axis->DcCmdCode = DC_DISABLE_OPERATION;
/*            
			if(Cia402Axis->Objects->DisableOperationOptionCode0x605C )
			{
				Cia402Axis->u16PendingOptionCode = 0x605C;
				break;
			}
*/
			Cia402Axis->Cia402BeReq = FALSE;
			if(BaseLoops->BaseCtrls->BaseEnable == FALSE)
			{
				Cia402Axis->State = STATE_SWITCHED_ON;
			}
		}
		else if(ControlWord.bit.QkStop && ControlWord.bit.EnVoltage && !ControlWord.bit.SwitchON)
		{				
			Cia402Axis->DcCmdCode = DC_SHUTDOWM;
/*            
			if(Cia402Axis->Objects->ShutdownOptionCode0x605B )
			{
				Cia402Axis->u16PendingOptionCode = 0x605B;
				break;
			}
*/			
			Cia402Axis->Cia402BeReq = FALSE;
			if(BaseLoops->BaseCtrls->BaseEnable == FALSE)
			{
				Cia402Axis->State = STATE_READY_TO_SWITCH_ON;
			}
		}
		else if(!ControlWord.bit.EnVoltage)
		{
			Cia402Axis->DcCmdCode = DC_DISABLE_VOLTAGE;

			Cia402Axis->Cia402BeReq = FALSE;
			if(BaseLoops->BaseCtrls->BaseEnable == FALSE)
			{
				Cia402Axis->State = STATE_SWITCH_ON_DISABLED;		
			}
		}
		else if (!ControlWord.bit.QkStop && ControlWord.bit.EnVoltage)
		{
			Cia402Axis->State = STATE_QUICK_STOP_ACTIVE;
			Cia402Axis->DcCmdCode = DC_QUICK_STOP;
		}
        else if (ControlWord.bit.Halt)
		{
			Cia402Axis->DcCmdCode = DC_ENABLE_OPERATION;
			if(Cia402Axis->Objects->HaltOptionCode0x605D )
			{
				Cia402Axis->u16PendingOptionCode = 0x605D;
				break;
			}
		}
		break;
		
	case STATE_QUICK_STOP_ACTIVE:
        StatusWord.bit.ReadySwitchOn = 1;
        StatusWord.bit.SwitchednOn = 1;
        StatusWord.bit.OperEnabled = 1;
        StatusWord.bit.Fault = 0;
		StatusWord.bit.VoltageEnabled = PowerOn;
        StatusWord.bit.QkStop = 0;
        StatusWord.bit.SwitchednOnDisabled = 0;
		StatusWord.bit.FnCtrlMode = 0;
		Cia402Axis->Cia402BeReq = TRUE;
		if(Cia402Axis->Objects->QuickstopOptionCode0x605A ) 
		{
			Cia402Axis->DcCmdCode = DC_ENABLE_OPERATION;
			Cia402Axis->u16PendingOptionCode = 0x605A;
			break;
		}
        break;
#if 0
        if(!ControlWord.bit.EnVoltage )
		{
			Cia402Axis->DcCmdCode = DC_DISABLE_VOLTAGE;

			Cia402Axis->Cia402BeReq = FALSE;
			if(BaseLoops->BaseCtrls->BaseEnable == FALSE)
			{
				Cia402Axis->State = STATE_SWITCH_ON_DISABLED;	
			}
		}
		break;
#endif        
		
	case STATE_FAULT_REACTION_ACTIVE:
        StatusWord.bit.ReadySwitchOn = 1;
        StatusWord.bit.SwitchednOn = 1;
        StatusWord.bit.OperEnabled = 1;
        StatusWord.bit.Fault = 1;
		StatusWord.bit.VoltageEnabled = PowerOn;
        StatusWord.bit.QkStop = 0;
        StatusWord.bit.SwitchednOnDisabled = 0;
		StatusWord.bit.FnCtrlMode = 0;
		Cia402Axis->Cia402BeReq = TRUE;
		if(Cia402Axis->Objects->FaultReactionCode0x605E ) 
		{
			Cia402Axis->u16PendingOptionCode = 0x605E;
			break;
		}
        break;
        #if 0
		Cia402Axis->Cia402BeReq = FALSE;
		if(BaseLoops->BaseCtrls->BaseEnable == FALSE)
		{
			Cia402Axis->State = STATE_FAULT;
		}
		break;
        #endif        
		
	case STATE_FAULT:
        StatusWord.bit.ReadySwitchOn = 0;
        StatusWord.bit.SwitchednOn = 0;
        StatusWord.bit.OperEnabled = 0;
        StatusWord.bit.Fault = 1;
		StatusWord.bit.VoltageEnabled = 0;
        StatusWord.bit.QkStop = 0;
        StatusWord.bit.SwitchednOnDisabled = 0;
		StatusWord.bit.FnCtrlMode = 0;
		Cia402Axis->Cia402BeReq = FALSE;
		if ((ControlWord.bit.RstAlm && !LastCtrlWord.bit.RstAlm)
					|| Cia402Axis->AlmRstCmd)
		{
			Cia402Axis->State = STATE_SWITCH_ON_DISABLED;
			Cia402Axis->DcCmdCode = DC_DISABLE_VOLTAGE;
            Cia402Axis->Objects->ErrorCode0x603F = 0;
			BaseLoops->BaseCtrls->CtrlCmdBit.bits.ClrAlm = 1;
			Cia402Axis->AlmRstCmd = 0;					
		}
		break;
		
	default:
		break;
	}


    if(ControlWord.bit.ClrEncMultTurnAndErr && !LastCtrlWord.bit.ClrEncMultTurnAndErr && 
															!BaseLoops->BaseCtrls->BaseEnable)	
    {
          pEnc->V.ExCmdEn = TRUE;
          pEnc->V.ExCmd = ENC_CLR_MULTITURNANDERR;
    }
 
    if(ControlWord.bit.ClrEnc2 && !LastCtrlWord.bit.ClrEnc2 && 
															!BaseLoops->BaseCtrls->BaseEnable)	
    {
          pEnc->V1.ExCmdEn = TRUE;
          pEnc->V1.ExCmd   = ENC_CLR_MULTITURNANDERR;
    }    

	StatusWord.bit.Warning = BaseLoops->AlmMngr->Status.WrnFlag;
	StatusWord.bit.Remote = 1;
	Cia402Axis->Objects->Statusword0x6041 = StatusWord.all;

	Cia402Axis->LastCtrlWord = ControlWord.all;
	
	if(Cia402Axis->Objects->ModesOfOperationDisplay0x6061 != PROFILE_POSITION_MODE)
	{
		Cia402Axis->LastCtrlWordForMode = ControlWord.all;
	}
    if(ControlWord.bit.RstEtherCAT && !LastCtrlWord.bit.RstEtherCAT
          && !BaseLoops->BaseCtrls->BaseEnable)	
    {
            ComUartStruct  *Send     = &CommandSend;
            Send->StartFlag = 0xAA;
            Send->CommandLenLow = 0x0E;
            Send->CommandLenHigh = 0x00;
            Send->DeviceAddr = 0xFF;
            Send->FunctionCode = 0x0E;
            Send->Buf[0] = 0x01;
            Send->Buf[1] = 0x00;
            Send->Buf[2] = 0x10;
            Send->Buf[3] = 0x01;
            Send->Buf[4] = 0x07;
            Send->Buf[5] = 0x20;
            Send->Buf[6] = 0x01;
            Send->Buf[7] = 0x00;
            Send->Buf[8] = 0xF4;
            Send->Buf[9] = 0x0B;
            Send->Buf[10] = 0x55;
            SendMark = 1;
          
    }    
	
    Cia402Axis->AlmRstCmd = 0;
    
    return StatusWord.bit.OperEnabled;
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 CiA402_StopControl(TCiA402Axis *Cia402Axis, BOOL MotStop)
{

    switch(Cia402Axis->u16PendingOptionCode)
    {
    case 0x605A:
        /*state transition 11 is pending analyse shutdown option code (0x605A)*/
        {
            Cia402Axis->StopCode = Cia402Axis->Objects->QuickstopOptionCode0x605A;
            /*masked and execute specified quick stop ramp characteristic */
            if(Cia402Axis->Objects->QuickstopOptionCode0x605A > 4 
				                     && Cia402Axis->Objects->QuickstopOptionCode0x605A <9)
            {
            	Cia402Axis->StopCode = Cia402Axis->Objects->QuickstopOptionCode0x605A - 4;
            }

            if(MotStop)
            {
                /*quick stop ramp is finished complete state transition*/
                Cia402Axis->u16PendingOptionCode = 0x0;
				//Cia402Axis->StopCode = 0;
                if(Cia402Axis->Objects->QuickstopOptionCode0x605A > 0
					                     && Cia402Axis->Objects->QuickstopOptionCode0x605A < 5)
                {
                    Cia402Axis->State = STATE_SWITCH_ON_DISABLED;    //continue state transition 12
                }
                else if(Cia402Axis->Objects->QuickstopOptionCode0x605A > 4 
					                         && Cia402Axis->Objects->QuickstopOptionCode0x605A < 9)
                {
                    Cia402Axis->Objects->Statusword0x6041 |= STATUSWORD_TARGET_REACHED;
                    Cia402Axis->Objects->Statusword0x6041 &= ~STATUSWORD_STATE_MASK;
                    Cia402Axis->Objects->Statusword0x6041 |= STATUSWORD_STATE_QUICKSTOPACTIVE;
                }
            }
        }
        break;
    case 0x605B:
        /*state transition 8 is pending analyse shutdown option code (0x605B)*/
        {
        	Cia402Axis->StopCode = Cia402Axis->Objects->ShutdownOptionCode0x605B;
			
            if(MotStop)
            {
                /*shutdown ramp is finished complete state transition*/
                Cia402Axis->u16PendingOptionCode = 0x0;
				//Cia402Axis->StopCode = 0;
                Cia402Axis->State = STATE_READY_TO_SWITCH_ON;    //continue state transition 8
            }
        }
        break;
    case 0x605C:
        /*state transition 5 is pending analyse Disable operation option code (0x605C)*/
        {
        	Cia402Axis->StopCode = Cia402Axis->Objects->DisableOperationOptionCode0x605C;
		
            if(MotStop)
            {
                /*disable operation ramp is finished complete state transition*/
                Cia402Axis->u16PendingOptionCode = 0x0;
				//Cia402Axis->StopCode = 0;
                Cia402Axis->State = STATE_SWITCHED_ON;    //continue state transition 5
            }
        }
        break;
      case 0x605D:
        {
        	Cia402Axis->StopCode = Cia402Axis->Objects->HaltOptionCode0x605D;
            if(MotStop)
            {
                Cia402Axis->u16PendingOptionCode = 0x0;
				//Cia402Axis->StopCode = 0;
                if(!(Cia402Axis->Objects->Controlword0x6040&0x100))
                {
                    Cia402Axis->StopCode = 0;
                }
            }
        }
        break;
    case 0x605E:
        /*state transition 14 is pending analyse Fault reaction option code (0x605E)*/
        {
        	Cia402Axis->StopCode = Cia402Axis->Objects->FaultReactionCode0x605E;
			
            //if(MotStop || Cia402Axis->StopMode == 0xF)
			if(MotStop)
            {
                /*fault reaction ramp is finished complete state transition*/
                Cia402Axis->u16PendingOptionCode = 0x0;
				//Cia402Axis->StopCode = 0;
                Cia402Axis->State = STATE_FAULT;    //continue state transition 14
            }
        }
        break;
    default:
        //pending transition code is invalid => values from the master are used
//        CiA402Axis->Objects->Statusword0x6041 |= STATUSWORD_DRIVE_FOLLOWS_COMMAND;
        break;
    }

    return Cia402Axis->StopCode;

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void Cia402_LocalError(TCiA402Axis *Cia402Axis, UINT16 ErrorCode, UINT16 StopMode)
{
	Cia402Axis->StopMode = StopMode;
		
	if(((Cia402Axis->State != STATE_FAULT_REACTION_ACTIVE) 
		&& (Cia402Axis->State != STATE_FAULT)) || Cia402Axis->LastSysPrmInit)
	{		
		Cia402Axis->Objects->ErrorCode0x603F = ErrorCode;
		if(Cia402Axis->Cia402BeReq)
		{
			Cia402Axis->State = STATE_FAULT_REACTION_ACTIVE;
		}
		else
		{
			Cia402Axis->State = STATE_FAULT;
		}	
		Cia402Axis->LastSysPrmInit	= 0;	
	}
	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 Cia402_GearRatioCal(TCiA402Axis *Cia402Axis, INT32 In )
{
    INT64 result = 0;
    INT64 temp0 = 0;
    INT64 temp1 = 0;
	UINT32 GearA = Cia402Axis->MotorRev;
	UINT32 GearB = Cia402Axis->LoadShaftRev;

	if(GearA ==0 || GearB == 0)
	{
		GearA = 1;
		GearB = 1;
	}
    
    temp0 = In;
    temp1 = temp0*GearA;
    result = (temp1/GearB);
	return (result);
}

PUBLIC REAL32 GearRatioCal(BASE_LOOP *BaseLoops, REAL32 In )
{
    REAL32 result = 0;
    REAL32 temp0 = 0;
    REAL32 temp1 = 0;
	REAL32 GearA = BaseLoops->Bprm->MotorRev;
	REAL32 GearB = BaseLoops->Bprm->LoadShaftRev;

	if(GearA ==0 || GearB == 0)
	{
		GearA = 1;
		GearB = 1;
	}
    temp0 = In;
    temp1 = temp0*GearA;
    result = (temp1/GearB);
	return (result);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 Cia402_GearRatioInvCal(TCiA402Axis *Cia402Axis, INT32 In )
{
    INT64 result = 0;
    INT64 temp0 = 0;
    INT64 temp1 = 0;
//	UINT32 GearA = Cia402Axis->Objects->MotorRevolutions0x6091;
//	UINT32 GearB = Cia402Axis->Objects->LoadShaftRevolutions0x6091;
    
	UINT32 GearA = Cia402Axis->MotorRev;
	UINT32 GearB = Cia402Axis->LoadShaftRev;    
    

	if(GearA ==0 || GearB == 0)
	{
		GearA = 1;
		GearB = 1;
	}
    
    temp0 = In;
    temp1 = temp0*GearB;
    result = (temp1/GearA);
    
	return (result);
}

PUBLIC REAL32 GearRatioInvCal(BASE_LOOP *BaseLoops, REAL32 In )
{
    REAL32 result = 0;
    REAL32 temp0 = 0;
    REAL32 temp1 = 0;
	REAL32 GearA = BaseLoops->Bprm->MotorRev;
	REAL32 GearB = BaseLoops->Bprm->LoadShaftRev;    

	if(GearA ==0 || GearB == 0)
	{
		GearA = 1;
		GearB = 1;
	}
    temp0 = In;
    temp1 = temp0*GearB;
    result = (temp1/GearA);
	return (result);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void Cia402_UpdateFeedback(BASE_LOOP *BaseLoops, UINT32 DiStatus) 
{
    CiA402_PRM	  *Objects;
    CIA402_STATUS_WORD    StatusWord = {0};
    
    Objects = BaseLoops->BaseCtrls->Cia402Axis.Objects;
    StatusWord.all = BaseLoops->BaseCtrls->Cia402Axis.Objects->Statusword0x6041;

	Objects->VelocitySensorValue0x6069 = BaseLoops->SpdObsFdb / BaseLoops->Bprm->Kmotspd;


	Objects->VelocityActualValue0x606C = Cia402_GearRatioInvCal(&BaseLoops->BaseCtrls->Cia402Axis,
								                                 Objects->VelocitySensorValue0x6069 );
    
    
	if(BaseLoops->BaseCtrls->Cia402Axis.VirtualAxisEn)
	{
        Objects->VelocitySensorValue0x6069 =  Objects->TargetVelocity0x60FF;
        Objects->VelocityActualValue0x606C =  Objects->TargetVelocity0x60FF; 
        
        Objects->PositionActualInternalvalue0x6063 =  Objects->TargetPosition0x607A;
        Objects->PositionActualValue0x6064 = Objects->TargetPosition0x607A;
        Objects->TorqueActualValue0x6077 = Objects->TargetTorque0x6071;          
	}
	else
	{
        Objects->VelocitySensorValue0x6069 = BaseLoops->SpdObsFdb / BaseLoops->Bprm->Kmotspd;
		

		
		Objects->VelocityActualValue0x606C = Cia402_GearRatioInvCal(&BaseLoops->BaseCtrls->Cia402Axis,
                                                                     Objects->VelocitySensorValue0x6069 );        
	
        
        Objects->PositionActualInternalvalue0x6063 = BaseLoops->PosFdb;

         if(BaseLoops->UseEncoder2)
         {
            Objects->PositionActualValue0x6064 = Objects->PositionActualInternalvalue0x6063 ;
         }
         else
         {
        Objects->PositionActualValue0x6064 = Cia402_GearRatioInvCal(&BaseLoops->BaseCtrls->Cia402Axis,
                                                             Objects->PositionActualInternalvalue0x6063 );
         }
        if(BaseLoops->Bprm->RvsDir)
	    {
            Objects->TorqueActualValue0x6077 = -(BaseLoops->CurLoop.V.IqFdb*NOR_MAXVAL_FLOAT)/
                                               (BaseLoops->Bprm->MaxCur*BaseLoops->Bprm->Kmottrq) ;
			BaseLoops->TrqFdbANm = Objects->TorqueActualValue0x6077/BaseLoops->Bprm->Ktrqper;     
        }
        else
        {
            Objects->TorqueActualValue0x6077 = (BaseLoops->CurLoop.V.IqFdb*NOR_MAXVAL_FLOAT)/
                                               (BaseLoops->Bprm->MaxCur*BaseLoops->Bprm->Kmottrq) ;
			BaseLoops->TrqFdbANm = Objects->TorqueActualValue0x6077/BaseLoops->Bprm->Ktrqper;    
        }
	}    
	
	BaseLoops->TrqFdbBNm = GearRatioCal(BaseLoops,BaseLoops->TrqFdbANm);

	Objects->CurrentActualValue0x6078 = BaseLoops->CurLoop.V.IqFdb/1.414;   
    Objects->DigitalInputs0x60FD = DiStatus;   

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void trapTorCtrl(PT_MODE_CTRL *pCtrlPT)
{
	float Tmp,Err;

	Tmp = pCtrlPT->TorSlop/(float)TASKB_FRQ;
	
	Err = (float)pCtrlPT->TargetTor - pCtrlPT->OutputTor; 

	if(Err > Tmp)
	{
		pCtrlPT->OutputTor += Tmp;
	}
	else if(Err < -Tmp)
	{
		pCtrlPT->OutputTor -= Tmp;
	}
	else
	{
		pCtrlPT->OutputTor = (float)pCtrlPT->TargetTor;

	}

	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void Cia402_PT_Mode(TCiA402Axis *Cia402Axis, BOOL BaseEnable )
{
	
	CiA402_PRM				*Objects;
	CIA402_STATUS_WORD		StatusWord = {0};
    CIA402_CTRL_WORD        ControlWord = {0};
		
	Objects = Cia402Axis->Objects;
	StatusWord.all = Objects->Statusword0x6041;
    ControlWord.all = Objects->Controlword0x6040;
	
	if(BaseEnable == FALSE)
	{
		MlibResetLongMemory(&Cia402Axis->PT_Ctrl, sizeof(Cia402Axis->PT_Ctrl)/4);
		Objects->TorqueDemand0x6074 = 0;
		return;
	}
        
    if((ControlWord.bit.Halt))
    {
        Cia402Axis->PT_Ctrl.FirstRun = 0; 
        return;
    }
    
    
    if(Cia402Axis->ChangControlMode == PPTPT_MODECHANGE || Cia402Axis->ChangControlMode == PVTPT_MODECHANGE)
    {
       MlibResetLongMemory(&Cia402Axis->PT_Ctrl, sizeof(Cia402Axis->PT_Ctrl)/4);
       Objects->TorqueDemand0x6074 = Objects->TorqueActualValue0x6077;
       Cia402Axis->PT_Ctrl.OutputTor = Objects->TorqueActualValue0x6077;
       Cia402Axis->ChangControlMode = NONE_MODECHANGE;
       Cia402Axis->PT_Ctrl.FirstRun = 1;
      
    }
    else if(Cia402Axis->PT_Ctrl.FirstRun == 0)
    {
       Objects->TorqueDemand0x6074 = 0;
       MlibResetLongMemory(&Cia402Axis->PT_Ctrl, sizeof(Cia402Axis->PT_Ctrl)/4);
       Cia402Axis->PT_Ctrl.FirstRun = 1;
    }

	// target torque limit
	if(Objects->PositiveTorqueLimitValue0x60E0 && (Objects->TargetTorque0x6071 > (INT16)Objects->PositiveTorqueLimitValue0x60E0))
	{
		Cia402Axis->PT_Ctrl.TargetTor = Objects->PositiveTorqueLimitValue0x60E0;
	}
	else if(Objects->NegativeTorqueLimitValue0x60E1 && (Objects->TargetTorque0x6071 < -(INT16)Objects->NegativeTorqueLimitValue0x60E1))
	{
		Cia402Axis->PT_Ctrl.TargetTor = -(INT16)Objects->NegativeTorqueLimitValue0x60E1;
	}
	else
	{
		Cia402Axis->PT_Ctrl.TargetTor = Objects->TargetTorque0x6071;
	}

     // Position limit warning
    if(Objects->PositionActualValue0x6064 < Objects->MinSoftwarePositionLimit0x607D)
    {     
        StatusWord.bit.InLimitActive = 1;        
    }
    else if(Objects->PositionActualValue0x6064 > Objects->MaxSoftwarePositionLimit0x607D)
    {
        StatusWord.bit.InLimitActive = 1;
    }
    else
    {
        StatusWord.bit.InLimitActive = 0;
    }	
	Cia402Axis->PT_Ctrl.TorSlop = Objects->TorqueSlope0x6087;
	
	trapTorCtrl(&Cia402Axis->PT_Ctrl);

	Objects->TorqueDemand0x6074 = (INT16) Cia402Axis->PT_Ctrl.OutputTor;
	
	//TODO:target reach
	
	Objects->Statusword0x6041 = StatusWord.all;
	Cia402Axis->LastCtrlWordForMode = Objects->Controlword0x6040;


}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void trapSpdCtrl(PV_MODE_CTRL *pCtrlPV)
{
	UINT32 DeltAcc,DeltDec;
	INT32 AccQuot, DecQuot, AccRem, DecRem;
	UINT32 Jerk1,Jerk2;
    INT32 Jerk1Quot,Jerk1Rem,Jerk2Quot,Jerk2Rem;
	float Time1,Time2,Time3;

	// In>0&&Out>0, In>0&Out=0, In=0&&Out=1, In<0&&Out>0
	if(pCtrlPV->OutputVel > 0 || ((pCtrlPV->OutputVel ==0) && (pCtrlPV->TargetVel > 0)))
	{
		DeltAcc = pCtrlPV->Acc;
		DeltDec = pCtrlPV->Dec;
	}
	else
	{
		DeltAcc = pCtrlPV->Dec;
		DeltDec = pCtrlPV->Acc;
	}
	switch(pCtrlPV->VelProfileType)
	{
		case LinearRamp:	
	AccQuot = DeltAcc/(UINT32)TASKB_FRQ;
	AccRem = DeltAcc%(UINT32)TASKB_FRQ;
	DecQuot = DeltDec/(UINT32)TASKB_FRQ;
	DecRem = DeltDec%(UINT32)TASKB_FRQ;
	
			if(pCtrlPV->TargetVel> (pCtrlPV->OutputVel + AccQuot))
			{					
				pCtrlPV->RmdVel += AccRem;
				if(pCtrlPV->RmdVel >= TASKB_FRQ)
				{
					pCtrlPV->RmdVel -= TASKB_FRQ;
					AccQuot++;
				}
				
				pCtrlPV->OutputVel += AccQuot;
			}
			else if(pCtrlPV->TargetVel < (pCtrlPV->OutputVel - DecQuot))
			{	
				pCtrlPV->RmdVel += DecRem;
				if(pCtrlPV->RmdVel >= TASKB_FRQ)
				{
					pCtrlPV->RmdVel -= TASKB_FRQ;
					DecQuot++;
				}

				pCtrlPV->OutputVel -= DecQuot;
			}
			else
			{
				pCtrlPV->RmdVel = 0;
				pCtrlPV->OutputVel = pCtrlPV->TargetVel;
			}
			
			break;
			
		case JerkFreeRamp:		
			if(pCtrlPV->Mark == 0)
			{
				pCtrlPV->OutputAcc = 0;
				pCtrlPV->SignChange = 0;
				pCtrlPV->OverTime = 0;
				if(pCtrlPV->TargetVel >= 0)
				{
					if((pCtrlPV->OutputVel >= 0) && (pCtrlPV->TargetVel - pCtrlPV->OutputVel >= 0))
					{
							pCtrlPV->UpDownTime = ((float)(pCtrlPV->TargetVel - pCtrlPV->OutputVel)*2*1000)/(float)DeltAcc;
							pCtrlPV->DetaTime = (float)( pCtrlPV->UpDownTime - (INT32)(pCtrlPV->JerkTime1 + pCtrlPV->JerkTime2))*0.5;
							pCtrlPV->TempJerk = DeltAcc;
					}
					else if((pCtrlPV->OutputVel >= 0) && (pCtrlPV->TargetVel - pCtrlPV->OutputVel < 0))
					{
							pCtrlPV->UpDownTime = ((float)(-pCtrlPV->TargetVel + pCtrlPV->OutputVel)*2*1000)/(float)DeltDec;
							pCtrlPV->DetaTime =(float)( pCtrlPV->UpDownTime- (INT32)(pCtrlPV->JerkTime1 + pCtrlPV->JerkTime2))*0.5;
							pCtrlPV->TempJerk = DeltDec;						
					}
					else
					{
							pCtrlPV->UpDownTime = ((float)- pCtrlPV->OutputVel*2*1000)/(float)DeltAcc;
							pCtrlPV->DetaTime = (float)(pCtrlPV->UpDownTime- (INT32)(pCtrlPV->JerkTime1 + pCtrlPV->JerkTime2))*0.5;
							pCtrlPV->TempJerk = DeltAcc;
							pCtrlPV->SignChange = 1;
					}						
				}
				else
				{
					if((pCtrlPV->OutputVel <= 0) && (pCtrlPV->TargetVel - pCtrlPV->OutputVel >= 0))
					{
						pCtrlPV->UpDownTime = ((float)(pCtrlPV->TargetVel - pCtrlPV->OutputVel)*2*1000)/(float)DeltAcc;
						pCtrlPV->DetaTime = (float)(pCtrlPV->UpDownTime- (INT32)(pCtrlPV->JerkTime1 + pCtrlPV->JerkTime2))*0.5;
						pCtrlPV->TempJerk = DeltAcc;
					}
					else if((pCtrlPV->OutputVel <= 0) && (pCtrlPV->TargetVel - pCtrlPV->OutputVel < 0))
					{
						pCtrlPV->UpDownTime = ((float)(-pCtrlPV->TargetVel + pCtrlPV->OutputVel)*2*1000)/(float)DeltDec;
						pCtrlPV->DetaTime = (float)(pCtrlPV->UpDownTime - (INT32)(pCtrlPV->JerkTime1 + pCtrlPV->JerkTime2))*0.5;
						pCtrlPV->TempJerk = DeltDec;
					}
					else
					{
						pCtrlPV->UpDownTime = ((float)pCtrlPV->OutputVel*2*1000)/(float)DeltDec;
						pCtrlPV->DetaTime = (float)(pCtrlPV->UpDownTime- (INT32)(pCtrlPV->JerkTime1 + pCtrlPV->JerkTime2))*0.5;
						pCtrlPV->TempJerk = DeltDec;
						pCtrlPV->SignChange = 1;
					}
				}
				pCtrlPV->Mark = 1;
			}
			if(pCtrlPV->DetaTime >= 0)
			{
				Time1 = (float) (pCtrlPV->JerkTime1 * (UINT32)(TASKB_FRQ) )* 0.001;
				Time2 = (float) (pCtrlPV->JerkTime2 * (UINT32)(TASKB_FRQ) )* 0.001;
				Time3 =pCtrlPV->DetaTime * (UINT32)(TASKB_FRQ) * 0.001;
				Jerk1 = (float)pCtrlPV->TempJerk * 1000/(float) (pCtrlPV->JerkTime1);
				Jerk2 = (float)pCtrlPV->TempJerk * 1000/(float) (pCtrlPV->JerkTime2);
				Jerk1Quot = Jerk1/ (UINT32)(TASKB_FRQ);
				Jerk1Rem = Jerk1- Jerk1Quot * (UINT32)(TASKB_FRQ);
				Jerk2Quot = Jerk2/ (UINT32)(TASKB_FRQ);
				Jerk2Rem = Jerk2 - Jerk2Quot * (UINT32)(TASKB_FRQ);
				if(pCtrlPV->JerkCnt <=Time1)
				{
					pCtrlPV->RmdAcc += Jerk1Rem;
					if(pCtrlPV->RmdAcc >= TASKB_FRQ)
					{
						pCtrlPV->RmdAcc -= TASKB_FRQ;
						Jerk1Quot++;
					}
					if(pCtrlPV->OutputAcc + Jerk1Quot <=pCtrlPV->TempJerk )
					{
						pCtrlPV->OutputAcc += Jerk1Quot;
					}
				}
				else if(pCtrlPV->JerkCnt <=Time1+Time3)
				{
					pCtrlPV->OutputAcc = pCtrlPV->TempJerk;
				}
				else if(pCtrlPV->JerkCnt <=Time1+Time2+Time3)
				{
					pCtrlPV->RmdAcc += Jerk2Rem;
					if(pCtrlPV->RmdAcc >= TASKB_FRQ)
					{
						pCtrlPV->RmdAcc -= TASKB_FRQ;
						Jerk2Quot++;
					}
					if(pCtrlPV->OutputAcc - Jerk2Quot >= 0 )
					{
						pCtrlPV->OutputAcc -= Jerk2Quot;
					}
				}
				else
				{
					pCtrlPV->JerkCnt =Time1+Time2+Time3+1;
					pCtrlPV->OverTime = 1;
				}
				pCtrlPV->JerkCnt ++; 			
			}
			else
			{
				Time3 =(float) ((UINT32)pCtrlPV->UpDownTime* (UINT32)(TASKB_FRQ)) * 0.001;
				Jerk1 = (float)pCtrlPV->TempJerk * 1000/(float) (pCtrlPV->UpDownTime);
				Jerk1Quot = Jerk1/(UINT32)(TASKB_FRQ);
				Jerk1Rem = Jerk1 - Jerk1Quot *(UINT32)(TASKB_FRQ);
				if(pCtrlPV->JerkCnt <=Time3)
				{
					pCtrlPV->RmdAcc += Jerk1Rem;
					if(pCtrlPV->RmdAcc >= TASKB_FRQ)
					{
						pCtrlPV->RmdAcc -= TASKB_FRQ;
						Jerk1Quot++;
					}
					if(pCtrlPV->OutputAcc + Jerk1Quot <= pCtrlPV->TempJerk )
					{
						pCtrlPV->OutputAcc += Jerk1Quot;
					}
				}
				else if(pCtrlPV->JerkCnt <=Time3+Time3)
				{
					pCtrlPV->RmdAcc += Jerk1Rem;
					if(pCtrlPV->RmdAcc >= TASKB_FRQ)
					{
						pCtrlPV->RmdAcc -= TASKB_FRQ;
						Jerk1Quot++;
					}
					if((pCtrlPV->OutputAcc - Jerk1Quot) >= 0 )
					{
						pCtrlPV->OutputAcc -= Jerk1Quot;
					}
				}
				else
				{
					pCtrlPV->JerkCnt =Time3+Time3+1;
					pCtrlPV->OverTime = 1;
				}
				pCtrlPV->JerkCnt ++; 	
			}
			AccQuot = pCtrlPV->OutputAcc/(UINT32)TASKB_FRQ;
			AccRem = pCtrlPV->OutputAcc%(UINT32)TASKB_FRQ;
			if(pCtrlPV->TargetVel> (pCtrlPV->OutputVel + AccQuot))
			{					
				pCtrlPV->RmdVel += AccRem;
				if(pCtrlPV->RmdVel >= TASKB_FRQ)
				{
					pCtrlPV->RmdVel -= TASKB_FRQ;
					AccQuot++;
				}
				pCtrlPV->OutputVel += AccQuot;
				if(((pCtrlPV->OutputVel + (INT32)pCtrlPV->TempJerk /(INT32)TASKB_FRQ > pCtrlPV->TargetVel)
					&& (pCtrlPV->OutputAcc  <= Jerk2Quot || pCtrlPV->OutputAcc  <= Jerk1Quot) )
						|| pCtrlPV->OverTime ==1) 
				{
					pCtrlPV->OutputVel = pCtrlPV->TargetVel;
					pCtrlPV->RmdVel = 0;
					pCtrlPV->RmdAcc = 0;
					pCtrlPV->JerkCnt = 0;
					pCtrlPV->OutputAcc = 0;
				}
				if(pCtrlPV->SignChange == 1 
					&&(((pCtrlPV->OutputVel + (INT32)pCtrlPV->TempJerk /(INT32)TASKB_FRQ > 0)
						&& (pCtrlPV->OutputAcc  <= Jerk2Quot || pCtrlPV->OutputAcc  <= Jerk1Quot) )
						|| pCtrlPV->OverTime ==1) )
				{
					pCtrlPV->OutputVel = 0;
					pCtrlPV->RmdVel = 0;
					pCtrlPV->RmdAcc = 0;
					pCtrlPV->JerkCnt = 0;
					pCtrlPV->OutputAcc = 0;
				}
			}
			else if(pCtrlPV->TargetVel < (pCtrlPV->OutputVel - AccQuot))
			{	
				pCtrlPV->RmdVel += AccRem;
				if(pCtrlPV->RmdVel >= TASKB_FRQ)
				{
					pCtrlPV->RmdVel -= TASKB_FRQ;
					AccQuot++;
				}
				pCtrlPV->OutputVel -= AccQuot;
				if(((pCtrlPV->OutputVel -(INT32)pCtrlPV->TempJerk /(INT32)TASKB_FRQ < pCtrlPV->TargetVel)
					&& (pCtrlPV->OutputAcc  <= Jerk2Quot || pCtrlPV->OutputAcc  <= Jerk1Quot) )
					|| pCtrlPV->OverTime ==1)
				{
					pCtrlPV->OutputVel = pCtrlPV->TargetVel;
					pCtrlPV->RmdVel = 0;
					pCtrlPV->RmdAcc = 0;
					pCtrlPV->JerkCnt = 0;
					pCtrlPV->OutputAcc = 0;
				}
				if(pCtrlPV->SignChange == 1
					&&(((pCtrlPV->OutputVel - (INT32)pCtrlPV->TempJerk /(INT32)TASKB_FRQ < 0)
						&& (pCtrlPV->OutputAcc  <= Jerk2Quot || pCtrlPV->OutputAcc  <= Jerk1Quot) ) 
						|| pCtrlPV->OverTime ==1))
				{
					pCtrlPV->OutputVel = 0;
					pCtrlPV->RmdVel = 0;
					pCtrlPV->RmdAcc = 0;
					pCtrlPV->JerkCnt = 0;
					pCtrlPV->OutputAcc = 0;
				}
			}
			else
			{
				pCtrlPV->RmdVel = 0;
				pCtrlPV->RmdAcc = 0;
				pCtrlPV->JerkCnt = 0;
				pCtrlPV->OutputAcc = 0;
				pCtrlPV->OutputVel = pCtrlPV->TargetVel;
			}		
			
			break;
			
		case JerkLimitedRamp:

			break;

		default:
			break;	
	}
	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void Cia402_PV_Mode(TCiA402Axis *Cia402Axis, BOOL BaseEnable )
{
	CiA402_PRM	            *Objects;
	CIA402_STATUS_WORD      StatusWord = {0};
    CIA402_CTRL_WORD        ControlWord = {0};
	
	Objects = Cia402Axis->Objects;
	StatusWord.all = Objects->Statusword0x6041;
    ControlWord.all = Objects->Controlword0x6040;

	if(BaseEnable == FALSE)
	{
		MlibResetLongMemory(&Cia402Axis->PV_Ctrl, sizeof(Cia402Axis->PV_Ctrl)/4);
		Objects->VelocityDemandValue0x606B = 0;
		return;
	}
    
    if((ControlWord.bit.Halt))
    {
        Cia402Axis->PV_Ctrl.FirstRun = 0; 
        return;
    }
    
    if(Cia402Axis->ChangControlMode == PPTPV_MODECHANGE || Cia402Axis->ChangControlMode == PTTPV_MODECHANGE)
    {   
        MlibResetLongMemory(&Cia402Axis->PV_Ctrl, sizeof(Cia402Axis->PV_Ctrl)/4);
        Objects->VelocityDemandValue0x606B = Objects->VelocityActualValue0x606C;
        Cia402Axis->PV_Ctrl.OutputVel = Objects->VelocityActualValue0x606C;
        Cia402Axis->PV_Ctrl.LastOutputVel = Objects->VelocityActualValue0x606C;
        Cia402Axis->PV_Ctrl.LastTargetVel = Cia402Axis->PV_Ctrl.LastOutputVel;
        Cia402Axis->PV_Ctrl.FirstRun = 1;       
        Cia402Axis->ChangControlMode = NONE_MODECHANGE;
    }
    else if(Cia402Axis->PV_Ctrl.FirstRun == 0)
    {
        MlibResetLongMemory(&Cia402Axis->PV_Ctrl, sizeof(Cia402Axis->PV_Ctrl)/4);
        Objects->VelocityDemandValue0x606B = 0;
        Cia402Axis->PV_Ctrl.FirstRun = 1;
    }

    // accerlation limit
	if(Objects->ProfileAcceleration0x6083)
	{
		Cia402Axis->PV_Ctrl.Acc = MlibLimitu32(Objects->ProfileAcceleration0x6083, Objects->Max_acceleration0x60C5);
	}
	else
	{
		Cia402Axis->PV_Ctrl.Acc = Objects->Max_acceleration0x60C5;
	}

	// deceleration limit
	if(Objects->ProfileDeceleration0x6084)
	{
		Cia402Axis->PV_Ctrl.Dec = MlibLimitu32(Objects->ProfileDeceleration0x6084, Objects->Max_deceleration0x60C6);
	}
	else
	{
		if(Objects->ProfileAcceleration0x6083)
		{
			Cia402Axis->PV_Ctrl.Dec = MlibLimitu32(Objects->ProfileAcceleration0x6083, Objects->Max_deceleration0x60C6);
		}
		else
		{
			Cia402Axis->PV_Ctrl.Dec = Objects->Max_deceleration0x60C6;
		}
	}

	// target velocity limit
	if(Objects->TargetVelocity0x60FF > (INT64)Objects->Max_profile_velocity0x607F)
	{
		Cia402Axis->PV_Ctrl.TargetVel = Objects->Max_profile_velocity0x607F;
	}
    else if(Objects->TargetVelocity0x60FF < -(INT64)Objects->Max_profile_velocity0x607F)
    {
        Cia402Axis->PV_Ctrl.TargetVel = -(INT64)Objects->Max_profile_velocity0x607F;
    }
	else
	{
		Cia402Axis->PV_Ctrl.TargetVel = Objects->TargetVelocity0x60FF;
	}
     // Position limit warning
    if(Objects->PositionActualValue0x6064 < Objects->MinSoftwarePositionLimit0x607D)
    {     
        StatusWord.bit.InLimitActive = 1;        
    }
    else if(Objects->PositionActualValue0x6064 > Objects->MaxSoftwarePositionLimit0x607D)
    {
        StatusWord.bit.InLimitActive = 1;
    }
    else
    {
        StatusWord.bit.InLimitActive = 0;
    }	

// Change the parameters of the PVmode after the speed command changes
	if(Cia402Axis->PV_Ctrl.TargetVel != Cia402Axis->PV_Ctrl.LastTargetVel)
	{
		Cia402Axis->PV_Ctrl.Mark = 0;
		Cia402Axis->PV_Ctrl.JerkCnt = 0;

		Cia402Axis->PV_Ctrl.VelProfileType = Objects->MotionProfileType0x6086;
        
		//Cia402Axis->PV_Ctrl.ProfileJerkUse = Objects->ProfileJerkUse0x60A3;		
		// TODO:Assign Jerk time to the cia402 parameter
		/*switch(Cia402Axis->PV_Ctrl.ProfileJerkUse)
		{
			case 1:		
				Cia402Axis->PV_Ctrl.JerkTime1 = Objects->ProfileJerk10x60A4;
				Cia402Axis->PV_Ctrl.JerkTime2 = Objects->ProfileJerk10x60A4;
				Cia402Axis->PV_Ctrl.JerkTime3 = Objects->ProfileJerk10x60A4;
				Cia402Axis->PV_Ctrl.JerkTime4 = Objects->ProfileJerk10x60A4;
				break;
		
			case 2:	
				Cia402Axis->PV_Ctrl.JerkTime1 = Objects->ProfileJerk10x60A4;
				Cia402Axis->PV_Ctrl.JerkTime2 = Objects->ProfileJerk10x60A4;
				Cia402Axis->PV_Ctrl.JerkTime3 = Objects->ProfileJerk20x60A4;
				Cia402Axis->PV_Ctrl.JerkTime4 = Objects->ProfileJerk20x60A4;
				break;

			case 4:		
				Cia402Axis->PV_Ctrl.JerkTime1 = Objects->ProfileJerk10x60A4;
				Cia402Axis->PV_Ctrl.JerkTime2 = Objects->ProfileJerk30x60A4;
				Cia402Axis->PV_Ctrl.JerkTime3 = Objects->ProfileJerk20x60A4;
				Cia402Axis->PV_Ctrl.JerkTime4 = Objects->ProfileJerk40x60A4;
				break;

			default:
				break;
		}*/
        
        Cia402Axis->PV_Ctrl.JerkTime1 = Objects->ProfileJerk10x60A4;
        Cia402Axis->PV_Ctrl.JerkTime2 = Objects->ProfileJerk30x60A4;
        Cia402Axis->PV_Ctrl.JerkTime3 = Objects->ProfileJerk20x60A4;
        Cia402Axis->PV_Ctrl.JerkTime4 = Objects->ProfileJerk40x60A4;
        
//        Cia402Axis->PV_Ctrl.JerkTime1 = Objects->ProfileJerk0x60A4.ProfileJerk1;
//        Cia402Axis->PV_Ctrl.JerkTime2 = Objects->ProfileJerk0x60A4.ProfileJerk3;
//        Cia402Axis->PV_Ctrl.JerkTime3 = Objects->ProfileJerk0x60A4.ProfileJerk2;
//        Cia402Axis->PV_Ctrl.JerkTime4 = Objects->ProfileJerk0x60A4.ProfileJerk4;        

		//Prevent calculation errors caused by Jerk time equal to 0
		if(Cia402Axis->PV_Ctrl.JerkTime1 == 0)
		{
			Cia402Axis->PV_Ctrl.JerkTime1 = 1;
		}

		if(Cia402Axis->PV_Ctrl.JerkTime2 == 0)
		{
			Cia402Axis->PV_Ctrl.JerkTime2 = 1;
		}

		if(Cia402Axis->PV_Ctrl.JerkTime3 == 0)
		{
			Cia402Axis->PV_Ctrl.JerkTime3 = 1;
		}

		if(Cia402Axis->PV_Ctrl.JerkTime4 == 0)
		{
			Cia402Axis->PV_Ctrl.JerkTime4 = 1;
		}
	}	
	
	trapSpdCtrl(&Cia402Axis->PV_Ctrl);
								   
    Objects->VelocityDemandValue0x606B = (INT32)Cia402Axis->PV_Ctrl.OutputVel;

	//  Change the Mark of the PVmode after the speed OutPut changes sign
	if(((Cia402Axis->PV_Ctrl.OutputVel <= 0 && Cia402Axis->PV_Ctrl.LastOutputVel > 0 )
		||(Cia402Axis->PV_Ctrl.OutputVel >= 0 && Cia402Axis->PV_Ctrl.LastOutputVel < 0 ))
			&& Cia402Axis->PV_Ctrl.TargetVel == Cia402Axis->PV_Ctrl.LastTargetVel)
	{		
		Cia402Axis->PV_Ctrl.Mark = 0;
		Cia402Axis->PV_Ctrl.JerkCnt = 0;		
	}

	// Target Reach
	if(Objects->VelocityActualValue0x606C < (Objects->TargetVelocity0x60FF + (INT32)Objects->VelocityWindow0x606D)
	  && Objects->VelocityActualValue0x606C > (Objects->TargetVelocity0x60FF - (INT32)Objects->VelocityWindow0x606D))
	{
		if(Cia402Axis->PV_Ctrl.VelWindowCnt < Objects->VelocityWindowTime0x606E * 1000/TASKB_CYCLEUS)
		{
			Cia402Axis->PV_Ctrl.VelWindowCnt++;
			StatusWord.bit.TargetReached = 0;
		}
		else
		{
            StatusWord.bit.TargetReached = 1;
		}
  	}
    else
    {
        Cia402Axis->PV_Ctrl.VelWindowCnt = 0;
        StatusWord.bit.TargetReached = 0;
    }

	//zero speed
	if(Objects->VelocityActualValue0x606C < (INT32)Objects->VelocityThreshold0x606F
	  && Objects->VelocityActualValue0x606C > - (INT32)Objects->VelocityThreshold0x606F)
	{
		if(Cia402Axis->PV_Ctrl.VelThresholdCnt <Objects->VelocityThresholdTime0x6070 * 1000/TASKB_CYCLEUS) 
		{
			Cia402Axis->PV_Ctrl.VelThresholdCnt++;
			StatusWord.bit.OperSpecific_b12 = 0;
		}
		else
		{
            StatusWord.bit.OperSpecific_b12 = 1;
		}
  	}
    else
    {
        Cia402Axis->PV_Ctrl.VelThresholdCnt = 0;
        StatusWord.bit.OperSpecific_b12 = 0;
    }	

	Cia402Axis->PV_Ctrl.LastTargetVel = Cia402Axis->PV_Ctrl.TargetVel;
	Cia402Axis->PV_Ctrl.LastOutputVel = Cia402Axis->PV_Ctrl.OutputVel;
	StatusWord.bit.OperSpecific_b13 = 0;
	Objects->Statusword0x6041 = StatusWord.all;
	Cia402Axis->LastCtrlWordForMode = Objects->Controlword0x6040;
		
}
 
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE BOOL trapPosCtrl(PP_MODE_CTRL *pCtrlPP)
{
	float TempTime1,TempTime2;
	INT32 VelAcc,VelDec,AccRem, DecRem,VelQuot,VelRem;
	INT32  DeltaPos,TempPos,AccTempPos,EndVelc,TarVel;
	
	pCtrlPP->PlanState = 0;
	
	VelAcc = pCtrlPP->Acc / (UINT32)TASKB_FRQ;
	VelDec = pCtrlPP->Dec / (UINT32)TASKB_FRQ;
	AccRem = pCtrlPP->Acc % (UINT32)TASKB_FRQ;
	DecRem = pCtrlPP->Dec % (UINT32)TASKB_FRQ;
	
        
        
        
	DeltaPos = pCtrlPP->TargetPos - pCtrlPP->InitPos;

	if(pCtrlPP->TargetPos == pCtrlPP->OutputPos)
	{
		return TRUE; // todo
	}

	//overflow
	if((pCtrlPP->TargetPos>0 && pCtrlPP->InitPos<0 && DeltaPos <0)
		||(pCtrlPP->TargetPos<0 && pCtrlPP->InitPos>0 && DeltaPos >0))
	{
		return  FALSE; // todo
	}
	
    if(DeltaPos < 0)
    {
    	TarVel = -(INT32)pCtrlPP->TargetVel;
		EndVelc = -(INT32)pCtrlPP->EndVel;
    	DeltaPos = -DeltaPos;
        pCtrlPP->PlanState |= 0x000;	
    }
    else if(DeltaPos > 0)
    {
    	TarVel = (INT32)pCtrlPP->TargetVel;
		EndVelc = (INT32)pCtrlPP->EndVel;
		pCtrlPP->PlanState |= 0x100;  	
    }
    else
    {
        return FALSE; // todo
    }

	//Position track type judgment
	if(pCtrlPP->InitVel >=0 && TarVel >= pCtrlPP->InitVel)
	{
		pCtrlPP->PlanState |= 0x011;  	
	}
	else if(pCtrlPP->InitVel <0 && TarVel <= pCtrlPP->InitVel)
	{
		pCtrlPP->PlanState |= 0x000;  	
	}
	else if(pCtrlPP->InitVel >=0 && TarVel < pCtrlPP->InitVel)
	{
		pCtrlPP->PlanState |= 0x001;  	
	}
	else if(pCtrlPP->InitVel <0 && TarVel > pCtrlPP->InitVel)
	{
		pCtrlPP->PlanState |= 0x010;  	
	}


	//Calculate the output speed for each cycle to accumulate the position command
	switch(pCtrlPP->PlanState)
	{
	/*Running in the negative direction, the initial speed is a negative speed or zero speed greater than or equal to the target speed;
	 first accelerates to the target speed, then runs at a constant speed, then decelerates*/
	case 0x000:		
		{
			pCtrlPP->SectPos[0] = 0.5*((float)TarVel * (float)TarVel - (float)pCtrlPP->InitVel * (float)pCtrlPP->InitVel)/ (float)pCtrlPP->Acc;

			pCtrlPP->SectPos[2] = 0.5*((float)TarVel * (float)TarVel - (float)EndVelc* (float)EndVelc)/ (float)pCtrlPP->Dec;
			
			TempPos = pCtrlPP->SectPos[0] + pCtrlPP->SectPos[2];

			//Determine if the distance is sufficient to accelerate to the planning speed and then slow down
			if(DeltaPos >=TempPos)
			{	
				pCtrlPP->SectPos[1] = DeltaPos - TempPos;
			
				if(pCtrlPP->OutputPos <= pCtrlPP->InitPos - pCtrlPP->SectPos[0] - pCtrlPP->SectPos[1] - pCtrlPP->SectPos[2] )
				{
					pCtrlPP->OutputVel = EndVelc;
				}
				else if(pCtrlPP->OutputPos <= pCtrlPP->InitPos - pCtrlPP->SectPos[0] - pCtrlPP->SectPos[1])
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					
					if(pCtrlPP->OutputVel + VelDec <= EndVelc)
					{
						pCtrlPP->OutputVel += VelDec;
					}
				}
				else if(pCtrlPP->OutputPos <= pCtrlPP->InitPos - pCtrlPP->SectPos[0])
				{
					pCtrlPP->OutputVel = TarVel ;
				}
				else
				{
					pCtrlPP->RmdVel  += AccRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelAcc++;
					}
					
					if(pCtrlPP->OutputVel - VelAcc >= TarVel )
					{
						pCtrlPP->OutputVel -= VelAcc;
					}
				}
				
			}
			else
			{
				//FS=maS=0.5*(m*Vt^2-m*Vi^2)   accS1-Dec(S-S1) = 0.5(Ve^2+Vi^2)   S1=( Ve^2+Vi^2+2*DecS)/(2(Acc+Dec))
				AccTempPos = (  (float)EndVelc * (float)EndVelc - (float)pCtrlPP->InitVel *  (float)pCtrlPP->InitVel + 2* (float)pCtrlPP->Dec *  (float)DeltaPos )
									/ (float)(2*(pCtrlPP->Acc + pCtrlPP->Dec)) ;
										
				if((pCtrlPP->OutputPos - pCtrlPP->InitPos) <= ( - AccTempPos))
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					
					if(pCtrlPP->OutputVel + VelDec <= EndVelc)
					{
						pCtrlPP->OutputVel += VelDec;
					}
				}
				else
				{	
					pCtrlPP->RmdVel  += AccRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelAcc++;
					}
					
					if(pCtrlPP->OutputVel - VelAcc >= TarVel )
					{
						pCtrlPP->OutputVel -= VelAcc;
					}		
				}
			}
					
			break;
		}
	
	/*Running in the negative direction, the initial speed is positive speed;
	first decelerate to zero speed, then accelerate to the target speed, then run at a constant speed, then decelerate*/
	case 0x001:	
		{
			TempTime1= (float)(pCtrlPP->InitVel) / (float)pCtrlPP->Dec;
			pCtrlPP->SectPos[0] = 0.5*((float)pCtrlPP->Dec * TempTime1*TempTime1);
			
			TempTime2 =  (float)(-TarVel ) /(float)pCtrlPP->Acc;
			pCtrlPP->SectPos[1] = 0.5*((float)pCtrlPP->Acc *TempTime2* TempTime2);

			pCtrlPP->SectPos[3] =0.5*((float)TarVel * (float)TarVel - (float)EndVelc * (float)EndVelc)/ (float)pCtrlPP->Dec;

			TempPos = -pCtrlPP->SectPos[0] + pCtrlPP->SectPos[1] + pCtrlPP->SectPos[3];

			//Determine if the distance is sufficient to accelerate to the planning speed and then slow down
			if(DeltaPos >=TempPos)
			{	
				pCtrlPP->SectPos[2] = DeltaPos - TempPos;

				if(pCtrlPP->OutputVel >=0)
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					
					pCtrlPP->OutputVel -= VelDec;
				}
				else
				{
					if(pCtrlPP->OutputPos <= (pCtrlPP->InitPos+ pCtrlPP->SectPos[0] - pCtrlPP->SectPos[1]-pCtrlPP->SectPos[2] -pCtrlPP->SectPos[3]) )
					{
						pCtrlPP->OutputVel = EndVelc;
					}
					else if(pCtrlPP->OutputPos <= (pCtrlPP->InitPos + pCtrlPP->SectPos[0] -pCtrlPP->SectPos[1]-pCtrlPP->SectPos[2]) )
					{
						pCtrlPP->RmdVel  += DecRem;
					
						if(pCtrlPP->RmdVel  >= TASKB_FRQ)
						{
							pCtrlPP->RmdVel   -= TASKB_FRQ;
							VelDec++;
						}
						
						if(pCtrlPP->OutputVel + VelDec <= EndVelc)
						{
							pCtrlPP->OutputVel += VelDec;
						}
					}
					else if(pCtrlPP->OutputPos <= (pCtrlPP->InitPos + pCtrlPP->SectPos[0] -pCtrlPP->SectPos[1]) )
					{
						pCtrlPP->OutputVel = TarVel;	
					}
					else 
					{
						pCtrlPP->RmdVel  += AccRem;
					
						if(pCtrlPP->RmdVel  >= TASKB_FRQ)
						{
							pCtrlPP->RmdVel   -= TASKB_FRQ;
							VelAcc++;
						}
						
						if(pCtrlPP->OutputVel - VelAcc >=TarVel )
						{
							pCtrlPP->OutputVel -= VelAcc;
						}
					}
				}
			}
			else
			{
				//FS=maS=0.5*(m*Vt^2-m*Vi^2)   accS1-Dec(S-S1) = 0.5(Ve^2+Vi^2)   S1=( Ve^2+Vi^2+2*DecS)/(2(Acc+Dec))
				AccTempPos = (  (float)EndVelc * (float)EndVelc + 2* (float)pCtrlPP->Dec * (float) (DeltaPos + pCtrlPP->SectPos[0]) )
									/ (float)(2*(pCtrlPP->Acc + pCtrlPP->Dec)) ;
				
				if(pCtrlPP->OutputVel >= 0)
				{	
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					pCtrlPP->OutputVel -= VelDec;
						
				}
				else 
				{
					if((pCtrlPP->OutputPos - pCtrlPP->InitPos) <= ( pCtrlPP->SectPos[0] - AccTempPos))
					{
						pCtrlPP->RmdVel  += DecRem;
						
						if(pCtrlPP->RmdVel  >= TASKB_FRQ)
						{
							pCtrlPP->RmdVel   -= TASKB_FRQ;
							VelDec++;
						}
						if(pCtrlPP->OutputVel + VelDec <= EndVelc)
						{
							pCtrlPP->OutputVel += VelDec;
						}		
					}
					else
					{	
						pCtrlPP->RmdVel  += AccRem;
					
						if(pCtrlPP->RmdVel  >= TASKB_FRQ)
						{
							pCtrlPP->RmdVel   -= TASKB_FRQ;
							VelAcc++;
						}
						if(pCtrlPP->OutputVel - VelAcc >=TarVel )
						{
							pCtrlPP->OutputVel -= VelAcc;
						}
					}
				}
			}
			
			break;
		}
	
	/*Running in the negative direction, the initial speed is a negative speed lower than the target speed;
	first decelerating to the target speed, then running at a constant speed, and then decelerating*/
	case 0x010:
		{
			pCtrlPP->SectPos[0] = 0.5*( (float)pCtrlPP->InitVel * (float)pCtrlPP->InitVel - (float)TarVel  * (float)TarVel )/ (float)pCtrlPP->Dec;

			pCtrlPP->SectPos[2] = 0.5*((float)TarVel * (float)TarVel - (float)EndVelc * (float)EndVelc)/ (float)pCtrlPP->Dec;
			
			TempPos = pCtrlPP->SectPos[0] + pCtrlPP->SectPos[2];

			//Determine if the distance is sufficient to accelerate to the planning speed and then slow down
			if(DeltaPos >=TempPos)
			{	
				pCtrlPP->SectPos[1] = DeltaPos - TempPos;
				
				if(pCtrlPP->OutputPos <= pCtrlPP->InitPos - pCtrlPP->SectPos[0] -pCtrlPP->SectPos[1] - pCtrlPP->SectPos[2])
				{
					pCtrlPP->OutputVel = EndVelc;
				}
				else if(pCtrlPP->OutputPos <= pCtrlPP->InitPos - pCtrlPP->SectPos[0] -pCtrlPP->SectPos[1])
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					if(pCtrlPP->OutputVel + VelDec <= EndVelc)
					{
						pCtrlPP->OutputVel += VelDec;
					}
				}
				else if(pCtrlPP->OutputPos <= pCtrlPP->InitPos - pCtrlPP->SectPos[0])
				{
					pCtrlPP->OutputVel = TarVel ;
				}
				else
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					if(pCtrlPP->OutputVel + VelDec <= TarVel )
					{
						pCtrlPP->OutputVel += VelDec;
					}
				}
			}
			else
			{	
				pCtrlPP->RmdVel  += DecRem;
					
				if(pCtrlPP->RmdVel  >= TASKB_FRQ)
				{
					pCtrlPP->RmdVel   -= TASKB_FRQ;
					VelDec++;
				}
				if(pCtrlPP->OutputVel + VelDec <= EndVelc)
				{
					pCtrlPP->OutputVel += VelDec;
				}
			}				
			break;
		}
	
	/*Running in the positive direction, the initial speed is a positive speed greater than the target speed;
	first decelerating to the target speed, then running at a constant speed, and then decelerating*/
	case 0x101:
		{
			pCtrlPP->SectPos[0] = 0.5*( (float)pCtrlPP->InitVel * (float)pCtrlPP->InitVel - (float)TarVel  * (float)TarVel )/ (float)pCtrlPP->Dec;

			pCtrlPP->SectPos[2] =0.5*((float)TarVel * (float)TarVel - (float)EndVelc* (float)EndVelc)/ (float)pCtrlPP->Dec;
			
			TempPos = pCtrlPP->SectPos[0] + pCtrlPP->SectPos[2];

			//Determine if the distance is sufficient to accelerate to the planning speed and then slow down
			if(DeltaPos >=TempPos)
			{	
				pCtrlPP->SectPos[1] = DeltaPos - TempPos;

				if(pCtrlPP->OutputPos >= pCtrlPP->InitPos + pCtrlPP->SectPos[0]+pCtrlPP->SectPos[1]+pCtrlPP->SectPos[2])
				{
					pCtrlPP->OutputVel = EndVelc;
				}
				else if(pCtrlPP->OutputPos >= pCtrlPP->InitPos + pCtrlPP->SectPos[0]+pCtrlPP->SectPos[1])
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					if(pCtrlPP->OutputVel - VelDec >= EndVelc)
					{
						pCtrlPP->OutputVel -= VelDec;
					}
				}
				else if(pCtrlPP->OutputPos >= pCtrlPP->InitPos + pCtrlPP->SectPos[0])
				{
					pCtrlPP->OutputVel = TarVel ;
				}
				else
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					if(pCtrlPP->OutputVel - VelDec >= TarVel )
					{
						pCtrlPP->OutputVel -= VelDec;
					}
				}	
				
			}
			else
			{
				pCtrlPP->RmdVel  += DecRem;
					
				if(pCtrlPP->RmdVel  >= TASKB_FRQ)
				{
					pCtrlPP->RmdVel   -= TASKB_FRQ;
					VelDec++;
				}
				if(pCtrlPP->OutputVel - VelDec >= EndVelc )
				{
					pCtrlPP->OutputVel -= VelDec;
				}
			}
					
			break;

		}
	
	/*Running in the positive direction, the initial speed is negative speed;
	first decelerate to zero speed, then accelerate to the target speed, then run at a constant speed, then decelerate*/
	case 0x110:
		{
			TempTime1= 	(float)( -pCtrlPP->InitVel) / (float)pCtrlPP->Dec;
			pCtrlPP->SectPos[0] = 0.5*((float)pCtrlPP->Dec * TempTime1*TempTime1);
			
			TempTime2 =  (float)TarVel /(float)pCtrlPP->Acc;
			pCtrlPP->SectPos[1] = 0.5*((float)pCtrlPP->Acc *TempTime2* TempTime2);

			pCtrlPP->SectPos[3] =0.5*((float)TarVel * (float)TarVel - (float)EndVelc * (float)EndVelc)/ (float)pCtrlPP->Dec;

			TempPos = -pCtrlPP->SectPos[0] + pCtrlPP->SectPos[1] + pCtrlPP->SectPos[3];

			//Determine if the distance is sufficient to accelerate to the planning speed and then slow down
			if(DeltaPos >=TempPos)
			{	
				pCtrlPP->SectPos[2] = DeltaPos - TempPos;
				
				if( pCtrlPP->OutputVel <=0)
				{	
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					pCtrlPP->OutputVel += VelDec;
				}
				else 
				{			
					if(pCtrlPP->OutputPos >= pCtrlPP->InitPos - pCtrlPP->SectPos[0]+pCtrlPP->SectPos[1]+pCtrlPP->SectPos[2] + pCtrlPP->SectPos[3]) 
					{
						pCtrlPP->OutputVel = EndVelc;
					}
					else if(pCtrlPP->OutputPos >=pCtrlPP->InitPos - pCtrlPP->SectPos[0]+pCtrlPP->SectPos[1]+pCtrlPP->SectPos[2])
					{
						pCtrlPP->RmdVel  += DecRem;
					
						if(pCtrlPP->RmdVel  >= TASKB_FRQ)
						{
							pCtrlPP->RmdVel   -= TASKB_FRQ;
							VelDec++;
						}
						if(pCtrlPP->OutputVel - VelDec >=EndVelc)
						{
							pCtrlPP->OutputVel -= VelDec;
						}
					}
					else if(pCtrlPP->OutputPos >= pCtrlPP->InitPos - pCtrlPP->SectPos[0]+pCtrlPP->SectPos[1])
					{
						pCtrlPP->OutputVel = TarVel ;	
					}
					else
					{
						pCtrlPP->RmdVel  += AccRem;
					
						if(pCtrlPP->RmdVel  >= TASKB_FRQ)
						{
							pCtrlPP->RmdVel   -= TASKB_FRQ;
							VelAcc++;
						}
						if(pCtrlPP->OutputVel + VelAcc <= TarVel )
						{
							pCtrlPP->OutputVel += VelAcc;
						}
					}	
				}
			}
			else
			{
				//FS=maS=0.5*(m*Vt^2-m*Vi^2)   accS1-Dec(S-S1) = 0.5(Ve^2+Vi^2)   S1=( Ve^2+Vi^2+2*DecS)/(2(Acc+Dec))
				AccTempPos = (  (float)EndVelc * (float)EndVelc + 2* (float)pCtrlPP->Dec * (float) (DeltaPos + pCtrlPP->SectPos[0]) )
									/ (float)(2*(pCtrlPP->Acc + pCtrlPP->Dec)) ;
				
				if(pCtrlPP->OutputVel <= 0)
				{	
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					pCtrlPP->OutputVel += VelDec;
				}
				else
				{
					if(pCtrlPP->OutputPos - pCtrlPP->InitPos >= ( - pCtrlPP->SectPos[0] + AccTempPos ))
					{
						pCtrlPP->RmdVel  += DecRem;
					
						if(pCtrlPP->RmdVel  >= TASKB_FRQ)
						{
							pCtrlPP->RmdVel   -= TASKB_FRQ;
							VelDec++;
						}
						if(pCtrlPP->OutputVel - VelDec >= EndVelc)
						{
							pCtrlPP->OutputVel -= VelDec;
						}	
					}
					else
					{	
						pCtrlPP->RmdVel  += AccRem;
					
						if(pCtrlPP->RmdVel  >= TASKB_FRQ)
						{
							pCtrlPP->RmdVel   -= TASKB_FRQ;
							VelAcc++;
						}
						if(pCtrlPP->OutputVel + VelAcc <= TarVel )
						{
							pCtrlPP->OutputVel += VelAcc;
						}
					}
				}
			}		
		
			break;
		}
	
	/*Running in the positive direction, the initial speed is a positive speed or zero speed that is less than or equal to the target speed;
	first accelerates to the target speed, then runs at a constant speed, and then decelerates*/
	case 0x111:
		{
			pCtrlPP->SectPos[0] = 0.5*((float)TarVel * (float)TarVel - (float)pCtrlPP->InitVel * (float)pCtrlPP->InitVel)/ (float)pCtrlPP->Acc;

			pCtrlPP->SectPos[2] = 0.5*((float)TarVel * (float)TarVel - (float)EndVelc * (float)EndVelc)/ (float)pCtrlPP->Dec;
			
			TempPos = pCtrlPP->SectPos[0] + pCtrlPP->SectPos[2];

			//Determine if the distance is sufficient to accelerate to the planning speed and then slow down
			if(DeltaPos >=TempPos)
			{	
				pCtrlPP->SectPos[1] = DeltaPos - TempPos;
			
				if(pCtrlPP->OutputPos >= pCtrlPP->InitPos + pCtrlPP->SectPos[0]+pCtrlPP->SectPos[1]+pCtrlPP->SectPos[2])
				{
					pCtrlPP->OutputVel = EndVelc;
				}
				else if(pCtrlPP->OutputPos >=pCtrlPP->InitPos + pCtrlPP->SectPos[0]+pCtrlPP->SectPos[1])
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					if(pCtrlPP->OutputVel - VelDec >= EndVelc)
					{
						pCtrlPP->OutputVel -= VelDec;
					}
				}
				else if(pCtrlPP->OutputPos >= pCtrlPP->InitPos + pCtrlPP->SectPos[0])
				{
					pCtrlPP->OutputVel = TarVel ;
				}
				else
				{
					pCtrlPP->RmdVel  += AccRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelAcc++;
					}
					if(pCtrlPP->OutputVel + VelAcc <= TarVel )
					{
						pCtrlPP->OutputVel += VelAcc;
					}
				}	
			}
			else
			{
				//FS=maS=0.5*(m*Vt^2-m*Vi^2)   accS1-Dec(S-S1) = 0.5(Ve^2+Vi^2)   S1=( Ve^2+Vi^2+2*DecS)/(2(Acc+Dec))
				AccTempPos = ( (float)EndVelc * (float)EndVelc - (float)pCtrlPP->InitVel *  (float)pCtrlPP->InitVel + 2* (float)pCtrlPP->Dec *  (float)DeltaPos )
									/ (float)(2*(pCtrlPP->Acc + pCtrlPP->Dec)) ;
				
                                
                                
				if(pCtrlPP->OutputPos - pCtrlPP->InitPos >= AccTempPos)
				{
					pCtrlPP->RmdVel  += DecRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelDec++;
					}
					if(pCtrlPP->OutputVel - VelDec >= EndVelc)
					{
						pCtrlPP->OutputVel -= VelDec;
					}
				}
				else
				{
                                  
                                    
					pCtrlPP->RmdVel  += AccRem;
					
					if(pCtrlPP->RmdVel  >= TASKB_FRQ)
					{
						pCtrlPP->RmdVel   -= TASKB_FRQ;
						VelAcc++;
					}
					if(pCtrlPP->OutputVel + VelAcc <=TarVel )
					{
						pCtrlPP->OutputVel += VelAcc;
					}			
                                        
				}
			}		
		
			break;
		}
		
	default:
		 //return FALSE; // todo
	    break;
	}

	VelQuot = pCtrlPP->OutputVel /(INT32)TASKB_FRQ;
	VelRem = pCtrlPP->OutputVel - VelQuot*(INT32)TASKB_FRQ;
		
	pCtrlPP->RmdPos += VelRem;
	if(pCtrlPP->RmdPos >= TASKB_FRQ)
	{
		pCtrlPP->RmdPos -= TASKB_FRQ;
		VelQuot++;
	}
	else if(pCtrlPP->RmdPos <= -(INT32)TASKB_FRQ)
	{
		pCtrlPP->RmdPos += TASKB_FRQ;
		VelQuot--;
	}

	//Position command calculation
	if((pCtrlPP->PlanState & 0x100) == 0x100)
	{
		if(pCtrlPP->OutputPos + VelQuot < pCtrlPP->TargetPos)
		{				
			pCtrlPP->IntegralPos += VelQuot;
			pCtrlPP->OutputPos = (INT32)pCtrlPP->IntegralPos + pCtrlPP->InitPos;
		}
		else
		{
			pCtrlPP->OutputPos = pCtrlPP->TargetPos;
                        pCtrlPP->OutputVel =0; 
		}

		/*The error of position and speed is because the mathematical integer operation can't be divisible, not the difference caused by the precision of the operation.
		Therefore, the maximum possible error is judged at the end to prevent the speed from decelerating to zero, and the position is still not up*/
		if((pCtrlPP->OutputPos + (INT32)pCtrlPP->TargetVel/(INT32)TASKB_FRQ > pCtrlPP->TargetPos) && (pCtrlPP->OutputVel <EndVelc + VelDec ))
		{
			pCtrlPP->OutputPos = pCtrlPP->TargetPos;
		}
	}
	else
	{
		if(pCtrlPP->OutputPos + VelQuot > pCtrlPP->TargetPos)
		{
			pCtrlPP->IntegralPos += VelQuot;
			pCtrlPP->OutputPos = (INT32)pCtrlPP->IntegralPos + pCtrlPP->InitPos;
		}
		else
		{
			pCtrlPP->OutputPos = pCtrlPP->TargetPos;
                        pCtrlPP->OutputVel =0; 
		}

		/*The error of position and speed is because the mathematical integer operation can't be divisible, not the difference caused by the precision of the operation.
		Therefore, the maximum possible error is judged at the end to prevent the speed from decelerating to zero, and the position is still not up*/
		if((pCtrlPP->OutputPos - (INT32)pCtrlPP->TargetVel/(INT32)TASKB_FRQ < pCtrlPP->TargetPos) && (pCtrlPP->OutputVel >EndVelc - VelDec ))
		{
			pCtrlPP->OutputPos = pCtrlPP->TargetPos;
		}
	}
        
        return  1;

	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void PP_Limit(TCiA402Axis *Cia402Axis )
{
	CiA402_PRM	        *Objects;
    CIA402_STATUS_WORD      StatusWord = {0};
	Objects = Cia402Axis->Objects;
    StatusWord.all = Objects->Statusword0x6041;
	
	// accerlation limit
	if(Objects->ProfileAcceleration0x6083)
	{
		Cia402Axis->PP_Ctrl.Acc = MlibLimitu32(Objects->ProfileAcceleration0x6083, Objects->Max_acceleration0x60C5);
	}
	else
	{
		Cia402Axis->PP_Ctrl.Acc = Objects->Max_acceleration0x60C5;
	}

	// deceleration limit
	if(Objects->ProfileDeceleration0x6084)
	{
		Cia402Axis->PP_Ctrl.Dec = MlibLimitu32(Objects->ProfileDeceleration0x6084, Objects->Max_deceleration0x60C6);
	}
	else
	{
		if(Objects->ProfileAcceleration0x6083)
		{
			Cia402Axis->PP_Ctrl.Dec = MlibLimitu32(Objects->ProfileAcceleration0x6083, Objects->Max_deceleration0x60C6);
		}
		else
		{
			Cia402Axis->PP_Ctrl.Dec = Objects->Max_deceleration0x60C6;
		}
	}
	
	// target velocity limit	
	if(Cia402Axis->PP_Ctrl.TargetVel >Objects->Max_profile_velocity0x607F)
	{
		Cia402Axis->PP_Ctrl.TargetVel = Objects->Max_profile_velocity0x607F;
	}

	// End velocity limit
	if(Cia402Axis->PP_Ctrl.EndVel > Objects->Max_profile_velocity0x607F)
	{
		Cia402Axis->PP_Ctrl.EndVel = Objects->Max_profile_velocity0x607F;
	}
	
	if(Cia402Axis->PP_Ctrl.EndVel > Cia402Axis->PP_Ctrl.TargetVel)
	{
		Cia402Axis->PP_Ctrl.EndVel = Cia402Axis->PP_Ctrl.TargetVel;
	}

	// Position limit
	if(Cia402Axis->PP_Ctrl.TargetPos < Objects->MinSoftwarePositionLimit0x607D)
	{
		Cia402Axis->PP_Ctrl.TargetPos = Objects->MinSoftwarePositionLimit0x607D;
        StatusWord.bit.InLimitActive = 1;
	}
	else if(Cia402Axis->PP_Ctrl.TargetPos > Objects->MaxSoftwarePositionLimit0x607D)
	{
		Cia402Axis->PP_Ctrl.TargetPos = Objects->MaxSoftwarePositionLimit0x607D;
        StatusWord.bit.InLimitActive = 1;
    }
    else
    {
        StatusWord.bit.InLimitActive = 0;
	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void Cia402_PP_Mode(TCiA402Axis *Cia402Axis, BOOL BaseEnable )
{
	CiA402_PRM	            *Objects;
	CIA402_STATUS_WORD      StatusWord = {0};
	CIA402_CTRL_WORD        ControlWord = {0};
	CIA402_CTRL_WORD        LastCtrlWordForMode = {0};

	Objects = Cia402Axis->Objects;
	StatusWord.all = Objects->Statusword0x6041;
	ControlWord.all = Objects->Controlword0x6040;
	LastCtrlWordForMode.all = Cia402Axis->LastCtrlWordForMode;

	if(BaseEnable == FALSE)
	{
		MlibResetLongMemory(&Cia402Axis->PP_Ctrl, sizeof(Cia402Axis->PP_Ctrl)/4);
		Cia402Axis->PP_Ctrl.InitPos = Objects->PositionActualValue0x6064;       
        Cia402Axis->PP_Ctrl.TargetPos = Objects->PositionActualValue0x6064;
		Cia402Axis->PP_Ctrl.IntegralPos = 0;
		Cia402Axis->PP_Ctrl.RmdVel = 0;
		Cia402Axis->PP_Ctrl.RmdPos = 0;
        Cia402Axis->PP_Ctrl.OutputPos = Cia402Axis->PP_Ctrl.InitPos;
        Cia402Axis->PP_Ctrl.OutputVel =0;
        Objects->PositionDemandValue0x6062 = Objects->PositionActualValue0x6064;

		return;
	}	
    if(ControlWord.bit.Halt)
    {
        Cia402Axis->PP_Ctrl.FirstRun = 0; 
        return;
    }   
    
    if(Cia402Axis->ChangControlMode == PVTPP_MODECHANGE)
    {
        MlibResetLongMemory(&Cia402Axis->PP_Ctrl, sizeof(Cia402Axis->PP_Ctrl)/4);
        Cia402Axis->PP_Ctrl.TargetPos = Objects->TargetPosition0x607A;
        Cia402Axis->PP_Ctrl.InitVel = Objects->VelocityActualValue0x606C;
        Cia402Axis->PP_Ctrl.TargetVel = Objects->ProfileVelocity0x6081;
        Cia402Axis->PP_Ctrl.EndVel = Objects->EndVelocity0x6082;
        PP_Limit(Cia402Axis);
//        Objects->PositionDemandValue0x6062 = Objects->PositionActualValue0x6064;
        Cia402Axis->PP_Ctrl.InitPos = Objects->PositionDemandValue0x6062;
        Cia402Axis->PP_Ctrl.IntegralPos = 0;
        Cia402Axis->PP_Ctrl.RmdVel = 0;
        Cia402Axis->PP_Ctrl.RmdPos = 0;
        Cia402Axis->PP_Ctrl.OutputPos = Cia402Axis->PP_Ctrl.InitPos;
        Cia402Axis->PP_Ctrl.OutputVel = Objects->VelocityActualValue0x606C;
	Cia402Axis->PP_Ctrl.FirstRun = 1; 
        Cia402Axis->PP_Ctrl.NewPointMark = 0;
        Cia402Axis->ChangControlMode = NONE_MODECHANGE;
        
    }
    else  if(Cia402Axis->ChangControlMode == PTTPP_MODECHANGE)
    {
        MlibResetLongMemory(&Cia402Axis->PP_Ctrl, sizeof(Cia402Axis->PP_Ctrl)/4);
        Cia402Axis->PP_Ctrl.TargetPos = Objects->TargetPosition0x607A;
        Cia402Axis->PP_Ctrl.InitVel = Objects->VelocityActualValue0x606C;
        Cia402Axis->PP_Ctrl.TargetVel = Objects->ProfileVelocity0x6081;
        Cia402Axis->PP_Ctrl.EndVel = Objects->EndVelocity0x6082;
        PP_Limit(Cia402Axis);
//        Objects->PositionDemandValue0x6062 = Objects->PositionActualValue0x6064;
        Cia402Axis->PP_Ctrl.InitPos = Objects->PositionDemandValue0x6062;
        Cia402Axis->PP_Ctrl.IntegralPos = 0;
        Cia402Axis->PP_Ctrl.RmdVel = 0;
        Cia402Axis->PP_Ctrl.RmdPos = 0;
        Cia402Axis->PP_Ctrl.OutputPos = Cia402Axis->PP_Ctrl.InitPos;
        Cia402Axis->PP_Ctrl.OutputVel = Objects->VelocityActualValue0x606C;
	Cia402Axis->PP_Ctrl.FirstRun = 1; 
        Cia402Axis->PP_Ctrl.NewPointMark = 0;
        Cia402Axis->ChangControlMode = NONE_MODECHANGE;
        
    }
    else if(Cia402Axis->PP_Ctrl.FirstRun == 0 )
    {       
        Cia402Axis->PP_Ctrl.InitPos = Objects->PositionActualValue0x6064;       
        Cia402Axis->PP_Ctrl.TargetPos = Objects->PositionActualValue0x6064;
		Cia402Axis->PP_Ctrl.IntegralPos = 0;
		Cia402Axis->PP_Ctrl.RmdVel = 0;
		Cia402Axis->PP_Ctrl.RmdPos = 0;
        Cia402Axis->PP_Ctrl.OutputPos = Cia402Axis->PP_Ctrl.InitPos;
        Cia402Axis->PP_Ctrl.OutputVel =0;
        Objects->PositionDemandValue0x6062 = Objects->PositionActualValue0x6064;
		Cia402Axis->PP_Ctrl.FirstRun = 1;  
    }
    

	//Control word corresponding bit rising edge trigger position contour mode
	if(!LastCtrlWordForMode.bit.OperSpecific_b4 && ControlWord.bit.OperSpecific_b4)
	{	
                 Cia402Axis->PTP_Ctrl.Mark = 0 ;
                 Cia402Axis->PTP_Ctrl.StopCNT = 0;
		// Single set-point 
		if(ControlWord.bit.OperSpecific_b5)
		{
			if(ControlWord.bit.OperSpecific_b6)
			{	// relative 
				Cia402Axis->PP_Ctrl.TargetPos = Objects->PositionDemandValue0x6062 + Objects->TargetPosition0x607A;
			}
			else
			{	// absolute	
				Cia402Axis->PP_Ctrl.TargetPos = Objects->TargetPosition0x607A;
			}
			Cia402Axis->PP_Ctrl.TargetVel = Objects->ProfileVelocity0x6081;
			Cia402Axis->PP_Ctrl.EndVel = Objects->EndVelocity0x6082;
			
			PP_Limit(Cia402Axis);
			
			Cia402Axis->PP_Ctrl.InitPos = Objects->PositionDemandValue0x6062;
			Cia402Axis->PP_Ctrl.InitVel = Cia402Axis->PP_Ctrl.OutputVel;
			Cia402Axis->PP_Ctrl.IntegralPos = 0;
			Cia402Axis->PP_Ctrl.RmdVel = 0;
			Cia402Axis->PP_Ctrl.RmdPos = 0;
            Cia402Axis->PP_Ctrl.OutputPos = Cia402Axis->PP_Ctrl.InitPos;


			Cia402Axis->PP_Ctrl.NewPointMark = 0;
		}
		else
		{
			//Set of set-points
			Cia402Axis->PP_Ctrl.NewPointMark = 1;
		}
                

	}

	//Set of set-points
	if(Cia402Axis->PP_Ctrl.NewPointMark)
	{
	    //Speed maintains the trigger time before reaching the first point
		if(ControlWord.bit.OperSpecific_b9)
		{
		    //Determine if the first point is reached
			if(Cia402Axis->PP_Ctrl.OutputPos == Cia402Axis->PP_Ctrl.TargetPos)
			{
				if(ControlWord.bit.OperSpecific_b6)
				{	// relative 
					Cia402Axis->PP_Ctrl.TargetPos = Objects->PositionDemandValue0x6062 + Objects->TargetPosition0x607A;
				}
				else
				{	// absolute	
					Cia402Axis->PP_Ctrl.TargetPos = Objects->TargetPosition0x607A;
				}

				Cia402Axis->PP_Ctrl.TargetVel = Objects->ProfileVelocity0x6081;
				Cia402Axis->PP_Ctrl.EndVel = Objects->EndVelocity0x6082;
				
				PP_Limit(Cia402Axis);
				
				Cia402Axis->PP_Ctrl.InitPos = Objects->PositionDemandValue0x6062;
				Cia402Axis->PP_Ctrl.InitVel = Cia402Axis->PP_Ctrl.OutputVel;
				Cia402Axis->PP_Ctrl.IntegralPos = 0;
				Cia402Axis->PP_Ctrl.RmdVel = 0;
				Cia402Axis->PP_Ctrl.RmdPos = 0;
                                Cia402Axis->PP_Ctrl.OutputPos = Cia402Axis->PP_Ctrl.InitPos;

				Cia402Axis->PP_Ctrl.NewPointMark = 0;
			}

			if((Cia402Axis->PP_Ctrl.OutputPos != Cia402Axis->PP_Ctrl.TargetPos) && (Cia402Axis->PP_Ctrl.NewPointMark == 1))
			{
				if( Cia402Axis->PP_Ctrl.OutputVel>=0)
				{
					Cia402Axis->PP_Ctrl.TargetVel = Cia402Axis->PP_Ctrl.OutputVel;
					Cia402Axis->PP_Ctrl.EndVel = Cia402Axis->PP_Ctrl.OutputVel;
				}
				else
				{
					Cia402Axis->PP_Ctrl.TargetVel = -Cia402Axis->PP_Ctrl.OutputVel;
					Cia402Axis->PP_Ctrl.EndVel = -Cia402Axis->PP_Ctrl.OutputVel;
				}
				PP_Limit(Cia402Axis);
				
				Cia402Axis->PP_Ctrl.InitPos = Objects->PositionDemandValue0x6062;
                Cia402Axis->PP_Ctrl.OutputPos = Cia402Axis->PP_Ctrl.InitPos;
				Cia402Axis->PP_Ctrl.InitVel = Cia402Axis->PP_Ctrl.OutputVel;
				Cia402Axis->PP_Ctrl.IntegralPos = 0;
				Cia402Axis->PP_Ctrl.RmdVel = 0;
				Cia402Axis->PP_Ctrl.RmdPos = 0;
				Cia402Axis->PP_Ctrl.NewPointMark = 2;
			}
		}
		else
		{
		    //update the configuration data of the next point untill reach the first point and 
			if(Cia402Axis->PP_Ctrl.OutputPos == Cia402Axis->PP_Ctrl.TargetPos)
			{
				if(ControlWord.bit.OperSpecific_b6)
				{	// relative 
					Cia402Axis->PP_Ctrl.TargetPos = Objects->PositionDemandValue0x6062 + Objects->TargetPosition0x607A;
				}
				else
				{	// absolute	
					Cia402Axis->PP_Ctrl.TargetPos = Objects->TargetPosition0x607A;
				}
				
				Cia402Axis->PP_Ctrl.TargetVel = Objects->ProfileVelocity0x6081;
				Cia402Axis->PP_Ctrl.EndVel = Objects->EndVelocity0x6082;
				
				PP_Limit(Cia402Axis);
				
				Cia402Axis->PP_Ctrl.InitVel = Cia402Axis->PP_Ctrl.OutputVel;
				Cia402Axis->PP_Ctrl.InitPos = Objects->PositionDemandValue0x6062;
                Cia402Axis->PP_Ctrl.OutputPos = Cia402Axis->PP_Ctrl.InitPos;
				Cia402Axis->PP_Ctrl.IntegralPos = 0;
				Cia402Axis->PP_Ctrl.RmdVel = 0;
				Cia402Axis->PP_Ctrl.RmdPos = 0;
				Cia402Axis->PP_Ctrl.NewPointMark = 0;
			}
		}
                
	}

    if(Cia402Axis->PP_Ctrl.FirstRun != 0)
    {
		trapPosCtrl(&Cia402Axis->PP_Ctrl);
								   
	    Objects->PositionDemandValue0x6062 = (INT32)Cia402Axis->PP_Ctrl.OutputPos;
    }
	
	// Target Reach
	if(Objects->PositionActualValue0x6064 < (Cia402Axis->PP_Ctrl.TargetPos + (INT32)Objects->PositionWindow0x6067)
	  && Objects->PositionActualValue0x6064 > (Cia402Axis->PP_Ctrl.TargetPos - (INT32)Objects->PositionWindow0x6067)
	  	&& !Cia402Axis->PP_Ctrl.NewPointMark)
	{
		if(Cia402Axis->PP_Ctrl.PosWindowCnt < Objects->PositionWindowTime0x6068 * 1000/TASKB_CYCLEUS)
		{
			Cia402Axis->PP_Ctrl.PosWindowCnt++;
			StatusWord.bit.TargetReached = 0;
		}
		else
		{
                        StatusWord.bit.TargetReached = 1;
//			Cia402Axis->PP_Ctrl.OutputVel =0;
		}
                
                if(  StatusWord.bit.TargetReached == 1 && Cia402Axis->PTP_Ctrl.Mark == 0 && Cia402Axis->PTP_Ctrl.StopCNT++ >= Cia402Axis->PTP_Ctrl.StopTime)
                {
                    Cia402Axis->PTP_Ctrl.AxisEnableStep = 4 ;
                }
                
  	}
        else
        {
            Cia402Axis->PP_Ctrl.PosWindowCnt = 0;
            StatusWord.bit.TargetReached = 0;
        }
        
        
    if(ControlWord.bit.OperSpecific_b4)
    {
        StatusWord.bit.OperSpecific_b12 = 1;
    }
    else
    {
        StatusWord.bit.OperSpecific_b12 = 0;
    }
	Cia402Axis->LastCtrlWordForMode = Objects->Controlword0x6040;
	Objects->Statusword0x6041 = StatusWord.all;

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
