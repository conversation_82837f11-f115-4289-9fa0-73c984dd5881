<?xml version="1.0"?>
<settings>
    <Jet>
        <PrevWtdReset>Hardware</PrevWtdReset>
        <OnlineReset>Software</OnlineReset>
        <JetConnSerialNo>14978</JetConnSerialNo>
        <JetConnFoundProbes />
        <DisableInterrupts>0</DisableInterrupts>
        <LeaveRunning>0</LeaveRunning>
        <MultiCoreRunAll>0</MultiCoreRunAll>
        <CpuHaltOnBreakpointSet>0</CpuHaltOnBreakpointSet>
    </Jet>
    <PlDriver>
        <FirstRun>0</FirstRun>
        <MemConfigValue>D:\2025\Code\humanjoint/script/R9A07G084M04.ddf</MemConfigValue>
    </PlDriver>
    <DebugChecksum>
        <Checksum>1169126587</Checksum>
    </DebugChecksum>
    <Exceptions>
        <StopOnUncaught>_ 0</StopOnUncaught>
        <StopOnThrow>_ 0</StopOnThrow>
    </Exceptions>
    <TerminalIO>
        <InputSource>1</InputSource>
        <InputMode2>10</InputMode2>
        <Filename>$PROJ_DIR$\TermIOInput.txt</Filename>
        <InputEcho>1</InputEcho>
        <ShowReset>0</ShowReset>
        <InputEncodingICU>0</InputEncodingICU>
        <OutputEncodingICU>0</OutputEncodingICU>
    </TerminalIO>
    <Stack>
        <FillEnabled>0</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <Disassembly>
        <InstrCount>0</InstrCount>
    </Disassembly>
    <SWOManager>
        <SamplingDivider>8192</SamplingDivider>
        <OverrideClock>0</OverrideClock>
        <CpuClock>955981737967633739</CpuClock>
        <SwoClock>7598542775866368778</SwoClock>
        <DataLogMode>0</DataLogMode>
        <ItmPortsEnabled>63</ItmPortsEnabled>
        <ItmTermIOPorts>1</ItmTermIOPorts>
        <ItmLogPorts>0</ItmLogPorts>
        <ItmLogFile>$PROJ_DIR$\ITM.log</ItmLogFile>
        <PowerForcePC>1</PowerForcePC>
        <PowerConnectPC>1</PowerConnectPC>
    </SWOManager>
    <ArmDriver>
        <EnableCache>1</EnableCache>
        <EnforceMemoryConfiguration>1</EnforceMemoryConfiguration>
    </ArmDriver>
    <struct_types>
        <Fmt0>AXIS_HANDLE-HardwareVer	3	0</Fmt0>
        <Fmt1>Angle_T-Angle	3	0</Fmt1>
        <Fmt2>CANFD_Frame-can_id	4	0</Fmt2>
        <Fmt3>CANFD_FrameHeader-length	4	0</Fmt3>
        <Fmt4>CANFD_SlaveState-lastRxTime	4	0</Fmt4>
        <Fmt5>CANFD_SlaveState-lastTxTime	4	0</Fmt5>
        <Fmt6>CANFD_SlaveState-timestamp	4	0</Fmt6>
        <Fmt7>CiA402_PRM-Statusword0x6041	4	0</Fmt7>
        <Fmt8>CiA402_PRM-TargetVelocity0x60FF	4	0</Fmt8>
        <Fmt9>MOTOR_PRM-RatCur	4	0</Fmt9>
        <Fmt10>MOTOR_PRM-RatFreq	3	0</Fmt10>
        <Fmt11>MOTOR_PRM-RatPow	3	0</Fmt11>
        <Fmt12>MOTOR_PRM-RatSpd	4	0</Fmt12>
        <Fmt13>MOTOR_PRM-RatTrq	4	0</Fmt13>
        <Fmt14>MOTOR_PRM-RatV	3	0</Fmt14>
        <Fmt15>can_frame_t-id	4	0</Fmt15>
    </struct_types>
    <array_types>
        <Fmt0>uint32_t[2]	4	0</Fmt0>
        <Fmt1>uint32_t[5]	4	0</Fmt1>
    </array_types>
    <watch_formats>
        <Fmt0>{W}42:-12	3	0</Fmt0>
        <Fmt1>{W}42:CanFdTimeMs	4	0</Fmt1>
        <Fmt2>{W}42:LastTime	4	0</Fmt2>
        <Fmt3>{W}42:cal_crc32	4	0</Fmt3>
        <Fmt4>{W}42:cpu_registers_CPSR	4	0</Fmt4>
        <Fmt5>{W}42:cpu_registers_DFAR	4	0</Fmt5>
        <Fmt6>{W}42:cpu_registers_DFSR	4	0</Fmt6>
        <Fmt7>{W}42:cpu_registers_IFAR	4	0</Fmt7>
        <Fmt8>{W}42:cpu_registers_IFSR	4	0</Fmt8>
        <Fmt9>{W}42:cpu_registers_LR	4	0</Fmt9>
        <Fmt10>{W}42:cpu_registers_SPSR	4	0</Fmt10>
        <Fmt11>{W}42:cpu_registers_cpsr	4	0</Fmt11>
        <Fmt12>{W}42:cpu_registers_lr	4	0</Fmt12>
        <Fmt13>{W}42:cpu_registers_pc	4	0</Fmt13>
        <Fmt14>{W}42:cpu_registers_r0	4	0</Fmt14>
        <Fmt15>{W}42:cpu_registers_r1	4	0</Fmt15>
        <Fmt16>{W}42:cpu_registers_r10	4	0</Fmt16>
        <Fmt17>{W}42:cpu_registers_r11	4	0</Fmt17>
        <Fmt18>{W}42:cpu_registers_r12	4	0</Fmt18>
        <Fmt19>{W}42:cpu_registers_r2	4	0</Fmt19>
        <Fmt20>{W}42:cpu_registers_r3	4	0</Fmt20>
        <Fmt21>{W}42:cpu_registers_r4	4	0</Fmt21>
        <Fmt22>{W}42:cpu_registers_r5	4	0</Fmt22>
        <Fmt23>{W}42:cpu_registers_r6	4	0</Fmt23>
        <Fmt24>{W}42:cpu_registers_r7	4	0</Fmt24>
        <Fmt25>{W}42:cpu_registers_r8	4	0</Fmt25>
        <Fmt26>{W}42:cpu_registers_r9	4	0</Fmt26>
        <Fmt27>{W}42:cpu_registers_sp	4	0</Fmt27>
        <Fmt28>{W}42:data	4	0</Fmt28>
        <Fmt29>{W}42:g_CrcAddr	4	0</Fmt29>
        <Fmt30>{W}42:g_Get_Crc	4	0</Fmt30>
        <Fmt31>{W}42:g_PosData	4	0</Fmt31>
        <Fmt32>{W}42:g_crc32	4	0</Fmt32>
        <Fmt33>{W}42:ilaspTime	4	0</Fmt33>
        <Fmt34>{W}42:input	4	0</Fmt34>
        <Fmt35>{W}42:inputTemp	4	0</Fmt35>
    </watch_formats>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <ETMTraceWindow>
        <PortWidth>4</PortWidth>
        <PortMode>0</PortMode>
        <CaptureDataValues>0</CaptureDataValues>
        <CaptureDataAddresses>0</CaptureDataAddresses>
        <CaptureDataRange>0</CaptureDataRange>
        <DataFirst>0</DataFirst>
        <DataLast>4294967295</DataLast>
        <StopWhen>0</StopWhen>
        <StallCPU>0</StallCPU>
        <NoPCCapture>0</NoPCCapture>
    </ETMTraceWindow>
    <Trace2>
        <Enabled>0</Enabled>
        <ShowSource>0</ShowSource>
    </Trace2>
    <SWOTraceWindow>
        <ForcedPcSampling>0</ForcedPcSampling>
        <ForcedInterruptLogs>0</ForcedInterruptLogs>
        <ForcedItmLogs>0</ForcedItmLogs>
        <EventCPI>0</EventCPI>
        <EventEXC>0</EventEXC>
        <EventFOLD>0</EventFOLD>
        <EventLSU>0</EventLSU>
        <EventSLEEP>0</EventSLEEP>
    </SWOTraceWindow>
    <DriverProfiling>
        <Enabled>0</Enabled>
        <Mode>2</Mode>
        <Graph>0</Graph>
        <Symbiont>0</Symbiont>
        <Exclusions />
    </DriverProfiling>
    <CallStackLog>
        <Enabled>0</Enabled>
    </CallStackLog>
    <CallStackStripe>
        <ShowTiming>1</ShowTiming>
    </CallStackStripe>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <DisassembleMode>
        <mode>0</mode>
    </DisassembleMode>
    <Breakpoints2>
        <Count>0</Count>
    </Breakpoints2>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
</settings>
