[FileInfo]
FileName=RF200.eds
FileVersion=1
FileRevision=1
EDSVersion=4.0
Description=EDS for Rtelligent Slave DS402
CreationTime=08:07PM
CreationDate=05-27-2024
CreatedBy=CANFestival
ModificationTime=08:07PM
ModificationDate=12-22-2022
ModifiedBy=CANFestival

[DeviceInfo]
VendorName=Jihua Laboratory Intelligent Robot Engineering Research Center
VendorNumber=0x00000130
ProductName=RF200
ProductNumber=0x01300007
RevisionNumber=0x00000100
OrderCode=0
BaudRate_10=1
BaudRate_20=1
BaudRate_50=1
BaudRate_125=1
BaudRate_250=1
BaudRate_500=1
BaudRate_800=1
BaudRate_1000=1
SimpleBootUpMaster=0
SimpleBootUpSlave=1
Granularity=8
DynamicChannelsSupported=0
CompactPDO=0
GroupMessaging=0
NrOfRXPDO=4
NrOfTXPDO=4
LSS_Supported=0

[DummyUsage]
Dummy0001=0
Dummy0002=1
Dummy0003=1
Dummy0004=1
Dummy0005=1
Dummy0006=1
Dummy0007=1

[Comments]
Lines=0

[MandatoryObjects]
SupportedObjects=3
1=0x1000
2=0x1001
3=0x1018

[1000]
ParameterName=Device Type
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0x00020192
PDOMapping=0

[1001]
ParameterName=Error Register
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=0
PDOMapping=1

[1018]
ParameterName=Identity
ObjectType=0x9
SubNumber=5

[1018sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=4
PDOMapping=0

[1018sub1]
ParameterName=Vendor ID
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1018sub2]
ParameterName=Product Code
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1018sub3]
ParameterName=Revision Number
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1018sub4]
ParameterName=Serial Number
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[OptionalObjects]
SupportedObjects=85
1=0x1003
2=0x1005
3=0x1006
4=0x1008
5=0x1009
6=0x100A
7=0x100C
8=0x100D
9=0x1010
10=0x1011
11=0x1014
12=0x1016
13=0x1017
14=0x1200
15=0x1400
16=0x1401
17=0x1402
18=0x1403
19=0x1600
20=0x1601
21=0x1602
22=0x1603
23=0x1800
24=0x1801
25=0x1802
26=0x1803
27=0x1A00
28=0x1A01
29=0x1A02
30=0x1A03
31=0x603F
32=0x6040
33=0x6041
34=0x605A
35=0x605B
36=0x605C
37=0x605D
38=0x605E
39=0x6060
40=0x6061
41=0x6062
42=0x6063
43=0x6064
44=0x6065
45=0x6066
46=0x6067
47=0x6068
48=0x6069
49=0x606B
50=0x606C
51=0x606D
52=0x606E
53=0x606F
54=0x6070
55=0x6071
56=0x6074
57=0x6077
58=0x607A
59=0x607C
60=0x607D
61=0x607F
62=0x6081
63=0x6082
64=0x6083
65=0x6084
66=0x6085
67=0x6086
68=0x6087
69=0x6088
70=0x6091
71=0x6092
72=0x6098
73=0x6099
74=0x609A
75=0x60A4
76=0x60B0
77=0x60B1
78=0x60B2
79=0x60C5
80=0x60C6
81=0x60E0
82=0x60E1
83=0x60F4
84=0x60FC
85=0x60FF

[1003]
ParameterName=Pre-defined Error Field
ObjectType=0x8
SubNumber=2

[1003sub0]
ParameterName=Number of Errors
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=1
PDOMapping=0

[1003sub1]
ParameterName=Standard Error Field
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1005]
ParameterName=SYNC COB ID
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x80
PDOMapping=0

[1006]
ParameterName=Communication Cycle Period
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1008]
ParameterName=Manufacturer Device Name
ObjectType=0x7
DataType=0x0009
AccessType=ro
DefaultValue= RossFinch-I Servo Driver
PDOMapping=0

[1009]
ParameterName=Manufacturer Hardware Version
ObjectType=0x7
DataType=0x0009
AccessType=ro
DefaultValue=04.02.20220524
PDOMapping=0

[100A]
ParameterName=Manufacturer Software Version
ObjectType=0x7
DataType=0x0009
AccessType=ro
DefaultValue=01.04.01.0402.20221212
PDOMapping=0

[100C]
ParameterName=Guard Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[100D]
ParameterName=Life Time Factor
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1010]
ParameterName=Store parameters
ObjectType=0x9
SubNumber=4

[1010sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=3
PDOMapping=0

[1010sub1]
ParameterName=Save All Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=1
PDOMapping=0

[1010sub2]
ParameterName=Save Communication Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=1
PDOMapping=0

[1010sub3]
ParameterName=Save Application Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=1
PDOMapping=0

[1011]
ParameterName=Restore Default Parameters
ObjectType=0x9
SubNumber=4

[1011sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=3
PDOMapping=0

[1011sub1]
ParameterName=Restore All Default Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1011sub2]
ParameterName=Restore Communication Default Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1011sub3]
ParameterName=Restore Application Default Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1014]
ParameterName=Emergency COB ID
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x80
PDOMapping=0

[1016]
ParameterName=Consumer Heartbeat Time
ObjectType=0x8
SubNumber=2

[1016sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=1
PDOMapping=0

[1016sub1]
ParameterName=Consumer Heartbeat Time
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x007FFFFF
PDOMapping=0

[1017]
ParameterName=Producer Heartbeat Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=1000
PDOMapping=0

[1200]
ParameterName=Server SDO Parameter
ObjectType=0x9
SubNumber=3

[1200sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1200sub1]
ParameterName=COB ID Client to Server (Receive SDO)
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=$NODEID+0x600
PDOMapping=0

[1200sub2]
ParameterName=COB ID Server to Client (Transmit SDO)
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=$NODEID+0x580
PDOMapping=0

[1400]
ParameterName=Receive PDO 1 Parameter
ObjectType=0x9
SubNumber=5

[1400sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=5
PDOMapping=0

[1400sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x200
PDOMapping=0

[1400sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=1
PDOMapping=0

[1400sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1400sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1400sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1401]
ParameterName=Receive PDO 2 Parameter
ObjectType=0x9
SubNumber=5

[1401sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=5
PDOMapping=0

[1401sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x300
PDOMapping=0

[1401sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1401sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1401sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1401sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1402]
ParameterName=Receive PDO 3 Parameter
ObjectType=0x9
SubNumber=5

[1402sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=5
PDOMapping=0

[1402sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x400
PDOMapping=0

[1402sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1402sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1402sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1402sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1403]
ParameterName=Receive PDO 4 Parameter
ObjectType=0x9
SubNumber=5

[1403sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=5
PDOMapping=0

[1403sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x500
PDOMapping=0

[1403sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1403sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1403sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1403sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600]
ParameterName=Receive PDO 1 Mapping
ObjectType=0x8
SubNumber=9

[1600sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=3
PDOMapping=0

[1600sub1]
ParameterName=PDO 1 Mapping for an application object 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x60400010
PDOMapping=0

[1600sub2]
ParameterName=PDO 1 Mapping for an application object 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x60ff0020
PDOMapping=0

[1600sub3]
ParameterName=PDO 1 Mapping for an application object 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub4]
ParameterName=PDO 1 Mapping for an application object 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub5]
ParameterName=PDO 1 Mapping for an application object 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub6]
ParameterName=PDO 1 Mapping for an application object 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub7]
ParameterName=PDO 1 Mapping for an application object 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub8]
ParameterName=PDO 1 Mapping for an application object 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601]
ParameterName=Receive PDO 2 Mapping
ObjectType=0x8
SubNumber=9

[1601sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=2
PDOMapping=0

[1601sub1]
ParameterName=PDO 2 Mapping for an application object 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x607a0020
PDOMapping=0

[1601sub2]
ParameterName=PDO 2 Mapping for an application object 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x60810020
PDOMapping=0

[1601sub3]
ParameterName=PDO 2 Mapping for an application object 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub4]
ParameterName=PDO 2 Mapping for an application object 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub5]
ParameterName=PDO 2 Mapping for an application object 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub6]
ParameterName=PDO 2 Mapping for an application object 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub7]
ParameterName=PDO 2 Mapping for an application object 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub8]
ParameterName=PDO 2 Mapping for an application object 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602]
ParameterName=Receive PDO 3 Mapping
ObjectType=0x8
SubNumber=9

[1602sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1602sub1]
ParameterName=PDO 3 Mapping for an application object 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub2]
ParameterName=PDO 3 Mapping for an application object 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub3]
ParameterName=PDO 3 Mapping for an application object 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub4]
ParameterName=PDO 3 Mapping for an application object 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub5]
ParameterName=PDO 3 Mapping for an application object 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub6]
ParameterName=PDO 3 Mapping for an application object 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub7]
ParameterName=PDO 3 Mapping for an application object 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub8]
ParameterName=PDO 3 Mapping for an application object 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603]
ParameterName=Receive PDO 4 Mapping
ObjectType=0x8
SubNumber=9

[1603sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1603sub1]
ParameterName=PDO 4 Mapping for an application object 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub2]
ParameterName=PDO 4 Mapping for an application object 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub3]
ParameterName=PDO 4 Mapping for an application object 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub4]
ParameterName=PDO 4 Mapping for an application object 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub5]
ParameterName=PDO 4 Mapping for an application object 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub6]
ParameterName=PDO 4 Mapping for an application object 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub7]
ParameterName=PDO 4 Mapping for an application object 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub8]
ParameterName=PDO 4 Mapping for an application object 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1800]
ParameterName=Transmit PDO 1 Parameter
ObjectType=0x9
SubNumber=6

[1800sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1800sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x180
PDOMapping=0

[1800sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=1
PDOMapping=0

[1800sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=1000
PDOMapping=0

[1800sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0


[1801]
ParameterName=Transmit PDO 2 Parameter
ObjectType=0x9
SubNumber=6

[1801sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1801sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x280
PDOMapping=0

[1801sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0x0a
PDOMapping=0

[1801sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=1000
PDOMapping=0

[1801sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802]
ParameterName=Transmit PDO 3 Parameter
ObjectType=0x9
SubNumber=6

[1802sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1802sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x380
PDOMapping=0

[1802sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0xFF
PDOMapping=0

[1802sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803]
ParameterName=Transmit PDO 4 Parameter
ObjectType=0x9
SubNumber=6

[1803sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1803sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x480
PDOMapping=0

[1803sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0


[1A00]
ParameterName=Transmit PDO 1 Mapping
ObjectType=0x8
SubNumber=9

[1A00sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=3
PDOMapping=0

[1A00sub1]
ParameterName=PDO 1 Mapping for a process data variable 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x60410010
PDOMapping=0

[1A00sub2]
ParameterName=PDO 1 Mapping for a process data variable 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x60610008
PDOMapping=0

[1A00sub3]
ParameterName=PDO 1 Mapping for a process data variable 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub4]
ParameterName=PDO 1 Mapping for a process data variable 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub5]
ParameterName=PDO 1 Mapping for a process data variable 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub6]
ParameterName=PDO 1 Mapping for a process data variable 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub7]
ParameterName=PDO 1 Mapping for a process data variable 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub8]
ParameterName=PDO 1 Mapping for a process data variable 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01]
ParameterName=Transmit PDO 2 Mapping
ObjectType=0x8
SubNumber=9

[1A01sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=2
PDOMapping=0

[1A01sub1]
ParameterName=PDO 2 Mapping for a process data variable 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x60640020
PDOMapping=0

[1A01sub2]
ParameterName=PDO 2 Mapping for a process data variable 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x606c0020
PDOMapping=0

[1A01sub3]
ParameterName=PDO 2 Mapping for a process data variable 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub4]
ParameterName=PDO 2 Mapping for a process data variable 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub5]
ParameterName=PDO 2 Mapping for a process data variable 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub6]
ParameterName=PDO 2 Mapping for a process data variable 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub7]
ParameterName=PDO 2 Mapping for a process data variable 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub8]
ParameterName=PDO 2 Mapping for a process data variable 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02]
ParameterName=Transmit PDO 3 Mapping
ObjectType=0x8
SubNumber=9

[1A02sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1A02sub1]
ParameterName=PDO 3 Mapping for a process data variable 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub2]
ParameterName=PDO 3 Mapping for a process data variable 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub3]
ParameterName=PDO 3 Mapping for a process data variable 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub4]
ParameterName=PDO 3 Mapping for a process data variable 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub5]
ParameterName=PDO 3 Mapping for a process data variable 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub6]
ParameterName=PDO 3 Mapping for a process data variable 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub7]
ParameterName=PDO 3 Mapping for a process data variable 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub8]
ParameterName=PDO 3 Mapping for a process data variable 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03]
ParameterName=Transmit PDO 4 Mapping
ObjectType=0x8
SubNumber=9

[1A03sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1A03sub1]
ParameterName=PDO 4 Mapping for a process data variable 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub2]
ParameterName=PDO 4 Mapping for a process data variable 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub3]
ParameterName=PDO 4 Mapping for a process data variable 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub4]
ParameterName=PDO 4 Mapping for a process data variable 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub5]
ParameterName=PDO 4 Mapping for a process data variable 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub6]
ParameterName=PDO 4 Mapping for a process data variable 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub7]
ParameterName=PDO 4 Mapping for a process data variable 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub8]
ParameterName=PDO 4 Mapping for a process data variable 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[603F]
ParameterName=Last Error Code
ObjectType=0x7
DataType=0x0006
AccessType=ro
DefaultValue=0
PDOMapping=1

[6040]
ParameterName=Controlword
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[6041]
ParameterName=Statusword
ObjectType=0x7
DataType=0x0006
AccessType=ro
DefaultValue=0
PDOMapping=1

[605A]
ParameterName=Quick Stop Option Code
ObjectType=0x7
DataType=0x0003
AccessType=ro
DefaultValue=0
PDOMapping=1

[605B]
ParameterName=Shut Down Option Code
ObjectType=0x7
DataType=0x0003
AccessType=ro
DefaultValue=0
PDOMapping=1

[605C]
ParameterName=Disable Operation Option Code
ObjectType=0x7
DataType=0x0003
AccessType=ro
DefaultValue=0
PDOMapping=1

[605D]
ParameterName=Halt Option Code
ObjectType=0x7
DataType=0x0003
AccessType=ro
DefaultValue=0
PDOMapping=1

[605E]
ParameterName=Fault Reaction Code
ObjectType=0x7
DataType=0x0003
AccessType=ro
DefaultValue=0
PDOMapping=1

[6060]
ParameterName=Modes of operation
ObjectType=0x7
DataType=0x0002
AccessType=rw
DefaultValue=0
PDOMapping=1

[6061]
ParameterName=Modes of operation display
ObjectType=0x7
DataType=0x0002
AccessType=ro
DefaultValue=0
PDOMapping=1

[6062]
ParameterName=Position demannd value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[6063]
ParameterName=Position Actual Internal Value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[6064]
ParameterName=Position actual value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[6065]
ParameterName=Following Error Window
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6066]
ParameterName=Following Error Timeout
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[6067]
ParameterName=Position window
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[6068]
ParameterName=Position window time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[6069]
ParameterName=Velocity sensor actual value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[606B]
ParameterName=Velocity demand value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[606C]
ParameterName=Velocity actual value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[606D]
ParameterName=Velocity Window
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[606E]
ParameterName=Velocity Window Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[606F]
ParameterName=Velocity Threshold
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[6070]
ParameterName=Velocity Threshold Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[6071]
ParameterName=Target Torque
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[6074]
ParameterName=Target Demand
ObjectType=0x7
DataType=0x0003
AccessType=ro
DefaultValue=0
PDOMapping=1

[6077]
ParameterName=Torque Actual Value
ObjectType=0x7
DataType=0x0003
AccessType=ro
DefaultValue=0
PDOMapping=1

[607A]
ParameterName=Target position
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[607C]
ParameterName=Home offset
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[607D]
ParameterName=Software position limit
ObjectType=0x9
SubNumber=3

[607Dsub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[607Dsub1]
ParameterName=Minimal position limit
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=0

[607Dsub2]
ParameterName=Maximal position limit
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=0

[607F]
ParameterName=Maximal profile velocity
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[6081]
ParameterName=Profile velocity
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6082]
ParameterName=End Profile Velocity
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6083]
ParameterName=Profile acceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6084]
ParameterName=Profile deceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6085]
ParameterName=Quick stop deceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6086]
ParameterName=Motion profile type
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[6087]
ParameterName=Torque Slope
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6088]
ParameterName=Torque Profile Type
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=0

[6091]
ParameterName=Gear ratio
ObjectType=0x9
SubNumber=3

[6091sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6091sub1]
ParameterName=Motor Revolutions
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6091sub2]
ParameterName=Load Shaft Revolutions
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6092]
ParameterName=Feed constant
ObjectType=0x9
SubNumber=3

[6092sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6092sub1]
ParameterName=Feed
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6092sub2]
ParameterName=Shaft Revolutions
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6098]
ParameterName=Homing method
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[6099]
ParameterName=Homing speeds
ObjectType=0x9
SubNumber=3

[6099sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6099sub1]
ParameterName=Speed for switch search
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6099sub2]
ParameterName=Speed for zero search
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[609A]
ParameterName=Homing acceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[60A4]
ParameterName=Homing Acceleration
ObjectType=0x9
SubNumber=7

[60A4sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[60A4sub1]
ParameterName=Profile Jerk Acceleration1
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60A4sub2]
ParameterName=Profile Jerk Acceleration2
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60A4sub3]
ParameterName=Profile Jerk Acceleration3
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60A4sub4]
ParameterName=Profile Jerk Acceleration4
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60A4sub5]
ParameterName=Profile Jerk Acceleration5
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60A4sub6]
ParameterName=Profile Jerk Acceleration6
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60B0]
ParameterName=Position Offset
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60B1]
ParameterName=Velocity Offset
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60B2]
ParameterName=Torque Offset
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[60C5]
ParameterName=Max Acceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[60C6]
ParameterName=Max Deceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[60E0]
ParameterName=Positive Torque Limit Value
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[60E1]
ParameterName=Negative Torque Limit Value
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[60F4]
ParameterName=Following Error Actual Value
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60FC]
ParameterName=Position Demand Internal Value
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[60FF]
ParameterName=Target velocity
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[ManufacturerObjects]
SupportedObjects=0
