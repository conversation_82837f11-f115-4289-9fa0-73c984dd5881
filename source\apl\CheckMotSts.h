/****************************************************************************************************
 *
 * FILE NAME:  CheckMotSts.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.14
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	14-08-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _CHECK_MOT_STS_H_
#define _CHECK_MOT_STS_H_

#include "Bprm.h"
#include "BaseCmnStruct.h"


/****************************************************************************************************/
/*		DEFINES																						*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Definition for motor rotation detection																		*/
/*--------------------------------------------------------------------------------------------------*/
#define MOTSPD_TGMGN	12710		/* 12710 / 16777216 x 6600 = 5rpm	LpxCheckMotorRun( )reference*/
#define MOTSPD_DBLVL	60000		/* 16777216 * 0.7/175 = 67108 -> 60000  LpxCheckMotorStop( )reference*/

/*--------------------------------------------------------------------------------------------------*/
/*		TaskC motor status structure definition														*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	struct 
	{							
		INT32			VcmpSpdLevel;			/* Speed matching detection width	[2^24/OvrSpd]	*/
		INT32			TgonSpdLevel;			/* Motor rotation detection level	[2^24/OvrSpd]	*/
	} conf;
	
	struct
	{							
		INT32			MotSpd;					/* Motor Speed										*/
		INT32			AbsMotSpd;				/* Absolute Motor Speed								*/
		INT32			LstMotSpd;				/* Last Motor Speed for ModeSwAcc Cal.				*/
		INT32			MotPos;					/* Motor Position									*/
		INT32			dMotPos;				/* Delta Motor Position								*/
		INT32			PcmdCntr;				/* Pulse Command Counter	[pulse]					*/
		INT32			dPcmdCnt;				/* Delta Pulse Command		[pulse]					*/

		INT32			UnMotSpd;				/* Motor speed				[pulse/64ms]			*/
		INT32			UnPfbk64ms;				/* Motor speed				[pulse/64ms]			*/
		INT32			UnPcmd64ms;				/* Command pulse speed		[pulse/64ms]			*/
		INT32			Pfbk64SumCnt;			/* For monitor motor speed calculation				*/
		INT32			Pcmd64Sum;				/* For monitor command pulse speed calculation		*/
		INT32			Pfbk64Sum;				/* For monitor motor speed calculation				*/
		INT32			MotSpd64Sum;			/* For monitor motor speed calculation				*/

		INT32			IsqrSum;				/* For monitor For current load calculation			*/
		INT32			IsqrSumCnt;				/* For monitor For current load calculation			*/
		INT32			UnIsqrSum;				/* Cumulative load factor	[(15000/MaxTrq)/10sec]	*/
		UINT32			UnCrntRef;				/* Squared current command value[(15000/MAXTRQ)^2]	*/
		UINT32			UnIsqrSumMax;			/* Maximum cumulative load factor[(15000/MaxTrq)/10sec]*/

		INT32			TrqRefMon;				/* Torque command monitor		[2^24/MaxTrq]		*/

		INT32			UnPcmd64Sum;			/* Command pulse speed 			[64ms]				*/
	} var;
} SEQ_MOT_STS;



/****************************************************************************************************/
/*		PROTOTYPE																					*/
/****************************************************************************************************/
PUBLIC void SMotSts_InputMotorStatus( SEQ_MOT_STS *MotSts, BASE_CTRL_OUT *BaseCtrlOut,
                               SEQ_CTRL_OUT *SeqCtrlOut, BPRMDAT *Bprm );


#endif

