/****************************************************************************************************
 *
 * FILE NAME:  HardApi.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "hal_data.h"
#include "bsp_timer.h"
#include "bsp_pwm.h"


// extern PWM_DC_SYNC  gPwmDcSync;


/***********************************************************************************************************************
* Function Name: 初始化用于时钟定时器的中断，周期1ms
* Description  : CMT4(unit2)  PCLKL/8 = 6.25Mhz    0.16 us/cnt
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_cmt4_init(void)
{
    uint32_t dummy;
    
    /* PCLKL时钟是50MHZ，定时器分频系数是32分频，计数1ms的定时器计数个数是1562.5 */

    uint16_t Unit = 2;  
    uint16_t Channel = 0;

    /* 将定时器从低功耗模式唤醒 */
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_CMT, Unit);
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);

    /* UNIT2 - CH0 : 定时器计数器清零/固定增计数模式 */
    R_CMT->UNT[Unit].CM[Channel].CNT = 0x0000;
    
    /* UNIT2- CH0 : 定时器周期/比较值设定(16位定时器) */
    R_CMT->UNT[Unit].CM[Channel].COR = 6250;
    
    /* UNIT2 - CH0 : 时钟源分频设置  PCLKL/8 */
    R_CMT->UNT[Unit].CM[Channel].CR_b.CKS = 0;
    /* 禁止产生中断 */ 
    R_CMT->UNT[Unit].CM[Channel].CR_b.CMIE = 1;
    
    /* 通道2单元 0通道启动 */
    R_CMT->UNT[Unit].CMSTR0 |= (uint16_t)(0x01 << Channel);
    
     /* MTU0.TGRD产生中断 */
    R_BSP_IrqDisable(VECTOR_NUMBER_CMT4_CMI);
    R_BSP_IrqCfg(VECTOR_NUMBER_CMT4_CMI, _MTU_PRIORITY_LEVEL1, (NULL));
    R_BSP_IrqEnable(VECTOR_NUMBER_CMT4_CMI);           
}

/**********************************************************
 * 获取用于程序计时的32位定时器的计数值
 * 
 *********************************************************/
uint32_t bsp_gettimercnt(void)
{
   return R_CMTW1->CMWCNT; 
}

/***********************************************************************************************************************
* Function Name: 初始化用于程序计时的32位的定时器
* Description  : CMTW1  PCLKL/8 = 6.25Mhz    0.16 us/cnt
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_timerCounter_init(void)
{
    uint32_t dummy;

    dummy = 1UL;
    /* Cancel CMTW stop state in LPC */
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_CMTW, dummy);
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);


    /*设置时钟分频 0： PCLKL/8  1： PCLKL/32  2：PCLKL/128 3：PCLKL/512*/
    R_CMTW1->CMWCR_b.CKS = 0;   
    
    /*设置时钟为32位计数*/
    R_CMTW1->CMWCR_b.CMS = 0; 
    
    
    /*设置时钟计数清除条件 ：不清除*/
    R_CMTW1->CMWCR_b.CCLR = 1;     
    
    /*不开启中断*/
    R_CMTW1->CMWCR_b.CMWIE = 0;
    R_CMTW1->CMWCR_b.IC0IE = 0;
    R_CMTW1->CMWCR_b.IC1IE = 0;
    R_CMTW1->CMWCR_b.OC0IE = 0;
    R_CMTW1->CMWCR_b.OC1IE = 0;
    
    /*配置定时器输出IO*/
    
    /*复位计数器*/
    R_CMTW1->CMWCNT = (uint32_t) 0x00000000U;
    
    /*开启定时 */
    R_CMTW1->CMWSTR_b.STR = 1;
  
}

/***********************************************************************************************************************
* Function Name: 初始化ethercat 同步捕获的定时器
* Description  : MTU3_8
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_dc_mtu8_init(void)
{
    /* 停止定时器MTU8 */
    R_MTU->TSTRA_b.CST8 = 0;

    /* MTU0定时器计数时钟为 PCLKH/1, 计数边沿为时钟的上升沿, 定时器计数器TCNT清零被禁止 */
    R_MTU8->TCR_b.TPSC = 0x00;
    R_MTU8->TCR_b.CKEG = 0x00;
    R_MTU8->TCR_b.CCLR = 0x00;
    R_MTU8->TCR2 = 0x00;

    /* 定时器计数器设置为0 */
    R_MTU8->TCNT = 0x0000;

    /* 0x00: 自由运行模式(Normal mode) */
    R_MTU8->TMDR1_b.MD = 0x0;
    /* TGRC和TGRA同时使用，TGRC作为TGRA的映射寄存器 1-使能映射， 0-禁止映射模式 */
    R_MTU8->TMDR1_b.BFA = 0;
    /* TGRD和TGRB同时使用，TGRD作为TGRB的映射寄存器 1-使能映射， 0-禁止映射模式 */
    R_MTU8->TMDR1_b.BFB = 0;

    /* 配置引脚捕获引脚功能 0-禁止输出/捕获功能，8-上升沿捕获，9-下降沿捕获，10-上升/下降沿均捕获 */
    R_MTU8->TIORH_b.IOA = 8;
    R_MTU8->TIORH_b.IOB = 0;
    
    /* 使能 TGIA 中断*/
    R_MTU8->TIER_b.TGIEA = 1;

    /* 启动定时器MTU8 */
    R_MTU->TSTRA_b.CST8 = 1;
    
    
     /* MTU0.TGRD产生中断 */
    R_BSP_IrqDisable(VECTOR_NUMBER_TGIA8);
    R_BSP_IrqCfg(VECTOR_NUMBER_TGIA8, _MTU_PRIORITY_LEVEL1, (NULL));
    R_BSP_IrqEnable(VECTOR_NUMBER_TGIA8);       

}

/***********************************************************************************************************************
* Function Name: 获取MTU3_8 捕获计数值
* Description  : MTU3_8
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
uint32_t bsp_get_mtu8_cmpcnt(void)
{
   /* 停止定时器MTU8 */
  return  R_MTU8->TGRA;
}

/***********************************************************************************************************************
* Function Name: 获取MTU3_8 捕获计数值
* Description  : MTU3_8
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
uint32_t bsp_get_mtu8_cnt(void)
{
   /* 停止定时器MTU8 */
  return  R_MTU8->TCNT;
}

/***********************************************************************************************************************
* Function Name: 中断写捕获标志位
* Description  : MTU3_8
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void r_mtu8_tgia_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
   
  // gPwmDcSync.InputFlag = 1;
    
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
}

/***********************************************************************************************************************
* Function Name: 在1ms的定时器中中断里调用 usb的中断处理函数和数码管处理函数
* Description  : 
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void r_cmt4_isr(void)
{
  asm volatile("cpsie I");     // Enabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM    
   
//  R_BSP_IrqDisable(VECTOR_NUMBER_USB_FI);
//  usb_pstd_usb_handler();
    
  asm volatile("cpsid I");     // Disabling interrupt
  asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM  
}