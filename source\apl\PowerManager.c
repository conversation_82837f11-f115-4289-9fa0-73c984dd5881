/****************************************************************************************************
 *
 * FILE NAME:  PowerManager.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.08.23
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	23-08-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "PowerManager.h"
#include "BaseLoops.h"
#include "HardApi.h"
#include "Alarm.h"
#include "Mlib.h"
//#include "fmc.h"

PRIVATE	void pcmAcPowerManager( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts );
PRIVATE void pcmDcPowerManager( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts, BOOL MotStop );
PRIVATE	void pcmDischargeDcPower( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts,
																REGENE_CTRL *RegCtrl, BOOL BeSts );
PRIVATE	void pcmCheckAcLossPhase( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts );
PRIVATE	void pcmCheckDcUnderVoltage( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts, BOOL SvonReq );
PRIVATE void LpxAdDetectDcVolt( DET_DC_VOLT *pDetectDc );


/****************************************************************************************************
 * DESCRIPTION:
 *    Initialize Power Manager Variables
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcmInitPowerManager( POWER_MNG *hPowMngr, UINT32 AlmOLInfo )
{	
	hPowMngr->MainPowChecker.P.AcoffDetctTime = 35;             // AC Power Off Detect Time 				 //commit by lly  20240118
//	hPowMngr->MainPowChecker.P.AcoffDetctTime = 200;             // AC Power Off Detect Time 				 //add by lly  20240118  
//	hPowMngr->MainPowChecker.P.DcbusWay = FALSE;		        // AC input
//    hPowMngr->MainPowChecker.P.Ac3Phase = FALSE;		        // power input :single phase
	hPowMngr->MainPowChecker.P.PowerChargeWaitTime = 2500;		// DC charging completion waiting time
	hPowMngr->MainPowChecker.P.PowerReadyWaitTime = 2600; 		// Power ready waiting time	
	hPowMngr->MainPowChecker.P.DischCheckVlt = 10;				// Rapid discharge confirmation threshold voltage
	hPowMngr->MainPowChecker.P.DischCompVlt = 80;				// Rapid discharge duration threshold voltage	
	hPowMngr->MainPowChecker.P.MainDischActTime = 500;			// Main rapid discharge time (same as charging time)
	hPowMngr->MainPowChecker.P.OvdetAlmFilter = 1;				// Overvoltage alarm filter(1=250us)
	hPowMngr->MainPowChecker.P.DcDischarge = FALSE;				// Rapid discharge selection at DC power input

	hPowMngr->DetDcVolt.P.VdetMode = VDET_AD;					// Main circuit bus voltage detection method(PnE0C.8,9)				
	hPowMngr->DetDcVolt.P.VdetMaxVolt = 255;				    // Amp Volt detect at max input[data/MaxV]		
	hPowMngr->DetDcVolt.P.VdetAdNorm = 0;					    // Main circuit detection AD conversion result normalization constant
	hPowMngr->DetDcVolt.P.VdetAdFil = 0;					    // Main circuit detection AD filter gain						
	hPowMngr->DetDcVolt.P.VdcdetAlmFilter = 1;			        // Main circuit detection error alarm filter(1=250us)
	hPowMngr->DetDcVolt.P.VdetPrmOk = TRUE;					    // Main circuit detection related parameter setting(1:OK, 0:NG)			
	hPowMngr->DetDcVolt.P.VdetAdadj = 0;					    // P-N voltage detection level adjustment
	hPowMngr->DetDcVolt.P.Kdcvolt = 0;					        // Main circuit detection value conversion gain[Admax] => [V]


	hPowMngr->RegeneCtrl.P.RegStartFilter = 1;
	hPowMngr->RegeneCtrl.P.RegStopFilter = 1;
	hPowMngr->RegeneCtrl.P.RegOnLevel = REGEN_ON_LEVEL;
	hPowMngr->RegeneCtrl.P.RegOffLevel = REGEN_OFF_LEVEL;
	hPowMngr->RegeneCtrl.P.RegSyn = PNREGSYN;
	hPowMngr->RegeneCtrl.P.RegRxIn = FALSE;
//	hPowMngr->RegeneCtrl.P.ResistorCapa = 0;

//	hPowMngr->RegeneCtrl.V.RegOL.P.Krgpower = 400;            // (400V*400V/100ohm)*0.25ms
//	hPowMngr->RegeneCtrl.V.RegOL.P.RgolAlmLevel = 100*3000;   // 100W*3000MS

	CHECK_MAIN_POWER	*pMainPowerChecker;
	CHECK_REGOL 		*pRegOL;
	
	pMainPowerChecker = &(hPowMngr->MainPowChecker);
	pRegOL = &(hPowMngr->RegeneCtrl.V.RegOL);

	/* Reset Variable Data */
	MlibResetLongMemory( &(pMainPowerChecker->V), sizeof(pMainPowerChecker->V)/4 );

	/* Reset Variable Data */
	MlibResetLongMemory( &(pRegOL->V), sizeof(pRegOL->V)/4 );

	if( AlmOLInfo & ALMDEF_OLINFO_RGOL )
	{ 
		pRegOL->V.LastRgolDetect = TRUE;

		switch( pRegOL->P.RgSumSel )
		{
		case REGPOWSUM_RATE95PER:					/* Set regenerative overload alarm level to 95% */
			pRegOL->V.RgolPowSum = ( pRegOL->P.RgolAlmLevel / 100 ) * 95;
			break;
		case REGPOWSUM_RATE50PER:					/* Set regenerative overload alarm level to 50% */
			pRegOL->V.RgolPowSum = ( pRegOL->P.RgolAlmLevel >> 1 );
			break;
		default:									/* Set regenerative overload alarm level to 50% */
			pRegOL->V.RgolPowSum = ( pRegOL->P.RgolAlmLevel >> 1 );
			break;
		}
	}

	/* Reset Variable Data */
	MlibResetLongMemory( &(hPowMngr->RegeneCtrl.V.RegChk), sizeof(hPowMngr->RegeneCtrl.V.RegChk)/4 );

	return;
	
	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcmPowerManager( POWER_MNG *hPowMngr, BOOL MotStopSts, BOOL BeSts , BOOL SvonReq)		
{
    if(MCU_DEVICE_ID_0==MCU_DEVICE_ID)
    {
        pcmAcPowerManager(&hPowMngr->MainPowChecker, &hPowMngr->PowerSts);
        
        pcmDcPowerManager(&hPowMngr->MainPowChecker, &hPowMngr->PowerSts, MotStopSts);

        pcmDischargeDcPower( &hPowMngr->MainPowChecker,    &hPowMngr->PowerSts,
                                                                          &hPowMngr->RegeneCtrl, BeSts );
        
        pcmCheckAcLossPhase(&hPowMngr->MainPowChecker, &hPowMngr->PowerSts);
    }
    else if( hPowMngr->PowerSts.DcVolt > hPowMngr->MainPowChecker.P.UvLevel )
    {
        hPowMngr->PowerSts.PowerOn = TRUE;								// Power On for Control			
		hPowMngr->PowerSts.PowerOnEep = TRUE;							// Power On for EEPROM	
        hPowMngr->PowerSts.ChargeOk = TRUE;
    }
    else
    {
        hPowMngr->PowerSts.PowerOn = FALSE;								
		hPowMngr->PowerSts.PowerOnEep = FALSE;							
        hPowMngr->PowerSts.ChargeOk = FALSE;
    }
	
	pcmCheckDcUnderVoltage(&hPowMngr->MainPowChecker, &hPowMngr->PowerSts, SvonReq);

}


/****************************************************************************************************
 * DESCRIPTION: Count Regene Transistor ON Time
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PowerSysReset( POWER_MNG *hPowMngr)
{
#if 0  
    if((0 == hPowMngr->PowerSts.PowerOn))
    {
      hPowMngr->PowerSts.PowerResetTime++;
      if(hPowMngr->PowerSts.PowerResetTime > (hPowMngr->MainPowChecker.P.PowerReadyWaitTime + 100))
      {
        hPowMngr->PowerSts.PowerResetTime = 0;
        hPowMngr->PowerSts.PowerReset = 1;
      }
    }
    else if((1 == hPowMngr->PowerSts.PowerOn) && (1 == hPowMngr->PowerSts.PowerReset))
    {
      hPowMngr->PowerSts.PowerReset = 0;
      hApi_SystemReset();
    }
#endif  
    
    if(0 == hPowMngr->PowerSts.Acon)
    {
      hPowMngr->PowerSts.PowerResetTime++;
      if(hPowMngr->PowerSts.PowerResetTime > (500))
      {
        hPowMngr->PowerSts.PowerResetTime = 0;
        hPowMngr->PowerSts.PowerReset = 1;
      }
    }
    else if((1 == hPowMngr->PowerSts.Acon) && (1 == hPowMngr->PowerSts.PowerReset))
    {
      hPowMngr->PowerSts.PowerReset = 0;
      hApi_SystemReset();
    }
    
	return;
}

/****************************************************************************************************
 * DESCRIPTION: Count Regene Transistor ON Time
 *
 * RETURNS:
 *
****************************************************************************************************/
//BOOL gTestAcon = TRUE;
PUBLIC void PcmInputPowerStatus( POWER_MNG *hPowMngr, BOOL Acon, BOOL RegeneAlm )
{
    static UINT16 AconCnt = 20;
	hPowMngr->PowerSts.AconSig_l = hPowMngr->PowerSts.AconSig;
    
//    if(!Acon)
//    {
//        if(AconCnt++>=20)
//        {
//            AconCnt = 20;
//            hPowMngr->PowerSts.AconSig = FALSE;
//        }
//    }
//    else
//    {
//        AconCnt = 0;
//        hPowMngr->PowerSts.AconSig = Acon;
//    }
    
//    hPowMngr->PowerSts.AconSig_l = hPowMngr->PowerSts.AconSig;
	hPowMngr->PowerSts.AconSig = Acon;
   
	hPowMngr->RegeneCtrl.V.RegAlmSig = RegeneAlm;
	return;
}



/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void pcmAcPowerManager( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts )
{
	/* Create flag in dead zone of AC power ON detection  */ 
	if(PowChecker->V.AconWaitTimer < 4 )
	{
		PowSts->AconDetectReady=FALSE;
	}
	else
	{
		PowSts->AconDetectReady=TRUE;
	}

	if( PowChecker->V.AconWaitTimer < 4 )
	{
		PowChecker->V.AconWaitTimer++;
		PowSts->Acon = FALSE;
	}
	else if( ( (PowChecker->P.DcbusWay == FALSE) &&  (PowSts->AconSig != FALSE) )
		  || (PowChecker->P.DcbusWay == TRUE ) )
	{
		PowSts->Acon = TRUE;						// AC Power On					
		PowChecker->V.AcoffChkTimer = 0;			// Reset Power Off Check Timer	
	}
	else
	{
		/* Instantaneous power-off retention processing */
		if( PowChecker->V.AcoffChkTimer < PowChecker->P.AcoffDetctTime )

		{
			PowChecker->V.AcoffChkTimer ++;
			if( PowChecker->V.AcoffChkTimer > 30 )
			{
				PowSts->PowerOnEep = FALSE; 					// Power Off for EEPROM 			
			}
		}
		else
		{
			PowSts->Acon = FALSE;								// AC Power Off 					
			PowSts->PowerOnEep = FALSE; 						// Power Off for EEPROM 			
		}
	}

	return;

}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void pcmDcPowerManager( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts, BOOL MotStop )
{
    // AC power input mode
	if( PowChecker->P.DcbusWay == FALSE )
	{
		if( PowSts->Acon == TRUE )		
		{
			if( PowChecker->V.DconChkTimer >= PowChecker->P.PowerReadyWaitTime )
			{ /* Power-ready waiting time elapses */
				PowSts->PowerOn = TRUE;								// Power On for Control			
				PowSts->PowerOnEep = TRUE;							// Power On for EEPROM			
			}
			else if( PowChecker->V.DconChkTimer >= PowChecker->P.PowerChargeWaitTime ) 
			{
				if( PowSts->DcVolt >= PowChecker->P.UvLevel )	// DC voltage check				
				{
					PowSts->ChargeOk = TRUE;	
					PowChecker->V.DconChkTimer++;
				}
				else
				{
					ALMSetGlobalAlarm( ALM_PUV );				
					PowSts->PowerOn = FALSE;
					PowSts->PowerOnEep = FALSE;
					PowSts->ChargeOk = FALSE;
					PowChecker->V.DconChkTimer = 0;
				}
			}
			else
			{
				PowChecker->V.DconChkTimer++;
			}
		}
		/* AC power off */
		else if(PowChecker->P.AcoffDischarge ||PowSts->DcVolt < PowChecker->P.UvLevel )
		{
			PowSts->PowerOn  = FALSE;
			PowSts->PowerOnEep = FALSE;
			PowSts->ChargeOk = FALSE;
			PowChecker->V.DconChkTimer = 0;
		}
	}
	// DC power input mode
	else
	{
		if( PowSts->Acon == TRUE )
		{
			if( PowSts->DcVolt >= (INT32)PowChecker->P.UvLevel )	// DC voltage check		
			{
				if( PowChecker->V.DconChkTimer >= PowChecker->P.PowerReadyWaitTime ) 
				{ 
					PowSts->PowerOn = TRUE;								// Power On for Control		
					PowSts->PowerOnEep = TRUE;							// Power On for EEPROM			
				}
				else
				{
					if( PowChecker->V.DconChkTimer >= PowChecker->P.PowerChargeWaitTime ) 
					{
						PowSts->ChargeOk = TRUE;		// Perform inrush circuit operation in time
					}
					PowChecker->V.DconChkTimer++;
				}
			}
			else
			{
				if( PowChecker->V.DconChkTimer >= (UINT32)PowChecker->P.PowerChargeWaitTime )
				{ 
					ALMSetGlobalAlarm( ALM_PUV );				
					PowSts->PowerOn = FALSE;
					PowSts->PowerOnEep = FALSE;
					PowChecker->V.DconChkTimer = 0U;
				}
				PowChecker->V.DconChkTimer++;

			}
		}
		else
		{
			PowSts->PowerOn = FALSE;
			PowSts->PowerOnEep = FALSE;
            PowSts->ChargeOk = FALSE;
			PowChecker->V.DconChkTimer = 0U;
		}

//todo		
//		PowSts->ChargeOk = PowSts->ConvMconSig;

	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE BOOL pcmDischargeSequence( CHECK_MAIN_POWER *PowChecker, INT32 DcVolt )
{
	BOOL	DischReq;	// Rapid discharge request 
	DischReq = FALSE;

	switch	( PowChecker->V.DischSeqNo )
	{
	case DISCH_INIT:
		PowChecker->V.DischStartVolt = DcVolt;					
		PowChecker->V.DischSeqNo = DISCH_CHK1ST;			
		PowChecker->V.DischActTimer = 1;						
		DischReq = TRUE;										
	break;

	case DISCH_CHK1ST: 
		if( PowChecker->V.DischActTimer == DISCH_CHK_TIME )
		{ // Rapid discharge time DISCH_CHK_TIME (20ms) elapsed 
			/* Stop rapid discharge for 1 scan */
			DischReq = FALSE;								
			PowChecker->V.DischActTimer++;
            

		}
		else if( PowChecker->V.DischActTimer > DISCH_CHK_TIME )
		{
			if( (PowChecker->V.DischStartVolt - DcVolt) < PowChecker->P.DischCheckVlt )	  
			{ /* When the voltage decrease is (10V) or less */
				DischReq = FALSE;								
				PowChecker->V.DischSeqNo = DISCH_FINISH;		
			}
			else
			{ 
				DischReq = TRUE;									
				PowChecker->V.DischStartVolt = DcVolt;			
				PowChecker->V.DischActTimer = 1;				
				PowChecker->V.DischSeqNo = DISCH_CHK2ND;		
			}
		}
		else
		{
			DischReq = TRUE;										
			PowChecker->V.DischActTimer++;
		}
	break;

	case DISCH_CHK2ND: 
		if( PowChecker->V.DischActTimer == DISCH_CHK_TIME )
		{ // Rapid discharge time DISCH_CHK_TIME (20ms) elapsed 
			/* Stop rapid discharge for 1 scan */
			DischReq = FALSE;									
			PowChecker->V.DischActTimer++;
            

		}
		else if( PowChecker->V.DischActTimer > DISCH_CHK_TIME )
		{
			if( ((PowChecker->V.DischStartVolt - DcVolt) < PowChecker->P.DischCheckVlt)	  
				&& (DcVolt > PowChecker->P.DischCompVlt) )
			{ // Voltage decrease is (10V) or less && residual voltage is (50V) or more 
				DischReq = FALSE;								
				PowChecker->V.DischSeqNo = DISCH_FINISH;		
			}
			else
			{ 
				DischReq = TRUE;									
				PowChecker->V.DischStartVolt = DcVolt;	
				PowChecker->V.DischActTimer = 1;				
				PowChecker->V.DischSeqNo = DISCH_CHK3RD;		
			}
		}
		else
		{
			DischReq = TRUE;									
			PowChecker->V.DischActTimer++;
		}
		break;

	case DISCH_CHK3RD: 
		if( PowChecker->V.DischActTimer == DISCH_CHK_TIME )
		{ // Rapid discharge time DISCH_CHK_TIME (20ms) elapsed 
			/* Stop rapid discharge for 1 scan */
			DischReq = FALSE;								
			PowChecker->V.DischActTimer++;
            
		}
		else if( PowChecker->V.DischActTimer > DISCH_CHK_TIME )
		{
			if( ((PowChecker->V.DischStartVolt - DcVolt) < PowChecker->P.DischCheckVlt)	 
				&& (DcVolt > PowChecker->P.DischCompVlt) )
			{ // Voltage decrease is (10V) or less && residual voltage is (50V) or more 
				DischReq = FALSE;								
				PowChecker->V.DischSeqNo = DISCH_FINISH;		
			}
			else
			{ 
				DischReq = TRUE;								
				PowChecker->V.DischStartVolt = DcVolt;	
				PowChecker->V.DischActTimer = 1;				
				PowChecker->V.DischSeqNo = DISCH_ACTIVE;		
			}
		}
		else
		{
			DischReq = TRUE;										
			PowChecker->V.DischActTimer++;
		}
		break;

	case DISCH_ACTIVE:
		if( PowChecker->V.DischActTimer >= PowChecker->P.MainDischActTime )
		{ 
			DischReq = FALSE;								
			PowChecker->V.DischActTimer = 0;
			PowChecker->V.DischSeqNo = DISCH_FINISH;	
           
		}
		else
		{
			DischReq = TRUE;										
			PowChecker->V.DischActTimer++;
		}
		break;

	case DISCH_FINISH: 
		DischReq = FALSE;									

		break;

	default: 
		DischReq = FALSE;										
		break;

	}

	return	DischReq;
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE	void pcmDischargeDcPower( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts,
																REGENE_CTRL *RegCtrl, BOOL BeSts )
{

	if( PowChecker->P.DcbusWay == TRUE )
	{
#if(1 == REGENE_USE)      
		if( (PowChecker->P.DcbusWay == TRUE) && (PowChecker->P.DcDischarge == TRUE) )
		{
			if( PowSts->Discharge == TRUE )
			{
				if( PowChecker->V.ConvInSigDischErrChkTimer >= (UINT32)CONVINSIGDISCHERRCHK_TIME )
				{
					if( (PowChecker->V.DcVoltOld - PowSts->DcVolt) < DCVOLT10V )
					{
						ALMSetGlobalAlarm( ALM_CONVIO );			
					}
				}
				else
				{
					PowChecker->V.ConvInSigDischErrChkTimer++;
				}
			}
			else
			{
				PowChecker->V.ConvInSigDischErrChkTimer = 0;
				PowChecker->V.DcVoltOld = PowSts->DcVolt;
			}

			if( PowSts->Acon == TRUE )
			{
				PowSts->Discharge = FALSE;
				PowChecker->V.DischChkTimer = 500;
			}
			else if( PowChecker->V.DischChkTimer >= 500 )
			{
				PowSts->Discharge = TRUE;								
			}
			else
			{ /* do nothing */
				;
			}
		}
       
		else
#endif           
		{
			PowSts->Discharge = FALSE;
			PowChecker->V.DischChkTimer = 500;
		}


		if( (PowChecker->P.DcbusWay) && (PowSts->DcVolt > PowChecker->P.UvLevel) )
		{ 
			if( (!PowSts->PowerOn) && (PowSts->Acon) )
			{ /* PowerOn=TRUE ACON detection within 240 ms*/
				PowChecker->V.AconDetectCount++;
				if( PowChecker->V.AconDetectCount >= 10 + PowChecker->P.PowerReadyWaitTime )
				{ 
					ALMSetGlobalAlarm( ALM_WIR );
		
					PowChecker->V.AlmWIR_Flg = TRUE;
		
					PowChecker->V.AconDetectCount = 10 + PowChecker->P.PowerReadyWaitTime;
				}
			}
			else if( PowChecker->V.AlmWIR_Flg )
			{ 
				ALMSetGlobalAlarm( ALM_WIR );
			}
			else
			{ 
				PowChecker->V.AlmWIR_Flg = FALSE;
				PowChecker->V.AconDetectCount = 0;
			}
		}
		else
		{ 
			PowChecker->V.AlmWIR_Flg = FALSE;
			PowChecker->V.AconDetectCount = 0;
		}

	}
    /* AC power input mode: Performs rapid discharge processing */															
	else if( (PowSts->Acon == TRUE) || (BeSts == TRUE) )											
	{ 
		PowSts->Discharge = FALSE;
		PowChecker->V.DischChkTimer = 500;
		PowChecker->V.DischSeqNo = DISCH_INIT;
	}
	else if( PowChecker->V.DischChkTimer >= 500 )
	{ /* Normally, if ACON falls and BaseBlock, rapid discharge sequence starts */
//		PowSts->Discharge = TRUE;
		if(PowChecker->P.AcoffDischarge == TRUE)
		{
			PowSts->Discharge = pcmDischargeSequence( PowChecker, PowSts->DcVolt );	
		}
		else
		{
			PowSts->Discharge = FALSE;
		}
	}
	else if( PowChecker->V.DischChkTimer < 10 )
	{ /* Processing that passes only when the control power is turned on (Acon = LO). No rapid discharge for 10 ms  */
		PowChecker->V.DischChkTimer++;
		PowSts->Discharge = FALSE;						
	}
	else if( PowChecker->V.DischChkTimer <310 )	// Rapid discharge for 300ms	
	{
		PowChecker->V.DischChkTimer++;
		PowChecker->V.WireAlmDetWait = 0;

		if(PowChecker->P.AcoffDischarge == TRUE)
		{
			PowSts->Discharge = TRUE;
		}
		else
		{
			PowSts->Discharge = FALSE;
		}
	}
	else													
	{													
		PowSts->Discharge = FALSE;							
		if( (PowChecker->P.AcoffDischarge == TRUE) && (PowSts->DcVolt > DCVOLT60V)
			&& ((RegCtrl->P.ResistorCapa != 0) || (RegCtrl->P.RegRxIn)) )
		{ 
			if( PowChecker->V.WireAlmDetWait >10 )
			{
				ALMSetGlobalAlarm( ALM_WIR );
			}
			else
			{
				PowChecker->V.WireAlmDetWait++;
			}
		}
		else
		{
			PowChecker->V.WireAlmDetWait = 0;
		}

	}

	return;
}

/****************************************************************************************************
* DESCRIPTION:
*
* RETURNS:
*
****************************************************************************************************/
PRIVATE	void pcmCheckAcLossPhase( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts )
{
	if( (PowChecker->P.Ac3Phase == TRUE) && (PowSts->PowerOn == TRUE) && (PowChecker->P.DcbusWay == FALSE) )
	{

		if( PowChecker->V.OpenChkTimer[0] != 0 )
		{
			PowChecker->V.OpenChkTimer[0]++;
		}

		if( (PowSts->AconSig == TRUE) && (PowSts->AconSig_l == FALSE) )
		{
			PowChecker->V.OpenChkTimer[1] = PowChecker->V.OpenChkTimer[0];
			if( PowChecker->V.OpenChkTimer[0] == 0 )
			{
				PowChecker->V.OpenChkTimer[0] = 1;			
			}
		}
		else if( (PowChecker->V.OpenChkTimer[0] - PowChecker->V.OpenChkTimer[1]) > 12  )
		{
			PowChecker->V.OpenChkTimer[0] = 0;				
			PowChecker->V.OpenChkTimer[1] = 0;			
		}

		if( PowChecker->V.OpenChkTimer[0] > ACOPEN_DETECT_WAIT )
		{
			ALMSetGlobalAlarm( ALM_PWPHASE );			

			PowChecker->V.OpenChkTimer[0] = 0;
			PowChecker->V.OpenChkTimer[1] = 0;
		}
	}
	else
	{
		PowChecker->V.OpenChkTimer[0] = 0;
		PowChecker->V.OpenChkTimer[1] = 0;
	}
	return;

}

/****************************************************************************************************
* DESCRIPTION:
*
* RETURNS:
*
****************************************************************************************************/
PRIVATE	void pcmCheckDcUnderVoltage( CHECK_MAIN_POWER *PowChecker, POWER_STATUS *PowSts, BOOL SvonReq )
{
	if( PowSts->PowerOn == TRUE || SvonReq )
	{
		if ( PowSts->DcVolt < PowChecker->P.UvLevel )
		{
			if( PowChecker->V.UvChkTimer >= PowChecker->P.UvFilter )
			{
				if( PowSts->DcVolt == 0 )
				{
					ALMSetGlobalAlarm( ALM_VDC );			// Abnormal main circuit detector		
				}
				else
				{
					ALMSetGlobalAlarm( ALM_PUV );			// Undervoltage			
				}
			}
			else
			{
				PowChecker->V.UvChkTimer ++;
			}
		}
		else
		{
			PowChecker->V.UvChkTimer = 0;
		}
		
		if(SvonReq)
		{
			if ( PowSts->DcVolt < PowChecker->P.UvWrnLevel )
			{
				if( PowSts->DcVolt != 0 )
				{
					ALMSetGlobalAlarm( WRN_UV );				// Undervoltage warning detection		
				}
			}
			else
			{
				ALMClearGlobalAlarm( WRN_UV );					// Undervoltage warning detection
			}
		}
	}
	else
	{
		PowChecker->V.UvChkTimer = 0;
	}
	return;

}


/****************************************************************************************************
* DESCRIPTION:
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC void PcmRegeneControl( REGENE_CTRL *pRegCtrl, POWER_STATUS *PowSts, BOOL MotStop )
{
	
	/* Regenerative overload alarm occurrence check */
	if( ALMCheckGlobalAlarm( ALM_RGOL ) )				/* For unit common alarm, only axis 1 */
	{ /* When regenerative overload  occurs  */
		if( pRegCtrl->V.RegAlmMotStop || MotStop )
		{
			pRegCtrl->V.RegAlmMotStop = TRUE;
		}
		else
		{
			pRegCtrl->V.RegAlmMotStop = FALSE;
		}
	}
	else
	{
		pRegCtrl->V.RegAlmMotStop = FALSE;
	}

	/* Main circuit detector abnormal alarm occurrence check */
	if( ALMCheckGlobalAlarm( ALM_VDC ) )				/* For unit common alarm, only axis 1 */
	{ 
		hApi_RegeneCtrlOff( );					
		pRegCtrl->V.RegStopCnt = 0;						/* Clear regeneration operation end counter */
		pRegCtrl->V.RegStartCnt = 0;					/* Clear Regenerative operation start wait counter */
	}
	else
	{ 
		/* Regenerative voltage level check */
		if( PowSts->DcVolt > (INT32)pRegCtrl->P.RegOnLevel )	
		{ /* When DC voltage exceeds the regeneration OnLevel */
			pRegCtrl->V.RegStopCnt = 0;					

			if( pRegCtrl->P.RegSyn == PNREGSYN )
			{	/* regeneration synchronization is enabled */
				if( pRegCtrl->V.RegStartCnt > (pRegCtrl->P.RegStartFilter + (INT32)pRegCtrl->P.RegSynWaitTime))
				{ /* When regenerative operation start waiting time elapses */
					pRegCtrl->V.RegOn = TRUE; 				/* Perform regenerative processing */
				}
				else
				{ /* Waiting for regeneration operation to start */
					pRegCtrl->V.RegStartCnt++;				/* Regenerative operation start wait counter update */
				}
			}
			else
			{
				if( pRegCtrl->V.RegStartCnt > pRegCtrl->P.RegStartFilter )
				{ /* When regenerative operation start waiting time elapses */
					pRegCtrl->V.RegOn = TRUE; 			    /* Perform regenerative processing */
				}
				else
				{ /* Waiting for regeneration operation to start */
					pRegCtrl->V.RegStartCnt++;			    /* Regenerative operation start wait counter update */
				}
			}
		}
		else
		{
			pRegCtrl->V.RegStartCnt = 0;					
			if( PowSts->DcVolt < (INT32)pRegCtrl->P.RegOffLevel ) 
			{ /* When the DC voltage falls below the regenerative OffLevel */
				if( pRegCtrl->V.RegStopCnt > pRegCtrl->P.RegStopFilter )
				{ 
					pRegCtrl->V.RegOn = FALSE;			    /* Stop regenerative processing */
				}
				else
				{ 
					pRegCtrl->V.RegStopCnt++; 			    /* Regeneration operation end wait counter update */
				}
			}
			else
			{ 
				pRegCtrl->V.RegStopCnt = 0;					/* Clear Regeneration operation end wait counter*/
			}
		}


		if( (PowSts->Discharge == TRUE)
			|| ((pRegCtrl->V.RegOn == TRUE)
			&& ((ALMCheckGlobalAlarm(ALM_RG) == FALSE) && (pRegCtrl->V.RegAlmMotStop == FALSE))			
			&& (ALMCheckGlobalAlarm(WRN_EXREG) == FALSE) ) )		
		{

			if( (pRegCtrl->P.RegSyn == PNREGSYN)
			 && (ALMCheckGlobalAlarm(WRN_RGOLF) == TRUE) )
			{ 
				hApi_RegeneCtrlOff( );	
			}
			else
			{
				hApi_RegeneCtrlOn( );	
				if( pRegCtrl->V.RegOn == TRUE )
				{
					pRegCtrl->V.RegOnCount[0] += 2;		
				}
			}

		}
		else
		{ 
			hApi_RegeneCtrlOff( );	
		}
	}

	return;

}

/****************************************************************************************************
 * DESCRIPTION: Count Regene Transistor ON Time
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcmCountRegeneOnTime( POWER_MNG *hPowMngr )
{
	INT32	lwk0;

    /* Delta Regene.Tr On Count for taskC Alarm Check */
	/* Current Regene ON Count */
	lwk0 = hPowMngr->RegeneCtrl.V.RegOnCount[0];
	/* Delta Regene ON Count */
	hPowMngr->RegeneCtrl.V.dRegOnCount = lwk0 - hPowMngr->RegeneCtrl.V.RegOnCount[1];
	/* Update Last Regene ON Count */
	hPowMngr->RegeneCtrl.V.RegOnCount[1] = lwk0;

	return;
}

/****************************************************************************************************
* DESCRIPTION:
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC INT32 PcmCheckRegeneOverLoad( REGENE_CTRL *pRegCtrl,UINT32 *AlmOLInfo)
{
	INT32	RegPower;
	INT32	LastRgolPowSum;
	INT32	ret;

	ret = CHK_REGOL_NO_OPERATION;

	/* Calculation of regenerative power */
	if( (pRegCtrl->P.ResistorCapa == 0) && (pRegCtrl->P.RegRxIn == FALSE) )
	{
		RegPower = 0;
	}
	else
	{
		RegPower = pRegCtrl->V.dRegOnCount * pRegCtrl->V.RegOL.P.Krgpower;
	}

	/* Save previous regenerative power integration value */
	LastRgolPowSum = pRegCtrl->V.RegOL.V.RgolPowSum;
	pRegCtrl->V.RegOL.V.RgolPowSum += (RegPower - pRegCtrl->V.RegOL.P.RgolPowBase);
	if( pRegCtrl->V.RegOL.V.RgolPowSum < 0 )
	{
		pRegCtrl->V.RegOL.V.RgolPowSum = 0;
	}

	if( pRegCtrl->V.RegOL.V.RgolPowSum > pRegCtrl->V.RegOL.P.RgolAlmLevel )
	{

		ret |= CHK_REGOL_SET_ALARM;
		pRegCtrl->V.RegOL.V.LastRgolDetect = FALSE; 
	}

	if( pRegCtrl->V.RegOL.V.RgolPowSum > (pRegCtrl->V.RegOL.P.RgolAlmLevel>>1) )
	{

		if( !pRegCtrl->V.RegOL.V.LastRgolDetect || pRegCtrl->V.RegOL.V.RgPowUp )
		{ 
			ret |= CHK_REGOL_SET_WARNING;			/* Regenerative overload warning */
		}
		else if( LastRgolPowSum < pRegCtrl->V.RegOL.V.RgolPowSum )
		{ 
			ret |= CHK_REGOL_SET_WARNING;			/* Regenerative overload warning */
			pRegCtrl->V.RegOL.V.RgPowUp = TRUE; 	/* Regenerative power increase flag on */
		}
		else
		{ 
			ret |= CHK_REGOL_CLR_WARNING;			/* Regenerative overload warning */
		}
	}
	else
	{
		if( pRegCtrl->V.RegOL.V.LastRgolDetect )
		{ 
			pRegCtrl->V.RegOL.V.LastRgolDetect = FALSE; 
			*AlmOLInfo &= ~ALMDEF_OLINFO_RGOL;		/* Regenerative overload information reset */
		}
		ret |= CHK_REGOL_CLR_WARNING;				/* Regenerative overload warning */
	}

	pRegCtrl->V.RegOL.V.RgPowerSum += RegPower;

	pRegCtrl->V.RegOL.V.RgMeanCycle++;
	if( pRegCtrl->V.RegOL.V.RgMeanCycle >= 10000 )
	{
		pRegCtrl->V.RegOL.V.RgPowerSum	= 0;
		pRegCtrl->V.RegOL.V.RgMeanCycle = 0;
	}

	return ret;
}


/****************************************************************************************************
* DESCRIPTION:  Calculation of regenerative power calculation gain
*  Regenerative power is expressed in the unit of [5000/converter capacity] with the average value between taskC
*  Since the ON/OFF of the regenerative transistor is counted in the taskB cycle, the regenerative power is as follows.
*
*						 (DcVolt)^2  * TASKB_CYCLEUS * 5000  											
*  Regenerative power = ------------------------------------------ * dRegOnCount							
*						(RegenR/1000) * TASKC_CYCLEUS * ConvW  											
*																									
* RETURNS:
*
****************************************************************************************************/
PUBLIC void IprmcalRegenePowerGain( REGENE_CTRL *RegeneCtrl, BPRMDAT *Bprm, REGCFG_PRM *RegPrm )
{
	INT32		kx, sx;
	INT32		Vdcsqr;
	INT32		RegenR;		

	/* Voltage calculation */
	if( Bprm->AmpDcinType == 1 )							/* Amplifier DC power input */
	{
		Vdcsqr = 0;									
	}
	else													/* Amplifier AC power input */
	{
		Vdcsqr = RegPrm->RegenLvl * RegPrm->RegenLvl;
	}

	/* Selection of regenerative resistance value */
	if( RegPrm->RegenPow != 0 )
	{ 
	    /* external regenerative resistor capacity set */
		if( RegPrm->ExtRegenR != 0 )
		{ 
			/* Use external regenerative resistance */
			RegenR = RegPrm->ExtRegenR * 10U;					/* Unit conversion[10mOhm]->[mOhm]*/

		}
		else
		{
			/* Use default regenerative resistance */
			RegenR = RegPrm->RegenR;
		}
	}
	else
	{
		/* Use default regenerative resistance */
		RegenR = RegPrm->RegenR;
	}

	/* Calculation of regenerative power calculation gain */
    kx = Vdcsqr*1000/RegenR;
	kx = kx*TASKB_CYCLEUS/TASKC_CYCLEUS;
	kx = (kx *5000/RegPrm->ConvW);
		
	RegeneCtrl->V.RegOL.P.Krgpower = kx;
}


/****************************************************************************************************
* DESCRIPTION:
*           Calculation of regenerative overload level
* RETURNS:
*
****************************************************************************************************/
PUBLIC BOOL PcalRegeneOverLoadLevel( REGENE_CTRL *RegeneCtrl,UINT16 RegenPow, UINT16 ReW, UINT16 ConvW )
{
	BOOL			PrmSetErr;
	INT32			kx, sx;
	CHECK_REGOL		*pRegOL;

	PrmSetErr = FALSE;
	pRegOL = &(RegeneCtrl->V.RegOL);

	if( RegenPow == 0 )									// When the set value is zero
	{
		pRegOL->P.RgolPowBase = ReW;				
	}
	else if( (RegenPow * 10) <= ConvW )					// Within the converter capacity
	{
		pRegOL->P.RgolPowBase = (RegenPow * 50000) / ConvW;
	}
	else												
	{
		PrmSetErr = TRUE;
	}


/*--------------------------------------------------------------------------------------------------*/
/*		Calculation of regenerative overload alarm level											*/
/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*		    	RgolAlmLevel = RgolPowBase * 2 * 100 sec											*/
/*							 = RgolPowBase * 2 * 100 * 1000 / TASKC_CYCLEUS							*/
/*																									*/
/*				if( RgolPowSum >= RgolAlmLevel ){ Regenerative overload alarm;}						*/
/*--------------------------------------------------------------------------------------------------*/
	kx = pRegOL->P.RgolPowBase*2*100*1000/TASKC_CYCLEMS;
	
	pRegOL->P.RgolAlmLevel = kx;

	RegeneCtrl->P.ResistorCapa = RegenPow;

	return PrmSetErr;
}



/****************************************************************************************************
* DESCRIPTION: Regenerative abnormality check process (Call from taskC)
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC BOOL PcmCheckRegeneAlarm( REGENE_CTRL *pRegCtrl, BOOL PowerOn, BOOL RegCtrlSts )
{
	BOOL	ret;
	BOOL	RegenCtrlIn;
	
	ret = CHK_REGENE_NO_OPERATION;
	
	RegenCtrlIn = hApi_GetRegeneCtrlStatue();

	if( ((pRegCtrl->P.RegRxIn == TRUE) || (pRegCtrl->P.ResistorCapa != 0))
		&&	(PowerOn == TRUE) )
	{
#if 0
		if (pRegCtrl->V.RegAlmSig == TRUE )
		{
			if( pRegCtrl->V.RegChk.RegeneErrCnt <= 4 ) 	//1ms check times
			{
				pRegCtrl->V.RegChk.RegeneErrCnt++;
			}
			else
			{
				ret = CHK_REGENE_SET_ALARM;
			}
			pRegCtrl->V.RegChk.RegeAlmSignal = TRUE;
		}
		else
#endif           
		{
			if( ((RegenCtrlIn == TRUE ) && (TRUE == RegCtrlSts)) ||
				((RegenCtrlIn == FALSE) && (FALSE == RegCtrlSts)) )	
			{
				if( pRegCtrl->V.RegChk.RegeneErrCnt <= 8 )  ////2ms check times
				{
					pRegCtrl->V.RegChk.RegeneErrCnt++;
				}
				else
				{
					ret = CHK_REGENE_SET_ALARM;
				}
				pRegCtrl->V.RegChk.RegeAlmSignal = TRUE;				
			}
			else
			{
				pRegCtrl->V.RegChk.RegeneErrCnt = 0;
				pRegCtrl->V.RegChk.RegeAlmSignal = FALSE;				
			}
		}
	}
	else
	{
		pRegCtrl->V.RegChk.RegeneErrCnt = 0;
		pRegCtrl->V.RegChk.RegeAlmSignal = FALSE;
	}

	return ret;
}


/****************************************************************************************************
* DESCRIPTION:
*
* RETURNS:
*
****************************************************************************************************/
//INT32 testV = 180;
PUBLIC INT32 PcmDetectDcVolt( DET_DC_VOLT *pDetectDc, POWER_STATUS *PowSts )
{
	INT32		ret;
	INT32		DcVolt;
#if 1	

    PowSts->DcVolt = hApi_GetDcVoltage();   // todo
    PowSts->DcVoltFilo = hApi_GetVdcFilo();
//    PowSts->DcVolt = testV;
    
	return DETECT_DC_NO_OPERATION;

#else

	ret = DETECT_DC_NO_OPERATION;

	switch(pDetectDc->P.VdetMode)
	{
		case VDET_PWM_FIX_FOLLOW:
		case VDET_PWM_VAR_FOLLOW:

			break;

		case VDET_AD:
			LpxAdDetectDcVolt( pDetectDc );
			break;

		default:
			pDetectDc->DcVoltx = 0;
			break;
	}

/*--------------------------------------------------------------------------------------------------*/
/*		DC power supply voltage detection value correction processing								*/
/*--------------------------------------------------------------------------------------------------*/
	DcVolt = ((pDetectDc->DcVoltx * (0x100 + pDetectDc->P.VdetAdadj)) >> 8);
	if( DcVolt >= 0 )
	{
		PowSts->DcVolt = DcVolt;
	}
	else
	{
		PowSts->DcVolt = 0;
	}


/*--------------------------------------------------------------------------------------------------*/
/*		DC power supply voltage detection value error check											*/
/*--------------------------------------------------------------------------------------------------*/
	if( PowSts->Acon == TRUE )
	{ /* Check AC Power ON */
		if( pDetectDc->DcvWaitCnt >= 20 ) // 20MS
		{ /* Check DcVolt Wait Count */
			if( (pDetectDc->DcVoltx == 0) || (pDetectDc->DcVoltx == pDetectDc->P.VdetMaxVolt ) )
			{
				if( pDetectDc->DcvErrCnt > 2 )  // 2MS
				{ 
					ALMSetGlobalAlarm( ALM_VDC );			// Abnormal main circuit detector
					pDetectDc->DcvErrCnt = 0;				// Reset Error Counter			
					pDetectDc->DcvWaitCnt = 0;				// Reset DcVolt Wait Counter		
					ret = ALM_VDC;							// Abnormal main circuit detector
				}
				else
				{
					pDetectDc->DcvErrCnt++;					// Increment Error Count		
				}
			}
			else
			{
				pDetectDc->DcvErrCnt = 0;				    // Reset DcVolt Wait Counter		
			}
		}
		else
		{
			pDetectDc->DcvWaitCnt++;						// Increment DcVolt Wait Count	
		}
	}
	else													// AC Power OFF						
	{
		pDetectDc->DcvErrCnt = 0;			                // Reset Error Counter				
		pDetectDc->DcvWaitCnt = 0;			                // Reset DcVolt Wait Counter		
	}

	return ret;

#endif

}


/****************************************************************************************************
* DESCRIPTION:
*            Call from taskB
* RETURNS:
*
****************************************************************************************************/
PUBLIC INT32 PcmCheckOverVoltage( CHECK_MAIN_POWER *PowerChecker, DET_DC_VOLT *pDetectDc,
														POWER_STATUS *PowSts, REGENE_CTRL *RegCtrl )
{
	INT32		ret;
	UINT16		OvLevel;

	ret = DETECT_DC_NO_OPERATION;

	if( (RegCtrl->P.ResistorCapa != 0) || (RegCtrl->P.RegRxIn) )
	{ /* regenerative use */
		OvLevel = PowerChecker->P.OvLevel;
	}
	else
	{ /* Non-regenerative use */
		OvLevel = PowerChecker->P.NonRegOvLevel;
	}

    /* DC power supply overvoltage check */
	if( PowSts->DcVolt > (INT32)OvLevel )				
	{
		if( pDetectDc->DcVoltx == pDetectDc->P.VdetMaxVolt )
		{
			if( pDetectDc->VdcdetAlmCnt > pDetectDc->P.VdcdetAlmFilter )
			{
				if(MCU_DEVICE_ID == 0x00)
				{
						ALMSetGlobalAlarm( ALM_VDC );				// Abnormal main circuit detector
						ret = ALM_VDC;	
				}
				else
				{
					if(ALMCheckGlobalAlarm(ALM_AIX1_GL) == TRUE)
					{
							ALMSetGlobalAlarm( ALM_VDC );				// Abnormal main circuit detector
					  	ret = ALM_VDC;						
					}					
				}			
			}
			else
			{
				pDetectDc->VdcdetAlmCnt++;					// Increment Error Counter
			}
		}
		else
		{
			if( pDetectDc->OvdetAlmCnt > PowerChecker->P.OvdetAlmFilter )
			{		
				if(MCU_DEVICE_ID == 0x00)
				{
					ALMSetGlobalAlarm( ALM_OV );				// Over voltage
					ret = ALM_OV;						
				}
				else
				{
					if(ALMCheckGlobalAlarm(ALM_AIX1_GL) == TRUE)
					{
							ALMSetGlobalAlarm( ALM_VDC );				// Abnormal main circuit detector
					  	ret = ALM_VDC;						
					}								
				}
			}
			else
			{
				pDetectDc->OvdetAlmCnt++;					// Increment Error Counter			
			}
		}
	}
	else
	{
		pDetectDc->VdcdetAlmCnt = 0;						// Reset Error Conter			
		pDetectDc->OvdetAlmCnt = 0;							// Reset Error Conter				
	}

	return ret;
}


/****************************************************************************************************
* DESCRIPTION:
*		
* RETURNS:
*
****************************************************************************************************/
PRIVATE void LpxAdDetectDcVolt( DET_DC_VOLT *pDetectDc )
{
	INT32	AdRead = 0;
	INT32	DcVoltx;

//	AdRead = hApi_GetDcVoltFromAd( ); 		// todo		
	DcVoltx = AdRead * pDetectDc->P.VdetAdNorm;


	if( DcVoltx < 0 )
	{
		DcVoltx = 0;
	}
	else if( DcVoltx > pDetectDc->P.VdetMaxVolt )
	{
		DcVoltx = pDetectDc->P.VdetMaxVolt;
	}

	pDetectDc->DcAdFili = DcVoltx;
	pDetectDc->DcAdFilo[0] =
		MlibLpfilter1( DcVoltx, pDetectDc->P.VdetAdFil, pDetectDc->DcAdFilo[0] );
	pDetectDc->DcAdFilo[1] =
		MlibLpfilter1( pDetectDc->DcAdFilo[0], pDetectDc->P.VdetAdFil, pDetectDc->DcAdFilo[1] );
	pDetectDc->DcAdFilo[2] =
		MlibLpfilter1( pDetectDc->DcAdFilo[1], pDetectDc->P.VdetAdFil, pDetectDc->DcAdFilo[2] );
	pDetectDc->DcVoltx = pDetectDc->DcAdFilo[2];

	return;
}

/****************************************************************************************************
* DESCRIPTION:
*
* RETURNS:
*
****************************************************************************************************/
PUBLIC void PcmRlyControlProcess( POWER_MNG *hPowMngr, BOOL DbOn, BOOL BaseEnable, UINT16 AxisID )	
{
	POWER_STATUS		*pPowSts;

	pPowSts = &hPowMngr->PowerSts;

	/* relay control */
	if (pPowSts->ChargeOk == FALSE )
	{ 		
		hApi_RelayControlOff(AxisID);           /* Relay OFF */
	}
	else
	{ 		
		hApi_RelayControlOn(AxisID);            /* Relay ON */
	}

	/* DB control */
	if( DbOn == TRUE && BaseEnable == FALSE)
	{ 
		hApi_DynamicBrakeOn( AxisID );			/* DBON */
	}
	else
	{
		hApi_DynamicBrakeOff( AxisID );			/* DBOFF */
	}
      
	return;

}



