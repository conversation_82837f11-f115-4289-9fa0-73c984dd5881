/****************************************************************************************************
 *
 * FILE NAME:  ProgramJog.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.22
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	22-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/

#include "ProgramJog.h"
#include "HardApi.h"

/****************************************************************************************************
 * DESCRIPTION:
 *       Program JOG initial parameter calculation
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void IprmcalPrgJog( PJOGHNDL *PJogHdl, BPRMDAT *Bprm)
{
	INT32 PcmdOvrSpd, kx, sx;
	REAL32 wk;

	PCMKPV  *PJogPcmk;
	PJOGV	*PJogV;

	PJogV = &PJogHdl->PJogV;
	PJogPcmk = &PJogHdl->PJogPcmk;

	wk = Bprm->MaxSpd / Bprm->Kmotpls;
	wk = wk * PS_CYCLEUS / 1000000.0f;
	PcmdOvrSpd = wk;

	MlibIpcalPcmdMaker( PcmdOvrSpd, Bprm->NorMaxSpd, Bprm->NorOvrSpd, FALSE, &PJogPcmk->P );

	if( PcmdOvrSpd == 0x7FFFFF )
	{
		PJogV->Cnst.PrmUnMatch2 = TRUE;
	}

}


/****************************************************************************************************
 * DESCRIPTION:
 *       Program JOG operation command creation variable reset
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PrgJogReset( PJOGHNDL *PJogHdl )
{
	MlibResetLongMemory( &PJogHdl->PJogPcmk.V, sizeof( PJogHdl->PJogPcmk.V )>>2 );
}


/****************************************************************************************************
 * DESCRIPTION:
 *       Program JOG operation command sequence (Task C to CALL)
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void CpxPrgJogPosSequence( PJOGHNDL *PJogHdl, SEQ_CTRL_OUT *SeqCtrlOut,
													ALARM *AlmManager, INT32 FnCtrlMcmd ) 
{
	PJOGV	*PJogV;
	PCMKPV  *PJogPcmk;

	PJogV = &PJogHdl->PJogV;
	PJogPcmk = &PJogHdl->PJogPcmk;

	/* Program JOG mode check */
	if( FnCtrlMcmd != CTRL_MODE_PJOG )
	{
//        PJogV->Var.DeltaPos = 0;
//        PJogV->Var.IniPos =  PJogV->Var.PosValue;
		return;
	}
	
	/* Program JOG operating condition check */
	/* Alarm activated/HWBB status/Main circuit shutoff/OT enabled during operation */	
	if( ((AlmManager)->Status.AlmFlag != FALSE)			
		|| (SeqCtrlOut->HwbbSts == TRUE)
		|| (SeqCtrlOut->MainPowerOn != TRUE)
		|| ((PJogV->Var.State == PJOG_START) && (SeqCtrlOut->OverTrvlSts == TRUE)) )
	{
		PJogV->Var.SeqPtr = 7;
	}
	else if( PJogV->Var.RoutCmd == PJOGCMD_ABORT )	/* Operation interruption */
	{
		PJogV->Var.SeqPtr = 6;
	}
	else if( (PJogV->Var.State == PJOG_START) && (SeqCtrlOut->BaseEnableReq == FALSE) )	/* Servo off during operation */
	{
		PJogV->Var.SeqPtr = 6;
	}
	if( PJogV->Var.RoutCmd == PJOGCMD_INIT )
	{
		PJogV->Var.SeqPtr = 0;
		PJogV->Var.CoutCmd = PJOGCMD_INIT;
		PJogV->Var.State = PJOG_INIT;
	}

	/* Program JOG operation command sequence */
	switch ( PJogV->Var.SeqPtr )
	{
		case 0 :	/* Initial operation state	*/
			if( (PJogV->Var.RoutCmd == PJOGCMD_START) && (SeqCtrlOut->BaseEnableReq == TRUE) )
			{
				PJogV->Var.MCntr = 0;
				KlibRstLongTimer(&PJogV->Var.TimeWork);
				if( (PJogV->Cnst.MTimes == 0) && ((PJogV->Cnst.Pattern == 2) || (PJogV->Cnst.Pattern == 3)) )
				{
					PJogV->Var.SeqPtr = 7;
				}
				else
				{
					PJogV->Var.SeqPtr = 1;
				}
				PJogV->Var.State = PJOG_START;
			}
			break;

		case 1 :	/* Waiting for driving */
			if( KlibGetLongTimerMs(PJogV->Var.TimeWork) >= (UINT32)PJogV->Cnst.WaitTime )
			{
				PrgJogReset( PJogHdl );
				PJogV->Var.CoutCmd = PJOGCMD_START;
				if( PJogV->Cnst.Pattern & 0x01 )
				{
					PJogV->Var.AbsPosCmd = -PJogV->Cnst.Distance;
				}
				else
				{
					PJogV->Var.AbsPosCmd = PJogV->Cnst.Distance;
				}
				PJogV->Var.SeqPtr = 2;
			}
			break;

		case 2 :	/* Position command is being issued */
			if( PJogPcmk->V.calendf )
			{
				PJogV->Var.SeqPtr = 3;
			}
			break;

		case 3 :	/* Operation end judgment */
			if( (++PJogV->Var.MCntr >= PJogV->Cnst.MTimes) && (PJogV->Cnst.MTimes != 0) )
			{
				PJogV->Var.SeqPtr = 5;
			}
			else
			{
				KlibRstLongTimer(&PJogV->Var.TimeWork);
				PJogV->Var.SeqPtr = 4;
			}
			break;

		case 4 :	/* Operation pattern branch */
			if( KlibGetLongTimerMs(PJogV->Var.TimeWork) >= (UINT32)PJogV->Cnst.WaitTime )
			{
				switch ( PJogV->Cnst.Pattern )
				{
					case 0 :
						PJogV->Var.AbsPosCmd += PJogV->Cnst.Distance;
						break;
					case 1 :
						PJogV->Var.AbsPosCmd -= PJogV->Cnst.Distance;
						break;
					case 2 :
						if( PJogV->Var.MCntr < (PJogV->Cnst.MTimes>>1) )
						{
							PJogV->Var.AbsPosCmd += PJogV->Cnst.Distance;
						}
						else
						{
							PJogV->Var.AbsPosCmd -= PJogV->Cnst.Distance;
						}
						break;
					case 3 :
						if( PJogV->Var.MCntr < (PJogV->Cnst.MTimes>>1) )
						{
							PJogV->Var.AbsPosCmd -= PJogV->Cnst.Distance;
						}
						else
						{
							PJogV->Var.AbsPosCmd += PJogV->Cnst.Distance;
						}
						break;
					case 4 :
						if( PJogV->Var.MCntr & 0x01 )
						{
							PJogV->Var.AbsPosCmd -= PJogV->Cnst.Distance;
						}
						else
						{
							PJogV->Var.AbsPosCmd += PJogV->Cnst.Distance;
						}
						break;
					case 5 :
						if( PJogV->Var.MCntr & 0x01 )
						{
							PJogV->Var.AbsPosCmd += PJogV->Cnst.Distance;
						}
						else
						{
							PJogV->Var.AbsPosCmd -= PJogV->Cnst.Distance;
						}
						break;
					default :
						break;
				}
				if( PJogV->Cnst.Pattern < 6 )
				{
					PJogV->Var.SeqPtr = 2;
				}
				else
				{
					PJogV->Var.SeqPtr = 5;
				}
			}
			break;

		case 5 :	/* End of operation */
			PJogV->Var.State = PJOG_END;
			PJogV->Var.CoutCmd = PJOGCMD_INIT;
			PJogV->Var.SeqPtr = 0;
			break;

		case 6 :	/* Operation interruption */
			PrgJogReset( PJogHdl );	
			PJogV->Var.CoutCmd = PJOGCMD_ABORT;
			PJogV->Var.State = PJOG_ABORT;
			PJogV->Var.SeqPtr = 0;
			break;

		case 7 :	/* Alarm is occurring */
			PrgJogReset( PJogHdl );		
			PJogV->Var.CoutCmd = PJOGCMD_ABORT;
			PJogV->Var.State = PJOG_ERROR;
			PJogV->Var.SeqPtr = 0;
			break;

		default :
			break;
	}

	/* Operation register setting */
	PJogHdl->PJogState = (PJogV->Var.State & 0x0F);

	/* Flag that turns on with TaskC and turns off with Round */
	PJogV->Var.TaskCRunFlg = TRUE;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		 Position command input processing (Program JOG operation mode) (CALL with Task B)
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC BOOL PrgJogMakePositionReference( PJOGHNDL *PJogHdl, INT32 *dPosRefo )
{
	PJOGV	*PJogV;
	PCMKPV	*PJogPcmk;

	PJogV = &PJogHdl->PJogV;
	PJogPcmk = &PJogHdl->PJogPcmk;
    if(PJogV->Var.SeqPtr == 0 || PJogV->Var.SeqPtr == 1)
    {
        *dPosRefo = 0;
        return 0;
    }

	*dPosRefo = MlibPcmdMaker( PJogV->Var.AbsPosCmd, 0, PJogPcmk->P.maxspd, PJogPcmk, PCMKMODE_POS );
	return( (BOOL)(PJogPcmk->V.calendf ));
}


