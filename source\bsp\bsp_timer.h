/*
 * bsp_eeprom.h
 *
 *  Created on: Oct 25, 2023
 *      Author: xgj12
 */

#ifndef BSP_TIMER_H_
#define BSP_TIMER_H_
#include "stdint.h"

#define CMTW1_PPR_US   (float)(50.0/8)         // us ->  CMTW1 cnt
#define CMTW1_PPR_MS   (CMTW1_PPR_US*1000)     // ms  -> CMTW1 cnt 
#define CMTW1_PPR_S   (CMTW1_PPR_MS*1000)      // s  -> CMTW1 cnt 
#define CMTW1_US_PPR   (float)(1/CMTW1_PPR_US) // CMTW1 cnt  -> us


/* Interrupt Priority Level Select (PRL[3:0]) */
#define _CMTW_PRIORITY_LEVEL0                 (0x00000000UL) /* Level 0 (highest) */
#define _CMTW_PRIORITY_LEVEL1                 (0x00000001UL) /* Level 1 */
#define _CMTW_PRIORITY_LEVEL2                 (0x00000002UL) /* Level 2 */
#define _CMTW_PRIORITY_LEVEL3                 (0x00000003UL) /* Level 3 */
#define _CMTW_PRIORITY_LEVEL4                 (0x00000004UL) /* Level 4 */
#define _CMTW_PRIORITY_LEVEL5                 (0x00000005UL) /* Level 5 */
#define _CMTW_PRIORITY_LEVEL6                 (0x00000006UL) /* Level 6 */
#define _CMTW_PRIORITY_LEVEL7                 (0x00000007UL) /* Level 7 */
#define _CMTW_PRIORITY_LEVEL8                 (0x00000008UL) /* Level 8 */
#define _CMTW_PRIORITY_LEVEL9                 (0x00000009UL) /* Level 9 */
#define _CMTW_PRIORITY_LEVEL10                (0x0000000AUL) /* Level 10 */
#define _CMTW_PRIORITY_LEVEL11                (0x0000000BUL) /* Level 11 */
#define _CMTW_PRIORITY_LEVEL12                (0x0000000CUL) /* Level 12 */
#define _CMTW_PRIORITY_LEVEL13                (0x0000000DUL) /* Level 13 */
#define _CMTW_PRIORITY_LEVEL14                (0x0000000EUL) /* Level 14 */
#define _CMTW_PRIORITY_LEVEL15                (0x0000000FUL) /* Level 15 */


void bsp_cmt4_init(void);
void bsp_timerCounter_init(void);
uint32_t bsp_gettimercnt(void);

void bsp_dc_mtu8_init(void);
uint32_t bsp_get_mtu8_cmpcnt(void);
uint32_t bsp_get_mtu8_cnt(void);
#endif /* BSP_TIMER_H_ */
