/****************************************************************************************************
 *
 * FILE NAME:  FnJog.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.11
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	11-08-2020 Version 1.00 : Created by Denny
****************************************************************************************************/

#include "BaseDef.h"
#include "Global.h"
#include "FunManager.h"



/****************************************************************************************************
	DEFINES
****************************************************************************************************/

#define	ZSRCH_DRV			0x00
#define	ZSRCH_END			0x01
#define	ZSRCH_ERR			0x02
/****************************************************************************************************
 * DESCRIPTION:
 *		
 * RETURNS:
 *
****************************************************************************************************/
PRM_RSLT PnCmdJog(void *AxisHdl)
{
	AXIS_HANDLE *Axis;
	Axis = (AXIS_HANDLE*)AxisHdl;     
        
	FUN_CMN_CONTROL		*FnCmnCtrl;

	FnCmnCtrl = Axis->FnCmnCtrl;

        KlibRstLongTimer(&FnCmnCtrl->JogTimer);
    
	if(FnCmnCtrl->FnSvonReq == TRUE )
	{
		if(FnCmnCtrl->FnCtrlMcmd != CTRL_MODE_JOG)
		{
			return( PRM_RSLT_CONDITION_ERR );	
		}
		else if((Axis->Prm->PnAuxPrm.JogCmd & 0x10) == 0)
		{
			FnCmnCtrl->FnSvonReq = FALSE;
//			FnCmnCtrl->FnSvControl = FALSE;
		}
		return PRM_RSLT_SUCCESS;
	}

	if(Axis->Prm->PnAuxPrm.JogCmd & 0x10)
	{
		FnCmnCtrl->FnSvControl = TRUE;
		FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_JOG;
		FnCmnCtrl->FnSensOnReq = TRUE;
		FnCmnCtrl->FnSvonReq = TRUE;
	}

	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		
 * RETURNS:
 *
****************************************************************************************************/
void BpiJogExec(void *AxisHdl)
{
	AXIS_HANDLE *Axis;
	Axis = (AXIS_HANDLE*)AxisHdl;    
	FUN_CMN_CONTROL		*FnCmnCtrl;
	INT32			JogSpdP1;
	UINT16   		JogCmd=0;

	FnCmnCtrl = Axis->FnCmnCtrl;
	
	if(FnCmnCtrl->FnSvonReq == TRUE)
	{
		JogSpdP1 = Axis->Prm->PnCtrlCfgPrm.JogSpd;

		JogCmd = Axis->Prm->PnAuxPrm.JogCmd & 0x1F;
		
		switch(JogCmd)
		{
		case 0x10:	/* Stop	*/
			FnCmnCtrl->JogSpeed = 0;
			KlibRstLongTimer(&FnCmnCtrl->JogTimer);
			break;
		
		case 0x11:	/*  Forward rotation	*/
			FnCmnCtrl->JogSpeed = Axis->BaseLoops->Bprm->Kspdrpm * JogSpdP1;
			break;

		case 0x12:	/* Reverse rotation	*/
			FnCmnCtrl->JogSpeed = -Axis->BaseLoops->Bprm->Kspdrpm * JogSpdP1;
			break;
			
		default:   // servo off
			KlibRstLongTimer(&FnCmnCtrl->JogTimer);
			FnCmnCtrl->FnSvonReq = FALSE;
			FnCmnCtrl->FnSvControl = FALSE;
			break;
		}

		if(Axis->Prm->PnCtrlCfgPrm.JogTime > 0 && (KlibGetLongTimerMs(FnCmnCtrl->JogTimer)
			> (UINT32)(Axis->Prm->PnCtrlCfgPrm.JogTime *100))  )
		{
			 FnCmnCtrl->JogSpeed = 0;
		}

	}
	else if(Axis->BaseCtrls->BaseEnable == FALSE)
	{
		FnCmnCtrl->FnSvControl = FALSE;
		FnCmnCtrl->JogSpeed = 0;
		FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_NOCMD;
		Axis->Prm->PnAuxPrm.JogCmd = 0;
	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *		 Entry function of JOG operation
 * RETURNS:
 *
****************************************************************************************************/
PRM_RSLT FnEnterJOGoperation(FUNEXE *FunExe, AXIS_HANDLE *Axis)
{
	FUN_CMN_CONTROL		*FnCmnCtrl;

	FnCmnCtrl = Axis->FnCmnCtrl;

	/* If the servo is on, end without entering*/
	if(FnCmnCtrl->State.FnSvCtrlDisable != FALSE)
	{
		return PRM_RSLT_CONDITION_ERR;
	}

	FnCmnCtrl->FnSvControl = TRUE;
	FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_JOG;
	FnCmnCtrl->FnSensOnReq = TRUE;
	FnCmnCtrl->JogSpeed = 0;

	FunExe->HoldFnMode = TRUE;/* No automatic end in Fn mode */

	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Execute function of JOG operation
 * RETURNS:
 *
****************************************************************************************************/
PRM_RSLT FnExecuteJOGoperation(FUNEXE *FunExe, AXIS_HANDLE *Axis)
{
	PRM_RSLT		errRes;
	FUN_CMN_CONTROL	*FnCmnCtrl;
	SEQ_CTRL_OUT	*SeqCtrlOut;
	ALARM			*AlmManager;
	PRMDATA			*Prm;
	BPRMDAT			*Bprm;
	INT32			JogSpdP1;

	FnCmnCtrl = Axis->FnCmnCtrl;
	SeqCtrlOut = Axis->SeqCtrlOut;

	AlmManager = Axis->AlmMngr;
	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

	errRes = PRM_RSLT_SUCCESS;

	if(FunExe->State == 0)
	{/* Servo OFF*/
		switch(FunExe->CmdCode)
		{
		case FCMD_SV:	/* Servo ON	*/
			if( (SeqCtrlOut->MainPowerOn == FALSE)
				|| (SeqCtrlOut->HwbbSts != FALSE)
				|| (AlmManager->AlmReady == FALSE) )
			{
				errRes = PRM_RSLT_CONDITION_ERR;
			}
			else
			{
				FnCmnCtrl->FnSvonReq = TRUE;
			}
			break;

		case FCMD_UP:	/* Forward rotation	*/
		case FCMD_DOWN:	/* Reverse rotation	*/
		default:
			break;
		}

		FnCmnCtrl->JogSpeed = 0;
		if(SeqCtrlOut->BaseEnableReq != FALSE)
		{
			FunExe->State = 1;
		}
	}
	else
	{/* Servo ON	*/
		JogSpdP1 = Prm->PnCtrlCfgPrm.JogSpd;

		switch(FunExe->CmdCode)
		{
		case FCMD_STOP:	/* Stop	*/
			FnCmnCtrl->JogSpeed = 0;
			break;
		case FCMD_UP:	/*  Forward rotation	*/
			FnCmnCtrl->JogSpeed = Bprm->Kspdrpm * JogSpdP1;
			KlibRstLongTimer(&FunExe->LongTimer);
			break;

		case FCMD_DOWN:	/* Reverse rotation	*/
			FnCmnCtrl->JogSpeed = -Bprm->Kspdrpm * JogSpdP1;
			KlibRstLongTimer(&FunExe->LongTimer);
			break;

		case FCMD_SV:	/* Servo off */
			FnCmnCtrl->FnSvonReq = FALSE;
			break;
		default:
			if(KlibGetLongTimerMs(FunExe->LongTimer)
				> (UINT32)(FnCmnCtrl->SafeStopTime*1000))
			{
				FnCmnCtrl->JogSpeed = 0;
			}
			break;
		}

		if(SeqCtrlOut->BaseEnableReq == FALSE)
		{
			FunExe->State = 0;
		}
	}

	return errRes;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Leave function of JOG operation
 * RETURNS:
 *
****************************************************************************************************/
void FnLeaveJOGoperation(FUNEXE *FunExe, AXIS_HANDLE *Axis)
{
	FUN_CMN_CONTROL	*FnCmnCtrl;

	FnCmnCtrl = Axis->FnCmnCtrl;


	FnCmnCtrl->JogSpeed = 0;
	FnCmnCtrl->FnSvonReq = FALSE;
	FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_NOCMD;
	FnCmnCtrl->FnSensOnReq = FALSE;
	FnCmnCtrl->FnSvControl = FALSE;
}



