/*******************************************************************************
								Copyright 2022 IAR Systems. All rights reserved.
Function:RZ/N2L(Cortex-R52) SPI Flashloader.

Note:MX25UR51245G(4-byte address SPI mode)

History:
 No  Rev  Date       Name			Note
---+-----+----------+-------------+--------------------------------------------
000 01.00 2022/07/29 S.Tonoshita	New Development
001 01.10 2022/11/28 S.Tonoshita	Add initialization of Compare Match Constant Register.
*******************************************************************************/

/******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <intrinsics.h>
#include <stdint.h>
#include <Renesas\R9A07G084.h>
#include "flash_loader.h"
#include "protect.h"
#include "MX25UR51245G.h"

/******************************************************************************/
#define FLASH_CMD_RDID				(0x9F00U)			// Read Identification (RDID)
#define FLASH_CMD_RSTEN				(0x6600U)			// Reset-Enable (RSTEN)
#define FLASH_CMD_RST				(0x9900U)			// Reset (RST)
#define FLASH_CMD_WREN				(0x0600U)			// Write enable
#define FLASH_CMD_READ4B			(0x0300U)			// Normal read,4byte
#define FLASH_CMD_PP4B				(0x0200U)			// Page Program,4byte
#define FLASH_CMD_RDSR				(0x0500U)			// Read status register
#define FLASH_CMD_SE4B				(0x2000U)			// Block erase 4KB,4byte

#define LIOCFGCS_PRTMD_1S1S1S		(0x000)				// Protocol mode 1S-1S-1S

#define FLASH_PAGE_SIZE				(256)				// Write page size(Byte)
#define CMTW_CLOCK_FREQUENCY		(6.250 * 1000)		// 6.250MHz operating clock
#define FLASH_RESET_WAIT			(25)				// 25ms
#define PAGE_PROGRAM_WAIT			(10)					// Max:0.75ms
#define WRITE_ENABLE				(2)					// 2ms
#define SECTOR_ERASE_WAIT			(800)				// Max:400ms
#define MAX_SPI_TRANS_SIZE			(8)					// Maximum SPI transfer size(8Byte)


//
// SPI Flash Status Register Bit Definition
//
#define	STATUS_WEL					(1 << 1)			// Status Register WEL bit definition
#define	STATUS_WIP					(1 << 0)			// Status Register WIP bit definition

/******************************************************************************/
uint32_t flash_init(uint32_t *id);
uint32_t flash_read(uint32_t addr,uint32_t size,uint8_t *buffer);
uint32_t flash_write(uint32_t addr,uint32_t count,const uint8_t *buffer);
uint32_t flash_sector_erase(uint32_t addr);

/******************************************************************************/
static uint32_t flash_block_read(uint32_t addr,uint32_t block,uint8_t *buffer);
static uint32_t flash_byte_read(uint32_t addr,uint32_t size,uint8_t *buffer);
static uint32_t flash_block_write(uint32_t addr,uint32_t block,const uint8_t *buffer);
static uint32_t flash_word_write(uint32_t addr,uint32_t count,const uint8_t *buffer);
static uint32_t flash_byte_write(uint32_t addr,uint32_t count,const uint8_t *buffer);
static uint32_t flash_reset(void);
static uint32_t flash_write_end(uint32_t max_time);
static uint32_t flash_get_id(uint32_t *id);
static void flash_wren(void);
static uint8_t flash_get_status(void);


#define UpgradeMark_ADDRESS_START    (uint32_t)0x68300000

/******************************************************************************/
uint32_t flash_init(uint32_t *id)
{
    uint8_t UpgradeFlag = 0;
    uint32_t reg;
    volatile uint32_t dummy;

	RWP_S->PRCRS  = (PRCRS_PRKEY | PRCRS_PRC0 | PRCRS_PRC1 | PRCRS_PRC2 | PRCRS_PRC3);	// Disable Safety Area Protect.
	RWP_NS->PRCRN = (PRCRN_PRKEY | PRCRN_PRC0 | PRCRN_PRC1 | PRCRN_PRC2 | PRCRN_PRC3);	// Disable Safety Area Protect.

	SYSC_NS->MSTPCRA_b.MSTPCRA05 = 0;													// xSPI Unit 0 Module Stop(Release from the module-stop state)
	dummy = SYSC_NS->MSTPCRA;															// Dummy-read the MSTPCRm register.
	dummy = XSPI1->INTC;																// Dummy-read any register of xSPI unit 0 at least 5 times.
	dummy = XSPI1->INTC;
	dummy = XSPI1->INTC;
	dummy = XSPI1->INTC;
	dummy = XSPI1->INTC;

	SYSC_NS->MSTPCRD_b.MSTPCRD05 = 0;													// CMTW Unit 0 Module Stop(Release from the module-stop state)
	dummy = SYSC_NS->MSTPCRD;															// Dummy-read the MSTPCRm register.
	dummy = CMTW0->CMWCOR;																// Dummy-read any register of CMTW unit 0 at least 5 times.
	dummy = CMTW0->CMWCOR;
	dummy = CMTW0->CMWCOR;
	dummy = CMTW0->CMWCOR;
	dummy = CMTW0->CMWCOR;

	SYSC_NS->SCKCR_b.DIVSELXSPI1 = 0x0;													// Base clock of xSPI Unit 0 is selected at 800MHz clock
	SYSC_NS->SCKCR_b.FSELXSPI1   = 0x6;													// xSPI Unit 0 clock set at 12.5MHz

	PTADR->RSELP17_b.RS7       = 1;														// PORT17_7(XSPI1_CKP) Set non safety region
	PORT_NSR->PFC17_b.PFC7     = 7;														// PORT17_7(XSPI1_CKP)
	PORT_NSR->PMC17_b.PMC7     = 1;														// PORT17_7(XSPI1_CKP)
    PORT_NSR->DRCTL17_H_b.DRV7 = 2;														// PORT17_7(XSPI1_CKP) Driving Ability Control High
    PORT_NSR->DRCTL17_H_b.SR7  = 1;														// PORT17_7(XSPI1_CKP) Slew Rate Control Fast

	PTADR->RSELP18_b.RS2       = 1;														// PORT18_2(XSPI1_CS0#) Set non safety region
	PORT_NSR->PFC18_b.PFC2     = 5;														// PORT18_2(XSPI1_CS0#)
	PORT_NSR->PMC18_b.PMC2     = 1;														// PORT18_2(XSPI1_CS0#)
    PORT_NSR->DRCTL18_L_b.DRV2 = 2;														// PORT18_2(XSPI1_CS0#) Driving Ability Control High
    PORT_NSR->DRCTL18_L_b.SR2  = 1;														// PORT18_2(XSPI1_CS0#) Slew Rate Control Fast

//	PTADR->RSELP16_b.RS1       = 1;														// PORT16_1(XSPI0_RESET0#) Set non safety region
//	PORT_NSR->PFC16_b.PFC1     = 0;														// PORT16_1(XSPI0_RESET0#)
//	PORT_NSR->PMC16_b.PMC1     = 1;														// PORT16_1(XSPI0_RESET0#)
//    PORT_NSR->DRCTL16_L_b.DRV1 = 2;														// PORT16_1(XSPI0_RESET0#) Driving Ability Control High
//    PORT_NSR->DRCTL16_L_b.SR1  = 1;														// PORT16_1(XSPI0_RESET0#) Slew Rate Control Fast

	PTADR->RSELP16_b.RS7       = 1;														// PORT16_7(XSPI1_IO0) Set non safety region
	PORT_NSR->PFC16_b.PFC7     = 2;														// PORT16_7(XSPI1_IO0)
	PORT_NSR->PMC16_b.PMC7     = 1;														// PORT16_7(XSPI1_IO0)
    PORT_NSR->DRCTL16_H_b.DRV7 = 2;														// PORT16_7(XSPI1_IO0) Driving Ability Control High
    PORT_NSR->DRCTL16_H_b.SR7  = 1;														// PORT16_7(XSPI1_IO0) Slew Rate Control Fast

	PTADR->RSELP17_b.RS0       = 1;														// PORT17_0(XSPI1_IO1) Set non safety region
	PORT_NSR->PFC17_b.PFC0     = 2;														// PORT17_0(XSPI1_IO1)
	PORT_NSR->PMC17_b.PMC0     = 1;														// PORT17_0(XSPI1_IO1)
    PORT_NSR->DRCTL17_L_b.DRV0 = 2;														// PORT17_0(XSPI1_IO1) Driving Ability Control High
    PORT_NSR->DRCTL17_L_b.SR0  = 1;														// PORT17_0(XSPI1_IO1) Slew Rate Control Fast
    

	PTADR->RSELP17_b.RS3       = 1;														// PORT16_7(XSPI1_IO2) Set non safety region
	PORT_NSR->PFC17_b.PFC3     = 7;														// PORT16_7(XSPI1_IO2)
	PORT_NSR->PMC17_b.PMC3     = 1;														// PORT16_7(XSPI1_IO2)
    PORT_NSR->DRCTL17_L_b.DRV3 = 2;														// PORT16_7(XSPI1_IO2) Driving Ability Control High
    PORT_NSR->DRCTL17_L_b.SR3  = 1;														// PORT16_7(XSPI1_IO2) Slew Rate Control Fast
                                                                                                            
	PTADR->RSELP17_b.RS4       = 1;														// PORT17_0(XSPI1_IO3) Set non safety region
	PORT_NSR->PFC17_b.PFC4     = 7;														// PORT17_0(XSPI1_IO3)
	PORT_NSR->PMC17_b.PMC4     = 1;														// PORT17_0(XSPI1_IO3)
    PORT_NSR->DRCTL17_H_b.DRV4 = 2;														// PORT17_0(XSPI1_IO3) Driving Ability Control High
    PORT_NSR->DRCTL17_H_b.SR4  = 1;														// PORT17_0(XSPI1_IO3) Slew Rate Control Fast    

	RWP_S->PRCRS  = PRCRS_PRKEY;														// Enable Safety Area Protect.
	RWP_NS->PRCRN = PRCRN_PRKEY;														// Enable Safety Area Protect.

    if( XSPI1->CDCTL0_b.TRREQ==1 ){														// Transaction request?
		return( RESULT_ERROR );
    }
	XSPI1->INTC_b.CMDCMPC      = 0x1;													// Command Completed interrupt clear
	XSPI1->BMCTL0_b.CS0ACC     = 0x0;													// AHB channel to slave0 memory area access enable(Read/Write disable)
	__DSB();
	for(;;){																			// Check no pending memory-mapping access
		reg = XSPI1->COMSTT;															// Read the xSPI Common Status Register(COMSTT)
		if( (reg & (XSPI0_COMSTT_MEMACC_Msk | XSPI0_COMSTT_WRBUFNE_Msk))==0 ){
			break;
		}
	}
	if( flash_reset()!=RESULT_OK ){														// Resets the flash memory
		return( RESULT_ERROR );
	}
	if( flash_get_id(id)!=RESULT_OK ){													// Read Identification
		return( RESULT_ERROR );
	}
    
    
    flash_byte_read(UpgradeMark_ADDRESS_START,1,&UpgradeFlag);
    
    if(UpgradeFlag != 0xFF)
    {
      flash_sector_erase(UpgradeMark_ADDRESS_START);
    }
    

	return( RESULT_OK );
}

/******************************************************************************/
uint32_t flash_read(uint32_t addr,uint32_t size,uint8_t *buffer)
{
	int32_t ret;
	uint32_t block;

	if( size==0 ){																// Size 0?
		return( RESULT_OK );
	}
	if( XSPI1->CDCTL0_b.TRREQ==1 ){												// Transfer in progress?
		return( RESULT_ERROR );
	}
	XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;							// Protocol mode 1S-1S-1S Select
	block = size/MAX_SPI_TRANS_SIZE;											// Calculate the read transfer size size
	if( 0<block ){
		if( flash_block_read(addr,block,buffer)!=RESULT_OK ){					// Write in blocks
			return( RESULT_ERROR );
		}
		buffer += MAX_SPI_TRANS_SIZE * block;
		addr   += MAX_SPI_TRANS_SIZE * block;
	}
	size  -= (block * MAX_SPI_TRANS_SIZE);										// Calculate the number of remaining bytes
	if( size==0 ){	
		return( RESULT_OK );
	}
	ret = flash_byte_read(addr,size,buffer);									// Read in byte
	return( ret );
}

/******************************************************************************/
uint32_t flash_write(uint32_t addr,uint32_t count,const uint8_t *buffer)
{
	uint32_t block;
	uint32_t wblock;

	if( XSPI1->CDCTL0_b.TRREQ==1 ){												// Transfer in progress?
		return( RESULT_ERROR );
	}
	block = count/FLASH_PAGE_SIZE;												// Calculate the write block size
	if( 0<block ){
		if( flash_block_write(addr,block,buffer)!=RESULT_OK ){					// Write in blocks
			return( RESULT_ERROR );
		}
		buffer += FLASH_PAGE_SIZE * block;
		addr   += FLASH_PAGE_SIZE * block;
	}
	count  -= (block * FLASH_PAGE_SIZE);										// Calculate the remaining size
	wblock  = count / sizeof(uint32_t);											// Calculate the remaining write word size
	if( 0<wblock ){
		if( flash_word_write(addr,wblock,buffer)!=RESULT_OK ){					// Write in words
			return( RESULT_ERROR );
		}
		buffer += sizeof(uint32_t) * wblock;
		addr   += sizeof(uint32_t) * wblock;
	}
	count  -= (wblock * sizeof(uint32_t));										// Calculate the number of remaining bytes
	if( count==0 ){	
		return( RESULT_OK );
	}
	return( flash_byte_write(addr,count,buffer) );								// Write in byte
}

/******************************************************************************/
uint32_t flash_sector_erase(uint32_t addr)
{
	if( XSPI1->CDCTL0_b.TRREQ==1 ){												// Transfer in progress?
		return( RESULT_ERROR );
	}
	flash_wren();																// Write permission command execution
	XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;							// Protocol mode 1S-1S-1S Select
	XSPI1->CDCTL0_b.TRREQ    = 0x0;												// No transaction
	XSPI1->CDTBUF0 = ( FLASH_CMD_SE4B << XSPI0_CDTBUF0_CMD_Pos ) |
					 ( 1U << XSPI0_CDTBUF0_TRTYPE_Pos )          |
					 ( 3U << XSPI0_CDTBUF0_ADDSIZE_Pos )         |
					 ( 1U << XSPI0_CDTBUF0_CMDSIZE_Pos );
	XSPI1->CDABUF0  = addr;
	__DSB();
	XSPI1->CDCTL0_b.TRREQ  = 0x1;												// Transaction request
	__DSB();
	while( XSPI1->INTS_b.CMDCMP!=1 ){											// Command Completed?
		__no_operation();
	}
	XSPI1->INTC_b.CMDCMPC = 0x1;												// Command Completed interrupt clear
	if( flash_write_end(CMTW_CLOCK_FREQUENCY*SECTOR_ERASE_WAIT)!=RESULT_OK ){	// waiting for write end
		return( RESULT_ERROR );
	}
	return( RESULT_OK );
}

/******************************************************************************/
static uint32_t flash_block_read(uint32_t addr,uint32_t block,uint8_t *buffer)
{
	uint32_t i;
	uint32_t *src = (uint32_t *)(buffer);

	for(i=0;i<block;i++){
		XSPI1->CDCTL0_b.TRREQ    = 0x0;											// No transaction
		XSPI1->CDTBUF0 = ( FLASH_CMD_READ4B << XSPI0_CDTBUF0_CMD_Pos) |
						 ( 0U << XSPI0_CDTBUF0_TRTYPE_Pos )           |
						 ( 8U << XSPI0_CDTBUF0_DATASIZE_Pos )         |
						 ( 3U << XSPI0_CDTBUF0_ADDSIZE_Pos )          |
						 ( 1U << XSPI0_CDTBUF0_CMDSIZE_Pos );
		XSPI1->CDABUF0  = addr;
		__DSB();
		XSPI1->CDCTL0_b.TRREQ  = 0x1;											// Transaction request
		__DSB();
		while( XSPI1->INTS_b.CMDCMP!=1 ){										// Command Completed?
			__no_operation();
		}
		XSPI1->INTC_b.CMDCMPC = 0x1;											// Command Completed interrupt clear
		*src = XSPI1->CDD0BUF0;
		src++;
		*src = XSPI1->CDD1BUF0;
		src++;
		addr+=MAX_SPI_TRANS_SIZE;
	}
	return( RESULT_OK );
}

/******************************************************************************/
static uint32_t flash_byte_read(uint32_t addr,uint32_t size,uint8_t *buffer)
{
	uint32_t i;

	if( size==0 ){																// Size 0?
		return( RESULT_OK );
	}
	if( XSPI1->CDCTL0_b.TRREQ==1 ){												// Transfer in progress?
		return( RESULT_ERROR );
	}
	XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;							// Protocol mode 1S-1S-1S Select
	for(i=0;i<size;i++){
		XSPI1->CDCTL0_b.TRREQ    = 0x0;											// No transaction
		XSPI1->CDTBUF0 = (FLASH_CMD_READ4B << XSPI0_CDTBUF0_CMD_Pos) |
						 (0U << XSPI0_CDTBUF0_TRTYPE_Pos)            |
						 (1U << XSPI0_CDTBUF0_DATASIZE_Pos)          |
						 (3U << XSPI0_CDTBUF0_ADDSIZE_Pos)           |
						 (1U << XSPI0_CDTBUF0_CMDSIZE_Pos);
		XSPI1->CDABUF0  = addr;
		__DSB();
		XSPI1->CDCTL0_b.TRREQ  = 0x1;											// Transaction request
		__DSB();
		while( XSPI1->INTS_b.CMDCMP!=1 ){										// Command Completed?
			__no_operation();
		}
		XSPI1->INTC_b.CMDCMPC = 0x1;											// Command Completed interrupt clear
		buffer[i] = (uint8_t)XSPI1->CDD0BUF0;									// 1byte Read
		addr++;
	}
	return( RESULT_OK );
}

/******************************************************************************/
static uint32_t flash_block_write(uint32_t addr,uint32_t block,const uint8_t *buffer)
{
	uint32_t i,j;
	const uint32_t *src = (const uint32_t *)(buffer);

	for(i=0;i<block;i++){														// Write by block
		for(j=0;j<(FLASH_PAGE_SIZE/MAX_SPI_TRANS_SIZE);j++){
			flash_wren();														// Write permission command execution
			XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;					// Protocol mode 1S-1S-1S Select
			XSPI1->CDCTL0_b.TRREQ    = 0x0;										// No transaction
			XSPI1->CDTBUF0  = (FLASH_CMD_PP4B << XSPI0_CDTBUF0_CMD_Pos ) |
							  (1U << XSPI0_CDTBUF0_TRTYPE_Pos)           |
							  (1U << XSPI0_CDTBUF0_CMDSIZE_Pos)          |
							  (3U << XSPI0_CDTBUF0_ADDSIZE_Pos)          |
							  (8U << XSPI0_CDTBUF0_DATASIZE_Pos);
			XSPI1->CDABUF0  = addr;												// Address settings
			XSPI1->CDD0BUF0 = (*src);											// Data settings
			src++;
			XSPI1->CDD1BUF0 = (*src);											// Data settings
			__DSB();
			XSPI1->CDCTL0_b.TRREQ  = 0x1;										// Transaction request
			__DSB();
			while( XSPI1->INTS_b.CMDCMP!=1 ){									// Command Completed?
				__no_operation();
			}
			XSPI1->INTC_b.CMDCMPC = 0x1;										// Command Completed interrupt clear
			if( flash_write_end(CMTW_CLOCK_FREQUENCY*PAGE_PROGRAM_WAIT)!=RESULT_OK ){	// waiting for write end
				return( RESULT_ERROR );
			}
			addr+=MAX_SPI_TRANS_SIZE;
			src++;
		}
	}
	return( RESULT_OK );
}

/******************************************************************************/
static uint32_t flash_word_write(uint32_t addr,uint32_t count,const uint8_t *buffer)
{
	uint32_t i;
	const uint32_t *src = (const uint32_t *)(buffer);

	for(i=0;i<count;i++){
		flash_wren();															// Write permission command execution
		XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;						// Protocol mode 1S-1S-1S Select	
		XSPI1->CDCTL0_b.TRREQ    = 0x0;											// No transaction
		XSPI1->CDTBUF0  = ( FLASH_CMD_PP4B << XSPI0_CDTBUF0_CMD_Pos ) |
						  ( 1U << XSPI0_CDTBUF0_TRTYPE_Pos )          |
						  ( 1U << XSPI0_CDTBUF0_CMDSIZE_Pos )         |
						  ( 3U << XSPI0_CDTBUF0_ADDSIZE_Pos )         |
						  ( 4U << XSPI0_CDTBUF0_DATASIZE_Pos );
		XSPI1->CDABUF0  = addr;													// Address settings
		XSPI1->CDD0BUF0 = (*src);												// Data settings
		__DSB();
		XSPI1->CDCTL0_b.TRREQ  = 0x1;											// Transaction request
		__DSB();
		while( XSPI1->INTS_b.CMDCMP!=1 ){										// Command Completed?
			__no_operation();
		}
		XSPI1->INTC_b.CMDCMPC = 0x1;											// Command Completed interrupt clear
		if( flash_write_end(CMTW_CLOCK_FREQUENCY*PAGE_PROGRAM_WAIT)!=RESULT_OK ){	// waiting for write end
			return( RESULT_ERROR );
		}
		addr+=4;
		src++;
	}
	return( RESULT_OK );
}

/******************************************************************************/
static uint32_t flash_byte_write(uint32_t addr,uint32_t count,const uint8_t *buffer)
{
	uint32_t data;

	switch(count){
	case 1:
		data  = (*buffer);
		data |= 0xffffff00ul;
		break;
	case 2:
		data = (*buffer);
		buffer++;
		data |= (*buffer) << 8;
		data |= 0xffff0000ul;
		break;
	case 3:
		data = (*buffer);
		buffer++;
		data |= (*buffer) << 8;
		buffer++;
		data |= (*buffer) << 16;
		data |= 0xff000000ul;
		break;
	default:
		data = 0;
		break;
	}
	flash_wren();																// Write permission command execution
	XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;							// Protocol mode 1S-1S-1S Select	
	XSPI1->CDCTL0_b.TRREQ    = 0x0;												// No transaction
	XSPI1->CDTBUF0 = ( FLASH_CMD_PP4B << XSPI0_CDTBUF0_CMD_Pos ) |
					 ( 1U << XSPI0_CDTBUF0_TRTYPE_Pos)           |
					 ( 1U << XSPI0_CDTBUF0_CMDSIZE_Pos)          |
					 ( 3U << XSPI0_CDTBUF0_ADDSIZE_Pos)          |
					 ( 4U << XSPI0_CDTBUF0_DATASIZE_Pos);
	XSPI1->CDABUF0  = addr;														// Address settings
	XSPI1->CDD0BUF0 = data;														// Data settings
	__DSB();
	XSPI1->CDCTL0_b.TRREQ  = 0x1;												// Transaction request
	__DSB();
	while( XSPI1->INTS_b.CMDCMP!=1 ){											// Command Completed?
		__no_operation();
	}
	XSPI1->INTC_b.CMDCMPC = 0x1;												// Command Completed interrupt clear
	if( flash_write_end(CMTW_CLOCK_FREQUENCY*PAGE_PROGRAM_WAIT)!=RESULT_OK ){	// waiting for write end
		return( RESULT_ERROR );
	}
	return( RESULT_OK );
}

/******************************************************************************/
static uint32_t flash_reset(void)
{
	XSPI1->CDCTL0_b.TRREQ    = 0x0;												// No transaction
	XSPI1->CDTBUF0 = ( FLASH_CMD_RSTEN << XSPI0_CDTBUF0_CMD_Pos ) |
					 ( 1U << XSPI0_CDTBUF0_TRTYPE_Pos ) |
					 ( 1U << XSPI0_CDTBUF0_CMDSIZE_Pos );
	__DSB();
	XSPI1->CDCTL0_b.TRREQ  = 0x1;												// Transaction request
	__DSB();
	while( XSPI1->INTS_b.CMDCMP!=1 ){											// Command Completed?
		__no_operation();
    }
	XSPI1->INTC_b.CMDCMPC = 0x1;												// Command Completed interrupt clear

	XSPI1->CDCTL0_b.TRREQ    = 0x0;												// No transaction
	XSPI1->CDTBUF0 = ( FLASH_CMD_RST << XSPI0_CDTBUF0_CMD_Pos ) |
					 ( 1U << XSPI0_CDTBUF0_TRTYPE_Pos )         |
					 ( 1U << XSPI0_CDTBUF0_CMDSIZE_Pos );
	__DSB();
	XSPI1->CDCTL0_b.TRREQ  = 0x1;												// Transaction request
	__DSB();
	while( XSPI1->INTS_b.CMDCMP!=1 ){											// Command Completed?
		__no_operation();
    }
	XSPI1->INTC_b.CMDCMPC = 0x1;												// Command Completed interrupt clear

	CMTW0->CMWCNT   = 0x0ul;													// Clear Compare Match Timer W (CMTW0)
	CMTW0->CMWCOR   = 0xfffffffful;												// Initialize Compare Match Constant Register (CMTW0)
	CMTW0->CMWSTR   = 1;														// Start Compare Match Timer W (CMTW0)
	for(;;){
		if( (FLASH_RESET_WAIT*CMTW_CLOCK_FREQUENCY)<CMTW0->CMWCNT ){			// Timeout?
			break;
		}
	}
	CMTW0->CMWSTR = 0;															// Stop Compare Match Timer W (CMTW0)
	return( RESULT_OK );														// Successful
}

/******************************************************************************/
static uint32_t flash_write_end(uint32_t max_time)
{
	CMTW0->CMWCNT   = 0x0ul;													// Clear Compare Match Timer W (CMTW0)
	CMTW0->CMWCOR   = 0xfffffffful;												// Initialize Compare Match Constant Register (CMTW0)
	CMTW0->CMWSTR   = 1;														// Start Compare Match Timer W (CMTW0)
	for(;;){
		if( (flash_get_status() & STATUS_WIP)==0 ){								// End of write?
			break;
		}
		if( max_time<CMTW0->CMWCNT ){											// Timerout?
			CMTW0->CMWSTR = 0;													// Stop Compare Match Timer W (CMTW0)
			return( RESULT_ERROR );
		}
	}
	CMTW0->CMWSTR = 0;															// Stop Compare Match Timer W (CMTW0)
	return( RESULT_OK );
}

/******************************************************************************/
static uint32_t flash_get_id(uint32_t *id)
{
	XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;							// Protocol mode 1S-1S-1S Select
	XSPI1->CDCTL0_b.TRREQ    = 0x0;												// No transaction
	XSPI1->CDTBUF0 = ( FLASH_CMD_RDID << XSPI0_CDTBUF0_CMD_Pos ) |
					 ( 0U << XSPI0_CDTBUF0_TRTYPE_Pos )          |
					 ( 1U << XSPI0_CDTBUF0_CMDSIZE_Pos )         |
					 ( 3U << XSPI0_CDTBUF0_DATASIZE_Pos );
	__DSB();
	XSPI1->CDCTL0_b.TRREQ  = 0x1;												// Transaction request
	__DSB();
	while( XSPI1->INTS_b.CMDCMP!=1 ){											// Command Completed?
		__no_operation();
    }
	XSPI1->INTC_b.CMDCMPC = 0x1;												// Command Completed interrupt clear
	(*id) = XSPI1->CDD0BUF0;
	return( RESULT_OK );														// Successful
}

/******************************************************************************/
static void flash_wren(void)
{
	XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;							// Protocol mode 1S-1S-1S Select
	XSPI1->CDCTL0_b.TRREQ    = 0x0;												// No transaction
	XSPI1->CDTBUF0 = (FLASH_CMD_WREN << XSPI0_CDTBUF0_CMD_Pos) |
					 (1U << XSPI0_CDTBUF0_TRTYPE_Pos)          |
					 (1U << XSPI0_CDTBUF0_CMDSIZE_Pos);
	__DSB();
	XSPI1->CDCTL0_b.TRREQ  = 0x1;												// Transaction request
	__DSB();
	while( XSPI1->INTS_b.CMDCMP!=1 ){											// Command Completed?
		__no_operation();
    }
	XSPI1->INTC_b.CMDCMPC = 0x1;												// Command Completed interrupt clear
}

/******************************************************************************/
static uint8_t flash_get_status(void)
{
	XSPI1->LIOCFGCS0_b.PRTMD = LIOCFGCS_PRTMD_1S1S1S;							// Protocol mode 1S-1S-1S Select
	XSPI1->CDCTL0_b.TRREQ    = 0x0;												// No transaction
	XSPI1->CDTBUF0 = (FLASH_CMD_RDSR << XSPI0_CDTBUF0_CMD_Pos) |
					 (0U << XSPI0_CDTBUF0_TRTYPE_Pos)          |
					 (1U << XSPI0_CDTBUF0_CMDSIZE_Pos)         |
					 (2U << XSPI0_CDTBUF0_DATASIZE_Pos);
	__DSB();
	XSPI1->CDCTL0_b.TRREQ  = 0x1;												// Transaction request
	__DSB();
	while( XSPI1->INTS_b.CMDCMP!=1 ){											// Command Completed?
		__no_operation();
    }
	XSPI1->INTC_b.CMDCMPC = 0x1;												// Command Completed interrupt clear
	return( (uint8_t)XSPI1->CDD0BUF0 );
}
