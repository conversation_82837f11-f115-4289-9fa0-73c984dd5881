/* generated configuration header file - do not edit */
#ifndef BSP_MCU_DEVICE_PN_CFG_H_
#define BSP_MCU_DEVICE_PN_CFG_H_
#define BSP_MCU_R9A07G084M04GBG
      #define BSP_ATCM_SIZE_BYTES (131072)
      #define BSP_BTCM_SIZE_BYTES (131072)
      #define BSP_SYSTEM_RAM_SIZE_BYTES (1572864)
      #define BSP_PACKAGE_FBGA
      #define BSP_PACKAGE_PINS (225)

      #define BSP_CFG_CPU (1)

      #define BSP_M_MPU0_SUPPORT
      #define BSP_M_MPU1_SUPPORT
      #define BSP_M_MPU2_SUPPORT
      #define BSP_M_MPU3_SUPPORT
      #define BSP_M_MPU4_SUPPORT
      #define BSP_M_MPU6_SUPPORT
      #define BSP_M_MPU7_SUPPORT
      #define BSP_M_MPU8_SUPPORT

      #define BSP_CFG_MPU0_READ0 (0)
      #define BSP_CFG_MPU0_WRITE0 (0)
      #define BSP_CFG_MPU0_STADD0 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU0_READ1 (0)
      #define BSP_CFG_MPU0_WRITE1 (0)
      #define BSP_CFG_MPU0_STADD1 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU0_READ2 (0)
      #define BSP_CFG_MPU0_WRITE2 (0)
      #define BSP_CFG_MPU0_STADD2 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU0_READ3 (0)
      #define BSP_CFG_MPU0_WRITE3 (0)
      #define BSP_CFG_MPU0_STADD3 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU0_READ4 (0)
      #define BSP_CFG_MPU0_WRITE4 (0)
      #define BSP_CFG_MPU0_STADD4 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU0_READ5 (0)
      #define BSP_CFG_MPU0_WRITE5 (0)
      #define BSP_CFG_MPU0_STADD5 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU0_READ6 (0)
      #define BSP_CFG_MPU0_WRITE6 (0)
      #define BSP_CFG_MPU0_STADD6 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU0_READ7 (0)
      #define BSP_CFG_MPU0_WRITE7 (0)
      #define BSP_CFG_MPU0_STADD7 (0x00000000)
      #define BSP_CFG_MPU0_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU1_READ0 (0)
      #define BSP_CFG_MPU1_WRITE0 (0)
      #define BSP_CFG_MPU1_STADD0 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU1_READ1 (0)
      #define BSP_CFG_MPU1_WRITE1 (0)
      #define BSP_CFG_MPU1_STADD1 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU1_READ2 (0)
      #define BSP_CFG_MPU1_WRITE2 (0)
      #define BSP_CFG_MPU1_STADD2 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU1_READ3 (0)
      #define BSP_CFG_MPU1_WRITE3 (0)
      #define BSP_CFG_MPU1_STADD3 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU1_READ4 (0)
      #define BSP_CFG_MPU1_WRITE4 (0)
      #define BSP_CFG_MPU1_STADD4 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU1_READ5 (0)
      #define BSP_CFG_MPU1_WRITE5 (0)
      #define BSP_CFG_MPU1_STADD5 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU1_READ6 (0)
      #define BSP_CFG_MPU1_WRITE6 (0)
      #define BSP_CFG_MPU1_STADD6 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU1_READ7 (0)
      #define BSP_CFG_MPU1_WRITE7 (0)
      #define BSP_CFG_MPU1_STADD7 (0x00000000)
      #define BSP_CFG_MPU1_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU2_READ0 (0)
      #define BSP_CFG_MPU2_WRITE0 (0)
      #define BSP_CFG_MPU2_STADD0 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU2_READ1 (0)
      #define BSP_CFG_MPU2_WRITE1 (0)
      #define BSP_CFG_MPU2_STADD1 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU2_READ2 (0)
      #define BSP_CFG_MPU2_WRITE2 (0)
      #define BSP_CFG_MPU2_STADD2 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU2_READ3 (0)
      #define BSP_CFG_MPU2_WRITE3 (0)
      #define BSP_CFG_MPU2_STADD3 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU2_READ4 (0)
      #define BSP_CFG_MPU2_WRITE4 (0)
      #define BSP_CFG_MPU2_STADD4 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU2_READ5 (0)
      #define BSP_CFG_MPU2_WRITE5 (0)
      #define BSP_CFG_MPU2_STADD5 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU2_READ6 (0)
      #define BSP_CFG_MPU2_WRITE6 (0)
      #define BSP_CFG_MPU2_STADD6 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU2_READ7 (0)
      #define BSP_CFG_MPU2_WRITE7 (0)
      #define BSP_CFG_MPU2_STADD7 (0x00000000)
      #define BSP_CFG_MPU2_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU3_READ0 (0)
      #define BSP_CFG_MPU3_WRITE0 (0)
      #define BSP_CFG_MPU3_STADD0 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU3_READ1 (0)
      #define BSP_CFG_MPU3_WRITE1 (0)
      #define BSP_CFG_MPU3_STADD1 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU3_READ2 (0)
      #define BSP_CFG_MPU3_WRITE2 (0)
      #define BSP_CFG_MPU3_STADD2 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU3_READ3 (0)
      #define BSP_CFG_MPU3_WRITE3 (0)
      #define BSP_CFG_MPU3_STADD3 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU3_READ4 (0)
      #define BSP_CFG_MPU3_WRITE4 (0)
      #define BSP_CFG_MPU3_STADD4 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU3_READ5 (0)
      #define BSP_CFG_MPU3_WRITE5 (0)
      #define BSP_CFG_MPU3_STADD5 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU3_READ6 (0)
      #define BSP_CFG_MPU3_WRITE6 (0)
      #define BSP_CFG_MPU3_STADD6 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU3_READ7 (0)
      #define BSP_CFG_MPU3_WRITE7 (0)
      #define BSP_CFG_MPU3_STADD7 (0x00000000)
      #define BSP_CFG_MPU3_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU4_READ0 (0)
      #define BSP_CFG_MPU4_WRITE0 (0)
      #define BSP_CFG_MPU4_STADD0 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU4_READ1 (0)
      #define BSP_CFG_MPU4_WRITE1 (0)
      #define BSP_CFG_MPU4_STADD1 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU4_READ2 (0)
      #define BSP_CFG_MPU4_WRITE2 (0)
      #define BSP_CFG_MPU4_STADD2 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU4_READ3 (0)
      #define BSP_CFG_MPU4_WRITE3 (0)
      #define BSP_CFG_MPU4_STADD3 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU4_READ4 (0)
      #define BSP_CFG_MPU4_WRITE4 (0)
      #define BSP_CFG_MPU4_STADD4 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU4_READ5 (0)
      #define BSP_CFG_MPU4_WRITE5 (0)
      #define BSP_CFG_MPU4_STADD5 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU4_READ6 (0)
      #define BSP_CFG_MPU4_WRITE6 (0)
      #define BSP_CFG_MPU4_STADD6 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU4_READ7 (0)
      #define BSP_CFG_MPU4_WRITE7 (0)
      #define BSP_CFG_MPU4_STADD7 (0x00000000)
      #define BSP_CFG_MPU4_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU6_READ0 (0)
      #define BSP_CFG_MPU6_WRITE0 (0)
      #define BSP_CFG_MPU6_STADD0 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU6_READ1 (0)
      #define BSP_CFG_MPU6_WRITE1 (0)
      #define BSP_CFG_MPU6_STADD1 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU6_READ2 (0)
      #define BSP_CFG_MPU6_WRITE2 (0)
      #define BSP_CFG_MPU6_STADD2 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU6_READ3 (0)
      #define BSP_CFG_MPU6_WRITE3 (0)
      #define BSP_CFG_MPU6_STADD3 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU6_READ4 (0)
      #define BSP_CFG_MPU6_WRITE4 (0)
      #define BSP_CFG_MPU6_STADD4 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU6_READ5 (0)
      #define BSP_CFG_MPU6_WRITE5 (0)
      #define BSP_CFG_MPU6_STADD5 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU6_READ6 (0)
      #define BSP_CFG_MPU6_WRITE6 (0)
      #define BSP_CFG_MPU6_STADD6 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU6_READ7 (0)
      #define BSP_CFG_MPU6_WRITE7 (0)
      #define BSP_CFG_MPU6_STADD7 (0x00000000)
      #define BSP_CFG_MPU6_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU7_READ0 (0)
      #define BSP_CFG_MPU7_WRITE0 (0)
      #define BSP_CFG_MPU7_STADD0 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU7_READ1 (0)
      #define BSP_CFG_MPU7_WRITE1 (0)
      #define BSP_CFG_MPU7_STADD1 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU7_READ2 (0)
      #define BSP_CFG_MPU7_WRITE2 (0)
      #define BSP_CFG_MPU7_STADD2 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU7_READ3 (0)
      #define BSP_CFG_MPU7_WRITE3 (0)
      #define BSP_CFG_MPU7_STADD3 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU7_READ4 (0)
      #define BSP_CFG_MPU7_WRITE4 (0)
      #define BSP_CFG_MPU7_STADD4 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU7_READ5 (0)
      #define BSP_CFG_MPU7_WRITE5 (0)
      #define BSP_CFG_MPU7_STADD5 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU7_READ6 (0)
      #define BSP_CFG_MPU7_WRITE6 (0)
      #define BSP_CFG_MPU7_STADD6 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU7_READ7 (0)
      #define BSP_CFG_MPU7_WRITE7 (0)
      #define BSP_CFG_MPU7_STADD7 (0x00000000)
      #define BSP_CFG_MPU7_ENDADD7 (0x00000000)
      #define BSP_CFG_MPU8_READ0 (0)
      #define BSP_CFG_MPU8_WRITE0 (0)
      #define BSP_CFG_MPU8_STADD0 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD0 (0x00000000)
      #define BSP_CFG_MPU8_READ1 (0)
      #define BSP_CFG_MPU8_WRITE1 (0)
      #define BSP_CFG_MPU8_STADD1 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD1 (0x00000000)
      #define BSP_CFG_MPU8_READ2 (0)
      #define BSP_CFG_MPU8_WRITE2 (0)
      #define BSP_CFG_MPU8_STADD2 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD2 (0x00000000)
      #define BSP_CFG_MPU8_READ3 (0)
      #define BSP_CFG_MPU8_WRITE3 (0)
      #define BSP_CFG_MPU8_STADD3 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD3 (0x00000000)
      #define BSP_CFG_MPU8_READ4 (0)
      #define BSP_CFG_MPU8_WRITE4 (0)
      #define BSP_CFG_MPU8_STADD4 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD4 (0x00000000)
      #define BSP_CFG_MPU8_READ5 (0)
      #define BSP_CFG_MPU8_WRITE5 (0)
      #define BSP_CFG_MPU8_STADD5 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD5 (0x00000000)
      #define BSP_CFG_MPU8_READ6 (0)
      #define BSP_CFG_MPU8_WRITE6 (0)
      #define BSP_CFG_MPU8_STADD6 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD6 (0x00000000)
      #define BSP_CFG_MPU8_READ7 (0)
      #define BSP_CFG_MPU8_WRITE7 (0)
      #define BSP_CFG_MPU8_STADD7 (0x00000000)
      #define BSP_CFG_MPU8_ENDADD7 (0x00000000)
#endif /* BSP_MCU_DEVICE_PN_CFG_H_ */
