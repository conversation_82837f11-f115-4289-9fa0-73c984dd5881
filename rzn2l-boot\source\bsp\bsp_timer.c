/****************************************************************************************************
 *
 * FILE NAME:  HardApi.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "hal_data.h"
#include "bsp_timer.h"

/**********************************************************
 * 获取用于程序计时的32位定时器的计数值
 * 
 *********************************************************/
uint32_t bsp_gettimercnt(void)
{
   return R_CMTW1->CMWCNT; 
}

/***********************************************************************************************************************
* Function Name: 初始化用于程序计时的32位的定时器
* Description  : CMTW1  PCLKL/8 = 6.25Mhz    0.16 us/cnt
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void bsp_timerCounter_init(void)
{
    uint32_t dummy;

    dummy = 1UL;
    /* Cancel CMTW stop state in LPC */
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_CMTW, dummy);
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);


    /*设置时钟分频 0： PCLKL/8  1： PCLKL/32  2：PCLKL/128 3：PCLKL/512*/
    R_CMTW1->CMWCR_b.CKS = 0;   
    
    /*设置时钟为32位计数*/
    R_CMTW1->CMWCR_b.CMS = 0; 
    
    
    /*设置时钟计数清除条件 ：不清除*/
    R_CMTW1->CMWCR_b.CCLR = 1;     
    
    /*不开启中断*/
    R_CMTW1->CMWCR_b.CMWIE = 0;
    R_CMTW1->CMWCR_b.IC0IE = 0;
    R_CMTW1->CMWCR_b.IC1IE = 0;
    R_CMTW1->CMWCR_b.OC0IE = 0;
    R_CMTW1->CMWCR_b.OC1IE = 0;
    
    /*配置定时器输出IO*/
    
    /*复位计数器*/
    R_CMTW1->CMWCNT = (uint32_t) 0x00000000U;
    
    /*开启定时 */
    R_CMTW1->CMWSTR_b.STR = 1;
  
}