/* generated configuration header file - do not edit */
#ifndef BOARD_CFG_H_
#define BOARD_CFG_H_
//#define BSP_CFG_RAM_EXECUTION (1)
        void bsp_init(void * p_args);


            #define BSP_CFG_XSPI1_X1_BOOT (1)
#if 0
            #define BSP_CFG_CACHE_FLG (0x00000000)
            #define BSP_CFG_CS0BCR_V_WRAPCFG_V (0x00000000)
            #define BSP_CFG_CS0WCR_V_COMCFG_V (0x00000000)
            #define BSP_CFG_DUMMY0_BMCFG_V (0x00000000)
            #define BSP_CFG_BSC_FLG_xSPI_FLG (0x00000000)
            #define BSP_CFG_LDR_ADDR_NML (0x6800004C)
            #define BSP_CFG_LDR_SIZE_NML (0x00006000)
            #define BSP_CFG_DEST_ADDR_NML (0x00102000)
            #define BSP_CFG_DNUMMY1 (0x00000000)
            #define BSP_CFG_DNUMMY2 (0x00000000)
            #define BSP_CFG_DNUMMY3_CSSCTL_V (0x0000003F)
            #define BSP_CFG_DNUMMY4_LIOCFGCS0_V (0x00070000)
            #define BSP_CFG_DNUMMY5 (0x00000000)
            #define BSP_CFG_DNUMMY6 (0x00000000)
            #define BSP_CFG_DNUMMY7 (0x00000000)
            #define BSP_CFG_DNUMMY8 (0x00000000)
            #define BSP_CFG_DNUMMY9 (0x00000000)
            #define BSP_CFG_DNUMMY10_ACCESS_SPEED (0x00000600)
            #define BSP_CFG_CHECK_SUM (0xEEA2)
#endif

#endif /* BOARD_CFG_H_ */
