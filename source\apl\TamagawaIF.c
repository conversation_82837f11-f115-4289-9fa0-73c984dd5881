/****************************************************************************************************
 *
 * FILE NAME:  TamagawaIF.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.09.08
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	08-09-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "hal_data.h"
#include "TamagawaIF.h"
#include "bsp_enc.h"

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
#if 1
PUBLIC UINT8 TmgwCRC8Check(UINT8 *ptr, UINT8 len)
{
    UINT8 TmgwCrc = 0;
    UINT8 i = 0;
        
    for(i =0; i<len; i++)
    {        
        TmgwCrc ^= *ptr;
        ptr++;
    }
    return TmgwCrc;
}
#endif

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void TmgwSetCF(UINT16 AxisID, UINT16 Lenght, UINT8* TmgwCF)
{
    bsp_sci2_3_transmit(AxisID,Lenght,TmgwCF);  // 读取编码器多圈+单圈    
}


/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT8 TmgwWriteEep(UINT16 AxisID, UINT8 *pData, UINT8 Addr)
{
  UINT8 Wbuffer[4] = {0};
  UINT8 Rbuffer[4] = {0};
  UINT8 WriteData = *pData;
  UINT8 ret = 0;
  UINT8 Busy = 1;
  UINT8 Crc_Temp = 0;

  Wbuffer[0] = TMGW_DATA_ID_6;
  Wbuffer[1] = Addr;
  Wbuffer[2] = *pData;
  Wbuffer[3] = TmgwCRC8Check(&Wbuffer[0],3); 
  
  // write data to eeprom 
  bsp_sci2_3_transmit_timeout(AxisID,4,&Wbuffer[0],100);
  
  // delay time 18 ms
  R_BSP_SoftwareDelay(18,BSP_DELAY_UNITS_MILLISECONDS);   
  
  while(Busy)
  {
    Wbuffer[0] = TMGW_DATA_ID_D;
    Wbuffer[2] = TmgwCRC8Check(&Wbuffer[0],2);  
    // read data from eeprom 
    bsp_sci2_3_transmit_timeout(AxisID,3,&Wbuffer[0],2000);
    
    // recevice data from encoder
    ret = bsp_sci2_3_receive_timeout(AxisID, 4,&Rbuffer[0],2000);
    
    // read ok and encoder is not in busy
    if(ret == 0 && (Rbuffer[1] & 0x80) == 0)
    {
      Busy = 0;
      
      if(WriteData != Rbuffer[2])
      {
        ret = 1;
      }
    }
    
    // delay time 100 us
    R_BSP_SoftwareDelay(100,BSP_DELAY_UNITS_MICROSECONDS);
  }      
 
  return ret;
}
/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT8 TmgwReadEep(UINT16 AxisID,UINT8 *Data, UINT8 Addr)
{
  
  UINT8 Wbuffer[3] = {0};
  UINT8 Rbuffer[4] = {0};
  UINT8 ret = 0, result = 0;
  UINT8 Crc_temp = 0;

  Wbuffer[0] = TMGW_DATA_ID_D;
  Wbuffer[1] = Addr;
  Wbuffer[2] = TmgwCRC8Check(&Wbuffer[0],2);
  
  // read data to eeprom 
  bsp_sci2_3_transmit_timeout(AxisID,3,&Wbuffer[0],2000);  
      
  // recevice data from encoder
  ret = bsp_sci2_3_receive_timeout(AxisID,4, &Rbuffer[0],2000);
  

  if(ret == 0)
  {
    Crc_temp = TmgwCRC8Check(&Rbuffer[0],3);
    if(Crc_temp != Rbuffer[3])
    {
      result = 2;
    }
    else
    {
      *Data = Rbuffer[2];
    }
  }
  else
  {
    //encoder break;
    result = 1;
  }
  
  // delay time 10 us
  R_BSP_SoftwareDelay(100,BSP_DELAY_UNITS_MICROSECONDS);
  
  return result;
}