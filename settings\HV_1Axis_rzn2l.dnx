<?xml version="1.0"?>
<settings>
    <Stack>
        <FillEnabled>1</FillEnabled>
        <OverflowWarningsEnabled>1</OverflowWarningsEnabled>
        <WarningThreshold>90</WarningThreshold>
        <SpWarningsEnabled>1</SpWarningsEnabled>
        <WarnLogOnly>1</WarnLogOnly>
        <UseTrigger>1</UseTrigger>
        <TriggerName>main</TriggerName>
        <LimitSize>0</LimitSize>
        <ByteLimit>50</ByteLimit>
    </Stack>
    <PlDriver>
        <FirstRun>0</FirstRun>
        <MemConfigValue>E:\1.Work\1.code\1.Workspace\Renesas\rzn2l_codespace\20231211\1_HV_Axis_flash_App_gitee/script/R9A07G084M04.ddf</MemConfigValue>
    </PlDriver>
    <Jet>
        <JetConnSerialNo>93039</JetConnSerialNo>
        <JetConnFoundProbes />
        <PrevWtdReset>Hardware</PrevWtdReset>
        <OnlineReset>Hardware</OnlineReset>
        <DisableInterrupts>0</DisableInterrupts>
        <LeaveRunning>0</LeaveRunning>
        <MultiCoreRunAll>0</MultiCoreRunAll>
    </Jet>
    <ArmDriver>
        <EnforceMemoryConfiguration>1</EnforceMemoryConfiguration>
        <EnableCache>1</EnableCache>
    </ArmDriver>
    <DebugChecksum>
        <Checksum>359928400</Checksum>
    </DebugChecksum>
    <Exceptions>
        <StopOnUncaught>_ 0</StopOnUncaught>
        <StopOnThrow>_ 0</StopOnThrow>
    </Exceptions>
    <Disassembly>
        <MixedMode>0</MixedMode>
        <InstrCount>0</InstrCount>
    </Disassembly>
    <CallStack>
        <ShowArgs>0</ShowArgs>
    </CallStack>
    <JLinkDriver>
        <WatchCond>_ 0</WatchCond>
        <Watch0>_ 0 "0x00000000" 4294967295 "0xFFFFFFFF" 0 "0x00000000" 4294967295 "0xFFFFFFFF" 3 4 0 0</Watch0>
        <Watch1>_ 0 "0x00000000" 4294967295 "0xFFFFFFFF" 0 "0x00000000" 4294967295 "0xFFFFFFFF" 3 4 0 0</Watch1>
        <jlinkResetStyle>13</jlinkResetStyle>
        <jlinkResetStrategy>4</jlinkResetStrategy>
        <LeaveTargetRunning>_ 0</LeaveTargetRunning>
        <CStepIntDis>_ 0</CStepIntDis>
    </JLinkDriver>
    <SWOManager>
        <SamplingDivider>8192</SamplingDivider>
        <OverrideClock>0</OverrideClock>
        <CpuClock>1095326835</CpuClock>
        <SwoClock>2035490898</SwoClock>
        <DataLogMode>0</DataLogMode>
        <ItmPortsEnabled>63</ItmPortsEnabled>
        <ItmTermIOPorts>1</ItmTermIOPorts>
        <ItmLogPorts>0</ItmLogPorts>
        <ItmLogFile>$PROJ_DIR$\ITM.log</ItmLogFile>
        <PowerForcePC>1</PowerForcePC>
        <PowerConnectPC>1</PowerConnectPC>
    </SWOManager>
    <struct_types>
        <Fmt0>AXIS_HANDLE-HardwareVer	4	0</Fmt0>
        <Fmt1>AXIS_HANDLE-Prm	4	0</Fmt1>
        <Fmt2>DIGKEY_MENU-LedValue	4	0</Fmt2>
        <Fmt3>DIGKEY_MENU-MenuName	4	0</Fmt3>
        <Fmt4>MODBUS_ST-C1P5Cnt	4	0</Fmt4>
        <Fmt5>MODBUS_ST-C3P5Cnt	4	0</Fmt5>
        <Fmt6>MOTOR_PRM-AbsEncBitM	4	0</Fmt6>
        <Fmt7>MOTOR_PRM-AbsEncBitS	4	0</Fmt7>
        <Fmt8>MOTOR_PRM-AbsEncOffset	4	0</Fmt8>
        <Fmt9>MOTOR_PRM-AbzEncPPR	4	0</Fmt9>
        <Fmt10>MOTOR_PRM-BisscDLen	4	0</Fmt10>
        <Fmt11>MOTOR_PRM-EncType	4	0</Fmt11>
        <Fmt12>MOTOR_PRM-Jmot	4	0</Fmt12>
        <Fmt13>MOTOR_PRM-MaxCur	4	0</Fmt13>
        <Fmt14>MOTOR_PRM-MaxSpd	4	0</Fmt14>
        <Fmt15>MOTOR_PRM-MaxTrq	4	0</Fmt15>
        <Fmt16>MOTOR_PRM-MotEmf	4	0</Fmt16>
        <Fmt17>MOTOR_PRM-MotKt	4	0</Fmt17>
        <Fmt18>MOTOR_PRM-MotL	4	0</Fmt18>
        <Fmt19>MOTOR_PRM-MotLd	4	0</Fmt19>
        <Fmt20>MOTOR_PRM-MotLq	4	0</Fmt20>
        <Fmt21>MOTOR_PRM-MotPolePairs	4	0</Fmt21>
        <Fmt22>MOTOR_PRM-MotR	4	0</Fmt22>
        <Fmt23>MOTOR_PRM-MotTs	4	0</Fmt23>
        <Fmt24>MOTOR_PRM-MotType	4	0</Fmt24>
        <Fmt25>MOTOR_PRM-MotorPitch	4	0</Fmt25>
        <Fmt26>MOTOR_PRM-RatCur	4	0</Fmt26>
        <Fmt27>MOTOR_PRM-RatFreq	4	0</Fmt27>
        <Fmt28>MOTOR_PRM-RatPow	4	0</Fmt28>
        <Fmt29>MOTOR_PRM-RatSpd	4	0</Fmt29>
        <Fmt30>MOTOR_PRM-RatTrq	4	0</Fmt30>
        <Fmt31>MOTOR_PRM-RatV	4	0</Fmt31>
        <Fmt32>REG_ACC_T-MemAddr	4	0</Fmt32>
        <Fmt33>TSDOINFOOBJDESC-DataType	4	0</Fmt33>
        <Fmt34>TSDOINFOOBJDESC-ObjFlags	4	0</Fmt34>
        <Fmt35>struct OBJ_ENTRY-Index	4	0</Fmt35>
        <Fmt36>usb_event_info_t-data_size	4	0</Fmt36>
        <Fmt37>usb_event_info_t-status	4	0</Fmt37>
    </struct_types>
    <array_types>
        <Fmt0>unsigned char[1019]	4	0</Fmt0>
        <Fmt1>unsigned char[3]	4	0</Fmt1>
        <Fmt2>unsigned char[4]	4	0</Fmt2>
        <Fmt3>unsigned long *[30]	4	0</Fmt3>
        <Fmt4>unsigned long[108]	4	0</Fmt4>
        <Fmt5>unsigned long[50]	4	0</Fmt5>
        <Fmt6>unsigned short[2]	4	0</Fmt6>
    </array_types>
    <watch_formats>
        <Fmt0>{W}1:flashdestination	4	0</Fmt0>
        <Fmt1>{W}1:packet_size	4	0</Fmt1>
        <Fmt2>{W}1:starttimer	4	0</Fmt2>
        <Fmt3>{W}42:AxisID	4	0</Fmt3>
        <Fmt4>{W}42:CRC_Value	4	0</Fmt4>
        <Fmt5>{W}42:CrcCal	4	0</Fmt5>
        <Fmt6>{W}42:EncReadMotPrm	4	0</Fmt6>
        <Fmt7>{W}42:Flash_Uid0	4	0</Fmt7>
        <Fmt8>{W}42:Index	4	0</Fmt8>
        <Fmt9>{W}42:IsrStartTime	4	0</Fmt9>
        <Fmt10>{W}42:ModBusIndex	4	0</Fmt10>
        <Fmt11>{W}42:ParameterIndex	4	0</Fmt11>
        <Fmt12>{W}42:TmgwReadEep	4	0</Fmt12>
        <Fmt13>{W}42:UpgradeFlag	4	0</Fmt13>
        <Fmt14>{W}42:bDcSyncActive	4	0</Fmt14>
        <Fmt15>{W}42:chcfg	4	0</Fmt15>
        <Fmt16>{W}42:gCRC_temp	4	0</Fmt16>
        <Fmt17>{W}42:gEepromVersion	4	0</Fmt17>
        <Fmt18>{W}42:gLastTimeB	4	0</Fmt18>
        <Fmt19>{W}42:gbytetest	4	0</Fmt19>
        <Fmt20>{W}42:gdbytetest	4	0</Fmt20>
        <Fmt21>{W}42:pReadData	4	0</Fmt21>
        <Fmt22>{W}42:srcvalue	4	0</Fmt22>
    </watch_formats>
    <Trace1>
        <Enabled>0</Enabled>
        <ShowSource>1</ShowSource>
    </Trace1>
    <ETMTraceWindow>
        <PortWidth>4</PortWidth>
        <PortMode>0</PortMode>
        <CaptureDataValues>0</CaptureDataValues>
        <CaptureDataAddresses>0</CaptureDataAddresses>
        <CaptureDataRange>0</CaptureDataRange>
        <DataFirst>0</DataFirst>
        <DataLast>4294967295</DataLast>
        <StopWhen>0</StopWhen>
        <StallCPU>0</StallCPU>
        <NoPCCapture>0</NoPCCapture>
    </ETMTraceWindow>
    <Trace2>
        <Enabled>0</Enabled>
        <ShowSource>0</ShowSource>
    </Trace2>
    <SWOTraceWindow>
        <ForcedPcSampling>0</ForcedPcSampling>
        <ForcedInterruptLogs>0</ForcedInterruptLogs>
        <ForcedItmLogs>0</ForcedItmLogs>
        <EventCPI>0</EventCPI>
        <EventEXC>0</EventEXC>
        <EventFOLD>0</EventFOLD>
        <EventLSU>0</EventLSU>
        <EventSLEEP>0</EventSLEEP>
    </SWOTraceWindow>
    <PowerLog>
        <GraphEnabled>0</GraphEnabled>
        <LogEnabled>0</LogEnabled>
        <ShowTimeLog>1</ShowTimeLog>
        <Title_0>ITrgPwr</Title_0>
        <Symbol_0>0 4 0</Symbol_0>
        <LiveEnabled>0</LiveEnabled>
        <LiveFile />
    </PowerLog>
    <DriverProfiling>
        <Enabled>0</Enabled>
        <Mode>2</Mode>
        <Graph>0</Graph>
        <Symbiont>0</Symbiont>
        <Exclusions />
    </DriverProfiling>
    <CallStackLog>
        <Enabled>0</Enabled>
    </CallStackLog>
    <CallStackStripe>
        <ShowTiming>1</ShowTiming>
    </CallStackStripe>
    <PowerProbe>
        <Frequency>10000</Frequency>
        <Probe0>ITrgPwr</Probe0>
        <ProbeSetup0>2 1 1 2 0 0</ProbeSetup0>
    </PowerProbe>
    <TermIOLog>
        <LoggingEnabled>_ 0</LoggingEnabled>
        <LogFile>_ ""</LogFile>
    </TermIOLog>
    <DisassembleMode>
        <mode>0</mode>
    </DisassembleMode>
    <Breakpoints2>
        <Bp0>_ 0 "EMUL_CODE" "{$PROJ_DIR$\source\bsp\bsp_usb.c}.819.13" 0 0 1 "" 0 "" 0</Bp0>
        <Bp1>_ 0 "EMUL_CODE" "{$PROJ_DIR$\rzn\fsp\src\r_usb_basic\src\hw\r_usb_mcu.c}.854.5" 0 0 1 "" 0 "" 0</Bp1>
        <Count>2</Count>
    </Breakpoints2>
    <Aliases>
        <Count>0</Count>
        <SuppressDialog>0</SuppressDialog>
    </Aliases>
</settings>
