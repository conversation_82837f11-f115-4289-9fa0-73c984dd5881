/*******************************************************************************
								Copyright 2022 IAR Systems. All rights reserved.
Function:RZ/N2L(Cortex-R52) MX25UR51245G SPI Flashloader.

Note:

History:
 No  Rev  Date       Name			Note
---+-----+----------+-------------+--------------------------------------------
000 01.00 2022/07/29 S.Tonoshita	New Development
*******************************************************************************/
#ifndef	__MX25UR51245G_H__
#define	__MX25UR51245G_H__

/******************************************************************************/
//#define FLASH_ID				(0x001A35C2U)									// Device ID

#define FLASH_ID				(0x0001840c8)									// Device ID

#define FLASH_SECTOR_BLOCK		(16384)											// Maximum Sectors
#define FLASH_SECTOR_SIZE		(4096)											// Sector erase size (Byte)

#ifndef	__MX25UR51245G_C__
	extern uint32_t flash_init(uint32_t *id);
	extern uint32_t flash_read(uint32_t addr,uint32_t size,uint8_t *buffer);
	extern uint32_t flash_write(uint32_t addr,uint32_t count,const uint8_t *buffer);
	extern uint32_t flash_sector_erase(uint32_t addr);
#endif // __MX25UR51245G_C__

#endif // __MX25UR51245G_H__
