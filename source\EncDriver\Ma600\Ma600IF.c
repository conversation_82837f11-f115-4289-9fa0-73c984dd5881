#include "bsp_spi.h"
#include "bsp_timer.h"
#include "bsp.h"
#include "Encoder.h"
#include "Ma600IF.h"


/**
 * @brief 角度相关数据全局变量
 * @details 存储从KTM59xx芯片读取的角度值、圈数和状态信息
 */
MA600_Angle_T MA600_Angle =
{
  .singleTurn = 0,            /* 单圈角度值，16位 */
  .multiTurn = 0,  /* 圈数计数 */
  .AlmCode = 0,      /* 状态信息，2位 */
};

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE BOOL ParityCheck(uint16_t angleSensor,uint8_t parity)
{
    uint8_t highStateCount = 0;
    BOOL ParityBitCheck;
    
    for (int i=0;i<16;++i)
    {
      if ((angleSensor & (1 << i)) != 0)
      {
          highStateCount++;
      }
    }

    if ((highStateCount % 2) == 0) // EVEN
    {
      if (parity == 0)
      {
        ParityBitCheck = true;
      }
      else
      {
        ParityBitCheck = false;
      }
    }
    else // ODD
    {
      if (parity == 1)
      {
        ParityBitCheck = true;
      }
      else
      {
        ParityBitCheck = false;
      }
    }
    
    return ParityBitCheck;
    
}
/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
static void  MA600_SPI_Transmit(uint32_t *ptxdata, uint16_t txsize, uint16_t rxsize)
{
    bsp_spi_transfer(ptxdata, rxsize, txsize, 0);
}

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS: 0:成功 1:失败
 * 失败，接收到的数据长度小于预期，认为断线 
****************************************************************************************************/
uint32_t Spi_rxbuffer1[2] = {0};
static uint8_t MA600_SPI_Receive(uint32_t *prxdata, uint16_t size)
{
    uint8_t result = 0;
    
    result = bsp_spi_receive(&Spi_rxbuffer1[0], size, 0);
    
    *prxdata =  Spi_rxbuffer1[1];
    *(prxdata+1) = Spi_rxbuffer1[0];
    
    return result;
}

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
static void MA600_SPI_TransmitReceive(uint32_t *spitxbuf, uint32_t *spirxbuf, uint16_t TSize,uint16_t RSize)
{
  MA600_SPI_Transmit(spitxbuf, RSize, TSize);
  
  uint32_t  starttimer = bsp_gettimercnt();
  while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*2)
  {
    // do nothing
  }  
  
  MA600_SPI_Receive(spirxbuf, RSize);
}

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
uint8_t hApi_MA600_readMagAlphaRegister(uint8_t address)
{
  uint32_t timeout=10;
  uint32_t delay=1;//ms
  
  uint8_t txData1[8]={0};
  uint8_t rxData1[8]={0};
  uint8_t txData2[8]={0};
  uint8_t rxData2[8]={0};
  
  txData1[0]=(0x2<<5)|(0x1F&address);

  MA600_SPI_TransmitReceive((uint32_t *)txData1, (uint32_t *)rxData1, 2, 2);
  

  uint32_t  starttimer = bsp_gettimercnt();
  while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*2)
  {
    // do nothing
  } 
  
  MA600_SPI_TransmitReceive((uint32_t *)txData2, (uint32_t *)rxData2, 2, 2);
  
  uint8_t registerReadbackValue = rxData2[0];
  return registerReadbackValue;
}

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
uint8_t hApi_MA600_writeMagAlphaRegister(uint8_t address, uint8_t value)    
{

  uint32_t timeout=10;
  uint32_t delay=20;//ms

  uint8_t txData1[8]={0};
  uint8_t rxData1[8]={0};
  uint8_t txData2[8]={0};
  uint8_t rxData2[8]={0};

  txData1[0]=(0x4<<5)|(0x1F&address);
  txData1[1]=value;


  uint8_t registerReadbackValue;


  MA600_SPI_TransmitReceive((uint32_t *)txData1, (uint32_t *)rxData1, 2, 2);

  uint32_t  starttimer = bsp_gettimercnt();
  while(bsp_gettimercnt() - starttimer < CMTW1_PPR_MS*2)
  {
    // do nothing
  } 
  
  MA600_SPI_TransmitReceive((uint32_t *)txData2, (uint32_t *)rxData2, 2, 2);


  registerReadbackValue=rxData2[0];

  return registerReadbackValue;
}

/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
uint8_t  hApi_MA600_readMagAlphaAngleWithParityBitCheck(uint32_t *pdata)
{
  uint8_t result = 0;
  uint8_t txData[8]={0};
  uint64_t data = MA600_READ_MultiTurn;

  result = MA600_SPI_Receive(pdata,2);

  
  MA600_SPI_Transmit((UINT32 *)&data, 2, 2);
  
  
  return 0;
}
/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
uint8_t hApi_MA600_Init(void)
{  
  uint8_t regAddress=0; 
  uint8_t regValue =0x80; 
  uint8_t readbackRegValue; 

  bsp_spi3_init(SPI3_AM600_MODE);
   
  //Read the initial register value
  readbackRegValue =  hApi_MA600_readMagAlphaRegister(regAddress); 
  //write the register with the desired value
  readbackRegValue = hApi_MA600_writeMagAlphaRegister(regAddress, regValue);
  
  return 0;
}


/****************************************************************************************************
 * DESCRIPTION:
 *        
 * RETURNS:
 *
****************************************************************************************************/
uint8_t hApi_MA600_PosDecoding(ENCODER *pEnc,ENCODER *pEnc2)
{
  uint8_t parity;
  uint8_t CRC_Read = 0;
  uint16_t angleSensor;
  uint8_t result = 0;

  MA600_Angle.multiTurn = 0;    
  MA600_Angle.singleTurn = pEnc->V.AbsRecvBuf[7]<<8 | pEnc->V.AbsRecvBuf[6];
  parity = ((pEnc->V.AbsRecvBuf[5] & 0x80) >> 7);  
  angleSensor = MA600_Angle.singleTurn;

  CRC_Read = ParityCheck(angleSensor,parity);

  if(CRC_Read)
  {
    pEnc->V.SingleTurn = MA600_Angle.singleTurn;
    pEnc->V.MultiTurn = MA600_Angle.multiTurn;
    pEnc->V.AbsAlmCode = MA600_Angle.AlmCode;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}