 /****************************************************************************************************
 *
 * FILE NAME:  GainChange.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.24
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	24-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _GAIN_CHANGE_H_
#define _GAIN_CHANGE_H_

#include "GselHandler.h"
//#include "MfcHandler.h"
#include "BaseLoops.h"

/****************************************************************************************************/
/*		DEFINES																						*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Gain Select Data Definition																	*/
/*--------------------------------------------------------------------------------------------------*/
#define	SVCDEF_NETGSEL_SETNUM	2				// Definition of the number of banks that support gain switching
#define	GAIN1					0x400000		// For effective gain monitor							
#define	GAIN2					0x800000		// For effective gain monitor					



/****************************************************************************************************/
/*		PROTOTYPE																					*/
/****************************************************************************************************/
PUBLIC void GselInitServoGainChange( GAIN_CHNG_HNDL *hGainChg );
PUBLIC INT32 GselDetGainChngTiming( BOOL CoinSignal, BOOL NearSignal, BOOL RefZSignal, INT32 Condition );
PUBLIC void GselManualGainChange( BASE_CTRL *BaseControls );				
PUBLIC void GselCalculatePrm( GAIN_CHNG_HNDL *hGainChg, UINT16 gnswwait1, UINT16 gnswwait2, INT32 ScanTime );
PUBLIC BOOL GselGet_AtGselStatus( GAIN_CHNG_HNDL *hGainChg );


/*--------------------------------------------------------------------------------------------------*/
/*		Auto Gain Change (AutoGainChange.c) 														*/
/*--------------------------------------------------------------------------------------------------*/
void	GselInitAutoGainChange( ATGSEL *pAtGsel, 		/* Initialize Auto Gain Change				*/
			GSELGAINS 	*prevGain,						/* prevGain 								*/
			GSELGAINS 	*nextGain,						/* nextGain 								*/
			GSELDOBS	*prevGainDobs,					/* prevGain(Dobs) 							*/
			GSELDOBS	*nextGainDobs );				/* nextGain(Dobs) 							*/
/*--------------------------------------------------------------------------------------------------*/
void	GselAutoGainChange( 							/* Auto Gain Change Main Function			*/
			BASE_CTRL  *BaseControls, 					/*											*/
			INT32 trigger );								/*											*/
/*--------------------------------------------------------------------------------------------------*/
void	GselRstAutoGainChange( 							/* Reset Auto Gain Change 					*/
			GAIN_CHNG_HNDL	*pGainChange );				/*											*/
/*--------------------------------------------------------------------------------------------------*/
void	PcalAutoGainChange( ATGSEL *pAtGsel, 			/* Auto Gain Change Parameter Calculation	*/
				INT32 swtime, 							/* Pnxxx/Pnxxx[ms] 							*/
				INT32 dlytime, 							/* Pnxxx/Pnxxx[ms] 							*/
				INT32 svcycleus );						/* Execute Cycle [us]						*/
/*--------------------------------------------------------------------------------------------------*/
void	PcalAtGseldKpSet( 								/* Kp gain calculation for automatic gain switching*/
				ATGSEL *pAtGsel, 
				GSELGAINS *dstGain );
void	PcalAtGseldKviSet( 								/* Kv and Kvi gain calculation at automatic gain switching*/
				ATGSEL *pAtGsel, 
				GSELGAINS *dstGain );
void	PcalAtGseldKlpfSet( 							/* Klpf gain calculation for automatic gain switching*/
				ATGSEL *pAtGsel, 
				GSELGAINS *dstGain );
void	PcalAtGseldKsSet( 								/* Gain calculation of Ks and Ksj at automatic gain switching*/
				ATGSEL *pAtGsel, 
				GSELDOBS *dstGain );



#endif

