/* generated configuration header file - do not edit */
#ifndef BSP_MCU_FAMILY_CFG_H_
#define BSP_MCU_FAMILY_CFG_H_
#include "bsp_mcu_device_pn_cfg.h"
            #include "bsp_mcu_device_cfg.h"
            #include "../../../rzn/fsp/src/bsp/mcu/rzn2l/bsp_mcu_info.h"
            #include "bsp_clock_cfg.h"
            #define BSP_MCU_GROUP_RZN2L (1)
            #define BSP_LOCO_HZ (240000)
            #define BSP_GLOBAL_SYSTEM_COUNTER_CLOCK_HZ  (25000000)
            #define BSP_CORTEX_VECTOR_TABLE_ENTRIES (32)
            #define BSP_VECTOR_TABLE_MAX_ENTRIES (448)
#endif /* BSP_MCU_FAMILY_CFG_H_ */
