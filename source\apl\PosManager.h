/****************************************************************************************************
 *
 * FILE NAME:  PosManager.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.05.28
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	28-05-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _POS_MANAGER_H_
#define _POS_MANAGER_H_

#include "BaseLoops.h"

/****************************************************************************************************/
/*		DEFINES																						*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Coin Signal Output Select Definition														*/
/*--------------------------------------------------------------------------------------------------*/
#define POSERR			0x00						// Position deviation / COIN output
#define POSERR_REFOUT	0x01						// Position deviation and position command filter output / COIN output
#define POSERR_REFIN	0x02						// Position deviation and position command input / COIN output


/****************************************************************************************************/
/*		PROTOTYPE																					*/
/****************************************************************************************************/
PUBLIC void PosMngInitPositionManager( BASE_CTRL *pBaseCtrl );
PUBLIC INT32 PosMngNetPosCmdManager( BASE_LOOP *BaseLoops, BPRMDAT *Bprm );
//PUBLIC void PosMngSenOnReCalcAbsoPos(MENCV *MencV, APOSRG *EncPos,
 //   	                         EGEAR *Egear, DBYTEX bit_dp, UINT16 limmlt, BOOL RvsDir );
//PUBLIC void PosMngSenOnReCalcAbsoScalePos(MENCV *MencV, APOSRG *EncPos, EGEAR *Egear, BOOL RvsDir);
PUBLIC void PosMngLatchNetFbkPos( BASE_CTRL *pBaseCtrl, SERVO_CONTROL_IF *SvControlIf, BPRMDAT *Bprm );

PUBLIC void PosMngResponseManager( BASE_CTRL *BaseControls );
PUBLIC void PosMngClrPosCmd( BASE_LOOP *BaseLoops);

PUBLIC void StlgInitSettlingTime( SETTLINGTIME *SettlingTime, INT32 CycleTimeUs );
PUBLIC void StlgCalculateSettlingTime( SETTLINGTIME *SettlingTime, INT32 dPcmda, BOOL CoinSts );
PUBLIC void StlgMakeMaxOverShoot( SETTLINGTIME *SettlingTime, POS_MNG_HNDL *PosManager, BOOL PosCtrlMode, INT32 PosErr );
PUBLIC void StlgMakeCoinOffTime( SETTLINGTIME *SettlingTime, INT32 dPcmda, BOOL CoinSts, BOOL PosCtrlMode );
PUBLIC void StlgCalculateOverShootErrLevel( SETTLINGTIME *SettlingTime, INT32 CoinLevel, INT32 ovserrdetlvl );


/****************************************************************************************************/
/*		API Function																				*/
/****************************************************************************************************/
PUBLIC void BpiEgearFuncSwitch( POS_MNG_HNDL *PosManager, BOOL Switch );
PUBLIC void BpiPcmdFilSwitch( POS_MNG_HNDL *PosManager, BOOL Switch );
PUBLIC void BpiRsetMaxOverShoot( SETTLINGTIME *SettlingTime );
PUBLIC void BpiRsetCoinOffTime( SETTLINGTIME *SettlingTime );
PUBLIC INT32 BpiGetCoinOffTime( SETTLINGTIME *SettlingTime );
PUBLIC INT32 BpiGetMaxOverShoot( SETTLINGTIME *SettlingTime );
PUBLIC INT32 BpiGetCoinLatchSts( SETTLINGTIME *SettlingTime );
PUBLIC INT32 BpiGetSettlingTime( SETTLINGTIME *SettlingTime );


#endif //_POS_MANAGER_H_

