#include "HardApi.h"
#include "Mlib.h"
#include "bsp.h"
#include "PowerManager.h"
#include "Global.h"
#include "LostPhaseCheck.h"
#include "BaseSetting.h"

#define PHASELACK_CHECK_PRETIME  40
#define PHASELACK_CHECK_TIME     48

LP_SEQ_DATA PhaseLackCheck[MAX_AXIS_NUM];



BOOL LostPhaseCheckParaInit(UINT16 AxisID )
{
    MlibResetLongMemory( &(PhaseLackCheck[AxisID]), sizeof(LP_SEQ_DATA)/4 ); 
    
    return 0;
}


BOOL LostPhaseCheck1Ms(UINT16 AxisID )
{
    UINT16 pwmVal;
    
    if(PhaseLackCheck[AxisID].CheckCnt < PHASELACK_CHECK_PRETIME)
    {
        hApi_PhaCheckPumpOn(AxisID);
        pwmVal = PWM_PERIOD_VALUE*5/6;
        hApi_PwmUpdate(AxisID, pwmVal, pwmVal, pwmVal);				
    }
    else if(PhaseLackCheck[AxisID].CheckCnt < PHASELACK_CHECK_TIME)
    {
        PhaseLackCheck[AxisID].sumlost[0] += bsp_dsmif_read(AxisID,0);
        PhaseLackCheck[AxisID].sumlost[1] += bsp_dsmif_read(AxisID,1); 
    }
    else
    {
        hApi_PhaCheckPumpOff(AxisID);
        pwmVal = PWM_PERIOD_VALUE/2;
        hApi_PwmUpdate(AxisID, pwmVal, pwmVal, pwmVal); 
    
        PhaseLackCheck[AxisID].UVWCR[0] = PhaseLackCheck[AxisID].sumlost[0]/8;
        PhaseLackCheck[AxisID].UVWCR[1] = PhaseLackCheck[AxisID].sumlost[1]/8;
    
        if((PhaseLackCheck[AxisID].UVWCR[0] > 36768) || (PhaseLackCheck[AxisID].UVWCR[0] <(28768))
        || (PhaseLackCheck[AxisID].UVWCR[1] > 36768) || (PhaseLackCheck[AxisID].UVWCR[1] <(28768)))
        {
            PhaseLackCheck[AxisID].LostPhaseCheckErr = 1;
        }                           
        PhaseLackCheck[AxisID].LostPhaseCheckSW = TRUE;			
    }
    
    PhaseLackCheck[AxisID].CheckCnt++;


    return PhaseLackCheck[AxisID].LostPhaseCheckSW;
}



UINT16 hApi_GetPhaseState(UINT16 AxisID)
{
    return (PhaseLackCheck[AxisID].LostPhaseCheckErr);
}


UINT16 LostPhaseFlagRest(UINT16 AxisID)
{
    PhaseLackCheck[AxisID].LostPhaseCheckErr = 0;
    PhaseLackCheck[AxisID].LostPhaseCheckSW  = 0;
    
    return 0;
}
