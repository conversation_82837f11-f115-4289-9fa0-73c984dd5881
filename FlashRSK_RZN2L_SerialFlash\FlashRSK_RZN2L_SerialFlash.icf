/*******************************************************************************
								Copyright 2022 IAR Systems. All rights reserved.
Function:RZ/N2L(Cortex-R52) SPI Flash loader Linker settings.

Note:

History:
 No  Rev  Date       Name			Note
---+-----+----------+-------------+--------------------------------------------
000 01.00 2022/07/29 S.Tonoshita	New Development
*******************************************************************************/
define symbol INTVEC_start = 0x10000000;
define symbol RAM_start    = 0x10001000;
define symbol RAM_end      = 0x1017FFFF;
define symbol CSTACK_size  = 0x200;

define memory mem with size = 4G;
define region RAM_region= mem:[from RAM_start to RAM_end ];

define block CSTACK    with alignment = 8, size = CSTACK_size   { };

do not initialize  { section .noinit };
do not initialize  { readwrite };

place at address mem:INTVEC_start { readonly section .intvec };

place at start of RAM_region { block RamTop with fixed order {readonly, section LOWEND}};
place at end of RAM_region   { block RamBottom with fixed order {section HIGHSTART, readwrite,
                               section .noinit, block CSTACK}};
