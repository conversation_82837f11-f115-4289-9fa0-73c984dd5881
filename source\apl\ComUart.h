/****************************************************************************************************
 *
 * FILE NAME:  ComUart.h
 *
 * DESCRIPTION:  
 *
 * CREATED ON:  2019.06.27
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	27-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _COM_UART_H_
#define _COM_UART_H_

#include "BaseDef.h"
//#include "usart.h"
#include "Global.h"
#include "DataTraceManager.h"


#define NO_ERROR      0x4B4F  // OK
#define ERROR_LEN     0x4C45  // EL
#define ERROR_INDEX   0x4945  // EI
#define ERROR_CRC     0x4345  // EC

#define MAX_CHANNEL_NUM  4
#define MAX_COLLECT_DATA_NUM     24576
#define MAX_COLLECT_BUFFER_NUM   49152

#define UART_RECV_MAX_NUM  1024
#define UART_SEND_MAX_NUM  1024

#define NO_TRIGGER             0
#define RISING_EDGE_TRIGGER    1
#define FALLING_EDGE_TRIGGER   2
#define DOUBLE_EDGE_TRIGGER    3

#define SAMPLE_STATE_WAIT      0
#define SAMPLE_STATE_RUN       1
#define SAMPLE_STATE_DONE      2
#define SAMPLE_STATE_ERROR     3


#define COM_CRC_OFFSET 
#define COM_END_OFFSET


#define USEUSB  0


typedef struct
{   
    UINT8           StartFlag;  
    UINT8           CommandLenLow;
    UINT8           CommandLenHigh;     
    UINT8           DeviceAddr;   
    UINT8           FunctionCode;     
    unsigned char   Buf[UART_RECV_MAX_NUM - 5];
}ComUartStruct;


typedef struct
{   
    uint8_t           rx_statu;  
    uint8_t           tx_statu;  
    uint32_t          rx_bytecount;
    uint32_t          tx_bytecount;
    ComUartStruct     CommandRecv;
    ComUartStruct     CommandSend;
    ComUartStruct    CommandSendLast;
    
}HmiStruct;

extern HmiStruct gHmiHandle;

typedef struct
{   
    UINT16          Index;  
    CHAR            *pName;
    UINT16          Length;
}VersionStruct;

extern  VersionStruct  VersionTbl[];
extern const UINT32  VersionTblNum;



extern PUBLIC void UartAppInit(void);  
extern PUBLIC void UartRecvDispatch(void);

extern PUBLIC void DataCollectSendLoop(AXIS_HANDLE *AxisA,HmiStruct *pHmiHandle);
extern PUBLIC void UartSendData(void);

extern BOOL RoserBusTransmit(ComUartStruct *Receive,ComUartStruct *Send,UINT32 DevId);
extern PUBLIC UINT16 GetCRC16(UINT8 *buf, UINT16 length);

#endif  // _COM_UART_H_
