/****************************************************************************************************
 *
 * FILE NAME:  PcmdFilter.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.05.26
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	26-05-2020 Version 1.00 : Created by Denny
****************************************************************************************************/

#include "PcmdFilter.h"

PRIVATE void pcmdFilExpFilterPrmChange( EXPFIL *ExpFil, INT32 PcmdIn, INT32 FilRest );
PRIVATE INT32 pcmdFilExpFilter( EXPFIL *ExpFil, INT32 PcmdIn );
PRIVATE INT32 pcmdFilLongMAFilter( MAFIL *MaFil, INT32 PcmdIn );
PRIVATE INT32 pcmdFilMoveAverageFilter( MAFIL *MaFil, INT32 PcmdIn );
PRIVATE INT32 pcmdFilHighFilter( MAFIL *MaFil, INT32 PcmdIn );
PRIVATE INT32 pcmdFilLongHFilter( MAFIL *MaFil, INT32 PcmdIn );



/****************************************************************************************************
 * DESCRIPTION:
 *	 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcmdFilInitialize( PCMDFIL *PcmdFil, VIBSUPFIL *VibSupFil )
{

	MlibResetLongMemory( &(PcmdFil->ExpFil.var), sizeof(PcmdFil->ExpFil.var)/4 );
	PcmdFil->ExpFil.conf.Kf[0] = PcmdFil->ExpFil.conf.Kf[1];
	PcmdFil->ExpFil.conf.Pbias[0] = PcmdFil->ExpFil.conf.Pbias[1];

	MlibResetLongMemory( &(PcmdFil->MaFil.var), sizeof(PcmdFil->MaFil.var)/4 );
	PcmdFil->MaFil.conf.PmafNum[0] = PcmdFil->MaFil.conf.PmafNum[1];
	PcmdFil->MaFil.conf.ImafSft[0] = PcmdFil->MaFil.conf.ImafSft[1];

	MlibResetLongMemory( &(PcmdFil->MaFil.var), sizeof(PcmdFil->MaFil.var)/4 );
	PcmdFil->MaFil.conf.PmafNumSec[0] = PcmdFil->MaFil.conf.PmafNumSec[1];
	PcmdFil->MaFil.conf.ImafSftSec[0] = PcmdFil->MaFil.conf.ImafSftSec[1];

	MlibResetLongMemory( &VibSupFil->var, sizeof(VibSupFil->var)/4 );
	VibSupFil->conf.Pexe = VibSupFil->conf.VibSupPrm;

	return;
}

/****************************************************************************************************
 * DESCRIPTION:
 *	  
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void FFCmdFilInitialize( MAFIL *pMaFil )
{
	MlibResetLongMemory( &(pMaFil->var), sizeof(pMaFil->var)/4 );
	pMaFil->conf.PmafNum[0] = pMaFil->conf.PmafNum[1];
	pMaFil->conf.ImafSft[0] = pMaFil->conf.ImafSft[1];

	return;
}

/****************************************************************************************************
 * DESCRIPTION:
 *	  Speed / Torque Feedforward Command moving average filter 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 FFcmdFilMoveAverageFilter( MAFIL *pMaFil, INT32 FFcmdIn, BOOL FilStop )
{
	INT32 SaveFFcmd;

	SaveFFcmd = FFcmdIn;

	/* 2^24 -> 15000 */
	FFcmdIn = MlibMulhigh32( FFcmdIn, 3840000 );

	if( pMaFil->conf.ImafSft[0] == 0 )
	{
		pMaFil->var.Filo = MlibPcmdMafil( FFcmdIn,
										 pMaFil->conf.PmafNum[0],
										 &pMaFil->var.Pmafvar,
										 &pMaFil->var.Pmafbuf[0] );
	}
	else
	{
		pMaFil->var.Filo = pcmdFilLongMAFilter( pMaFil, FFcmdIn );
	}

	/* 15000 -> 2^24 */
	pMaFil->var.Filo = MlibMulhigh32( (pMaFil->var.Filo<<16), 73300775 );	

	if ( FilStop == TRUE )
	{	
		pMaFil->var.Filo = SaveFFcmd;	
	}
	
	return( pMaFil->var.Filo );

}

/****************************************************************************************************
 * DESCRIPTION:
 *      
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC INT32 PcmdFilRuntimeService( PCMDFIL *PcmdFil, INT32 PcmdIn, INT32 FilRest, BOOL FilStop )
{
	INT32	PcmdOut;
	INT32	OrgPcmdIn;

	if ( FilStop == TRUE )
	{
		OrgPcmdIn = PcmdIn;
		PcmdIn = 0;
	}
	else
	{
		OrgPcmdIn = 0;
	}

	PcmdOut = pcmdFilMoveAverageFilter( &(PcmdFil->MaFil), PcmdIn );

	PcmdOut = pcmdFilHighFilter( &(PcmdFil->MaFil), PcmdOut );

//	pcmdFilExpFilterPrmChange( &(PcmdFil->ExpFil), PcmdOut, FilRest );
//	PcmdOut = pcmdFilExpFilter( &(PcmdFil->ExpFil), PcmdOut );

	PcmdOut += OrgPcmdIn;

	return( PcmdOut );
}



/****************************************************************************************************
 * DESCRIPTION:
 *    
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void pcmdFilExpFilterPrmChange( EXPFIL *ExpFil, INT32 PcmdIn, INT32 FilRest )
{

	if ( (PcmdIn == 0) && (FilRest == 0) )
	{													
		ExpFil->conf.Kf[0] = ExpFil->conf.Kf[1];		
		ExpFil->conf.Pbias[0] = ExpFil->conf.Pbias[1];	
		if( ExpFil->conf.Kf[0] == 1 )					
		{
			ExpFil->conf.Pbias[0] = 0;
		}
	}

	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *    
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 pcmdFilExpFilter( EXPFIL *ExpFil, INT32 PcmdIn )
{

	ExpFil->var.Pexfvar.sumx = ExpFil->var.Pexfvar.sumx + PcmdIn;
	if ( MlibABS( ExpFil->var.Pexfvar.sumx ) > ExpFil->conf.Pbias[0] )
	{
		ExpFil->var.Filo = MlibMulkxBiasrem( ExpFil->var.Pexfvar.sumx,
											 ExpFil->conf.Kf[0],
											 &ExpFil->var.Pexfvar.remx,
											 ExpFil->conf.Pbias[0] );
	}
	else
	{
		ExpFil->var.Filo = ExpFil->var.Pexfvar.sumx;
	}
	ExpFil->var.Pexfvar.sumx = ExpFil->var.Pexfvar.sumx - ExpFil->var.Filo;

	if ( ( PcmdIn != 0 ) || ( ExpFil->var.Pexfvar.sumx != 0 ) )
	{

		ExpFil->var.RefZStatus = FALSE;					
	}
	
	return( ExpFil->var.Filo );
}



/****************************************************************************************************
 * DESCRIPTION:
 *    
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 pcmdFilLongMAFilter( MAFIL *MaFil, INT32 PcmdIn )
{
	INT32	pcmdfilo;

	MaFil->var.PcmdInX += PcmdIn;
	if( ++MaFil->var.Index >= (1 << MaFil->conf.ImafSft[0]) )
	{
		MaFil->var.PcmdInY = MlibPcmdMafil( MaFil->var.PcmdInX,
											MaFil->conf.PmafNum[0],
											&MaFil->var.Pmafvar,
											&MaFil->var.Pmafbuf[0] );
		
		MaFil->var.Index   = 0;
		MaFil->var.PcmdInX = 0;
	}

	pcmdfilo = MlibPcmdImafil( MaFil->var.PcmdInY,
							   MaFil->conf.ImafSft[0],
							   MaFil->var.Index,
							   &MaFil->var.Pimfvar );

	return( pcmdfilo );

}


/****************************************************************************************************
 * DESCRIPTION:
 *    
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 pcmdFilMoveAverageFilter( MAFIL *MaFil, INT32 PcmdIn )
{

	if( MaFil->conf.ImafSft[0] == 0 )
	{

		MaFil->var.Filo = MlibPcmdMafil( PcmdIn,
										 MaFil->conf.PmafNum[0],
										 &MaFil->var.Pmafvar,
										 &MaFil->var.Pmafbuf[0] );
	}
	else
	{

		MaFil->var.Filo = pcmdFilLongMAFilter( MaFil, PcmdIn );
	}

	if( (PcmdIn | MaFil->var.PcmdInX | MaFil->var.Pmafvar.zcntx | MaFil->var.Pimfvar.zcntx) == 0 )
	{
		MaFil->conf.PmafNum[0] = MaFil->conf.PmafNum[1];	
		MaFil->conf.ImafSft[0] = MaFil->conf.ImafSft[1];	
	}
	else
	{
						
		MaFil->var.RefZStatus = FALSE;					
				
	}
	return( MaFil->var.Filo );

}

/****************************************************************************************************
 * DESCRIPTION:
 *    
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 pcmdFilHighFilter( MAFIL *MaFil, INT32 PcmdIn )
{

	if( MaFil->conf.ImafSftSec[0] == 0 )
	{
		
		MaFil->var.FiloSec = MlibPcmdMafilSec( PcmdIn,
										 MaFil->conf.PmafNumSec[0],
										 &MaFil->var.PmafvarSec,
										 &MaFil->var.PmafbufSec[0] );
	}
	else
	{

		MaFil->var.FiloSec = pcmdFilLongHFilter( MaFil, PcmdIn );
	}

	if( (PcmdIn | MaFil->var.PcmdInXSec | MaFil->var.PmafvarSec.zcntxSec | MaFil->var.PimfvarSec.zcntxSec) == 0 )
	{
		MaFil->conf.PmafNumSec[0] = MaFil->conf.PmafNumSec[1];	
		MaFil->conf.ImafSftSec[0] = MaFil->conf.ImafSftSec[1];	
	}
	else
	{

		MaFil->var.RefZStatus = FALSE;					

	}

	return( MaFil->var.FiloSec );

}


/****************************************************************************************************
 * DESCRIPTION:
 *    
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 pcmdFilLongHFilter( MAFIL *MaFil, INT32 PcmdIn )
{
	INT32	pcmdfilo;

	MaFil->var.PcmdInXSec += PcmdIn;
	if( ++MaFil->var.IndexSec >= (1 << MaFil->conf.ImafSftSec[0]) )
	{
		MaFil->var.PcmdInYSec = MlibPcmdMafilSec( MaFil->var.PcmdInXSec,
												  MaFil->conf.PmafNumSec[0],
												  &MaFil->var.PmafvarSec,
												  &MaFil->var.PmafbufSec[0] );
			MaFil->var.IndexSec   = 0;
			MaFil->var.PcmdInXSec = 0;
	}

	pcmdfilo = MlibPcmdImafilSec( MaFil->var.PcmdInYSec,
								   MaFil->conf.ImafSftSec[0],
								   MaFil->var.IndexSec,
								   &MaFil->var.PimfvarSec );

	return( pcmdfilo );

}

/****************************************************************************************************
 * DESCRIPTION:
 *    
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcmdFilCalculatePrmExpFilter( EXPFIL *ExpFil, UINT16 PrmTime, REAL32 Kmotpls,
														          REAL32 MaxVel, INT32 SvCycleUs )
{
	INT32	PrmTimeUs;
	INT32	kx,sx;


	PrmTimeUs = PrmTime * 100;

	kx = SUMX_LIM*Kmotpls;
	kx = kx/(MaxVel*1000);
	PrmTimeUs = ( kx < PrmTimeUs ) ? kx : PrmTimeUs ;

	if( PrmTimeUs <= SvCycleUs )			
	{
		ExpFil->conf.Kf[1] = 1;
	}
	else										
	{
		ExpFil->conf.Kf[1] = SvCycleUs/PrmTimeUs;
	}

	return;

}

/****************************************************************************************************
 * DESCRIPTION:
 *	  
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcmdFilCalculatePrmExpFilBias( EXPFIL *ExpFil, UINT16 expbias1, INT32 SvCycleUs )
{
	INT32	kx;
	INT32	sx;

	kx = expbias1;
	kx = kx*SvCycleUs/1000000;
//	ExpFil->conf.Pbias[1] = (sx<=31)? (kx>>sx) : 0;

	ExpFil->conf.Pbias[1] = 0;

	return;

}


/****************************************************************************************************
* DESCRIPTION:
*	  
* RETURNS:
*
****************************************************************************************************/
PUBLIC void PcmdFilCalculatePrmMoveAvergeFilter( MAFIL *MaFil, UINT16 PrmTime, UINT16 Highrad, INT32 SvCycleUs)
{
	INT32	i,x;
	INT32	MaTimeUs;

	UINT16  rad; 

	rad = Highrad>>1;
	MaTimeUs= 100*PrmTime-PrmTime*rad;

	if( MaTimeUs <= (MAFIL_BUFNUM * SvCycleUs) )
	{
		MaFil->conf.ImafSft[1] = 0;
		MaFil->conf.PmafNum[1] = MaTimeUs / SvCycleUs;
		if( MaFil->conf.PmafNum[1] < 1 ){ MaFil->conf.PmafNum[1] = 1;}
	}
	else
	{
		x = MAFIL_BUFNUM * SvCycleUs;					
		for( i=1; i < 5; i++ )								
		{													
			if( (x << i) >= MaTimeUs ){ break;}			
		}													

		MaFil->conf.ImafSft[1] = (UCHAR)i;
		MaFil->conf.PmafNum[1] = MaTimeUs / (SvCycleUs << i);
		if( MaFil->conf.PmafNum[1] > MAFIL_BUFNUM ){ MaFil->conf.PmafNum[1] = MAFIL_BUFNUM;}
	}

	return;

}

/****************************************************************************************************
* DESCRIPTION:
*	  
* RETURNS:
*
****************************************************************************************************/
void	PcmdFilCalculatePrmHighFilter( MAFIL *MaFil, UINT16 PrmTime, UINT16 Highrad, INT32 SvCycleUs)
{
	INT32	i,x;
	INT32	MaTimeUs;
	UINT16  rad;

    rad = Highrad>>1;
    MaTimeUs=  PrmTime*rad;   

    if( MaTimeUs <= (MAFIL_BUFNUM * SvCycleUs) )
    {
    	MaFil->conf.ImafSftSec[1] = 0;
    	MaFil->conf.PmafNumSec[1] = MaTimeUs / SvCycleUs;
    	if( MaFil->conf.PmafNumSec[1] < 1 ){ MaFil->conf.PmafNumSec[1] = 1;}
    }

    else
    {
    	x = MAFIL_BUFNUM * SvCycleUs;					
    	for( i=1; i < 5; i++ )								
    	{												
    		if( (x << i) >= MaTimeUs ){ break;}			
    	}													

    		MaFil->conf.ImafSftSec[1] = (UINT8)i;
    		MaFil->conf.PmafNumSec[1] = MaTimeUs / (SvCycleUs << i);
    		if( MaFil->conf.PmafNumSec[1] > MAFIL_BUFNUM ){ MaFil->conf.PmafNumSec[1] = MAFIL_BUFNUM;}
    }

    return;

}

/****************************************************************************************************
* DESCRIPTION:
*	  
* RETURNS:
*
****************************************************************************************************/
void	PcmdFilSetRefZStatus( PCMDFIL *PcmdFil, BOOL status )
{
	PcmdFil->ExpFil.var.RefZStatus = status;
	PcmdFil->MaFil.var.RefZStatus = status;
	return;
}


/****************************************************************************************************
* DESCRIPTION:
*	  
* RETURNS:
*
****************************************************************************************************/
BOOL	PcmdFilGetRefZStatus( PCMDFIL *PcmdFil )
{
	return	(PcmdFil->ExpFil.var.RefZStatus & PcmdFil->MaFil.var.RefZStatus);
}

/****************************************************************************************************
* DESCRIPTION:
*	  
* RETURNS:
*
****************************************************************************************************/
PUBLIC INT32 PcmdFilVibSupFilter( VIBSUPFIL *VibSupFil, INT32 dPcmd, BOOL *RefZSignal )
{
	INT32	AvffFili;
	INT32	AvffFilo;
	INT32	x1;
	INT32	x2;
	INT32	x3;
	INT32	wk = 0;

	if( (dPcmd == 0) && (VibSupFil->var.Buf == 0) )
	{ 
		VibSupFil->conf.Pexe = VibSupFil->conf.VibSupPrm;		
	}
	AvffFili = dPcmd;									


	if( VibSupFil->conf.Pexe.enable )
	{	
		x1 = MlibPfbkxremNolim( AvffFili, VibSupFil->conf.Pexe.Kff1, &VibSupFil->var.rem1 );
		x2 = MlibPfbkxremNolim( VibSupFil->var.wkbf1, VibSupFil->conf.Pexe.Kff2, &VibSupFil->var.rem2 );
		x3 = MlibPfbkxremNolim( VibSupFil->var.wkbf2, VibSupFil->conf.Pexe.Kff3, &VibSupFil->var.rem3 );

		VibSupFil->var.wkbf1 += (x1 - x3 - AvffFili);
		VibSupFil->var.wkbf2 += (x1 - x3 + x2);

		AvffFilo = x1 - x3;									

		VibSupFil->var.Buf = MlibPerrcalx( AvffFili, AvffFilo, VibSupFil->var.FilioErr );
		if( VibSupFil->var.Buf != 0 )
		{
			*RefZSignal = FALSE;
		}


		if( (AvffFilo == 0) && (VibSupFil->var.wkbf1 == 0) && (VibSupFil->var.wkbf2 == 0) )
		{ 
			MlibResetLongMemory( &VibSupFil->var, sizeof(VibSupFil->var)/4 );
		}


		wk += AvffFilo;											
		VibSupFil->var.Filo = wk;								
	}
	else
	{	
		wk = AvffFili;										
		VibSupFil->var.Filo = wk;
	}

	return( wk );
}


/****************************************************************************************************
* DESCRIPTION:
*	  
* RETURNS:
*
****************************************************************************************************/
BOOL	PcmdFilCalculatePrmVibSupFilSW( VIBSUPFIL *VibSupFil, UINT32 mdlsw,
													UINT32 ff_feq, UINT32 ff_fil, INT32 ScanTimeNs )
{
	BOOL	PrmSetErr;
	INT32	wk;

	PrmSetErr = FALSE;
//	wk = (mdlsw >> 4) & 0x0F;

//	switch( wk )
//	{
//	case 0x00: 
//	case 0x01:
//		VibSupFil->conf.VibSupSetting = FALSE;
//		break;
//	case 0x02: 
//		VibSupFil->conf.VibSupSetting = TRUE;
//		break;
//	default :
//		PrmSetErr = TRUE;
//		break;
//	}


//	wk = mdlsw & 0x0F;
//	switch( wk )
//	{
//	case 0x00: 

//		VibSupFil->conf.VibSupSetting = FALSE;
//		break;
//	}

    VibSupFil->conf.VibSupSetting = mdlsw;

	PcmdFilCalculatePrmVibSupFil( VibSupFil, ff_feq, ff_fil, ScanTimeNs );

	return	PrmSetErr;
}




/****************************************************************************************************
* DESCRIPTION:
*	  
* RETURNS:
*
****************************************************************************************************/
PUBLIC void PcmdFilCalculatePrmVibSupFil( VIBSUPFIL *VibSupFil, UINT32 ff_frq, UINT32 ff_fil, INT32 ScanTimeNs )
{
	VIBSUPFILPRM	wrkp;
	INT32			u1;
	INT32			s, sx;
	INT32			kx;
	INT32			wk;
	INT32			omega2;

	wrkp.enable = VibSupFil->conf.VibSupSetting;
	u1 = ff_fil;

/*--------------------------------------------------------------------------------------------------*/
/*																									*/
/*					      (ff_frq/10)[Hz] * (ff_fil[%]/100) * (80/100)* 10^9						*/
/*			omega2_limit = --------------------------------------------------------					*/
/*				   		               2 * PAI * CycleNs											*/
/*--------------------------------------------------------------------------------------------------*/
	wk = (INT32)ff_frq * (INT32)ff_fil;				
	omega2 = MlibMIN( wk, (MAXOMEGA2(ScanTimeNs) * 1000) );	


/*--------------------------------------------------------------------------------------------------*/
/*					      ff_fil^2																	*/
/*				Kff1 = --------------																*/
/*				   		   10000																	*/
/*--------------------------------------------------------------------------------------------------*/
//	wrkp.Kff1 = (((INT64)(u1 * u1))<<24)/(INT64)10000;
	wrkp.Kff1 = MlibScalKxgain( u1 * u1, 1, 10000, NULL, 24 );



/*--------------------------------------------------------------------------------------------------*/
/*					     PAI * omega2 * CycleNs														*/
/*				Kff2 = --------------------------													*/
/*				   		       1000 * 10^9															*/
/*--------------------------------------------------------------------------------------------------*/
/*					     4*PAI * omega2 * CycleNs													*/
/*				Kff3 = -----------------------------												*/
/*				   		       1000 * 10^9															*/
/*--------------------------------------------------------------------------------------------------*/

//	kx =omega2*ScanTimeNs/C10POW9;
//	kx = kx/1000000000;

//	wrkp.Kff2 = kx*3141593;			                                 /* 3141593  =   PAI*1000000	*/
//	wrkp.Kff3 = kx*12566371;			                             /* 12566371 = 4*PAI*1000000	*/

//	kx =(INT64)omega2*(INT64)ScanTimeUs*(INT64)3141593/(INT64)C10POW9;

//	wrkp.Kff2 = ((INT64)kx<<24) / 1000000;
//	wrkp.Kff3 = 4*wrkp.Kff2;

	kx = MlibScalKxgain( omega2, ScanTimeNs, C10POW9, &s, 0 );
	kx = MlibPcalKxgain( kx, 1, 1000000000, &s, 0 );
	sx = s;

	wrkp.Kff2 = MlibPcalKxgain( kx, 3141593, 1, &s, 24 );				/* 3141593  =   PAI*1000000	*/
	wrkp.Kff3 = MlibPcalKxgain( kx, 12566371, 1, &sx , 24 );			/* 12566371 = 4*PAI*1000000	*/

	VibSupFil->conf.VibSupPrm = wrkp;

}


