/****************************************************************************************************
 *
 * FILE NAME:  FieldWeakening.c
 *
 * DESCRIPTION: Weakness field control parameter calculation module
 *
 * CREATED ON:  2020.11.26
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	26-11-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseDef.h"
#include "MicroPrmCal.h"
#include "Mlib.h"


/****************************************************************************************************
 * DESCRIPTION:
 *	     Calculation of parameters for weak field control ( Execution period : 250us )
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcmWFPrmCalc( WEAKENFIELD *wf_ptr, INT32 MotSpd, INT32 DcVolt)
{
	REAL32	work1;
	REAL32	work2;
	REAL32	SpdInv;
	REAL32	SpdInv_2;

/*--------------------------------------------------------------------------------------------------*/
/*		Average value calculation of main circuit voltage[V]									    */
/*--------------------------------------------------------------------------------------------------*/
	wf_ptr->var.VdcSum += DcVolt;
	if ( ++wf_ptr->var.VdcSumCnt >= 8 )
	{
		work1 = wf_ptr->var.VdcSum / wf_ptr->var.VdcSumCnt;
		
		wf_ptr->var.VdcAsic = work1/1.732f;
		wf_ptr->var.VdcSum = 0.0f;
		wf_ptr->var.VdcSumCnt = 0;
	}

/*--------------------------------------------------------------------------------------------------*/
/*		1/vel  1/vel^2 calculation																	*/
/*--------------------------------------------------------------------------------------------------*/
	/* The normalization of the speed is set to 2 ^ 24 -> 15000*/ 
	work1 = MlibABS( MotSpd );
	work1 = 15000.0f* (work1/NOR_MAXVAL_FLOAT);
    
	/* Speed judgment */
	if ( work1 < 1000.0f )
	{
		/* 1000 or less when calculating at 1000.*/
		work1 = 1000.0f;
	}

	SpdInv = 1.0f / work1;			/* SpdInv =  1/vel */
	SpdInv_2 = SpdInv * SpdInv;		/* SpdInv_2 = 1/vel^2 */

	// Voltage feedback loop gain calculation											
	wf_ptr->var.Kpv = SpdInv* wf_ptr->conf.KpvBeforeDiv;

	// Voltage feedback loop integral gain calculation								
	wf_ptr->var.Kiv = SpdInv * wf_ptr->conf.KivBeforeDiv;
    
/*--------------------------------------------------------------------------------------------------*/
/*		D axis current command limit calculation													*/
/*--------------------------------------------------------------------------------------------------*/
	/* D axis current command limit Term1 calculation */
	work1 = wf_ptr->var.VdcAsic * wf_ptr->var.VdcAsic;
	work2 = SpdInv_2 * wf_ptr->conf.IdrefLimTerm1;
	work2 = work1 * work2;

	/* D axis current command limit Term4 calculation */
	work1 = SpdInv * wf_ptr->conf.IdrefLimTerm4;

	/* D axis current command limit value calculation(Term1-Term23-Term4) */
	work2 = work2 - wf_ptr->conf.IdrefLimTerm23 - work1;

	if ( work2 < 0 )
	{
        if(work2 < wf_ptr->conf.IdrefMin)
        {
            work2 = wf_ptr->conf.IdrefMin;
        }

        wf_ptr->var.IqrefLim = SpdInv*wf_ptr->var.VdcAsic*wf_ptr->conf.IqrefLimTerm;
        
		if( work2 < -1.0f )
		{
			wf_ptr->var.IdrefLim = -1.0f;
		}
		else
		{
			wf_ptr->var.IdrefLim = work2;
		}
	}
	else
	{
		wf_ptr->var.IdrefLim = 0;
        wf_ptr->var.IqrefLim = 1.0f;
	}
}



/****************************************************************************************************/
/*																									*/
/*		Calculation of constant for voltage feedback loop proportional gain calculation				*/
/*																									*/
/****************************************************************************************************/
/*			         2*PI * Kpv * Tiv *10^-6														*/
/*		Kpv(Asic) = ------------------------														*/
/*					          wL																	*/
/*			  2PI * 10^-6 		15000	  1.0				 1										*/
/*		   = ---------------- * ------- * ------- * Kpv * Tiv * ----- 								*/
/*			  pole * Lx 	    Nmax	  Imax				wm										*/
/*			Calculates the portion except for wm. 													*/
/*																									*/
/*		1 / wm computes and multiplies 2 ^ 24 / wm in scanb.										*/
/*																									*/
/*		Kpv     [Hz] : Voltage feedback loop gain													*/
/*		Tiv	    [us] : Voltage feedback loop integral time constant									*/
/*		Lx      [H] : Motor inductance																*/
/*		pole    [-] : Motro pole																	*/
/*		Namx [rad/s] : Over speed																	*/
/*		wm [15000/Nmax] : Motor Speed																*/
/*																									*/
/****************************************************************************************************/
PUBLIC void PcalVFBKp( WEAKENFIELD *WeakenField, BPRMDAT *Bprm, UINT16 kv, UINT16 tv )
{

	REAL32	fw;

	fw = 2*PI / (REAL32)(Bprm->MotPolePairNum);
	fw = fw * (REAL32)kv;
	fw = fw * (REAL32)tv / Bprm->MotLd;
	fw = fw *Bprm->Vdc/ 1732050.8F;    // 1.7320508*1000000.0f
	fw = fw * 15000.0F / Bprm->OvrSpd;
	fw = fw * 1.0F / Bprm->MaxCur;
	WeakenField->conf.KpvBeforeDiv = fw;

}



/****************************************************************************************************/
/*																									*/
/*		Calculation of voltage feedback loop integral gain											*/
/*																									*/
/****************************************************************************************************/
/*																									*/
/*			   		     Ts	    2PI * Gain * Tv*10^-6	   Ts										*/
/*		Kiv(Asic) = Kpv * ------ = ----------------------- * -----									*/
/*					     Tiv      		 wL			       Tiv										*/
/*			  2PI * Ts*10^-9	 15000	 1.0			 1											*/
/*		   = ---------------- * ------- * ------- * Kpv * ----- 									*/
/*			    pole * Lx 	    Nmax	 Imax			wm											*/
/*			Calculates the portion except for wm. 													*/
/*			1/wm performs in scanb																	*/
/*																									*/
/*		1 / wm computes and multiplies 2 ^ 24 /wm in scanb.											*/
/*																									*/
/*		Kpv     [Hz] : Voltage feedback loop gain													*/
/*		Tiv	    [us] : Voltage feedback loop integral time constant									*/
/*		Lx       [H] : Motor inductance																*/
/*		pole     [-] : Motor pole																	*/
/*		Namx [rad/s] : Over speed																	*/
/*		?m [15000/Nmax] : Motor Speed																*/
/*																									*/
/****************************************************************************************************/
PUBLIC void PcalVFBKi( WEAKENFIELD *WeakenField, BPRMDAT *Bprm, UINT16 kv)
{

	float	fw;

	fw = 2*PI / (REAL32)(Bprm->MotPolePairNum);
	fw = fw * (REAL32)kv / Bprm->MotLd;
	fw = fw *Bprm->Vdc/1.7320508f;;
	fw = fw * (REAL32)CUR_CYCLENS / 1000000000.0F;
	fw = fw * 15000.0F / Bprm->OvrSpd;
	fw = fw * 1.0F / Bprm->MaxCur;

	WeakenField->conf.KivBeforeDiv = fw;

}




/****************************************************************************************************/
/*																									*/
/*		Calculation of constant for d-axis current command limit value calculation					*/
/*		(One item calculation)																		*/
/*																									*/
/****************************************************************************************************/
/*		All calculations are not effective values, but 0-P are considered							*/
/*																									*/
/*				  Vmax^2 - (Kewm)^2 - (wL)^2*Imax^2	    R											*/
/*		Id*lim = ------------------------------------- - -----*Imax 								*/
/*						   2 * wL * Ke*wm			    wL											*/
/*			Calculate Term1																			*/
/*																									*/
/*		Vmax = Vdc(Actual) * Prm.v1max / 100 * Prm.vmaxid / 100 / 2 [Vo-p]							*/
/*		Prm.v1max	Pnxxx[%]		115																*/
/*		Prm.vmaxid	Pnxxx[%]		95																*/
/*																									*/
/*				 (Prm.v1max * Prm.vmaxid)^2 	      1												*/
/*		Term1 = ---------------------------------- * ------											*/
/*				 4*sqrt(2) * 10^8 * pole * Lx * Emf	wm^2											*/
/*			Calculate the portion except for wm ^ 2 												*/
/*																									*/
/*									 (Prm.v1max * Prm.vmaxid)^2	   1.0	     15000^2				*/
/*		WeakenField.IdrefLimTerm1 = ------------------------------*--------- * ---------- 			*/
/*									 4*sqrt(2) * pole * Lx * Emf   Imax	   	 Nmax^2					*/
/*																									*/
/*		1 / wm computes and multiplies 2 ^ 24 / wm in scanb.										*/
/*																									*/
/*		Vdc  [V] 		  : DC Voltage																*/
/*		Emf  [Vrms/rad/s] : EMF Constant															*/
/*		Lx   [H]          : Inductance																*/
/*		Rx   [ohm] 		  : Resistance																*/
/*		Imax [Ao-p]       : Max. Current															*/
/*		Nmax [rad/s]      : Max. Speed																*/
/*		pole [-]          : pole No.																*/
/*		wm  [15000/Nmax] : Motor Speed																*/
/*																									*/
/****************************************************************************************************/
PUBLIC void PcalIdrefLimTerm1( WEAKENFIELD *WeakenField,  BPRMDAT *Bprm, REAL32 v1max, REAL32 vmaxid)
{
	REAL32		kx,sx;
	REAL32		Vdc;
	REAL32		Keangle;
	REAL32		MotLd;
	REAL32		MotEmf;
	REAL32		MaxCur;
	REAL32		OvrSpd;

	MotLd		= Bprm->MotLd;
	MotEmf		= Bprm->MotEmf;   
	MaxCur		= Bprm->MaxCur;
	OvrSpd		= Bprm->OvrSpd;

	kx = v1max * v1max/10000.0f;  
	kx = kx * vmaxid * vmaxid/10000.0f;

	kx = kx/(2.0f*Bprm->MotPolePairNum);          
	kx = kx/MotLd;
	kx = kx/MotEmf;

	kx = kx * 1.0f/MaxCur;
	kx = kx * 15000.0f/OvrSpd;
	kx = kx * 15000.0f/OvrSpd;

	WeakenField->conf.IdrefLimTerm1 = kx;

	WeakenField->conf.V1Max = Bprm->Vdc * v1max / 100.0f;
    
    kx = Bprm->MotEmf/(Bprm->MotPolePairNum*Bprm->MotLd);
    WeakenField->conf.IdrefMin = -kx/MaxCur;
    
    kx = v1max/100.0f;
    kx = kx/Bprm->MotPolePairNum;          
	kx = kx/MotLd;
    kx = kx * 1.0f/MaxCur;
    kx = kx * 15000.0f/OvrSpd;
    
    WeakenField->conf.IqrefLimTerm = kx; 
}


/****************************************************************************************************/
/*																									*/
/*		Calculation of constant for d-axis current command limit value calculation					*/
/*		(2,3,4 items calculation)																	*/
/*																									*/
/****************************************************************************************************/
/*		All calculations are not effective values, but 0-P are considered							*/
/*																									*/
/*				  Vmax^2 - (Kewm)^2 - (wL)^2*Imax^2	    R											*/
/*		Id*lim = ------------------------------------- - -----*Imax 								*/
/*						   2 * wL * Ke*wm			    wL											*/
/*																									*/
/*			 	 sqrt(2) * Emf																		*/
/*		Term2 = -----------																			*/
/*			  	 pole * Lx 		   																	*/
/*																									*/
/*																									*/
/*			 	 pole * L * Imax^2																	*/
/*		Term3 = -------------------																	*/
/*			      sqrt(2) * 4 * Emf																	*/
/*			 										1.0												*/
/*		WeakenField.IdrefLimTerm23 = (Term2 + Term3) * -------										*/
/*			 										Imax											*/
/*																									*/
/*				 2 * Rx * Imax		1																*/
/*		Term4 = --------------- * -----																*/
/*				   pole * Lx 	   wm																*/
/*		Calculate the portion except for wm															*/
/*			 							   1.0	   15000											*/
/*		WeakenField.IdrefLimTerm4 = Term4 * -------* ------- 										*/
/*			 							  Imax	   Nmax												*/
/*																									*/
/*		1 / wm computes and multiplies 2 ^ 24 / wm in scanb.										*/
/*																									*/
/*		Emf  [Vrms/rad/s] : EMF Constant															*/
/*		Lx   [H]          : Inductance																*/
/*		Rx   [ohm] 		  : Resistance																*/
/*		Imax [Ao-p]       : Max. Current															*/
/*		Nmax [rad/s]      : Max. Speed																*/
/*		pole [-]          : pole No.																*/
/*		wm  [15000/Nmax] : Motor Speed																*/
/*																									*/
/****************************************************************************************************/
PUBLIC void PcalIdrefLimTerm234(WEAKENFIELD* weaken_ptr, BPRMDAT* Bprm)
{
	REAL32		kx,ky,kz;
	REAL32		MotEmf;
	REAL32		MotLd;
	REAL32		MaxCur;
	REAL32		MotR;
	REAL32		OvrSpd;

	MotEmf		= Bprm->MotEmf;   
	MotLd		= Bprm->MotLd;
	MaxCur		= Bprm->MaxCur;
	MotR		= Bprm->MotR;
	OvrSpd		= Bprm->OvrSpd;

	/* Calculate 2,3 items */
	kx  = MotEmf;
	kx  = kx / (2.0f*Bprm->MotPolePairNum);
	kx  = kx / MotLd;

	ky  = Bprm->MotPolePairNum * MotLd;
	ky  = ky/2.0f;               
	ky	= ky * MaxCur/MotEmf;
	ky	= ky * MaxCur;

	kz  = kx + ky;
	kz  = kz * 1.0f/MaxCur;
	weaken_ptr->conf.IdrefLimTerm23 = kz;

	/* Calculate 4 items */
	kx  = MotR * MaxCur/Bprm->MotPolePairNum;
	kx  = kx/MotLd;
	kx	= kx * 1.0f/MaxCur;
	kx	= kx * 15000.0f/OvrSpd;

	weaken_ptr->conf.IdrefLimTerm4 = kx;

}