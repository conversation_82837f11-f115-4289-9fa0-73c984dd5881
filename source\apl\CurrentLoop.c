/******************************************************************************
* Includes <System Includes> , "Project Includes"
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseLoops.h"
#include "Mlib.h"
#include "math.h"
#include "MotorIdentification.h"
//#define USE_VOLT_FF    TRUE
#define USE_WEAK_FIELD TRUE
#define MAX_IDQ_LIM  1.8f 
PRIVATE void WeakFieldPICal(WEAKFV     *WkFv);
PRIVATE void CurrentPICal(CUR_LOOP *P);
PRIVATE void SinCosCal(CUR_LOOP *P);
PRIVATE void SVPWMCal(CUR_LOOP *P);
PRIVATE void PWMCal(CUR_LOOP *P);
PRIVATE void DeadTimeCompensation(CUR_LOOP *P);
/******************************************************************************
* Exported global variables
******************************************************************************/
PUBLIC float SqrtFloat(float x)
{
	float xhalf = 0.5f*x;
	int i = *(int*)&x;                // get bits for floating VALUE 
	i = 0x5f375a86- (i>>1);           // gives initial guess y0
	x = *(float*)&i;                  // convert bits BACK to float
	x = x*(1.5f-xhalf*x*x);           // Newton step, repeating increases accuracy
	return 1/x;
}
/******************************************************************************
* Function Name : CurrentLoopInit
* Description   : CurrentLoop Parameter initialization
* Arguments     : none
* Return Value  : none
******************************************************************************/
PUBLIC void CurrentLoopInit(BASE_LOOP *BaseLoops)
{
    CUR_LOOP   *pCur = &BaseLoops->CurLoop;
    
    pCur->P.PWMPRD = PWM_PERIOD_VALUE;
    pCur->P.VDCinvTSQRT = pCur->P.PWMPRD >> 1;
    pCur->P.VDCinvTCon0 = pCur->P.VDCinvTSQRT * C_FSqrt3;
    pCur->P.Tonmin = C_Tonmin;
    pCur->P.Tonmax = pCur->P.PWMPRD - (pCur->P.Tonmin * 2);
 
}

/******************************************************************************
* Function Name : CurrentLoopInit
* Description   : CurrentLoop Parameter initialization
* Arguments     : none
* Return Value  : none
******************************************************************************/
PUBLIC void CurrentRefSelect(BASE_LOOP *BaseLoops, BOOL VfContrl )
{
	CUR_LOOP   *pCur = &BaseLoops->CurLoop;

    if(VfContrl)
    {
        pCur->V.VdRefVF = BaseLoops->VdRefVF;
        pCur->V.VqRefVF = BaseLoops->VqRefVF;
//        pCur->V.ElecAngle = BaseLoops->ElecAngleVF;

//        if((BaseLoops->Enc->V.PhaseReady) && 
//								(pCur->V.MPIFlag == 0))
//        	pCur->V.ElecAngle = BaseLoops->Enc->V.ElecAngle;
    }
    else
    {
        pCur->V.IqRef = BaseLoops->IqRef;
        pCur->V.IdRef = BaseLoops->IdRef;
//        pCur->V.ElecAngle = BaseLoops->Enc->V.ElecAngle; 
    }
	
}


/******************************************************************************
* Function Name : CurrentLoopExec
* Description   : CurrentLoop calculation
* Arguments     : none
* Return Value  : none
******************************************************************************/
float           gDeltaUa = 0.01f;
float           gTestUa=0.1f;
float           gTestUb=0.07f;
volatile UINT16 gTeDeltaAg = 15;
volatile float  uduqAmp = 0.08f;
UINT16          OpenLoop = 0;
//float Freq = 200.0f;
//float mod_comp_fact = 0.0f;
//float mod_alpha_comp;
//float mod_beta_comp;
PUBLIC void CurrentLoopExec(BASE_LOOP *BaseLoops, BOOL VfContrl)
{
      REAL32     NotFilOutTmp=0;
      REAL32     fw,fw2,fw3;
      CUR_LOOP   *pCur = &BaseLoops->CurLoop;
      WEAKFV     *WkFv =  &BaseLoops->WeakFV;
      
          
      if( BaseLoops->BaseCtrls->BaseEnable == FALSE )
      {
          MlibResetLongMemory( &(pCur->V), sizeof(pCur->V)/4 );
          MlibResetLongMemory( &BaseLoops->WeakFV, sizeof(WEAKFV)/4 );
          return;
      }

//	CurrentRefSelect(BaseLoops, VfContrl );

    NotFilOutTmp = pCur->V.IqRef;
    
    /* notch filter 1 */
	if(pCur->P.NotFilCfg & 0x0F)
	{
		NotFilOutTmp = FlibNotchFilter2(NotFilOutTmp, pCur->V.NotOut1, pCur->V.NotIn1, pCur->P.NotchKn1);
	}
	/* notch filter 2 */
	if((pCur->P.NotFilCfg>>8) & 0x0F)
	{
		NotFilOutTmp =FlibNotchFilter2(NotFilOutTmp, pCur->V.NotOut2, pCur->V.NotIn2, pCur->P.NotchKn2);
	}
	/* notch filter 3 */
	if((pCur->P.NotFilCfg>>16) & 0x0F)
	{
		NotFilOutTmp =FlibNotchFilter2(NotFilOutTmp, pCur->V.NotOut3, pCur->V.NotIn3, pCur->P.NotchKn3);
	}
	/* notch filter 4 */
	if((pCur->P.NotFilCfg>>24) & 0x0F)
	{
		NotFilOutTmp =FlibNotchFilter2(NotFilOutTmp, pCur->V.NotOut4, pCur->V.NotIn4, pCur->P.NotchKn4);
	}

    BaseLoops->TrqLitFlag = FALSE;

    if(NotFilOutTmp > pCur->V.IqRefLmtP)
    {
            pCur->V.IqRefNotFil = pCur->V.IqRefLmtP;
            BaseLoops->TrqLitFlag = TRUE;
    }
    else if(NotFilOutTmp < pCur->V.IqRefLmtN)
    {
            pCur->V.IqRefNotFil = pCur->V.IqRefLmtN;
            BaseLoops->TrqLitFlag = TRUE;
    }
    else
    {
            pCur->V.IqRefNotFil = NotFilOutTmp;
    }
	
    //Sine & Cosine calculate
    SinCosCal(pCur);

    // if UVW line sequence is reverse,overturn the current sequence
//    ExChangeCurSample(BaseLoops, pCur);  

    //Clarke
    pCur->V.Ialfa = pCur->V.Ia;
    pCur->V.Ibeta = (2.0f * pCur->V.Ib + pCur->V.Ia) * C_FSqrt3Inv;

    //Park
    fw = (pCur->V.Ialfa * pCur->V.CosTheta) + (pCur->V.Ibeta * pCur->V.SinTheta);
    pCur->V.IqFdb = (-pCur->V.Ialfa * pCur->V.SinTheta) + (pCur->V.Ibeta * pCur->V.CosTheta);
    
    pCur->V.IdFdb = fw;
    
    #if 1
    float tempL = (float)BaseLoops->Bprm->MotLq/(float)BaseLoops->Bprm->MotLd;
    if((tempL < 0.7f) || (tempL > 1.4f))
    {       
        fw3= BaseLoops->Bprm->MotEmf/BaseLoops->Bprm->MotPolePairNum;   // 0.2433
        fw2 = fw3*0.5f / (BaseLoops->Bprm->MotLq - BaseLoops->Bprm->MotLd);  // 60
        fw = fw2 * fw2 + pCur->V.IqRef*pCur->V.IqRef;    // 
        fw = fw2 - SqrtFloat(fw);
        
        pCur->V.IdRef = 1.414f *fw;

        fw = -fw3/BaseLoops->Bprm->MotLd;
        fw = 1.414*fw;
        
        if(pCur->V.IdRef < fw)
            pCur->V.IdRef = fw;
    }
   

    #endif    
    
    if(BaseLoops->CurrentTest)
    {
          pCur->V.IdRefLit = pCur->V.IdRef;
          pCur->V.IqRefLit = pCur->V.IqRef;      
    }
#if(USE_WEAK_FIELD == TRUE)
    else if(pCur->P.WfSw == TRUE)
    {
    #if 0
        // weak field control IdRef calculation       
        fw = MlibABS(BaseLoops->SpdRefSum);
        fw2 = BaseLoops->Bprm->KmotVel*BaseLoops->Bprm->MaxSpd;
        if(fw > fw2) fw = fw2;
        fw = pCur->P.WfV1Rat  - fw;
        if(fw>0) 
            fw=0;   
        
        WkFv->IdOut = fw * pCur->P.KwfId; 
        
        
	#else
	
		// weak field control pid calculation
	WeakFieldPICal(WkFv);

	WkFv->IdOut = WkFv->IdOut * BaseLoops->Bprm->MaxCur;
        if(WkFv->IdOut < 0 ) pCur->V.WeakFieldFlag = 1;
        else   pCur->V.WeakFieldFlag = 0;       
        
        WkFv->IdOut += pCur->V.IdRef;
        
        fw = WkFv->WfIdRefLimAct * BaseLoops->Bprm->MaxCur;
        if (WkFv->IdOut < fw)
        {
            WkFv->IdOut = fw;
        }
	#endif

        fw = BaseLoops->Bprm->MaxCur * WkFv->WfIqRefLim;
        
        if(fw < 0 ) fw = -fw;
        
        fw2 = BaseLoops->Bprm->MaxCur * BaseLoops->Bprm->MaxCur - WkFv->IdOut * WkFv->IdOut;
        if(fw2<=0.0f)  fw2 = 0.0f;
        
        fw2 = SqrtFloat(fw2);
        
        if(fw > fw2)  fw = fw2;      
        if(pCur->V.IqRefNotFil > fw )
        {
            WkFv->IqOut = fw;
			BaseLoops->TrqLitFlag = TRUE;
        }
        else if(pCur->V.IqRefNotFil < -fw )
		{
            WkFv->IqOut = -fw;
			BaseLoops->TrqLitFlag = TRUE;
        }
        else
        {
            WkFv->IqOut = pCur->V.IqRefNotFil;
        }
             
        pCur->V.IdRefLit = WkFv->IdOut;
        pCur->V.IqRefLit = WkFv->IqOut;
    }
 	else
	{
         // if function generation 
        if(pCur->V.IdRef < -0.7*BaseLoops->Bprm->MaxCur)
            pCur->V.IdRef = -0.7*BaseLoops->Bprm->MaxCur;
		fw2 = BaseLoops->Bprm->MaxCur * BaseLoops->Bprm->MaxCur - pCur->V.IdRef * pCur->V.IdRef;
        if(fw2<=0.0f) 
            fw2 = 0.0f;
        fw = SqrtFloat(fw2);
		if(pCur->V.IqRefNotFil > fw )
        {
            WkFv->IqOut = fw;
			BaseLoops->TrqLitFlag = TRUE;
        }
        else if(pCur->V.IqRefNotFil < -fw )
		{
            WkFv->IqOut = -fw;
			BaseLoops->TrqLitFlag = TRUE;
        }
        else
        {
            WkFv->IqOut = pCur->V.IqRefNotFil;
        }
		pCur->V.IdRefLit = pCur->V.IdRef;
		pCur->V.IqRefLit = WkFv->IqOut;
	}   
    
#endif

    if(BaseLoops->Bprm->UWVSeq)
    {
        pCur->V.IdRefLit = -pCur->V.IdRefLit;
    }
    
    pCur->V.IqFdbFil = FlibLpfilter1(pCur->V.IqFdb, 0.5f, pCur->V.IqFdbFil);
    pCur->V.IdFdbFil = FlibLpfilter1(pCur->V.IdFdb, 0.5f, pCur->V.IdFdbFil);   
	// pid calculation
	CurrentPICal(pCur);

if(BaseLoops->FunVoltFF)
{
	REAL32 OmigaSpd = BaseLoops->Bprm->DirSign* BaseLoops->SpdObsFdb/BaseLoops->Bprm->KmotVel;
        
	REAL32 Rs = BaseLoops->Bprm->MotR;
	REAL32 WrLq = BaseLoops->Bprm->MotPolePairNum* OmigaSpd*BaseLoops->Bprm->MotLq;
    REAL32 WrLd = BaseLoops->Bprm->MotPolePairNum* OmigaSpd*BaseLoops->Bprm->MotLd;
	REAL32 WrPhi = OmigaSpd* BaseLoops->Bprm->MotEmf;

	//RId-wLqIq
	REAL32 UdComp = Rs*pCur->V.IdRefLit - WrLq*pCur->V.IqRefLit;
	//RIq+wLdId+KeSpd
	REAL32 UqComp = Rs*pCur->V.IqRefLit + WrLd*pCur->V.IdRefLit + WrPhi;

	pCur->V.VdRef = pCur->V.VdRef + (UdComp/Uab_LEVEL);
	pCur->V.VqRef = pCur->V.VqRef + (UqComp/Uab_LEVEL);
        
        BaseLoops->DebugVar1 = BaseLoops->SpdObsFdb*1000;
        
       BaseLoops->DebugVar2 = pCur->V.VdRef*1000 ;
       BaseLoops->DebugVar3 = pCur->V.VqRef*1000 ; 
       
       BaseLoops->DebugVar4 = UqComp*1000;
        
	
}
   
	WkFv->WfVdRef = pCur->V.VdRef ;
	WkFv->WfVqRef = pCur->V.VqRef ;

	fw = pCur->V.VdRef*pCur->V.VdRef + pCur->V.VqRef*pCur->V.VqRef;

	if(fw > 1.1f)	
	{
      if(pCur->V.VdRef >= 1.05f)
      {
          pCur->V.VdRef = 1.05f;
      }
      else if(pCur->V.VdRef <= -1.05f)
      {
          pCur->V.VdRef = -1.05f;
      }     
          
      fw2 = 1.1f - pCur->V.VdRef*pCur->V.VdRef;
      
      if(fw2<0.0016f)
      {
          fw2 = 0.0016f;
      }
      
        fw2 = SqrtFloat(fw2);
      
      if(pCur->V.VqRef > fw2)
      {
          pCur->V.VqRef = fw2;
      }
      else if(pCur->V.VqRef < -fw2)
      {
          pCur->V.VqRef = -fw2;
      }  
      
      pCur->V.VoltLmtFlg = FALSE;
	}
	else
	{
            pCur->V.VoltLmtFlg = FALSE;
	}
	

	//Open loop VF control
	if(VfContrl == TRUE)
	{
        pCur->V.IdKiPart = 0.0f;
        pCur->V.IqKiPart = 0.0f;
        pCur->V.VdRef = pCur->V.VdRefVF;
        pCur->V.VqRef = pCur->V.VqRefVF;
	}
	
    if(OpenLoop == 1)
    {
      pCur->V.VdRef = gTestUa;
      pCur->V.VqRef = gTestUb;         
    }        
        
	// inverse park
	pCur->V.Valfa = -pCur->V.VqRef * pCur->V.SinTheta + pCur->V.VdRef * pCur->V.CosTheta;
    pCur->V.Vbeta = pCur->V.VqRef * pCur->V.CosTheta + pCur->V.VdRef * pCur->V.SinTheta;


    pCur->V.IalfaRef = -pCur->V.IqRefLit * pCur->V.SinTheta + pCur->V.IdRefLit * pCur->V.CosTheta;
    pCur->V.IbetaRef = pCur->V.IqRefLit * pCur->V.CosTheta + pCur->V.IdRefLit * pCur->V.SinTheta;

    pCur->V.IaRef = pCur->V.IalfaRef;
    pCur->V.IbRef = (C_FSqrt3*pCur->V.IbetaRef - pCur->V.IalfaRef)*0.5f;
    pCur->V.IcRef = -pCur->V.IaRef - pCur->V.IbRef;
    
//    float mod_alpha_filter_sgn = (2.0 / 3.0) * MlibSIGN(pCur->V.IaRef) - (1.0 / 3.0) * MlibSIGN(pCur->V.IbRef) - (1.0 / 3.0) * MlibSIGN(pCur->V.IcRef);
//	float mod_beta_filter_sgn = C_FSqrt3Inv * MlibSIGN(pCur->V.IbRef) - C_FSqrt3Inv * MlibSIGN(pCur->V.IcRef);
//	mod_alpha_comp = mod_alpha_filter_sgn * mod_comp_fact;
//	mod_beta_comp = mod_beta_filter_sgn * mod_comp_fact;   
    
    static UINT16 gTestAngle = 0;

    if(( BaseLoops->BaseCtrls->BaseEnable == TRUE ) && (OpenLoop == 2))
    {
	    gTestAngle = gTestAngle + gTeDeltaAg;
        
        gTestAngle = gTestAngle % C_2SinUint;
        
        pCur->V.ElecAngle = gTestAngle;
        
        SinCosCal(pCur);

        pCur->V.Valfa = gTestUa*pCur->V.CosTheta;  
        pCur->V.Vbeta = gTestUa*pCur->V.SinTheta;  
    }
    
  
    pCur->V.Valfa = -pCur->V.Valfa; 
    pCur->V.Vbeta = -pCur->V.Vbeta;

    


    if(OpenLoop == 3)
    {
      pCur->V.Valfa = gTestUa;
      pCur->V.Vbeta = gTestUb;
    }
    
    if(OpenLoop != 0)
    {
        pCur->V.IdKiPart = 0.0f;
        pCur->V.IqKiPart = 0.0f;          
        pCur->V.IdKpPart = 0.0f;
        pCur->V.IqKpPart = 0.0f;        
    }      
    
    
//    pCur->V.Valfa = gTestUa;
//    pCur->V.Vbeta = gTestUb;
//    
//    BaseLoops->DebugVar2 = pCur->V.Valfa;
//    BaseLoops->DebugVar3 = pCur->V.Vbeta;
    
	// svpwm
	SVPWMCal(pCur);

    // if UVW line sequence is reverse,overturn the PWM sequence
//    ExChangePWMOut(BaseLoops, pCur);
	DeadTimeCompensation(pCur);

}


PUBLIC void DC_CurrentLoopExec(BASE_LOOP *BaseLoops)
{
    REAL32     NotFilOutTmp=0;
      REAL32     fw,fw2,fw3;
      CUR_LOOP   *pCur = &BaseLoops->CurLoop;
      WEAKFV     *WkFv =  &BaseLoops->WeakFV;
      
          
      if( BaseLoops->BaseCtrls->BaseEnable == FALSE )
      {
          MlibResetLongMemory( &(pCur->V), sizeof(pCur->V)/4 );
          MlibResetLongMemory( &BaseLoops->WeakFV, sizeof(WEAKFV)/4 );
          return;
      }

//	CurrentRefSelect(BaseLoops, VfContrl );

    NotFilOutTmp = pCur->V.IqRef;
    
    /* notch filter 1 */
	if(pCur->P.NotFilCfg & 0x0F)
	{
		NotFilOutTmp = FlibNotchFilter2(NotFilOutTmp, pCur->V.NotOut1, pCur->V.NotIn1, pCur->P.NotchKn1);
	}
	/* notch filter 2 */
	if((pCur->P.NotFilCfg>>8) & 0x0F)
	{
		NotFilOutTmp =FlibNotchFilter2(NotFilOutTmp, pCur->V.NotOut2, pCur->V.NotIn2, pCur->P.NotchKn2);
	}
	/* notch filter 3 */
	if((pCur->P.NotFilCfg>>16) & 0x0F)
	{
		NotFilOutTmp =FlibNotchFilter2(NotFilOutTmp, pCur->V.NotOut3, pCur->V.NotIn3, pCur->P.NotchKn3);
	}
	/* notch filter 4 */
	if((pCur->P.NotFilCfg>>24) & 0x0F)
	{
		NotFilOutTmp =FlibNotchFilter2(NotFilOutTmp, pCur->V.NotOut4, pCur->V.NotIn4, pCur->P.NotchKn4);
	}

    BaseLoops->TrqLitFlag = FALSE;

    if(NotFilOutTmp > pCur->V.IqRefLmtP)
    {
            pCur->V.IqRefLit = pCur->V.IqRefLmtP;
            BaseLoops->TrqLitFlag = TRUE;
    }
    else if(NotFilOutTmp < pCur->V.IqRefLmtN)
    {
            pCur->V.IqRefLit = pCur->V.IqRefLmtN;
            BaseLoops->TrqLitFlag = TRUE;
    }
    else
    {
            pCur->V.IqRefLit = NotFilOutTmp;
    }
	

    pCur->V.IqRef = pCur->V.IqRefLit;
    pCur->V.IdRef = 0;
    
    pCur->V.IqFdb =  pCur->V.Ia;
    pCur->V.IdFdb = 0;
    
    
     pCur->V.VoltLmtFlg = FALSE;
    
    CurrentPICal(pCur);
    
    
    REAL32 Rs = BaseLoops->Bprm->MotR;
    	//RIq+wLdId+KeSpd
	REAL32 UqComp = Rs*pCur->V.IqRefLit;
    pCur->V.VqRef = pCur->V.VqRef + (UqComp/pCur->V.ActualVdcFilo);
    
    BaseLoops->DebugVar3 = pCur->V.VqRef*1000;
     
    if(pCur->V.VqRef >= 1.0f)
    {
        pCur->V.VqRef = 1.0f;
    }
    else if(pCur->V.VqRef <= -1.0f)
    {
        pCur->V.VqRef = -1.0f;
    }  
  
  
    if(OpenLoop == 1)
    {
      pCur->V.VdRef = gTestUa;
      pCur->V.VqRef = gTestUb;         
    }  
 
    // pwm
    PWMCal(pCur);

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void WeakFieldPICal(WEAKFV     *WkFv)
{
	REAL32	   fw0,fw1,fw2,fw3,fw4;

/*--------------------------------------------------------------------------------------------------*/
//		Vqmax = sqrt( Vmax^2 - Vd^2 )																*
/*--------------------------------------------------------------------------------------------------*/
//    REAL32 kx = DcVolt/179.0f;
//	fw0 = WkFv->WfV1Max * WkFv->WfV1Max;		  // Vmax^2
	fw0 = 0.95f;                                   // (2/sqrt(3))^2
	fw1 = WkFv->WfVdRef * WkFv->WfVdRef;		  // Vd^2 
	fw0 = fw0-fw1;								  // Vmax^2 - Vd^2
	if(fw0 < 0) 								  // if (Vmax^2 - Vd^2)< 0, then (Vmax^2 - Vd^2) = 0
		fw0 = 0;
	
	fw2 = SqrtFloat( fw0 );						  // sqrt(Vmax^2 - Vd^2 )							
	
	WkFv->WfVqMax = fw2;						  // Vqmax = sqrt(Vmax^2 - Vd^2 )	
/*--------------------------------------------------------------------------------------------------*/
//		TMP0 = Vqmax - Vq																			*
/*--------------------------------------------------------------------------------------------------*/
	fw1 = WkFv->WfVqRef;
    
	if( fw1 < 0 )
	{
		fw1 =- fw1;    // |Vq|			
	}
	fw0 = WkFv->WfVqMax - fw1;

/*--------------------------------------------------------------------------------------------------*/
/*		Proportional&Integral calculation															*/
/*--------------------------------------------------------------------------------------------------*/
	WkFv->WfKpPart = WkFv->WfKp * fw0 ;
	WkFv->WfKiPart = WkFv->WfKiPart + WkFv->WfKi * fw0 ;
	
	WkFv->WfKiPart = FlibLimitul(WkFv->WfKiPart, 0.0f, -1.0f);
	WkFv->WfOutPreSat = WkFv->WfKpPart + WkFv->WfKiPart;

//	if(WkFv->WfKpPart > 0.0f)
//	{
//		WkFv->WfVdRef = 0.0f;
//		WkFv->WfKiPart = 0.0f;
//	}
	if(WkFv->WfKpPart < -1.0f)
	{
		WkFv->WfVdRef = -1.0f;
		WkFv->WfKiPart = 0.0f;
	}
	else if(WkFv->WfOutPreSat > 0.0f)
	{
		WkFv->WfVdRef = 0.0f;
		WkFv->WfKiPart = 0.0f;
	}
	else if(WkFv->WfOutPreSat < -1.0f)
	{
		WkFv->WfVdRef = -1.0f;
		WkFv->WfKiPart = -1.0f - WkFv->WfKpPart;
	}
	else
	{
		WkFv->WfVdRef = WkFv->WfOutPreSat;
	}

    WkFv->WfIdRefLimAct  = WkFv->WfIdRefLim;
    if(WkFv->WfIdRefLimAct < WF_MAXID)
    {
        WkFv->WfIdRefLimAct = WF_MAXID;
    }
	WkFv->IdOut = FlibLimitul(WkFv->WfVdRef, 0.0f, WkFv->WfIdRefLimAct);

}


/******************************************************************************
* Function Name : CurrentPICal
* Description   : current loop pi calculation function
* Arguments     : CUR_LOOP
* Return Value  : none
******************************************************************************/
PRIVATE void CurrentPICal(CUR_LOOP *P)
{
	P->V.IdErr = P->V.IdRefLit - P->V.IdFdb;
	P->V.IqErr = P->V.IqRefLit - P->V.IqFdb;
 
	P->V.IqKpPart = P->P.KpQ * P->V.IqErr;
	if(P->V.VoltLmtFlg == FALSE)
		P->V.IqKiPart = P->V.IqKiPart + P->P.KiQ * P->V.IqErr;

	P->V.IqKiPart = FlibLimitul(P->V.IqKiPart, MAX_IDQ_LIM, -MAX_IDQ_LIM);
	P->V.IqOutPreSat = P->V.IqKpPart + P->V.IqKiPart;

	if(P->V.IqKpPart > MAX_IDQ_LIM)
	{
            P->V.VqRef = MAX_IDQ_LIM;
            P->V.IqKiPart = 0.0f;
	}
	else if(P->V.IqKpPart < -MAX_IDQ_LIM)
	{
            P->V.VqRef = -MAX_IDQ_LIM;
            P->V.IqKiPart = 0.0f;
	}
	else if(P->V.IqOutPreSat > MAX_IDQ_LIM)
	{
            P->V.VqRef = MAX_IDQ_LIM;
            P->V.IqKiPart = MAX_IDQ_LIM - P->V.IqKpPart;
	}
	else if(P->V.IqOutPreSat < -MAX_IDQ_LIM)
	{
            P->V.VqRef = -MAX_IDQ_LIM;
            P->V.IqKiPart = -MAX_IDQ_LIM - P->V.IqKpPart;
	}
	else
	{
            P->V.VqRef = P->V.IqOutPreSat;
	}

	P->V.IdKpPart = P->P.KpD * P->V.IdErr;
	if(P->V.VoltLmtFlg == FALSE)
		P->V.IdKiPart = P->V.IdKiPart + P->P.KiD * P->V.IdErr;
	
	P->V.IdKiPart = FlibLimitul(P->V.IdKiPart, MAX_IDQ_LIM, -MAX_IDQ_LIM);
	P->V.IdOutPreSat = P->V.IdKpPart + P->V.IdKiPart;

//	P->V.IdOutPreSat += P->V.IdRef*KKR;

	if(P->V.IdKpPart > MAX_IDQ_LIM)
	{
            P->V.VdRef = MAX_IDQ_LIM;
            P->V.IdKiPart = 0.0f;
	}
	else if(P->V.IdKpPart < -MAX_IDQ_LIM)
	{
            P->V.VdRef = -MAX_IDQ_LIM;
            P->V.IdKiPart = 0.0f;
	}
	else if(P->V.IdOutPreSat > MAX_IDQ_LIM)
	{
            P->V.VdRef = MAX_IDQ_LIM;
            P->V.IdKiPart = MAX_IDQ_LIM - P->V.IdKpPart;
	}
	else if(P->V.IdOutPreSat < -MAX_IDQ_LIM)
	{
            P->V.VdRef = -MAX_IDQ_LIM;
            P->V.IdKiPart = -MAX_IDQ_LIM - P->V.IdKpPart;
	}
	else
	{
	    P->V.VdRef = P->V.IdOutPreSat;
	}

}


/******************************************************************************
* Function Name : SinCosCal
* Description   : sin/cos calculation function
* Arguments     : CUR_LOOP
* Return Value  : none
******************************************************************************/
extern const REAL32 SinCTable[];
PRIVATE void SinCosCal(CUR_LOOP *P)
{
	UINT16 Tmp = P->V.ElecAngle>>1;        // 65536 -> C_SinUint(32768)
	
	if(Tmp > C_SinUint3D4)
	{
		P->V.SinTheta = -SinCTable[C_SinUint - Tmp];
		P->V.CosTheta = SinCTable[Tmp - C_SinUint3D4];
	}
	else if(Tmp > C_SinUint1D2)
	{
		P->V.SinTheta = -SinCTable[Tmp - C_SinUint1D2];
		P->V.CosTheta = -SinCTable[C_SinUint3D4 - Tmp];
	}
	else if(Tmp > C_SinUint1D4)
	{
		P->V.SinTheta = SinCTable[C_SinUint1D2 - Tmp];
		P->V.CosTheta = -SinCTable[Tmp - C_SinUint1D4];
	}
	else
	{
		P->V.SinTheta = SinCTable[Tmp];
		P->V.CosTheta = SinCTable[C_SinUint1D4 - Tmp];
	}
}

PRIVATE void OverModulation(INT16 PwmPRD, INT32* T1, INT32* T2)
{
    INT32 P1 = *T1, P2 = *T2;
    INT32 PSum = P1 + P2;

    if(PSum > PwmPRD)   // 1.0
    {
        P1 = PwmPRD * P1 / PSum;
        P2 = PwmPRD * P2 / PSum;

        if(PSum > PwmPRD*1.15)  // 1.15*6250
        {
           INT32 PsumDelta = PSum - PwmPRD*1.15;

           if(P1 > P2)
           {
              if(P2 < PsumDelta)
              {
               P2 = 0;
               P1 = PwmPRD;
              }
              else
              {
               P2 -= PsumDelta;
               P1 += PsumDelta;
              }
           }
           else
           {
              if(P1 < PsumDelta)
              {
               P1 = 0;
               P2 = PwmPRD;
              }
              else
              {
               P1 -= PsumDelta;
               P2 += PsumDelta;
              }
           }
        }

        *T1 = P1;
        *T2 = P2;
    }
}
/******************************************************************************
* Function Name : SinCosCal
* Description   : sin/cos calculation function
* Arguments     : CUR_LOOP
* Return Value  : none
******************************************************************************/
PRIVATE void SVPWMCal(CUR_LOOP *P)
{
	INT32 Temp0 = 0;
	REAL32 Va,Vb,Vc;
	INT16  Sector,X,Y,Z,Ta,Tb,Tc;
    INT32  T1,T2;

	Va = P->V.Vbeta;
	Vb = (-P->V.Vbeta + C_FSqrt3 * P->V.Valfa) * C_F1D2;
	Vc = (-P->V.Vbeta - C_FSqrt3 * P->V.Valfa) * C_F1D2;

    /* 60 degrees sector determination */
	Sector = 0;
	if ( Va > 0 )  { Sector += 1 ;}
	if ( Vb > 0 )  { Sector += 2 ;}
	if ( Vc > 0 )  { Sector += 4 ;}

	X = (INT16)(2 * P->V.Vbeta * P->P.VDCinvTSQRT);
	Y = (INT16)(P->V.Vbeta * P->P.VDCinvTSQRT + P->V.Valfa * P->P.VDCinvTCon0);
	Z = (INT16)(P->V.Vbeta * P->P.VDCinvTSQRT - P->V.Valfa * P->P.VDCinvTCon0);

    /* T1 and T2 calculation depending on the sector number */
	switch( Sector )
	{
		case 0:
			T1= -Y;
			T2= -Z;
			break;
		case 1:
			T1=  Z;
			T2=  Y;
			break;
		case 2:
			T1=  Y;
			T2= -X;
			break;
		case 3:
			T1= -Z;
			T2=  X;
			break;
		case 4:
			T1= -X;
			T2=  Z;
			break;
		case 5:
			T1=  X;
			T2= -Y;
			break;
		case 6:
			T1= -Y;
			T2= -Z;
			break;
		case 7:
			T1= -Y;
			T2= -Z;
			break;
	}

	if ((T1 - P->P.Tonmin) < 0 ) { T1 = P->P.Tonmin ; }
	if ((T2 - P->P.Tonmin) < 0 ) { T2 = P->P.Tonmin ; }

    OverModulation(P->P.PWMPRD, &T1, &T2);

	Ta= (P->P.PWMPRD- T1- T2) >> 1;
	Tb= Ta + T1;
	Tc= Tb + T2;

	switch( Sector )
	{

		case 0:
			P->V.TaNumber= Tb;
			P->V.TbNumber= Tc;
			P->V.TcNumber= Ta;
			break;
		case 1:
			P->V.TaNumber= Tb;
			P->V.TbNumber= Ta;
			P->V.TcNumber= Tc;
			break;
		case 2:
			P->V.TaNumber= Ta;
			P->V.TbNumber= Tc;
			P->V.TcNumber= Tb;
			break;
		case 3:
			P->V.TaNumber= Ta;
			P->V.TbNumber= Tb;
			P->V.TcNumber= Tc;
			break;
		case 4:
			P->V.TaNumber= Tc;
			P->V.TbNumber= Tb;
			P->V.TcNumber= Ta;
			break;
		case 5:
			P->V.TaNumber= Tc;
			P->V.TbNumber= Ta;
			P->V.TcNumber= Tb;
			break;
		case 6:
			P->V.TaNumber= Tb;
			P->V.TbNumber= Tc;
			P->V.TcNumber= Ta;
			break;
		case 7:
			P->V.TaNumber= Tb;
			P->V.TbNumber= Tc;
			P->V.TcNumber= Ta;
			break;
        default :
            break;
	}
    
  
}



PRIVATE void PWMCal(CUR_LOOP *P)
{
  INT16 Temp = P->V.VqRef * P->P.VDCinvTSQRT ;
  
  
  if(Temp > P->P.VDCinvTSQRT)
  {
    Temp = P->P.VDCinvTSQRT;
  }
  
  
  if(Temp < -P->P.VDCinvTSQRT)
  {
    Temp = -P->P.VDCinvTSQRT;
  }
   
  P->V.TaNumber =  P->P.VDCinvTSQRT + Temp;
  P->V.TbNumber =  P->P.VDCinvTSQRT - Temp;  
  
  if(P->V.TaNumber>0.95*PWM_PERIOD_VALUE)
  {
    P->V.TaNumber = 0.95*PWM_PERIOD_VALUE;
  }
  else if(P->V.TaNumber<0.05*PWM_PERIOD_VALUE)
  {
     P->V.TaNumber = 0.05*PWM_PERIOD_VALUE;
  }
  
  if(P->V.TbNumber>0.95*PWM_PERIOD_VALUE)
  {
    P->V.TbNumber = 0.95*PWM_PERIOD_VALUE;
  }
   else if(P->V.TbNumber<0.05*PWM_PERIOD_VALUE)
  {
     P->V.TbNumber = 0.05*PWM_PERIOD_VALUE;
  }
  
  P->V.TcNumber =  PWM_PERIOD_VALUE/2;
  

}


/******************************************************************************
* Function Name : SinCosCal
* Description   : sin/cos calculation function
* Arguments     : CUR_LOOP
* Return Value  : none
******************************************************************************/
INT16 Tdead = 0;  // 
INT16 TdeadSet = -240;
PRIVATE void DeadTimeCompensation(CUR_LOOP *P)
{
	float TmpIa, TmpIb, TmpIc;
    
    P->V.Ic = -(P->V.Ia + P->V.Ib);
    
    TmpIa = P->V.Ia;
    TmpIb = P->V.Ib;
    TmpIc = P->V.Ic;
    
//    P->V.Ic = -(P->V.Ia + P->V.Ib);

//    if(P->V.Ia < 0.3f && P->V.Ia > -0.3f)
//    {
//    	TmpIa = P->V.IaRef;
//    }
//    else
//    {
//    	TmpIa = P->V.Ia;
//    }

//    if(P->V.Ib < 0.3f && P->V.Ib > -0.3f)
//    {
//    	TmpIb = P->V.IbRef;
//    }
//    else
//    {
//    	TmpIb = P->V.Ib;
//    }

//    if(P->V.Ic < 0.3f && P->V.Ic > -0.3f)
//    {
//    	TmpIc = P->V.IcRef;
//    }
//    else
//    {
//    	TmpIc = P->V.Ic;
//    }
    
    
//    if(P->V.IqRefLit < 0.3f && P->V.IqRefLit > -0.3f)
//    {
//        Tdead = 0;
//    }
//    else
//    {
//        Tdead = TdeadSet;
//    }
    
//    INT32  elecAngle = P->V.ElecAngle;
// 
//    if(elecAngle < 16384 && elecAngle > 0)
//    {
//        TmpIa = -P->V.IqRef;
//    }
//    else
//    {
//        TmpIa = P->V.IqRef;
//    }
//    
//    if(elecAngle < 27306 && elecAngle > 10922)
//    {
//        TmpIb = -P->V.IqRef;
//    }
//    else
//    {
//        TmpIb = P->V.IqRef;
//    }

//    if(elecAngle > 5461 && elecAngle < 21845)
//    {
//        TmpIc = P->V.IqRef;
//    }
//    else
//    {
//        TmpIc = -P->V.IqRef;
//    }
    
    // Sector 1
    if((TmpIa > 0) && (TmpIb < 0 ) && (TmpIc < 0))
    {
    	P->V.TaNumber = P->V.TaNumber - Tdead;
    }
    else if((TmpIa> 0) && (TmpIb > 0 ) && (TmpIc < 0))
    {
        //Sector = 2;
    	P->V.TcNumber = P->V.TcNumber + Tdead;
    }
    else if((TmpIa < 0) && (TmpIb > 0 ) && (TmpIc < 0))
    {
    	//Sector = 3;
    	P->V.TbNumber = P->V.TbNumber - Tdead;
    }

	else if((TmpIa < 0) && (TmpIb > 0 ) && (TmpIc > 0))
    {
		//Sector = 4;
		P->V.TaNumber = P->V.TaNumber + Tdead;
    }

	else if((TmpIa < 0) && (TmpIb < 0 ) && (TmpIc > 0))
    {
		//Sector = 5;
		P->V.TcNumber = P->V.TcNumber - Tdead;
    }

	else if((TmpIa> 0) && (TmpIb < 0 ) && (TmpIc > 0))
    {
		//Sector = 6;
		P->V.TbNumber = P->V.TbNumber + Tdead;
    }

}








