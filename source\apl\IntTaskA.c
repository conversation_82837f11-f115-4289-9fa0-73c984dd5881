/****************************************************************************************************
 *
 * FILE NAME:  IntTaskA.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.14
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	14-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "intrinsics.h"                                             // intrinsic functions header
#include <math.h>
#include "Global.h"
#include "HardApi.h"
#include "ComUart.h"
#include "modbus.h"
#include "bsp_pwm.h"
#include "bsp.h"
#include "canfd_protocol.h"

extern UINT32 gTaskATest;
extern UINT8 g_intercpu_intflag;

extern PUBLIC volatile BOOL gSystemInitDone;

extern PUBLIC void CurrentRefSelect(BASE_LOOP *BaseLoops, BOOL VfContrl );
extern PUBLIC void CurrentLoopExec(BASE_LOOP *BaseLoops, BOOL VfContrl);

/******************************************************************************
* Function Name : function IntTaskA
* Description   : 62.5us
* Arguments     : none
* Return Value  : none
******************************************************************************/
UINT32 LastTime=0, ilaspTime=0;
UINT32 CilaspTime=0;
UINT16 TASKA_delay = 0;
PUBLIC void IntTaskAExec(void)
{
    BOOL                  ServoOn;
    UINT16                ax_id;
    UINT16                slot;
    AXIS_HANDLE           *AxisA;
    TASK_MEASURE_TIME	  *TaskProcessTime;
    CiA402_PRM	    	  *Prm402;
    BASE_CTRL             *BaseCtrls;
    BOOL                  UnderFlowFlag = 0;
    /* Read time stamp at the entrance of interrupt: for calculate the elapsed time of interrupt*/
    UINT32 IsrStartTime = hApi_GetTimerCounter();   
    ilaspTime = IsrStartTime - LastTime;
    LastTime = IsrStartTime;
    
    UnderFlowFlag  = bsp_getpwm_state();
    
    
     AxisA                = GetAxisHandle(0); 
     TaskProcessTime      = AxisA->TaskProcessTime;   
     UINT16          FunctionSw = (AxisA->Prm->PnCtrlCfgPrm.FunctionSw); 
#if 1
     if(UnderFlowFlag)
     {
       
       if(AxisA[0].BaseLoops->Enc->P.EncExeNum==0 || AxisA[0].BaseLoops->Enc->P.EncExeNum==1)
       {
          
            /* Encoder Get: for electric angle, position increment */
            for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
            {  
                ServoOn = AxisA[ax_id].BaseCtrls->BaseEnable;
                GetEncoderPulse(AxisA[ax_id].BaseLoops->Enc, AxisA[ax_id].AlmMngr, ax_id, ServoOn);
            } 
       
            /* Encoder calculation: for electric angle, position increment */
            for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
            {
                ServoOn = AxisA[ax_id].BaseCtrls->BaseEnable;
                EncoderCalExec(AxisA[ax_id].BaseLoops->Enc,AxisA[ax_id].BaseLoops->Enc2, AxisA[ax_id].AlmMngr, 
                    ax_id, ServoOn, AxisA[ax_id].BaseLoops->Bprm->UWVSeq,
                    AxisA[ax_id].Prm->PnMotPrm.MotType);
            }                

       }
       else
       {
         if(AxisA[0].BaseLoops->Enc->V.EncExeCNT%AxisA[0].BaseLoops->Enc->P.EncExeNum)
         {
                    /* Encoder Get: for electric angle, position increment */
            for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
            {  
                ServoOn = AxisA[ax_id].BaseCtrls->BaseEnable;
                GetEncoderPulse(AxisA[ax_id].BaseLoops->Enc, AxisA[ax_id].AlmMngr, ax_id, ServoOn);
            } 
            /* Encoder calculation: for electric angle, position increment */
            for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
            {
                ServoOn = AxisA[ax_id].BaseCtrls->BaseEnable;
                EncoderCalExec(AxisA[ax_id].BaseLoops->Enc,AxisA[ax_id].BaseLoops->Enc2, AxisA[ax_id].AlmMngr, 
                    ax_id, ServoOn, AxisA[ax_id].BaseLoops->Bprm->UWVSeq,
                    AxisA[ax_id].Prm->PnMotPrm.MotType);
            }                
         }
         AxisA[0].BaseLoops->Enc->V.EncExeCNT++;
       }
    }
#endif   
    
    if(FALSE ==hApi_AdcOffsetCheck()) 
    {
      if(TRUE == UnderFlowFlag)
      {
          bsp_clrpwm_state();
      }
      return;  
    }
 
    CanFd_Send();
 
           
    for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
    {
        
        /* Calculate phase current */
        hApi_GetPhaseCurrent(ax_id, &AxisA[ax_id].BaseLoops->CurLoop.V.Ia, 
                                    &AxisA[ax_id].BaseLoops->CurLoop.V.Ib,
                                    &AxisA[ax_id].BaseLoops->CurLoop.V.Ic,
                                    AxisA[ax_id].BaseLoops->CurLoop.P.CurSplKx);
        
        
        AxisA[ax_id].BaseLoops->CurLoop.V.ActualVdc = AxisA[ax_id].PowMngr->PowerSts.DcVolt;
        AxisA[ax_id].BaseLoops->CurLoop.V.ActualVdcFilo = AxisA[ax_id].PowMngr->PowerSts.DcVoltFilo;
                                                         
        AxisA[ax_id].Prm->PnCtrlCfgPrm.PluseInCnt =  AxisA[ax_id].BaseLoops->Enc->V1.MotPos; 
    }     
     
    #if NO_ETHERCAT
    if(!(FunctionSw &  FUNC_DC_SET))
    {
    if(FALSE != edsCompCheck(AxisA[0].BaseCtrls->BaseEnable,UnderFlowFlag))
    {
        TaskProcessTime->SACount = 2;
        TaskProcessTime->SBCount = 0;
        TaskProcessTime->SCCount = 0;
        }   
        }
    
    else
    #endif  
    {
      if(TRUE == UnderFlowFlag)
      {
          bsp_clrpwm_state();
      }
    }
     
    slot = TaskProcessTime->SACount & 0x3; 
    
    if(slot == 3)
    {
        for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
        {
                CurrentRefSelect(AxisA[ax_id].BaseLoops, AxisA[ax_id].BaseLoops->CtrlMode == BASE_MODE_VF ); 
        } 
    }
    else if(slot == 0)
    {
        if(TaskProcessTime->SB_exeflg)
        {
            for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
            {
                hApi_PwmDisable(ax_id);
            }		        
            ALMSetGlobalAlarm( ALM_ISR_TIME );          
        }
        else
        {
          hApi_SetTaskBInterrupt();
        } 
    }   
   

    // **** 2300 for two axis cur control **** //
    for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++)
    {
        CUR_LOOP   *pCur = &AxisA[ax_id].BaseLoops->CurLoop;
        Prm402     = AxisA[ax_id].BaseCtrls->Cia402Axis.Objects;
        BaseCtrls  = AxisA->BaseCtrls;
        
        pCur->V.MPIFlag = AxisA[ax_id].MotorIdentify->MotorParamIdent.V.FPStart;
      
        if(AxisA[ax_id].BaseLoops->CtrlMode == BASE_MODE_VF)
        {
            pCur->V.ElecAngle = AxisA[ax_id].BaseLoops->ElecAngleVF;
    
			if((AxisA[ax_id].BaseLoops->Enc->V.PhaseReady) && 
									(pCur->V.MPIFlag == 0))
				pCur->V.ElecAngle = AxisA[ax_id].BaseLoops->Enc->V.ElecAngle;
		}
		else
		{
			pCur->V.ElecAngle = AxisA[ax_id].BaseLoops->Enc->V.ElecAngle; 

			if(AxisA[ax_id].BaseLoops->Bprm->UWVSeq)
			{
				pCur->V.ElecAngle = 0x8000 - pCur->V.ElecAngle;
			}
		}


        /* Current loop calculation */
  
        if(AxisA[ax_id].BaseLoops->CtrlMode == BASE_MODE_VF)
        {
            CurrentLoopExec(AxisA[ax_id].BaseLoops, 1);
        }
        else
        {
            CurrentLoopExec(AxisA[ax_id].BaseLoops, 0);
        }

        
       
                    
        if(AxisA[ax_id].Prm->PnCfgPrm.OpenLoopT == 4)
        {
            /* Update PWM compare value */
            hApi_SetPwmCfg(ax_id, PWM_MTU3_DEAD_CFG, AxisA[ax_id].Prm->PnCfgPrm.PwmTestDuty);
            UINT16 PwmTestDuty = hApi_GetPwmCfg(ax_id, PWM_MTU3_DEAD_CFG);
            hApi_PwmUpdateTest(ax_id, PwmTestDuty);
        }
        else
        {
            /* Update PWM compare value */
            hApi_PwmUpdate(ax_id, pCur->V.TaNumber, pCur->V.TbNumber, pCur->V.TcNumber);
        }


        // Check Phase Oc
        AdetCheckSwOc( AxisA[ax_id].AlmMngr, AxisA[ax_id].CheckAlm, AxisA[ax_id].BaseLoops); 
        // AdetCheckPhaseLack( AxisA[ax_id].AlmMngr, &(AxisA[ax_id].CheckAlm->PhaseChk), AxisA[ax_id].BaseLoops);

    #if NO_ETHERCAT
        if(AxisA[ax_id].BaseCtrls->CtrlModeSet.CtrlMode.b.cm == CTRL_MODE_CSP)
		{
          if(edsGetSyncSts() != PWM_DC_SYNC_DONE  || gPwmDcSync.DcSyncFlag == TRUE)
          {
            if(AxisA[ax_id].BaseCtrls->CmdEnable)
            {
                 Prm402->PositionDemandValue0x6062 = Prm402->TargetPosition0x607A;	 
            }
            gPwmDcSync.UpdateFlag = 0;
          }
		}
        else if (AxisA[ax_id].BaseCtrls->CtrlModeSet.CtrlMode.b.cm == CTRL_MODE_CSV)
        {
           if(edsGetSyncSts() != PWM_DC_SYNC_DONE  || gPwmDcSync.DcSyncFlag == TRUE)
          {
            if(AxisA[ax_id].BaseCtrls->CmdEnable)
            {
                 Prm402->VelocityDemandValue0x606B = Prm402->TargetVelocity0x60FF;
            }
            gPwmDcSync.UpdateFlag = 0;
          }          
        }
        else if (AxisA[ax_id].BaseCtrls->CtrlModeSet.CtrlMode.b.cm == CTRL_MODE_CST)
        {
          if(edsGetSyncSts() != PWM_DC_SYNC_DONE  || gPwmDcSync.DcSyncFlag == TRUE)
          {
            if(AxisA[ax_id].BaseCtrls->CmdEnable)
            {
                 Prm402->TorqueDemand0x6074 = Prm402->TargetTorque0x6071; 
            }
            gPwmDcSync.UpdateFlag = 0;
          }         
        }  
        #endif
    }
     
    #if NO_ETHERCAT
    edsClrDcSyncFlag();    
    #endif
    #if NO_MODBUS 
    // modbus 
    ModbusSchedual(&ModbusStruct);
    #endif
    
    // Data Collection
    DtrcExecute(AxisA[0].TrcHndl);
    

    // Alarm Data Cache
    for(ax_id = 0; ax_id < MAX_AXIS_NUM; ax_id++) 
    {
       TvcExecute(AxisA[ax_id].TvcLoop);
    }
    
   
    /* Task Process time calculation */
    TaskProcessTime->SACount++; 
    

    TaskProcessTime->SA_crtTime = hApi_GetTimerCounter() - IsrStartTime;
    TaskProcessTime->SA_sumTime += TaskProcessTime->SA_crtTime;
    if( TaskProcessTime->SA_crtTime > TaskProcessTime->SA_maxTime)
    {
            TaskProcessTime->SA_maxTime = TaskProcessTime->SA_crtTime;
    }

    if( TaskProcessTime->SB_exeflg == TRUE )
    {
            TaskProcessTime->SAinB_exeTime += TaskProcessTime->SA_crtTime;
    }
    else
    {
            TaskProcessTime->SAinB_exeTime = 0;
    }
    
    if( TaskProcessTime->SC_exeflg == TRUE )
    {
            TaskProcessTime->SAinC_exeTime += TaskProcessTime->SA_crtTime;
    }
    else
    {
            TaskProcessTime->SAinC_exeTime = 0;
    }

     if(TaskProcessTime->SA_maxTime > (UINT32)TASKA_MAX_TIME)
    {
        ALMSetGlobalAlarm( ALM_ISR_TIME );
    }    
}

/*******************************************************************************
 End of function IntTaskA
*******************************************************************************/


