/*
 * DataCacheManager.c
 *
 *  Created on: 
 *      Author: Sorve
 */


#include "DataTraceDef.h"
#include "Global.h"
#include "DataTraceCal.h"
#include "DataCache.h"

#pragma optimize=none 
PUBLIC UINT8 CacheDataUpdate( TVCLOOP *TvcLoop, INT32 Idx )
{
	UINT16 i = 0;
	UINT16 k = 0;
    if(Idx >= CacheDL)
    {
        return 1;
    }
    
	for(i=0;i<8;i++)
	{
        k = TvcLoop->P.Channel[i];
        if((TvcLoop->V.Tvcdp[k].GetVarData)!= NULL && (TvcLoop->V.AxisPtr != NULL))
        {
            TvcLoop->V.Data[i][Idx] =  TvcLoop->V.Tvcdp[k].GetVarData(TvcLoop->V.AxisPtr);
        }
	}
    
//	for(i=1;i<37;i++)
//	{
//		if(i == TvcLoop->P.Channel[k])
//		{
//			if((TvcLoop->V.Tvcdp[i].GetVarData)!= NULL && (TvcLoop->V.AxisPtr != NULL))
//			{
//				TvcLoop->V.Data[k][Idx] =  TvcLoop->V.Tvcdp[i].GetVarData(TvcLoop->V.AxisPtr);
//			}
//			k++;
//			if(k>7)
//			{
//				return 0 ;
//			}
//		}
//	}
    return 0;
}


#pragma optimize=none 
PRIVATE BOOL TvcCheckTriggerProcedure( TVCLOOP *TvcLoop )
{
	INT32	x,y;
	TRCREG	*TrcReg;
	TRCEXEP *TrcExeP;
	TRCEXEV *TrcExeV;
	switch(TvcLoop->P.TriModel)
	{
	   case  0:
		   
         if((TvcLoop->V.Tvcdp[31].GetVarData)!= NULL && (TvcLoop->V.AxisPtr != NULL))
			{
			   if(TvcLoop->V.Tvcdp[31].GetVarData(TvcLoop->V.AxisPtr) == TRUE)
			   {
				   return TRUE;	   
			   }
			}
		   break;
	   case  1:
		   
		   if(((TvcLoop->V.Tvcdp[32].GetVarData)!= NULL && (TvcLoop->V.AxisPtr != NULL)))
		   {
			   if(TvcLoop->V.Tvcdp[32].GetVarData(TvcLoop->V.AxisPtr) == TRUE)
			   {
				   return TRUE;	   
			   }		   
		   }
       case  2:
		  
		   //todo
		   break;
	}
	 return 0;	
	
	
	
	
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE UINT16 TvcInitPrmCheck(  TVCLOOP *TvcLoop, UINT16 SmpTime)
{
  UINT16 rc = 0;
  
  if(SmpTime == 0xFFFF || SmpTime > 128)
  {
     // 
    rc = 1;
  }
  
  for(UINT16 i =0;i< 8;i++)
  { 
      if(TvcLoop->P.Channel[i] == 0xFFFF || TvcLoop->P.Channel[i] >= 37)
      {
         rc = 1;
         
         break;
        
      }
  }
  
  return rc;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC UINT16 TvcInit(  TVCLOOP *TvcLoop ,UINT32 Ch1Setting, UINT32 Ch2Setting,UINT16 SmpTime)
{
  
    UINT16 BitShift = 0;
    UINT16 rc = 0;
    
    if(1 == TvcInitPrmCheck(TvcLoop,SmpTime))
    {
      SmpTime = 2;
      Ch1Setting = 0x08070524;
      Ch2Setting = 0x16231311;
      rc = 1;
    }

    TvcLoop->P.SamplePreiod = SmpTime;

    TvcLoop->P.TrcDCnum = 10;
    TvcLoop->P.TriModel = 0;
    
    for(UINT16 i =0;i< 8;i++)
    {
      if(i< 4)
      {
        BitShift = i*8;
        
        TvcLoop->P.Channel[i] = (UINT16)((Ch1Setting >> BitShift) & 0x000000FF);
      }
      else
      {
         BitShift = (i-4)*8;
         TvcLoop->P.Channel[i] =(UINT16)((Ch2Setting >> BitShift) & 0x000000FF);
      }
    }    
    
	TvcLoop->V.TrcStep = 1;
    
    TvcLoop->V.TrcUpload.SaveDnum = CacheDL * 8 *4;    //size of uint8_t
    TvcLoop->V.TrcUpload.FrameCNT = 0;
    TvcLoop->V.TrcUpload.WriteDnum = 0;
    TvcLoop->V.TrcUpload.ChannelId = 0;
    TvcLoop->V.TrcUpload.DataIndex = 0;
    
    
    return rc;  
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
#pragma optimize=none 
PUBLIC void TvcExecute(  TVCLOOP *TvcLoop )
{
	
	
//	if(TvcLoop->V.FinishFlay)
//	{
//		TvcLoop->V.TrcStep = 1;
//		TvcLoop->V.TrcSamp = 0;
//		return;
//	}
	
	
	if( (++TvcLoop->V.TrcSamp >= TvcLoop->P.SamplePreiod)  )
	{
		TvcLoop->V.TrcSamp = 0;
	}
	else
	{
		return;
	}
	
	switch( TvcLoop->V.TrcStep )
	{
		/* Data Trace for PreTrigger */
		case  1:
			//
			TvcLoop->V.Mnum++;
			if(TvcLoop->V.Mnum >= CacheDL)
				TvcLoop->V.Mnum = 0;
			CacheDataUpdate(TvcLoop,TvcLoop->V.Mnum);
			
			//
			if(TvcCheckTriggerProcedure( TvcLoop ) == TRUE)
            {
                TvcLoop->V.TrcStep = 2;
				TvcLoop->V.TrcCnt = 0;
 
			}

			break;

		/* Data Trace and Wait Trigger */
		case  2:
	        // 
			TvcLoop->V.Mnum++;
			if(TvcLoop->V.Mnum >= CacheDL)
				TvcLoop->V.Mnum = 0;
			CacheDataUpdate(TvcLoop,TvcLoop->V.Mnum);
			
			TvcLoop->V.TrcCnt++;
			if(TvcLoop->V.TrcCnt > TvcLoop->P.TrcDCnum)
			{
				TvcLoop->V.TrcCnt = 0;
				TvcLoop->V.TrcStep = 3;
				TvcLoop->V.Front = TvcLoop->V.Mnum+1;
                if(TvcLoop->V.Front >= CacheDL)
                {
                    TvcLoop->V.Front=0;
                }
				TvcLoop->V.FinishFlay = 1;
				TvcLoop->V.ReStare = 0;
			}
			break;
		case  3:
	


			if(TvcLoop->V.ReStare)
			{
				TvcLoop->V.ReStare = 0;
				TvcLoop->V.TrcCnt = 0;
				TvcLoop->V.TrcStep = 1;
				TvcLoop->V.FinishFlay = 0;
                
                TvcLoop->V.TrcUpload.bSendEn = 0;
                TvcLoop->V.TrcUpload.ChannelId = 0;
                TvcLoop->V.TrcUpload.DataIndex = 0;
                TvcLoop->V.TrcUpload.FrameCNT = 0;
                TvcLoop->V.TrcUpload.SaveDnum = CacheDL * 8 *4;    //size of uint8_t
                TvcLoop->V.TrcUpload.WriteDnum = 0;
                
			}
			break;
		default:
			TvcLoop->V.TrcStep = 1;
			break;	
	}
	
}
















