  /****************************************************************************************************
 *
 * FILE NAME:  InertiaIdent.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.03.16
 *
 * AUTHOR:      Xuxiao
 *
 * History:
 ****************************************************************************************************
	16-03-2020 Version 1.00 : Created by Xuxiao
****************************************************************************************************/
#include "MotorIdentification.h"
#include "Mlib.h"
#include "Global.h"
#include "FunManager.h"

#define ReachSpdErr  	43019			// [10 rpm =>> 43019]
#define StartSpdErr  	1290570			// [300 rpm =>> 430190]

/****************************************************************************************************
 * DESCRIPTION:
 *		 Entry function of JOG operation
 * RETURNS:
 *
****************************************************************************************************/
PRM_RSLT FnEnterInertialoperation(FUNEXE *FunExe, AXIS_HANDLE *Axis)
{
	FUN_CMN_CONTROL		*FnCmnCtrl;
    Inertia_FIND  *InertiaIdent = &Axis->MotorIdentify->InertiaIdent;
    
	FnCmnCtrl = Axis->FnCmnCtrl;
        
    if(FnCmnCtrl->State.FnSvCtrlDisable != FALSE)
	{
		return PRM_RSLT_CONDITION_ERR;
	}

	if(FALSE != ALMCheckEachState( Axis->AlmMngr, ALM_PRMUNMATCH ))
	{
		return PRM_RSLT_CONDITION_ERR;
	}
    
    InertiaIdent->V.StartFlag = 0;    
    InertiaIdent->V.InitPos = InertiaIdent->V.PosValue;
    
    InertiaIdent->V.IntegralPos = 0;        
    InertiaIdent->V.RmdPos = 0;
    InertiaIdent->V.RmdVel = 0;        
    InertiaIdent->V.IntegrUp = 0;
    InertiaIdent->V.IntegrDown = 0;
    InertiaIdent->V.RunCNT = 0;
    InertiaIdent->V.Direction = 1;
    InertiaIdent->V.OutputVel = 0;
    InertiaIdent->V.LastLoadInertiaRatio = Axis->Prm->PnCtrlCfgPrm.Jrat;
    InertiaIdent->V.LoadInertiaRatio = Axis->Prm->PnCtrlCfgPrm.Jrat;
    

	FnCmnCtrl->FnSvControl = TRUE;
	FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_INERTIALDENT;
	FnCmnCtrl->FnSensOnReq = TRUE;

	FunExe->HoldFnMode = TRUE;/* No automatic end in Fn mode */

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Execute function of JOG operation
 * RETURNS:
 *
****************************************************************************************************/
PRM_RSLT FnExecuteInertialoperation(FUNEXE *FunExe, AXIS_HANDLE *Axis)
{
	PRM_RSLT		errRes;
	FUN_CMN_CONTROL	*FnCmnCtrl;
	SEQ_CTRL_OUT	*SeqCtrlOut;
	ALARM			*AlmManager;
	PRMDATA			*Prm;
	BPRMDAT			*Bprm;
    Inertia_FIND  *InertiaIdent = &Axis->MotorIdentify->InertiaIdent;

	FnCmnCtrl = Axis->FnCmnCtrl;
	SeqCtrlOut = Axis->SeqCtrlOut;

	AlmManager = Axis->AlmMngr;
	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

	errRes = PRM_RSLT_SUCCESS;

	if(FunExe->State == 0)
	{/* Servo OFF*/
		switch(FunExe->CmdCode)
		{
		case FCMD_SV:	/* Servo ON	*/
			if( (SeqCtrlOut->MainPowerOn == FALSE)
				|| (SeqCtrlOut->HwbbSts != FALSE)
				|| (AlmManager->AlmReady == FALSE) )
			{
				errRes = PRM_RSLT_CONDITION_ERR;
			}
			else
			{
				FnCmnCtrl->FnSvonReq = TRUE;
			}
			break;

		case FCMD_UP:	/* Forward rotation	*/
		case FCMD_DOWN:	/* Reverse rotation	*/
		default:
			break;
		}

		if(SeqCtrlOut->BaseEnableReq != FALSE)
		{
			FunExe->State = 1;
		}
	}
	else
	{/* Servo ON	*/

		switch(FunExe->CmdCode)
		{
            
		case FCMD_UP:	/*  Forward rotation	*/
            InertiaIdent->V.StartFlag = 1;
			KlibRstLongTimer(&FunExe->LongTimer);
			break;

		case FCMD_SV:	/* Servo off */
            InertiaIdent->V.StartFlag = 0;
			FnCmnCtrl->FnSvonReq = FALSE;

			break;
        
		default:
			if(KlibGetLongTimerMs(FunExe->LongTimer)
				> (UINT32)(FnCmnCtrl->SafeStopTime*1000))
			{
				 InertiaIdent->V.StartFlag = 0;
			}
			break;
		}

		if(SeqCtrlOut->BaseEnableReq == FALSE)
		{
			FunExe->State = 0;
		}
	}

	return errRes;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Leave function of JOG operation
 * RETURNS:
 *
****************************************************************************************************/
void FnLeaveInertialoperation(FUNEXE *FunExe, AXIS_HANDLE *Axis)
{
	FUN_CMN_CONTROL	*FnCmnCtrl;

	FnCmnCtrl = Axis->FnCmnCtrl;

    Axis->MotorIdentify->InertiaIdent.V.StartFlag = 0;
	FnCmnCtrl->FnSvonReq = FALSE;
	FnCmnCtrl->FnCtrlMcmd = CTRL_MODE_NOCMD;
	FnCmnCtrl->FnSensOnReq = FALSE;
	FnCmnCtrl->FnSvControl = FALSE;
}


/***********************************************************************
 * DESCRIPTION: Inertia Identification
 *
 * RETURNS:
 *
***********************************************************************/
PUBLIC void InertiaIdentCal(Inertia_FIND* InertiaIdent, BASE_LOOP *P)	
{
	float Ts = 0.00025f;
    INT32 VelAcc,AccRem,VelQuot,VelRem;    
    INT16 Temp;  
    
    if(InertiaIdent->V.StartFlag == 0)
    {     
        InertiaIdent->V.RmdPos = 0;
        InertiaIdent->V.RmdVel = 0;        
        InertiaIdent->V.IntegrUp = 0;
        InertiaIdent->V.IntegrDown = 0;
        InertiaIdent->V.RunCNT = 0;
        InertiaIdent->V.Direction = 1;
        InertiaIdent->V.OutputVel = 0;
        return; 
    }
    
    VelAcc = InertiaIdent->P.Acc / (UINT32)TASKB_FRQ;
	AccRem = InertiaIdent->P.Acc % (UINT32)TASKB_FRQ;
    
    InertiaIdent->V.Iq = P->CurLoop.V.IqFdb;    
    InertiaIdent->V.RunCNT++;
              
    if(InertiaIdent->V.Direction == 1)
    {
        if(InertiaIdent->V.RunCNT < InertiaIdent->V.RunTime)
        {
            InertiaIdent->V.RmdVel  += AccRem;        
            if(InertiaIdent->V.RmdVel  >= TASKB_FRQ)
            {
                InertiaIdent->V.RmdVel  -= TASKB_FRQ;
                VelAcc++;
            }
            
            if(InertiaIdent->V.OutputVel + VelAcc <= InertiaIdent->P.SpeedMax )
            {
                InertiaIdent->V.OutputVel += VelAcc;
            }
            
            InertiaIdent->V.IntegrUp += InertiaIdent->V.Iq;
            InertiaIdent->V.Speed = InertiaIdent->V.OutputVel;
        }			
        else if(InertiaIdent->V.RunCNT < InertiaIdent->V.RunTime*2)
        {
            InertiaIdent->V.RmdVel += AccRem;       
            if(InertiaIdent->V.RmdVel  >= TASKB_FRQ)
            {
                InertiaIdent->V.RmdVel  -= TASKB_FRQ;
                VelAcc++;
            }
            
            if(InertiaIdent->V.OutputVel - VelAcc >= 0)
            {
                InertiaIdent->V.OutputVel -= VelAcc;
            }
            InertiaIdent->V.IntegrDown += InertiaIdent->V.Iq;
        }
        else
        {
            InertiaIdent->V.ForwardInertia =(InertiaIdent->V.IntegrUp - InertiaIdent->V.IntegrDown) 
                                      * Ts * InertiaIdent->P.Kt 												
                                      / ((float)(2*InertiaIdent->V.Speed)
                                          *2*3.14/P->Enc->P.EncPPR ) *10000.0f;	// 2 * 2pi/percnt
        
            InertiaIdent->V.OutputVel = 0;
            InertiaIdent->V.RunCNT = 0;
            InertiaIdent->V.Direction = 0;
            InertiaIdent->V.IntegrUp = 0;
            InertiaIdent->V.IntegrDown = 0;
        }
        
    }
    else if(InertiaIdent->V.Direction == 0)
    {       
        if(InertiaIdent->V.RunCNT < InertiaIdent->V.RunTime)
        {
            InertiaIdent->V.RmdVel += AccRem;       
            if(InertiaIdent->V.RmdVel  >= TASKB_FRQ)
            {
                InertiaIdent->V.RmdVel  -= TASKB_FRQ;
                VelAcc++;
            }
            
            if(InertiaIdent->V.OutputVel - VelAcc >= -(INT32)InertiaIdent->P.SpeedMax)
            {
                InertiaIdent->V.OutputVel -= VelAcc;
            }
            InertiaIdent->V.IntegrUp += InertiaIdent->V.Iq;
            
            InertiaIdent->V.Speed = InertiaIdent->V.OutputVel;
        }		
        else if(InertiaIdent->V.RunCNT < InertiaIdent->V.RunTime*2)
        {
            InertiaIdent->V.RmdVel  += AccRem;        
            if(InertiaIdent->V.RmdVel  >= TASKB_FRQ)
            {
                InertiaIdent->V.RmdVel  -= TASKB_FRQ;
                VelAcc++;
            }
            
            if(InertiaIdent->V.OutputVel + VelAcc <= 0 )
            {
                InertiaIdent->V.OutputVel += VelAcc;
            }

            InertiaIdent->V.IntegrDown += InertiaIdent->V.Iq;
        }
        else 
        {
            InertiaIdent->V.NegativeInertia = (InertiaIdent->V.IntegrUp - InertiaIdent->V.IntegrDown) 
                                      * Ts * InertiaIdent->P.Kt 												
                                      / ((float)(2*InertiaIdent->V.Speed)
                                          *2*3.14/P->Enc->P.EncPPR )*10000.0f;	// 2 * 2pi/percnt
            InertiaIdent->V.IntegrUp = 0;
            InertiaIdent->V.IntegrDown = 0;
            InertiaIdent->V.OutputVel = 0;
            InertiaIdent->V.RunCNT = 0;
            InertiaIdent->V.Direction = 1;
        }
    }
    InertiaIdent->V.Inertia = 0.5f * (InertiaIdent->V.NegativeInertia + InertiaIdent->V.ForwardInertia);
            Temp = (InertiaIdent->V.Inertia - InertiaIdent->P.MotInertia)
                            /InertiaIdent->P.MotInertia *100.0f;
            if(Temp>=0)
            {
               InertiaIdent->V.LoadInertiaRatio = Temp;
            }
            else
            {
               InertiaIdent->V.LoadInertiaRatio = 0;
 
    }
    
    VelQuot = InertiaIdent->V.OutputVel /(INT32)TASKB_FRQ;
    VelRem = InertiaIdent->V.OutputVel - VelQuot*(INT32)TASKB_FRQ;
        
    InertiaIdent->V.RmdPos += VelRem;
    if(InertiaIdent->V.RmdPos >= TASKB_FRQ)
    {
        InertiaIdent->V.RmdPos -= TASKB_FRQ;
        VelQuot++;
    }
    else if(InertiaIdent->V.RmdPos <= -(INT32)TASKB_FRQ)
    {
        InertiaIdent->V.RmdPos += TASKB_FRQ;
        VelQuot--;
    }
                
    InertiaIdent->V.IntegralPos += VelQuot;
    						    
    InertiaIdent->V.LoadInertiaRatio = 0.5f*(InertiaIdent->V.LoadInertiaRatio+InertiaIdent->V.LastLoadInertiaRatio);
    InertiaIdent->V.LastLoadInertiaRatio = InertiaIdent->V.LoadInertiaRatio;
}

/***********************************************************************
 * DESCRIPTION: Inertia Identification online
 *
 * RETURNS:
 *
***********************************************************************/
PUBLIC void InertiaIdentOLCal(Inertia_FIND_OL* InertiaIdentOL, BASE_LOOP *P)	
{
    INT16 TempInertia;
    REAL32 yt,lt[2],ft[2],Pn0[4],theta0[2];
    REAL32 temp1,temp2,temp3,temp4,den;
    
    if(InertiaIdentOL->V.StartFlag)
    {
        InertiaIdentOL->V.IdentCNT++;
        InertiaIdentOL->V.Iq  = P->CurLoop.V.IqFdb;
        InertiaIdentOL->V.Wm0 = P->BaseCtrls->Cia402Axis.Objects->VelocityActualValue0x606C *2*3.14f/(float)P->Enc->P.EncPPR;   
        InertiaIdentOL->V.Te0 = InertiaIdentOL->P.Kt * InertiaIdentOL->V.Iq *0.01f;
        
        InertiaIdentOL->V.SpeedRef = P->SpdLoop.V.SpdRef;
        InertiaIdentOL->V.SpeedFdb = P->SpdFdb;
                
        switch (InertiaIdentOL->V.IdentState)
        {
        case 0:
            if((InertiaIdentOL->V.SpeedRef -  InertiaIdentOL->V.SpeedFdb >= StartSpdErr)
                    ||(InertiaIdentOL->V.SpeedFdb  - InertiaIdentOL->V.SpeedRef >= StartSpdErr))
            {          
               InertiaIdentOL->V.IdentState = 1;
               InertiaIdentOL->V.IdentCNT = 0; 
            }
            break;
        case 1:
            yt = InertiaIdentOL->V.Te1;
            ft[0] = InertiaIdentOL->V.Wm0;
            ft[1] = -InertiaIdentOL->V.Wm1;


            temp1 = InertiaIdentOL->V.Pn1[0] * ft[0];
            temp2 = InertiaIdentOL->V.Pn1[3] * ft[1];
            temp3 = InertiaIdentOL->V.Pn1[1] * ft[1];
            temp4 = InertiaIdentOL->V.Pn1[2] * ft[0];   
        
            // lt = (Pn1*f)/(ft*Pn1*f + lamda)
            den = 1.0f/(ft[0] * (temp1 + temp3) + ft[1] * (temp2 + temp4) 
                        + InertiaIdentOL->P.ForgetFactor);
            lt[0] = (temp1 + temp3) * den;
            lt[1] = (temp2 + temp4) * den;
        
            temp1 = InertiaIdentOL->V.Pn1[0] * ft[0];
            temp2 = InertiaIdentOL->V.Pn1[2] * ft[1];
            temp3 = InertiaIdentOL->V.Pn1[1] * ft[0];
            temp4 = InertiaIdentOL->V.Pn1[3] * ft[1];
        
            // Pn0 = (Pn1-lt*ft*pn1)/lamda
            den = 1.0f/InertiaIdentOL->P.ForgetFactor;
            Pn0[0] = (InertiaIdentOL->V.Pn1[0] - lt[0] * (temp1 + temp2)) * den;
            Pn0[1] = (InertiaIdentOL->V.Pn1[1] - lt[0] * (temp3 + temp4)) * den;
            Pn0[2] = (InertiaIdentOL->V.Pn1[2] - lt[1] * (temp1 + temp2)) * den;
            Pn0[3] = (InertiaIdentOL->V.Pn1[3] - lt[1] * (temp3 + temp4)) * den;
            
            // theta0 = theta1 + K(y - ft*theta0)
            den = yt - ft[0] * InertiaIdentOL->V.theta1[0] 
                     - ft[1] * InertiaIdentOL->V.theta1[1];   
            theta0[0] = InertiaIdentOL->V.theta1[0] + lt[0] *den;
            theta0[1] = InertiaIdentOL->V.theta1[1] + lt[1] *den;
            
            InertiaIdentOL->V.theta1[0] = theta0[0];
            InertiaIdentOL->V.theta1[1] = theta0[1];           
            InertiaIdentOL->V.Pn1[0] = Pn0[0];
            InertiaIdentOL->V.Pn1[1] = Pn0[1];
            InertiaIdentOL->V.Pn1[2] = Pn0[2];
            InertiaIdentOL->V.Pn1[3] = Pn0[3];
            
            if((InertiaIdentOL->V.SpeedRef - InertiaIdentOL->V.SpeedFdb <= ReachSpdErr)
                    &&(InertiaIdentOL->V.SpeedRef - InertiaIdentOL->V.SpeedFdb >= -ReachSpdErr))
            {
                InertiaIdentOL->V.Friction = InertiaIdentOL->V.theta1[0] 
                                                - InertiaIdentOL->V.theta1[1];
                
                // J[kgm^2] = theta * Ts => J[kgm^2] = theta * 0.00025 => J[10^-6 kgm^2] = theta * 250
                InertiaIdentOL->V.Inertia = InertiaIdentOL->V.theta1[1] *250.0f;
               
                TempInertia = (InertiaIdentOL->V.Inertia - InertiaIdentOL->P.MotInertia)
                                /InertiaIdentOL->P.MotInertia *100.0f;
                if(TempInertia>=0)
                {
                   InertiaIdentOL->V.LoadInertiaRatio = TempInertia;
                }
                else
                {
                   InertiaIdentOL->V.LoadInertiaRatio = 0;
                }
                InertiaIdentOL->V.IdentState = 0;
                InertiaIdentOL->V.IdentCNT = 0;
                  
            }
            else if(InertiaIdentOL->V.IdentCNT >= InertiaIdentOL->P.TimeThreshold)
            {
               InertiaIdentOL->V.IdentState = 2;
            }      
            break;
        case 2:              
            InertiaIdentOL->V.IdentState = 0;
            InertiaIdentOL->V.IdentCNT = 0;           
            break;
        }

       InertiaIdentOL->V.Wm1 = InertiaIdentOL->V.Wm0;
       InertiaIdentOL->V.Te1 = InertiaIdentOL->V.Te0;      
    
    }
    
}
