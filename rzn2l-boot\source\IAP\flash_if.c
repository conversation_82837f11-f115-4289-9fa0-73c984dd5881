/**
  ******************************************************************************
  * @file    IAP_Main/Src/flash_if.c 
  * <AUTHOR> Application Team
  * @brief   This file provides all the memory related operation functions.
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by ST under Ultimate Liberty license
  * SLA0044, the "License"; You may not use this file except in compliance with
  * the License. You may obtain a copy of the License at:
  *                             www.st.com/SLA0044
  *
  ******************************************************************************
  */ 

/** @addtogroup STM32H7xx_IAP
  * @{
  */

/* Includes ------------------------------------------------------------------*/
#include "stdint.h"
#include "common.h"
#include "flash_if.h"
#include "cmd_flash.h"
/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/

/* Private functions ---------------------------------------------------------*/


/**
  * @brief  This function does an erase of all user flash area
  * @param  StartSector: start of user flash area
  * @retval 0: user flash area successfully erased
  *         1: error occurred
  */
uint32_t FLASH_If_Erase(uint32_t Startaddr, uint32_t Endaddr)
{

  uint32_t ret = 0;
#if 0    
    
  uint32_t Saddr  = Startaddr;
  uint32_t Eaddr  = Endaddr;
   uint32_t  sector_Erase_len   = 0U;   

  float     Bn = (Endaddr - Startaddr)/ERASE_BLOCK_SIZE;
  uint32_t  BlockNum = (Endaddr - Startaddr)/ERASE_BLOCK_SIZE;
  
  
  if(Bn > BlockNum)
  {
    BlockNum = BlockNum + 1;
  }
  
  for(uint32_t i = 0;i < BlockNum;i++)
  {
    ret    =  cmd_erase_flash(Startaddr, ERASE_BLOCK_SIZE);
    
    Startaddr = Startaddr + ERASE_BLOCK_SIZE;
    
    if(ret != 0)
    {
      break;
    }
  }
  
#endif   
  
  return (ret);
}

/**
  * @brief  This function writes a data buffer in flash (data are 32-bit aligned).
  * @note   After writing data buffer, the flash content is checked.
  * @param  FlashAddress: start address for writing data buffer
  * @param  Data: pointer on data buffer
  * @param  DataLength: length of data buffer (unit is 32-bit word)   
  * @retval 0: Data successfully written to Flash memory
  *         1: Error occurred while writing data in Flash memory
  *         2: Written Data in flash memory is different from expected one
  */

uint32_t FLASH_If_Erase_Sector(void)
{
  uint32_t ret = 0;
  
#if 0
  uint32_t UpgradeFlag = 0;

  UpgradeFlag = *(uint32_t *)UpgradeMark_ADDRESS_START;
  
   if(UpgradeFlag != 0xFF)
  {
    ret = FLASH_If_Erase(APPLICATION_ADDRESS1_START,APPLICATION_ADDRESS1_END);
  }
  else
  {
    ret = FLASH_If_Erase(APPLICATION_ADDRESS2_START,APPLICATION_ADDRESS2_END);
  }
#endif   
  
  return (ret);
}

/**
  * @brief  This function writes a data buffer in flash (data are 32-bit aligned).
  * @note   After writing data buffer, the flash content is checked.
  * @param  FlashAddress: start address for writing data buffer
  * @param  Data: pointer on data buffer
  * @param  DataLength: length of data buffer (unit is 32-bit word)   
  * @retval 0: Data successfully written to Flash memory
  *         1: Error occurred while writing data in Flash memory
  *         2: Written Data in flash memory is different from expected one
  */
uint32_t FLASH_If_Write(uint32_t FlashAddress, uint8_t* pData ,uint32_t DataLength)
{
  uint8_t ret;
  
 /* Unlock the Flash to enable the flash control register access *************/ 
  ret = cmd_write_flash(FlashAddress, pData, DataLength);
  
  return ret;
}


/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
