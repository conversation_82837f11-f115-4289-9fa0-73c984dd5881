/*
* Copyright (c) 2020 - 2024 Renesas Electronics Corporation and/or its affiliates
*
* SPDX-License-Identifier: BSD-3-Clause
*/

/***********************************************************************************************************************
 * Includes   <System Includes> , "Project Includes"
 **********************************************************************************************************************/
#include "bsp_api.h"

/***********************************************************************************************************************
 * Macro definitions
 **********************************************************************************************************************/

/***********************************************************************************************************************
 * Typedef definitions
 **********************************************************************************************************************/

/***********************************************************************************************************************
 * Exported global variables (to be accessed by other files)
 **********************************************************************************************************************/

/***********************************************************************************************************************
 * Private global variables and functions
 **********************************************************************************************************************/

/*******************************************************************************************************************//**
 *        This assembly language routine takes roughly 4 cycles per loop. 2 additional cycles
 *        occur when the loop exits. The 'naked' attribute  indicates that the specified function does not need
 *        prologue/epilogue sequences generated by the compiler.
 * @param[in]     loop_cnt  The number of loops to iterate.
 **********************************************************************************************************************/
BSP_ATTRIBUTE_STACKLESS void r_bsp_software_delay_loop (__attribute__((unused)) uint32_t loop_cnt)
{
    __asm volatile ("sw_delay_loop:         \n"

#if defined(__ICCARM__) || defined(__ARMCC_VERSION)
                    "   subs r0, #1         \n"   ///< 1 cycle
#elif defined(__GNUC__)
                    "   sub r0, r0, #1      \n"   ///< 1 cycle
#endif

                    "   cmp r0, #0          \n"   ///< 1 cycle

                    "   bne sw_delay_loop   \n"   ///< 2 cycles

                    "   bx lr               \n"); ///< 2 cycles
}
