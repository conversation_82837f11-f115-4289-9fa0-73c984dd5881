<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<raConfiguration version="7">
  <generalSettings>
    <option key="#Board#" value="board.rzn2lcustom.xspi1_x1"/>
    <option key="CPU" value="RZN2L"/>
    <option key="Core" value="CR52_0"/>
    <option key="#TargetName#" value="R9A07G084M04GBG"/>
    <option key="#TargetARCHITECTURE#" value="cortex-r52"/>
    <option key="#DeviceCommand#" value="R9A07G084M04"/>
    <option key="#RTOS#" value="_none"/>
    <option key="#pinconfiguration#" value="R9A07G084M04GBG.pincfg"/>
    <option key="#FSPVersion#" value="1.3.0"/>
    <option key="#SELECTED_TOOLCHAIN#" value="iar.arm.toolchain"/>
  </generalSettings>
  <raBspConfiguration/>
  <raClockConfiguration>
    <node id="board.clock.main.freq" option="board.clock.main.freq.25m"/>
    <node id="board.clock.loco.enable" option="board.clock.loco.enable.enabled"/>
    <node id="board.clock.pll0.display" option="board.clock.pll0.display.value"/>
    <node id="board.clock.pll1" option="board.clock.pll1.initial"/>
    <node id="board.clock.pll1.display" option="board.clock.pll1.display.value"/>
    <node id="board.clock.ethernet.source" option="board.clock.ethernet.source.main"/>
    <node id="board.clock.reference.display" option="board.clock.reference.display.value"/>
    <node id="board.clock.loco.freq" option="board.clock.loco.freq.240k"/>
    <node id="board.clock.clma0.enable" option="board.clock.clma0.enable.enabled"/>
    <node id="board.clock.clma0.error" option="board.clock.clma0.error.not_mask"/>
    <node id="board.clock.clma3.error" option="board.clock.clma3.error.not_mask"/>
    <node id="board.clock.clma1.error" option="board.clock.clma1.error.mask"/>
    <node id="board.clock.clma3.enable" option="board.clock.clma3.enable.enabled"/>
    <node id="board.clock.clma1.enable" option="board.clock.clma1.enable.enabled"/>
    <node id="board.clock.clma2.enable" option="board.clock.clma2.enable.enabled"/>
    <node id="board.clock.clma0.cmpl" mul="1" option="_edit"/>
    <node id="board.clock.clma1.cmpl" mul="1" option="_edit"/>
    <node id="board.clock.clma2.cmpl" mul="1" option="_edit"/>
    <node id="board.clock.clma3.cmpl" mul="1" option="_edit"/>
    <node id="board.clock.alternative.source" option="board.clock.alternative.source.loco"/>
    <node id="board.clock.clma0.cmph" mul="1023" option="_edit"/>
    <node id="board.clock.clma1.cmph" mul="1023" option="_edit"/>
    <node id="board.clock.clma2.cmph" mul="1023" option="_edit"/>
    <node id="board.clock.clma3.cmph" mul="1023" option="_edit"/>
    <node id="board.clock.iclk.freq" option="board.clock.iclk.freq.200m"/>
    <node id="board.clock.cpu0clk.mul" option="board.clock.cpu0clk.mul.1"/>
    <node id="board.clock.cpu0clk.display" option="board.clock.cpu0clk.display.value"/>
    <node id="board.clock.ckio.div" option="board.clock.ckio.div.4"/>
    <node id="board.clock.ckio.display" option="board.clock.ckio.display.value"/>
    <node id="board.clock.sci0asyncclk.sel" option="board.clock.sci0asyncclk.sel.1"/>
    <node id="board.clock.sci1asyncclk.sel" option="board.clock.sci1asyncclk.sel.1"/>
    <node id="board.clock.sci2asyncclk.sel" option="board.clock.sci2asyncclk.sel.1"/>
    <node id="board.clock.sci3asyncclk.sel" option="board.clock.sci3asyncclk.sel.1"/>
    <node id="board.clock.sci4asyncclk.sel" option="board.clock.sci4asyncclk.sel.1"/>
    <node id="board.clock.sci5asyncclk.sel" option="board.clock.sci5asyncclk.sel.1"/>
    <node id="board.clock.spi0asyncclk.sel" option="board.clock.spi0asyncclk.sel.1"/>
    <node id="board.clock.spi1asyncclk.sel" option="board.clock.spi1asyncclk.sel.1"/>
    <node id="board.clock.spi2asyncclk.sel" option="board.clock.spi2asyncclk.sel.1"/>
    <node id="board.clock.spi3asyncclk.sel" option="board.clock.spi3asyncclk.sel.1"/>
    <node id="board.clock.pclkshost.display" option="board.clock.pclkshost.display.value"/>
    <node id="board.clock.pclkgptl.display" option="board.clock.pclkgptl.display.value"/>
    <node id="board.clock.pclkh.display" option="board.clock.pclkh.display.value"/>
    <node id="board.clock.pclkm.display" option="board.clock.pclkm.display.value"/>
    <node id="board.clock.pclkl.display" option="board.clock.pclkl.display.value"/>
    <node id="board.clock.pclkadc.display" option="board.clock.pclkadc.display.value"/>
    <node id="board.clock.pclkcan.freq" option="board.clock.pclkcan.freq.40m"/>
    <node id="board.clock.xspi.clk0.freq" option="board.clock.xspi.clk0.freq.12m"/>
    <node id="board.clock.xspi.clk1.freq" option="board.clock.xspi.clk1.freq.12m"/>
    <node id="board.clock.tclk.freq" option="board.clock.tclk.freq.100m"/>
  </raClockConfiguration>
  <raPinConfiguration>
    <pincfg active="true" name="" symbol="">
      <configSetting altId="jtag_fslash_swd.tck_swclk.p02_7" configurationId="jtag_fslash_swd.tck_swclk" isUsedByDriver="true"/>
      <configSetting altId="jtag_fslash_swd.tdi.p02_5" configurationId="jtag_fslash_swd.tdi" isUsedByDriver="true"/>
      <configSetting altId="jtag_fslash_swd.tdo.p02_4" configurationId="jtag_fslash_swd.tdo" isUsedByDriver="true"/>
      <configSetting altId="jtag_fslash_swd.tms_swdio.p02_6" configurationId="jtag_fslash_swd.tms_swdio" isUsedByDriver="true"/>
      <configSetting altId="p03_0.output.toinput.low" configurationId="p03_0"/>
      <configSetting altId="p20_1.output.toinput.low" configurationId="p20_1"/>
      <configSetting altId="p20_2.output.toinput.low" configurationId="p20_2"/>
      <configSetting altId="p20_3.output.toinput.low" configurationId="p20_3"/>
      <configSetting altId="p20_4.output.toinput.low" configurationId="p20_4"/>
      <configSetting altId="usb_hs.usb_vbusin.p07_4" configurationId="usb_hs.usb_vbusin" isUsedByDriver="true"/>
      <configSetting altId="xspi1.xspi1_ckp.p17_7" configurationId="xspi1.xspi1_ckp" isUsedByDriver="true"/>
      <configSetting altId="xspi1.xspi1_cs0_hash.p18_2" configurationId="xspi1.xspi1_cs0_hash" isUsedByDriver="true"/>
      <configSetting altId="xspi1.xspi1_io0.p16_7" configurationId="xspi1.xspi1_io0" isUsedByDriver="true"/>
      <configSetting altId="xspi1.xspi1_io1.p17_0" configurationId="xspi1.xspi1_io1" isUsedByDriver="true"/>
      <configSetting altId="xspi1.xspi1_io2.p17_3" configurationId="xspi1.xspi1_io2" isUsedByDriver="true"/>
      <configSetting altId="xspi1.xspi1_io3.p17_4" configurationId="xspi1.xspi1_io3" isUsedByDriver="true"/>
    </pincfg>
  </raPinConfiguration>
</raConfiguration>
