/****************************************************************************************************
 *
 * FILE NAME:  MemobusIF.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.30
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	30-10-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef	_MEMOBUS_IF_H 
#define _MEMOBUS_IF_H 

#include "BaseDef.h"
#include "RegAccessIF.h"


/****************************************************************************************************/
/*		DEFINES																						*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Channel definition																			*/
/*--------------------------------------------------------------------------------------------------*/
#define	COM2_OPT1		0x01					// CH2 : C1 message										
#define	COM3_OPT2		0x02					// CH3 : C2 message										

/*--------------------------------------------------------------------------------------------------*/
/*	MEMOBUS Message communication header part definition											*/
/*--------------------------------------------------------------------------------------------------*/
#define	MSG_MAXBUFSZB			582				// Buffer size (>= maximum Memobus size)			
#define	MBUS_MINSIZE			8				// MEMOBUS message minimum number of bytes
#define	MBUS_MAXSIZE			480				// Maximum number of bytes of MEMOBUS message (number divisible by INT32)
#define	MBUS_HDROFS				0x00			// Header size (0 bytes)		
#define	MBUS_MIN_REGNUM			1				// Minimum register read/write count


/****************************************************************************************************/
/*		STRUCT DEFINITION																			*/
/****************************************************************************************************/
typedef	struct	
{
	INT32				  AxisAddress;
	REG_MANAGER_HANDLE	*hRegMngr;
	void				  *Axis;		        // Start address for each axis 
} MEMOBUS_ADDR_MAP;

/*--------------------------------------------------------------------------------------------------*/
/*		Data definition for register IF																*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct	
{				
	UINT8	RxBuf[ MSG_MAXBUFSZB ];				// Receive data buffer							
	UINT8	TxBuf[ MSG_MAXBUFSZB ];				// Transmit data buffer								
	UINT8	*CmdBuf;							// Command message buffer pointer					
	UINT8	*ResBuf;							// Response message buffer pointer					
	UINT16	RelayHdSize;						// Relay command header size						
	UINT16	RcvbLength;							// Received data length (in bytes)						
	UINT16	RcvwLength;							// Received data length (in words)						
	UINT16	TrnbLength;							// Transmission data length (in bytes)						
	INT32	NodeAddress;						// Own station address										
	UINT32	ComTimeLong;						// For communication timeout measurement							

	MEMOBUS_ADDR_MAP	AxisMap[MAX_AXIS_NUM];  // Axis address definition									
	
	UINT32	ErrRno;								// Register read: MEMOBUS error information		
	UINT16	OpeDspMode;							// Operator user constant display mode				

} MEMOBUS_IF;



/****************************************************************************************************/
/*		PROTOTYPE																					*/
/****************************************************************************************************/
/* MEMOBUS message processing initialization */
PUBLIC void MbusFuncInitialize(MEMOBUS_IF *MbusIfPtr, MEMOBUS_ADDR_MAP *AxisCfg, INT32 node_address );

/* MEMOBUS message analysis & execution processing	*/
PUBLIC UINT16 MbusFuncMsgProcedure( MEMOBUS_IF *MbusIfPtr );

/* MEMOBUS message buffer Swap processing (for BigEndian) */
PUBLIC void mbusFuncBufSwap( UINT8 *RxBuf );

#endif /* _MEMOBUS_IF_H */

