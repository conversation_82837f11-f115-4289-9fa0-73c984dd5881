/****************************************************************************************************
 *
 * FILE NAME:  BaseLoops.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.13
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	13-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "bsp.h"
#include "modbusdrv.h"

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
void SciReadEnable(void)
{
  // 使用fifo接收
  if(1 == SciClearError())   // 清除错误
  {
    SciResetRxFifo();     //  清空接收fifo
  }

  if(1 != R_SCI4->CCR0_b.RE)
  {
    R_SCI4->CCR0_b.RE = 1;        //使能接收
  }
  R_BSP_PinWrite(BSP_IO_PORT_15_PIN_6,BSP_IO_LEVEL_LOW);
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
void SciReadDisEnable(void)
{
  SciResetRxFifo();   //  清空接收fifo
  // 使用fifo接收
  if(1 == R_SCI4->CCR0_b.RE)
  {
    R_SCI4->CCR0_b.RE = 0;        //使能接收
  }
  
  R_BSP_PinWrite(BSP_IO_PORT_15_PIN_6,BSP_IO_LEVEL_HIGH);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
void SciResetRxFifo(void)
{
  // 使用fifo接收
  UINT16 temp = 0; 
  //R_SCI4->FCR_b.RFRST = 1;     //需要TE和RE都等于0才能写,清空fifo数据

  for(temp = 0; temp < R_SCI4->FRSR_b.R;temp++)
  {
      (void)(R_SCI4->RDR & 0x00FF);
  }  
  
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
void SciSendEnable(UINT8 *pdata, UINT16 Length)
{
  // 使用DMA发送
  sci_sci4_dma_tx(pdata,Length);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
BOOL SciGetTendFlag(void)
{
  // 使用DMA发送
  return R_SCI4->CSR_b.TEND;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
BOOL SciClearError(void)
{
  BOOL ret = 0;
  UINT16 temp = 0;
  
  if(R_SCI4->CSR & SCI_UART_RCVR_ERR_MASK)
  {
    R_SCI4->CFCLR |=  SCI_UART_RCVR_ERRCLR_MASK;
    ret = 1;
  }
  
  return ret;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
UINT16 ReadErrorFlag(void)
{
  if(R_SCI4->CSR & SCI_UART_RCVR_ERR_MASK)
  {
    R_SCI4->CFCLR |=  SCI_UART_RCVR_ERRCLR_MASK;
  }
}


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
UINT16 ReadRxDataNum(void)
{
  return R_SCI4->FRSR_b.R;
}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
UINT8 ReadRxData(void)
{
  return (R_SCI4->RDR_b.RDAT & 0x00FF);
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS: 0- no data in fifo  1-data in fifo
 *
****************************************************************************************************/
BOOL CheckRxState(void)
{
  BOOL ret = 0;
  if(0 != R_SCI4->FRSR_b.R) //fifo 
  {
    ret = 1;
  }
  return ret;
}