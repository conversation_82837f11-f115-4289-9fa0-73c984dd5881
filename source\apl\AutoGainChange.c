/****************************************************************************************************
 *
 * FILE NAME:  AutoGainChange.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.07.24
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	24-07-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "BaseDef.h"
#include "GainChange.h"
#include "Mlib.h"

/****************************************************************************************************/
/*																									*/
/*		Macro Definition																			*/
/*																									*/
/****************************************************************************************************/
#if 0

#define _GainLauFilter( pact, pnxt, pat, ks )                             							\
		(pact)->ks = GainLauFilter( (pact)->ks, (pnxt)->ks, (pat)->conf.dGain.ks )
#endif
/*--------------------------------------------------------------------------------------------------*/
#define _FilGnLauFilter( pact, pnxt, pat, ks )                             							\
		(pact)->ks = FilGnLauFilter( (pact)->ks, (pnxt)->ks, (pat)->conf.dGain.ks,			\
						(pat)->conf.GselTrqFilMin )
/*--------------------------------------------------------------------------------------------------*/
#define _GainLauFilterD( pact, pnxt, pat, ks )                             							\
		(pact)->ks = FilGnLauFilter( (pact)->ks, (pnxt)->ks, (pat)->conf.dGainDobs.ks , 	\
						(pat)->conf.GselTrqFilMin )
/*--------------------------------------------------------------------------------------------------*/
#define _DeltaFilGnCalc( pat, gselbuf, ks)  												\
		gselbuf.ks = DeltaFilGnCalc( (pat)->var.prevGain->ks, (pat)->var.nextGain->ks,		\
						(pat)->conf.SwTime, (pat)->conf.GselTrqFilMin, (pat)->conf.SvCycleUs )
/*--------------------------------------------------------------------------------------------------*/
#if 0
#define _DeltaGainCalc( pat, gselbuf, ks )  														\
		gselbuf.ks = DeltaGainCalc( (pat)->var.prevGain->ks, (pat)->var.nextGain->ks, 		\
						(pat)->conf.SwTime, (pat)->conf.SvCycleUs )
#endif
/*--------------------------------------------------------------------------------------------------*/

/****************************************************************************************************/
/*																									*/
/*		Function Prototypes 																		*/
/*																									*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Public Functions																			*/
/*--------------------------------------------------------------------------------------------------*/
PUBLIC void GselInitAutoGainChange( ATGSEL *pAtGsel,		/* Initialize Auto Gain Change			*/
			GSELGAINS	*prevGain,						/* prevGain 								*/
			GSELGAINS	*nextGain,						/* nextGain 								*/
			GSELDOBS	*prevGainDobs,					/* prevGain(Dobs)							*/
			GSELDOBS	*nextGainDobs );				/* nextGain(Dobs)							*/
/*--------------------------------------------------------------------------------------------------*/
PUBLIC void GselAutoGainChange( 						/* Auto Gain Change Main Function			*/
			BASE_CTRL  *BaseControls,				    /*											*/
			INT32 trigger ); 							/*											*/
/*--------------------------------------------------------------------------------------------------*/
PUBLIC void GselRstAutoGainChange(						/* Reset Auto Gain Change					*/
			GAIN_CHNG_HNDL	*pGainChange ); 			/*											*/
/*--------------------------------------------------------------------------------------------------*/
PUBLIC void PcalAutoGainChange( 						/* Auto Gain Change Parameter Calculation    */
			ATGSEL *pAtGsel,							/* Auto Gain Change Variable				*/
			INT32 swtime,								/* Gain Switch Time (Pn131/Pn132)[ms]		*/
			INT32 dlytime,								/* Change Delay Time (Pn135/Pn136)[ms]		*/
			INT32 svcycleus );							/* Execute Cycle [us]						*/
/*--------------------------------------------------------------------------------------------------*/
PUBLIC void PcalAtGseldKpSet(					
			ATGSEL *pAtGsel,							/* Auto Gain Change Variable				*/
			GSELGAINS *dstGain );						/* Destination Gains						*/
/*--------------------------------------------------------------------------------------------------*/
PUBLIC void PcalAtGseldKviSet(						
			ATGSEL *pAtGsel,							/* Auto Gain Change Variable				*/
			GSELGAINS *dstGain );						/* Destination Gains						*/
/*--------------------------------------------------------------------------------------------------*/
PUBLIC void PcalAtGseldKlpfSet( 							
			ATGSEL *pAtGsel,							/* Auto Gain Change Variable				*/
			GSELGAINS *dstGain );						/* Destination Gains						*/
/*--------------------------------------------------------------------------------------------------*/
PUBLIC void PcalAtGseldKsSet(							
			ATGSEL *pAtGsel,							/* Auto Gain Change Variable				*/
			GSELDOBS *dstGain );						/* Destination Gains						*/
/*--------------------------------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------*/
/*		Private Functions																			*/
/*--------------------------------------------------------------------------------------------------*/
PRIVATE void LpxDeltaGainChange(						/* delta gain calc. for auto gain change	*/
				ATGSEL *pAtGsel,						/* Auto Gain Change Variables				*/
				GSELGAINS *pActGains ); 				/* Auto Gain Change Actual Gains			*/
/*--------------------------------------------------------------------------------------------------*/
PRIVATE void LpxDeltaGainDobs(							/* delta gain calc. for Dist. Observer		*/
				ATGSEL *pAtGsel,						/* Auto Gain Change Variables				*/
				GSELDOBS *pActDobs );					/* Auto Gain Change Actual Gains			*/
/*--------------------------------------------------------------------------------------------------*/
PRIVATE void LpxDeltaAmonGain(							/* delta gain calc. for Analog Monitor		*/
				ATGSEL *pAtGsel,						/* Auto Gain Change Variables				*/
				INT32 *pActGain, 						/* Auto Gain Change Actual Gain 			*/
				INT32 PrvGain,							/* Auto Gain Change Previous Gain			*/
				INT32 NxtGain ); 						/* Auto Gain Change Next Gain				*/
/*--------------------------------------------------------------------------------------------------*/
PRIVATE INT32 GainLauFilter(							/* Gain straight line switching calculation	*/
				INT32 Src,								/* source gain								*/
				INT32 Dst,								/* destination gain 						*/
				INT32 Delta );							/* delta gain								*/
/*--------------------------------------------------------------------------------------------------*/
PRIVATE REAL32 FilGnLauFilter( 							/* Filter gain linear switching calculation	*/
				REAL32 Src,								/* source gain								*/
				REAL32 Dst,								/* destination gain 						*/
				REAL32 Delta, 							/* delta gain								*/
				REAL32 Min ); 							/* minimum gain 							*/
/*--------------------------------------------------------------------------------------------------*/
PRIVATE INT32 DeltaGainCalc(							/* Delta gain calculation					*/
				INT32 prev_gn,							/* previous gain							*/
				INT32 next_gn,							/* next gain								*/
				USHORT time,							/* switch time [ms] 						*/
				INT32 SvCycleUs );						/* cycle time [us]							*/
/*--------------------------------------------------------------------------------------------------*/
PRIVATE REAL32 DeltaFilGnCalc( 							/* Delta filter gain calculation			*/
				REAL32 prev_gn,							/* previous gain							*/
				REAL32 next_gn,							/* next gain								*/
				UINT16 time,							/* switch time [ms] 						*/
				REAL32 Min,								/* minimum gain 							*/
				INT32 SvCycleUs );						/* cycle time [us]							*/


/****************************************************************************************************
 * DESCRIPTION:
 *		 Initialize Auto Gain Change 
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void GselInitAutoGainChange( ATGSEL *pAtGsel,		/* Initialize Auto Gain Change			*/
			GSELGAINS	*prevGain,						/* prevGain 								*/
			GSELGAINS	*nextGain,						/* nextGain 								*/
			GSELDOBS	*prevGainDobs,					/* prevGain(Dobs)							*/
			GSELDOBS	*nextGainDobs ) 				/* nextGain(Dobs)							*/
{
/*--------------------------------------------------------------------------------------------------*/
	MlibResetLongMemory( &(pAtGsel->var), sizeof(pAtGsel->var)/4 ); 	   /* reset memory			*/
/*--------------------------------------------------------------------------------------------------*/
	pAtGsel->var.prevGain = prevGain;									   /* set previous gain 	*/
	pAtGsel->var.nextGain = nextGain;									   /* set next gain 		*/
	pAtGsel->var.prevGainDobs = prevGainDobs;							   /* set previous gain 	*/
	pAtGsel->var.nextGainDobs = nextGainDobs;							   /* set next gain 		*/
/*--------------------------------------------------------------------------------------------------*/
	pAtGsel->conf.GselTrqFilMin = FlibPcalKf1gain( 10, PS_CYCLEUS, 0 );		 /* set minimum			*/
/*--------------------------------------------------------------------------------------------------*/
}

/****************************************************************************************************
 * DESCRIPTION:
 *		 Auto Gain Change Main Function
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void GselAutoGainChange( BASE_CTRL *BaseControls,  INT32 trigger )							
{
	ATGSEL	*pAtGsel;
	GSELGAINS *pActGains;
	GSELDOBS *pActDobs;
	INT32	*pActAmon;

	GAIN_CHNG_HNDL *hGainChg	= &(BaseControls->GainChange);
	MFCTRL *pMfcData			= &(BaseControls->MFControl);
	CTRL_CMD_PRM *CtrlCmdPrm	= &(BaseControls->CtrlCmdPrm);

	pActGains = &(hGainChg->GselGains[0]);
	pActDobs = &(hGainChg->GselDobs[0]);
	pActAmon = &(hGainChg->AmonActGain);

	/* Second gain transition or second gain is being selected when condition A is met*/
	if ( trigger )
	{
		pAtGsel = &(hGainChg->AtGselA); 						// 1st -> 2nd gain switching	
		if ( pAtGsel->var.Timer >= pAtGsel->conf.Wait ) 		// After the 1st -> 2nd gain waiting time has elapsed
		{
			LpxDeltaGainChange( pAtGsel, pActGains );			/* delta gain change				*/
			LpxDeltaGainDobs( pAtGsel, pActDobs );				/* delta gain change (DISOBS)		*/
			LpxDeltaAmonGain( pAtGsel, pActAmon, GAIN1, GAIN2 );   /* delta gain change (AMON) 		*/
			hGainChg->ActGain = 2;								/* set monitor						*/
			hGainChg->AtGselB.var.Timer = 0;					/* Wait time 2 timer initialization	*/
		}
		else
		{
			(pAtGsel->var.Timer)++; 							/* Wait time 1 timer count up 		*/
		}
	}
	/* The first gain transition or the first gain is being selected when condition B is met.*/
	else
	{
		pAtGsel = &(hGainChg->AtGselB); 						// 2nd -> 1st gain switching		
		if ( pAtGsel->var.Timer >= pAtGsel->conf.Wait ) 		// After the 2nd -> 1st gain wait time has elapsed
		{
			LpxDeltaGainChange( pAtGsel, pActGains );			/* delta gain change				*/
			LpxDeltaGainDobs( pAtGsel, pActDobs );				/* delta gain change (DISOBS)		*/
			LpxDeltaAmonGain( pAtGsel, pActAmon, GAIN2, GAIN1 );   /* delta gain change (AMON) 		*/
			hGainChg->ActGain = 1;								/* set monitor						*/
			hGainChg->AtGselA.var.Timer = 0;					/* Wait time 1 timer initialization	*/
		}
		else
		{
			(pAtGsel->var.Timer)++; 							/* Wait time 2 timer count up		*/
		}
	}

	/* When the condition is neither A nor B, the previous gain remains the same.*/
	/* MFC does not have automatic gain switching. 1st gain remains*/
	/*----------------------------------------------------------------------------------------------*/
	pMfcData->conf.MfcPrm[0] = pMfcData->conf.MfcPrm[1];

	/*----------------------------------------------------------------------------------------------*/
	/* Speed limit gain does not have automatic gain switching. 1st gain remains 					*/
	/*----------------------------------------------------------------------------------------------*/
	CtrlCmdPrm->OverTrqLoopGain[0]	= CtrlCmdPrm->OverTrqLoopGain[1];
	CtrlCmdPrm->OverTrqIntegGain[0] = CtrlCmdPrm->OverTrqIntegGain[1];
/*--------------------------------------------------------------------------------------------------*/
	return;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		 Delta Gain Change
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void GselRstAutoGainChange(		GAIN_CHNG_HNDL	*pGainChange )			
{
		pGainChange->AtGselA.var.Timer = 0;
		pGainChange->AtGselB.var.Timer = 0;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Delta Gain Change
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void LpxDeltaGainChange( ATGSEL *pAtGsel, GSELGAINS *pActGains )
{
GSELGAINS *pNxtGains = pAtGsel->var.nextGain;
	/*----------------------------------------------------------------------------------------------*/
	if ( pAtGsel->conf.SwTime != 0 )				/* Delta gain switching 						*/
	{
	/*----------------------------------------------------------------------------------------------*/
	/*	Delta Gain Change																			*/
	/*----------------------------------------------------------------------------------------------*/
		_FilGnLauFilter( pActGains, pNxtGains, pAtGsel, Kp );
		_FilGnLauFilter( pActGains, pNxtGains, pAtGsel, Kv );
		_FilGnLauFilter( pActGains, pNxtGains, pAtGsel, Kvi );
		_FilGnLauFilter( pActGains, pNxtGains, pAtGsel, Kv2 );
		_FilGnLauFilter( pActGains, pNxtGains, pAtGsel, Kv2Inv );
		_FilGnLauFilter( pActGains, pNxtGains, pAtGsel, Klpf );
//		_GainLauFilter( pActGains, pNxtGains, pAtGsel, KpInv );
		_FilGnLauFilter( pActGains, pNxtGains, pAtGsel, VirtualPosKp );
		_FilGnLauFilter( pActGains, pNxtGains, pAtGsel, ZcKp );
	}
	else									
	{
		*pActGains = *pNxtGains;
	}
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Delta Gain Change for the Disturbance Observer
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void LpxDeltaGainDobs(							/* delta gain calc. for Dist. Observer		*/
				ATGSEL *pAtGsel,						/* Auto Gain Change Variables				*/
				GSELDOBS *pActDobs )					/* Auto Gain Change Actual Gains			*/
{
	GSELDOBS *pNxtDobs = pAtGsel->var.nextGainDobs;

	if ( pAtGsel->conf.SwTime != 0 )				
	{
	/*----------------------------------------------------------------------------------------------*/
	/*	Delta Gain Change (Disturbance Observer)													*/
	/*----------------------------------------------------------------------------------------------*/
		_GainLauFilterD( pActDobs, pNxtDobs, pAtGsel, Ks );
		_GainLauFilterD( pActDobs, pNxtDobs, pAtGsel, Ksj );
	}
	else									
	{
		*pActDobs = *pNxtDobs;
	}
	return;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Delta Gain Change for Analog Monitor
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE void LpxDeltaAmonGain(							/* delta gain calc. for Analog Monitor		*/
				ATGSEL *pAtGsel,						/* Auto Gain Change Variables				*/
				INT32 *pActGain, 						/* Auto Gain Change Actual Gain 			*/
				INT32 PrvGain,							/* Auto Gain Change Previous Gain			*/
				INT32 NxtGain )							/* Auto Gain Change Next Gain				*/
{
	INT32	wkx;

	if ( pAtGsel->conf.SwTime != 0 )				
	{
		if ( NxtGain > PrvGain )						/* Forward									*/
		{
			wkx = (*pActGain) + pAtGsel->conf.AmondGain;
			if ( wkx > NxtGain )						/* Limit									*/
			{
				*pActGain = NxtGain;		
			}
			else
			{
				*pActGain = wkx;		
			}
		}
		else											/* Backward 								*/
		{
			wkx = (*pActGain) - pAtGsel->conf.AmondGain;
			if ( wkx < NxtGain )						/* Limit									*/
			{
				*pActGain = NxtGain;		
			}
			else
			{
				*pActGain = wkx;		
			}
		}
	}
	else										
	{
		*pActGain = NxtGain;
	}
	return;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Gain straight line switching calculation
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 GainLauFilter( INT32 Src, INT32 Dst, INT32 Delta )
{
	INT32	wrk;
	INT32	sft;
	INT32	kx;
	INT32	sx;
	INT32	sdx;
	INT32	RetGain;
	INT32	wrk_Src;

	if ( ( Src == Dst ) || ( Delta == 0 ) )
	{
		RetGain = Dst;
	}
	else
	{
		sft = ( ( Src >> 24 ) - ( Delta >> 24 ) );
		
		sx = ( Src >> 24 );
		sdx = ( Dst >> 24 );
		if(sft <= 0)
		{
			wrk = ( ( Delta & 0x00FFFFFF ) >> (-sft) );
		}
		else
		{
			wrk_Src = ( ( Src & 0x00FFFFFF ) >> sft );
			sx = Delta >> 24;
			Src = ( ( sx << 24 ) | wrk_Src );
			wrk = Delta & 0x00FFFFFF;
		}
		
		if( wrk == 0 )	{	wrk = 1;	}
		
		if ( ( sx < sdx ) || ( ( sx == sdx ) && ( Src > Dst ) ) )
		{
			kx = ( ( Src & 0x00FFFFFF ) - wrk );
			
			if ( (kx < 0x400000) && (sx < sdx) )
			{
				kx <<= 1;
				sx += 1;
			}
			RetGain = ( ( sx << 24 ) | kx );
			
			if ( ((sx == sdx) && (RetGain <= Dst)) || (kx <= 0) )	RetGain = Dst;
		}
		else
		{
			kx = ( ( Src & 0x00FFFFFF ) + wrk );
			
			if ( kx > 0x7FFFFF )
			{
				kx >>= 1;
				sx -= 1;
			}
			RetGain = ( ( sx << 24 ) | kx );
			
			if ( ((sx == sdx) && (RetGain >= Dst)) || (sx < sdx) )	
				RetGain = Dst;
		}
	}

	return( RetGain );
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Filter gain linear switching calculation 
 * 		The minimum time constant parameter was used as an argument
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 FilGnLauFilter( REAL32 Src, REAL32 Dst, REAL32 Delta, REAL32 Min )
{
	REAL32	wrk;
	REAL32	kx;
	REAL32	sx;
	REAL32	RetGain;
	REAL32	wrk_Dst;

	if( Src == Dst )
	{
		RetGain = Dst;
	}
	else
	{
		/* Add processing when Src = 0, Dst = 0 */
		/* When the filter gain is 0, the calculation in this is performed using the gain when the minimum time constant (0.01ms) is set.*/
		if( (Src ) == 0 )
		{
			Src = Min;
		}
		wrk_Dst = Dst;
		if( (Dst) == 0 )
		{
			Dst = Min;
		}

		wrk = ( Delta);
		
		if( wrk == 0 )	{	wrk = 0.0000001f;	}
		
		if ( Src > Dst )
		{
			RetGain = ( ( Src  ) - wrk );
		
			if ( RetGain <= Dst )	RetGain = Dst;
		}
		else
		{
			RetGain = ( ( Src  ) + wrk );
			
			if( RetGain >= Dst )
			{
				if( (wrk_Dst ) == 0 )	/* If Dst = 0, also set RetGain to 0  */
				{
					RetGain = wrk_Dst;
				}
				else
				{
					RetGain = Dst;
				}
			}
		}
	}

	return( RetGain );
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Automatic gain switching parameter calculation
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcalAutoGainChange( 						/* Auto Gain Change Parameter Calculation	*/
				ATGSEL *pAtGsel,						/* Auto Gain Change Variable				*/
				INT32 swtime,							/* Gain Switch Time (PnXXX/PnXXX)[ms]		*/
				INT32 dlytime,							/* Change Delay Time (PnXXX/PnXXX)[ms]		*/
				INT32 svcycleus )						/* Execute Cycle [us]						*/
{
		/* set parameter*/
		pAtGsel->conf.SwTime = swtime;								/* set gain switch time 		*/
		pAtGsel->conf.SvCycleUs = svcycleus;						/* set cycle					*/
		pAtGsel->conf.Wait = dlytime * 1000 / svcycleus;			/* set wait time				*/

		/* set delta gain */
		PcalAtGseldKpSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Kp			*/
		PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Kv, Kvi		*/
		PcalAtGseldKlpfSet( pAtGsel, &(pAtGsel->conf.dGain) );		/* calculate delta Klpf 		*/
		PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate delta Ks			*/

		/* set analog monitor dgain */
		if ( swtime != 0 )
		{
			pAtGsel->conf.AmondGain = ( GAIN1 * svcycleus ) / ( 1000 * swtime );
		}
		else
		{
			pAtGsel->conf.AmondGain = GAIN1;
		}

		return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Kp gain calculation for automatic gain switching
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcalAtGseldKpSet( ATGSEL *pAtGsel, GSELGAINS *dstGain )
{
GSELGAINS GselGbufx;

	_DeltaFilGnCalc( pAtGsel, GselGbufx, Kp );
	_DeltaFilGnCalc( pAtGsel, GselGbufx, ZcKp );
//	_DeltaGainCalc( pAtGsel, GselGbufx, KpInv );
	_DeltaFilGnCalc( pAtGsel, GselGbufx, VirtualPosKp );

	dstGain->Kp = GselGbufx.Kp;
	dstGain->ZcKp = GselGbufx.ZcKp;
//	dstGain->KpInv = GselGbufx.KpInv;
	dstGain->VirtualPosKp = GselGbufx.VirtualPosKp;

	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Kv and Kvi gain calculation at automatic gain switching
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcalAtGseldKviSet( ATGSEL *pAtGsel, GSELGAINS *dstGain )
{
	GSELGAINS GselGbufx;

	_DeltaFilGnCalc( pAtGsel, GselGbufx, Kv );
	_DeltaFilGnCalc( pAtGsel, GselGbufx, Kvi );
	_DeltaFilGnCalc( pAtGsel, GselGbufx, Kv2 );
	_DeltaFilGnCalc( pAtGsel, GselGbufx, Kv2Inv );

	dstGain->Kv = GselGbufx.Kv;
	dstGain->Kvi = GselGbufx.Kvi;
	dstGain->Kv2 = GselGbufx.Kv2;
	dstGain->Kv2Inv = GselGbufx.Kv2Inv;

	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Kipf gain calculation for automatic gain switching
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcalAtGseldKlpfSet( ATGSEL *pAtGsel, GSELGAINS *dstGain )
{
	dstGain->Klpf = DeltaFilGnCalc( 
				pAtGsel->var.prevGain->Klpf, 
				pAtGsel->var.nextGain->Klpf, 
				pAtGsel->conf.SwTime,
				pAtGsel->conf.GselTrqFilMin,
				pAtGsel->conf.SvCycleUs );
	return;
}


/****************************************************************************************************
 * DESCRIPTION:
 *		Ks and Ksj gain calculation at automatic gain switching
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void PcalAtGseldKsSet( ATGSEL *pAtGsel, GSELDOBS *dstGain )
{
	GSELDOBS GselDobsBufx;

	GselDobsBufx.Ks 	= DeltaFilGnCalc( pAtGsel->var.prevGainDobs->Ks, 
										pAtGsel->var.nextGainDobs->Ks, 
										pAtGsel->conf.SwTime,
                                        0,
										pAtGsel->conf.SvCycleUs );
	GselDobsBufx.Ksj	= DeltaFilGnCalc( pAtGsel->var.prevGainDobs->Ksj, 
										pAtGsel->var.nextGainDobs->Ksj, 
										pAtGsel->conf.SwTime,
                                        0,
										pAtGsel->conf.SvCycleUs );

	dstGain->Ks = GselDobsBufx.Ks;
	dstGain->Ksj = GselDobsBufx.Ksj;

	return;
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Delta gain calculation at automatic gain switching
 *		The calculation cycle was used as an argument
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 DeltaGainCalc( INT32 prev_gn, INT32 next_gn, UINT16 time, INT32 SvCycleUs )
{
	INT32	s;
	INT32	sft;
	INT32	wrk;

	sft = ( ( prev_gn >> 24 ) - ( next_gn >> 24 ) );

	if ( sft < 0 )
	{
		s = ( prev_gn >> 24 );
		wrk = MlibABS( ( (prev_gn & 0x00FFFFFF) - ( (next_gn & 0x00FFFFFF ) >> (-sft) )) );
	}
	else
	{
		s = ( next_gn >> 24 );
		wrk = MlibABS( ( ((prev_gn & 0x00FFFFFF) >> sft ) - (next_gn & 0x00FFFFFF ) ) );
	}
	if ( time == 0 )
	{
		wrk = MlibPcalKxgain( wrk, 1, 1, &s, 24 );
	}
	else
	{
		wrk = MlibPcalKxgain( wrk, SvCycleUs, 1000*time, &s, 24 );
	}
	return( wrk );
}

/****************************************************************************************************
 * DESCRIPTION:
 *		Delta filter gain calculation at automatic gain switching
 *		Minimum time constant and calculation cycle were used as arguments
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE REAL32 DeltaFilGnCalc( REAL32 prev_gn, REAL32 next_gn, UINT16 time, REAL32 Min, INT32 SvCycleUs )
{
	REAL32	sx;
	REAL32	kx;


	if( (prev_gn ) == 0 )
	{
		prev_gn = Min;
	}
	if( (next_gn ) == 0 )
	{
		next_gn = Min;
	}

	kx = MlibABS( ( prev_gn  - next_gn  ) );

	if ( time != 0)
		kx = (SvCycleUs * kx) / ( 1000.0f * time );

	return ( kx );
}



