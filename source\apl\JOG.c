/****************************************************************************************************
 *
 * FILE NAME:  JOG.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.28
 *
 * AUTHOR:      XUXIAO
 *
 * History:
 ****************************************************************************************************
	28-10-2019 Version 1.00 : Created by XUXIAO
****************************************************************************************************/
#include "Cia402Appl.h"
#include  "JOG.h"
#include "Mlib.h"

PUBLIC void JOGExec(TCiA402Axis *Cia402Axis, BOOL BaseEnable )
{
//	CiA402_PRM 			 *Objects = Cia402Axis->Objects;
	JOG_MODE_CTRL		 *pCtrlJog = &Cia402Axis->JOG_Ctrl;

	INT32 VelAcc,AccRem;
	
	VelAcc = pCtrlJog->P.Acc / (UINT32)TASKB_FRQ;
	AccRem = pCtrlJog->P.Acc % (UINT32)TASKB_FRQ;

    if(pCtrlJog->V.UpDownAct == JOGUP)
    {
         pCtrlJog->V.RmdVel += AccRem;
         
         if(pCtrlJog->V.RmdVel >= TASKB_FRQ)
         {
             pCtrlJog->V.RmdVel -= TASKB_FRQ;
             VelAcc++;
         }

         if(pCtrlJog->V.OutputVel + VelAcc <= (INT32)pCtrlJog->P.TargetVel )
         {
             pCtrlJog->V.OutputVel += VelAcc;
         }
         else
         {
             pCtrlJog->V.OutputVel = (INT32)pCtrlJog->P.TargetVel;
         }
    }
    else if(pCtrlJog->V.UpDownAct == JOGDOWN)
    {
         pCtrlJog->V.RmdVel += AccRem;
         
         if(pCtrlJog->V.RmdVel >= TASKB_FRQ)
         {
             pCtrlJog->V.RmdVel -= TASKB_FRQ;
             VelAcc++;
         }

         if(pCtrlJog->V.OutputVel - VelAcc >= -(INT32)pCtrlJog->P.TargetVel )
         {
             pCtrlJog->V.OutputVel -= VelAcc;
         }
         else
         {
             pCtrlJog->V.OutputVel = -(INT32)pCtrlJog->P.TargetVel;
         }        
    }
    else
    {
         pCtrlJog->V.UpDownAct =0;
         pCtrlJog->V.OutputVel = 0;
    }			
}




