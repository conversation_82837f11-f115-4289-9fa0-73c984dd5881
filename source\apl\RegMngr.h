/****************************************************************************************************
 *
 * FILE NAME:  RegMngr.h
 *
 * DESCRIPTION:   Register and Parameter Access Interface
 *
 * CREATED ON:  2019.11.19
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	19-11-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _ML_REG_MANAGER_H_
#define _ML_REG_MANAGER_H_

#include "PnPrmStruct.h"
#include "RegAccessIF.h"
//#include "ComManager.h"
//#include "UnitPrmStruct.h"
//#include "MtPrm.h"


#define DEV_MAX_STR			32			/* Maximum model byte size->Match ID_RD size (32)*/


/*--------------------------------------------------------------------------------------------------*/
/*		Register access API return code																*/
/*--------------------------------------------------------------------------------------------------*/
#define REG_SUCCESS				0		/* Successful completion */
#define REG_CONTINUE			1		/* Continue */
#define REG_NO_ERR				2		/* Register number error */
#define REG_SIZE_ERR			3		/* Register size error */
#define REG_DATA_ERR			4		/* Register data range error */
#define REG_CNDTN_ERR			5		/* Abnormal execution condition */
#define REG_TYPE_ERR			6		/* Type abnormal */


/*--------------------------------------------------------------------------------------------------*/
/*		Register access command code																*/
/*--------------------------------------------------------------------------------------------------*/
typedef enum 
{
	REG_CMD_NONE = 0,					/* 0:No Operation */
	REG_CMD_PRMRD,						/* 1:Register read */
	REG_CMD_PRMWR,						/* 2:egister write */
	REG_CMD_RECALC,						/* 3:Parameter recalculation */
	REG_CMD_ALMHISTCLR,					/* 4:Clear servo alarm log */
	REG_CMD_SYSRST,						/* 5:System reset */
	REG_CMD_CHGCYC,						/* 6:Recalculation of parameters by changing task period */
	REG_CMD_INITPRM,					/* 7:Parameter initialization */
	REG_CMD_IDRD,						/* 8:ID_RD */
	REG_CMD_ADJ_RD,						/* 9:Reading process with ADJ command (no size required) */
}REG_CMD_TYPE;


/*--------------------------------------------------------------------------------------------------*/
/*		Register access structure																    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT32		Number;					/* Register number */
	INT32		Length;					/* Number of registers */
	INT32		AccessSize;				/* Access size(1,2,4byte) */
	INT32		offset;					/* Offset */ 
	BOOL		EepWrite;				/* EEPROM write enabled */
	INT32		*pBuf;					/* Register data storage destination */
	INT32		BufferSize;				/* Buffer size */
} REG_DATA;


/*--------------------------------------------------------------------------------------------------*/
/*		REG Manager structure																        */
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	volatile REG_CMD_TYPE	accCode;		/* Command code	 */
	UINT32			accSts;					/* status */
	UINT32			accErrCode;				/* Error status */
	UINT32			accSeq;					/* Register access execution sequence status */
	BOOL			accWaitBusy;			/* Register access Busy state */
	REG_DATA		*accData;				/* Register access data */
	INT32			accBuffer[32];			/* Register access buffer */

	struct
	{
//		MTPRM_INI	mt;						/* Motion initial parameter data */
		UINT8		rateChgSw;				/* Posing constant selection */
		UINT8		monStop;				/* Monitor stop */
	} iniPrm;								/* Initial parameter load data */

//	MT_PRM			*mtPrm;					/* Motion parameter RAM address */
//	COM_NET_PRM		*netPrm;				/* Communication parameters */
	PRMDATA			*Prm;					/* Parameter RAM address */
	void			*hSvRegMngr;			/* Register access handle */
} REG_HNDL;


UINT8 PrmEcatObjRead( UINT16 Index, UINT8 Subindex, UINT32 Size, UINT16 *pData, UINT8 bCompleteAccess );
UINT32 PrmCANObjRead( UINT16 Index, UINT8 Subindex, UINT32 *Size, UINT16 *pData, UINT8 bCompleteAccess );

UINT8 PrmEcatObjWrite( UINT16 Index, UINT8 Subindex, UINT32 Size, UINT16 *pData, UINT8 bCompleteAccess );
UINT32 PrmCANObjWrite( UINT16 Index, UINT8 Subindex, UINT32 Size, UINT16 *pData, UINT8 bCompleteAccess );
UINT8 PrmUartObjWrite( UINT16 Index, UINT8 Subindex, UINT32 Size, UINT16 *pData, UINT8 bCompleteAccess );

void *PrmObjGetRamPtr( UINT16 Index, UINT8 Subindex );



PUBLIC PRM_ATTR * RegMngrGetAttrByArrayIdx(REG_ACC_T *AccessPrm);     
PUBLIC PRM_ATTR*  PrmObjGetIndexByArrayIdx(REGIF_CONFIG_T *RegCfg, UINT16 Number);

UINT32 PrmModbusObjLenGet( UINT16 ArrayIdx, UINT32 *Lenght, REG_ACC_T *AccessPrmAddr);
UINT32 PrmModbusObjWrite(UINT16 *pData, UINT32 Size, REG_ACC_T *AccessPrmAddr);
UINT32 PrmModBusObjRead(UINT16 *pData, UINT32 Size, REG_ACC_T *AccessPrmAddr);

#endif /* _ML_REG_MANAGER_H_ */


