<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<raConfiguration version="7">
  <generalSettings>
    <option key="#Board#" value="board.rzn2lcustom.xspi1_x1"/>
    <option key="CPU" value="RZN2L"/>
    <option key="Core" value="CR52_0"/>
    <option key="#TargetName#" value="R9A07G084M04GBG"/>
    <option key="#TargetARCHITECTURE#" value="cortex-r52"/>
    <option key="#DeviceCommand#" value="R9A07G084M04"/>
    <option key="#RTOS#" value="_none"/>
    <option key="#pinconfiguration#" value="R9A07G084M04GBG.pincfg"/>
    <option key="#FSPVersion#" value="1.3.0"/>
    <option key="#SELECTED_TOOLCHAIN#" value="iar.arm.toolchain"/>
  </generalSettings>
  <raBspConfiguration>
    <config id="config.board.rzn2lcustom.xspi1_x1">
      <property id="config.board.cache_flg" value="0x00000000"/>
      <property id="config.board.wrapcfg_v" value="0x00000000"/>
      <property id="config.board.comcfg_v" value="0x00000000"/>
      <property id="config.board.bmcfg_v" value="0x00000000"/>
      <property id="config.board.xspi_flg" value="0x00000000"/>
      <property id="config.board.ldr_addr_nml" value="0x6800004C"/>
      <property id="config.board.ldr_size_nml" value="0x00010000"/>
      <property id="config.board.dest_addr_nml" value="0x00102000"/>
      <property id="config.board.cssctl_v" value="0x0000003F"/>
      <property id="config.board.liocfgcs0_v" value="0x00070000"/>
      <property id="config.board.access_speed" value="0x00000600"/>
      <property id="config.board.check_sum" value="Auto Calculate."/>
    </config>
    <config id="config.bsp.rzn2l.R9A07G084M04GBG">
      <property id="config.bsp.part_number" value="config.bsp.part_number.value"/>
      <property id="config.bsp.atcm_size_bytes" value="config.bsp.atcm_size_bytes.value"/>
      <property id="config.bsp.btcm_size_bytes" value="config.bsp.btcm_size_bytes.value"/>
      <property id="config.bsp.system_ram_size_bytes" value="config.bsp.system_ram_size_bytes.value"/>
      <property id="config.bsp.package_style" value="config.bsp.package_style.value"/>
      <property id="config.bsp.package_pins" value="config.bsp.package_pins.value"/>
      <property id="config.bsp.cpu" value="config.bsp.cpu.cpu0"/>
      <property id="config.bsp.fsp.mpu0_read_reg0" value="config.bsp.fsp.mpu0_read_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu0_write_reg0" value="config.bsp.fsp.mpu0_write_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu0_reg0_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_reg0_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_read_reg1" value="config.bsp.fsp.mpu0_read_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu0_write_reg1" value="config.bsp.fsp.mpu0_write_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu0_reg1_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_reg1_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_read_reg2" value="config.bsp.fsp.mpu0_read_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu0_write_reg2" value="config.bsp.fsp.mpu0_write_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu0_reg2_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_reg2_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_read_reg3" value="config.bsp.fsp.mpu0_read_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu0_write_reg3" value="config.bsp.fsp.mpu0_write_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu0_reg3_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_reg3_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_read_reg4" value="config.bsp.fsp.mpu0_read_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu0_write_reg4" value="config.bsp.fsp.mpu0_write_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu0_reg4_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_reg4_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_read_reg5" value="config.bsp.fsp.mpu0_read_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu0_write_reg5" value="config.bsp.fsp.mpu0_write_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu0_reg5_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_reg5_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_read_reg6" value="config.bsp.fsp.mpu0_read_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu0_write_reg6" value="config.bsp.fsp.mpu0_write_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu0_reg6_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_reg6_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_read_reg7" value="config.bsp.fsp.mpu0_read_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu0_write_reg7" value="config.bsp.fsp.mpu0_write_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu0_reg7_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu0_reg7_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_read_reg0" value="config.bsp.fsp.mpu1_read_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu1_write_reg0" value="config.bsp.fsp.mpu1_write_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu1_reg0_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_reg0_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_read_reg1" value="config.bsp.fsp.mpu1_read_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu1_write_reg1" value="config.bsp.fsp.mpu1_write_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu1_reg1_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_reg1_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_read_reg2" value="config.bsp.fsp.mpu1_read_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu1_write_reg2" value="config.bsp.fsp.mpu1_write_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu1_reg2_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_reg2_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_read_reg3" value="config.bsp.fsp.mpu1_read_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu1_write_reg3" value="config.bsp.fsp.mpu1_write_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu1_reg3_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_reg3_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_read_reg4" value="config.bsp.fsp.mpu1_read_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu1_write_reg4" value="config.bsp.fsp.mpu1_write_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu1_reg4_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_reg4_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_read_reg5" value="config.bsp.fsp.mpu1_read_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu1_write_reg5" value="config.bsp.fsp.mpu1_write_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu1_reg5_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_reg5_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_read_reg6" value="config.bsp.fsp.mpu1_read_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu1_write_reg6" value="config.bsp.fsp.mpu1_write_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu1_reg6_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_reg6_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_read_reg7" value="config.bsp.fsp.mpu1_read_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu1_write_reg7" value="config.bsp.fsp.mpu1_write_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu1_reg7_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu1_reg7_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_read_reg0" value="config.bsp.fsp.mpu2_read_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu2_write_reg0" value="config.bsp.fsp.mpu2_write_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu2_reg0_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_reg0_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_read_reg1" value="config.bsp.fsp.mpu2_read_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu2_write_reg1" value="config.bsp.fsp.mpu2_write_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu2_reg1_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_reg1_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_read_reg2" value="config.bsp.fsp.mpu2_read_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu2_write_reg2" value="config.bsp.fsp.mpu2_write_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu2_reg2_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_reg2_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_read_reg3" value="config.bsp.fsp.mpu2_read_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu2_write_reg3" value="config.bsp.fsp.mpu2_write_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu2_reg3_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_reg3_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_read_reg4" value="config.bsp.fsp.mpu2_read_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu2_write_reg4" value="config.bsp.fsp.mpu2_write_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu2_reg4_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_reg4_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_read_reg5" value="config.bsp.fsp.mpu2_read_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu2_write_reg5" value="config.bsp.fsp.mpu2_write_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu2_reg5_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_reg5_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_read_reg6" value="config.bsp.fsp.mpu2_read_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu2_write_reg6" value="config.bsp.fsp.mpu2_write_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu2_reg6_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_reg6_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_read_reg7" value="config.bsp.fsp.mpu2_read_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu2_write_reg7" value="config.bsp.fsp.mpu2_write_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu2_reg7_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu2_reg7_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_read_reg0" value="config.bsp.fsp.mpu3_read_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu3_write_reg0" value="config.bsp.fsp.mpu3_write_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu3_reg0_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_reg0_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_read_reg1" value="config.bsp.fsp.mpu3_read_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu3_write_reg1" value="config.bsp.fsp.mpu3_write_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu3_reg1_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_reg1_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_read_reg2" value="config.bsp.fsp.mpu3_read_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu3_write_reg2" value="config.bsp.fsp.mpu3_write_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu3_reg2_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_reg2_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_read_reg3" value="config.bsp.fsp.mpu3_read_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu3_write_reg3" value="config.bsp.fsp.mpu3_write_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu3_reg3_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_reg3_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_read_reg4" value="config.bsp.fsp.mpu3_read_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu3_write_reg4" value="config.bsp.fsp.mpu3_write_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu3_reg4_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_reg4_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_read_reg5" value="config.bsp.fsp.mpu3_read_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu3_write_reg5" value="config.bsp.fsp.mpu3_write_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu3_reg5_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_reg5_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_read_reg6" value="config.bsp.fsp.mpu3_read_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu3_write_reg6" value="config.bsp.fsp.mpu3_write_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu3_reg6_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_reg6_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_read_reg7" value="config.bsp.fsp.mpu3_read_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu3_write_reg7" value="config.bsp.fsp.mpu3_write_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu3_reg7_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu3_reg7_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_read_reg0" value="config.bsp.fsp.mpu4_read_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu4_write_reg0" value="config.bsp.fsp.mpu4_write_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu4_reg0_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_reg0_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_read_reg1" value="config.bsp.fsp.mpu4_read_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu4_write_reg1" value="config.bsp.fsp.mpu4_write_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu4_reg1_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_reg1_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_read_reg2" value="config.bsp.fsp.mpu4_read_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu4_write_reg2" value="config.bsp.fsp.mpu4_write_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu4_reg2_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_reg2_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_read_reg3" value="config.bsp.fsp.mpu4_read_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu4_write_reg3" value="config.bsp.fsp.mpu4_write_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu4_reg3_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_reg3_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_read_reg4" value="config.bsp.fsp.mpu4_read_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu4_write_reg4" value="config.bsp.fsp.mpu4_write_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu4_reg4_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_reg4_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_read_reg5" value="config.bsp.fsp.mpu4_read_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu4_write_reg5" value="config.bsp.fsp.mpu4_write_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu4_reg5_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_reg5_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_read_reg6" value="config.bsp.fsp.mpu4_read_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu4_write_reg6" value="config.bsp.fsp.mpu4_write_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu4_reg6_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_reg6_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_read_reg7" value="config.bsp.fsp.mpu4_read_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu4_write_reg7" value="config.bsp.fsp.mpu4_write_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu4_reg7_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu4_reg7_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_read_reg0" value="config.bsp.fsp.mpu6_read_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu6_write_reg0" value="config.bsp.fsp.mpu6_write_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu6_reg0_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_reg0_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_read_reg1" value="config.bsp.fsp.mpu6_read_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu6_write_reg1" value="config.bsp.fsp.mpu6_write_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu6_reg1_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_reg1_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_read_reg2" value="config.bsp.fsp.mpu6_read_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu6_write_reg2" value="config.bsp.fsp.mpu6_write_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu6_reg2_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_reg2_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_read_reg3" value="config.bsp.fsp.mpu6_read_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu6_write_reg3" value="config.bsp.fsp.mpu6_write_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu6_reg3_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_reg3_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_read_reg4" value="config.bsp.fsp.mpu6_read_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu6_write_reg4" value="config.bsp.fsp.mpu6_write_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu6_reg4_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_reg4_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_read_reg5" value="config.bsp.fsp.mpu6_read_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu6_write_reg5" value="config.bsp.fsp.mpu6_write_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu6_reg5_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_reg5_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_read_reg6" value="config.bsp.fsp.mpu6_read_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu6_write_reg6" value="config.bsp.fsp.mpu6_write_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu6_reg6_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_reg6_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_read_reg7" value="config.bsp.fsp.mpu6_read_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu6_write_reg7" value="config.bsp.fsp.mpu6_write_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu6_reg7_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu6_reg7_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_read_reg0" value="config.bsp.fsp.mpu7_read_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu7_write_reg0" value="config.bsp.fsp.mpu7_write_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu7_reg0_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_reg0_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_read_reg1" value="config.bsp.fsp.mpu7_read_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu7_write_reg1" value="config.bsp.fsp.mpu7_write_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu7_reg1_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_reg1_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_read_reg2" value="config.bsp.fsp.mpu7_read_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu7_write_reg2" value="config.bsp.fsp.mpu7_write_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu7_reg2_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_reg2_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_read_reg3" value="config.bsp.fsp.mpu7_read_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu7_write_reg3" value="config.bsp.fsp.mpu7_write_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu7_reg3_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_reg3_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_read_reg4" value="config.bsp.fsp.mpu7_read_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu7_write_reg4" value="config.bsp.fsp.mpu7_write_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu7_reg4_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_reg4_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_read_reg5" value="config.bsp.fsp.mpu7_read_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu7_write_reg5" value="config.bsp.fsp.mpu7_write_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu7_reg5_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_reg5_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_read_reg6" value="config.bsp.fsp.mpu7_read_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu7_write_reg6" value="config.bsp.fsp.mpu7_write_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu7_reg6_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_reg6_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_read_reg7" value="config.bsp.fsp.mpu7_read_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu7_write_reg7" value="config.bsp.fsp.mpu7_write_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu7_reg7_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu7_reg7_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_read_reg0" value="config.bsp.fsp.mpu8_read_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu8_write_reg0" value="config.bsp.fsp.mpu8_write_reg0.disabled"/>
      <property id="config.bsp.fsp.mpu8_reg0_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_reg0_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_read_reg1" value="config.bsp.fsp.mpu8_read_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu8_write_reg1" value="config.bsp.fsp.mpu8_write_reg1.disabled"/>
      <property id="config.bsp.fsp.mpu8_reg1_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_reg1_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_read_reg2" value="config.bsp.fsp.mpu8_read_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu8_write_reg2" value="config.bsp.fsp.mpu8_write_reg2.disabled"/>
      <property id="config.bsp.fsp.mpu8_reg2_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_reg2_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_read_reg3" value="config.bsp.fsp.mpu8_read_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu8_write_reg3" value="config.bsp.fsp.mpu8_write_reg3.disabled"/>
      <property id="config.bsp.fsp.mpu8_reg3_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_reg3_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_read_reg4" value="config.bsp.fsp.mpu8_read_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu8_write_reg4" value="config.bsp.fsp.mpu8_write_reg4.disabled"/>
      <property id="config.bsp.fsp.mpu8_reg4_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_reg4_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_read_reg5" value="config.bsp.fsp.mpu8_read_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu8_write_reg5" value="config.bsp.fsp.mpu8_write_reg5.disabled"/>
      <property id="config.bsp.fsp.mpu8_reg5_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_reg5_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_read_reg6" value="config.bsp.fsp.mpu8_read_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu8_write_reg6" value="config.bsp.fsp.mpu8_write_reg6.disabled"/>
      <property id="config.bsp.fsp.mpu8_reg6_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_reg6_end" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_read_reg7" value="config.bsp.fsp.mpu8_read_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu8_write_reg7" value="config.bsp.fsp.mpu8_write_reg7.disabled"/>
      <property id="config.bsp.fsp.mpu8_reg7_start" value="0x00000000"/>
      <property id="config.bsp.fsp.mpu8_reg7_end" value="0x00000000"/>
    </config>
    <config id="config.bsp.rzn2l">
      <property id="config.bsp.common.fiq" value="0x400"/>
      <property id="config.bsp.common.irq" value="0x400"/>
      <property id="config.bsp.common.abt" value="0x400"/>
      <property id="config.bsp.common.und" value="0x400"/>
      <property id="config.bsp.common.sys" value="0x400"/>
      <property id="config.bsp.common.svc" value="0x400"/>
      <property id="config.bsp.common.heap" value="0x2000"/>
      <property id="config.bsp.common.c_runtime_init" value="config.bsp.common.c_runtime_init.enabled"/>
      <property id="config.bsp.common.tfu_mathlib" value="config.bsp.common.tfu_mathlib.enabled"/>
    </config>
    <config id="config.bsp.rzn2l.fsp">
      <property id="config.bsp.fsp.mcu.sci_uart.max_baud" value="16000000"/>
      <property id="config.bsp.fsp.mcu.sci_spi.max_bitrate" value="37500000"/>
      <property id="config.bsp.fsp.mcu.spi.max_bitrate" value="50000000"/>
      <property id="config.bsp.fsp.mcu.adc.sample_and_hold" value="1"/>
    </config>
    <config id="config.bsp.rz">
      <property id="config.bsp.common.vcc" value="3300"/>
      <property id="config.bsp.common.checking" value="config.bsp.common.checking.disabled"/>
      <property id="config.bsp.common.assert" value="config.bsp.common.assert.none"/>
      <property id="config.bsp.common.error_log" value="config.bsp.common.error_log.none"/>
      <property id="config.bsp.common.soft_reset" value="config.bsp.common.soft_reset.disabled"/>
      <property id="config.bsp.common.port_protect" value="config.bsp.common.port_protect.enabled"/>
      <property id="config.bsp.common.early_init" value="config.bsp.common.early_init.disabled"/>
    </config>
  </raBspConfiguration>
  <raClockConfiguration>
    <node id="board.clock.main.freq" option="board.clock.main.freq.25m"/>
    <node id="board.clock.loco.enable" option="board.clock.loco.enable.enabled"/>
    <node id="board.clock.pll0.display" option="board.clock.pll0.display.value"/>
    <node id="board.clock.pll1" option="board.clock.pll1.initial"/>
    <node id="board.clock.pll1.display" option="board.clock.pll1.display.value"/>
    <node id="board.clock.ethernet.source" option="board.clock.ethernet.source.main"/>
    <node id="board.clock.reference.display" option="board.clock.reference.display.value"/>
    <node id="board.clock.loco.freq" option="board.clock.loco.freq.240k"/>
    <node id="board.clock.clma0.enable" option="board.clock.clma0.enable.enabled"/>
    <node id="board.clock.clma0.error" option="board.clock.clma0.error.not_mask"/>
    <node id="board.clock.clma3.error" option="board.clock.clma3.error.not_mask"/>
    <node id="board.clock.clma1.error" option="board.clock.clma1.error.mask"/>
    <node id="board.clock.clma3.enable" option="board.clock.clma3.enable.enabled"/>
    <node id="board.clock.clma1.enable" option="board.clock.clma1.enable.enabled"/>
    <node id="board.clock.clma2.enable" option="board.clock.clma2.enable.enabled"/>
    <node id="board.clock.clma0.cmpl" mul="1" option="_edit"/>
    <node id="board.clock.clma1.cmpl" mul="1" option="_edit"/>
    <node id="board.clock.clma2.cmpl" mul="1" option="_edit"/>
    <node id="board.clock.clma3.cmpl" mul="1" option="_edit"/>
    <node id="board.clock.alternative.source" option="board.clock.alternative.source.loco"/>
    <node id="board.clock.clma0.cmph" mul="1023" option="_edit"/>
    <node id="board.clock.clma1.cmph" mul="1023" option="_edit"/>
    <node id="board.clock.clma2.cmph" mul="1023" option="_edit"/>
    <node id="board.clock.clma3.cmph" mul="1023" option="_edit"/>
    <node id="board.clock.iclk.freq" option="board.clock.iclk.freq.200m"/>
    <node id="board.clock.cpu0clk.mul" option="board.clock.cpu0clk.mul.1"/>
    <node id="board.clock.cpu0clk.display" option="board.clock.cpu0clk.display.value"/>
    <node id="board.clock.ckio.div" option="board.clock.ckio.div.4"/>
    <node id="board.clock.ckio.display" option="board.clock.ckio.display.value"/>
    <node id="board.clock.sci0asyncclk.sel" option="board.clock.sci0asyncclk.sel.1"/>
    <node id="board.clock.sci1asyncclk.sel" option="board.clock.sci1asyncclk.sel.1"/>
    <node id="board.clock.sci2asyncclk.sel" option="board.clock.sci2asyncclk.sel.1"/>
    <node id="board.clock.sci3asyncclk.sel" option="board.clock.sci3asyncclk.sel.1"/>
    <node id="board.clock.sci4asyncclk.sel" option="board.clock.sci4asyncclk.sel.1"/>
    <node id="board.clock.sci5asyncclk.sel" option="board.clock.sci5asyncclk.sel.1"/>
    <node id="board.clock.spi0asyncclk.sel" option="board.clock.spi0asyncclk.sel.1"/>
    <node id="board.clock.spi1asyncclk.sel" option="board.clock.spi1asyncclk.sel.1"/>
    <node id="board.clock.spi2asyncclk.sel" option="board.clock.spi2asyncclk.sel.1"/>
    <node id="board.clock.spi3asyncclk.sel" option="board.clock.spi3asyncclk.sel.1"/>
    <node id="board.clock.pclkshost.display" option="board.clock.pclkshost.display.value"/>
    <node id="board.clock.pclkgptl.display" option="board.clock.pclkgptl.display.value"/>
    <node id="board.clock.pclkh.display" option="board.clock.pclkh.display.value"/>
    <node id="board.clock.pclkm.display" option="board.clock.pclkm.display.value"/>
    <node id="board.clock.pclkl.display" option="board.clock.pclkl.display.value"/>
    <node id="board.clock.pclkadc.display" option="board.clock.pclkadc.display.value"/>
    <node id="board.clock.pclkcan.freq" option="board.clock.pclkcan.freq.40m"/>
    <node id="board.clock.xspi.clk0.freq" option="board.clock.xspi.clk0.freq.12m"/>
    <node id="board.clock.xspi.clk1.freq" option="board.clock.xspi.clk1.freq.12m"/>
    <node id="board.clock.tclk.freq" option="board.clock.tclk.freq.100m"/>
  </raClockConfiguration>
  <raComponentSelection>
    <component apiversion="" class="Common" condition="" group="all" subgroup="fsp_common" variant="" vendor="Renesas" version="1.3.0">
      <description>Board Support Package Common Files</description>
      <originalPack>Renesas.RZN.1.3.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_ioport" variant="" vendor="Renesas" version="1.3.0">
      <description>I/O Port</description>
      <originalPack>Renesas.RZN.1.3.0.pack</originalPack>
    </component>
    <component apiversion="" class="CMSIS" condition="" group="CMSIS5" subgroup="CoreR" variant="" vendor="Arm" version="5.7.0+renesas.1">
      <description>Arm CMSIS Version 5 - Core (R)</description>
      <originalPack>Arm.CMSIS5.5.7.0+renesas.1.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="rzn2l" subgroup="device" variant="R9A07G084M04GBG" vendor="Renesas" version="1.3.0">
      <description>Board support package for R9A07G084M04GBG</description>
      <originalPack>Renesas.RZN_mcu_rzn2l.1.3.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="rzn2l" subgroup="device" variant="" vendor="Renesas" version="1.3.0">
      <description>Board support package for RZN2L</description>
      <originalPack>Renesas.RZN_mcu_rzn2l.1.3.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="rzn2l" subgroup="fsp" variant="" vendor="Renesas" version="1.3.0">
      <description>Board support package for RZN2L - FSP Data</description>
      <originalPack>Renesas.RZN_mcu_rzn2l.1.3.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="Board" subgroup="rzn2l_custom" variant="xspi1_x1_boot" vendor="Renesas" version="1.3.0">
      <description>RZN2L Custom Board Support Files (xSPI1 x1 boot mode)</description>
      <originalPack>Renesas.RZN2L_board_custom.1.3.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_usb_basic" variant="" vendor="Renesas" version="1.3.0">
      <description>USB Basic</description>
      <originalPack>Renesas.RZN.1.3.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_usb_pcdc" variant="" vendor="Renesas" version="1.3.0">
      <description>USB Peripheral Communications Device Class</description>
      <originalPack>Renesas.RZN.1.3.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_xspi_qspi" variant="" vendor="Renesas" version="1.3.0">
      <description>Quad Serial Peripheral Interface Flash on Expanded Serial Peripheral Interface</description>
      <originalPack>Renesas.RZN.1.3.0.pack</originalPack>
    </component>
  </raComponentSelection>
  <raElcConfiguration/>
  <raIcuConfiguration/>
  <raModuleConfiguration>
    <module id="module.driver.ioport_on_ioport.0">
      <property id="module.driver.ioport.name" value="g_ioport"/>
      <property id="module.driver.ioport.elc_trigger_output_group1" value="_disabled"/>
      <property id="module.driver.ioport.port_select_output_group1" value=""/>
      <property id="module.driver.ioport.operation_output_group1" value="Low output"/>
      <property id="module.driver.ioport.elc_trigger_output_group2" value="_disabled"/>
      <property id="module.driver.ioport.port_select_output_group2" value=""/>
      <property id="module.driver.ioport.operation_output_group2" value="Low output"/>
      <property id="module.driver.ioport.elc_trigger_input_group1" value="_disabled"/>
      <property id="module.driver.ioport.event_control_input_group1" value="Disabled"/>
      <property id="module.driver.ioport.port_select_input_group1" value=""/>
      <property id="module.driver.ioport.edge_detect_input_group1" value="Rising edge"/>
      <property id="module.driver.ioport.buffer_overwrite_input_group1" value="Disabled"/>
      <property id="module.driver.ioport.p16_0_initial_value_input_group1" value="Low input"/>
      <property id="module.driver.ioport.p16_1_initial_value_input_group1" value="Low input"/>
      <property id="module.driver.ioport.p16_2_initial_value_input_group1" value="Low input"/>
      <property id="module.driver.ioport.p16_3_initial_value_input_group1" value="Low input"/>
      <property id="module.driver.ioport.p16_5_initial_value_input_group1" value="Low input"/>
      <property id="module.driver.ioport.p16_6_initial_value_input_group1" value="Low input"/>
      <property id="module.driver.ioport.p16_7_initial_value_input_group1" value="Low input"/>
      <property id="module.driver.ioport.elc_trigger_input_group2" value="_disabled"/>
      <property id="module.driver.ioport.event_control_input_group2" value="Disabled"/>
      <property id="module.driver.ioport.port_select_input_group2" value=""/>
      <property id="module.driver.ioport.edge_detect_input_group2" value="Rising edge"/>
      <property id="module.driver.ioport.buffer_overwrite_input_group2" value="Disabled"/>
      <property id="module.driver.ioport.p18_0_initial_value_input_group2" value="Low input"/>
      <property id="module.driver.ioport.p18_1_initial_value_input_group2" value="Low input"/>
      <property id="module.driver.ioport.p18_2_initial_value_input_group2" value="Low input"/>
      <property id="module.driver.ioport.p18_3_initial_value_input_group2" value="Low input"/>
      <property id="module.driver.ioport.p18_4_initial_value_input_group2" value="Low input"/>
      <property id="module.driver.ioport.p18_5_initial_value_input_group2" value="Low input"/>
      <property id="module.driver.ioport.p18_6_initial_value_input_group2" value="Low input"/>
      <property id="module.driver.ioport.event_control_single_port0" value="Disabled"/>
      <property id="module.driver.ioport.event_direction_single_port0" value="Output direction"/>
      <property id="module.driver.ioport.port_number_single_port0" value="P16_0"/>
      <property id="module.driver.ioport.elc_trigger_single_output_port0" value="_disabled"/>
      <property id="module.driver.ioport.operation_single_output_port0" value="Low output"/>
      <property id="module.driver.ioport.edge_detect_single_output_port0" value="Rising edge"/>
      <property id="module.driver.ioport.event_control_single_port1" value="Disabled"/>
      <property id="module.driver.ioport.event_direction_single_port1" value="Output direction"/>
      <property id="module.driver.ioport.port_number_single_port1" value="P16_0"/>
      <property id="module.driver.ioport.elc_trigger_single_output_port1" value="_disabled"/>
      <property id="module.driver.ioport.operation_single_output_port1" value="Low output"/>
      <property id="module.driver.ioport.edge_detect_single_output_port1" value="Rising edge"/>
      <property id="module.driver.ioport.event_control_single_port2" value="Disabled"/>
      <property id="module.driver.ioport.event_direction_single_port2" value="Output direction"/>
      <property id="module.driver.ioport.port_number_single_port2" value="P16_0"/>
      <property id="module.driver.ioport.elc_trigger_single_output_port2" value="_disabled"/>
      <property id="module.driver.ioport.operation_single_output_port2" value="Low output"/>
      <property id="module.driver.ioport.edge_detect_single_output_port2" value="Rising edge"/>
      <property id="module.driver.ioport.event_control_single_port3" value="Disabled"/>
      <property id="module.driver.ioport.event_direction_single_port3" value="Output direction"/>
      <property id="module.driver.ioport.port_number_single_port3" value="P16_0"/>
      <property id="module.driver.ioport.elc_trigger_single_output_port3" value="_disabled"/>
      <property id="module.driver.ioport.operation_single_output_port3" value="Low output"/>
      <property id="module.driver.ioport.edge_detect_single_output_port3" value="Rising edge"/>
    </module>
    <module id="module.driver.qspi_on_xspi_qspi.1892393381">
      <property id="module.driver.xspi_qspi.name" value="g_qspi1"/>
      <property id="module.driver.xspi_qspi.unit" value="1"/>
      <property id="module.driver.xspi_qspi.chip_select" value="module.driver.xspi_qspi.chip_select.cs0"/>
      <property id="module.driver.xspi_qspi.flash_size" value="module.driver.xspi_qspi.flash_size.flash_size_16mb"/>
      <property id="module.driver.xspi_qspi.spi_protocol" value="module.driver.xspi_qspi.spi_protocol.protocol_1s_1s_1s"/>
      <property id="module.driver.xspi_qspi.address_bytes" value="module.driver.xspi_qspi.address_bytes.address_bytes_3"/>
      <property id="module.driver.xspi_qspi.dummy_clocks" value="module.driver.xspi_qspi.dummy_clocks.dummy_clocks_0"/>
      <property id="module.driver.xspi_qspi.prefetch_function" value="module.driver.xspi_qspi.prefetch_function.prefetch_function_enable"/>
      <property id="module.driver.xspi_qspi.page_program_command" value="0x02"/>
      <property id="module.driver.xspi_qspi.read_command" value="0x03"/>
      <property id="module.driver.xspi_qspi.write_enable_command" value="0x06"/>
      <property id="module.driver.xspi_qspi.status_command" value="0x05"/>
      <property id="module.driver.xspi_qspi.write_status_bit" value="0"/>
      <property id="module.driver.xspi_qspi.sector_erase_command" value="0x20"/>
      <property id="module.driver.xspi_qspi.sector_erase_size" value="4096"/>
      <property id="module.driver.xspi_qspi.block_erase_command" value="0xD8"/>
      <property id="module.driver.xspi_qspi.block_erase_size" value="65536"/>
      <property id="module.driver.xspi_qspi.block_erase_32kb_command" value="0x52"/>
      <property id="module.driver.xspi_qspi.block_erase_32kb_size" value="32768"/>
      <property id="module.driver.xspi_qspi.chip_erase_command" value="0xC7"/>
      <property id="module.driver.xspi_qspi.xip_enter_command" value="0x20"/>
      <property id="module.driver.xspi_qspi.xip_exit_command" value="0xFF"/>
      <property id="module.driver.xspi_qspi.command_to_command_interval_clocks" value="module.driver.xspi_qspi.command_to_command_interval_clocks.7_cycles"/>
      <property id="module.driver.xspi_qspi.cs_pulldown_lead" value="module.driver.xspi_qspi.cs_pulldown_lead.no_extension"/>
      <property id="module.driver.xspi_qspi.cs_pullup_lag" value="module.driver.xspi_qspi.cs_pullup_lag.no_extension"/>
    </module>
    <module id="module.driver.pcdc_on_usb.1584083519">
      <property id="module.driver.pcdc.name" value="g_pcdc0"/>
    </module>
    <module id="module.driver.basic_on_usb.93066044">
      <property id="module.driver.basic.name" value="g_basic0"/>
      <property id="module.driver.usb_basic.usb_mode" value="module.driver.usb_basic.usb_mode.host"/>
      <property id="module.driver.usb_basic.usb_speed" value="module.driver.usb_basic.usb_speed.hs"/>
      <property id="module.driver.usb_basic.usb_classtype" value="module.driver.usb_basic.usb_classtype.pcdc"/>
      <property id="module.driver.usb_basic.p_usb_reg" value="g_usb_descriptor"/>
      <property id="module.driver.usb_basic.complience_cb" value="NULL"/>
      <property id="module.driver.usb_basic.ipl" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.ipl_r" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.ipl_d0" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.ipl_d1" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.rtos_callback" value="NULL"/>
      <property id="module.driver.usb_basic.other_context" value="NULL"/>
    </module>
    <context id="_hal.0">
      <stack module="module.driver.ioport_on_ioport.0"/>
      <stack module="module.driver.qspi_on_xspi_qspi.1892393381"/>
      <stack module="module.driver.pcdc_on_usb.1584083519">
        <stack module="module.driver.basic_on_usb.93066044" requires="module.driver.basic_on_usb.requires.basic"/>
      </stack>
    </context>
    <config id="config.driver.xspi_qspi">
      <property id="config.driver.xspi_qspi.param_checking_enable" value="config.driver.xspi_qspi.param_checking_enable.bsp"/>
    </config>
    <config id="config.driver.usb_basic">
      <property id="config.driver.usb_basic.param_checking_enable" value="config.driver.usb_basic.param_checking_enable.bsp"/>
      <property id="config.driver.usb_basic.buswait" value="config.driver.usb_basic.buswait.7"/>
      <property id="config.driver.usb_basic.power_source" value="config.driver.usb_basic.power_source.high"/>
      <property id="config.driver.usb_basic.request" value="config.driver.usb_basic.request.enable"/>
      <property id="config.driver.usb_basic.dblb" value="config.driver.usb_basic.dblb.enable"/>
      <property id="config.driver.usb_basic.cntmd" value="config.driver.usb_basic.cntmd.disable"/>
      <property id="config.driver.usb_basic.dma" value="config.driver.usb_basic.dma.disable"/>
    </config>
    <config id="config.driver.usb_pcdc">
      <property id="config.driver.usb_pcdc.bulk_in" value="config.driver.usb_pcdc.bulk_in.pipe1"/>
      <property id="config.driver.usb_pcdc.bulk_out" value="config.driver.usb_pcdc.bulk_out.pipe2"/>
      <property id="config.driver.usb_pcdc.int_in" value="config.driver.usb_pcdc.int_in.pipe6"/>
    </config>
    <config id="config.driver.usb_pcdc_class"/>
    <config id="config.driver.ioport">
      <property id="config.driver.ioport.checking" value="config.driver.ioport.checking.system"/>
    </config>
  </raModuleConfiguration>
  <raPinConfiguration>
    <pincfg active="true" name="R9A07G084M04GBG.pincfg" selected="true" symbol="g_bsp_pin_cfg">
      <configSetting altId="jtag_fslash_swd.mode.jtag.free" configurationId="jtag_fslash_swd.mode"/>
      <configSetting altId="jtag_fslash_swd.tck_swclk.p02_7" configurationId="jtag_fslash_swd.tck_swclk"/>
      <configSetting altId="jtag_fslash_swd.tdi.p02_5" configurationId="jtag_fslash_swd.tdi"/>
      <configSetting altId="jtag_fslash_swd.tdo.p02_4" configurationId="jtag_fslash_swd.tdo"/>
      <configSetting altId="jtag_fslash_swd.tms_swdio.p02_6" configurationId="jtag_fslash_swd.tms_swdio"/>
      <configSetting altId="p02_4.jtag_fslash_swd.tdo" configurationId="p02_4"/>
      <configSetting altId="p02_4.gpio_speed.gpio_speed_high" configurationId="p02_4.gpio_drivecapacity"/>
      <configSetting altId="p02_4.gpio_mode.gpio_mode_peripheral" configurationId="p02_4.gpio_mode"/>
      <configSetting altId="p02_4.sr.sr.fast" configurationId="p02_4.sr"/>
      <configSetting altId="p02_5.jtag_fslash_swd.tdi" configurationId="p02_5"/>
      <configSetting altId="p02_5.gpio_mode.gpio_mode_peripheral" configurationId="p02_5.gpio_mode"/>
      <configSetting altId="p02_6.jtag_fslash_swd.tms_swdio" configurationId="p02_6"/>
      <configSetting altId="p02_6.gpio_speed.gpio_speed_high" configurationId="p02_6.gpio_drivecapacity"/>
      <configSetting altId="p02_6.gpio_mode.gpio_mode_peripheral" configurationId="p02_6.gpio_mode"/>
      <configSetting altId="p02_6.sr.sr.fast" configurationId="p02_6.sr"/>
      <configSetting altId="p02_7.jtag_fslash_swd.tck_swclk" configurationId="p02_7"/>
      <configSetting altId="p02_7.gpio_mode.gpio_mode_peripheral" configurationId="p02_7.gpio_mode"/>
      <configSetting altId="p03_0.output.toinput.low" configurationId="p03_0"/>
      <configSetting altId="p03_0.gpio_mode.gpio_mode_out.toinput.low" configurationId="p03_0.gpio_mode"/>
      <configSetting altId="p07_4.usb_hs.usb_vbusin" configurationId="p07_4"/>
      <configSetting altId="p07_4.gpio_mode.gpio_mode_peripheral" configurationId="p07_4.gpio_mode"/>
      <configSetting altId="p16_7.xspi1.xspi1_io0" configurationId="p16_7"/>
      <configSetting altId="p16_7.gpio_speed.gpio_speed_high" configurationId="p16_7.gpio_drivecapacity"/>
      <configSetting altId="p16_7.gpio_mode.gpio_mode_peripheral" configurationId="p16_7.gpio_mode"/>
      <configSetting altId="p16_7.smt.smt.enable" configurationId="p16_7.smt"/>
      <configSetting altId="p16_7.sr.sr.fast" configurationId="p16_7.sr"/>
      <configSetting altId="p17_0.xspi1.xspi1_io1" configurationId="p17_0"/>
      <configSetting altId="p17_0.gpio_speed.gpio_speed_high" configurationId="p17_0.gpio_drivecapacity"/>
      <configSetting altId="p17_0.gpio_mode.gpio_mode_peripheral" configurationId="p17_0.gpio_mode"/>
      <configSetting altId="p17_0.smt.smt.enable" configurationId="p17_0.smt"/>
      <configSetting altId="p17_0.sr.sr.fast" configurationId="p17_0.sr"/>
      <configSetting altId="p17_3.xspi1.xspi1_io2" configurationId="p17_3"/>
      <configSetting altId="p17_3.gpio_speed.gpio_speed_high" configurationId="p17_3.gpio_drivecapacity"/>
      <configSetting altId="p17_3.gpio_mode.gpio_mode_peripheral" configurationId="p17_3.gpio_mode"/>
      <configSetting altId="p17_3.smt.smt.enable" configurationId="p17_3.smt"/>
      <configSetting altId="p17_3.sr.sr.fast" configurationId="p17_3.sr"/>
      <configSetting altId="p17_4.xspi1.xspi1_io3" configurationId="p17_4"/>
      <configSetting altId="p17_4.gpio_speed.gpio_speed_high" configurationId="p17_4.gpio_drivecapacity"/>
      <configSetting altId="p17_4.gpio_mode.gpio_mode_peripheral" configurationId="p17_4.gpio_mode"/>
      <configSetting altId="p17_4.smt.smt.enable" configurationId="p17_4.smt"/>
      <configSetting altId="p17_4.sr.sr.fast" configurationId="p17_4.sr"/>
      <configSetting altId="p17_7.xspi1.xspi1_ckp" configurationId="p17_7"/>
      <configSetting altId="p17_7.gpio_speed.gpio_speed_high" configurationId="p17_7.gpio_drivecapacity"/>
      <configSetting altId="p17_7.gpio_mode.gpio_mode_peripheral" configurationId="p17_7.gpio_mode"/>
      <configSetting altId="p17_7.smt.smt.enable" configurationId="p17_7.smt"/>
      <configSetting altId="p17_7.sr.sr.fast" configurationId="p17_7.sr"/>
      <configSetting altId="p18_2.xspi1.xspi1_cs0_hash" configurationId="p18_2"/>
      <configSetting altId="p18_2.gpio_speed.gpio_speed_high" configurationId="p18_2.gpio_drivecapacity"/>
      <configSetting altId="p18_2.gpio_mode.gpio_mode_peripheral" configurationId="p18_2.gpio_mode"/>
      <configSetting altId="p18_2.smt.smt.enable" configurationId="p18_2.smt"/>
      <configSetting altId="p18_2.sr.sr.fast" configurationId="p18_2.sr"/>
      <configSetting altId="p20_1.output.toinput.low" configurationId="p20_1"/>
      <configSetting altId="p20_1.gpio_mode.gpio_mode_out.toinput.low" configurationId="p20_1.gpio_mode"/>
      <configSetting altId="p20_2.output.toinput.low" configurationId="p20_2"/>
      <configSetting altId="p20_2.gpio_mode.gpio_mode_out.toinput.low" configurationId="p20_2.gpio_mode"/>
      <configSetting altId="p20_3.output.toinput.low" configurationId="p20_3"/>
      <configSetting altId="p20_3.gpio_mode.gpio_mode_out.toinput.low" configurationId="p20_3.gpio_mode"/>
      <configSetting altId="p20_4.output.toinput.low" configurationId="p20_4"/>
      <configSetting altId="p20_4.gpio_mode.gpio_mode_out.toinput.low" configurationId="p20_4.gpio_mode"/>
      <configSetting altId="usb_hs.mode.custom.free" configurationId="usb_hs.mode"/>
      <configSetting altId="usb_hs.usb_vbusin.p07_4" configurationId="usb_hs.usb_vbusin"/>
      <configSetting altId="xspi1.mode.custom_40_3.3v_41.free" configurationId="xspi1.mode"/>
      <configSetting altId="xspi1.xspi1_ckp.p17_7" configurationId="xspi1.xspi1_ckp"/>
      <configSetting altId="xspi1.xspi1_cs0_hash.p18_2" configurationId="xspi1.xspi1_cs0_hash"/>
      <configSetting altId="xspi1.xspi1_io0.p16_7" configurationId="xspi1.xspi1_io0"/>
      <configSetting altId="xspi1.xspi1_io1.p17_0" configurationId="xspi1.xspi1_io1"/>
      <configSetting altId="xspi1.xspi1_io2.p17_3" configurationId="xspi1.xspi1_io2"/>
      <configSetting altId="xspi1.xspi1_io3.p17_4" configurationId="xspi1.xspi1_io3"/>
    </pincfg>
  </raPinConfiguration>
</raConfiguration>
