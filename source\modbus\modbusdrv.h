/****************************************************************************************************
 *
 * FILE NAME:  Alarm.h
 *
 * DESCRIPTION: 
 *
 * CREATED ON:  2019.06.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _MODBUS_DRV_H_
#define _MODBUS_DRV_H_


void SciReadEnable(void);
void SciReadDisEnable(void);
void SciSendEnable(UINT8 *pdata, UINT16 Length);

BOOL SciGetTendFlag(void);

void SciResetRxFifo(void);

BOOL SciClearError(void);
UINT16 ReadRxDataNum(void);
UINT8 ReadRxData(void);
BOOL CheckRxState(void);

#endif // end #ifndef _MODBUS_DRV_H_