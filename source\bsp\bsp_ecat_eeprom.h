/*
 * bsp_eeprom.h
 *
 *  Created on: Oct 25, 2023
 *      Author: xgj12
 */

#ifndef BSP_ECAT_EEPROM_H_
#define BSP_ECAT_EEPROM_H_
#include <stdint.h>

#define CFG_IIC_EACT_EEPROM_BASE     R_IIC0_BASE


#define CFG_ECAT_EEPROM_SLAVE_ADDRESS           0x0050   //从机地址器件地址AT24C16---1010(P10)(P9)(P8)(读写位有硬件模块写入,不计入)
#define CFG_ECAT_EEPROM_I2C_PAGE_SIZE           16       //EEPROM页大小AT24C16---最大一次页写入16个字节
#define CFG_ECAT_EEPROM_I2C_PAGE_NUMBER         128      //EEPROM页个数AT24C16---128页*16字节/页=2048字节



void bsp_ecat_eeprom_init(void);
void bsp_eeprom_deinit(void);
uint8_t Ecat_I2C_Mem_Read(uint16_t Dev<PERSON>ddress, uint16_t <PERSON><PERSON><PERSON><PERSON><PERSON>, uint8_t * pData, uint16_t Size, uint32_t Timeout);
uint8_t Ecat_I2C_Mem_Write(uint16_t DevAddress, uint16_t MemAddress, uint8_t * pData, uint16_t Size, uint32_t Timeout);
#endif /* BSP_ECAT_EEPROM_H_ */
