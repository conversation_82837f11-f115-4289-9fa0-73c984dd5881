include "memory_regions.icf";

/* The memory information for each device is done in memory regions file.
 * The starting address and length of memory not defined in memory regions file are defined as 0. */

if (isdefinedsymbol(ATCM_START))
{
    define symbol ATCM_PRV_START = ATCM_START;
}
else
{
    define symbol ATCM_PRV_START = 0;
}

if (isdefinedsymbol(ATCM_LENGTH))
{
    define symbol ATCM_PRV_LENGTH = ATCM_LENGTH;
}
else
{
    define symbol ATCM_PRV_LENGTH = 0;
}

if (isdefinedsymbol(BTCM_START))
{
    define symbol BTCM_PRV_START = BTCM_START;
}
else
{
    define symbol BTCM_PRV_START = 0;
}

if (isdefinedsymbol(BTCM_LENGTH))
{
    define symbol BTCM_PRV_LENGTH = BTCM_LENGTH;
}
else
{
    define symbol BTCM_PRV_LENGTH = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_START))
{
    define symbol SYSTEM_RAM_PRV_START = SYSTEM_RAM_START;
}
else
{
    define symbol SYSTEM_RAM_PRV_START = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_LENGTH))
{
    define symbol SYSTEM_RAM_PRV_LENGTH = SYSTEM_RAM_LENGTH;
}
else
{
    define symbol SYSTEM_RAM_PRV_LENGTH = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_MIRROR_START))
{
    define symbol SYSTEM_RAM_MIRROR_PRV_START = SYSTEM_RAM_MIRROR_START;
}
else
{
    define symbol SYSTEM_RAM_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(SYSTEM_RAM_MIRROR_LENGTH))
{
    define symbol SYSTEM_RAM_MIRROR_PRV_LENGTH = SYSTEM_RAM_MIRROR_LENGTH;
}
else
{
    define symbol SYSTEM_RAM_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_MIRROR_START))
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_START = xSPI0_CS0_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH = xSPI0_CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_MIRROR_START))
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_START = xSPI0_CS1_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH = xSPI0_CS1_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_MIRROR_START))
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_START = xSPI1_CS0_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH = xSPI1_CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_MIRROR_START))
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_START = xSPI1_CS1_SPACE_MIRROR_START;
}
else
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_MIRROR_LENGTH))
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH = xSPI1_CS1_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS0_SPACE_MIRROR_START))
{
    define symbol CS0_SPACE_MIRROR_PRV_START = CS0_SPACE_MIRROR_START;
}
else
{
    define symbol CS0_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS0_SPACE_MIRROR_LENGTH))
{
    define symbol CS0_SPACE_MIRROR_PRV_LENGTH = CS0_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS0_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS2_SPACE_MIRROR_START))
{
    define symbol CS2_SPACE_MIRROR_PRV_START = CS2_SPACE_MIRROR_START;
}
else
{
    define symbol CS2_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS2_SPACE_MIRROR_LENGTH))
{
    define symbol CS2_SPACE_MIRROR_PRV_LENGTH = CS2_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS2_SPACE_MIRROR_PRV_LENGTH = 0;
}


if (isdefinedsymbol(CS3_SPACE_MIRROR_START))
{
    define symbol CS3_SPACE_MIRROR_PRV_START = CS3_SPACE_MIRROR_START;
}
else
{
    define symbol CS3_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS3_SPACE_MIRROR_LENGTH))
{
    define symbol CS3_SPACE_MIRROR_PRV_LENGTH = CS3_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS3_SPACE_MIRROR_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS5_SPACE_MIRROR_START))
{
    define symbol CS5_SPACE_MIRROR_PRV_START = CS5_SPACE_MIRROR_START;
}
else
{
    define symbol CS5_SPACE_MIRROR_PRV_START = 0;
}

if (isdefinedsymbol(CS5_SPACE_MIRROR_LENGTH))
{
    define symbol CS5_SPACE_MIRROR_PRV_LENGTH = CS5_SPACE_MIRROR_LENGTH;
}
else
{
    define symbol CS5_SPACE_MIRROR_PRV_LENGTH = 0;
}


if (isdefinedsymbol(xSPI0_CS0_SPACE_START))
{
    define symbol xSPI0_CS0_SPACE_PRV_START = xSPI0_CS0_SPACE_START;
}
else
{
    define symbol xSPI0_CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS0_SPACE_LENGTH))
{
    define symbol xSPI0_CS0_SPACE_PRV_LENGTH = xSPI0_CS0_SPACE_LENGTH;
}
else
{
    define symbol xSPI0_CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_START))
{
    define symbol xSPI0_CS1_SPACE_PRV_START = xSPI0_CS1_SPACE_START;
}
else
{
    define symbol xSPI0_CS1_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI0_CS1_SPACE_LENGTH))
{
    define symbol xSPI0_CS1_SPACE_PRV_LENGTH = xSPI0_CS1_SPACE_LENGTH;
}
else
{
    define symbol xSPI0_CS1_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_START))
{
    define symbol xSPI1_CS0_SPACE_PRV_START = xSPI1_CS0_SPACE_START;
}
else
{
    define symbol xSPI1_CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS0_SPACE_LENGTH))
{
    define symbol xSPI1_CS0_SPACE_PRV_LENGTH = xSPI1_CS0_SPACE_LENGTH;
}
else
{
    define symbol xSPI1_CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_START))
{
    define symbol xSPI1_CS1_SPACE_PRV_START = xSPI1_CS1_SPACE_START;
}
else
{
    define symbol xSPI1_CS1_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(xSPI1_CS1_SPACE_LENGTH))
{
    define symbol xSPI1_CS1_SPACE_PRV_LENGTH = xSPI1_CS1_SPACE_LENGTH;
}
else
{
    define symbol xSPI1_CS1_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS0_SPACE_START))
{
    define symbol CS0_SPACE_PRV_START = CS0_SPACE_START;
}
else
{
    define symbol CS0_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS0_SPACE_LENGTH))
{
    define symbol CS0_SPACE_PRV_LENGTH = CS0_SPACE_LENGTH;
}
else
{
    define symbol CS0_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS2_SPACE_START))
{
    define symbol CS2_SPACE_PRV_START = CS2_SPACE_START;
}
else
{
    define symbol CS2_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS2_SPACE_LENGTH))
{
    define symbol CS2_SPACE_PRV_LENGTH = CS2_SPACE_LENGTH;
}
else
{
    define symbol CS2_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS3_SPACE_START))
{
    define symbol CS3_SPACE_PRV_START = CS3_SPACE_START;
}
else
{
    define symbol CS3_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS3_SPACE_LENGTH))
{
    define symbol CS3_SPACE_PRV_LENGTH = CS3_SPACE_LENGTH;
}
else
{
    define symbol CS3_SPACE_PRV_LENGTH = 0;
}

if (isdefinedsymbol(CS5_SPACE_START))
{
    define symbol CS5_SPACE_PRV_START = CS5_SPACE_START;
}
else
{
    define symbol CS5_SPACE_PRV_START = 0;
}

if (isdefinedsymbol(CS5_SPACE_LENGTH))
{
    define symbol CS5_SPACE_PRV_LENGTH = CS5_SPACE_LENGTH;
}
else
{
    define symbol CS5_SPACE_PRV_LENGTH = 0;
}

/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\a_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = ATCM_PRV_START;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_ROM_start__ = xSPI1_CS0_SPACE_PRV_START + 0x00020100;
define symbol __ICFEDIT_region_ROM_end__   = xSPI1_CS0_SPACE_PRV_START + 0x0006FFFF;
define symbol __ICFEDIT_region_RAM_start__ = ATCM_PRV_START + 0x00000100;
define symbol __ICFEDIT_region_RAM_end__   = ATCM_PRV_START + 0x0001FFFF;
/**** End of ICF editor section. ###ICF###*/

define memory mem with size = 4G;
define region ROM_region   = mem:[from __ICFEDIT_region_ROM_start__   to __ICFEDIT_region_ROM_end__];
define region RAM_region   = mem:[from __ICFEDIT_region_RAM_start__   to __ICFEDIT_region_RAM_end__];

define symbol __region_D_LDR_DATA_start__    = BTCM_PRV_START;
define symbol __region_D_LDR_DATA_end__      = BTCM_PRV_START + 0x00001FFF;
define symbol __region_D_LDR_PRG_start__     = BTCM_PRV_START + 0x00002000;
define symbol __region_D_LDR_PRG_end__       = BTCM_PRV_START + 0x0001FFFF;

define symbol __region_DMAC_LINK_MODE_start__         = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00044000;
define symbol __region_DMAC_LINK_MODE_end__           = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00040000 - 1;
define symbol __region_SHARED_NONCACHE_BUFFER_start__ = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00040000;
define symbol __region_SHARED_NONCACHE_BUFFER_end__   = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00020000 - 1;
define symbol __region_NONCACHE_BUFFER_start__        = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 0x00020000;
define symbol __region_NONCACHE_BUFFER_end__          = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 1;

define symbol __region_ATCM_start__              = ATCM_PRV_START;
define symbol __region_ATCM_end__                = ATCM_PRV_START + ATCM_PRV_LENGTH - 1;
define symbol __region_BTCM_start__              = BTCM_PRV_START;
define symbol __region_BTCM_end__                = BTCM_PRV_START + BTCM_PRV_LENGTH - 1;
define symbol __region_SYSTEM_RAM_start__        = SYSTEM_RAM_PRV_START;
define symbol __region_SYSTEM_RAM_end__          = SYSTEM_RAM_PRV_START + SYSTEM_RAM_PRV_LENGTH - 1;
define symbol __region_SYSTEM_RAM_MIRROR_start__ = SYSTEM_RAM_MIRROR_PRV_START;
define symbol __region_SYSTEM_RAM_MIRROR_end__   = SYSTEM_RAM_MIRROR_PRV_START + SYSTEM_RAM_MIRROR_PRV_LENGTH - 1;

define symbol __region_XSPI0_CS0_MIRROR_start__ = xSPI0_CS0_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI0_CS0_MIRROR_end__   = xSPI0_CS0_SPACE_MIRROR_PRV_START + xSPI0_CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS1_MIRROR_start__ = xSPI0_CS1_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI0_CS1_MIRROR_end__   = xSPI0_CS1_SPACE_MIRROR_PRV_START + xSPI0_CS1_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS0_MIRROR_start__ = xSPI1_CS0_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI1_CS0_MIRROR_end__   = xSPI1_CS0_SPACE_MIRROR_PRV_START + xSPI1_CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS1_MIRROR_start__ = xSPI1_CS1_SPACE_MIRROR_PRV_START;
define symbol __region_XSPI1_CS1_MIRROR_end__   = xSPI1_CS1_SPACE_MIRROR_PRV_START + xSPI1_CS1_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS0_MIRROR_start__       = CS0_SPACE_MIRROR_PRV_START;
define symbol __region_CS0_MIRROR_end__         = CS0_SPACE_MIRROR_PRV_START + CS0_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS2_MIRROR_start__       = CS2_SPACE_MIRROR_PRV_START;
define symbol __region_CS2_MIRROR_end__         = CS2_SPACE_MIRROR_PRV_START + CS2_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS3_MIRROR_start__       = CS3_SPACE_MIRROR_PRV_START;
define symbol __region_CS3_MIRROR_end__         = CS3_SPACE_MIRROR_PRV_START + CS3_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_CS5_MIRROR_start__       = CS5_SPACE_MIRROR_PRV_START;
define symbol __region_CS5_MIRROR_end__         = CS5_SPACE_MIRROR_PRV_START + CS5_SPACE_MIRROR_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS0_start__        = xSPI0_CS0_SPACE_PRV_START;
define symbol __region_XSPI0_CS0_end__          = xSPI0_CS0_SPACE_PRV_START + xSPI0_CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI0_CS1_start__        = xSPI0_CS1_SPACE_PRV_START;
define symbol __region_XSPI0_CS1_end__          = xSPI0_CS1_SPACE_PRV_START + xSPI0_CS1_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS0_start__        = xSPI1_CS0_SPACE_PRV_START;
define symbol __region_XSPI1_CS0_end__          = xSPI1_CS0_SPACE_PRV_START + xSPI1_CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_XSPI1_CS1_start__        = xSPI1_CS1_SPACE_PRV_START;
define symbol __region_XSPI1_CS1_end__          = xSPI1_CS1_SPACE_PRV_START + xSPI1_CS1_SPACE_PRV_LENGTH - 1;
define symbol __region_CS0_start__              = CS0_SPACE_PRV_START;
define symbol __region_CS0_end__                = CS0_SPACE_PRV_START + CS0_SPACE_PRV_LENGTH - 1;
define symbol __region_CS2_start__              = CS2_SPACE_PRV_START;
define symbol __region_CS2_end__                = CS2_SPACE_PRV_START + CS2_SPACE_PRV_LENGTH - 1;
define symbol __region_CS3_start__              = CS3_SPACE_PRV_START;
define symbol __region_CS3_end__                = CS3_SPACE_PRV_START + CS3_SPACE_PRV_LENGTH - 1;
define symbol __region_CS5_start__              = CS5_SPACE_PRV_START;
define symbol __region_CS5_end__                = CS5_SPACE_PRV_START + CS5_SPACE_PRV_LENGTH - 1;

/************** SPI boot mode setting **************/
define symbol __region_LDR_PARAM_start__     = xSPI1_CS0_SPACE_PRV_START;
define symbol __region_LDR_PARAM_end__       = xSPI1_CS0_SPACE_PRV_START + 0x0000004B;
define symbol __region_S_LDR_PRG_start__     = xSPI1_CS0_SPACE_PRV_START + 0x0000004C;
define symbol __region_S_LDR_PRG_end__       = xSPI1_CS0_SPACE_PRV_START + 0x0000604B;
define symbol __region_S_LDR_DATA_start__    = xSPI1_CS0_SPACE_PRV_START + 0x0000604C;
define symbol __region_S_LDR_DATA_end__      = xSPI1_CS0_SPACE_PRV_START + 0x0000804B;

define symbol __region_S_intvec_start__      = xSPI1_CS0_SPACE_PRV_START + 0x00020000;
define symbol __region_S_intvec_end__        = xSPI1_CS0_SPACE_PRV_START + 0x000200FF;
define symbol __region_S_RAM_start__         = xSPI1_CS0_SPACE_PRV_START + 0x00070000;
define symbol __region_S_RAM_end__           = xSPI1_CS0_SPACE_PRV_START + 0x0009FFFF;
/****************************************************/

define region D_LDR_DATA_region = mem:[from __region_D_LDR_DATA_start__   to __region_D_LDR_DATA_end__];
define region D_LDR_PRG_region  = mem:[from __region_D_LDR_PRG_start__   to __region_D_LDR_PRG_end__];

define region LDR_PARAM_region  = mem:[from __region_LDR_PARAM_start__   to __region_LDR_PARAM_end__];
define region S_LDR_PRG_region  = mem:[from __region_S_LDR_PRG_start__   to __region_S_LDR_PRG_end__];
define region S_LDR_DATA_region = mem:[from __region_S_LDR_DATA_start__   to __region_S_LDR_DATA_end__];

define region S_intvec_region = mem:[from __region_S_intvec_start__  to __region_S_intvec_end__];
define region S_RAM_region    = mem:[from __region_S_RAM_start__  to __region_S_RAM_end__];

define region DMAC_LINK_MODE_region          = mem:[from __region_DMAC_LINK_MODE_start__   to __region_DMAC_LINK_MODE_end__];
define region SHARED_NONCACHE_BUFFER_region  = mem:[from __region_SHARED_NONCACHE_BUFFER_start__   to __region_SHARED_NONCACHE_BUFFER_end__];
define region NONCACHE_BUFFER_region         = mem:[from __region_NONCACHE_BUFFER_start__   to __region_NONCACHE_BUFFER_end__];

define region ATCM_region              = mem:[from __region_ATCM_start__  to __region_ATCM_end__ ];
define region BTCM_region              = mem:[from __region_BTCM_start__  to __region_BTCM_end__ ];
define region SYSTEM_RAM_region        = mem:[from __region_SYSTEM_RAM_start__  to __region_SYSTEM_RAM_end__ ];
define region SYSTEM_RAM_MIRROR_region = mem:[from __region_SYSTEM_RAM_MIRROR_start__  to __region_SYSTEM_RAM_MIRROR_end__ ];
define region XSPI0_CS0_MIRROR_region  = mem:[from __region_XSPI0_CS0_MIRROR_start__  to __region_XSPI0_CS0_MIRROR_end__ ];
define region XSPI0_CS1_MIRROR_region  = mem:[from __region_XSPI0_CS1_MIRROR_start__  to __region_XSPI0_CS1_MIRROR_end__ ];
define region XSPI1_CS0_MIRROR_region  = mem:[from __region_XSPI1_CS0_MIRROR_start__  to __region_XSPI1_CS0_MIRROR_end__ ];
define region XSPI1_CS1_MIRROR_region  = mem:[from __region_XSPI1_CS1_MIRROR_start__  to __region_XSPI1_CS1_MIRROR_end__ ];
define region CS0_MIRROR_region        = mem:[from __region_CS0_MIRROR_start__  to __region_CS0_MIRROR_end__ ];
define region CS2_MIRROR_region        = mem:[from __region_CS2_MIRROR_start__  to __region_CS2_MIRROR_end__ ];
define region CS3_MIRROR_region        = mem:[from __region_CS3_MIRROR_start__  to __region_CS3_MIRROR_end__ ];
define region CS5_MIRROR_region        = mem:[from __region_CS5_MIRROR_start__  to __region_CS5_MIRROR_end__ ];
define region XSPI0_CS0_region         = mem:[from __region_XSPI0_CS0_start__  to __region_XSPI0_CS0_end__ ];
define region XSPI0_CS1_region         = mem:[from __region_XSPI0_CS1_start__  to __region_XSPI0_CS1_end__ ];
define region XSPI1_CS0_region         = mem:[from __region_XSPI1_CS0_start__  to __region_XSPI1_CS0_end__ ];
define region XSPI1_CS1_region         = mem:[from __region_XSPI1_CS1_start__  to __region_XSPI1_CS1_end__ ];
define region CS0_region               = mem:[from __region_CS0_start__  to __region_CS0_end__ ];
define region CS2_region               = mem:[from __region_CS2_start__  to __region_CS2_end__ ];
define region CS3_region               = mem:[from __region_CS3_start__  to __region_CS3_end__ ];
define region CS5_region               = mem:[from __region_CS5_start__  to __region_CS5_end__ ];

define block LDR_PRG_RBLOCK with fixed order, alignment = 4
                             { ro code section .loader_text_init object startup.o,
                               ro code object startup.o,
                               ro code object system.o,
                               ro code object bsp_clocks.o,
                               ro code object bsp_irq.o,
                               ro code object bsp_register_protection.o,
                               ro code object r_ioport.o,
                               ro code section .warm_start_init }
                               except { ro code section .intvec_init};
define block LDR_PRG_WBLOCK with fixed order, alignment = 4
                             { rw code section .loader_text object startup.o,
                               rw code object startup.o,
                               rw code object system.o,
                               rw code object bsp_clocks.o,
                               rw code object bsp_irq.o,
                               rw code object bsp_register_protection.o,
                               rw code object r_ioport.o,
                               rw code section .warm_start }
                               except { rw code section .intvec};
define block LDR_DATA_ZBLOCK with alignment = 4
                             { section .bss object startup.o,
                               section .bss object system.o,
                               section .bss object bsp_clocks.o,
                               section .bss object bsp_irq.o,
                               section .bss object bsp_register_protection.o,
                               section .bss object r_ioport.o,
                               section .bss object bsp_io.o };
define block LDR_DATA_RBLOCK with fixed order, alignment = 4
                             { section .data_init object startup.o,
                               section .data_init object system.o,
                               section .data_init object bsp_clocks.o,
                               section .data_init object bsp_irq.o,
                               section .data_init object bsp_register_protection.o,
                               section .data_init object r_ioport.o };
define block LDR_DATA_WBLOCK with fixed order, alignment = 4
                             { section .data object startup.o,
                               section .data object system.o,
                               section .data object bsp_clocks.o,
                               section .data object bsp_irq.o,
                               section .data object bsp_register_protection.o,
                               section .data object r_ioport.o };
                               
define block USER_CUSTOM_PRG_RBLOCK with fixed order
                             { 
                               ro code object IntTaskA.o,
                               ro code object IntTaskB.o,
                               ro code object TamagawaIF.o,
                               ro code object TaskCMain.o,
                               ro code object BaseControls.o,
                               ro code object BaseLoops.o,
                               ro code object BasePrmCal.o,                              
                               ro code object BaseSequence.o,
                               ro code object BisscEncIF.o,
                               ro code object bsp.o,
                               ro code object bsp_adc.o,
                               ro code object bsp_dsmif.o,
                               ro code object bsp_enc.o,
                               ro code object bsp_pwm.o,
                               ro code object bsp_reset.o, 
                               ro code object bsp_timer.o,
							   ro code object bsp_eeprom,
                               ro code object CheckMotSts.o,
                               ro code object Cia402Appl.o,
                               ro code object coeappl.o,
                               ro code object ComRegMngr.o,
                               ro code object CurrentLoop.o,
                               ro code object PosManager.o,
                               ro code object PowerManager.o,        
                               ro code object IntTaskC.o,  
                               ro code object HardApi.o,  
                               ro code object FieldWeakening.o,  
							   ro code object app.o,  
							   ro code object Encoder.o, 
							   ro code object I2C_LED.o, 
							   ro code object MLib.o,
							   ro code object SinTable.o,
                              };

define block USER_CUSTOM_PRG_WBLOCK with fixed order
                             { 
                               rw code object IntTaskA.o,
                               rw code object IntTaskB.o,
                               rw code object TamagawaIF.o,
                               rw code object TaskCMain.o,
                               rw code object BaseControls.o,
                               rw code object BaseLoops.o,
                               rw code object BasePrmCal.o,                              
                               rw code object BaseSequence.o,
                               rw code object BisscEncIF.o,
                               rw code object bsp.o,
                               rw code object bsp_adc.o,
                               rw code object bsp_dsmif.o,
                               rw code object bsp_enc.o,
                               rw code object bsp_pwm.o,
                               rw code object bsp_reset.o, 
                               rw code object bsp_timer.o,
							   rw code object bsp_eeprom,
                               rw code object CheckMotSts.o,
                               rw code object Cia402Appl.o,
                               rw code object coeappl.o,
                               rw code object ComRegMngr.o,
                               rw code object CurrentLoop.o,
                               rw code object PosManager.o,
                               rw code object PowerManager.o,        
                               rw code object IntTaskC.o,  
                               rw code object HardApi.o,  
                               rw code object FieldWeakening.o, 
							   rw code object app.o,  
							   rw code object Encoder.o, 
							   rw code object I2C_LED.o, 
							   rw code object MLib.o,
							   rw code object SinTable.o,
                               };

                               
define block USER_CUSTOM_DATA_ZBLOCK with fixed order, alignment = 4
                             { 
                               section .bss object IntTaskA.o,
                               section .bss object IntTaskB.o,
                               section .bss object TamagawaIF.o,
                               section .bss object TaskCMain.o,
                               section .bss object BaseControls.o,
                               section .bss object BaseLoops.o,
                               section .bss object BasePrmCal.o,      
                               section .bss object BaseSequence.o,
                               section .bss object BisscEncIF.o,
                               section .bss object bsp.o,
                               section .bss object bsp_adc.o,
                               section .bss object bsp_dsmif.o,
                               section .bss object bsp_enc.o,
                               section .bss object bsp_pwm.o,
                               section .bss object bsp_reset.o, 
                               section .bss object bsp_timer.o,
                               section .bss object CheckMotSts.o,
                               section .bss object Cia402Appl.o,
                               section .bss object coeappl.o,
                               section .bss object ComRegMngr.o,
                               section .bss object CurrentLoop.o,
                               section .bss object PosManager.o,
                               section .bss object PowerManager.o,    
                               section .bss object IntTaskC.o,  
                               section .bss object HardApi.o,  
                               section .bss object FieldWeakening.o, 
							   section .bss object app.o,  
							   section .bss object Encoder.o, 
							   section .bss object I2C_LED.o, 
							   section .bss object MLib.o,	 
							   section .bss object SinTable.o, 	
                               };
define block USER_CUSTOM_DATA_RBLOCK with fixed order, alignment = 4
                             { 
                               section .data_init object IntTaskA.o,
                               section .data_init object IntTaskB.o,
                               section .data_init object TamagawaIF.o,
                               section .data_init object TaskCMain.o,
                               section .data_init object BaseControls.o,
                               section .data_init object BaseLoops.o,
                               section .data_init object BasePrmCal.o,      
                               section .data_init object BaseSequence.o,
                               section .data_init object BisscEncIF.o,
                               section .data_init object bsp.o,
                               section .data_init object bsp_adc.o,
                               section .data_init object bsp_dsmif.o,
                               section .data_init object bsp_enc.o,
                               section .data_init object bsp_pwm.o,
                               section .data_init object bsp_reset.o, 
                               section .data_init object bsp_timer.o,
                               section .data_init object CheckMotSts.o,
                               section .data_init object Cia402Appl.o,
                               section .data_init object coeappl.o,
                               section .data_init object ComRegMngr.o,                
                               section .data_init object CurrentLoop.o,
                               section .data_init object PosManager.o,
                               section .data_init object PowerManager.o,    
                               section .data_init object IntTaskC.o,  
                               section .data_init object HardApi.o,  
                               section .data_init object FieldWeakening.o, 	
							   section .data_init object app.o,  
							   section .data_init object Encoder.o, 
							   section .data_init object I2C_LED.o, 
							   section .data_init object MLib.o,	
							   section .rodata_init object SinTable.o, 
                               };
                               
define block USER_CUSTOM_DATA_WBLOCK with fixed order, alignment = 4
                             { 
                               section .data object IntTaskA.o,
                               section .data object IntTaskB.o,
                               section .data object TamagawaIF.o,
                               section .data object TaskCMain.o,
                               section .data object BaseControls.o,
                               section .data object BaseLoops.o,
                               section .data object BasePrmCal.o,      
                               section .data object BaseSequence.o,
                               section .data object BisscEncIF.o,
                               section .data object bsp.o,
                               section .data object bsp_adc.o,
                               section .data object bsp_dsmif.o,
                               section .data object bsp_enc.o,
                               section .data object bsp_pwm.o,
                               section .data object bsp_reset.o, 
                               section .data object bsp_timer.o,
                               section .data object CheckMotSts.o,
                               section .data object Cia402Appl.o,
                               section .data object coeappl.o,
                               section .data object ComRegMngr.o,              
                               section .data object CurrentLoop.o,
                               section .data object PosManager.o,
                               section .data object PowerManager.o,    
                               section .data object IntTaskC.o,         
                               section .data object HardApi.o,  
                               section .data object FieldWeakening.o, 
							   section .data object app.o,  
							   section .data object Encoder.o, 
							   section .data object I2C_LED.o, 
							   section .data object MLib.o,
							   section .rodata object SinTable.o,                        
                               };                               
                               
                               

define block VECTOR_RBLOCK with alignment = 32 { ro code section .intvec_init};
define block VECTOR_WBLOCK with alignment = 32 { rw code section .intvec};
define block USER_PRG_RBLOCK with fixed order, alignment = 4  { ro code };
define block USER_PRG_WBLOCK with fixed order, alignment = 4  { rw code };
define block USER_DATA_ZBLOCK with alignment = 4{ section .bss };
define block USER_DATA_RBLOCK with fixed order
                              { section .data_init,
                                section __DLIB_PERTHREAD_init,
                                section .rodata_init,
                                section .version_init };
define block USER_DATA_WBLOCK with fixed order
                              { section .data,
                                section __DLIB_PERTHREAD,
                                section .rodata,
                                section .version };
                                
define block DMAC_LINK_MODE_ZBLOCK with alignment = 4 { section .dmac_link_mode* };
define block SHARED_NONCACHE_BUFFER_ZBLOCK with alignment = 32 { section .shared_noncache_buffer* };
define block NONCACHE_BUFFER_ZBLOCK with alignment = 32 { section .noncache_buffer* };

//define block CUSTOM_QUICK_DATA_ZBLOCK  with alignment = 4 { section .myVariSection };

initialize manually  { ro code object startup.o,
                       ro code object system.o,
                       ro code object bsp_clocks.o,
                       ro code object bsp_irq.o,
                       ro code object bsp_register_protection.o,
                       ro code object r_ioport.o,
                       ro code section .intvec,
                       ro code section .warm_start,
                                                  
                       ro code object IntTaskA.o,
                       ro code object IntTaskB.o,
                       ro code object TamagawaIF.o,
                       ro code object TaskCMain.o,
                       ro code object BaseControls.o,
                       ro code object BaseLoops.o,
                       ro code object BasePrmCal.o,        
                       ro code object BaseSequence.o,
                       ro code object BisscEncIF.o,
                       ro code object bsp.o,
                       ro code object bsp_adc.o,
                       ro code object bsp_dsmif.o,
                       ro code object bsp_enc.o,
                       ro code object bsp_pwm.o,
                       ro code object bsp_reset.o, 
                       ro code object bsp_timer.o,
                       ro code object CheckMotSts.o,
                       ro code object Cia402Appl.o,
                       ro code object coeappl.o,          
                       ro code object ComRegMngr.o,
                       ro code object CurrentLoop.o,
                       ro code object PosManager.o,
                       ro code object PowerManager.o,    
                       ro code object IntTaskC.o,  
                       ro code object HardApi.o,  
                       ro code object FieldWeakening.o,  	
					   ro code object app.o,  
					   ro code object Encoder.o, 
					   ro code object I2C_LED.o, 
					   ro code object MLib.o,	
					   ro code object SinTable.o,				   

                       ro code,
                       section .data,
                       section __DLIB_PERTHREAD,
                       section .rodata,
                       section .version ,    
                       };

do not initialize   { section .noinit,
                      section .bss,
                      section .dmac_link_mode*,
                      section .shared_noncache_buffer*,
                      section .noncache_buffer*,
                      rw section HEAP,
                      rw section .stack*,
                      rw section .sys_stack,
                      rw section .svc_stack,
                      rw section .irq_stack,
                      rw section .fiq_stack,
                      rw section .und_stack,
                      rw section .abt_stack };

//do not initialize   { section .myVariSection};

define symbol __region_D_LDR_start__   = SYSTEM_RAM_PRV_START + SYSTEM_RAM_PRV_LENGTH - 0x004E000;
define symbol __region_D_LDR_end__     = SYSTEM_RAM_PRV_START + SYSTEM_RAM_PRV_LENGTH - 0x0044000 - 1;

define symbol __region_D_VECTOR_start__ = ATCM_PRV_START;
define symbol __region_D_VECTOR_end__   = ATCM_PRV_START + 0x4F;

define symbol __region_D_ATCM_start__ = ATCM_PRV_START + 0x50;
define symbol __region_D_ATCM_end__   = __region_ATCM_end__;

define symbol __region_S_LTBL_start__   = xSPI1_CS0_SPACE_PRV_START  + 0x00100000;
define symbol __region_S_LTBL_end__     = xSPI1_CS0_SPACE_PRV_START  + 0x00101000 - 1;

define symbol __region_S_LDR_start__    = xSPI1_CS0_SPACE_PRV_START + 0x00101000;
define symbol __region_S_LDR_end__      = xSPI1_CS0_SPACE_PRV_START + 0x0010B000  - 1;


define symbol __region_S_VECTOR_start__    = xSPI1_CS0_SPACE_PRV_START + 0x0010B000;
define symbol __region_S_VECTOR_end__      = xSPI1_CS0_SPACE_PRV_START + 0x0010B050 - 1;
define symbol __region_S_APP_start__       = xSPI1_CS0_SPACE_PRV_START + 0x0010B050;
define symbol __region_S_APP_end__         = xSPI1_CS0_SPACE_PRV_START + 0x01000000 - 1;


define region D_LDR_region          =  mem: [from __region_D_LDR_start__ to __region_D_LDR_end__ ];
define region D_VECTOR_region       =  mem: [from ATCM_PRV_START to  __region_D_VECTOR_end__ ];
define region D_ATCM_region       =  mem: [from __region_D_ATCM_start__ to  __region_D_ATCM_end__ ];

               
               
define region S_LDR_region          =  mem: [from __region_S_LDR_start__  to __region_S_LDR_end__ ];
define region S_LOADER_TBL_region   =  mem: [from __region_S_LTBL_start__ to __region_S_LTBL_end__ ];
define region S_VECTOR_region       =  mem: [from __region_S_VECTOR_start__  to __region_S_VECTOR_end__ ];
define region S_APP_region          =  mem: [from __region_S_APP_start__  to __region_S_APP_end__ ];


place at start of  D_VECTOR_region { block VECTOR_WBLOCK };                          
                          
place at start of  D_ATCM_region  {  
                        block LDR_PRG_WBLOCK,
                        };
place in D_ATCM_region  {  
                           block USER_CUSTOM_PRG_WBLOCK,
						   section USER_CUSTOM_DATA_WBLOCK, block USER_CUSTOM_DATA_WBLOCK,
						   section USER_CUSTOM_DATA_ZBLOCK, block USER_CUSTOM_DATA_ZBLOCK,  
                           
                         };                             
place in BTCM_region         {   
								 section LDR_DATA_WBLOCK, block LDR_DATA_WBLOCK,                            
                                 section LDR_DATA_ZBLOCK, block LDR_DATA_ZBLOCK,
								 
                                 readwrite, 
                                 rw section .sys_stack,
                                 rw section .svc_stack,
                                 rw section .irq_stack,
                                 rw section .fiq_stack,
                                 rw section .und_stack,
                                 rw section .abt_stack,    
                                 
                               
                                 
                              };
                     
place in SYSTEM_RAM_region   { 
                               block USER_PRG_WBLOCK,
							   block USER_DATA_WBLOCK, 
                               block USER_DATA_ZBLOCK,
                               rw section HEAP,
                               rw section .stack* };                          


                          
place in S_LDR_region { block LDR_PRG_RBLOCK,
                        section LDR_DATA_RBLOCK,
                        block LDR_DATA_RBLOCK ,
                        };
                        
                        
place at start  of S_LOADER_TBL_region {readonly section user_loader_tbl};                  

place at start of S_VECTOR_region {block VECTOR_RBLOCK};


place in S_APP_region  { 
                         block USER_CUSTOM_PRG_RBLOCK,
                         block USER_CUSTOM_DATA_RBLOCK,
                         block USER_PRG_RBLOCK,
                         block USER_DATA_RBLOCK, 
                         readonly,
                         };                         



place in DMAC_LINK_MODE_region { block DMAC_LINK_MODE_ZBLOCK };
place in SHARED_NONCACHE_BUFFER_region { block SHARED_NONCACHE_BUFFER_ZBLOCK };
place in NONCACHE_BUFFER_region { block NONCACHE_BUFFER_ZBLOCK };
place in SYSTEM_RAM_MIRROR_region { };
place in XSPI0_CS0_MIRROR_region { };
place in XSPI0_CS1_MIRROR_region { };
place in XSPI1_CS0_MIRROR_region { };
place in XSPI1_CS1_MIRROR_region { };
place in CS0_MIRROR_region { };
place in CS2_MIRROR_region { };
place in CS3_MIRROR_region { };
place in CS5_MIRROR_region { };
place in XSPI0_CS0_region { };
place in XSPI0_CS1_region { };
place in XSPI1_CS0_region { };
place in XSPI1_CS1_region { };
place in CS0_region { };
place in CS2_region { };
place in CS3_region { };
place in CS5_region { };