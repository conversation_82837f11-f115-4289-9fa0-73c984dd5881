/* generated vector source file - do not edit */
        #include "bsp_api.h"
        /* Do not build these data structures if no interrupts are currently allocated because IAR will have build errors. */
        #if VECTOR_DATA_IRQ_COUNT > 0
        BSP_DONT_REMOVE const fsp_vector_t g_vector_table[BSP_ICU_VECTOR_MAX_ENTRIES] =
        {
                        [0] = r_intcpu0_isr, /* INTCPU0 (Software interrupt 0) */
            [1] = r_intcpu1_isr, /* INTCPU1 (Software interrupt 1) */
            [21] = dmac_int_isr, /* DMAC0_INT0 (DMAC0 transfer completion 0) */
            [22] = dmac_int_isr, /* DMAC0_INT1 (DMAC0 transfer completion 1) */
            [53] = cmt_cm_int_isr, /* CMT0_CMI (CMT0 Compare match) */
            [54] = null, /* CMT1_CMI (CMT1 Compare match) */
            [57] = r_cmt4_isr, /* CMT4_CMI (CMT4 Compare match) */
            [69] = null, /* TGIA0 (MTU0.TGRA input capture/compare match) */
            [70] = null, /* TGIB0 (MTU0.TGRB input capture/compare match) */
            [71] = null, /* TGIC0 (MTU0.TGRC input capture/compare match) */
            [72] = r_mtu0_tgid_isr, /* TGID0 (MTU0.TGRD input capture/compare match) */
            [73] = null, /* TCIV0 (MTU0.TCNT overflow) */
            [74] = null, /* TGIE0 (MTU0.TGRE compare match) */
            [75] = null, /* TGIF0 (MTU0.TGRF compare match) */
            [84] = r_mtu3_tgia_isr, /* TGIA3 (MTU3.TGRA input capture/compare match) */
            [93] = null, /* TCIV4 (MTU4.TCNT overflow/underflow) */
            [107] = r_mtu8_tgia_isr, /* TGIA8 (MTU8.TGRA input capture/compare match) */
            [239] = null, /* GPT13_OVF (GPT13 GTCNT overflow (GTPR compare match)) */
            [277] = ethercat_ssc_port_isr_esc_sync0, /* ESC_SYNC0 (EtherCAT Sync0 interrupt) */
            [278] = ethercat_ssc_port_isr_esc_sync1, /* ESC_SYNC1 (EtherCAT Sync1 interrupt) */
            [279] = ethercat_ssc_port_isr_esc_cat, /* ESC_CAT (EtherCAT interrupt) */
            [285] = null, /* USB_FI (USB (Function) interrupt) */
            [286] = null, /* USB_FDMA0 (USB (Function) DMA 0 transmit completion) */
            [287] = null, /* USB_FDMA1 (USB (Function) DMA 1 transmit completion) */
            [288] = r_sci0_eri_isr, /* SCI0_ERI (SCI0 Receive error) */
            [289] = r_sci0_rxi_isr, /* SCI0_RXI (SCI0 Receive data full) */
            [290] = null, /* SCI0_TXI (SCI0 Transmit data empty) */
            [291] = r_sci0_tei_isr, /* SCI0_TEI (SCI0 Transmit end) */
            [292] = null, /* SCI1_ERI (SCI1 Receive error) */
            [293] = null, /* SCI1_RXI (SCI1 Receive data full) */
            [294] = null, /* SCI1_TXI (SCI1 Transmit data empty) */
            [295] = null, /* SCI1_TEI (SCI1 Transmit end) */
            [296] = null, /* SCI2_ERI (SCI2 Receive error) */
            [297] = null, /* SCI2_RXI (SCI2 Receive data full) */
            [298] = null, /* SCI2_TXI (SCI2 Transmit data empty) */
            [299] = null, /* SCI2_TEI (SCI2 Transmit end) */
            [300] = null, /* SCI3_ERI (SCI3 Receive error) */
            [301] = null, /* SCI3_RXI (SCI3 Receive data full) */
            [302] = null, /* SCI3_TXI (SCI3 Transmit data empty) */
            [303] = null, /* SCI3_TEI (SCI3 Transmit end) */
            [304] = r_sci4_eri_isr, /* SCI4_ERI (SCI4 Receive error) */
            [305] = r_sci4_rxi_isr, /* SCI4_RXI (SCI4 Receive data full) */
            [306] = null, /* SCI4_TXI (SCI4 Transmit data empty) */
            [307] = r_sci4_tei_isr, /* SCI4_TEI (SCI4 Transmit end) */
            [312] = null, /* IIC1_EEI (IIC1 Transfer error or event generation) */
            [313] = null, /* IIC1_RXI (IIC1 Receive data full) */
            [314] = null, /* IIC1_TXI (IIC1 Transmit data empty) */
            [315] = null, /* IIC1_TEI (IIC1 Transmit end) */
            [316] = canfd_rx_fifo_isr, /* CAN_RXF (CANFD RX FIFO interrupt) */
			            [317] = canfd_error_isr, /* CAN_GLERR (CANFD Global error interrupt) */
            [318] = canfd_channel_tx_isr, /* CAN0_TX (CANFD0 Channel TX interrupt) */
            [319] = canfd_error_isr, /* CAN0_CHERR (CANFD0 Channel CAN error interrupt) */
			[320] = canfd_common_fifo_rx_isr, /* CAN0_COMFRX (CANFD0 Common RX FIFO or TXQ interrupt) */
            [329] = spi_rxi_isr, /* SPI1_SPRI (SPI1 Reception buffer full) */
            [330] = spi_txi_isr, /* SPI1_SPTI (SPI1 Transmit buffer empty) */
            [332] = spi_eri_isr, /* SPI1_SPEI (SPI1 errors) */
            [333] = spi_tei_isr, /* SPI1_SPCEND (SPI1 Communication complete) */
            [343] = null, /* DSMIF0_CDRUI (DSMIF0 current data register update (ORed ch0 to ch2)) */
            [344] = null, /* DSMIF1_CDRUI (DSMIF1 current data register update (ORed ch3 to ch5)) */
            [439] = null, /* IIC2_EEI (IIC2 Transfer error or event generation) */
            [440] = null, /* IIC2_RXI (IIC2 Receive data full) */
            [441] = null, /* IIC2_TXI (IIC2 Transmit data empty) */
            [442] = null, /* IIC2_TEI (IIC2 Transmit end) */
            [443] = spi_rxi_isr, /* SPI3_SPRI (SPI3 Reception buffer full) */
            [444] = spi_txi_isr, /* SPI3_SPTI (SPI3 Transmit buffer empty) */
            [446] = spi_eri_isr, /* SPI3_SPEI (SPI3 errors) */
            [447] = spi_tei_isr, /* SPI3_SPCEND (SPI3 Communication complete) */
        };

        #endif