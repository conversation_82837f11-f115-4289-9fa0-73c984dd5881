#include "hal_data.h"
#include "bsp.h"
#include "flash_if.h"
#include "cmd_otp.h"
#include "menu.h"

FSP_CPP_HEADER
void R_BSP_WarmStart(bsp_warm_start_event_t event) BSP_PLACE_IN_SECTION(".warm_start");
FSP_CPP_FOOTER

uint8_t  UpgradeMark = 0;
uint32_t BackgroudCNT = 0;
uint8_t  BackgroudMark = 0;

extern uint32_t UpgradeAddr;


#define APP2_SHIFT1_ADDR       (0x100000UL)

/* loader_table definition */
typedef struct {
    uint32_t   prgsrc;
    uint32_t   prgdst;
    uint32_t   prgsize;
    uint32_t   datasrc;
    uint32_t   datadst;
    uint32_t   datasize;   
} loader_table;


loader_table *loader_param = NULL;

extern void bsp_copy_4byte(uint32_t * src, uint32_t * dst, uint32_t bytesize);

uint8_t uidbuff[8] = {0};

// add by lly 20231125
#pragma section="init_uid"

// add by lly 20231125
#pragma location="init_uid"

    __root const uint32_t Flash_Uid[6] =
    {   
        0xFFFFFFFF,  // 
        0xFFFFFFFF,  //         
        0x0F16EE04,  // 
        0xF7f92001,  //   
        0x880A800A,  // 
        0x491BFC35,  //           
    };
uint32_t  Uidflashaddr =  (uint32_t) __section_begin("init_uid");

#define QSPI_WRITE_MASK           ((uint32_t)0xFFFFFFC0UL)
/*******************************************************************************************************************//**
 * main() is generated by the FSP Configuration editor and is used to generate threads if an RTOS is used.  This function
 * is called by main() when no RTOS is used.
 **********************************************************************************************************************/
void hal_entry(void)
{
    uint8_t  UpgradeFlag= 0;
    uint32_t dest_addr = 0U;
    uint8_t Uid[8] = {0};
    uint16_t WriteUid = 1;
    ret_err_t ret      = RET_FAIL;

    fsp_err_t fsp_err;
    
    
    
    user_bsp_init();
    
    
    /* TODO: add your own code here */
    R_XSPI_QSPI_Open(&g_qspi1_ctrl, &g_qspi1_cfg);
     
   *(uint32_t *)&Uid[0] = *(uint32_t *)Uidflashaddr;
   *(uint32_t *)&Uid[4] = *(uint32_t *)(Uidflashaddr+4);

    if((*(uint32_t *)&Uid[0] != 0xFFFFFFFF) && (*(uint32_t *)&Uid[1] != 0xFFFFFFFF))
    {
        WriteUid = 0;
    }   
    
       /* read uid and write uid in flash */
    cmd_get_unique_id(&uidbuff[0]);   
  
    
    if(WriteUid == 1)
    {
       /* read uid and write uid in flash */
        cmd_get_unique_id(&uidbuff[0]);      
       /* Write data. */
        uint8_t src_data[64];
        
        memset(src_data, 0xFFU, 64);
        
        for (uint32_t i = 0U; i < 8; i++)
        {
            src_data[i] = uidbuff[i];
        }        
        fsp_err = R_XSPI_QSPI_Write(&g_qspi1_ctrl, src_data, (uint8_t *)((Uidflashaddr) & QSPI_WRITE_MASK), 8);      
        
        if (FSP_SUCCESS != fsp_err)
        {
//            flash_err = FLASH_ERR_MODULE;
        }
        
        /* Wait for status register to update. */
        FSP_HARDWARE_REGISTER_WAIT((&g_qspi1_ctrl)->p_reg->COMSTT_b.WRBUFNE, 0);
        FSP_HARDWARE_REGISTER_WAIT((&g_qspi1_ctrl)->p_reg->COMSTT_b.MEMACC, 0);
        
        spi_flash_status_t status_write;
        do
        {
            (void) R_XSPI_QSPI_StatusGet(&g_qspi1_ctrl, &status_write);
        } while (true == status_write.write_in_progress);        
    }
    
     
#if 1  
    while (BackgroudCNT++ <= 3)
    {

         /* Receive key */
        R_BSP_PinWrite(PORT_485SS_DE,PIN_LEVEL_LOW);
        userdef_scifa0_uart_receive(&BackgroudMark,1,1000);
        
    }
    
    if(BackgroudMark == '1')
    {
      UpgradeMark = 0x01; 
      FLASH_If_Write(UpgradeAddr, &UpgradeMark ,1);           
      
      Main_Menu ();
    }
    else
#endif      
    {
      // 禁止usb中断
      UpgradeFlag = *(uint8_t *)UpgradeMark_ADDRESS_START;

      ret = load_user_application(&dest_addr,UpgradeFlag);
       
      if (RET_SUCCESS == ret) 
      {
          R_BSP_IrqDisable(VECTOR_NUMBER_USB_FI);  
          /* Jump to dest_addr. Launch the UserApp or Update program. */
          void (*application_main)(void);
          
          __disable_irq();
          
          __asm volatile("dsb");
          application_main = (void(*)(void))dest_addr;
          application_main();
      }   
      else
      { 
        UpgradeMark = 0x01; 
        FLASH_If_Write(UpgradeAddr, &UpgradeMark ,1);     

        Main_Menu ();
      }
    }

    while(1)
    {
       /* Toggle board LEDs */
      
    }
}

/*******************************************************************************************************************//**
 * This function is called at various points during the startup process.  This implementation uses the event that is
 * called right before main() to set up the pins.
 *
 * @param[in]  event    Where at in the start up process the code is currently at
 **********************************************************************************************************************/
ret_err_t load_user_application (uint32_t *p_addr,uint8_t UpgradeFlag)
{
    ret_err_t ret = RET_FAIL;
    uint32_t * srcaddr = NULL; 
    uint32_t * dstaddr = NULL; 
    uint32_t size  = 0;
    
    uint32_t * checkaddr1 = NULL; 
    uint32_t * checkaddr2 = NULL; 
    
    
    /* check the flash have program */
    loader_param = (loader_table*)0x68100000;
    checkaddr1 = (uint32_t *)loader_param->prgsrc;
    
    /* check the flash have program */
    loader_param = (loader_table*)0x68200000;
    checkaddr2 = (uint32_t *)loader_param->prgsrc;    
    
    if((checkaddr1 == (uint32_t *)0xFFFFFFFF) && (UpgradeFlag == 0xFF)) //flash is new and no program in flash 
      return ret;
   
    if((checkaddr2 == (uint32_t *)0xFFFFFFFF) && (UpgradeFlag == 0x01)) //flash is new and no program in flash 
      return ret;
    
    
    do
    {
      if(UpgradeFlag == 0xFF)
      {
         loader_param = (loader_table*)0x68100000;
         srcaddr = (uint32_t *)loader_param->prgsrc;
         dstaddr = (uint32_t *)loader_param->prgdst;
         size = loader_param->prgsize; 
         bsp_copy_4byte(srcaddr, dstaddr, size);
         
        // bsp_copy_4byte((uint32_t *)loader_param->datasrc, (uint32_t *)loader_param->datadst, loader_param->datasize);           
      }
      else if(UpgradeFlag == 0x01)
      {
         loader_param = (loader_table*)0x68200000;
         srcaddr = (uint32_t *)(loader_param->prgsrc + APP2_SHIFT1_ADDR);
         dstaddr = (uint32_t *)loader_param->prgdst;
         size = loader_param->prgsize; 
           
         bsp_copy_4byte(srcaddr, dstaddr, size);  
       
        // bsp_copy_4byte((uint32_t *)loader_param->datasrc, (uint32_t *)loader_param->datadst, loader_param->datasize);                 
      }
      else
      {
         loader_param = (loader_table*)0x68200000;
         srcaddr = (uint32_t *)(loader_param->prgsrc + APP2_SHIFT1_ADDR);
         dstaddr = (uint32_t *)loader_param->prgdst;
         size = loader_param->prgsize; 
           
         bsp_copy_4byte(srcaddr, dstaddr, size);             
        // bsp_copy_4byte((uint32_t *)loader_param->datasrc, (uint32_t *)loader_param->datadst, loader_param->datasize);    
      }
       
       
      *p_addr = (uint32_t)loader_param->prgdst;

       ret = RET_SUCCESS;
        
    } while (0);
    
    return ret;
}  
/*******************************************************************************************************************//**
 * This function is called at various points during the startup process.  This implementation uses the event that is
 * called right before main() to set up the pins.
 *
 * @param[in]  event    Where at in the start up process the code is currently at
 **********************************************************************************************************************/
void R_BSP_WarmStart (bsp_warm_start_event_t event)
{
    if (BSP_WARM_START_RESET == event)
    {
    	/* Pre clock initialization */
    }

    if (BSP_WARM_START_POST_C == event)
    {
        /* C runtime environment and system clocks are setup. */

        /* Configure pins. */
        R_IOPORT_Open(&g_ioport_ctrl, &g_bsp_pin_cfg);
    }
}
