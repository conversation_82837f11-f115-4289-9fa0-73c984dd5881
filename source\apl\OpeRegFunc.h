/****************************************************************************************************
 *
 * FILE NAME:  OpeRegFunc.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2020.08.12
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	12-08-2020 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _OPE_REG_FUNC_H_
#define _OPE_REG_FUNC_H_


#include "RegAccessIf.h"


extern const PRM_TBL   OpeRegTable[];
extern const UINT32   OpeRegTableEntNum;

PRM_RSLT fnCalcOprationMode1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, void *Axis, INT32 *pValue);
PRM_RSLT fnCalcOprationCmd1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, void *Axis, INT32 *pValue);
PRM_RSLT fnCalcOperationFinish(PRM_ACCCMD Cmd, UINT32 ArrayIdx, void *Axis, INT32 *pValue);
PRM_RSLT fnCalcAccessLevel(PRM_ACCCMD Cmd, UINT32 ArrayIdx, void *Axis, INT32 *pValue);
PRM_RSLT fnCalcOprationMode2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, void *Axis, INT32 *pValue);
PRM_RSLT fnCalcOprationCmd2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, void *Axis, INT32 *pValue);

PRM_RSLT FnAdatFilType(PRM_ACCCMD Cmd, UINT32 ArrayIdx, void *Axis, INT32 *pValue);


#endif

