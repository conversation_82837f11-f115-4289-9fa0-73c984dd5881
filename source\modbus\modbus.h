/****************************************************************************************************
 *
 * FILE NAME:  modbus.h
 *
 * DESCRIPTION: 
 *
 * CREATED ON:  2019.06.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-06-2019 Version 1.00 : Created by Denny
****************************************************************************************************/

#ifndef _MODBUS_H_
#define _MODBUS_H_

#define MODBUS_STATE_INIT    0
#define MODBUS_STATE_IDLE    1
#define MODBUS_STATE_RECEIVE 2
#define MODBUS_STATE_CTRL    3
#define MODBUS_STATE_SEND    4
#define MODBUS_STATE_ERROR   5 
   
#define MODBUS_RX_ISEMPTY    0    



#define MODBUS_OK      0
#define MODBUS_CMD_ERROR     1
#define MODBUS_CRC_ERROR     2
#define MODBUS_ADDR_ERROR    3
#define MODBUS_PRM_ERROR     4


#define ADDR                   0    //帧地址域，1~247
#define COMMCMD                1    //读写标志域，03读，06写
#define INEDXH                 2    //参数地址高有效字节，对应功能码组号
#define INEDXL                 3    //参数地址低有效字节，对应功能码组偏移量
#define DATALH                 4    //数据长度高有效字节
#define DATALL                 5    //数据长度低有效字节
#define CRCL                   6    //CRC校验低有效字节
#define CRCH                   7    //CRC校验高有效字节


typedef	struct 
{ 
    UINT16 CmdErr:1;            // 1  命令码错误 Err2
    UINT16 CrcChkErr:1;         // 2  CRC校验故障 Err3
    UINT16 AddrOver:1;          // 3  无效地址 Err4
    UINT16 ParaOver:1;          // 4  无效参数 Err5
    UINT16 DataReadWrite:1;     // 5  数据写禁止操作
    UINT16 Rsvd:10;             // 15 保留            
}MODBUS_ST_ERROR;



typedef	struct 
{ 
    struct
    {
      UINT32  C3P5threshold;
      UINT32  C1P5threshold; 
      BOOL    Endian;
    }P;

   
	UINT16     State;                              // Modbus  Schedua state
    UINT16     RxState;  
    UINT16     RxLength;
    UINT16     TxState;
    UINT32     C3P5Cnt;
    UINT32     C1P5Cnt;  
    BOOL       ErrorFlag;
    BOOL       RxEndFlag;
    
    UINT8      RxData[256];
    UINT8      TxData[256];
    
    MODBUS_ST_ERROR  ErrBit;

}MODBUS_ST;

extern MODBUS_ST ModbusStruct; 


void ModbusInit(MODBUS_ST *Modbus,UINT8 BuadIndex, UINT8 Format,BOOL Endian);
void ModbusSchedual(MODBUS_ST *ModbusStruct);

void ModbusRead (MODBUS_ST *modbus,UINT8 SlaveID);
void ModbusWriteOne(MODBUS_ST *modbus,UINT8 SlaveID);
void ModbusWrite(MODBUS_ST *modbus,UINT8 SlaveID);
BOOL ModBusTransmit(MODBUS_ST *modbus, UINT8 SlaveID);
#endif // end #ifndef _MODBUS_H_