/*
 * bsp_spi.h
 *
 *  Created on: Oct 25, 2023
 *      Author: xgj12
 */

#ifndef BSP_SPI_H_
#define BSP_SPI_H_
#include "r_spi.h"


#define SPI_CH0       (0)
#define SPI_CH1       (1)

#define TRANSFER_SIZE   (2)
#define SPI_ERROR_MASK  (R_SPI0_SPSR_OVRF_Pos|R_SPI0_SPSR_MODF_Pos|R_SPI0_SPSR_PERF_Pos|R_SPI0_SPSR_UDRF_Pos)



void bsp_spi_transfer(uint32_t *tx_data, uint16_t RxLenght, uint16_t TxLenght,uint8_t Channel); 
uint8_t bsp_spi_receive(uint32_t *rx_data, uint16_t length, uint8_t Channel);
uint8_t bsp_spi_txstate(uint8_t Channel);
uint8_t bsp_spi_rxstate(uint8_t Channel);
void bsp_spi_readwrite(uint32_t *tx_data, uint16_t RxLenght, uint16_t TxLenght,uint8_t Channel);

// spi3 init

#define SPI3_BISSC_MODE   0
#define SPI3_KTM59XX_MODE 1
#define SPI3_AM600_MODE   2
void bsp_spi3_init(uint16_t mode);
void bsp_spi3_close(void);

#endif /* BSP_SPI_H_ */
