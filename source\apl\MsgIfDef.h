/****************************************************************************************************
 *
 * FILE NAME:  MsgIfDef.h
 *
 * DESCRIPTION:  Message Interface Define Header File
 *
 * CREATED ON:  2019.11.01
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	01-11-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef	MSFGIF_DEF_H_
#define	MSFGIF_DEF_H_


#include "Sif.h"



/****************************************************************************************************/
/*																									*/
/*		Define Function Code, Error Code, etc														*/
/*																									*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Main Function Code Definition																*/
/*--------------------------------------------------------------------------------------------------*/
#define	MFC_NONE			0x00				// Not used											
#define	MFC_LOOPBACK		0x08				// Loopback test								
#define	MFC_SVXMSG16		0x40				// Servo expansion message								
#define	MFC_SVXMSG32		0x41				// Servo expansion message								
#define	MFC_MECHATROLINK	0x42				// MECHATROLINK Message								
#define	MFC_SYSDWNLD		0x50				// System download							
#define	MFC_RELAY 			0x51				// Relay command									

/*--------------------------------------------------------------------------------------------------*/
/*		Sub Function Code Definition for 40H/41H													*/
/*--------------------------------------------------------------------------------------------------*/
#define	SFC_RD_ONEREG		0x40				// Single register read								
#define	SFC_WR_ONEREG		0x06				// Single register write							
#define	SFC_RD_SEQREG		0x03				// Continuous register read								
#define	SFC_WR_SEQREG		0x10				// Continuous register write								
#define	SFC_RD_RANREG		0x0D				// Non-continuous register read								
#define	SFC_WR_RANREG		0x0E				// Non-consecutive register write								
#define	SFC_RD_MAXNUM		0x41				// Read the maximum number of registers							
/*--------------------------------------------------------------------------------------------------*/
#define	SFC_RD_SEQMEM		0x50				// Continuous memory read									
#define	SFC_WR_SEQMEM		0x51				// Continuous memory writing									

/*--------------------------------------------------------------------------------------------------*/
/*		Sub Function Code Definition for 42H														*/
/*--------------------------------------------------------------------------------------------------*/
#define	SFC_RD_REQREG_42	0x01				// Read memory										
#define	SFC_WR_SEQREG_42	0x02				// Write memory										
#define	SFC_RD_RANREG_42	0x03				// Memory read (non-continuous)						
#define	SFC_WR_RANREG_42	0x04				// Memory write (non-contiguous)							
#define	SFC_RD_MAXNUM_42	0x11				// Read maximum message size							
#define	SFC_RD_PRDCTID_42	0x7F				// Read model information									
/*--------------------------------------------------------------------------------------------------*/
#define	MBUS42_HEADERSIZE	0x08				// 42H command header size								
#define	MBUS42_ADRSIZE		0x04				// 42H Command address part size								
#define	MBUS42_DATASIZEL	0x04				// 42H Command data part size (during non-continuous writing)				
#define	MBUS42_CRC_SIZE		0x02				// 42H command CRC section size								
#define	MBUS42_DTYPE_B		0x01				// Data type: Byte type(8bit)								
#define	MBUS42_DTYPE_S		0x02				// Data type: Short type(16bit)							
#define	MBUS42_DTYPE_L		0x03				// Data type: Long type(32bit)								
/*--------------------------------------------------------------------------------------------------*/
/*		Sub Function Code Definition for 51H														*/
/*--------------------------------------------------------------------------------------------------*/
#define	MBUS51_FIX_SIZE			0x0E			// Relay command fixed size								
#define	MBUS51_PASS_DATA_SIZE	0x08			// Relay command variable size (changes in multiples of this size)
#define	MBUS51_MAX_ROUTENUM		0x0A			// Maximum number of relay stages							

/*--------------------------------------------------------------------------------------------------*/
/*		Download			SubFuncCode		   S:Support, -:Not Support								*/
/*--------------------------------------------------------------------------------------------------*/
#define	SFC_DLID_RD			0x0000				// S : Read ID information								
#define	SFC_SWVER_RD		0x0001				// S : Program write condition acquisition						
#define	SFC_DLWAY_RD		0x0002				// S : Program write condition acquisition						
#define	SFC_DL_START		0x0003				// S : Start downloading									
#define	SFC_DL_PRGDEL		0x0004				// S : Block erase									
#define	SFC_DL_PRGWR		0x0005				// S : Program writing								
#define	SFC_DL_DELSND		0x0006				// - : Block erase information (erasure information transfer only, not actual erase)
#define	SFC_DL_PRGSND		0x0007				// - : Program transmission (transmission only, not actual writing)	
#define	SFC_DL_PRGDELA		0x0008				// - : Block batch erase command							
#define	SFC_DL_PRGWRA		0x0009				// - : Program batch write command						
#define	SFC_DL_END			0x000A				// S : Download end command								
#define	SFC_DL_PRGRD		0x000B				// S : Program read command							
#define	SFC_DL_REBOOT		0x000C				// S : Program restart command							
#define	SFC_DL_SIFCHG		0x000D				// S : Serial IF speed change								
#define	SFC_DL_HIS_RD		0x000E				// S : Download history download							
#define	SFC_DL_HIS_WR		0x000F				// S : Download history read/write							
/*--------------------------------------------------------------------------------------------------*/
/*		Error Code Definition																		*/
/*--------------------------------------------------------------------------------------------------*/
#define	ECD_ERR_MFC			0x00				// Error : Main Function Code						
#define	ECD_ERR_SFC			0x00				// Error : Sub Function Code						
/*--------------------------------------------------------------------------------------------------*/
/*		Memobus Error Code Definition															    */
/*--------------------------------------------------------------------------------------------------*/
#define	SMBUS_EC_FNCDERR	(0x01 | 0x200)		// Function code error							
#define	SMBUS_EC_REGNOERR	(0x02 | 0x200)		// Bad register number									
#define	SMBUS_EC_DANUMERR	(0x03 | 0x200)		// Number defect abnormality										
/*--------------------------------------------------------------------------------------------------*/
#define	SMBUS_SEC_REGNOERR	(0x30 | 0x200)		// Bad register number (high level)				
#define	SMBUS_SEC_ACCSERR	(0x31 | 0x200)		// Access restriction error								
#define	SMBUS_SEC_DALMTERR	(0x32 | 0x200)		// Setting value out of range error								
#define	SMBUS_SEC_DAMTHERR	(0x33 | 0x200)		// Data consistency error 								
#define	SMBUS_SEC_CONDERR	(0x34 | 0x200)		// Condition error										
#define	SMBUS_SEC_INTLOCK	(0x35 | 0x200)		// Processing conflict error									
/*--------------------------------------------------------------------------------------------------*/
/*		Response/no response definition at the start of transmission								*/
/*--------------------------------------------------------------------------------------------------*/
#define	RESMT_COMPLETE		0x01				// MsgType : Completion response								
#define	RESMT_NORESPONSE	0x00				// MsgType : No response (message abnormal)				
/*--------------------------------------------------------------------------------------------------*/
/*		Message error code definition															    */
/*--------------------------------------------------------------------------------------------------*/
#define	SMSGEC_FNCDERR		0x10				// Function code error							
#define	SMSGEC_SEQXERR		0x10				// Communication sequence error (continuous transmission) 
#define	SMSGEC_CMDXERR		0x14				// Download start command error 	
/*--------------------------------------------------------------------------------------------------*/
/*		Auxiliary function response hold															*/
/*--------------------------------------------------------------------------------------------------*/
#define	REGRES_NOHOLD		0x00				// Auxiliary function response hold release
#define	REGRES_HOLD			0x01				// Auxiliary function response hold

/****************************************************************************************************/
/*																									*/
/*		Register IF Data Definition																	*/
/*																									*/
/****************************************************************************************************/
/*--------------------------------------------------------------------------------------------------*/
/*		Register IF function normal return value definition											*/
/*--------------------------------------------------------------------------------------------------*/
#define	REGIF_COMPLETE		0x00		// Processing complete							
#define	REGIF_CONTINUE		0x01		// Continue processing							
/*--------------------------------------------------------------------------------------------------*/
/*		Register IF function error return value definition (also used as message error code)	      */
/*--------------------------------------------------------------------------------------------------*/
#define	REGIF_CMDERR		0x10		// Register IF command error			
#define	REGIF_REGNOERR		0x11		// Register number error 				
#define	REGIF_DALMTERR		0x12		// Data error (upper / lower limit error)
#define	REGIF_DACALERR		0x13		// Data error (calculation error)			
#define	REGIF_DAMTHERR		0x14		// Data integrity error 				
#define	REGIF_DAMTHERR2		(0x14 | 0x100)		// Data integrity error 		
#define	REGIF_DANUMERR		0x15		// Abnormal number of data   				
#define	REGIF_FOPEERR1		0x30		// During operation mode occupied by other channels 	
#define	REGIF_FOPEERR2		0x31		// Occurrence of operation mode occupied by other channels	
#define	REGIF_FOPEERR3		0x3F		// Processing competition with other channels				
#define	REGIF_FOPEERR4		0x3F		// Auxiliary function execution error					


/****************************************************************************************************/
/*																									*/
/*		Message Interface Struct Definition															*/
/*																									*/
/****************************************************************************************************/
typedef	struct	
{				
	struct	                                    // Hmsg identification flag
	{														
		UINT32	OpeMsgIF		:1;				/* Operator identification flag						*/
		UINT32	PopMsgIF		:1;				/* Panel operator identification flag				*/
		UINT32	LopMsgIF		:1;				/* LCD operator identification flag					*/
		UINT32	XxxOpeIF		:1;				/* Reserved											*/
	/*----------------------------------------------------------------------------------------------*/
		UINT32	ComMsgIF		:1;				/* Communication message identification flag		*/
		UINT32	SifMsgIF		:1;				/* Serial communication message identification flag	*/
		UINT32	UsbMsgIF		:1;				/* USB communication message identification flag	*/
		UINT32	NetMsgIF		:1;				/* Network message identification flag				*/
		UINT32	LnkMsgIF		:1;				/* Link communication message identification flag	*/
		UINT32	MlnkMsgIF		:1;				/* Mechatronic link communication message identification flaG*/
	/*----------------------------------------------------------------------------------------------*/
		UINT32	SpareFlags		:22;			// Spare											
	} f;									   
/*--------------------------------------------------------------------------------------------------*/
	CSIF   *Sif;								/* Pointer to the Serial interface					*/
	UINT8  *CmdBuf;								/* Pointer to the Command Message Buffer			*/
	UINT8  *ResBuf;								/* Pointer to the Response Message Buffer			*/
/*--------------------------------------------------------------------------------------------------*/
	UINT16	RcvBufSize;							/* Receive Message Buffer Size						*/
	UINT16	SndBufSize;							/* Send Message Buffer Size							*/
/*--------------------------------------------------------------------------------------------------*/
	UINT16	CmdMsgLen;							/* Command Message Length							*/
	UINT16	AccessLvl;							/* Access Level USER1/USER2/SYSTEM)					*/
/*--------------------------------------------------------------------------------------------------*/
	UINT16	ComMode;							/* Communication Mode (LCDOPE,MSG,etc)				*/
	UINT16	ComPortType;						/* Channel Information ( not used )					*/
/*--------------------------------------------------------------------------------------------------*/
	UINT16	ErrRno;								/* Error Register Number							*/
	UINT16	LstErrRno;							/* Last Error Register Number						*/
/*--------------------------------------------------------------------------------------------------*/
	UINT16	FunSts;								/* Function Execution Status						*/
	UINT8	LastAxisIdx;						/* Multi-Axis : Last Axis Index						*/
	UINT8	ReserveChar0;						/* Reserved											*/
/*--------------------------------------------------------------------------------------------------*/
	UINT32	ComChkTime;							/* Timeout Timer									*/
/*--------------------------------------------------------------------------------------------------*/
	void	*pAxRsc;					
/*--------------------------------------------------------------------------------------------------*/
	UINT16	OpeDspMode;							/* Operator user parameter display selection		*/	
	UINT16	LstOpeDspMode;						/* Operator user parameter display selection		*/	

	void	*AxisMap;				

} MSGIF;

typedef	MSGIF*	HMSGIF;						


/*--------------------------------------------------------------------------------------------------*/
    /* Message Handle Check Macro Definition*/
/*--------------------------------------------------------------------------------------------------*/
#define	KPI_CHK_OpeHmsg( Hmsg )		( (Hmsg)->f.OpeMsgIF )
#define	KPI_CHK_LopHmsg( Hmsg )		( (Hmsg)->f.LopMsgIF )
#define	KPI_CHK_ComHmsg( Hmsg )		( (Hmsg)->f.ComMsgIF )
#define	KPI_CHK_SifHmsg( Hmsg )		( (Hmsg)->f.SifMsgIF )
#define	KPI_CHK_UsbHmsg( Hmsg )		( (Hmsg)->f.UsbMsgIF )
#define	KPI_CHK_NetHmsg( Hmsg )		( (Hmsg)->f.NetMsgIF )
#define	KPI_CHK_LnkHmsg( Hmsg )		( (Hmsg)->f.LnkMsgIF )
#define	KPI_CHK_PopHmsg( Hmsg )		( (Hmsg)->f.PopMsgIF )

/*--------------------------------------------------------------------------------------------------*/
/*
 *	Massage Handle Initialization
 *
 *  KRI_INIT_MSGIF() : Initialize Message Handle
 *  KRI_BIND_MSGIF() : Bind a Message Handle to Other Message Handle
 *
 */
/*--------------------------------------------------------------------------------------------------*/
#define KRI_INIT_MSGIF( hmsg, csif )	{ 		/* bind CSIF to Message IF */ 	\
		MlibResetLongMemory( hmsg, sizeof(MSGIF)/4 );	/* reset memory */		\
		(hmsg)->Sif = (csif);					/* set sif */			\
		(hmsg)->CmdBuf = (csif)->RxBuf;			/* set cmd buf */ 		\
		(hmsg)->ResBuf = (csif)->TxBuf;			/* set res buf */		\
		(hmsg)->RcvBufSize = (csif)->RcvBufSize;/* set rcv buf size */ \
		(hmsg)->SndBufSize = (csif)->RcvBufSize;/* set send buf size */\
	}
/*--------------------------------------------------------------------------------------------------*/
#define KRI_BIND_MSGIF( hmsg, src )	{ 			/* bind Message IF to other Message IF */ 	\
		(hmsg)->Sif = (src)->Sif;				/* set sif */			\
		(hmsg)->CmdBuf = (src)->CmdBuf;			/* set cmd buf */ 		\
		(hmsg)->ResBuf = (src)->ResBuf;			/* set res buf */		\
		(hmsg)->RcvBufSize = (src)->RcvBufSize; /* set rcv buf size */ \
		(hmsg)->SndBufSize = (src)->RcvBufSize; /* set send buf size */\
	}

/*--------------------------------------------------------------------------------------------------*/
/*
 * Message Handle Flag Initialization
 * 
 * KRI_SET_FLG_LCDOPE() : for Lcd Operator
 * KRI_SET_FLG_SRLMSG() : for UART message 
 * KRI_SET_FLG_USBMSG() : for USB message
 * KRI_SET_FLG_PNLOPE() : for Panel Operator
 *
 */
/*--------------------------------------------------------------------------------------------------*/
#define KRI_SET_FLG_LCDOPE(hMsg)	{ \
					*(UINT *)&((hMsg)->f) = (UINT)0; \
					(hMsg)->f.OpeMsgIF = 1; \
					(hMsg)->f.LopMsgIF = 1; \
				}
#define KRI_SET_FLG_SRLMSG(hMsg)	{ \
					*(UINT *)&((hMsg)->f) = (UINT)0; \
					(hMsg)->f.ComMsgIF = 1; \
					(hMsg)->f.SifMsgIF = 1; \
				}
#define KRI_SET_FLG_USBMSG(hMsg)	{ \
					*(UINT *)&((hMsg)->f) = (UINT)0; \
					(hMsg)->f.ComMsgIF = 1; \
					(hMsg)->f.UsbMsgIF = 1; \
				}
#define KRI_SET_FLG_PNLOPE(hMsg)	{ \
					*(UINT *)&((hMsg)->f) = (UINT)0; \
					(hMsg)->f.OpeMsgIF = 1; \
					(hMsg)->f.PopMsgIF = 1; \
				}
/* <S150> >>>>> */
#define KRI_SET_FLG_MLNKMSG(hMsg)	{ \
					*(UINT *)&((hMsg)->f) = (UINT)0; \
					(hMsg)->f.ComMsgIF = 1; \
					(hMsg)->f.MlnkMsgIF = 1; \
				}
/* <<<<< <S150> */


/*--------------------------------------------------------------------------------------------------*/
/*
 *  Message Check Macro
 *
 *  KPI_CHK_OPEMSG() : LCD Operator
 *  KPI_CHK_SYSDLMSG() : Download Message
 *  KPI_CHK_M3MSG() : Mechatrolink Message
 *
 */
/*--------------------------------------------------------------------------------------------------*/
#define	KPI_CHK_OPEMSG( fc ) ((0x60 <= fc) && (fc <= 0x64))		/* Operator message check		*/
#define	KPI_CHK_SYSDLMSG( fc ) (0x50 == fc)						/* Download message check	*/
#define	KPI_CHK_M3MSG( fc ) (0x42 == fc)						/* M3 message check				*/

/*--------------------------------------------------------------------------------------------------*/
/*		Serial Modes (for SerComMode)																*/
/*--------------------------------------------------------------------------------------------------*/
#define SCM_NOCOM		0							/* No connected equipment									*/
#define SCM_LCDCOM		1							/* LCD operator								*/
#define SCM_MSGCOM		2							/* Message device(PC,etc)							*/
#define SCM_TSTCOM		3							/* Tester equipment(Debug)							*/

/*--------------------------------------------------------------------------------------------------*/
/*		Access level(Note: 0 is not available)															*/
/*--------------------------------------------------------------------------------------------------*/
#define ACCLVL_USER1			1				/* User 1										*/
#define ACCLVL_USER2			2				/* User 2										*/
#define ACCLVL_USER3			3				/* User 3										*/
#define ACCLVL_SYSTEM			4				/* system										*/


/*--------------------------------------------------------------------------------------------------*/
/*
 * Function Message Definition 
 * 
 * Lcd operator and panel operator use this for executing  function.
 *
 */
/*--------------------------------------------------------------------------------------------------*/
typedef struct _FUNMSG {
	MSGIF	*Super;							/* @@ Super Class (Message Interface) */
/*--------------------------------------------------------------------------------------------------*/
	UINT32 	*pKeyMk;						/* Pointer to the Key Make (OFF->ON) Flag */
	UINT32 	*pKeyBrk;						/* Pointer to the Key Break(ON->OFF) Flag */
	UINT32 	*pKeyLvl;						/* Pointer to the Key Level Flag */
	UINT32	*pBlnkKick;						/* Pointer to the Blink Flag */
	UINT8	*pTxBuf;						/* Pointer to the Display Inforamtion Buffer */
/*--------------------------------------------------------------------------------------------------*/
	void	*pVar;							/* Pointer to the Function common variable area */
} FUNMSG;


/*--------------------------------------------------------------------------------------------------*/
/*
 * Panel Operator Message Definition
 *
 * Command :
 * 		includes panel button information. (RinK.Pnl)
 * 
 * Response :
 * 		includes panel led display code information. (RoutK.PnlLed)
 *
 */
/*--------------------------------------------------------------------------------------------------*/
#define PNL_N_LED  (5)
#define PNLCMD_PNLBTN_IDX (0)
typedef struct _PnlCmdMsg {
//	UCHAR Magic[2];						/* Magic Number { 'P', 'O' } */
	UINT8 PnlBtn;						/* Panel Operator Input Button Switch Code */
} PNLCMDMSG;

#define PNLRSP_LEDCODE_IDX (0)
typedef struct _PnlRspMsg {

	UINT8 LedCode[PNL_N_LED];			/* Panel Operator Output Led Code */
	UINT8 LedBlnkSw[PNL_N_LED];			/* Panel Operator Blink Switch */
} PNLRSPMSG;



#endif  /* MSFGIF_DEF_H_ */

