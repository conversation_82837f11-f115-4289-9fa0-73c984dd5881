/****************************************************************************************************
 *
 * FILE NAME:  FunGenerator.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.10.23
 *
 * AUTHOR:      XUXIAO
 *
 * History:
 ****************************************************************************************************
	23-10-2019 Version 1.00 : Created by XUXIAO
****************************************************************************************************/
#include "Cia402Appl.h"
#include "FunGenerator.h"
#include "Mlib.h"


/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
extern const REAL32 SinCTable[];

PRIVATE REAL32 SinSample(UINT32 PeriodNum, UINT32 Tick)
{
	REAL32  Rst;
	UINT32  Tmp = Tick%PeriodNum;
	
	Tmp = Tmp * C_SinUint/PeriodNum;
	
	if(Tmp > C_SinUint3D4)
	{
		Rst = -SinCTable[C_SinUint - Tmp];

	}
	else if(Tmp > C_SinUint1D2)
	{
		Rst = -SinCTable[Tmp - C_SinUint1D2];

	}
	else if(Tmp > C_SinUint1D4)
	{
		Rst = SinCTable[C_SinUint1D2 - Tmp];

	}
	else
	{
		Rst = SinCTable[Tmp];
	}
    
	return  Rst ;
}
/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 SquareSample(UINT32 PeriodNum, UINT32 Tick)
{
	UINT32 Tmp = Tick%PeriodNum;

	if(Tmp<(PeriodNum/2))
	{
		return 1;
	}
	else
	{
		return -1;
	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PRIVATE INT32 StepSample(UINT32 PeriodNum, UINT32 Tick)
{
	UINT32 Tmp = Tick%PeriodNum;
	
	if(Tmp<(PeriodNum))
	{
		return 1;
	}
	else
	{
		return 0;
	}

}

/****************************************************************************************************
 * DESCRIPTION:
 *
 * RETURNS:
 *
****************************************************************************************************/
PUBLIC void FuncGeneratorExec(TCiA402Axis *Cia402Axis, BOOL BaseEnable )
{
	CiA402_PRM	            *Objects;
	
	Objects = Cia402Axis->Objects;
	FG_MODE_CTRL *pFgCtrl = &Cia402Axis->FG_Ctrl;
    if(BaseEnable == FALSE)
    {
       pFgCtrl->V.InitPos = Cia402Axis->Objects->PositionActualValue0x6064;
       pFgCtrl->V.LinearDir = 0;
       pFgCtrl->V.OutputRef = 0;
       pFgCtrl->V.Cnt = 0;
       pFgCtrl->V.FgAct = 0;
       return; 
    }

	if(pFgCtrl->V.FgAct)
	{
		if(FG_SinWave == pFgCtrl->P.FgType)
		{
			pFgCtrl->V.OutputRef = pFgCtrl->P.FgAmp * SinSample(pFgCtrl->P.PeriodNum, pFgCtrl->V.Cnt);
		}
		else if(FG_SquareWave == pFgCtrl->P.FgType)
		{
			pFgCtrl->V.OutputRef = pFgCtrl->P.FgAmp * SquareSample(pFgCtrl->P.PeriodNum, pFgCtrl->V.Cnt);
		}
		else if(FG_StepWave == pFgCtrl->P.FgType)
		{
			pFgCtrl->V.OutputRef = pFgCtrl->P.FgAmp * StepSample(pFgCtrl->P.PeriodNum, pFgCtrl->V.Cnt);
		}			
        else if(FG_LinearWave == pFgCtrl->P.FgType)
		{       
            INT32 AccQuot =  pFgCtrl->P.Slope/(UINT32)TASKB_FRQ;
            INT32 AccRem =  pFgCtrl->P.Slope%(UINT32)TASKB_FRQ;
            if(pFgCtrl->V.LinearDir == 0)
            {
            if(pFgCtrl->P.FgAmp > (pFgCtrl->V.OutputRef + AccQuot))
            {					
                pFgCtrl->V.Rmd += AccRem;
                if(pFgCtrl->V.Rmd >= TASKB_FRQ)
                {
                    pFgCtrl->V.Rmd -= TASKB_FRQ;
                    AccQuot++;
                }
                pFgCtrl->V.OutputRef += AccQuot;
                pFgCtrl->V.Cnt = 0;
            }
            else if(pFgCtrl->P.FgAmp < (pFgCtrl->V.OutputRef - AccQuot))
            {	
                pFgCtrl->V.Rmd += AccRem;
                if(pFgCtrl->V.Rmd >= TASKB_FRQ)
                {
                    pFgCtrl->V.Rmd -= TASKB_FRQ;
                    AccQuot++;
                }
                pFgCtrl->V.OutputRef -= AccQuot;
                pFgCtrl->V.Cnt = 0;
            }
            else
            {
                    if(pFgCtrl->V.Cnt == pFgCtrl->P.TotalNum)
                    {
                        pFgCtrl->V.LinearDir = 1;
                        pFgCtrl->V.Cnt = 0;
                    }
                pFgCtrl->V.Rmd = 0;
                pFgCtrl->V.OutputRef = pFgCtrl->P.FgAmp;
                }
            }
            else
            {
                if(0 > (pFgCtrl->V.OutputRef + AccQuot))
                {					
                    pFgCtrl->V.Rmd += AccRem;
                    if(pFgCtrl->V.Rmd >= TASKB_FRQ)
                    {
                        pFgCtrl->V.Rmd -= TASKB_FRQ;
                        AccQuot++;
                    }
                    pFgCtrl->V.OutputRef += AccQuot;
                    pFgCtrl->V.Cnt = 0;
                }
                else if(0 < (pFgCtrl->V.OutputRef - AccQuot))
                {	
                    pFgCtrl->V.Rmd += AccRem;
                    if(pFgCtrl->V.Rmd >= TASKB_FRQ)
                    {
                        pFgCtrl->V.Rmd -= TASKB_FRQ;
                        AccQuot++;
                    }
                    pFgCtrl->V.OutputRef -= AccQuot;
                    pFgCtrl->V.Cnt = 0;
                }
                else
                {
                    pFgCtrl->V.Cnt = pFgCtrl->P.TotalNum;
                    pFgCtrl->V.FgAct = 0;
                    pFgCtrl->V.Rmd = 0;
                    pFgCtrl->V.OutputRef = 0;
                }
            }
		}		        
		else 
		{
			pFgCtrl->V.OutputRef = 0;
		}
	
		if(pFgCtrl->V.Cnt++ >= pFgCtrl->P.TotalNum)
		{
			pFgCtrl->V.FgAct = 0;
            pFgCtrl->V.OutputRef = 0;
		}
	
	}
	else
	{
        pFgCtrl->V.LinearDir = 0;
		pFgCtrl->V.OutputRef = 0;
		pFgCtrl->V.Cnt = 0;
//		pFgCtrl->V.InitPos = Objects->PositionActualValue0x6064;
//		Objects->VelocityDemandValue0x606B = 0;
	}
	
}


