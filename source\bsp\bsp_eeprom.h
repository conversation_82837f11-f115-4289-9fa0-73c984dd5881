/*
 * bsp_eeprom.h
 *
 *  Created on: Oct 25, 2023
 *      Author: xgj12
 */

#ifndef BSP_EEPROM_H_
#define BSP_EEPROM_H_
#include <stdint.h>

#define CFG_IIC_EEPROM_BASE     R_IIC1_BASE


#define AT24C04                 1
#define AT24C08                 2
#define AT24C32                 3
#define AT24C128                4

#define EEPROM_TYPE             AT24C128     //EEPROM类型

#if (EEPROM_TYPE==AT24C04)
#define SLAVE_ADDRESS           0x0050  // 从机地址器件地址AT24C04---1010(A2)(A1)(P8)(读写位有硬件模块写入,不计入)
#define I2C_PAGE_SIZE           16      // EEPROM页大小AT24C04---最大一次页写入16个字节
#define I2C_PAGE_NUMBER         32      // EEPROM页个数AT24C04---32页*16字节/页=512字节
#elif (EEPROM_TYPE==AT24C08)
#define SLAVE_ADDRESS           0x0050  // 从机地址器件地址AT24C08---1010(A2)(P9)(P8)(读写位有硬件模块写入,不计入)
#define I2C_PAGE_SIZE           16      // EEPROM页大小AT24C08---最大一次页写入16个字节
#define I2C_PAGE_NUMBER         64      // EEPROM页个数AT24C08---64页*16字节/页=1024字节
#elif (EEPROM_TYPE==AT24C32)
#define SLAVE_ADDRESS           0x0050  // 从机地址器件地址AT24C32---1010(A2)(A1)(A0)(读写位有硬件模块写入,不计入)
#define I2C_PAGE_SIZE           32      // EEPROM页大小AT24C32---最大一次页写入32个字节
#define I2C_PAGE_NUMBER         128     // EEPROM页个数AT24C08---128页*32字节/页=4096字节
#elif (EEPROM_TYPE==AT24C128)
#define SLAVE_ADDRESS           0x0050  // 从机地址器件地址AT24C32---1010(A2)(A1)(A0)(读写位有硬件模块写入,不计入)
#define I2C_PAGE_SIZE           64      // EEPROM页大小AT24C32---最大一次页写入32个字节
#define I2C_PAGE_NUMBER         256     // EEPROM页个数AT24C08---128页*32字节/页=4096字节
#endif


#define ACCESS_SUCCESS      0
#define ACCESS_ERROR        1
#define ACCESS_BUSY         2
#define ACCESS_TIMEOUT      3

#define I2C_READ_CMD        0    // I2C读命令
#define I2C_WRITE_CMD       1    // I2C写命令

#define CFG_FREE_RUN_TIMER_CHANNEL          2   // 自由运行用定时器CMT通道(注：修改时需要同步修改配置头文件中的)

#define CFG_TIMER_CHANNEL_FUNC              0   // 用于Eeprom读写函数的超时的通道号
#define CFG_TIMER_CHANNEL_STEP              1   // 用于Eeprom读写函数内部操作使用的超时通道号
#define CFG_TIMER_CHANNEL_PROGRAM           2   // 用于Eeprom数据编程的超时通道号

#define CFG_EEPROM_WRITE_WAIT_TIME_US       5000UL // 数据传输到Eeprom芯片后，等待Eeprom保存完成的等待时间 [us]
   
typedef enum
{
    I2C_RW_OK = 0,
    I2C_RW_DELAY_BEFORE_START = 1,
    I2C_RW_BUSY_CHECK = 2,
    I2C_RW_MASTER_MODE_SELECT = 3,
    I2C_RW_MASTER_TRANSMITTER_MODE_SELECTED = 4,
    I2C_RW_MASTER_TRANSMITTED_ADDRESS_HIGH = 5,
    I2C_RW_MASTER_TRANSMITTED_ADDRESS_LOW = 6,
    I2C_WRITE_WAIT_DMA_END = 7,
    I2C_WRITE_DMA_END = 8,
    I2C_WRITE_WAIT_END = 9,
    I2C_WRITE_WAIT_END_STEP1 = 10,
    I2C_WRITE_WAIT_END_STEP2_DELAY_BEFORE = 11,
    I2C_WRITE_WAIT_END_STEP2 = 12,
    I2C_READ_MASTER_MODE_SELECT = 13,
    I2C_READ_MASTER_TRANSMITTER_MODE_SELECTED = 14,
    I2C_READ_WAIT_DMA_END = 15,
    I2C_RW_WRONG_REPEAT = 16,
    I2C_RW_MASTER_MODE_STOP = 17,
    I2C_RW_WAIT_REPEAT_START_ACK = 18,
    I2C_RW_WAIT_RECEIVE_TX = 19,
    I2C_RW_WAIT_RECEIVE_RX = 20,
    I2C_RW_WAIT_RECEIVE_FINISH = 21,
    I2C_RW_WAIT_RECEIVE_STOP = 22,
    I2C_RW_ERR_INIT_1,
    I2C_RW_ERR_INIT_2,
    I2C_RW_ERR_INIT_3,
    I2C_RW_ERR_INIT_4,
    I2C_RW_ERR_INIT_5,
    I2C_RW_ERR_INIT_6,
    I2C_RW_ERR_INIT_7,
    I2C_RW_ERR_INIT_8,
} ENU_I2C_RW_STATE;


typedef struct
{
    uint8_t *pDataBuf;
    uint16_t EepromByteAddr;
    uint16_t ByteNumber;
    uint8_t bIsRead;
    uint8_t bStart;
    uint8_t bFinish;
    uint8_t SlaveId;
    uint8_t retryCnt;
    uint8_t funcTimeoutFlag;
    uint32_t funcTimeoutSet;
    uint8_t stepTimeoutFlag;
    uint32_t stepTimeoutSet;
    uint8_t programTimeoutFlag;   /* 写Eeprom编程等待操作完成 */
    uint32_t programTimeoutSet;    
    uint32_t usToTimerTickQ10;
    ENU_I2C_RW_STATE I2cRwState;
    ENU_I2C_RW_STATE I2cDealState;
} STR_EEPROM;





void bsp_eeprom_init(void);
uint16_t getTimerCnt(void);
uint8_t I2C_Mem_Read(uint16_t DevAddress, uint16_t MemAddress, uint16_t MemAddSize, uint8_t * pData, uint16_t Size, uint32_t Timeout);
uint8_t I2C_Mem_Write(uint16_t DevAddress, uint16_t MemAddress, uint16_t MemAddSize, uint8_t * pData, uint16_t Size, uint32_t Timeout);


#endif /* BSP_EEPROM_H_ */
