/****************************************************************************************************
 *
 * FILE NAME:  FnMfcTuning.h
 *
 * DESCRIPTION:  Mfc with Vibration Suppression Tuning
 *
 * CREATED ON:  2021.02.19
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	19-02-2021 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _FN_MFC_TUNING_H_
#define _FN_MFC_TUNING_H_

#include "BaseDef.h"
#include "RegAccessIf.h"



/****************************************************************************************************/
/*																									*/
/*		Structure Definition																		*/
/*																									*/
/****************************************************************************************************/
typedef	struct MFCTUNE {
	struct {
		UINT16	MdlVibFreq;					/* 0x20B0 : MFC adjustment frequency with vibration suppression*/
		UINT16	MdlVibState;				/* 0x20B1 : MFC adjustment with vibration suppression completed*/
		UINT16	MdlVibFreq2;				/* 0x20B2 : Vibration suppression function Tuning frequency 2*/
		UINT16	MdlVibFil2;					/* 0x20B3 : Vibration suppression function Tuning frequency 2 correction*/
		UINT16	MdlVibState2;				/* 0x20B4 : Vibration suppression function setting 2 Completion confirmation*/
		UINT16	Dummy;						/* For Alignment										*/
	} OpeReg;
/*--------------------------------------------------------------------------------------------------*/
	struct {
		BOOL	DispMatch;					/* Does the display match the operation?				*/
		UINT32	FfWaitCntr;					/* Waiting time elapsed counter							*/
		UINT16	DetFreq;					/* Detection frequency									*/
		UINT16	OrgMdlgn;					/* Kpm before change									*/
		UINT16	OrgAntFrq;					/* Fa before change										*/
		UINT16	OrgResFrq;					/* Fr before change										*/
		UINT16	OrgMdlSw;					/* Switch before change									*/
		UINT16	Dummy;						/* For Alignment										*/
	} var;
} MFCTUNE;



/****************************************************************************************************/
/*																									*/
/*		Constant Definition																			*/
/*																									*/
/****************************************************************************************************/
enum VIBMFC_SEQ_STS {
	VIBMFC_SEQ_INIT		= 0,			
	VIBMFC_SEQ_MEASURE,						/* Vibration is being detected							*/
	VIBMFC_SEQ_ADJUST,						/* Vibration frequency fine tuning						*/
	VIBMFC_SEQ_SETREQ,						/* Frequency (model following control with vibration suppression) setting*/
	VIBMFC_SEQ_WAITMFC,						/* Waiting for frequency setting to be valid (model following control is already valid)*/
	VIBMFC_SEQ_WAITNORM,					/* Waiting for model following control setting with vibration suppression to be valid*/
	VIBMFC_SEQ_SETCOMP,						/* Setting completed									*/
	VIBMFC_SEQ_END,							/* End processing										*/
	VIBMFC_SEQ_NOOPWAIT,					/* No Operation 										*/
};
/*--------------------------------------------------------------------------------------------------*/
enum VIBMFC_CMP_STS {
	VIBMFC_STS_COMP		= 0,				/* Completion status: Setting completed / initials	    */
	VIBMFC_STS_WAIT,						/* Completion status: Waiting for setting				*/
	VIBMFC_STS_TMOUT,						/* Completion status: Setting failure (timeout)			*/
};
/*--------------------------------------------------------------------------------------------------*/
#define		VIBMFC_TUN_MAX			3		/* Gain tuning maximum digit							*/
#define		VIBMFC_TUN_MIN			1		/* Gain / frequency tuning minimum digit				*/
/*--------------------------------------------------------------------------------------------------*/
#define		VIBMFC_DET_FRQ_MIN		10		/* Lower limit of residual vibration detection frequency:11.0Hz*/
#define		VIBMFC_DET_FRQ_MAX		1000	/* Upper limit of residual vibration detection frequency:100.0Hz*/
/*--------------------------------------------------------------------------------------------------*/
#define		VIBMFC_ONE_MASS			0x01	/* Rigid system model									*/
#define		VIBMFC_TWO_MASS1		0x02	/* 2 Inertial frame model 1								*/
#define		VIBMFC_TWO_MASS2		0x03	/* 2 Inertial frame model 2								*/
#define		VIBMFC_ONE_BASE			0x04	/* Rigid system + machine stand model					*/
/*--------------------------------------------------------------------------------------------------*/
#define		VIBMFC_PRM_ACTIVE		0x0011	/* Rigid system + machine stand model setting(Pn317)	*/
/*--------------------------------------------------------------------------------------------------*/
#define		VIBMFC_FF_WAITTMOUT		8000	/* FF level change timeout: 8[sec]						*/

/****************************************************************************************************/
/*																									*/
/*		Function Prototype Definition																*/
/*																									*/
/****************************************************************************************************/
PRM_RSLT	RpiMfcTuningSetFreq( MFCTUNE *MfcTune, UINT16 Frq );
PRM_RSLT	RpiMfcTuningSetFreq2( MFCTUNE *MfcTune, UINT16 Frq );
PRM_RSLT	RpiMfcTuningSetFreq2Comp( MFCTUNE *MfcTune, UINT16 Fil );
void		RpiRegSetVibMfcTuningFrequency( MFCTUNE *MfcTune, UINT16 Frq );
void		RpiRegSetMdlVibFreq2( MFCTUNE *MfcTune, UINT16 Frq2 );
void		RpiRegSetMdlVibFil2( MFCTUNE *MfcTune, UINT16 Fil );



#endif

