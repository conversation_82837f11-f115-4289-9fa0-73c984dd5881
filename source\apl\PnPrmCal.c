/****************************************************************************************************
 *
 * FILE NAME:  PnPrmCal.c
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.09.20
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#include "PnPrmCal.h"
#include "BasePrmCal.h"
#include "ExCtrlPrmCal.h"
#include "ExControls.h"
#include "Mlib.h"
#include "HardApi.h"
#include "PnPrmListTbl.h"
#include "MicroPrmCal.h"
#include "fundefine.h"
#include "canfd_slave.h"
#include "GainChange.h"

extern PUBLIC BOOL gSystemInitDone;
/****************************************************************************************************/
/*		Model following control parameter value copy												*/
/****************************************************************************************************/
PUBLIC void PcalCopyMFCCfgParam(MFC_CFG_PRM *MfcCfgPrm, const PRMDATA *Prm)
{
	MfcCfgPrm->mdlsw	= Prm->PnAdvPrm.MfcSw;		
	MfcCfgPrm->mdlgn	= Prm->PnAdvPrm.MfcGn;		
	MfcCfgPrm->mdlzeta	= Prm->PnAdvPrm.MfcGnComp;		
	MfcCfgPrm->mdlptff	= Prm->PnAdvPrm.MfcBiasFw;		
	MfcCfgPrm->mdlntff	= Prm->PnAdvPrm.MfcBiasRv;		
	MfcCfgPrm->mdlantfrq= Prm->PnAdvPrm.VibSupFrq1A;	
	MfcCfgPrm->mdlresfrq= Prm->PnAdvPrm.VibSupFrq1B;
	MfcCfgPrm->mdlvff	= Prm->PnAdvPrm.MfcVffComp;		
	MfcCfgPrm->mdlgn2	= Prm->PnAdvPrm.MfcGn2;		
	MfcCfgPrm->mdlzeta2	= Prm->PnAdvPrm.MfcGnComp2;	
}

/****************************************************************************************************/
/*		Type A damping control parameter value copy								                    */
/****************************************************************************************************/
PRIVATE void PcalCopyResVibCfgParam(RESVIB_CFG_PRM *ResVibCfgPrm, const PRMDATA *Prm)
{
	ResVibCfgPrm->jrate 	  = Prm->PnCtrlCfgPrm.Jrat;		
	ResVibCfgPrm->avibfrq 	  = Prm->PnAdvPrm.AvibFrq;		
	ResVibCfgPrm->avibgn	  = Prm->PnAdvPrm.AvibGn;		
	ResVibCfgPrm->avibdamp    = Prm->PnAdvPrm.AvibDamp;	
	ResVibCfgPrm->avibfil1	  = Prm->PnAdvPrm.AvibFil1;	
	ResVibCfgPrm->avibfil2	  = Prm->PnAdvPrm.AvibFil2;
	ResVibCfgPrm->avibdamp2   = Prm->PnAdvPrm.AvibDamp2;	
	ResVibCfgPrm->avibfrq2	  = Prm->PnAdvPrm.AvibFrq2;	
}

/****************************************************************************************************/
/*		Pn002 :Motor Rated Frequency								                                */
/****************************************************************************************************/
PRM_RSLT pncal_RatFreq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
	REAL32  		fw=0;
    PRMDATA			*Prm;
    Prm = Axis->Prm;

	if(gSystemInitDone == 0 ||Prm->PnAuxPrm.MotIdent>0) 
		return PRM_RSLT_SUCCESS; 
	
	// fw = (REAL32)Prm->PnMotPrm.RatFreq * 6.0F / (REAL32)Prm->PnMotPrm.RatSpd;

	// Prm->PnMotPrm.MotPolePairs = ROUND_TO_UINT16(fw);
	
    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn003 :Motor Rated Power								                                    */
/****************************************************************************************************/
PRM_RSLT pncal_RatPow(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
    PRMDATA			*Prm;
    Prm = Axis->Prm;

	if(gSystemInitDone == 0 ||Prm->PnAuxPrm.MotIdent>0) 
		return PRM_RSLT_SUCCESS; 
	

	if(Prm->PnMotPrm.RatPow < 15)  // 0-150w
	{
		Prm->PnMotPrm.Jmot = 20;
	}
	else if(Prm->PnMotPrm.RatPow < 35)  // 150-350w
	{
		Prm->PnMotPrm.Jmot = 40;
	}
	else if(Prm->PnMotPrm.RatPow < 60)  // 350-600w
	{
		Prm->PnMotPrm.Jmot = 80;
	}
	else if(Prm->PnMotPrm.RatPow < 85)	// 600w-850w
	{
		Prm->PnMotPrm.Jmot = 140;
	}
	else if(Prm->PnMotPrm.RatPow < 110)  // 850w-1100w
	{
		Prm->PnMotPrm.Jmot = 300;
	}
	else if(Prm->PnMotPrm.RatPow < 130)  // 1100w-1300w
	{
		Prm->PnMotPrm.Jmot = 500;
	}
	else                              // >=1300w
	{
		Prm->PnMotPrm.Jmot = 800;
	}


    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn004 :Motor Rated Voltage								                                    */
/****************************************************************************************************/
PRM_RSLT pncal_RatVolt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
	REAL32  		fw=0;	
    PRMDATA			*Prm;
    Prm = Axis->Prm;

	if(gSystemInitDone == 0 ||Prm->PnAuxPrm.MotIdent>0) 
		return PRM_RSLT_SUCCESS; 
	

	if(Prm->PnMotPrm.RatV > 330)   // 380V AC
	{
		fw = 300.0f/(REAL32)Prm->PnMotPrm.RatSpd;
	}
	else if(Prm->PnMotPrm.RatV > 200)  // 220V AC
	{
		fw = 150.0f/(REAL32)Prm->PnMotPrm.RatSpd;
	}
	else if(Prm->PnMotPrm.RatV > 100)  // 110V AC
	{
		fw = 75.0f/(REAL32)Prm->PnMotPrm.RatSpd;
	}
	else if(Prm->PnMotPrm.RatV > 48)  // 48V DC
	{
		fw = 48.0f/(REAL32)Prm->PnMotPrm.RatSpd;
	}
    else  // 24V DC
    {
        fw = 24.0f/(REAL32)Prm->PnMotPrm.RatSpd;
    }
	
	fw = fw*100000.0f;
	Prm->PnMotPrm.MotEmf = fw;

    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn005 :Motor Rated Current								                                */
/****************************************************************************************************/
PRM_RSLT pncal_RatCur(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
    PRMDATA			*Prm;
        REAL32          MotEmf;
    REAL32          MotKt;
    Prm = Axis->Prm;

	if(gSystemInitDone == 0 ||Prm->PnAuxPrm.MotIdent>0) 
		return PRM_RSLT_SUCCESS; 
    
	Prm->PnMotPrm.MaxCur = Prm->PnMotPrm.RatCur * 9/3;    // 9/3 = 3
            Prm->PnMotPrm.MotKt   = (UINT16)(10*(REAL32)Prm->PnMotPrm.RatTrq/(REAL32)Prm->PnMotPrm.RatCur);  // Cal Kt [0.01Nm/A]
    
    MotKt  = (REAL32)Prm->PnMotPrm.MotKt;
    MotEmf = MotKt/(100*C_FSqrt3*9.55);                                            // [V/rpm]
    if(Prm->PnMotPrm.MotType == 1)
    {
       Prm->PnMotPrm.MotEmf  = (UINT16)(MotEmf*100);                            // Cal Ke [0.01V/rpm]    
    }
   else
   {
    Prm->PnMotPrm.MotEmf  = (UINT16)(MotEmf*100*1000);                            // Cal Ke [0.01mV/rpm]
   }    

    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn006 :Motor Rated Torque								                                */
/****************************************************************************************************/
PRM_RSLT pncal_RatTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
    PRMDATA			*Prm;
    REAL32          MotEmf;
    REAL32          MotKt;
    Prm = Axis->Prm;

	if(gSystemInitDone == 0 ||Prm->PnAuxPrm.MotIdent>0) 
		return PRM_RSLT_SUCCESS; 
    
	Prm->PnMotPrm.MaxTrq= Prm->PnMotPrm.RatTrq * 9/3;    // 9/5 = 3
    
    Prm->PnMotPrm.MotKt   = (UINT16)(10*(REAL32)Prm->PnMotPrm.RatTrq/(REAL32)Prm->PnMotPrm.RatCur);  // Cal Kt [0.01Nm/A]
    
    MotKt  = (REAL32)Prm->PnMotPrm.MotKt;
    MotEmf = MotKt/(100*C_FSqrt3*9.55);                                            // [V/rpm]
    if(Prm->PnMotPrm.MotType == 1)
    {
       Prm->PnMotPrm.MotEmf  = (UINT16)(MotEmf*100);                            // Cal Ke [0.01V/rpm]    
    }
   else
   {
    Prm->PnMotPrm.MotEmf  = (UINT16)(MotEmf*100*1000);                            // Cal Ke [0.01mV/rpm]
   }  
    
    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn007 :Motor Rated Speed								                                */
/****************************************************************************************************/
PRM_RSLT pncal_RatSpd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
	REAL32  		fw=0;
    PRMDATA			*Prm;
    Prm = Axis->Prm;

	if(gSystemInitDone == 0 ||Prm->PnAuxPrm.MotIdent>0) 
		return PRM_RSLT_SUCCESS; 
	
	// fw = (REAL32)Prm->PnMotPrm.RatFreq * 6.0F / (REAL32)Prm->PnMotPrm.RatSpd;

	// Prm->PnMotPrm.MotPolePairs = ROUND_TO_UINT16(fw);

	// Prm->PnMotPrm.MaxSpd = ((UINT32)Prm->PnMotPrm.RatSpd) * 2;    // 5/4 = 1.25

//	if(Prm->PnMotPrm.RatV > 330)   // 380V AC
//	{
//		fw = 300.0f/(REAL32)Prm->PnMotPrm.RatSpd;
//	}
//	else if(Prm->PnMotPrm.RatV > 200)  // 220V AC
//	{
//		fw = 150.0f/(REAL32)Prm->PnMotPrm.RatSpd;
//	}
//	else if(Prm->PnMotPrm.RatV > 100)  // 110V AC
//	{
//		fw = 75.0f/(REAL32)Prm->PnMotPrm.RatSpd;
//	}
//	else
//	{
//		fw = 0.0f;
//	}
//	
//	fw = fw*100000.0f;
//	Prm->PnMotPrm.MotEmf = fw;

    return PRM_RSLT_SUCCESS;
}
/****************************************************************************************************/
/*		Pn015 :AbsEncOffset								                                */
/****************************************************************************************************/
PRM_RSLT pncal_AbsEncOffset(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
    PRMDATA			*Prm;
    Prm = Axis->Prm;
	
    if(Axis->BaseCtrls->BaseEnable)
    {
         return PRM_RSLT_RWACC_ERR;
    }

	Axis->BaseLoops->Enc->P.MechAngleOffset = Prm->PnMotPrm.AbsEncOffset;

    return PRM_RSLT_SUCCESS;
}



/****************************************************************************************************/
/*		Pn107 : Reverse rotate direction								                            */
/****************************************************************************************************/
PRM_RSLT pncal_ResDir(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
    PRMDATA			*Prm;
    BASE_LOOP       *BaseLoops;
    Prm = Axis->Prm;
    BaseLoops = Axis->BaseLoops;
	if(Axis->BaseCtrls->BaseEnable)
    {
        return PRM_RSLT_RWACC_ERR;
    }
	if(Prm->PnCfgPrm.ResDir)
    {
       BaseLoops->Bprm->RvsDir = TRUE;
       BaseLoops->Bprm->DirSign = -1;
    }
    else
    {
       BaseLoops->Bprm->RvsDir = FALSE;
       BaseLoops->Bprm->DirSign = 1;
    }
	Axis->BaseLoops->Enc->V.EncConnectLast = 0;

    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn10A : Stop Mode								                           				    */
/****************************************************************************************************/
PRM_RSLT pncal_StpMode(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
    PRMDATA			*Prm;
   ALM_STOP			*AlmStop;
   FORCE_STOP		*ForceStop;
   OT_SEQ_DATA			*OtSeqData;
   
    Prm = Axis->Prm;
	AlmStop = &(Axis->BaseSeq->AlmStop);
	ForceStop = &(Axis->BaseSeq->ForceStop);
            OtSeqData = &(Axis->BaseSeq->OtSeqData);

	// Servo OFF and Gr1 Stop Mode
	switch( Prm->PnCfgPrm.AlmStpMode & 0x0F )
	{
		case 0x00: 
			AlmStop->P.StopMode = STOPMODE_DBSTOPDB; 
			break;
		case 0x01: 
			AlmStop->P.StopMode = STOPMODE_DBSTOPFR; 
			break;
		case 0x02: 
			AlmStop->P.StopMode = STOPMODE_FREERUN;
			break;
		default: break;
	}

	if(USE_DB == FALSE)
	{
		AlmStop->P.StopMode = STOPMODE_FREERUN;
	}

	// Gr2 Stop Mode
	switch( (Prm->PnCfgPrm.AlmStpMode>>4) & 0x0F )
	{
		case 0x00:  // The same to ( Prm->PnCfgPrm.AlmStpMode  & 0x0F )Byte0
			AlmStop->P.G2AlmStopMode = GR2STOPMODE_DEFAULT ;
			break;

		case 0x01:	// The motor torque is stopped with the maximum torque
			switch( Prm->PnCfgPrm.AlmStpMode  & 0x0F )
			{
				case 0x00:	
					AlmStop->P.G2AlmStopMode = GR2STOPMODE_ZSTOPDB ;
					if(USE_DB == FALSE)
					{
						AlmStop->P.G2AlmStopMode = GR2STOPMODE_ZSTOPFR;
					}
					break;
					
				case 0x01:
				case 0x02:	
					AlmStop->P.G2AlmStopMode = GR2STOPMODE_ZSTOPFR ;
					break;
				
				default:	
					AlmStop->P.G2AlmStopMode = GR2STOPMODE_ZSTOPFR ;
					break;
			}
			break;


		case 0x02:	
					AlmStop->P.G2AlmStopMode = GR2STOPMODE_ZSTOPFR ;
					break;

		case 0x03:	
			switch( Prm->PnCfgPrm.AlmStpMode  & 0x0F )
			{
				case 0x00:	
					AlmStop->P.G2AlmStopMode = GR2STOPMODE_LDSTOPDB ;
					if(USE_DB == FALSE)
					{
						AlmStop->P.G2AlmStopMode = GR2STOPMODE_LDSTOPFR;
					}
					break;
					
				case 0x01:
				case 0x02:	
					AlmStop->P.G2AlmStopMode = GR2STOPMODE_LDSTOPFR ;
					break;
				default:	
					AlmStop->P.G2AlmStopMode = GR2STOPMODE_LDSTOPFR ;
					break;
			}
			break;

		case 0x04:	
			AlmStop->P.G2AlmStopMode = GR2STOPMODE_LDSTOPFR ;
			break;
		
		default:   
			AlmStop->P.G2AlmStopMode = GR2STOPMODE_DEFAULT ; 
			break;
		
	}

	// force stop mode
	switch( (Prm->PnCfgPrm.AlmStpMode>>8) & 0x0F )
	{
		case 0x00:  // The same to ( Prm->PnCfgPrm.AlmStpMode  & 0x0F )Byte0
					ForceStop->P.ForceStopMode = FSTOPMODE_DEFAULT ;
					break;

		case 0x01:	
			switch( Prm->PnCfgPrm.AlmStpMode  & 0x0F )
			{
				case 0x00:	
					ForceStop->P.ForceStopMode = FSTOPMODE_ZSTOPDB ;
					if(USE_DB == FALSE)
					{
						ForceStop->P.ForceStopMode = FSTOPMODE_ZSTOPFR ;
					}
					break;
					
				case 0x01:
				case 0x02:	
					ForceStop->P.ForceStopMode = FSTOPMODE_ZSTOPFR ;
					break;
				default:	
					ForceStop->P.ForceStopMode = FSTOPMODE_ZSTOPFR ;
					break;
			}
			break;

		case 0x02:	
			ForceStop->P.ForceStopMode = FSTOPMODE_ZSTOPFR ;
			break;

		case 0x03:	
			switch( Prm->PnCfgPrm.AlmStpMode  & 0x0F )
			{
				case 0x00:	
					ForceStop->P.ForceStopMode = FSTOPMODE_LDSTOPDB ;
					if(USE_DB == FALSE)
					{
						ForceStop->P.ForceStopMode = FSTOPMODE_LDSTOPFR ;
					}
					break;
				case 0x01:
				case 0x02:	
					ForceStop->P.ForceStopMode = FSTOPMODE_LDSTOPFR ;
					break;
				default:	
					ForceStop->P.ForceStopMode = FSTOPMODE_LDSTOPFR ;
					break;
			}
			break;

		case 0x04:	
			ForceStop->P.ForceStopMode = FSTOPMODE_LDSTOPFR ;
			break;

		default:	
			ForceStop->P.ForceStopMode = FSTOPMODE_DEFAULT ;
			break;
	}

            switch( (Prm->PnCfgPrm.AlmStpMode>>12) & 0x0F )
	{
		case 0x00: OtSeqData->conf.OtStopMode = OTSTOPMODE_DEFAULT; break;
		case 0x01: OtSeqData->conf.OtStopMode = OTSTOPMODE_ZSTOPZC; break;
		case 0x02: OtSeqData->conf.OtStopMode = OTSTOPMODE_ZSTOPFR; break;
		case 0x03: OtSeqData->conf.OtStopMode = OTSTOPMODE_LDSTOPZC; break;    /* <S1F5> */
		case 0x04: OtSeqData->conf.OtStopMode = OTSTOPMODE_LDSTOPFR; break;    /* <S1F5> */
		default: OtSeqData->conf.OtStopMode = OTSTOPMODE_DEFAULT; break;
		
    }

	
    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn10B : Fault NO.1 Stop Mode								                      	        */
/****************************************************************************************************/
PRM_RSLT pncal_Fault1StpMode(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{    
    PRMDATA			*Prm;
    BASE_LOOP       *BaseLoops;
    Prm = Axis->Prm;
    BaseLoops = Axis->BaseLoops;



    return PRM_RSLT_SUCCESS;
}



/****************************************************************************************************/
/*		Pn10C : Brake Release Delay Time[ms]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_brkoffdelay(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;
	BK_SEQ_DATA		*BkSeqData;

	Prm = Axis->Prm;
	BkSeqData = &(Axis->BaseSeq->BkSeqData);

	if( Prm->PnCfgPrm.BrkRlsTim <= 1000 )
	{
		BkSeqData->P.BkoffDelayTime = Prm->PnCfgPrm.BrkRlsTim;
	}
	else
	{
		BkSeqData->P.BkoffDelayTime = 1000;
	}

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn10D : Brake Active Wait Time[ms]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_brkwait(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;
	BK_SEQ_DATA		*BkSeqData;

	Prm = Axis->Prm;
	BkSeqData = &(Axis->BaseSeq->BkSeqData);

	if( Prm->PnCfgPrm.BrkTim <= 1000 )
	{
		BkSeqData->P.BkonWaitTime = Prm->PnCfgPrm.BrkTim;
	}
	else
	{
		BkSeqData->P.BkonWaitTime = 1000;
	}

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn10E : Brake Active Speed[rpm]									                            */
/****************************************************************************************************/
PRM_RSLT pncal_brkspd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;
	BK_SEQ_DATA		*BkSeqData;

	Prm = Axis->Prm;
	BkSeqData = &(Axis->BaseSeq->BkSeqData);

	BkSeqData->P.BkonSpdLevel = (REAL32)Prm->PnCfgPrm.BrkSpd* Axis->BaseLoops->Bprm->Kspdrpm;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn10F : Brake Active Delay Time[ms]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_brkdelay(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;
	BK_SEQ_DATA		*BkSeqData;

	Prm = Axis->Prm;
	BkSeqData = &(Axis->BaseSeq->BkSeqData);

	if( Prm->PnCfgPrm.BrkDelay <= 1000 )
	{
		BkSeqData->P.BkonDelayTime = Prm->PnCfgPrm.BrkDelay;
	}
	else
	{
		BkSeqData->P.BkonDelayTime = 1000;
	}

	return PRM_RSLT_SUCCESS;

}
/****************************************************************************************************/
/*		Pn111 : LedWarnUse								                        */
/****************************************************************************************************/
PRM_RSLT pncal_LedWarn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;
	ALARM			*AlmMngr;

	Prm = Axis->Prm;
	AlmMngr =  Axis->AlmMngr;


	AlmMngr->LedWarn = Prm->PnCfgPrm.LedWrnUse;

	return PRM_RSLT_SUCCESS;

}
/****************************************************************************************************/
/*		Pn115 : Ext Regen Resistor Power[0.01kw]											        */
/****************************************************************************************************/
PRM_RSLT pncal_ExtRegenW(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	PRMDATA	*Prm = Axis->Prm;

	UINT16 ConvW = CONVERTER_POWER;  // todo
	UINT16 ReW = 0;  // todo
	if(FALSE != PcalRegeneOverLoadLevel( &(Axis->PowMngr->RegeneCtrl),
	                                     Prm->PnCfgPrm.ExtRegenPow, ReW, ConvW ))
	{
		ALMSetPramError( Axis->AlmMngr, pndef_ExtRegenPow.Number );
	}

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn116 : Ext Regen Resistor [Ohm]											        */
/****************************************************************************************************/
PRM_RSLT pncal_ExtRegenR(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    REGCFG_PRM		RegPrm; 
    PRMDATA	        *Prm = Axis->Prm;
    BPRMDAT         *Bprm = Axis->BaseLoops->Bprm;

	RegPrm.RegenPow = Prm ->PnCfgPrm.ExtRegenPow;
	RegPrm.ExtRegenR = Prm ->PnCfgPrm.ExtRegenR*100;     // ohm->10mohm
	RegPrm.RegenR = Prm ->PnCfgPrm.MinRegenR*1000;   // todo  ohm->mohm
	RegPrm.ReW = 0;    //todo
	RegPrm.RegenLvl = REGEN_ON_LEVEL;     // todo
	RegPrm.ConvW = CONVERTER_POWER;    // todo   IPM max power

	IprmcalRegenePowerGain(&(Axis->PowMngr->RegeneCtrl),Bprm,&RegPrm);	
    
	return PRM_RSLT_SUCCESS;
}
/****************************************************************************************************/
/*		Pn118 : Power Input Select											          				*/
/****************************************************************************************************/
PRM_RSLT pncal_PowInputSel(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	if(Axis->AxisID == AXIS_1)
	{
		switch(Axis->Prm->PnCfgPrm.PowInputSel)
		{
			case 0:
				Axis->PowMngr->MainPowChecker.P.Ac3Phase = FALSE;
				Axis->PowMngr->MainPowChecker.P.DcbusWay = FALSE;
				break;
				
			case 1 :
				Axis->PowMngr->MainPowChecker.P.Ac3Phase = TRUE;
				Axis->PowMngr->MainPowChecker.P.DcbusWay = FALSE;
				break;
				
			case 2 :
				Axis->PowMngr->MainPowChecker.P.Ac3Phase = FALSE;
				Axis->PowMngr->MainPowChecker.P.DcbusWay = TRUE;
				break;
				
			default :
				Axis->PowMngr->MainPowChecker.P.Ac3Phase = TRUE;
				Axis->PowMngr->MainPowChecker.P.DcbusWay = FALSE;
				break;
		}
	}
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn11E : Motor line UVW sequence									    */
/****************************************************************************************************/
PRM_RSLT pncal_UWVSeq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    if(Axis->Prm->PnCfgPrm.UWVSeq)
    {
        Axis->BaseLoops->Bprm->UWVSeq = TRUE;
    }
    else
    {
        Axis->BaseLoops->Bprm->UWVSeq = FALSE;
    }
    return PRM_RSLT_SUCCESS;

}

	
/****************************************************************************************************/
/*		 Pn11F : Ac Off Discharge Switch															*/
/****************************************************************************************************/
PRM_RSLT pncal_AcoffDischargeSw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    if(Axis->AxisID != AXIS_1) return PRM_RSLT_SUCCESS;
        
	if(Axis->Prm->PnCfgPrm.AcoffDischarge)
	{
		Axis->PowMngr->MainPowChecker.P.AcoffDischarge = TRUE;
	}
	else
	{
		Axis->PowMngr->MainPowChecker.P.AcoffDischarge = FALSE;
	}
	return PRM_RSLT_SUCCESS;

}


/****************************************************************************************************/
/*		Pn202 : Position loop gain [0.1/s]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_EncZeroOffset(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

        if(Axis->BaseLoops->UseEncoder2)
        {
           Axis->BaseLoops->Enc->P.EncZeroOffset = 0;
           Axis->BaseLoops->Enc2->P.EncZeroOffset = Axis->Prm->PnCfgPrm.EncZeroOffset;
		   Axis->BaseLoops->Enc2->V.EncZeroOffFlag = 1;
        }
        else
        {
          Axis->BaseLoops->Enc->P.EncZeroOffset = Axis->Prm->PnCfgPrm.EncZeroOffset;
          Axis->BaseLoops->Enc2->P.EncZeroOffset = 0;
		  Axis->BaseLoops->Enc2->V.EncZeroOffFlag = 0;
        }

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn202 : Position loop gain [0.1/s]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_posgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 	*Prm;
	BPRMDAT 	*Bprm;

	Bprm = Axis->BaseLoops->Bprm;
	Prm = Axis->Prm;

	PcalBasePosPCtrll( Axis->BaseLoops, (Prm->PnCtrlCfgPrm.PosLoopHz),1);

	ATGSEL	*pAtGsel;								
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);		/* condition A */
	PcalAtGseldKpSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKp */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);		/* condition B */
	PcalAtGseldKpSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKp */

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn203 : Speed loop gain [0.1Hz]									                            */
/****************************************************************************************************/
PRM_RSLT pncal_spdloophz(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 		*Prm;
	BPRMDAT 		*Bprm;
	DOBS_PRM		*DobsPrm;
	DOBS_CFGPRM 	DobsCfgPrm;
	ATGSEL	        *pAtGsel;	

	Bprm = Axis->BaseLoops->Bprm;
	Prm = Axis->Prm;

//    PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 0 );
    
	PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 1 );
	PcalSpeedLimitGain( Axis->BaseCtrls, Bprm, Prm,  1 );

									
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);		/* condition A */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);		/* condition B */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */

	#if NO_ADVFUNC
	DobsPrm = &(Axis->BaseCtrls->GainChange.GselDobs[1]);

	DobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	DobsCfgPrm.loophz = (Prm->PnCtrlCfgPrm.SpdLoopHz);		
	DobsCfgPrm.loophz2 = Prm->PnCtrlCfgPrm.SpdLoopHz2;
	DobsCfgPrm.dobgn = Prm->PnAdvPrm.DobsGn;			
	DobsCfgPrm.dobgn2 = Prm->PnAdvPrm.DobsGn2;
	DobsCfgPrm.dtrqgn = Prm->PnAdvPrm.DobsCoff;	
	DobsCfgPrm.dlpfil = Prm->PnAdvPrm.DobsFreqComp;	
	DobsCfgPrm.dobjgn = Prm->PnAdvPrm.DobsGnComp;		

	DobsCalculatePrm( &(DobsPrm[0]), Bprm, &DobsCfgPrm, 0 );
	#endif

	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);			/* condition A */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate dKs */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);			/* condition B */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate dKs */

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn204 : Speed Loop Time Constant [0.01ms]									                */
/****************************************************************************************************/
PRM_RSLT pncal_spdlooptime(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 		*Prm;
	BPRMDAT 		*Bprm;

	Bprm = Axis->BaseLoops->Bprm;
	Prm = Axis->Prm;

    PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 0 ); 
    
	PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 1 );

	ATGSEL	*pAtGsel;									
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);		/* condition A */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);		/* condition B */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn205 : Load Inertia Ratio [1%]									                            */
/****************************************************************************************************/
PRM_RSLT pncal_jrate(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 	*Prm;
	BPRMDAT 	*Bprm;
	#if NO_ADVFUNC
	DOBS_PRM		*DobsPrm;
	DOBS_CFGPRM 	DobsCfgPrm;
	EHVOBS_CTRL 	*EhVobsCtrl;
	EHVOBS_CFGPRM EhVobsCfgPrm;
	RESVIB_CFG_PRM   ResVibCfgPrm;
	MFC_CFG_PRM     MfcCfgPrm;
	#endif
	BOOL			PrmChgSts;
	ATGSEL			*pAtGsel;		

	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

    PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 0 );  
    
	PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 1 );
	PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 2 );

									
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);		/* condition A */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);		/* condition B */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */
	#if NO_ADVFUNC
	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 0 );
	
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
						 &MfcCfgPrm,
						 Axis->Prm->PnCtrlCfgPrm.Jrat,
						 Axis->BaseLoops->Bprm,
						 0, 1 );

	DobsPrm = &(Axis->BaseCtrls->GainChange.GselDobs[1]);
	
	DobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	DobsCfgPrm.loophz = (Prm->PnCtrlCfgPrm.SpdLoopHz);		
	DobsCfgPrm.loophz2 = Prm->PnCtrlCfgPrm.SpdLoopHz2;
	DobsCfgPrm.dobgn = Prm->PnAdvPrm.DobsGn;			
	DobsCfgPrm.dobgn2 = Prm->PnAdvPrm.DobsGn2;
	DobsCfgPrm.dtrqgn = Prm->PnAdvPrm.DobsCoff; 
	DobsCfgPrm.dlpfil = Prm->PnAdvPrm.DobsFreqComp; 
	DobsCfgPrm.dobjgn = Prm->PnAdvPrm.DobsGnComp;	

	DobsCalculatePrm( &(DobsPrm[0]), Bprm, &DobsCfgPrm, 0 );
	DobsCalculatePrm( &(DobsPrm[1]), Bprm, &DobsCfgPrm, 1 );
	#endif
								
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);			/* condition A */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate dKs */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);			/* condition B */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate dKs */

	#if NO_ADVFUNC
	EhVobsCtrl = &(Axis->BaseCtrls->EhVobsCtrl);
	EhVobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;	
	EhVobsCfgPrm.evobgn = Prm->PnAdvPrm.VobsGn;	
	EhVobsCfgPrm.evobjgn = Prm->PnAdvPrm.VobsPosCompGn;
	EhVobsCfgPrm.trqfil11 = Prm->PnCtrlCfgPrm.TrqFilTime;	
	PrmChgSts = EhVobsCalculatePrm( EhVobsCtrl, Bprm, &EhVobsCfgPrm ); 
	if( FALSE == PrmChgSts )
	{
		ALMSetServoAlarm( Axis->AlmMngr, ALM_PRM );
	}
	#endif

	PcalSpeedLimitGain( Axis->BaseCtrls, Bprm, Prm, 1 );
	PcalSpeedLimitGain( Axis->BaseCtrls, Bprm, Prm, 2 );
	#if NO_ADVFUNC
	PcalCopyResVibCfgParam(&ResVibCfgPrm, Prm);
	ResVibCalculatePrm( &(Axis->BaseCtrls->ResVib), &ResVibCfgPrm, Bprm->Kvx, PS_CYCLENS);

	DetVibObsCalculateJGain( &(Axis->DetVib->DetVibObs), Axis->BaseLoops->Bprm->Kvx,
		                                                          Prm->PnCtrlCfgPrm.Jrat, PS_CYCLENS);
	#endif

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn206 : Speed loop gain 2 [0.1Hz]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_spdloophz2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 		*Prm;
	BPRMDAT 		*Bprm;
	DOBS_PRM		*DobsPrm;
	DOBS_CFGPRM 	DobsCfgPrm;
	ATGSEL	        *pAtGsel;	

	Bprm = Axis->BaseLoops->Bprm;
	Prm = Axis->Prm;
    
	PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 2 );
	PcalSpeedLimitGain( Axis->BaseCtrls, Bprm, Prm,  2 );

									
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);		/* condition A */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);		/* condition B */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */

	#if NO_ADVFUNC
	DobsPrm = &(Axis->BaseCtrls->GainChange.GselDobs[1]);

	DobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	DobsCfgPrm.loophz = (Prm->PnCtrlCfgPrm.SpdLoopHz);		
	DobsCfgPrm.loophz2 = Prm->PnCtrlCfgPrm.SpdLoopHz2;
	DobsCfgPrm.dobgn = Prm->PnAdvPrm.DobsGn;			
	DobsCfgPrm.dobgn2 = Prm->PnAdvPrm.DobsGn2;
	DobsCfgPrm.dtrqgn = Prm->PnAdvPrm.DobsCoff;	
	DobsCfgPrm.dlpfil = Prm->PnAdvPrm.DobsFreqComp;	
	DobsCfgPrm.dobjgn = Prm->PnAdvPrm.DobsGnComp;		

	DobsCalculatePrm( &(DobsPrm[1]), Bprm, &DobsCfgPrm, 1 );
	#endif

	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);			/* condition A */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate dKs */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);			/* condition B */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs) );	/* calculate dKs */

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn207 : Speed Loop Time Constant 2[0.01ms]									                */
/****************************************************************************************************/
PRM_RSLT pncal_spdlooptime2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 		*Prm;
	BPRMDAT 		*Bprm;

	Bprm = Axis->BaseLoops->Bprm;
	Prm = Axis->Prm;

//    PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 0 ); 
    
	PcalBaseSpdCtrl( Axis->BaseLoops, Prm, 2 );

	ATGSEL	*pAtGsel;									
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);		/* condition A */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);		/* condition B */
	PcalAtGseldKviSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKv, dKvi */

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn208 : Position loop gain 2 [0.1/s]									                    */
/****************************************************************************************************/
PRM_RSLT pncal_posgn2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 	*Prm;
	BPRMDAT 	*Bprm;

	Bprm = Axis->BaseLoops->Bprm;
	Prm = Axis->Prm;

	PcalBasePosPCtrll( Axis->BaseLoops, Prm->PnCtrlCfgPrm.PosLoopHz2,2);

	ATGSEL	*pAtGsel;								
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);		/* condition A */
	PcalAtGseldKpSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKp */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);		/* condition B */
	PcalAtGseldKpSet( pAtGsel, &(pAtGsel->conf.dGain) );	/* calculate dKp */

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn209 : Current Loop Gain [Hz]									                            */
/****************************************************************************************************/
PRM_RSLT pncal_curloophz(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
    Prm = Axis->Prm;

	if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_CURLOOPTI_SET)
	{
		Axis->BaseLoops->CurTiType = 1;
	}
	else
	{
		Axis->BaseLoops->CurTiType = 0;
	}

    PcalBaseCurCtrl( Axis->BaseLoops , &Prm->PnCtrlCfgPrm.CurLoopHz, &Prm->PnCtrlCfgPrm.CurLoopTi);

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn20A : Speed Mode Switch [1]									                            */
/****************************************************************************************************/
PRM_RSLT pncal_spdmodsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
	MODE_SW		*ModeSwData;
	
    Prm = Axis->Prm;
	ModeSwData =  &(Axis->BaseCtrls->ModeSw);

	if(Prm->PnCtrlCfgPrm.ModeSw <= 4)
	{
		ModeSwData->conf.ModeSWSel = Prm->PnCtrlCfgPrm.ModeSw;
	}
	else
	{
		ModeSwData->conf.ModeSWSel = 4;
	}

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn20B : Mode Switch Torque Value [%]									                    */
/****************************************************************************************************/
PRM_RSLT pncal_spdmstrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT 	*Bprm;
    PRMDATA 	*Prm;
	MODE_SW		*ModeSwData;
	
    Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;
	ModeSwData =  &(Axis->BaseCtrls->ModeSw);

	Prm->PnCtrlCfgPrm.ModeSwTrq = (REAL32)Prm->PnMotPrm.MaxTrq/(REAL32)Prm->PnMotPrm.RatTrq*100.0f;
	
	REAL32 Tmp = (REAL32)Prm->PnCtrlCfgPrm.ModeSwTrq*10.0f;     // 1% -> 0.1%
	
	ModeSwData->conf.MSWTrqLevel = Tmp*Bprm->Kmottrq;           // 0.1% -> 2^24

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn20C : Mode Switch Speed Value [rpm]									                    */
/****************************************************************************************************/
PRM_RSLT pncal_spdmsspd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT 	*Bprm;
    PRMDATA 	*Prm;
	MODE_SW		*ModeSwData;
	
    Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;
	ModeSwData =  &(Axis->BaseCtrls->ModeSw);

    /* rpm -> 2^24 */
	ModeSwData->conf.MSWSpdLevel = (REAL32)Prm->PnCtrlCfgPrm.ModeSwSpd * Bprm->Kspdrpm;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn20D : Mode Switch Acc Value [rpm/s]									                    */
/****************************************************************************************************/
PRM_RSLT pncal_spdmsacc(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT 	*Bprm;
    PRMDATA 	*Prm;
	MODE_SW		*ModeSwData;

	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;
	ModeSwData =  &(Axis->BaseCtrls->ModeSw);

	REAL32 fw = (REAL32)Prm->PnCtrlCfgPrm.ModeSwAcc * Bprm->Kspdrpm;

			
	fw = fw * (REAL32)TASKC_CYCLEUS/1000000.0F;
	
	if( fw > (REAL32)0x01000000 )
	{
		ModeSwData->conf.MSWAccLevel = (REAL32)0x01000000;
	}
	else
	{
		ModeSwData->conf.MSWAccLevel = fw;
	}

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn20E : Mode Switch PosErr Value [pulse]									                */
/****************************************************************************************************/
PRM_RSLT pncal_spdmsperr(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
	MODE_SW		*ModeSwData;
	
    Prm = Axis->Prm;
	ModeSwData =  &(Axis->BaseCtrls->ModeSw);

	ModeSwData->conf.MSWErrLevel = Prm->PnCtrlCfgPrm.ModeSwPE;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn20F : torque feedforward configure 									                    */
/****************************************************************************************************/
PRM_RSLT pncal_trqffcfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
	
    Prm = Axis->Prm;

	Axis->BaseCtrls->CtrlCmdPrm.TrqffCfg = Prm->PnCtrlCfgPrm.TrqffCfg;


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn210 : torque feedforward filter time constant[0.01ms] 								    */
/****************************************************************************************************/
PRM_RSLT pncal_trqfffil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
	
    Prm = Axis->Prm;

	Axis->BaseCtrls->CtrlCmdPrm.KtrqffFil = FlibPcalKf1gain( Prm->PnCtrlCfgPrm.TrqffFil*10, PS_CYCLEUS, 0);

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn211 : torque feedforward gain[1%]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_trqffgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT 	*Bprm;
    PRMDATA 	*Prm;

    Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;
	
	Axis->BaseCtrls->CtrlCmdPrm.Ktrqff = PcalTrqFFGain(Bprm->Kvx, 
		                                              Prm,
		                                              PS_CYCLEUS );	


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn212 : speed feedforward configure 									                    */
/****************************************************************************************************/
PRM_RSLT pncal_spdffcfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
	
    Prm = Axis->Prm;

	Axis->BaseCtrls->CtrlCmdPrm.SpdffCfg = Prm->PnCtrlCfgPrm.SpdffCfg;


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn213 : speed feedforward filter time constant[0.01ms] 								        */
/****************************************************************************************************/
PRM_RSLT pncal_spdfffil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
	
    Prm = Axis->Prm;
	
	Axis->BaseCtrls->CtrlCmdPrm.KspdffFil = FlibPcalKf1gain( Prm->PnCtrlCfgPrm.SpdffFil*10, PS_CYCLEUS, 0);

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn214 : speed feedforward gain[1%]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_spdffgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT 	*Bprm;
    PRMDATA 	*Prm;

    Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;
	
	Axis->BaseCtrls->CtrlCmdPrm.Kspdff = PcalSpdFFGain( Bprm->Kpx, 
		                                             Prm->PnCtrlCfgPrm.SpdffGain,
		                                             PS_CYCLEUS );

	return PRM_RSLT_SUCCESS;

}



/****************************************************************************************************/
/*		Pn215 : Servo On Speed Limit [rpm]									                        */
/****************************************************************************************************/
PRM_RSLT pncal_SvoOnSpdLim(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA				*Prm;
	CTRL_CMD_PRM		*CtrlCmdPrm;

	Prm = Axis->Prm;
	CtrlCmdPrm	= &(Axis->BaseCtrls->CtrlCmdPrm);
	
	CtrlCmdPrm->SvonSpdLmtLevel = (REAL32)Prm->PnCtrlCfgPrm.SvoOnSpdLim * Axis->BaseLoops->Bprm->Kspdrpm;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn216 : Torque control speed limit [rpm]									                */
/****************************************************************************************************/
PRM_RSLT pncal_TconSpdLim(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA				*Prm;
	CTRL_CMD_PRM		*CtrlCmdPrm;

	Prm = Axis->Prm;
	CtrlCmdPrm	= &(Axis->BaseCtrls->CtrlCmdPrm);
	
	CtrlCmdPrm->OvrTrqSpdLim = (REAL32)Prm->PnCtrlCfgPrm.TconSpdLim * Axis->BaseLoops->Bprm->Kspdrpm;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn217 : speed moving average filter configure				  			                    */
/****************************************************************************************************/
PRM_RSLT pncal_spdMafilCfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA				*Prm;

	Prm = Axis->Prm;

	if(Prm->PnCtrlCfgPrm.SpdMaFilCfg >=0 && Prm->PnCtrlCfgPrm.SpdMaFilCfg <=4 )
	{
		Axis->BaseLoops->MotSpdMaFil->manumBit = Prm->PnCtrlCfgPrm.SpdMaFilCfg; 
	}
	else
	{
		Axis->BaseLoops->MotSpdMaFil->manumBit = 4;
	}
        
	Axis->BaseLoops->MotSpdMaFil->idx = 0;
	Axis->BaseLoops->MotSpdMaFil->ksub = 0;
	Axis->BaseLoops->MotSpdMaFil->mabufSumx = 0;
            
        Axis->BaseLoops->MotSpdMaFil1ms[0].manumBit = 6;
        Axis->BaseLoops->MotSpdMaFil1ms[0].idx = 0;
        Axis->BaseLoops->MotSpdMaFil1ms[0].ksub = 0;
        Axis->BaseLoops->MotSpdMaFil1ms[0].mabufSumx = 0;
        
        Axis->BaseLoops->MotSpdMaFil1ms[1].manumBit = 6;
        Axis->BaseLoops->MotSpdMaFil1ms[1].idx = 0;
        Axis->BaseLoops->MotSpdMaFil1ms[1].ksub = 0;
        Axis->BaseLoops->MotSpdMaFil1ms[1].mabufSumx = 0;    
        
        Axis->BaseLoops->MotSpdMaFil1ms[2].manumBit = 6;
        Axis->BaseLoops->MotSpdMaFil1ms[2].idx = 0;
        Axis->BaseLoops->MotSpdMaFil1ms[2].ksub = 0;
        Axis->BaseLoops->MotSpdMaFil1ms[2].mabufSumx = 0;        

        Axis->BaseLoops->MotSpdMaFil1ms[3].manumBit = 6;
        Axis->BaseLoops->MotSpdMaFil1ms[3].idx = 0;
        Axis->BaseLoops->MotSpdMaFil1ms[3].ksub = 0;
        Axis->BaseLoops->MotSpdMaFil1ms[3].mabufSumx = 0;                

	return PRM_RSLT_SUCCESS;

}


/****************************************************************************************************/
/*		Pn218 : First torque command filter time constant [0.01ms]									*/
/****************************************************************************************************/
PRM_RSLT pncal_TrqFilTi(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	TUNELESS_SETPRM 	SetPrm;
	Prm = Axis->Prm;

	PcalBaseTrqLpassFilter( &(Axis->BaseLoops->TrqFil),
							Axis->BaseCtrls,
							Prm->PnCtrlCfgPrm.TrqFilTime,
							Prm->PnCtrlCfgPrm.TrqFilTime,
							1 );
    
//	PcalBaseTrqLpassFilter( &(Axis->BaseLoops->TrqFil),
//							Axis->BaseCtrls,
//							Prm->PnCtrlCfgPrm.TrqFilTime,
//							Prm->PnCtrlCfgPrm.TrqFilTime2,
//							0 );
	#if NO_ADVFUNC
	SetPrm.DatLevel = Prm->PnAdvPrm.TlsCfg; 		
	SetPrm.trqfil11 = Prm->PnCtrlCfgPrm.TrqFilTime; 	
	SetPrm.ampw = Prm->PnCfgPrm.DevRatW;				
	SetPrm.ignq = Prm->PnCtrlCfgPrm.CurLoopHz;				
	SetPrm.kiq = Prm->PnCtrlCfgPrm.CurLoopTi;						
	SetPrm.fbfil2 = 50; 			
	SetPrm.TuneLessGain = 2000; 	
	SetPrm.TuneLessStiff = 40; 
	SetPrm.TuneLessTrqLpf = 50; 
	SetPrm.TuneLessVfbLpf = 50; 			
	SetPrm.DetVib = Axis->DetVib;
//	SetPrm.ANotchSeq = Axis->ANotchSeq;

	TuneLessCalculatePrm( Axis->BaseLoops, 
						   Axis->BaseLoops->Bprm,
						   &SetPrm,
						   Axis->Prm );

   #endif													   
   ATGSEL  *pAtGsel;							
   pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);    	/* condition A */
   PcalAtGseldKlpfSet( pAtGsel, &(pAtGsel->conf.dGain));   /* calculate dKlpf */
   pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);    	/* condition B */
   PcalAtGseldKlpfSet( pAtGsel, &(pAtGsel->conf.dGain));   /* calculate dKlpf */
   

	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn219 : Second torque command filter frequency [Hz]												*/
/****************************************************************************************************/
PRM_RSLT pncal_TrqFil2Freq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;	
	INT32	TrqRefFil;
	INT32	TuneLessFil;

	Prm = Axis->Prm;

	if( (TuneLessGetTuneLessUse(&Axis->BaseCtrls->TuneLessCtrl) == TRUE)			/* TuningLessUse*/
		&& (TuneLessGetTuneLessModeEx(&Axis->BaseCtrls->TuneLessCtrl) == TRUE) )    /* TuningLessEx*/
	{ 
		TuneLessFil = 1500;
	}
	else
	{
		TuneLessFil = 0;
	}

	if( Prm->PnCtrlCfgPrm.TrqFil2Freq >= 2500 )
	{ 
		TrqRefFil = TuneLessFil;
	}
	else
	{
		TrqRefFil = Prm->PnCtrlCfgPrm.TrqFil2Freq ;
	}


	if( (TrqRefFil >= 2500) || (TrqRefFil < 100) )
	{ 
		Axis->BaseCtrls->CtrlCmdPrm.LpassFil2 = FALSE;
	}
 

	PcalBaseTrqLpassFilter2( &(Axis->BaseLoops->TrqFil), TrqRefFil, Prm->PnCtrlCfgPrm.TrqFil2Q );

	if( (TrqRefFil < 2500) && (TrqRefFil >= 100) )
	{ 
		Axis->BaseCtrls->CtrlCmdPrm.LpassFil2 = TRUE;
	}

	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************/
/*		Pn21A : Second torque command filter Q value [0.01]											*/
/****************************************************************************************************/
PRM_RSLT pncal_TrqFil2Q(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;
	
	PcalBaseTrqLpassFilter2( &(Axis->BaseLoops->TrqFil),
							 Prm->PnCtrlCfgPrm.TrqFil2Freq,
							 Prm->PnCtrlCfgPrm.TrqFil2Q );

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn21B : Forward internal torque limit[%]											        */
/****************************************************************************************************/
PRM_RSLT pncal_fwtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	Prm->PnCtrlCfgPrm.FwIntTrqLmt = (REAL32)Prm->PnMotPrm.MaxTrq/(REAL32)Prm->PnMotPrm.RatTrq*100.0f;
	/* [%] --> [2^24/MaxTrq] */
	Axis->BaseCtrls->TrqLimitData.P.FwIntTrqLmt = 10.0f* Prm->PnCtrlCfgPrm.FwIntTrqLmt
	                                          *Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn21C : Reverse internal torque limit[%]											        */
/****************************************************************************************************/
PRM_RSLT pncal_rvtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	Prm->PnCtrlCfgPrm.RvIntTrqLmt = (REAL32)Prm->PnMotPrm.MaxTrq/(REAL32)Prm->PnMotPrm.RatTrq*100.0f;
	/* [%] --> [2^24/MaxTrq] */
	Axis->BaseCtrls->TrqLimitData.P.RvIntTrqLmt = 10.0f* Prm->PnCtrlCfgPrm.RvIntTrqLmt
											  *Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn21D : Forward external torque limit[%]													*/
/****************************************************************************************************/
PRM_RSLT pncal_outfwtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	/* [%] --> [2^24/MaxTrq] */
	Axis->BaseCtrls->TrqLimitData.P.FwExtTrqLmt = 10.0f* Prm->PnCtrlCfgPrm.FwExtTrqLmt
	                						   *Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn21E : Reverse external torque limit[%]													*/
/****************************************************************************************************/
PRM_RSLT pncal_outrvtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	/* [%] --> [2^24/MaxTrq] */
	Axis->BaseCtrls->TrqLimitData.P.RvExtTrqLmt = 10.0f* Prm->PnCtrlCfgPrm.RvExtTrqLmt
	                						   *Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn21F : Emergency stop torque limit[%]													    */
/****************************************************************************************************/
PRM_RSLT pncal_emgtrqlmt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	/* [%] --> [2^24/MaxTrq] */
	Axis->BaseCtrls->TrqLimitData.P.EstopTrqLmt = 10.0f* Prm->PnCtrlCfgPrm.EstopTrqLmt
	                						   *Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn228 : Speed command filter time constant [0.01ms]											*/
/****************************************************************************************************/
PRM_RSLT pncal_srfil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	PRMDATA	*Prm;
	BPRMDAT	*Bprm;

	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;
	
	Axis->BaseCtrls->CtrlCmdPrm.KVrfFil = FlibPcalKf1gain( Prm->PnCtrlCfgPrm.SpdRefLpfTime*10, TASKB_CYCLEUS, 0);

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn229 : Notch Filter config					                                            */
/****************************************************************************************************/
PRM_RSLT pncal_notchcfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	Axis->BaseLoops->CurLoop.P.NotFilCfg = Axis->Prm->PnCtrlCfgPrm.NotchFilterCfg;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn22A--Pn22C : Notch Filter 1 Frequency[Hz], Q Value[0.01], Depth[0.001]					*/
/****************************************************************************************************/
PRM_RSLT pncal_notchfilter1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	CTRL_CFG_PRM   *CtrlPrm = &Axis->Prm->PnCtrlCfgPrm;

	REAL32 Fc =  (REAL32)CtrlPrm->NotchFilterFc1;
	REAL32 Qx =  (REAL32)CtrlPrm->NotchFilterQ1/100.0f;
	REAL32 Dx =  (REAL32)CtrlPrm->NotchFilterDepth1/1000.0f;
	REAL32 Fs =  (REAL32)CUR_FRQ;

	FilbPcalNotchFilter2(Fc, Qx, Dx, Axis->BaseLoops->CurLoop.P.NotchKn1, Fs);


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn22D--Pn22F : Notch Filter 2 Frequency[Hz], Q Value[0.01], Depth[0.001]					*/
/****************************************************************************************************/
PRM_RSLT pncal_notchfilter2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	CTRL_CFG_PRM   *CtrlPrm = &Axis->Prm->PnCtrlCfgPrm;

	REAL32 Fc =  (REAL32)CtrlPrm->NotchFilterFc2;
	REAL32 Qx =  (REAL32)CtrlPrm->NotchFilterQ2/100.0f;
	REAL32 Dx =  (REAL32)CtrlPrm->NotchFilterDepth2/1000.0f;
	REAL32 Fs =  (REAL32)CUR_FRQ;

	FilbPcalNotchFilter2(Fc, Qx, Dx, Axis->BaseLoops->CurLoop.P.NotchKn2, Fs);


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn230--Pn232 : Notch Filter 3 Frequency[Hz], Q Value[0.01], Depth[0.001]					*/
/****************************************************************************************************/
PRM_RSLT pncal_notchfilter3(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	CTRL_CFG_PRM   *CtrlPrm = &Axis->Prm->PnCtrlCfgPrm;

	REAL32 Fc =  (REAL32)CtrlPrm->NotchFilterFc3;
	REAL32 Qx =  (REAL32)CtrlPrm->NotchFilterQ3/100.0f;
	REAL32 Dx =  (REAL32)CtrlPrm->NotchFilterDepth3/1000.0f;
	REAL32 Fs =  (REAL32)CUR_FRQ;

	FilbPcalNotchFilter2(Fc, Qx, Dx, Axis->BaseLoops->CurLoop.P.NotchKn3, Fs);


	return PRM_RSLT_SUCCESS;

}
/****************************************************************************************************/
/*		 Pn233--Pn235 : Notch Filter 4 Frequency[Hz], Q Value[0.01], Depth[0.001]					*/
/****************************************************************************************************/
PRM_RSLT pncal_notchfilter4(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	CTRL_CFG_PRM   *CtrlPrm = &Axis->Prm->PnCtrlCfgPrm;

	REAL32 Fc =  (REAL32)CtrlPrm->NotchFilterFc4;
	REAL32 Qx =  (REAL32)CtrlPrm->NotchFilterQ4/100.0f;
	REAL32 Dx =  (REAL32)CtrlPrm->NotchFilterDepth4/1000.0f;
	REAL32 Fs =  (REAL32)CUR_FRQ;

	FilbPcalNotchFilter2(Fc, Qx, Dx, Axis->BaseLoops->CurLoop.P.NotchKn4, Fs);


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn236 : Speed feedback filter time constant [0.01ms]										*/
/****************************************************************************************************/
PRM_RSLT pncal_sfbfil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	CTRL_CMD_PRM	*CtrlCmdPrm;						
	PRMDATA 	*Prm;

	CtrlCmdPrm	= &(Axis->BaseCtrls->CtrlCmdPrm);
	Prm = Axis->Prm;
			
	CtrlCmdPrm->KVfdbfil = FlibPcalKf1gain( ((Prm->PnCtrlCfgPrm.SpdFdbLpfTime) * 10 ), PS_CYCLEUS, 0);

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn239 : Current Loop Ti[0.01ms]									                            */
/****************************************************************************************************/
PRM_RSLT pncal_curloopti(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
    Prm = Axis->Prm;
    
	if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_CURLOOPTI_SET)
	{
		Axis->BaseLoops->CurTiType = 1;
	}
	else
	{
		Axis->BaseLoops->CurTiType = 0;
	}

    PcalBaseCurCtrl( Axis->BaseLoops , &Prm->PnCtrlCfgPrm.CurLoopHz, &Prm->PnCtrlCfgPrm.CurLoopTi);

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn23B : Position Reference Ma Filter Time[0.1ms]					                        */
/****************************************************************************************************/
PRM_RSLT pncal_PosRefMaFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
    Prm = Axis->Prm;

	PcmdFilCalculatePrmMoveAvergeFilter( &(Axis->BaseCtrls->PcmdFil.MaFil),
										 Prm->PnCtrlCfgPrm.PosRefMaFil,
										 Prm->PnCtrlCfgPrm.PosRefHFRad,
										 PS_CYCLEUS );
	
	PcmdFilCalculatePrmHighFilter( &(Axis->BaseCtrls->PcmdFil.MaFil),
								   Prm->PnCtrlCfgPrm.PosRefMaFil,
								   Prm->PnCtrlCfgPrm.PosRefHFRad,
								   PS_CYCLEUS);
	
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn23C : Speed feedforward Ma Filter Time[0.1ms]									            */
/****************************************************************************************************/
PRM_RSLT pncal_SpdffMaFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
    Prm = Axis->Prm;

	PcmdFilCalculatePrmMoveAvergeFilter( &(Axis->BaseCtrls->VFFcmdFil),
										 Prm->PnCtrlCfgPrm.SpdffMaFil,
										 0,
										 PS_CYCLEUS);
	FFCmdFilInitialize( &(Axis->BaseCtrls->VFFcmdFil) );
	
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn23D: Torque feedforward Ma Filter Time[0.1ms]									            */
/****************************************************************************************************/
PRM_RSLT pncal_TrqffMaFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
    Prm = Axis->Prm;

	PcmdFilCalculatePrmMoveAvergeFilter( &(Axis->BaseCtrls->TFFcmdFil),
										 Prm->PnCtrlCfgPrm.TrqffMaFil,
										 0,
										 PS_CYCLEUS );
	FFCmdFilInitialize( &(Axis->BaseCtrls->TFFcmdFil) );

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn23E: Position Reference high filter ratio[%]									            */
/****************************************************************************************************/
PRM_RSLT pncal_PosRefHFRad(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA 	*Prm;
    Prm = Axis->Prm;

	PcmdFilCalculatePrmMoveAvergeFilter( &(Axis->BaseCtrls->PcmdFil.MaFil),
										 Prm->PnCtrlCfgPrm.PosRefMaFil,
										 Prm->PnCtrlCfgPrm.PosRefHFRad,
										 PS_CYCLEUS );
	
	PcmdFilCalculatePrmHighFilter( &(Axis->BaseCtrls->PcmdFil.MaFil),
								   Prm->PnCtrlCfgPrm.PosRefMaFil,
								   Prm->PnCtrlCfgPrm.PosRefHFRad,
								   PS_CYCLEUS);

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn23F: Position Reference Exp Filter Time[0.1ms]									        */
/****************************************************************************************************/
PRM_RSLT pncal_PosRefExpFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT 	*Bprm;
    PRMDATA 	*Prm;
	
    Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

	PcmdFilCalculatePrmExpFilter( &(Axis->BaseCtrls->PcmdFil.ExpFil),
								  Prm->PnCtrlCfgPrm.PosRefExpFil,
								  Bprm->Kmotpls,
								  Bprm->MaxSpd,
								  PS_CYCLEUS);


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn301 : A type Vibration Suppression Option 											    */
/****************************************************************************************************/
PRM_RSLT pncal_AvibOpt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	ResVibCalculatePrmSW( &(Axis->BaseCtrls->ResVib),
							 Axis->Prm->PnAdvPrm.AvibOpt,
						     Axis->BaseCtrls->TuneLessCtrl.conf.TuningLessUse );
	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn240 : Program Jog switch											    					*/
/****************************************************************************************************/
PRM_RSLT pncal_pjogsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	if( (Axis->FnCmnCtrl->FnCtrlMcmd == CTRL_MODE_PJOG)
									&& (Axis->BaseCtrls->PJogHdl.PJogState == 1) )
	{
		return( PRM_RSLT_CONDITION_ERR );		
	}
	else
	{
		PcalPjogSw( Axis ); 				
		return PRM_RSLT_SUCCESS;
	}

}

/****************************************************************************************************/
/*		Pn241 : Program Jog moving distance											    			*/
/****************************************************************************************************/
PRM_RSLT pncal_pjogdist(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	if( (Axis->FnCmnCtrl->FnCtrlMcmd == CTRL_MODE_PJOG)
									&& (Axis->BaseCtrls->PJogHdl.PJogState == 1) )

	{
		return( PRM_RSLT_CONDITION_ERR );		
	}
	else
	{
		PcalPjogDistance( Axis );			
		return PRM_RSLT_SUCCESS;
	}

}

/****************************************************************************************************/
/*		Pn242 : Program Jog moving speed											    			*/
/****************************************************************************************************/
PRM_RSLT pncal_pjogspd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	if( (Axis->FnCmnCtrl->FnCtrlMcmd == CTRL_MODE_PJOG)
									&& (Axis->BaseCtrls->PJogHdl.PJogState == 1) )

	{
		return( PRM_RSLT_CONDITION_ERR );		
	}
	else
	{
		PcalPjogRotspd( Axis );			
		return PRM_RSLT_SUCCESS;
	}

}

/****************************************************************************************************/
/*		Pn243 : Program Jog Acc/Dec time										    				*/
/****************************************************************************************************/
PRM_RSLT pncal_pjogacctm(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	if( (Axis->FnCmnCtrl->FnCtrlMcmd == CTRL_MODE_PJOG)
									&& (Axis->BaseCtrls->PJogHdl.PJogState == 1) )

	{
		return( PRM_RSLT_CONDITION_ERR );		
	}
	else
	{
		PcalPjogAcctime( Axis );			
		return PRM_RSLT_SUCCESS;
	}

}

/****************************************************************************************************/
/*		Pn244 : Program Jog wait time											    				*/
/****************************************************************************************************/
PRM_RSLT pncal_pjogwaittm(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	if( (Axis->FnCmnCtrl->FnCtrlMcmd == CTRL_MODE_PJOG)
									&& (Axis->BaseCtrls->PJogHdl.PJogState == 1) )

	{
		return( PRM_RSLT_CONDITION_ERR );		
	}
	else
	{
		PcalPjogWaitTime( Axis );			
		return PRM_RSLT_SUCCESS;
	}

}

/****************************************************************************************************/
/*		Pn245 : Program Jog moving number											    			*/
/****************************************************************************************************/
PRM_RSLT pncal_pjognum(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	if( (Axis->FnCmnCtrl->FnCtrlMcmd == CTRL_MODE_PJOG)
									&& (Axis->BaseCtrls->PJogHdl.PJogState == 1) )

	{
		return( PRM_RSLT_CONDITION_ERR );		
	}
	else
	{
		PcalPjogNum( Axis );			
		return PRM_RSLT_SUCCESS;
	}

}

/****************************************************************************************************/
/*		Pn246 : Jog Speed											    							*/
/****************************************************************************************************/
PRM_RSLT pncal_jogspd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	REAL32 fx;

	PRMDATA 		*Prm;
	BPRMDAT 		*Bprm;

	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

	if( Prm->PnCtrlCfgPrm.JogAccT == 0 )
	{
		fx = 0x01000000;
	}
	else
	{
//		fx = (10000 * 0x01000000 / ( Bprm->PerOvrSpd + 10000 ))*PS_CYCLEUS/ ( 1000 * Prm->PnCtrlCfgPrm.JogAccT );
		fx = MlibABS(Prm->PnCtrlCfgPrm.JogSpd);
        fx = Bprm->Kspdrpm * fx * PS_CYCLEUS / (1000.0 * Prm->PnCtrlCfgPrm.JogAccT ) ;
	}
	
	Axis->BaseCtrls->CtrlCmdPrm.SpdSfsAcc = fx;
	
	if( Prm->PnCtrlCfgPrm.JogDecT == 0 )
	{
		fx = 0x01000000;
	}
	else
	{
//		fx = 10000 * 0x01000000 / ( Bprm->PerOvrSpd + 10000 ) * PS_CYCLEUS / ( 1000 * Prm->PnCtrlCfgPrm.JogDecT );
		fx = MlibABS(Prm->PnCtrlCfgPrm.JogSpd);
		fx = Bprm->Kspdrpm * fx * PS_CYCLEUS / (1000.0 * Prm->PnCtrlCfgPrm.JogDecT ) ;
	}
	Axis->BaseCtrls->CtrlCmdPrm.SpdSfsDec = fx;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn247 : Jog Acceleration time											    				*/
/****************************************************************************************************/
PRM_RSLT pncal_jogacct(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
/*																									*/
/*						10000 * 0x01000000 * ScanTime												*/
/*			dSpd = ---------------------------------------											*/
/*					 (PerOvrSpd+10000)	* 1000 * AccTimex											*/
/*																									*/
/*																									*/
/*			AccTimex [ms] : acceleration time														*/
/*			ScanTime [us] : Calculation cycle														*/
/*																									*/

	REAL32 fx;

	PRMDATA 		*Prm;
	BPRMDAT 		*Bprm;

	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

	if( Prm->PnCtrlCfgPrm.JogAccT == 0 )
	{
		fx = 0x01000000;
	}
	else
	{
//		fx = (10000 * 0x01000000 / ( Bprm->PerOvrSpd + 10000 ))*PS_CYCLEUS/ ( 1000 * Prm->PnCtrlCfgPrm.JogAccT );
        fx = Bprm->Kspdrpm * Prm->PnCtrlCfgPrm.JogSpd * PS_CYCLEUS
		                                            / (1000.0 * Prm->PnCtrlCfgPrm.JogAccT ) ;
	}
	Axis->BaseCtrls->CtrlCmdPrm.SpdSfsAcc = fx;
	

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn248 : Jog Deceleration time											    				*/
/****************************************************************************************************/
PRM_RSLT pncal_jogdect(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	REAL32 fx;

	PRMDATA *Prm;
	BPRMDAT *Bprm;

	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

	if( Prm->PnCtrlCfgPrm.JogDecT == 0 )
	{
		fx = 0x01000000;
	}
	else
	{
//		fx = 10000 * 0x01000000 / ( Bprm->PerOvrSpd + 10000 ) * PS_CYCLEUS / ( 1000 * Prm->PnCtrlCfgPrm.JogDecT );
        fx = Bprm->Kspdrpm * Prm->PnCtrlCfgPrm.JogSpd * PS_CYCLEUS
		                                            / (1000.0 * Prm->PnCtrlCfgPrm.JogDecT ) ;
	}
	Axis->BaseCtrls->CtrlCmdPrm.SpdSfsDec = fx;


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn249 : Gain Change Time 1											    					*/
/****************************************************************************************************/
PRM_RSLT pncal_gnchgt1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT *Bprm;

	Bprm = Axis->BaseLoops->Bprm;

	PcalAutoGainChange( &(Axis->BaseCtrls->GainChange.AtGselA),		
						Axis->Prm->PnCtrlCfgPrm.GnChgT1,					
						Axis->Prm->PnCtrlCfgPrm.GnChgWait1,				
						PS_CYCLEUS );							

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn24A : Gain Change Time 2											    					*/
/****************************************************************************************************/
PRM_RSLT pncal_gnchgt2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT *Bprm;

	Bprm = Axis->BaseLoops->Bprm;

	PcalAutoGainChange( &(Axis->BaseCtrls->GainChange.AtGselB),		
						Axis->Prm->PnCtrlCfgPrm.GnChgT2,					
						Axis->Prm->PnCtrlCfgPrm.GnChgWait2,				
						PS_CYCLEUS );	


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn24B : Gain Change Wait Time 1											    				*/
/****************************************************************************************************/
PRM_RSLT pncal_gnchgwait1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT *Bprm;

	Bprm = Axis->BaseLoops->Bprm;

	PcalAutoGainChange( &(Axis->BaseCtrls->GainChange.AtGselA),		
						Axis->Prm->PnCtrlCfgPrm.GnChgT1,					
						Axis->Prm->PnCtrlCfgPrm.GnChgWait1,				
						PS_CYCLEUS );							

	return PRM_RSLT_SUCCESS;

}


/****************************************************************************************************/
/*		Pn24C : Gain Change Wait Time 2											    				*/
/****************************************************************************************************/
PRM_RSLT pncal_gnchgwait2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BPRMDAT *Bprm;

	Bprm = Axis->BaseLoops->Bprm;

	PcalAutoGainChange( &(Axis->BaseCtrls->GainChange.AtGselB),		
						Axis->Prm->PnCtrlCfgPrm.GnChgT2,					
						Axis->Prm->PnCtrlCfgPrm.GnChgWait2,				
						PS_CYCLEUS );	

	return PRM_RSLT_SUCCESS;

}


/****************************************************************************************************/
/*		Pn24D : Gain Change Switch											    					*/
/****************************************************************************************************/
PRM_RSLT pncal_gnchgsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	UINT32	ucwk;
	PRMDATA *Prm;

	Prm = Axis->Prm;

	/* Pn24D.0 : Gain switching selection setting */
	switch( Prm->PnCtrlCfgPrm.GnChgSw & 0x0F )
	{
	case 0x00:
		Axis->BaseCtrls->GainChange.AtGsel = FALSE;
		break;
	case 0x01:
		Axis->BaseCtrls->GainChange.AtGsel = FALSE;
		break;
	case 0x02:
		Axis->BaseCtrls->GainChange.AtGsel = TRUE;
		break;
	default :
		ALMSetPramError( Axis->AlmMngr, pndef_GnChgSw.Number );
		break;
	}

	/* Pn24D.1 : Automatic gain switching condition A selection setting */
	ucwk = ( Prm->PnCtrlCfgPrm.GnChgSw >> 4 ) & 0x0F;
	switch( ucwk )
	{
		case 0x00:								
		case 0x01:									
		case 0x02:								
		case 0x03:									
		case 0x04:									
		case 0x05:									
			Axis->BaseCtrls->GainChange.AtGselTrgA = ucwk;
			break;
		default :
			ALMSetPramError( Axis->AlmMngr, pndef_GnChgSw.Number );
			break;
	}


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn250 : First torque command filter time constant2 [0.01ms]									*/
/****************************************************************************************************/
PRM_RSLT pncal_TrqFilTi2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	TUNELESS_SETPRM 	SetPrm;
	Prm = Axis->Prm;

	PcalBaseTrqLpassFilter( &(Axis->BaseLoops->TrqFil),
							Axis->BaseCtrls,
							Prm->PnCtrlCfgPrm.TrqFilTime,
							Prm->PnCtrlCfgPrm.TrqFilTime2,
							2 );
    
//	PcalBaseTrqLpassFilter( &(Axis->BaseLoops->TrqFil),
//							Axis->BaseCtrls,
//							Prm->PnCtrlCfgPrm.TrqFilTime,
//							Prm->PnCtrlCfgPrm.TrqFilTime2,
//							0 );
	
   													   
   ATGSEL  *pAtGsel;							
   pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);    	/* condition A */
//   PcalAtGseldKlpfSet( pAtGsel, &(pAtGsel->conf.dGain));   /* calculate dKlpf */
   pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);    	/* condition B */
//   PcalAtGseldKlpfSet( pAtGsel, &(pAtGsel->conf.dGain));   /* calculate dKlpf */
   

	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn250 : Gravity Torque [0.1%]																*/
/****************************************************************************************************/
PRM_RSLT pncal_GravTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	Axis->BaseCtrls->FrictionM.GrvTrq = Prm->PnCtrlCfgPrm.GravTrq*Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn251 : Static Friction Positive Torque [0.1%]												*/
/****************************************************************************************************/
PRM_RSLT pncal_PosClombTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	Axis->BaseCtrls->FrictionM.PosCTrq = Prm->PnCtrlCfgPrm.PosClombTrq*Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn252 : Static Friction Negetive Torque [0.1%]												*/
/****************************************************************************************************/
PRM_RSLT pncal_NegClombTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	Axis->BaseCtrls->FrictionM.NegCTrq = Prm->PnCtrlCfgPrm.NegClombTrq*Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn253 : Viscosity Torque [0.1%]																*/
/****************************************************************************************************/
PRM_RSLT pncal_ViscTrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	Axis->BaseCtrls->FrictionM.VisTrq = Prm->PnCtrlCfgPrm.ViscTrq*Axis->BaseLoops->Bprm->Kmottrq;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn254 : Friction Speed[rpm]																*/
/****************************************************************************************************/
PRM_RSLT pncal_FricSpd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	Axis->BaseCtrls->FrictionM.SpdHys = (REAL32)Prm->PnCtrlCfgPrm.FricSpd * Axis->BaseLoops->Bprm->Kspdrpm ;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn255 : Position  Disturbance Compensation [0.1%]																*/
/****************************************************************************************************/
PRM_RSLT pncal_PosDisturComp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;
	ALARM 			*Alm;
	REAL32      PosDis;
	UINT32	    PPRef = 17;
	REAL32		PPRKx = 1;
	REAL32		PosTrqKx = 0.075;

	Prm = Axis->Prm;
	Alm = Axis->AlmMngr;
	if(Prm->PnMotPrm.EncType == ENC_TYPE_INCREMENT)
	{
		PPRKx = Prm->PnMotPrm.AbzEncPPR/(1 << PPRef);
	}
	else
	{
		PPRKx = (1 << Prm->PnMotPrm.AbsEncBitS)/(1 << PPRef);
	}
	if(PPRKx == 0)
	{
		Axis->BaseCtrls->PosDisCDisable = 1;
	}
	PosTrqKx = PosTrqKx/PPRKx;
	
	if(Prm->PnCtrlCfgPrm.PosDisturComp == 0)
	{
			Axis->BaseCtrls->PosDisCDisable = 1;
	}
	else
	{
			Axis->BaseCtrls->PosDisCDisable = 0;
	}

	Axis->BaseCtrls->PosDisturCompKx = PosTrqKx *(Prm->PnCtrlCfgPrm.PosDisturComp*0.0001);
	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn256 : FunctionSw                									*/
/****************************************************************************************************/
PRM_RSLT pncal_FunctionSw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;

	Prm = Axis->Prm;
	if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_FUN_TRQ_SET)
	{
		Axis->BaseLoops->FunTqrCurType = 1;
	}
	else
	{
		Axis->BaseLoops->FunTqrCurType = 0;
	}    
	if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_VIRAXI_SET)
	{
		Axis->BaseCtrls->Cia402Axis.VirtualAxisEn = 1;
		Axis->AlmMngr->AlmSetMsk[0] = 0xFFFFFFFF;
        Axis->AlmMngr->AlmSetMsk[1] = 0xFFFFFFFF;
        Axis->AlmMngr->AlmSetMsk[2] = 0xFFFFFFFF;
        Axis->AlmMngr->AlmSetMsk[3] = 0xFFFFFFFF;
	}
	else
	{
		Axis->BaseCtrls->Cia402Axis.VirtualAxisEn = 0;
	}
	if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_CURLOOPTI_SET)
	{
		Axis->BaseLoops->CurTiType = 1;
	}
	else
	{
		Axis->BaseLoops->CurTiType = 0;
	}
        
       if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_VOLT_FF_SET)
	{
		Axis->BaseLoops->FunVoltFF = 1;
	}
	else
	{
		Axis->BaseLoops->FunVoltFF = 0;
	}
        
        
        if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_SPDFDB_HR)
	{
		Axis->BaseLoops->MotSpdHr = 1;
	}
	else
	{
		Axis->BaseLoops->MotSpdHr = 0;
	}

	if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_CUR_LIMIT_SET)
	{
		Axis->BaseLoops->FuncCurLimit = 0;
	}
	else
	{
		Axis->BaseLoops->FuncCurLimit = 1;  //default is 0 and enable torque limit
	}

	if(Axis->Prm->PnCtrlCfgPrm.FunctionSw  & FUNC_ENC_INCABNORMAL_COMP)
	{
		Axis->BaseLoops->Enc->P.EncIacEnable = 1;
	}
	else
	{
		Axis->BaseLoops->Enc->P.EncIacEnable = 0; //default is 0 and enable velocity limit
	}
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn258 :Pulse Speed Mode Switch                    0:Pluse+direction  1:encoder mode										*/
/****************************************************************************************************/
PRM_RSLT pncal_PulseSpdMode(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA			*Prm;

	Prm = Axis->Prm;

//		if(Axis->BaseCtrls->CmdEnable == FALSE)
//		{
//			if(Prm->PnCtrlCfgPrm.PulseSpdMode)
//			{
//					MX_TIM3_EncoderMode_Init(); 
//			}
//			else
//			{
//					MX_TIM3_Init();
//			}
//		}
        

	return PRM_RSLT_SUCCESS;
}
/****************************************************************************************************/
/*		Pn302 : A type Vibration Suppression damping frequency										*/
/****************************************************************************************************/
PRM_RSLT pncal_AvibFrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	RESVIB_CFG_PRM ResVibCfgPrm;
	PRMDATA 		*Prm;
	
	Prm = Axis->Prm;

	PcalCopyResVibCfgParam(&ResVibCfgPrm, Prm);
	ResVibCalculatePrm( &(Axis->BaseCtrls->ResVib), &ResVibCfgPrm, Axis->BaseLoops->Bprm->Kvx, PS_CYCLENS);


	
	#endif
        
        return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn303 : A type Vibration Suppression damping gain correction 								*/
/****************************************************************************************************/
PRM_RSLT pncal_AvibGn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	RESVIB_CFG_PRM ResVibCfgPrm;
	PRMDATA 		*Prm;
	
	Prm = Axis->Prm;

	PcalCopyResVibCfgParam(&ResVibCfgPrm, Prm);
	ResVibCalculatePrm( &(Axis->BaseCtrls->ResVib), &ResVibCfgPrm, Axis->BaseLoops->Bprm->Kvx, PS_CYCLENS);

	return PRM_RSLT_SUCCESS;
	#endif
return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn304 : A type Vibration Suppression damping gain 											*/
/****************************************************************************************************/
PRM_RSLT pncal_AvibDamp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	RESVIB_CFG_PRM ResVibCfgPrm;
	PRMDATA 		*Prm;
	
	Prm = Axis->Prm;

	PcalCopyResVibCfgParam(&ResVibCfgPrm, Prm);
	ResVibCalculatePrm( &(Axis->BaseCtrls->ResVib), &ResVibCfgPrm, Axis->BaseLoops->Bprm->Kvx, PS_CYCLENS);

	return PRM_RSLT_SUCCESS;
	#endif
return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn305 : A type Vibration Suppression damping filter time constant 1 correction 				*/
/****************************************************************************************************/
PRM_RSLT pncal_AvibFil1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	RESVIB_CFG_PRM ResVibCfgPrm;
	PRMDATA 		*Prm;
	
	Prm = Axis->Prm;

	PcalCopyResVibCfgParam(&ResVibCfgPrm, Prm);
	ResVibCalculatePrm( &(Axis->BaseCtrls->ResVib), &ResVibCfgPrm, Axis->BaseLoops->Bprm->Kvx, PS_CYCLENS);

	return PRM_RSLT_SUCCESS;
	#endif
return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn306 : A type Vibration Suppression damping filter time constant 2 correction 				*/
/****************************************************************************************************/
PRM_RSLT pncal_AvibFil2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	RESVIB_CFG_PRM ResVibCfgPrm;
	PRMDATA 		*Prm;
	
	Prm = Axis->Prm;

	PcalCopyResVibCfgParam(&ResVibCfgPrm, Prm);
	ResVibCalculatePrm( &(Axis->BaseCtrls->ResVib), &ResVibCfgPrm, Axis->BaseLoops->Bprm->Kvx, PS_CYCLENS);

	return PRM_RSLT_SUCCESS;
	#endif
return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn307 : A type Vibration Suppression Inertia damping gain 														*/
/****************************************************************************************************/
PRM_RSLT pncal_AvibDamp2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{	
	#if NO_ADVFUNC
	RESVIB_CFG_PRM ResVibCfgPrm;
	PRMDATA 		*Prm;
	
	Prm = Axis->Prm;

	PcalCopyResVibCfgParam(&ResVibCfgPrm, Prm);
	ResVibCalculatePrm( &(Axis->BaseCtrls->ResVib), &ResVibCfgPrm, Axis->BaseLoops->Bprm->Kvx, PS_CYCLENS);

	return PRM_RSLT_SUCCESS;
	#endif
       return PRM_RSLT_SUCCESS; 
}

/****************************************************************************************************/
/*		Pn308 : A type Vibration Suppression Inertia damping  frequency								*/
/****************************************************************************************************/
PRM_RSLT pncal_AvibFrq2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	RESVIB_CFG_PRM ResVibCfgPrm;
	PRMDATA 		*Prm;
	
	Prm = Axis->Prm;

	PcalCopyResVibCfgParam(&ResVibCfgPrm, Prm);
	ResVibCalculatePrm( &(Axis->BaseCtrls->ResVib), &ResVibCfgPrm, Axis->BaseLoops->Bprm->Kvx, PS_CYCLENS);

	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************/
/*		Pn309 : Disturbance observer gain [%]														*/
/****************************************************************************************************/
PRM_RSLT pncal_dobgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	DOBS_PRM		*DobsPrm;
	DOBS_CFGPRM		DobsCfgPrm;
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	DobsPrm = &(Axis->BaseCtrls->GainChange.GselDobs[1]);		

	DobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	DobsCfgPrm.loophz = (Prm->PnCtrlCfgPrm.SpdLoopHz);		// Pn203 : Speed loop gain	
	DobsCfgPrm.loophz2 = Prm->PnCtrlCfgPrm.SpdLoopHz2;		// Pn206 : Speed loop gain 2	
	DobsCfgPrm.dobgn = Prm->PnAdvPrm.DobsGn;				// Pn309 : Disturbance observer gain
	DobsCfgPrm.dobgn2 = Prm->PnAdvPrm.DobsGn2;		        // Pn30A : Disturbance observer gain 2
	DobsCfgPrm.dtrqgn = Prm->PnAdvPrm.DobsCoff;		        // Pn30B : Disturbance observer coefficient			
	DobsCfgPrm.dlpfil = Prm->PnAdvPrm.DobsFreqComp;			// Pn30C : Disturbance observer freq correction	
	DobsCfgPrm.dobjgn = Prm->PnAdvPrm.DobsGnComp;			// Pn30D : Disturbance observer gain correction

	DobsCalculatePrm( &(DobsPrm[0]), Axis->BaseLoops->Bprm, &DobsCfgPrm, 0 );

	Axis->BaseCtrls->GainChange.GselDobs[0] = Axis->BaseCtrls->GainChange.GselDobs[1];

	ATGSEL	*pAtGsel;								
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);			/* condition A */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs)); 	/* calculate dKs */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);			/* condition B */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs)); 	/* calculate dKs */

	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn30A : Disturbance observer gain 2[%]														*/
/****************************************************************************************************/
PRM_RSLT pncal_dobgn2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	DOBS_PRM		*DobsPrm;
	DOBS_CFGPRM		DobsCfgPrm;
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	DobsPrm = &(Axis->BaseCtrls->GainChange.GselDobs[1]);		

	DobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	DobsCfgPrm.loophz = (Prm->PnCtrlCfgPrm.SpdLoopHz);		// Pn203 : Speed loop gain	
	DobsCfgPrm.loophz2 = Prm->PnCtrlCfgPrm.SpdLoopHz2;		// Pn206 : Speed loop gain 2	
	DobsCfgPrm.dobgn = Prm->PnAdvPrm.DobsGn;				// Pn309 : Disturbance observer gain
	DobsCfgPrm.dobgn2 = Prm->PnAdvPrm.DobsGn2;		        // Pn30A : Disturbance observer gain 2
	DobsCfgPrm.dtrqgn = Prm->PnAdvPrm.DobsCoff;		        // Pn30B : Disturbance observer coefficient			
	DobsCfgPrm.dlpfil = Prm->PnAdvPrm.DobsFreqComp;			// Pn30C : Disturbance observer freq correction	
	DobsCfgPrm.dobjgn = Prm->PnAdvPrm.DobsGnComp;			// Pn30D : Disturbance observer gain correction

	DobsCalculatePrm( &(DobsPrm[1]), Axis->BaseLoops->Bprm, &DobsCfgPrm, 1 );

	Axis->BaseCtrls->GainChange.GselDobs[0] = Axis->BaseCtrls->GainChange.GselDobs[1];

	ATGSEL	*pAtGsel;							
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);			/* condition A */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs)); 	/* calculate dKs */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);			/* condition B */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs)); 	/* calculate dKs */
	#endif
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn30B : Disturbance observer coefficient [%]											    */
/****************************************************************************************************/
PRM_RSLT pncal_dtrqgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	DOBS_PRM		*DobsPrm;
	DOBS_CFGPRM		DobsCfgPrm;
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	DobsPrm = &(Axis->BaseCtrls->GainChange.GselDobs[1]);		

	DobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	DobsCfgPrm.loophz = (Prm->PnCtrlCfgPrm.SpdLoopHz);		// Pn203 : Speed loop gain	
	DobsCfgPrm.loophz2 = Prm->PnCtrlCfgPrm.SpdLoopHz2;		// Pn206 : Speed loop gain 2	
	DobsCfgPrm.dobgn = Prm->PnAdvPrm.DobsGn;				// Pn309 : Disturbance observer gain
	DobsCfgPrm.dobgn2 = Prm->PnAdvPrm.DobsGn2;		        // Pn30A : Disturbance observer gain 2
	DobsCfgPrm.dtrqgn = Prm->PnAdvPrm.DobsCoff;		        // Pn30B : Disturbance observer coefficient			
	DobsCfgPrm.dlpfil = Prm->PnAdvPrm.DobsFreqComp;			// Pn30C : Disturbance observer freq correction	
	DobsCfgPrm.dobjgn = Prm->PnAdvPrm.DobsGnComp;			// Pn30D : Disturbance observer gain correction	

	DobsCalculatePrm( &(DobsPrm[0]), Axis->BaseLoops->Bprm, &DobsCfgPrm, 0 );
	DobsCalculatePrm( &(DobsPrm[1]), Axis->BaseLoops->Bprm, &DobsCfgPrm, 1 );

	Axis->BaseCtrls->GainChange.GselDobs[0] = Axis->BaseCtrls->GainChange.GselDobs[1];
	#endif
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn30C : Disturbance observer freq correction [0.1Hz]										*/
/****************************************************************************************************/
PRM_RSLT pncal_dlpfil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	DOBS_PRM		*DobsPrm;
	DOBS_CFGPRM		DobsCfgPrm;
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	DobsPrm = &(Axis->BaseCtrls->GainChange.GselDobs[1]);		

	DobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	DobsCfgPrm.loophz = (Prm->PnCtrlCfgPrm.SpdLoopHz);		// Pn203 : Speed loop gain	
	DobsCfgPrm.loophz2 = Prm->PnCtrlCfgPrm.SpdLoopHz2;		// Pn206 : Speed loop gain 2	
	DobsCfgPrm.dobgn = Prm->PnAdvPrm.DobsGn;				// Pn309 : Disturbance observer gain
	DobsCfgPrm.dobgn2 = Prm->PnAdvPrm.DobsGn2;		        // Pn30A : Disturbance observer gain 2
	DobsCfgPrm.dtrqgn = Prm->PnAdvPrm.DobsCoff;		        // Pn30B : Disturbance observer coefficient			
	DobsCfgPrm.dlpfil = Prm->PnAdvPrm.DobsFreqComp;			// Pn30C : Disturbance observer freq correction	
	DobsCfgPrm.dobjgn = Prm->PnAdvPrm.DobsGnComp;			// Pn30D : Disturbance observer gain correction	

	DobsCalculatePrm( &(DobsPrm[0]), Axis->BaseLoops->Bprm, &DobsCfgPrm, 0 );
	DobsCalculatePrm( &(DobsPrm[1]), Axis->BaseLoops->Bprm, &DobsCfgPrm, 1 );

	Axis->BaseCtrls->GainChange.GselDobs[0] = Axis->BaseCtrls->GainChange.GselDobs[1];

	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn30D : Disturbance observer gain correction [%]										    */
/****************************************************************************************************/
PRM_RSLT pncal_dobjgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	DOBS_PRM		*DobsPrm;
	DOBS_CFGPRM		DobsCfgPrm;
	PRMDATA			*Prm;

	Prm = Axis->Prm;

	DobsPrm = &(Axis->BaseCtrls->GainChange.GselDobs[1]);		

	DobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	DobsCfgPrm.loophz = (Prm->PnCtrlCfgPrm.SpdLoopHz);		// Pn203 : Speed loop gain	
	DobsCfgPrm.loophz2 = Prm->PnCtrlCfgPrm.SpdLoopHz2;		// Pn206 : Speed loop gain 2	
	DobsCfgPrm.dobgn = Prm->PnAdvPrm.DobsGn;				// Pn309 : Disturbance observer gain
	DobsCfgPrm.dobgn2 = Prm->PnAdvPrm.DobsGn2;		        // Pn30A : Disturbance observer gain 2
	DobsCfgPrm.dtrqgn = Prm->PnAdvPrm.DobsCoff;		        // Pn30B : Disturbance observer coefficient			
	DobsCfgPrm.dlpfil = Prm->PnAdvPrm.DobsFreqComp;			// Pn30C : Disturbance observer freq correction	
	DobsCfgPrm.dobjgn = Prm->PnAdvPrm.DobsGnComp;			// Pn30D : Disturbance observer gain correction	

	DobsCalculatePrm( &(DobsPrm[0]), Axis->BaseLoops->Bprm, &DobsCfgPrm, 0 );
	DobsCalculatePrm( &(DobsPrm[1]), Axis->BaseLoops->Bprm, &DobsCfgPrm, 1 );

	Axis->BaseCtrls->GainChange.GselDobs[0] = Axis->BaseCtrls->GainChange.GselDobs[1];

	ATGSEL	*pAtGsel;								
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselA);			/* condition A */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs)); 	/* calculate dKs */
	pAtGsel = &(Axis->BaseCtrls->GainChange.AtGselB);			/* condition B */
	PcalAtGseldKsSet( pAtGsel, &(pAtGsel->conf.dGainDobs)); 	/* calculate dKs */

	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn30E : Speed observer gain [Hz]															*/
/****************************************************************************************************/
PRM_RSLT pncal_evobgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA		*Prm;
	BPRMDAT		*Bprm;
	#if NO_ADVFUNC
	EHVOBS_CTRL		*EhVobsCtrl;
	EHVOBS_CFGPRM	EhVobsCfgPrm;

	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;
	EhVobsCtrl = &(Axis->BaseCtrls->EhVobsCtrl);

	EhVobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	EhVobsCfgPrm.evobgn = Prm->PnAdvPrm.VobsGn;		        // Pn30E : Speed observer gain  				
	EhVobsCfgPrm.evobjgn = Prm->PnAdvPrm.VobsPosCompGn ;    // Pn30F : Speed observer pos compensation gain		
	EhVobsCfgPrm.trqfil11 = Prm->PnCtrlCfgPrm.TrqFilTime;   // Pn218 : 1st torque command filter time constant
	EhVobsCalculatePrm( EhVobsCtrl, Bprm, &EhVobsCfgPrm);
	#endif
    
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn30F : Speed observer pos compensation gain[%]											*/
/****************************************************************************************************/
PRM_RSLT pncal_evobjgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	PRMDATA		*Prm;
	BPRMDAT		*Bprm;
	EHVOBS_CTRL		*EhVobsCtrl;
	EHVOBS_CFGPRM	EhVobsCfgPrm;

	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;
	EhVobsCtrl = &(Axis->BaseCtrls->EhVobsCtrl);

	EhVobsCfgPrm.jrate = Prm->PnCtrlCfgPrm.Jrat;
	EhVobsCfgPrm.evobgn = Prm->PnAdvPrm.VobsGn;		        // Pn30E : Speed observer gain  				
	EhVobsCfgPrm.evobjgn = Prm->PnAdvPrm.VobsPosCompGn ;    // Pn30F : Speed observer pos compensation gain		
	EhVobsCfgPrm.trqfil11 = Prm->PnCtrlCfgPrm.TrqFilTime;   // Pn218 : 1st torque command filter time constant
	EhVobsCalculatePrm( EhVobsCtrl, Bprm, &EhVobsCfgPrm);

	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn310 : Advanced Application Switch											            */
/****************************************************************************************************/
PRM_RSLT pncal_advappsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 	*Prm;
	EHVOBS_CTRL 	*EhVobsCtrl;
	DOBS_CTRL       *DobsCtrl;

	#if NO_ADVFUNC
	Prm = Axis->Prm;
	
	EhVobsCtrl = &(Axis->BaseCtrls->EhVobsCtrl);
	DobsCtrl = &(Axis->BaseCtrls->DobsCtrl);


	if(Prm->PnAdvPrm.AdvAppSW & 0xF)
	{
		EhVobsCalculatePrmSW(&EhVobsCtrl->P.EhVobsUse, Axis->BaseCtrls->TuneLessCtrl.conf.TuningLessUse, 5);	
	}
	else
	{
		EhVobsCalculatePrmSW(&EhVobsCtrl->P.EhVobsUse, Axis->BaseCtrls->TuneLessCtrl.conf.TuningLessUse, 0);	
	}

	if((Prm->PnAdvPrm.AdvAppSW>>4) & 0xF)
	{
		DobsCalculatePrmSW(&DobsCtrl->V.DobsAct, Axis->BaseCtrls->TuneLessCtrl.conf.TuningLessUse, 1 );
	}
	else
	{
		DobsCalculatePrmSW(&DobsCtrl->V.DobsAct, Axis->BaseCtrls->TuneLessCtrl.conf.TuningLessUse, 0 );
	}
	#endif
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn311 : End Vibration Suppression Option											        */
/****************************************************************************************************/
PRM_RSLT pncal_evib_opt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 	*Prm;

	Prm = Axis->Prm;

	PcmdFilCalculatePrmVibSupFilSW( &(Axis->BaseCtrls->VibSupFil),
										Prm->PnAdvPrm.EVibOpt,
										Prm->PnAdvPrm.EVibFrq,
										Prm->PnAdvPrm.EVibFil,
										PS_CYCLENS);


	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************/
/*		 Pn312 : End Vibration Suppression frequency											    */
/****************************************************************************************************/
PRM_RSLT pncal_evib_frq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 	*Prm;

	Prm = Axis->Prm;

	PcmdFilCalculatePrmVibSupFil( &(Axis->BaseCtrls->VibSupFil),
									Prm->PnAdvPrm.EVibFrq,
									Prm->PnAdvPrm.EVibFil,
									PS_CYCLENS);


	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn313 : End Vibration Suppression compensation 								            */
/****************************************************************************************************/
PRM_RSLT pncal_evib_fil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA 	*Prm;

	Prm = Axis->Prm;

	PcmdFilCalculatePrmVibSupFil( &(Axis->BaseCtrls->VibSupFil),
									Prm->PnAdvPrm.EVibFrq,
									Prm->PnAdvPrm.EVibFil,
									PS_CYCLENS);	

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn314 : Tuneless setting								                                    */
/****************************************************************************************************/
PRM_RSLT pncal_tls_cfg(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	PRMDATA 	*Prm;
	TUNELESS_SETPRM		SetPrm;
	
	Prm = Axis->Prm;
	
	SetPrm.DatLevel = Prm->PnAdvPrm.TlsCfg;			
	SetPrm.trqfil11 = Prm->PnCtrlCfgPrm.TrqFilTime;		
	SetPrm.ampw = Prm->PnCfgPrm.DevRatW;				
	SetPrm.ignq = Prm->PnCtrlCfgPrm.CurLoopHz;				
	SetPrm.kiq = Prm->PnCtrlCfgPrm.CurLoopTi;						
	SetPrm.fbfil2 = 50; 			
	SetPrm.TuneLessGain = 2000; 	
	SetPrm.TuneLessStiff = 40; 
	SetPrm.TuneLessTrqLpf = 50; 
	SetPrm.TuneLessVfbLpf = 50; 				
	SetPrm.DetVib = Axis->DetVib;
//	SetPrm.ANotchSeq = Axis->ANotchSeq;

	TuneLessCalculatePrm( Axis->BaseLoops, 
	                       Axis->BaseLoops->Bprm,
						   &SetPrm,
						   Axis->Prm );


	#endif
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn317 : Model following control switch								                        */
/****************************************************************************************************/
PRM_RSLT pncal_mfcsw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	if(FALSE != MfcCalculatePrmSW( &(Axis->BaseCtrls->MFControl),
								   &MfcCfgPrm,
								   Axis->Prm->PnCtrlCfgPrm.Jrat,
								   Axis->BaseLoops->Bprm ) )
	{
		ALMSetPramError( Axis->AlmMngr, pndef_MfcSw.Number );
	}
	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn318 : Model following control gain								                        */
/****************************************************************************************************/
PRM_RSLT pncal_mfcgn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 0 );

	RpiRegSetOnePrmTuningFfLevel( Axis->OnePrmTune,   Axis->Prm->PnAdvPrm.MfcGn );
////////todo
	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn319 : MFC gain correction								                                */
/****************************************************************************************************/
PRM_RSLT pncal_mfcgncomp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 0 );


	#endif	
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn31A : MFC forward bias								                     			    */
/****************************************************************************************************/
PRM_RSLT pncal_mfcfwbias(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 0 );
	#endif

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn31B : MFC reverse bias								                        			*/
/****************************************************************************************************/
PRM_RSLT pncal_mfcrvbias(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 0 );

	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn31C : Vibration suppression 1 frequency A								              	*/
/****************************************************************************************************/
PRM_RSLT pncal_mfcvibfreqA(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC		
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 0 );

	 RpiRegSetVibMfcTuningFrequency( Axis->MfcTune, Axis->Prm->PnAdvPrm.VibSupFrq1A );
//////todo
	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn31D : Vibration suppression 1 frequency B					           					*/
/****************************************************************************************************/
PRM_RSLT pncal_mfcvibfreqB(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 0 );

	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn31E : MFC Velocity feedforward compensation								              	*/
/****************************************************************************************************/
PRM_RSLT pncal_mfcvff(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 0 );


	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn31F : MFC gain 2					           												*/
/****************************************************************************************************/
PRM_RSLT pncal_mfcgn2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 1 );

	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn320 : MFC gain correction 2								              					*/
/****************************************************************************************************/
PRM_RSLT pncal_mfcgncomp2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	MFC_CFG_PRM MfcCfgPrm;

	PcalCopyMFCCfgParam(&MfcCfgPrm, Axis->Prm);
	MfcCalculatePrm( &(Axis->BaseCtrls->MFControl),
					 &MfcCfgPrm,
					 Axis->Prm->PnCtrlCfgPrm.Jrat,
					 Axis->BaseLoops->Bprm,
					 0, 1 );
	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn321 : Weak field control gain								              				*/
/****************************************************************************************************/
PRM_RSLT pncal_wfbkp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;

	Prm = Axis->Prm;

	PcalVFBKp( &(Axis->BaseCtrls->WeakenField), Axis->BaseLoops->Bprm, 
											  Prm->PnAdvPrm.WfbKp, Prm->PnAdvPrm.WfbTi );

	PcalVFBKi( &(Axis->BaseCtrls->WeakenField), Axis->BaseLoops->Bprm, Prm->PnAdvPrm.WfbKp );

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn322 : Weak field control time constant								        			*/
/****************************************************************************************************/
PRM_RSLT pncal_wfbti(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;

	Prm = Axis->Prm;

	PcalVFBKp( &(Axis->BaseCtrls->WeakenField), Axis->BaseLoops->Bprm, 
											  Prm->PnAdvPrm.WfbKp, Prm->PnAdvPrm.WfbTi );

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn323 : Weak field IdRef Set								        			*/
/****************************************************************************************************/
PRM_RSLT pncal_wfidset(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    
    PcalBaseWfCtrl(Axis->BaseLoops ,Axis->Prm);
    
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn324 : Weak field control Switch								        			*/
/****************************************************************************************************/
PRM_RSLT pncal_wfswitch(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;

	Prm = Axis->Prm;
    
    if(Prm->PnAdvPrm.WfSwitch)
    {
        Axis->BaseLoops->CurLoop.P.WfSw = TRUE;
    }
    else
    {
        Axis->BaseLoops->CurLoop.P.WfSw = FALSE;
    }

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn325 : Vibration suppression 2 frequency								        			*/
/****************************************************************************************************/
PRM_RSLT pncal_vibSupfreq2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	PcmdFilCalculatePrmVibSupFil( &(Axis->BaseCtrls->VibSupFil), Axis->Prm->PnAdvPrm.VibSupFrq2,
								  Axis->Prm->PnAdvPrm.VibSupFrq2Comp, PS_CYCLENS );
	
	RpiRegSetMdlVibFreq2( Axis->MfcTune,
						  Axis->Prm->PnAdvPrm.VibSupFrq2 );

	return PRM_RSLT_SUCCESS;   
	#endif
        return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn326 : Vibration suppression 2 frequency correction								        */
/****************************************************************************************************/
PRM_RSLT pncal_vibSupfreq2Cmp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	PcmdFilCalculatePrmVibSupFil( &(Axis->BaseCtrls->VibSupFil), Axis->Prm->PnAdvPrm.VibSupFrq2,
								   Axis->Prm->PnAdvPrm.VibSupFrq2Comp, PS_CYCLENS );
	
	RpiRegSetMdlVibFil2( Axis->MfcTune, Axis->Prm->PnAdvPrm.VibSupFrq2Comp ); 
	
	return PRM_RSLT_SUCCESS;
	#endif
        return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn332 : Clamping Jaw Reverse Limit								        */
/****************************************************************************************************/
PRM_RSLT FnClampingJawForLit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    
        CLAMPINGJAW_INIT    *ClampingJawInit = &Axis->MotorIdentify->ClampingJawInit;
        
        ClampingJawInit->V.PosForwardLimit = Axis->Prm->PnAdvPrm.ClampingJawForLit;
	
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn333 : Clamping Rev Reverse Limit								        */
/****************************************************************************************************/
PRM_RSLT FnClampingJawRevLit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    
        CLAMPINGJAW_INIT    *ClampingJawInit = &Axis->MotorIdentify->ClampingJawInit;
        
        ClampingJawInit->V.PosReverseLimit = Axis->Prm->PnAdvPrm.ClampingJawRevLit;
	
	return PRM_RSLT_SUCCESS;

}
/****************************************************************************************************/
/*		 Pn401 : DI1 Fuction Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_FuncSlt_DI1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	// PRMDATA	*Prm = Axis->Prm;
    // UINT16 FuncDI,LogicDI,DiNum,Item,OldFuncDI;
    
    
    // Item  = 0;
    // DiNum = 0;
    // FuncDI  =  Prm->PnInputPrm.FuncDI1;
    // LogicDI =  Prm->PnInputPrm.LogicDI1;
    
    // OldFuncDI = Axis->SequenceIO->DiSignal[0].FuncCfg;
    
    // if(Axis->SequenceIO->DiSignal[0].FuncCfg == FuncDI)
    // {
    //   return PRM_RSLT_SUCCESS;
    // }
    
    // if(Di_Allocate_Error(Axis->SequenceIO,FuncDI,OldFuncDI) == 0)
    // {
    //   SeqIo_DiCfg(Axis->SequenceIO,FuncDI,LogicDI,Item,DiNum);
    //   return PRM_RSLT_SUCCESS;
    // }
    // else 
    // {
    //   return PRM_RSLT_CONDITION_ERR;
    // }
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn402 : DI1 Logic Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_LogicSlt_DI1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	// PRMDATA	*Prm = Axis->Prm;
    // UINT16 FuncDI,LogicDI,DiNum,Item;
    
    // Item  = 1;
    // DiNum = 0;

    // FuncDI  =  Prm->PnInputPrm.FuncDI1;
    // LogicDI =  Prm->PnInputPrm.LogicDI1;
    
    // SeqIo_DiCfg(Axis->SequenceIO,FuncDI,LogicDI,Item,DiNum);

	return PRM_RSLT_SUCCESS;
}
/****************************************************************************************************/
/*		 Pn401 : DI2 Fuction Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_FuncSlt_DI2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	// PRMDATA	*Prm = Axis->Prm;
    // UINT16 FuncDI,LogicDI,DiNum,Item,OldFuncDI;
   
    // Item  = 0;
    // DiNum = 1;
    // FuncDI  =  Prm->PnInputPrm.FuncDI2;
    // LogicDI =  Prm->PnInputPrm.LogicDI2;
    
    
    // OldFuncDI = Axis->SequenceIO->DiSignal[1].FuncCfg;
    
    // if(Axis->SequenceIO->DiSignal[1].FuncCfg == FuncDI)
    // {
    //   return PRM_RSLT_SUCCESS;
    // }
    
    // if(Di_Allocate_Error(Axis->SequenceIO,FuncDI,OldFuncDI) == 0)
    // {
    //   SeqIo_DiCfg(Axis->SequenceIO,FuncDI,LogicDI,Item,DiNum);
    //   return PRM_RSLT_SUCCESS;
    // }
    // else 
    // {
    //   return PRM_RSLT_CONDITION_ERR;
    // }
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn402 : DI2 Logic Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_LogicSlt_DI2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	// PRMDATA	*Prm = Axis->Prm;
    // UINT16 FuncDI,LogicDI,DiNum,Item;
    
    // Item  = 1;
    // DiNum = 1;
    
    // FuncDI  =  Prm->PnInputPrm.FuncDI2;
    // LogicDI =  Prm->PnInputPrm.LogicDI2;
    
   
    // SeqIo_DiCfg(Axis->SequenceIO,FuncDI,LogicDI,Item,DiNum);

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn401 : DI3 Fuction Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_FuncSlt_DI3(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	// PRMDATA	*Prm = Axis->Prm;
    // UINT16 FuncDI,LogicDI,DiNum,Item,OldFuncDI;
    
    // Item  = 0;
    // DiNum = 2;
   
    // FuncDI  =  Prm->PnInputPrm.FuncDI3;
    // LogicDI =  Prm->PnInputPrm.LogicDI3;
    
    // OldFuncDI = Axis->SequenceIO->DiSignal[2].FuncCfg;
     
    
    // if(Axis->SequenceIO->DiSignal[2].FuncCfg == FuncDI)
    // {
    //   return PRM_RSLT_SUCCESS;
    // }
    
    // if(Di_Allocate_Error(Axis->SequenceIO,FuncDI,OldFuncDI) == 0)
    // {
    //   SeqIo_DiCfg(Axis->SequenceIO,FuncDI,LogicDI,Item,DiNum);
    //   return PRM_RSLT_SUCCESS;
    // }
    // else 
    // {
    //   return PRM_RSLT_CONDITION_ERR;
    // }
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn402 : DI3 Logic Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_LogicSlt_DI3(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	// PRMDATA	*Prm = Axis->Prm;
    // UINT16 FuncDI,LogicDI,DiNum,Item;
    
    // Item = 1;
    // DiNum = 2;
 
    // FuncDI  =  Prm->PnInputPrm.FuncDI3;
    // LogicDI =  Prm->PnInputPrm.LogicDI3;
    
    // SeqIo_DiCfg(Axis->SequenceIO,FuncDI,LogicDI,Item,DiNum);

	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************/
/*		 Pn401 : DI4 Fuction Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_FuncSlt_DI4(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
// 	PRMDATA	*Prm = Axis->Prm;
//     UINT16 FuncDI,LogicDI,DiNum,Item,OldFuncDI;
    
//     Item  = 0;
//     DiNum = 3;
//     FuncDI  =  Prm->PnInputPrm.FuncDI4;
//     LogicDI =  Prm->PnInputPrm.LogicDI4;
//     OldFuncDI = Axis->SequenceIO->DiSignal[3].FuncCfg;
    

//     if(Axis->SequenceIO->DiSignal[3].FuncCfg == FuncDI)
//     {
//       return PRM_RSLT_SUCCESS;
//     }
    
//     if(Di_Allocate_Error(Axis->SequenceIO,FuncDI,OldFuncDI) == 0)
//     {
//       SeqIo_DiCfg(Axis->SequenceIO,FuncDI,LogicDI,Item,DiNum);
//       return PRM_RSLT_SUCCESS;
//     }
//     else 
//     {
//       return PRM_RSLT_CONDITION_ERR;
//     }
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn402 : DI3 Logic Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_LogicSlt_DI4(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	// PRMDATA	*Prm = Axis->Prm;
    // UINT16 FuncDI,LogicDI,DiNum,Item;
    
    // Item  = 1;
    // DiNum = 3;
    // FuncDI  =  Prm->PnInputPrm.FuncDI4;
    // LogicDI =  Prm->PnInputPrm.LogicDI4;
    
     
    // SeqIo_DiCfg(Axis->SequenceIO,FuncDI,LogicDI,Item,DiNum);

	// return PRM_RSLT_SUCCESS;
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn401 : DO Fuction Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_FuncSlt_DO1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;

	Prm = Axis->Prm;

	// SeqIo_DoCfg(Axis->SequenceIO, Prm->PnOutputPrm.FuncDO1 , Prm->PnOutputPrm.LogicDO1, 0);
    // SeqIo_DoCfg(Axis->SequenceIO, Prm->PnOutputPrm.FuncDO2 , Prm->PnOutputPrm.LogicDO2, 1);
    // SeqIo_DoCfg(Axis->SequenceIO, Prm->PnOutputPrm.FuncDO3 , Prm->PnOutputPrm.LogicDO3, 2);
    // SeqIo_DoCfg(Axis->SequenceIO, Prm->PnOutputPrm.FuncDO4 , Prm->PnOutputPrm.LogicDO4, 3);
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn402 : DO Logic Select								        							*/
/****************************************************************************************************/
PRM_RSLT pncal_LogicSlt_DO1(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;

	Prm = Axis->Prm;
	
    // SeqIo_DoCfg(Axis->SequenceIO, Prm->PnOutputPrm.FuncDO1 , Prm->PnOutputPrm.LogicDO1, 0);
    // SeqIo_DoCfg(Axis->SequenceIO, Prm->PnOutputPrm.FuncDO2 , Prm->PnOutputPrm.LogicDO2, 1);
    // SeqIo_DoCfg(Axis->SequenceIO, Prm->PnOutputPrm.FuncDO3 , Prm->PnOutputPrm.LogicDO3, 2);
    // SeqIo_DoCfg(Axis->SequenceIO, Prm->PnOutputPrm.FuncDO4 , Prm->PnOutputPrm.LogicDO4, 3);

	return PRM_RSLT_SUCCESS;
}
/****************************************************************************************************/
/*		Pn601 : Vdc OV Level[V]													                    */
/****************************************************************************************************/
PRM_RSLT pncal_ovlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	CHECK_MAIN_POWER	*PowerChecker;
	
    if(Axis->AxisID == AXIS_1)
    {
		Prm = Axis->Prm;
		PowerChecker = &(Axis->PowMngr->MainPowChecker);

		PowerChecker->P.OvLevel = Prm->PnAlmCfgPrm.OverVoltValue;
    }
	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn602 : NonRegen Vdc OV Level[V]													        */
/****************************************************************************************************/
PRM_RSLT pncal_nonregenovlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	CHECK_MAIN_POWER	*PowerChecker;
	
    if(Axis->AxisID == AXIS_1)
    {
		Prm = Axis->Prm;
		PowerChecker = &(Axis->PowMngr->MainPowChecker);

		PowerChecker->P.NonRegOvLevel = Prm->PnAlmCfgPrm.NonRegOvLevel;
    }

	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************/
/*		Pn603 : Vdc Uv Level[V]													                    */
/****************************************************************************************************/
PRM_RSLT pncal_uvlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	CHECK_MAIN_POWER	*PowerChecker;
	
    if(Axis->AxisID == AXIS_1)
    {
		Prm = Axis->Prm;
		PowerChecker = &(Axis->PowMngr->MainPowChecker);

		PowerChecker->P.UvLevel = Prm->PnAlmCfgPrm.UnderVoltValue;
    }
	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn604 : Vdc Uv Filter[ms]													            */
/****************************************************************************************************/
PRM_RSLT pncal_uvfil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	CHECK_MAIN_POWER	*PowerChecker;
	
    if(Axis->AxisID == AXIS_1)
    {
		Prm = Axis->Prm;
		PowerChecker = &(Axis->PowMngr->MainPowChecker);

		PowerChecker->P.UvFilter = Prm->PnAlmCfgPrm.UnderVoltFil;
    }
	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn605 : Vdc Uv Warn Level[V]													            */
/****************************************************************************************************/
PRM_RSLT pncal_uvwrnlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	CHECK_MAIN_POWER	*PowerChecker;
	
    if(Axis->AxisID == AXIS_1)
    {
		Prm = Axis->Prm;
		PowerChecker = &(Axis->PowMngr->MainPowChecker);

		PowerChecker->P.UvWrnLevel = Prm->PnAlmCfgPrm.UvWrnValue;
    }
	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn606 : over speed level [rpm]													*/
/****************************************************************************************************/
PRM_RSLT pncal_ovrspdlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	UINT16 MotorMaxSpd = (REAL32)(Axis->Prm->PnMotPrm.MaxSpd) * 1.1f;

	if(Prm->PnAlmCfgPrm.OvrSpdValue > MotorMaxSpd)
	{
		Axis->CheckAlm->OvrSpd.OsAlmLvl = MotorMaxSpd*Axis->BaseLoops->Bprm->Kspdrpm;
	}
	else
	{
		Axis->CheckAlm->OvrSpd.OsAlmLvl = Prm->PnAlmCfgPrm.OvrSpdValue * Axis->BaseLoops->Bprm->Kspdrpm;
	}
	Axis->CheckAlm->OvrSpd.OSCountMax = 20;    //20ms   todo
		
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn607 : over position error warn level [%]													*/
/****************************************************************************************************/
PRM_RSLT pncal_ovrperwrnlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	Axis->CheckAlm->OvrPosErr.PerWrnLevel = Axis->CheckAlm->OvrPosErr.PerAlmLevel * 
		                                   Prm->PnAlmCfgPrm.OvrPerWrnLevel/100;
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn608 : over position error alarm level [pulse]												*/
/****************************************************************************************************/
PRM_RSLT pncal_ovrperalmlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	Axis->CheckAlm->OvrPosErr.PerAlmLevel = Prm->PnAlmCfgPrm.OvrPerAlmLevel;
	Axis->CheckAlm->OvrPosErr.PerWrnLevel = Axis->CheckAlm->OvrPosErr.PerAlmLevel * 
		                                   Prm->PnAlmCfgPrm.OvrPerWrnLevel/100;

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn609 : servo on over position error warn level [%]										    */
/****************************************************************************************************/
PRM_RSLT pncal_svonovrperwrnlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	Axis->CheckAlm->OvrPosErr.SvonPerWrnLevel = Axis->CheckAlm->OvrPosErr.SvonPerAlmLevel * 
											   Prm->PnAlmCfgPrm.SvonOvrPerWrnLevel/100;
	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		Pn60A : servo on over position error alarm level  [pulse]									*/
/****************************************************************************************************/
PRM_RSLT pncal_svonovrperalmlv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	Prm = Axis->Prm;

	Axis->CheckAlm->OvrPosErr.SvonPerAlmLevel = Prm->PnAlmCfgPrm.SvonOvrPerAlmLevel;
	Axis->CheckAlm->OvrPosErr.SvonPerWrnLevel = Axis->CheckAlm->OvrPosErr.SvonPerAlmLevel * 
											   Prm->PnAlmCfgPrm.SvonOvrPerWrnLevel/100;

	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		Pn60B : Over Run Detection Torque [%]									                    */
/****************************************************************************************************/
PRM_RSLT pncal_ovrruntrq(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	UINT32			work1;
	PRMDATA			*Prm;
	BPRMDAT			*Bprm;
	CHECK_OVRRUN	*pOvrRun;

	Prm = Axis->Prm;		
	Bprm = Axis->BaseLoops->Bprm;		
	pOvrRun = &(Axis->CheckAlm->OvrRun);
	work1 = Prm->PnAlmCfgPrm.OvrRunTrq;
	
	if ( work1 > (UINT32)Bprm->PerMaxTrq )
	{
		pOvrRun->OvrTrqLevel = 0x01000000;
	}
	else
	{

		pOvrRun->OvrTrqLevel = work1 * 0x01000000 / Bprm->PerMaxTrq;

	}
	

	pOvrRun->OvrSpdLevel = 0x01000000 / 60;


	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn60D : Alarm Mask									    */
/****************************************************************************************************/
PRM_RSLT pncal_AlmMask(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{	

	//warning set mask
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0001)
	{
		ALMSetMask(Axis->AlmMngr, WRN_OF, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_BEOF, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_BOOT, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_UV, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_EXREG, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_RGOLF, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_ENCBA, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_OLF, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_MIDT, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_VIB, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_POS, TRUE);
		ALMSetMask(Axis->AlmMngr, WRN_POSLIMIT, TRUE);
	}
	else
	{
		ALMSetMask(Axis->AlmMngr, WRN_OF, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_BEOF, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_BOOT, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_UV, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_EXREG, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_RGOLF, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_ENCBA, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_OLF, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_MIDT, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_VIB, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_POS, FALSE);
		ALMSetMask(Axis->AlmMngr, WRN_POSLIMIT, FALSE);
	}

	//encoder error
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0002)
	{
		ALMSetMask(Axis->AlmMngr, ALM_ENCOS, TRUE);  // encoder over speed
		ALMSetMask(Axis->AlmMngr, ALM_ENCCE, TRUE); // encoder counter error
		ALMSetMask(Axis->AlmMngr, ALM_ENCOF, TRUE); // encoder counter overflow
		ALMSetMask(Axis->AlmMngr, ALM_ENCOH, TRUE);  // encoder over heat
		ALMSetMask(Axis->AlmMngr, ALM_ENCME, TRUE); // encoder multi-turn error
		ALMSetMask(Axis->AlmMngr, ALM_ENCBE, TRUE); // encoder battery error				   
	}
	else
	{
		ALMSetMask(Axis->AlmMngr, ALM_ENCOS, FALSE);  // encoder over speed
		ALMSetMask(Axis->AlmMngr, ALM_ENCCE, FALSE); // encoder counter error
		ALMSetMask(Axis->AlmMngr, ALM_ENCOF, FALSE); // encoder counter overflow
		ALMSetMask(Axis->AlmMngr, ALM_ENCOH, FALSE);  // encoder over heat
		ALMSetMask(Axis->AlmMngr, ALM_ENCME, FALSE); // encoder multi-turn error
		ALMSetMask(Axis->AlmMngr, ALM_ENCBE, FALSE); // encoder battery error	

	}
	
	//position error
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0004)
	{
		ALMSetMask(Axis->AlmMngr, ALM_OF, TRUE);    // over position error
		ALMSetMask(Axis->AlmMngr, ALM_BEOF, TRUE); // over position error at servo on
		ALMSetMask(Axis->AlmMngr, ALM_BEVLMTOF, TRUE); // over position error at servo on when speed limit
		ALMSetMask(Axis->AlmMngr, WRN_OF, TRUE);    // over position warning
		ALMSetMask(Axis->AlmMngr, WRN_BEOF, TRUE); // over position warning at servo on
	}
	else
	{		
		ALMSetMask(Axis->AlmMngr, ALM_OF, FALSE);    // over position error
		ALMSetMask(Axis->AlmMngr, ALM_BEOF, FALSE); // over position error at servo on
		ALMSetMask(Axis->AlmMngr, ALM_BEVLMTOF, FALSE); // over position error at servo on when speed limit
		if((Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0001) == 0)
		{
			ALMSetMask(Axis->AlmMngr, WRN_OF, FALSE);    // over position warning
			ALMSetMask(Axis->AlmMngr, WRN_BEOF, FALSE); // over position warning at servo on
		}
	}
	
	//under voltage error
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0008)
	{
		ALMSetMask(Axis->AlmMngr, ALM_PUV, TRUE);    // under voltage
		ALMSetMask(Axis->AlmMngr, WRN_UV, TRUE); 
	}
	else
	{
		ALMSetMask(Axis->AlmMngr, ALM_PUV, FALSE);    // under voltage
		
		if((Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0001) == 0)
			ALMSetMask(Axis->AlmMngr, WRN_UV, FALSE); 
	}
	
	//overload
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0010)
	{
		ALMSetMask(Axis->AlmMngr, ALM_OLF1, TRUE); 
		ALMSetMask(Axis->AlmMngr, ALM_OLF2, TRUE); 
		ALMSetMask(Axis->AlmMngr, WRN_OLF, TRUE); 
	}
	else
	{
		ALMSetMask(Axis->AlmMngr, ALM_OLF1, FALSE); 
		ALMSetMask(Axis->AlmMngr, ALM_OLF2, FALSE); 
		
		if((Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0001) == 0)
			ALMSetMask(Axis->AlmMngr, WRN_OLF, FALSE); 		
	}

	//Motor Stall
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0020)
	{
		ALMSetMask(Axis->AlmMngr, ALM_STALL, TRUE); 
	}
	else
	{
		ALMSetMask(Axis->AlmMngr, ALM_STALL, FALSE); 
	}
	
	//Motor Over Run
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0040)
	{
		ALMSetMask(Axis->AlmMngr, ALM_ORUN, TRUE); 
	}
	else
	{
		ALMSetMask(Axis->AlmMngr, ALM_ORUN, FALSE); 
	}

	//Motor Over Speed
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0080)
	{
		ALMSetMask(Axis->AlmMngr, ALM_OS, TRUE); 
	}
	else
	{
		ALMSetMask(Axis->AlmMngr, ALM_OS, FALSE); 
	}
        
        
        //Temp Err
	if(Axis->Prm->PnAlmCfgPrm.AlmMask & 0x0100)
	{
		ALMSetMask(Axis->AlmMngr, ALM_IPM_OTM, TRUE); 
		ALMSetMask(Axis->AlmMngr, ALM_FAN_TEMP, TRUE);
	}
	else
	{
		ALMSetMask(Axis->AlmMngr, ALM_IPM_OTM, FALSE); 
		ALMSetMask(Axis->AlmMngr, ALM_FAN_TEMP, FALSE);
	}
	
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn60E : Vibration detection sensitivity									                */
/****************************************************************************************************/
PRM_RSLT pncal_VibDetSens(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA *Prm;
	BPRMDAT	*Bprm;
		
	#if NO_ADVFUNC
	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

	if( (Prm->PnMotPrm.MotType&0xFF) == MOTTYPE_ROTARY )
	{ 
		Axis->DetVib->P.VibCheckLevel =
			         (REAL32)Prm->PnAlmCfgPrm.VibChkSpd * Axis->BaseLoops->Bprm->Kspdrpm
					 *3.0f *(REAL32) Prm->PnAlmCfgPrm.VibChkSens / 100.0f;

		DetVibResetMaxMinSpeedError( Axis->DetVib );
	}

	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn60F : Vibration detection value									                        */
/****************************************************************************************************/
PRM_RSLT pncal_VibChkSpd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA *Prm;
	BPRMDAT	*Bprm;
		
	#if NO_ADVFUNC
	Prm = Axis->Prm;
	Bprm = Axis->BaseLoops->Bprm;

	if( (Prm->PnMotPrm.MotType&0xFF) == MOTTYPE_ROTARY )
	{ 
		Axis->DetVib->P.VibCheckLevel =
			         (REAL32)Prm->PnAlmCfgPrm.VibChkSpd * Axis->BaseLoops->Bprm->Kspdrpm
					 *3.0f *(REAL32) Prm->PnAlmCfgPrm.VibChkSens / 100.0f;

		DetVibResetMaxMinSpeedError( Axis->DetVib );
	}

	#endif
	return PRM_RSLT_SUCCESS;


}

/****************************************************************************************************/
/*		 Pn610 : Low Frequency Vibration detection value							                */
/****************************************************************************************************/
PRM_RSLT pncal_JstVibChkSpd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA *Prm;
	Prm = Axis->Prm;

	#if NO_ADVFUNC
	if( (Prm->PnMotPrm.MotType&0xFF) == MOTTYPE_ROTARY )
	{ 
		Axis->DetVib->P.JstVibCheckLevel =
						(REAL32)Prm->PnAlmCfgPrm.JstVibChkSpd* Axis->BaseLoops->Bprm->Kspdrpm;

		Axis->DetVib->P.JstVibCheckLevel *= 8.0f;
	}

	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn611 : Vibration detection switch							                                */
/****************************************************************************************************/
PRM_RSLT pncal_VibDetSw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	#if NO_ADVFUNC
	Axis->DetVib->P.VibCheckSel = Axis->Prm->PnAlmCfgPrm.VibDetSw & 0x000F;
	#endif	
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn612 : Remain vibration detection width							                        */
/****************************************************************************************************/
PRM_RSLT pncal_remdetw(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	BPRMDAT	*Bprm;
	#if NO_ADVFUNC
	
	Prm = Axis->Prm;

	RemVibCalculateMonitorPrm( Axis->RemVibFreq,
	                           Prm->PnAlmCfgPrm.RemVibDetW,
	                           Prm->PnAlmCfgPrm.PosCoinLvl,
	                           PS_CYCLEUS );
	#endif

	return PRM_RSLT_SUCCESS;

}
/****************************************************************************************************/
/*		 Pn613 : Position completion width  							                            */
/****************************************************************************************************/
PRM_RSLT pncal_coinlvl(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;
	BPRMDAT	*Bprm;

	#if NO_ADVFUNC
	Prm = Axis->Prm;

	Axis->BaseCtrls->PosManager.conf.CoinLevel = Prm->PnAlmCfgPrm.PosCoinLvl;
	
	RemVibCalculateMonitorPrm( Axis->RemVibFreq,
							   Prm->PnAlmCfgPrm.RemVibDetW,
							   Prm->PnAlmCfgPrm.PosCoinLvl,
							   PS_CYCLEUS );

	Axis->BaseCtrls->SettlingTime.OvsErrDetLvl = Prm->PnAlmCfgPrm.PosCoinLvl;
//	StlgCalculateOverShootErrLevel( &(Axis->BaseCtrls->SettlingTime), 
//		                             Prm->PnAlmCfgPrm.PosCoinLvl, 100 );

	#endif
	return PRM_RSLT_SUCCESS;

}

/****************************************************************************************************/
/*		 Pn614 : Approach signal range							                                    */
/****************************************************************************************************/
PRM_RSLT pncal_nearlvl(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRMDATA	*Prm;

	Prm = Axis->Prm;

	Axis->BaseCtrls->PosManager.conf.NearLevel = Prm->PnAlmCfgPrm.PosNearLvl;

	return PRM_RSLT_SUCCESS;

}
/****************************************************************************************************/
/*		 Pn701 : clear alarm 											                            */
/****************************************************************************************************/
PRM_RSLT pnexe_SysPrmInit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
  AUX_PRM         	*AuxPrm     = &Axis->Prm->PnAuxPrm;
  
  if(Axis->AxisID == 0)
  {
    if(AuxPrm->SysPrmInit == 0x5A)
    {
      Axis[1].Prm->PnAuxPrm.SysPrmInit = 0x5A;
    }
  }

  return PRM_RSLT_SUCCESS;

}
/****************************************************************************************************/
/*		 Pn703 : clear alarm 											                            */
/****************************************************************************************************/
PRM_RSLT pnexe_clearalarm(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRM_RSLT			rslt;
	AUX_PRM         	*AuxPrm     = &Axis->Prm->PnAuxPrm;
	ALARM				*AlmMngr    = Axis->AlmMngr;
	BASE_CTRL			*BaseCtrls  = Axis->BaseLoops->BaseCtrls;

	
	if(AuxPrm->FaultRst)
	{
		BaseCtrls->Cia402Axis.AlmRstCmd = 1;
	}
	
	return PRM_RSLT_NEEDRESET;
}

/****************************************************************************************************/
/*		 Pn704 : reset alarm log											                        */
/****************************************************************************************************/
PRM_RSLT pnexe_resetlog(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRM_RSLT	rslt;

    ALMSetAlarmHistResetRequest(Axis->AlmMngr);

	return PRM_RSLT_NEEDRESET;
}

/****************************************************************************************************/
/*		 Pn705 : Motor Parameter Identification											    */
/****************************************************************************************************/
PRM_RSLT pnexe_MotIdent(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRM_RSLT	rslt;

	rslt = (PRM_RSLT)PnCmdMotorIdent(Axis);

	return rslt;
}
/****************************************************************************************************/
/*		 Pn714 : Clamping Jaw Init											*/
/****************************************************************************************************/
PRM_RSLT pnexe_ClampingJawInit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    PRM_RSLT	rslt;
    // rslt = PnCmdClampingJaw( Axis );
    
    return PRM_RSLT_SUCCESS;
    
}

/****************************************************************************************************/
/*		 Pn708 : Function generator start											                */
/****************************************************************************************************/
PRM_RSLT pnexe_fungenstrt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	CTRL_CFG_PRM    *CtrlCfgPrm = &Axis->Prm->PnCtrlCfgPrm;
	FG_MODE_CTRL	*FgCtrl = &Axis->BaseCtrls->Cia402Axis.FG_Ctrl ;

	if(!FgCtrl->V.FgAct)
	{
		FgCtrl->P.FgType = CtrlCfgPrm->FunGenType;
		FgCtrl->P.FgAmp = CtrlCfgPrm->FunGenAmp;
        FgCtrl->P.Slope = CtrlCfgPrm->FunGenSlope;
		FgCtrl->P.PeriodNum = TASKB_FRQ*10/CtrlCfgPrm->FunGenFrq;
		FgCtrl->P.TotalNum = FgCtrl->P.PeriodNum * CtrlCfgPrm->FunGenNum;
        FgCtrl->V.InitPos = Axis->Prm->PnCia402Prm.PositionActualValue0x6064;
        FgCtrl->V.OutputRef = 0;
		FgCtrl->V.Cnt = 0;
        FgCtrl->V.LinearDir = 0;
	}
	FgCtrl->V.FgAct = Axis->Prm->PnAuxPrm.FunGenStart;

	return PRM_RSLT_NEEDRESET;
}
/****************************************************************************************************/
/*		 Pn709 : Fn Servo Off											    */
/****************************************************************************************************/
PRM_RSLT pnexe_FnSvoOff(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRM_RSLT	rslt;

	Axis->FnCmnCtrl->FnSvonReq = FALSE;

	return PRM_RSLT_NEEDRESET;
}


/****************************************************************************************************/
/*		 Pn70A : Jog Command											    */
/****************************************************************************************************/
PRM_RSLT pnexe_PrgJogCmd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRM_RSLT	rslt;

	rslt = (PRM_RSLT)PnCmdPrgJog( Axis );

	return rslt;
}


/****************************************************************************************************/
/*		 Pn70B : Jog Command											    */
/****************************************************************************************************/
PRM_RSLT pnexe_JogCmd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRM_RSLT	rslt;

	rslt = (PRM_RSLT)PnCmdJog( Axis );

	return rslt;
}

/****************************************************************************************************/
/*		 Pn70A : Motor Paramter Identification start											    */
/****************************************************************************************************/
PRM_RSLT pnexe_mpistrt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    MOTORPARAM_FIND  *MotorParamIdent = &Axis->MotorIdentify->MotorParamIdent;

    if( (Axis->FnCmnCtrl->FnCtrlMcmd == CTRL_MODE_MOTORIdent)
									&& (MotorParamIdent->V.FPStart == 1) )
	{
		return( PRM_RSLT_CONDITION_ERR );		
	}
    else
    {
        MotorParamIdent->P.FPCur = Axis->BaseLoops->Bprm->RatCur;
    }    

	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn70B : Motor Inertia Identification start											    */
/****************************************************************************************************/
PRM_RSLT pnexe_miistrt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    Inertia_FIND  *InertiaIdent = &Axis->MotorIdentify->InertiaIdent;

   	if( (Axis->FnCmnCtrl->FnCtrlMcmd == CTRL_MODE_INERTIALDENT)
									&& (InertiaIdent->V.StartFlag == 1) )
	{
		return( PRM_RSLT_CONDITION_ERR );		
	}
	else
	{ 				
        InertiaIdent->P.Kt = Axis->Prm->PnMotPrm.MotKt;
        InertiaIdent->P.MotInertia = Axis->Prm->PnMotPrm.Jmot;
        InertiaIdent->P.AcceleTime = Axis->Prm->PnCtrlCfgPrm.InertiaAccTime;
        InertiaIdent->P.Position = Axis->Prm->PnCtrlCfgPrm.InertiaPosition;
        
        InertiaIdent->P.SpeedMax = ((float)InertiaIdent->P.Position/(float)InertiaIdent->P.AcceleTime) * 1000.0f;       
        InertiaIdent->P.Acc = ((float)InertiaIdent->P.SpeedMax/(float)InertiaIdent->P.AcceleTime) *1000.0f;
        
        if(InertiaIdent->P.SpeedMax > Axis->Prm->PnCia402Prm.Max_profile_velocity0x607F)
        {
            InertiaIdent->P.SpeedMax = Axis->Prm->PnCia402Prm.Max_profile_velocity0x607F;             
            InertiaIdent->P.AcceleTime = (float)InertiaIdent->P.SpeedMax/(float)InertiaIdent->P.Acc * 1000.0f;
        }
        if(InertiaIdent->P.Acc > Axis->Prm->PnCia402Prm.Max_deceleration0x60C6
                    || InertiaIdent->P.Acc > Axis->Prm->PnCia402Prm.Max_acceleration0x60C5)
        {
            InertiaIdent->P.Acc = MlibLimitu32(Axis->Prm->PnCia402Prm.Max_deceleration0x60C6, 
                                        Axis->Prm->PnCia402Prm.Max_acceleration0x60C5);            
            InertiaIdent->P.AcceleTime = (float)InertiaIdent->P.SpeedMax/(float)InertiaIdent->P.Acc * 1000.0f;
        }
        
        InertiaIdent->V.RunTime = InertiaIdent->P.AcceleTime * (UINT32)TASKB_FRQ *0.001f;
        
        return PRM_RSLT_SUCCESS;
    }  
}
/****************************************************************************************************/
/*												    */
/****************************************************************************************************/
PRM_RSLT pnexe_enctype(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    MOTOR_IDENTIFY *MotIdt = Axis->MotorIdentify;
      if(!Axis->BaseLoops->UseEncoder2)
    {
   	if((gSystemInitDone == 1) && ((Axis->Prm->PnMotPrm.EncType != ENC_TYPE_INCREMENT) && (Axis->Prm->PnMotPrm.MotType == MOTTYPE_ROTARY)))
	{
        INT32 EncPPR = 1L << Axis->Prm->PnMotPrm.AbsEncBitS;
        
        Axis->Prm->PnCia402Prm.Max_profile_velocity0x607F = EncPPR * 65;
        Axis->Prm->PnCia402Prm.Max_deceleration0x60C6 = EncPPR* 500;
        Axis->Prm->PnCia402Prm.Max_acceleration0x60C5 = EncPPR* 500;
        Axis->Prm->PnAlmCfgPrm.OvrPerAlmLevel = EncPPR * 10;
	}
	else if((gSystemInitDone == 1) && ((Axis->Prm->PnMotPrm.EncType == ENC_TYPE_INCREMENT)
                                           ||(Axis->Prm->PnMotPrm.MotType == MOTTYPE_LINEAR)
                                           ||(Axis->Prm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)))
    {
        INT32 EncPPR = Axis->Prm->PnMotPrm.AbzEncPPR;
        
        Axis->Prm->PnCia402Prm.Max_profile_velocity0x607F = EncPPR * 65;
        Axis->Prm->PnCia402Prm.Max_deceleration0x60C6 = EncPPR* 500;
        Axis->Prm->PnCia402Prm.Max_acceleration0x60C5 = EncPPR* 500;
        Axis->Prm->PnAlmCfgPrm.OvrPerAlmLevel = EncPPR * 10;
    }
    
    if(Axis->Prm->PnMotPrm.EncType == ENC_TYPE_INCREMENT)
    {
      MotIdt->FhaseFind.P.FFMethod = FF_METHOD_FIRST_EN;
    }
    
    if((Axis->Prm->PnMotPrm.MotType != MOTTYPE_ROTARY) && (Axis->Prm->PnMotPrm.MotType != MOTTYPE_LINEAR)&& (Axis->Prm->PnMotPrm.MotType != MOTTYPE_VOICECOIL))
    {
      Axis->Prm->PnMotPrm.MotType  = MOTTYPE_ROTARY;
      
      return PRM_RSLT_CONDITION_ERR;
    }
    }
        
     return PRM_RSLT_SUCCESS;
      
}

/****************************************************************************************************/
/*												    */
/****************************************************************************************************/
PRM_RSLT pnexe_useenc2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    ENCODER *EncV = Axis->BaseLoops->Enc; 
    ENCODER *EncV2 = Axis->BaseLoops->Enc2;
    REAL32 MaxAccPerEncTime;
    UINT32 EncPPR;	
    REAL32 MaxAcc = (REAL32)Axis->Prm->PnCia402Prm.Max_acceleration0x60C5;

    Axis->BaseLoops->UseEncoder2 = Axis->Prm->PnMotPrm.UseEncoder2;

    
    if(Axis->BaseLoops->UseEncoder2)
    {
      
      if((gSystemInitDone == 1) && ((Axis->Prm->PnMotPrm.EncType2 != ENC_TYPE_INCREMENT) && (Axis->Prm->PnMotPrm.MotType == MOTTYPE_ROTARY)))
      {
          EncPPR = 1L << Axis->Prm->PnMotPrm.AbsEncBitS2;
          
          Axis->Prm->PnCia402Prm.Max_profile_velocity0x607F = EncPPR * 65;
          Axis->Prm->PnCia402Prm.Max_deceleration0x60C6 = EncPPR* 500;
          Axis->Prm->PnCia402Prm.Max_acceleration0x60C5 = EncPPR* 500;
          Axis->Prm->PnAlmCfgPrm.OvrPerAlmLevel = EncPPR * 10;
          }
          else if((gSystemInitDone == 1) && ((Axis->Prm->PnMotPrm.EncType2 == ENC_TYPE_INCREMENT)
		  											||(Axis->Prm->PnMotPrm.MotType == MOTTYPE_LINEAR)
															||(Axis->Prm->PnMotPrm.MotType == MOTTYPE_VOICECOIL)))
      {
         	EncPPR = Axis->Prm->PnMotPrm.AbzEncPPR2;
          
          Axis->Prm->PnCia402Prm.Max_profile_velocity0x607F = EncPPR * 65;
          Axis->Prm->PnCia402Prm.Max_deceleration0x60C6 = EncPPR* 500;
          Axis->Prm->PnCia402Prm.Max_acceleration0x60C5 = EncPPR* 500;
          Axis->Prm->PnAlmCfgPrm.OvrPerAlmLevel = EncPPR * 10;
      }
      
      if((Axis->Prm->PnMotPrm.MotType != MOTTYPE_ROTARY) && (Axis->Prm->PnMotPrm.MotType != MOTTYPE_LINEAR)&& (Axis->Prm->PnMotPrm.MotType != MOTTYPE_VOICECOIL))
      {
        Axis->Prm->PnMotPrm.MotType  = MOTTYPE_ROTARY;
        
        return PRM_RSLT_CONDITION_ERR;
      }
    }

    return PRM_RSLT_SUCCESS;
      
}
/****************************************************************************************************/
/*												    */
/****************************************************************************************************/
PRM_RSLT pncal_EncAbnormalValue(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    ENCODER *EncV = Axis->BaseLoops->Enc; 
    ENCODER *EncV2 = Axis->BaseLoops->Enc2;
    
    if(Axis->Prm->PnAlmCfgPrm.EncAbnormalValue == 0 || Axis->Prm->PnAlmCfgPrm.EncAbnormalValue == 0xFFFF)
        Axis->Prm->PnAlmCfgPrm.EncAbnormalValue = 1500;
    
    EncV->P.EncMaxPluseInc =  Axis->Prm->PnAlmCfgPrm.EncAbnormalValue;  	
    EncV2->P.EncMaxPluseInc = GearRatioInvCal(Axis->BaseLoops,EncV->P.EncMaxPluseInc);	

    return PRM_RSLT_SUCCESS;
      
}


/****************************************************************************************************/
/*												    */
/****************************************************************************************************/
PRM_RSLT pnexe_encconfig(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{


//    if(Axis->Prm->PnMotPrm.EncConfig&0xFF)
//    {
//       Axis->BaseLoops->Enc->P.EncSoftBitS =  Axis->Prm->PnMotPrm.EncConfig&0xFF;
//    }
//    else
//    {
//      Axis->BaseLoops->Enc->P.EncSoftBitS = Axis->BaseLoops->Enc->P.EncBitS;
//    }
//    
//    Axis->BaseLoops->Enc->P.EncExeNum =  (Axis->Prm->PnMotPrm.EncConfig>>8)&0xFF;
//    
//    if(Axis->BaseLoops->Enc->P.EncSoftBitS < Axis->BaseLoops->Enc->P.EncBitS)
//    {
//      Axis->BaseLoops->Enc->P.EncSoftBitSwNum = Axis->BaseLoops->Enc->P.EncBitS - Axis->BaseLoops->Enc->P.EncSoftBitS;
//    }
//    else
//    {
//      Axis->BaseLoops->Enc->P.EncSoftBitSwNum = 0;
//    }
        
     return PRM_RSLT_SUCCESS;
      
}

PRM_RSLT pnexe_encconfig2(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{


//    if(Axis->Prm->PnMotPrm.EncConfig2&0xFF)
//    {
//       Axis->BaseLoops->Enc2->P.EncSoftBitS =  Axis->Prm->PnMotPrm.EncConfig2&0xFF;
//    }
//    else
//    {
//      Axis->BaseLoops->Enc2->P.EncSoftBitS = Axis->BaseLoops->Enc2->P.EncBitS;
//    }
//    
//  
//     Axis->BaseLoops->Enc2->P.EncExeNum =  (Axis->Prm->PnMotPrm.EncConfig2>>8)&0xFF;
//
//    
//    if(Axis->BaseLoops->Enc2->P.EncSoftBitS < Axis->BaseLoops->Enc2->P.EncBitS)
//    {
//      Axis->BaseLoops->Enc2->P.EncSoftBitSwNum = Axis->BaseLoops->Enc2->P.EncBitS - Axis->BaseLoops->Enc2->P.EncSoftBitS;
//    }
//    else
//    {
//      Axis->BaseLoops->Enc2->P.EncSoftBitSwNum = 0;
//    }
    
        
     return PRM_RSLT_SUCCESS;
      
}
/****************************************************************************************************/
/*		 											    */
/****************************************************************************************************/
PRM_RSLT pnexe_singleturn(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    INT32 EncPPR = 1L << Axis->Prm->PnMotPrm.AbsEncBitS;

   	if((gSystemInitDone == 1) & (Axis->Prm->PnMotPrm.EncType != ENC_TYPE_INCREMENT))
	{
            Axis->Prm->PnCia402Prm.Max_profile_velocity0x607F = EncPPR * 65;
            Axis->Prm->PnCia402Prm.Max_deceleration0x60C6 = EncPPR* 500;
            Axis->Prm->PnCia402Prm.Max_acceleration0x60C5 = EncPPR* 500;
            Axis->Prm->PnAlmCfgPrm.OvrPerAlmLevel = EncPPR * 10;
	}
	
        
     return PRM_RSLT_SUCCESS;
      
}


/****************************************************************************************************/
/*		 Pn016 :											    */
/****************************************************************************************************/
PRM_RSLT pnexe_abzenc(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    INT32 EncPPR = Axis->Prm->PnMotPrm.AbzEncPPR;

   	if((gSystemInitDone == 1) & (Axis->Prm->PnMotPrm.EncType == ENC_TYPE_INCREMENT))
	{
        Axis->Prm->PnCia402Prm.Max_profile_velocity0x607F = EncPPR * 65;
        Axis->Prm->PnCia402Prm.Max_deceleration0x60C6 = EncPPR* 500;
        Axis->Prm->PnCia402Prm.Max_acceleration0x60C5 = EncPPR* 500;
        Axis->Prm->PnAlmCfgPrm.OvrPerAlmLevel = EncPPR * 10;
	}
	
        
     return PRM_RSLT_SUCCESS;
      
}
/****************************************************************************************************/
/*		 Pn70C : Motor Inertia online Identification start											*/
/****************************************************************************************************/
PRM_RSLT pnexe_miiolstrt(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    Inertia_FIND_OL  *InertiaIdentOL = &Axis->MotorIdentify->InertiaIdentOL;

    if(!InertiaIdentOL->V.StartFlag)
    {
		InertiaIdentOL->P.TimeThreshold = 4000; //1s      
        InertiaIdentOL->P.Kt = Axis->Prm->PnMotPrm.MotKt;
        InertiaIdentOL->P.MotInertia = Axis->Prm->PnMotPrm.Jmot;
        InertiaIdentOL->P.ForgetFactor = 0.97f;
        InertiaIdentOL->V.Pn1[0] = 1000000;
        InertiaIdentOL->V.Pn1[1] = 0;
        InertiaIdentOL->V.Pn1[2] = 0;
        InertiaIdentOL->V.Pn1[3] = 1000000;
        InertiaIdentOL->V.theta1[0] = 0;
        InertiaIdentOL->V.theta1[1] = 0;
        
        InertiaIdentOL->V.IdentState = 0;
        InertiaIdentOL->V.Inertia = InertiaIdentOL->P.MotInertia * (Axis->Prm->PnCtrlCfgPrm.Jrat*0.01f+1.0f);
        
        if(InertiaIdentOL->V.Inertia == 0)
        {
            InertiaIdentOL->V.Inertia = Axis->Prm->PnMotPrm.Jmot;
        }        
    }
    InertiaIdentOL->V.StartFlag = Axis->Prm->PnAuxPrm.MIIOLStart;     
    return PRM_RSLT_SUCCESS;

}
/****************************************************************************************************/
/*		 Pn70D : Time Stamp											                        */
/****************************************************************************************************/
PRM_RSLT pnexe_TimeStamp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    if(Axis->AxisID == AXIS_1)
    {
                
       Axis->AlmMngr->AlmTmStampRst = TRUE;
    }
    
	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn70D : Get encoder cmd											*/
/****************************************************************************************************/
PRM_RSLT pnexe_enccmd(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    ENCODER  *pEnc = Axis->BaseLoops->Enc;
    
    if( Axis->Prm->PnAuxPrm.EncCmd && !Axis->BaseCtrls->BaseEnable)
    {
        if(pEnc->V.ExCmdEn == 0)
        {
            pEnc->V.ExCmdEn = Axis->Prm->PnAuxPrm.EncCmd;
        }
        return PRM_RSLT_SUCCESS;
    }
    else if(Axis->BaseCtrls->BaseEnable)
    {
        pEnc->V.ExCmdEn = 0;
        return PRM_RSLT_RWACC_ERR;
    }
    
    pEnc->V.ExCmdEn = 0;
    
    return PRM_RSLT_SUCCESS;
    
}
/****************************************************************************************************/
/*		 PnF0F : FollowingErrorWindow0x6065*/
/****************************************************************************************************/
PRM_RSLT pnexe_follerrwin(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA	*Prm;
	Prm = Axis->Prm;

	Axis->CheckAlm->PosFollowErr.FollowAlmLevel = Prm->PnCia402Prm.FollowingErrorWindow0x6065;
	
	return PRM_RSLT_SUCCESS;
}
/****************************************************************************************************/
/*		 PnF10 : FollowingErrorTimeout0x6066										*/
/****************************************************************************************************/
PRM_RSLT pnexe_follerrtime(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA	*Prm;
	Prm = Axis->Prm;

	Axis->CheckAlm->PosFollowErr.FollowAlmTime = Prm->PnCia402Prm.FollowingErrorTimeout0x6066 ;
	
	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************/
/*		 Pn129 : Dev communication identification										    */
/****************************************************************************************************/
PRM_RSLT pnexe_DevID(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
  
//  DeviceSerial = Axis->Prm->PnCfgPrm.DevID;
  
  return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn70E : Dev communication identification config											*/
/****************************************************************************************************/
PRM_RSLT pnexe_devidconfig(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    UINT8 DevComId = (UINT8)(Axis->Prm->PnAuxPrm.DevIdConfig & 0xFF);
    Axis->Prm->PnCfgPrm.DevID = DevComId;
    
    return PRM_RSLT_SUCCESS;
    
}

/****************************************************************************************************/
/*		 Pn223 : Phase Find Current */		
/****************************************************************************************************/
PRM_RSLT pncal_phfind(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    BPRMDAT *Bprm = Axis->BaseLoops->Bprm;
    PRMDATA *PnPrm = Axis->Prm;
    MOTOR_IDENTIFY *MotIdt = Axis->MotorIdentify;
    
    MotIdt->FhaseFind.P.FFMethod = PnPrm->PnCtrlCfgPrm.FFMethod;
    if(PnPrm->PnCtrlCfgPrm.FFCur == 0)
    {
      MotIdt->FhaseFind.P.FFCur           = (REAL32)PnPrm->PnMotPrm.RatCur*1.414/10.0f;
    }
    else
    {
      MotIdt->FhaseFind.P.FFCur           = (REAL32)PnPrm->PnCtrlCfgPrm.FFCur*1.414/10.0f;
    }
    MotIdt->FhaseFind.P.FFVolt = MotIdt->FhaseFind.P.FFCur * Bprm->MotR/UDC_LEVEL; 
    MotIdt->FhaseFind.P.FFStableTime = (UINT32)(PnPrm->PnCtrlCfgPrm.FFStableTime*1000.0f/(REAL32)TASKC_CYCLEUS);
    
    REAL32 RampDelta = PnPrm->PnCtrlCfgPrm.FFRampTime *1000.0f/(REAL32)TASKC_CYCLEUS;
    MotIdt->FhaseFind.P.FFRampDelta = MotIdt->FhaseFind.P.FFVolt/(REAL32)RampDelta;
    
    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*		 Pn262 :  Motor parameters indentify current */		
/****************************************************************************************************/
PRM_RSLT pncal_MotIdenCur(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA *PnPrm = Axis->Prm;
    MOTOR_IDENTIFY *MotIdt = Axis->MotorIdentify;
    
    if(PnPrm->PnCtrlCfgPrm.MotIdentCur == 0)
    {
      MotIdt->MotorParamIdent.P.FPLCurRef = (REAL32)PnPrm->PnMotPrm.RatCur*1.414/10.0f;
    }
    else 
    {
      if(PnPrm->PnCtrlCfgPrm.MotIdentCur >=  1.5*PnPrm->PnMotPrm.RatCur)
      {
        MotIdt->MotorParamIdent.P.FPLCurRef = (REAL32)PnPrm->PnMotPrm.RatCur*1.414/10.0f;
      } 
      else
      {
        MotIdt->MotorParamIdent.P.FPLCurRef = (REAL32)PnPrm->PnCtrlCfgPrm.MotIdentCur*1.414/10.0f;
      }
    }  
    
    return PRM_RSLT_SUCCESS;
}
/****************************************************************************************************/
/*		 Pn262 :  Motor parameters indentify current */		
/****************************************************************************************************/
PRM_RSLT pncal_gearatio(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
  if(!Axis->BaseCtrls->BaseEnable)
  {
    Axis->BaseCtrls->Cia402Axis.MotorRev = Axis->Prm->PnCia402Prm.MotorRevolutions0x6091;
    Axis->BaseCtrls->Cia402Axis.LoadShaftRev = Axis->Prm->PnCia402Prm.LoadShaftRevolutions0x6091;
    
    Axis->BaseLoops->Bprm->MotorRev =  Axis->Prm->PnCia402Prm.MotorRevolutions0x6091;
    Axis->BaseLoops->Bprm->LoadShaftRev = Axis->Prm->PnCia402Prm.LoadShaftRevolutions0x6091;
    return PRM_RSLT_SUCCESS;
  }
  else
  {
    return PRM_RSLT_RWACC_ERR;
  }
}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_PosRefFil(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    PRMDATA *Prm;
    Prm = Axis->Prm;
    
    PcalBasePosRefLpassFilter(Axis->BaseCtrls,Prm->PnCtrlCfgPrm.PosRefFil);

    return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_PosStiff(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

  PRMDATA *Prm;
  Prm = Axis->Prm;

  Axis->BaseCtrls->CtrlCmdPrm.Kposstiff = Prm->PnCtrlCfgPrm.PosStiff*0.01;  // Nm -> 2^24

  return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_SpdStiff(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
  PRMDATA *Prm;
  Prm = Axis->Prm;
  Axis->BaseCtrls->CtrlCmdPrm.Kspdstiff = Prm->PnCtrlCfgPrm.SpdStiff*0.01;

  return PRM_RSLT_SUCCESS;	
}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_CalHumanPosKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    // 位置转换系数-将位置指令rad 转换成减速机端 cnt
    REAL32 Span = 0.0;
    REAL32 tmp = 0.0;
	PRM_RSLT	    rslt;
    PRM_ATTR        *PrmAttr = NULL;
    REGIF_CONFIG_T  *RegCfg = Axis->RegManager->hPnReg;

    HumanRotCtrl.Prm.Max_Postion = Axis->Prm->PnCustomPrm.Max_Postion*0.01f;
    HumanRotCtrl.Prm.Min_Postion = Axis->Prm->PnCustomPrm.Min_Postion*0.01f;

    Span = (REAL32)(HumanRotCtrl.Prm.Max_Postion - HumanRotCtrl.Prm.Min_Postion);

    HumanRotCtrl.Prm.PosiontQ2Rad  = Span/65535.0f;   


    if(Axis->Prm->PnMotPrm.UseEncoder2 != 0)
    {
        HumanRotCtrl.Prm.PosiontQ2Cnt = (HumanRotCtrl.Prm.PosiontQ2Rad/C2_PI)*Axis->BaseLoops->Bprm->FbPulse1;

    }
    else
    {
        HumanRotCtrl.Prm.PosiontQ2Cnt =  (HumanRotCtrl.Prm.PosiontQ2Rad/C2_PI)*Axis->BaseLoops->Bprm->FbPulse; 	
    }

    UINT32 CoeIndex = 0x2010003C;
    PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
    rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);

    if(PRM_RSLT_SUCCESS != rslt)
    {
//        ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
    }	

    CoeIndex = 0x2010003D;
    PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
    rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);

    if(PRM_RSLT_SUCCESS != rslt)
    {
//        ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
    }
			
	
  	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_CalHumanVelKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    // 位置转换系数-将位置指令rad 转换成减速机端 cnt
    REAL32 Span = 0.0;
    REAL32 tmp = 0.0;
	PRM_RSLT	    rslt;
    PRM_ATTR        *PrmAttr = NULL;
    REGIF_CONFIG_T  *RegCfg = Axis->RegManager->hPnReg;

    HumanRotCtrl.Prm.Max_Velocity = Axis->Prm->PnCustomPrm.Max_Velocity*0.01f;
	HumanRotCtrl.Prm.Min_Velocity = -HumanRotCtrl.Prm.Max_Velocity;
    // 速度转换系数-将速度指令rad/s 转换成减速机端速度 cnt/s
	Span = (REAL32)HumanRotCtrl.Prm.Max_Velocity*2.0f;
	HumanRotCtrl.Prm.VelocityQ2Rads = Span/65535.0f;


    // if(Axis->Prm->PnMotPrm.UseEncoder2 != 0)
	// {
	// 	HumanRotCtrl.Prm.VelocityQ2Cnts = (HumanRotCtrl.Prm.VelocityQ2Rads/C2_PI)*Axis->BaseLoops->Bprm->FbPulse1;
	// }
	// else
	// {
		HumanRotCtrl.Prm.VelocityQ2Cnts = (HumanRotCtrl.Prm.VelocityQ2Rads/C2_PI)*Axis->BaseLoops->Bprm->FbPulse;
	// }

    UINT32 CoeIndex = 0x2010003E;
    PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
    rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);

    if(PRM_RSLT_SUCCESS != rslt)
    {
//        ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
    }
		
  	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_CalHumanTrqKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

    // 位置转换系数-将位置指令rad 转换成减速机端 cnt
    REAL32 Span = 0.0;
    REAL32 tmp = 0.0;
	PRM_RSLT	    rslt;
    PRM_ATTR        *PrmAttr = NULL;
    REGIF_CONFIG_T  *RegCfg = Axis->RegManager->hPnReg;

    HumanRotCtrl.Prm.Max_Torque = Axis->Prm->PnCustomPrm.Max_Torque*0.01;
    HumanRotCtrl.Prm.Min_Torque = -HumanRotCtrl.Prm.Max_Torque;

    // 转矩转换系数-将转矩指令Nm 
    Span = (REAL32)HumanRotCtrl.Prm.Max_Torque*2.0f;
	HumanRotCtrl.Prm.TorqueQ2Nm = Span/65535.0f;

	
    UINT32 CoeIndex = 0x2010003F;
    PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
    rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);

    if(PRM_RSLT_SUCCESS != rslt)
    {
//        ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
    }	
		
  	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_CalHumanTimeout(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	HumanRotCtrl.Prm.CanFdBreakTime = Axis->Prm->PnAlmCfgPrm.BusBreakTime;

	HumanRotCtrl.Timeout.UserEnable =Axis->Prm->PnAlmCfgPrm.BusBreakEnable;
	return PRM_RSLT_SUCCESS;
}


/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_CalHumanCurKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRM_RSLT	    rslt;
    PRM_ATTR        *PrmAttr = NULL;
    REGIF_CONFIG_T  *RegCfg = Axis->RegManager->hPnReg;
		

    HumanRotCtrl.Prm.RefMaxCur = Axis->Prm->PnCustomPrm.RefMaxCur*0.01;
    HumanRotCtrl.Prm.RefMinCur = -HumanRotCtrl.Prm.RefMaxCur;

    UINT32 CoeIndex = 0x20100040;
    PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
    rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);

    if(PRM_RSLT_SUCCESS != rslt)
    {
//        ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
    }	
		
  	return PRM_RSLT_SUCCESS;	

}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_CalHumanVolKx(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	PRM_RSLT	    rslt;
    PRM_ATTR        *PrmAttr = NULL;
    REGIF_CONFIG_T  *RegCfg = Axis->RegManager->hPnReg;
		
    HumanRotCtrl.Prm.RefMaxVol = Axis->Prm->PnCustomPrm.RefMaxVol*0.01;

    UINT32 CoeIndex = 0x20100041;
    PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
    rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);

    if(PRM_RSLT_SUCCESS != rslt)
    {
//        ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
    }	
		
  	return PRM_RSLT_SUCCESS;	

}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_OverCur(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
    REAL32      work1;
	REAL32      DevMax;
	BPRMDAT		*Bprm = Axis->BaseLoops->Bprm;	

	DevMax = (REAL32)Axis->Prm->PnCfgPrm.DevMaxCur*1.414f*0.1f;  // APK
	if(Axis->Prm->PnAlmCfgPrm.OverCurLvl == 0)
	{
		work1 = 1.3 *Bprm->MaxCur;
	}
	else
	{
		work1 = Axis->Prm->PnAlmCfgPrm.OverCurLvl*0.01*Bprm->RatCur;
	}

    if(work1 >= DevMax)
    {
        Axis->CheckAlm->SwOc.SwOcCur = 1.05f*DevMax;	
		Axis->CheckAlm->SwOc.SwOcTime = 6;
    }
    else
    {
        Axis->CheckAlm->SwOc.SwOcCur = work1;
		Axis->CheckAlm->SwOc.SwOcTime = 12;

    }
  return PRM_RSLT_SUCCESS;
}	


/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_CalSftPosLimit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{

	REAL32 tmp = 0.0;	

    if(Axis->Prm->PnMotPrm.UseEncoder2 != 0)
    {
          HumanRotCtrl.Prm.IpdPosPLmit = C2_PI*(REAL32)Axis->BaseCtrls->Cia402Axis.Objects->MaxSoftwarePositionLimit0x607D/(REAL32)Axis->BaseLoops->Bprm->FbPulse1;
          HumanRotCtrl.Prm.IpdPosNLmit = C2_PI*(REAL32)Axis->BaseCtrls->Cia402Axis.Objects->MinSoftwarePositionLimit0x607D/(REAL32)Axis->BaseLoops->Bprm->FbPulse1;
    }
    else
    {
          tmp = C2_PI*(REAL32)Axis->BaseCtrls->Cia402Axis.Objects->MaxSoftwarePositionLimit0x607D/(REAL32)Axis->BaseLoops->Bprm->FbPulse;
//          HumanRotCtrl.Prm.IpdPosPLmit = GearRatioInvCal(Axis->BaseLoops,tmp);
          HumanRotCtrl.Prm.IpdPosPLmit = tmp;
          tmp = C2_PI*(REAL32)Axis->BaseCtrls->Cia402Axis.Objects->MinSoftwarePositionLimit0x607D/(REAL32)Axis->BaseLoops->Bprm->FbPulse;
//          HumanRotCtrl.Prm.IpdPosNLmit = GearRatioInvCal(Axis->BaseLoops,tmp);
          HumanRotCtrl.Prm.IpdPosNLmit = tmp;
    }

	Axis->BaseLoops->IpdPosSfNLimit = HumanRotCtrl.Prm.IpdPosNLmit;
	Axis->BaseLoops->IpdPosSfPLimit = HumanRotCtrl.Prm.IpdPosPLmit;	

	Axis->CheckAlm->OverLimit.conf.IPDPosNLmit = HumanRotCtrl.Prm.IpdPosNLmit;
	Axis->CheckAlm->OverLimit.conf.IPDPosPLmit = HumanRotCtrl.Prm.IpdPosPLmit;	


 	return PRM_RSLT_SUCCESS;
}

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_HjVelLimit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BASE_LOOP *BaseLoops = Axis->BaseLoops;

	REAL32 tmp = 0.0;	

	BaseLoops->IpdSpdSfLimit = Axis->Prm->PnCustomPrm.HjVelLimit*0.01;

 	return PRM_RSLT_SUCCESS;
}	

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_HjTrqLimit(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BASE_LOOP *BaseLoops = Axis->BaseLoops;

	REAL32 tmp = 0.0;	

	BaseLoops->IpdTrqSfLimit = Axis->Prm->PnCustomPrm.HjTrqLimit*0.01;

 	return PRM_RSLT_SUCCESS;
}	

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_HjLimitKp(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BASE_LOOP *BaseLoops = Axis->BaseLoops;

	BaseLoops->IpdSfLimitKp = Axis->Prm->PnCustomPrm.HjLimitKp*0.01;

 	return PRM_RSLT_SUCCESS;
}	

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_HjLimitKv(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	BASE_LOOP *BaseLoops = Axis->BaseLoops;

	BaseLoops->IpdSfLimitKv = Axis->Prm->PnCustomPrm.HjLimitKv*0.01;	

 	return PRM_RSLT_SUCCESS;
}		

/****************************************************************************************************/
/*											*/
/****************************************************************************************************/
PRM_RSLT pncal_HjIpdGainCal(PRM_ACCCMD Cmd, UINT32 ArrayIdx, AXIS_HANDLE *Axis, INT32 *pValue)
{
	PRM_RSLT	    rslt;
    PRM_ATTR        *PrmAttr = NULL;
    REGIF_CONFIG_T  *RegCfg = Axis->RegManager->hPnReg;

	HumanRotCtrl.Prm.IpdKpMax = Axis->Prm->PnCustomPrm.IpdKpMax*0.01;
	HumanRotCtrl.Prm.IpdKdMax = Axis->Prm->PnCustomPrm.IpdKdMax*0.01;

	UINT32 CoeIndex = 0x20100052;
    PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
    rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);

    if(PRM_RSLT_SUCCESS != rslt)
    {	
		ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
	}	

	CoeIndex = 0x20100053;
    PrmAttr = PrmGetAttrByCoeIndex(RegCfg,CoeIndex);
    rslt = PrmStoreValueToEeprom(RegCfg, PrmAttr, 0);

    if(PRM_RSLT_SUCCESS != rslt)
    {	
		ALMSetServoAlarm(Axis->AlmMngr, ALM_EEPROM);  
	}		

	return PRM_RSLT_SUCCESS;
}	

