/****************************************************************************************************
 *
 * FILE NAME:  BaseDef.h
 *
 * DESCRIPTION:  1.Basic Data Type Definition
 *              2.Basic Constant Definition
 *              3.Basic Macro Definition
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _VERSION_DEF_H
#define	_VERSION_DEF_H

/*--------------------------------------------------------------------------------------------------*/
/*--------------------------------------------------------------------------------------------------*/
/*		Software version definition										                            */
/*--------------------------------------------------------------------------------------------------*/
//----MCU_SOFTWARE_VERSION description : ----xx.xx.xx.xx.xxxxxxxx-------------------------------//
//----Relase Version.Hardware Version.Main SoftWare Version.Sub SoftWare Version.Modify Data----//

#define  MCU_SOFTWARE_VERSION            "04.04.03.0402.20250629"

#define  DEVICE_HARDWARE_VERSION         (0x0003)
#define  DEVICE_HARDWARE_VERSION_CHAR    "01.04.20250627"

#define  BASE_DEVICE_HARDWARE_VERSION     (0x0001)   // [!!!Caution!!!]Prohibit modification

#ifndef DEVICE_NAME
#if(DEVICE_HARDWARE_VERSION   == 0x0001)        
  #define DEVICE_NAME                     "MeteoriteX01"         // �ؽ�
#elif(DEVICE_HARDWARE_VERSION == 0x0002)
  #define DEVICE_NAME                     "MeteoriteM01"        // �����ŷ� �ű�KTM
#elif(DEVICE_HARDWARE_VERSION == 0x0003)
  #define DEVICE_NAME                     "MeteoriteX02"        // �����ŷ� �ű�MA600
#elif(DEVICE_HARDWARE_VERSION == 0x0004)
  #define DEVICE_NAME                     "MeteoriteM02"        // �����ŷ� �ű�MA600
#endif
#endif


#ifndef _ECAT_CAN_COM_ 
#define  MCU_SOFTWARE_VERSION_LEN        sizeof(MCU_SOFTWARE_VERSION)
#else
#define  MCU_SOFTWARE_VERSION_LEN        sizeof(MCU_SOFTWARE_VERSION) - 1
#endif


#ifndef _ECAT_CAN_COM_ 
#define  DEVICE_SOFTWARE_VERSION_LEN     sizeof(DEVICE_HARDWARE_VERSION_CHAR)
#else
#define  DEVICE_SOFTWARE_VERSION_LEN     sizeof(DEVICE_HARDWARE_VERSION_CHAR) - 1
#endif

#ifndef _ECAT_CAN_COM_ 
#define  DEVICE_NAME_LEN        sizeof(DEVICE_NAME)
#else
#define  DEVICE_NAME_LEN        sizeof(DEVICE_NAME)-1
#endif

#define  EEP_PV_START                    9
#define  EEP_PV_LEN                      4

#define  SERVO_DEVICE_VERSION            (0x01010111)
#define  EEP_PRM_VERSION                 (0x0401)




#endif