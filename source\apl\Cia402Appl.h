/****************************************************************************************************
 *
 * FILE NAME:   Cia402Appl.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.24
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	24-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _CIA402_APPL_H
#define	_CIA402_APPL_H

#include "BaseDef.h"
#include "PnPrmStruct.h"



/*--------------------------------------------------------------------------------------------------*/
/*		ControlWord Commands Mask (IEC61800_184e) 													*/
/*--------------------------------------------------------------------------------------------------*/
#define CONTROLWORD_COMMAND_SHUTDOWN_MASK                    0x0087 /**< \brief Shutdown command mask*/
#define CONTROLWORD_COMMAND_SWITCHON_MASK                    0x00C7 /**< \brief Switch on command mask*/
#define CONTROLWORD_COMMAND_SWITCHON_ENABLEOPERATION_MASK    0x008F /**< \brief Switch on & Enable command mask*/
#define CONTROLWORD_COMMAND_DISABLEVOLTAGE_MASK              0x0082 /**< \brief Disable voltage command mask*/
#define CONTROLWORD_COMMAND_QUICKSTOP_MASK                   0x0086 /**< \brief Quickstop command mask*/
#define CONTROLWORD_COMMAND_DISABLEOPERATION_MASK            0x008F /**< \brief Disable operation command mask*/
#define CONTROLWORD_COMMAND_ENABLEOPERATION_MASK             0x008F /**< \brief Enable operation command mask*/
#define CONTROLWORD_COMMAND_FAULTRESET_MASK                  0x0080 /**< \brief Fault reset command mask*/

/*--------------------------------------------------------------------------------------------------*/
/*		ControlWord Commands (IEC61800_184e)														*/
/*--------------------------------------------------------------------------------------------------*/
#define CONTROLWORD_COMMAND_SHUTDOWN                         0x0006 /**< \brief Shutdown command*/
#define CONTROLWORD_COMMAND_SWITCHON                         0x0007 /**< \brief Switch on command*/
#define CONTROLWORD_COMMAND_SWITCHON_ENABLEOPERATION         0x000F /**< \brief Switch on & Enable command*/
#define CONTROLWORD_COMMAND_DISABLEVOLTAGE                   0x0000 /**< \brief Disable voltage command*/
#define CONTROLWORD_COMMAND_QUICKSTOP                        0x0002 /**< \brief Quickstop command*/
#define CONTROLWORD_COMMAND_DISABLEOPERATION                 0x0007 /**< \brief Disable operation command*/
#define CONTROLWORD_COMMAND_ENABLEOPERATION                  0x000F /**< \brief Enable operation command*/
#define CONTROLWORD_COMMAND_FAULTRESET                       0x0080 /**< \brief Fault reset command*/

/*--------------------------------------------------------------------------------------------------*/
/*		StatusWord Masks and Flags																	*/
/*--------------------------------------------------------------------------------------------------*/

#define STATUSWORD_STATE_MASK                                0x006F /**< \brief State mask*/
#define STATUSWORD_VOLTAGE_ENABLED                           0x0010 /**< \brief Indicate high voltage enabled*/
#define STATUSWORD_WARNING                                   0x0080 /**< \brief Warning active*/
#define STATUSWORD_MANUFACTORSPECIFIC                        0x0100 /**< \brief Manufacturer specific*/
#define STATUSWORD_INTERNAL_LIMIT                            0x0800 /**< \brief Internal limit*/
#define STATUSWORD_REMOTE                                    0x0200 /**< \brief Set if the control word is processed*/
#define STATUSWORD_TARGET_REACHED                            0x0400 /**< \brief Target reached*/
#define STATUSWORD_INTERNALLIMITACTIVE                       0x0800 /**< \brief Internal limit active*/
#define STATUSWORD_DRIVE_FOLLOWS_COMMAND                     0x1000 /**< \brief Drive follows command (used in cyclic synchronous modes)*/

/*--------------------------------------------------------------------------------------------------*/
/*		StatusWord																                	*/
/*--------------------------------------------------------------------------------------------------*/
#define STATUSWORD_STATE_NOTREADYTOSWITCHON                  0x0000 /**< \brief Not ready to switch on*/
#define STATUSWORD_STATE_SWITCHEDONDISABLED                  0x0040 /**< \brief Switched on but disabled*/
#define STATUSWORD_STATE_READYTOSWITCHON                     0x0021 /**< \brief Ready to switch on*/
#define STATUSWORD_STATE_SWITCHEDON                          0x0023 /**< \brief Switched on*/
#define STATUSWORD_STATE_OPERATIONENABLED                    0x0027 /**< \brief Operation enabled*/
#define STATUSWORD_STATE_QUICKSTOPACTIVE                     0x0007 /**< \brief Quickstop active*/
#define STATUSWORD_STATE_FAULTREACTIONACTIVE                 0x000F /**< \brief Fault reaction active*/
#define STATUSWORD_STATE_FAULT                               0x0008 /**< \brief Fault state*/

/*--------------------------------------------------------------------------------------------------*/
/*		CiA402 State machine																	    */
/*--------------------------------------------------------------------------------------------------*/
#define STATE_NOT_READY_TO_SWITCH_ON        0x0001 /**< \brief Not ready to switch on (optional)*/
#define STATE_SWITCH_ON_DISABLED            0x0002 /**< \brief Switch on but disabled (optional)*/
#define STATE_READY_TO_SWITCH_ON            0x0004 /**< \brief Ready to switch on (mandatory)*/
#define STATE_SWITCHED_ON                   0x0008 /**< \brief Switch on (mandatory)*/
#define STATE_OPERATION_ENABLED             0x0010 /**< \brief Operation enabled (mandatory)*/
#define STATE_QUICK_STOP_ACTIVE             0x0020 /**< \brief Quick stop active (optional)*/
#define STATE_FAULT_REACTION_ACTIVE         0x0040 /**< \brief Fault reaction active (mandatory)*/
#define STATE_FAULT                         0x0080 /**< \brief Fault state (mandatory)*/

/*--------------------------------------------------------------------------------------------------*/
/*		CiA402 Modes of Operation (object 0x6060) (IEC61800_184e)									*/
/*--------------------------------------------------------------------------------------------------*/
// -128 to -1 Manufacturer-specific operation modes

#define NO_MODE                     0 /**< \brief No mode*/
#define PROFILE_POSITION_MODE       1 /**< \brief Position Profile mode*/
#define VELOCITY_MODE               2 /**< \brief Velocity mode*/
#define PROFILE_VELOCITY_MOCE       3 /**< \brief Velocity Profile mode*/
#define PROFILE_TORQUE_MODE         4 /**< \brief Torque Profile mode*/
//5 reserved                          
#define HOMING_MODE                 6 /**< \brief Homing mode*/
#define INTERPOLATION_POSITION_MODE 7 /**< \brief Interpolation Position mode*/
#define CYCLIC_SYNC_POSITION_MODE   8 /**< \brief Cyclic Synchronous Position mode*/
#define CYCLIC_SYNC_VELOCITY_MODE   9 /**< \brief Cyclic Synchronous Velocity mode*/
#define CYCLIC_SYNC_TORQUE_MODE     10/**< \brief Cyclic Synchronous Torque mode*/
//+11 to +127 reserved

/*--------------------------------------------------------------------------------------------------*/
/*		 CiA402 Error Codes (object 0x603F) (IEC61800_184e)											*/
/*--------------------------------------------------------------------------------------------------*/
#define ERROR_SHORT_CIRCUIT_EARTH_LEAKAGE_INPUT             0x2110 /**< \brief Short circuit/earth leakage (input)*/
#define ERROR_EARTH_LEAKAGE_INPUT                           0x2120 /**< \brief Earth leakage (input)*/
#define ERROR_EARTH_LEAKAGE_PHASE_L1                        0x2121 /**< \brief Earth leakage phase L1*/
#define ERROR_EARTH_LEAKAGE_PHASE_L2                        0x2122 /**< \brief Earth leakage phase L2*/
#define ERROR_EARTH_LEAKAGE_PHASE_L3                        0x2123 /**< \brief Earth leakage phase L3*/
#define ERROR_SHORT_CIRCUIT_INPUT                           0x2130 /**< \brief Short circuit (input)*/
#define ERROR_SHORT_CIRCUIT_PHASES_L1_L2                    0x2131 /**< \brief Short circuit phases L1-L2*/
#define ERROR_SHORT_CIRCUIT_PHASES_L2_L3                    0x2132 /**< \brief Short circuit phases L2-L3*/
#define ERROR_SHORT_CIRCUIT_PHASES_L3_L1                    0x2133 /**< \brief Short circuit phases L3-L1*/
#define ERROR_INTERNAL_CURRENT_NO1                          0x2211 /**< \brief Internal current no 1*/
#define ERROR_INTERNAL_CURRENT_NO2                          0x2212 /**< \brief Internal current no 2*/
#define ERROR_OVER_CURRENT_IN_RAMP_FUNCTION                 0x2213 /**< \brief Over-current in ramp function*/
#define ERROR_OVER_CURRENT_IN_THE_SEQUENCE                  0x2214 /**< \brief Over-current in the sequence*/
#define ERROR_CONTINUOUS_OVER_CURRENT_DEVICE_INTERNAL       0x2220 /**< \brief Continuous over current (device internal)*/
#define ERROR_CONTINUOUS_OVER_CURRENT_DEVICE_INTERNAL_NO1   0x2221 /**< \brief Continuous over current no 1*/
#define ERROR_CONTINUOUS_OVER_CURRENT_DEVICE_INTERNAL_NO2   0x2222 /**< \brief Continuous over current no 2*/
#define ERROR_SHORT_CIRCUIT_EARTH_LEAKAGE_DEVICE_INTERNAL   0x2230 /**< \brief Short circuit/earth leakage (device internal)*/
#define ERROR_EARTH_LEAKAGE_DEVICE_INTERNAL                 0x2240 /**< \brief Earth leakage (device internal)*/
#define ERROR_SHORT_CIRCUIT_DEVICE_INTERNAL                 0x2250 /**< \brief Short circuit (device internal)*/
#define ERROR_CONTINUOUS_OVER_CURRENT                       0x2310 /**< \brief Continuous over current*/
#define ERROR_CONTINUOUS_OVER_CURRENT_NO1                   0x2311 /**< \brief Continuous over current no 1*/
#define ERROR_CONTINUOUS_OVER_CURRENT_NO2                   0x2312 /**< \brief Continuous over current no 2*/
#define ERROR_SHORT_CIRCUIT_EARTH_LEAKAGE_MOTOR_SIDE        0x2320 /**< \brief Short circuit/earth leakage (motor-side)*/
#define ERROR_EARTH_LEAKAGE_MOTOR_SIDE                      0x2330 /**< \brief Earth leakage (motor-side)*/
#define ERROR_EARTH_LEAKAGE_PHASE_U                         0x2331 /**< \brief Earth leakage phase U*/
#define ERROR_EARTH_LEAKAGE_PHASE_V                         0x2332 /**< \brief Earth leakage phase V*/
#define ERROR_EARTH_LEAKAGE_PHASE_W                         0x2333 /**< \brief Earth leakage phase W*/
#define ERROR_SHORT_CIRCUIT_MOTOR_SIDE                      0x2340 /**< \brief Short circuit (motor-side)*/
#define ERROR_SHORT_CIRCUIT_PHASES_U_V                      0x2341 /**< \brief Short circuit phases U-V*/
#define ERROR_EARTH_LEAKAGE_PHASE_V_W                       0x2342 /**< \brief Earth leakage phase V-W*/
#define ERROR_EARTH_LEAKAGE_PHASE_W_U                       0x2343 /**< \brief Earth leakage phase W-U*/
#define ERROR_LOAD_LEVEL_FAULT_I2T_THERMAL_STATE            0x2350 /**< \brief Load level fault (I2t, thermal state)*/
#define ERROR_LOAD_LEVEL_WARNING_I2T_THERMAL_STATE          0x2351 /**< \brief Load level warning (I2t, thermal state)*/
#define ERROR_MAINS_OVER_VOLTAGE                            0x3110 /**< \brief Mains over-voltage*/
#define ERROR_MAINS_OVER_VOLTAGE_PHASE_L1                   0x3111 /**< \brief Mains over-voltage phase L1*/
#define ERROR_MAINS_OVER_VOLTAGE_PHASE_L2                   0x3112 /**< \brief Mains over-voltage phase L2 */
#define ERROR_MAINS_OVER_VOLTAGE_PHASE_L3                   0x3113 /**< \brief Mains over-voltage phase L3*/
#define ERROR_MAINS_UNDER_VOLTAGE                           0x3120 /**< \brief Mains under-voltage*/
#define ERROR_MAINS_UNDER_VOLTAGE_PHASE_L1                  0x3121 /**< \brief Mains under-voltage phase L1*/
#define ERROR_MAINS_UNDER_VOLTAGE_PHASE_L2                  0x3122 /**< \brief Mains under-voltage phase L2*/
#define ERROR_MAINS_UNDER_VOLTAGE_PHASE_L3                  0x3123 /**< \brief Mains under-voltage phase L3*/
#define ERROR_PHASE_FAILURE                                 0x3130 /**< \brief Phase failure*/
#define ERROR_PHASE_FAILURE_L1                              0x3131 /**< \brief Phase failure L1*/
#define ERROR_PHASE_FAILURE_L2                              0x3132 /**< \brief Phase failure L2*/
#define ERROR_PHASE_FAILURE_L3                              0x3133 /**< \brief Phase failure L3*/
#define ERROR_PHASE_SEQUENCE                                0x3134 /**< \brief Phase sequence*/
#define ERROR_MAINS_FREQUENCY                               0x3140 /**< \brief Mains frequency*/
#define ERROR_MAINS_FREQUENCY_TOO_GREAT                     0x3141 /**< \brief Mains frequency too great*/
#define ERROR_MAINS_FREQUENCY_TOO_SMALL                     0x3142 /**< \brief Mains frequency too small*/
#define ERROR_DC_LINK_OVER_VOLTAGE                          0x3210 /**< \brief DC link over-voltage*/
#define ERROR_OVER_VOLTAGE_NO_1                             0x3211 /**< \brief Over-voltage no  1*/
#define ERROR_OVER_VOLTAGE_NO_2                             0x3212 /**< \brief Over voltage no  2 */
#define ERROR_DC_LINK_UNDER_VOLTAGE                         0x3220 /**< \brief DC link under-voltage*/
#define ERROR_UNDER_VOLTAGE_NO_1                            0x3221 /**< \brief Under-voltage no  1*/
#define ERROR_UNDER_VOLTAGE_NO_2                            0x3222 /**< \brief Under-voltage no  2*/
#define ERROR_LOAD_ERROR                                    0x3230 /**< \brief Load error*/
#define ERROR_OUTPUT_OVER_VOLTAGE                           0x3310 /**< \brief Output over-voltage*/
#define ERROR_OUTPUT_OVER_VOLTAGE_PHASE_U                   0x3311 /**< \brief Output over-voltage phase U*/
#define ERROR_OUTPUT_OVER_VOLTAGE_PHASE_V                   0x3312 /**< \brief Output over-voltage phase V*/
#define ERROR_OUTPUT_OVER_VOLTAGE_PHASE_W                   0x3313 /**< \brief Output over-voltage phase W*/
#define ERROR_ARMATURE_CIRCUIT                              0x3320 /**< \brief Armature circuit*/
#define ERROR_ARMATURE_CIRCUIT_INTERRUPTED                  0x3321 /**< \brief Armature circuit interrupted*/
#define ERROR_FIELD_CIRCUIT                                 0x3330 /**< \brief Field circuit error */
#define ERROR_FIELD_CIRCUIT_INTERRUPTED                     0x3331 /**< \brief Field circuit interrupted*/
#define ERROR_EXCESS_AMBIENT_TEMPERATURE                    0x4110 /**< \brief Excess ambient temperature*/
#define ERROR_TOO_LOW_AMBIENT_TEMPERATURE                   0x4120 /**< \brief Too low ambient temperature*/
#define ERROR_TEMPERATURE_SUPPLY_AIR                        0x4130 /**< \brief Temperature supply air*/
#define ERROR_TEMPERATURE_AIR_OUTLET                        0x4140 /**< \brief Temperature air outlet*/
#define ERROR_EXCESS_TEMPERATURE_DEVICE                     0x4210 /**< \brief Excess temperature device*/
#define ERROR_TOO_LOW_TEMPERATURE_DEVICE                    0x4220 /**< \brief Too low temperature device*/
#define ERROR_TEMPERATURE_DRIVE                             0x4300 /**< \brief Temperature drive error*/
#define ERROR_EXCESS_TEMPERATURE_DRIVE                      0x4310 /**< \brief Excess temperature drive error*/
#define ERROR_TOO_LOW_TEMPERATURE_DRIVE                     0x4320 /**< \brief Too low temperature drive error*/
#define ERROR_TEMPERATURE_SUPPLY                            0x4400 /**< \brief Temperature supply error*/
#define ERROR_EXCESS_TEMPERATURE_SUPPLY                     0x4410 /**< \brief Excess temperature supply*/
#define ERROR_TOO_LOW_TEMPERATURE_SUPPLY                    0x4420 /**< \brief Too low temperature supply*/
#define ERROR_SUPPLY_ERROR                                  0x5100 /**< \brief Supply error*/
#define ERROR_SUPPLY_LOW_VOLTAGE                            0x5110 /**< \brief Supply low voltage*/
#define ERROR_U1_SUPPLY_15V                                 0x5111 /**< \brief U1 = supply +15V/-15V*/
#define ERROR_U2_SUPPLY_24_V                                0x5112 /**< \brief U2 = supply +24 V*/
#define ERROR_U3_SUPPLY_5_V                                 0x5113 /**< \brief U3 = supply +5 V*/
#define ERROR_U4_MANUFACTURER_SPECIFIC                      0x5114 /**< \brief U4 = manufacturer-specific error*/
#define ERROR_U5_MANUFACTURER_SPECIFIC                      0x5115 /**< \brief U5 = manufacturer-specific error*/
#define ERROR_U6_MANUFACTURER_SPECIFIC                      0x5116 /**< \brief U6 = manufacturer-specific error*/
#define ERROR_U7_MANUFACTURER_SPECIFIC                      0x5117 /**< \brief U7 = manufacturer-specific error*/
#define ERROR_U8_MANUFACTURER_SPECIFIC                      0x5118 /**< \brief U8 = manufacturer-specific error*/
#define ERROR_U9_MANUFACTURER_SPECIFIC                      0x5119 /**< \brief U9 = manufacturer-specific error*/
#define ERROR_SUPPLY_INTERMEDIATE_CIRCUIT                   0x5120 /**< \brief Supply intermediate circuit*/
//#define ERROR_CONTROL                                     0x5200
#define ERROR_CONTROL_MEASUREMENT_CIRCUIT                   0x5210 /**< \brief Measurement circuit*/
#define ERROR_CONTROL_COMPUTING_CIRCUIT                     0x5220 /**< \brief Computing circuit*/
#define ERROR_OPERATING_UNIT                                0x5300 /**< \brief Operating unit error*/
#define ERROR_POWER_SECTION                                 0x5400 /**< \brief Power section error*/
#define ERROR_OUTPUT_STAGES                                 0x5410 /**< \brief Output stages error*/
#define ERROR_CHOPPER                                       0x5420 /**< \brief Chopper error*/
#define ERROR_INPUT_STAGES                                  0x5430 /**< \brief Input stages error*/
#define ERROR_CONTACTS_ERROR                                0x5440 /**< \brief Contacts error*/
#define ERROR_CONTACT_1_MANUFACTURER_SPECIFIC               0x5441 /**< \brief Contact 1 = manufacturer-specific error*/
#define ERROR_CONTACT_2_MANUFACTURER_SPECIFIC               0x5442 /**< \brief Contact 2 = manufacturer-specific error*/
#define ERROR_CONTACT_3_MANUFACTURER_SPECIFIC               0x5443 /**< \brief Contact 3 = manufacturer-specific error*/
#define ERROR_CONTACT_4_MANUFACTURER_SPECIFIC               0x5444 /**< \brief Contact 4 = manufacturer-specific error*/
#define ERROR_CONTACT_5_MANUFACTURER_SPECIFIC               0x5445 /**< \brief Contact 5 = manufacturer-specific error*/
#define ERROR_FUSES_ERROR                                   0x5450 /**< \brief Fuses error*/
#define ERROR_S1_L1                                         0x5451 /**< \brief S1 = l1 error*/
#define ERROR_S2_L2                                         0x5452 /**< \brief S2 = l2 error*/
#define ERROR_S3_L3                                         0x5453 /**< \brief S3 = l3 error*/
#define ERROR_S4_MANUFACTURER_SPECIFIC                      0x5454 /**< \brief S4 = manufacturer-specific error*/
#define ERROR_S5_MANUFACTURER_SPECIFIC                      0x5455 /**< \brief S5 = manufacturer-specific error*/
#define ERROR_S6_MANUFACTURER_SPECIFIC                      0x5456 /**< \brief S6 = manufacturer-specific error*/
#define ERROR_S7_MANUFACTURER_SPECIFIC                      0x5457 /**< \brief S7 = manufacturer-specific error*/
#define ERROR_S8_MANUFACTURER_SPECIFIC                      0x5458 /**< \brief S8 = manufacturer-specific error*/
#define ERROR_S9_MANUFACTURER_SPECIFIC                      0x5459 /**< \brief S9 = manufacturer-specific error*/
#define ERROR_HARDWARE_MEMORY                               0x5500 /**< \brief Hardware memory error*/
#define ERROR_RAM                                           0x5510 /**< \brief RAM error*/
#define ERROR_ROM_EPROM                                     0x5520 /**< \brief ROM/EPROM error*/
#define ERROR_EEPROM                                        0x5530 /**< \brief EEPROM error*/
#define ERROR_SOFTWARE_RESET_WATCHDOG                       0x6010 /**< \brief Software reset (watchdog)*/
//0x6301_TO_0x630F        ERROR_DATA_RECORD_NO_1_TO_NO_15          
#define ERROR_LOSS_OF_PARAMETERS                            0x6310 /**< \brief Loss of parameters*/
#define ERROR_PARAMETER_ERROR                               0x6320 /**< \brief Parameter error*/
#define ERROR_POWER_ERROR                                   0x7100 /**< \brief Power error*/
#define ERROR_BRAKE_CHOPPER                                 0x7110 /**< \brief Brake chopper*/
#define ERROR_FAILURE_BRAKE_CHOPPER                         0x7111 /**< \brief Failure brake chopper*/
#define ERROR_OVER_CURRENT_BRAKE_CHOPPER                    0x7112 /**< \brief Over current brake chopper*/
#define ERROR_PROTECTIVE_CIRCUIT_BRAKE_CHOPPER              0x7113 /**< \brief Protective circuit brake chopper error*/
#define ERROR_MOTOR_ERROR                                   0x7120 /**< \brief Motor error*/
#define ERROR_MOTOR_BLOCKED                                 0x7121 /**< \brief Motor blocked error*/
#define ERROR_MOTOR_ERROR_OR_COMMUTATION_MALFUNC            0x7122 /**< \brief Motor error or commutation malfunc */
#define ERROR_MOTOR_TILTED                                  0x7123 /**< \brief Motor tilted*/
#define ERROR_MEASUREMENT_CIRCUIT                           0x7200 /**< \brief Measurement circuit*/
#define ERROR_SENSOR_ERROR                                  0x7300 /**< \brief Sensor error*/
#define ERROR_TACHO_FAULT                                   0x7301 /**< \brief Tacho fault*/
#define ERROR_TACHO_WRONG_POLARITY                          0x7302 /**< \brief Tacho wrong polarity*/
#define ERROR_RESOLVER_1_FAULT                              0x7303 /**< \brief Resolver 1 fault*/
#define ERROR_RESOLVER_2_FAULT                              0x7304 /**< \brief Resolver 2 fault*/
#define ERROR_INCREMENTAL_SENSOR_1_FAULT                    0x7305 /**< \brief Incremental sensor 1 fault*/
#define ERROR_INCREMENTAL_SENSOR_2_FAULT                    0x7306 /**< \brief Incremental sensor 2 fault*/
#define ERROR_INCREMENTAL_SENSOR_3_FAULT                    0x7307 /**< \brief Incremental sensor 3 fault*/
#define ERROR_SPEED                                         0x7310 /**< \brief Speed error*/
#define ERROR_POSITION                                      0x7320 /**< \brief Position error*/
#define ERROR_COMPUTATION_CIRCUIT                           0x7400 /**< \brief Computation circuit*/
#define ERROR_COMMUNICATION                                 0x7500 /**< \brief Communication error*/
#define ERROR_SERIAL_INTERFACE_NO_1                         0x7510 /**< \brief Serial interface no  1 error*/
#define ERROR_SERIAL_INTERFACE_NO_2                         0x7520 /**< \brief Serial interface no  2 error*/
#define ERROR_DATA_STORAGE_EXTERNAL                         0x7600 /**< \brief Data storage (external) error*/
#define ERROR_TORQUE_CONTROL                                0x8300 /**< \brief Torque control error*/
#define ERROR_EXCESS_TORQUE                                 0x8311 /**< \brief Excess torque error*/
#define ERROR_DIFFICULT_START_UP                            0x8312 /**< \brief Difficult start up error*/
#define ERROR_STANDSTILL_TORQUE                             0x8313 /**< \brief Standstill torque error*/
#define ERROR_INSUFFICIENT_TORQUE                           0x8321 /**< \brief Insufficient torque error*/
#define ERROR_TORQUE_FAULT                                  0x8331 /**< \brief Torque fault*/
#define ERROR_VELOCITY_SPEED_CONTROLLER                     0x8400 /**< \brief Velocity speed controller*/
#define ERROR_POSITION_CONTROLLER                           0x8500 /**< \brief Position controller*/
#define ERROR_POSITIONING_CONTROLLER                        0x8600 /**< \brief Positioning controller*/
#define ERROR_FOLLOWING_ERROR                               0x8611 /**< \brief Following error*/
#define ERROR_REFERENCE_LIMIT                               0x8612 /**< \brief Reference limit*/
#define ERROR_SYNC_CONTROLLER                               0x8700 /**< \brief Sync controller*/
#define ERROR_WINDING_CONTROLLER                            0x8800 /**< \brief Winding controller*/
#define ERROR_PROCESS_DATA_MONITORING                       0x8900 /**< \brief Process data monitoring*/
//#define ERROR_CONTROL                                     0x8A00
#define ERROR_DECELERATION                                  0xF001 /**< \brief Deceleration error*/
#define ERROR_SUB_SYNCHRONOUS_RUN                           0xF002 /**< \brief Sub-synchronous run error*/
#define ERROR_STROKE_OPERATION                              0xF003 /**< \brief Stroke operation error*/
//#define ERROR_CONTROL                                     0xF004
//0xFF00_TO_0xFFFF        MANUFACTURER_SPECIFIC                   


/*--------------------------------------------------------------------------------------------------*/
/*		CiA402 generic error option code values														*/
/*		   Note: Not all values are valid for each error option code.								*/
/*		   A detailed description of the option code values are listed in the specification IEC 61800-7-200                */
/*		   0x605B    : action in state transition 8													*/
/*		   0x605C    : action in state transition 5													*/
/*--------------------------------------------------------------------------------------------------*/
#define DISABLE_DRIVE                    0 /**< \brief Disable drive (options: 0x605B; 0x605C; 0x605E)*/
#define SLOW_DOWN_RAMP                   1 /**< \brief Slow down ramp (options: 0x605B; 0x605C; 0x605E)*/
#define QUICKSTOP_RAMP                   2 /**< \brief Quick stop ramp (options: 0x605E)*/
#define STOP_ON_CURRENT_LIMIT            3 /**< \brief Stop on current limit (options: 0x605E)*/
#define STOP_ON_VOLTAGE_LIMIT            4 /**< \brief Stop on voltage limit (options: 0x605E)*/

/*--------------------------------------------------------------------------------------------------*/
/*		Specific values for Quick stop option code (object 0x605A) (IEC61800_184e)					*/
/*		indicated the quick stop function                                           				*/
/*--------------------------------------------------------------------------------------------------*/
//-32768 to -1        MANUFACTURER_SPECIFIC
#define SLOWDOWN_RAMP_NO_TRANSIT                5 /**< \brief Slow down on slow down ramp and stay in Quick Stop Active*/
#define QUICKSTOP_RAMP_NO_TRANSIT               6 /**< \brief Slow down on quick stop ramp and stay in Quick Stop Active*/
#define CURRENT_LIMIT_NO_TRANSIT                7 /**< \brief Slow down on current limit and stay in Quick Stop Active*/
#define VOLTAGE_LIMIT_NO_TRANSIT                8 /**< \brief Slow down on voltage limit and stay in Quick Stop Active*/
//9 to 32767        RESERVED

/*--------------------------------------------------------------------------------------------------*/
/*		Module Identifications (each supported operation mode has a module)							*/
/*--------------------------------------------------------------------------------------------------*/
#define CSV_CSP_MODULE_ID       0x00119800 /**< \brief Module Id for cyclic synchronous position/velocity mode (dynamic switching supported)*/
#define CSP_MODULE_ID           0x00219800 /**< \brief Module Id for cyclic synchronous position mode*/
#define CSV_MODULE_ID           0x00319800 /**< \brief Module Id for cyclic synchronous velocity mode*/

/*--------------------------------------------------------------------------------------------------*/
/*		DC Command code Identifications                        							            */
/*--------------------------------------------------------------------------------------------------*/
#define DC_SHUTDOWM			        1
#define DC_SWITCH_ON		        2
#define DC_DISABLE_VOLTAGE		    3
#define DC_QUICK_STOP		        4
#define DC_DISABLE_OPERATION		5
#define DC_ENABLE_OPERATION   		6
#define DC_FAULT         			7

/*--------------------------------------------------------------------------------------------------*/
/*		Profile type Identifications                        							            */
/*--------------------------------------------------------------------------------------------------*/
#define LinearRamp			        0
#define JerkFreeRamp		       	2
#define JerkLimitedRamp		        3

/*--------------------------------------------------------------------------------------------------*/
/*		Home Method Identifications                        							            */
/*--------------------------------------------------------------------------------------------------*/
#define NegativeLimit_FallEdge_Index					1
#define PositiveLimit_FallEdge_Index					2
#define HomeInPositive_FallEdge_Index		    		3
#define HomeInPositive_RisEdge_Index		    		4
#define HomeInNegative_FallEdge_Index		    		5
#define HomeInNegative_RisEdge_Index		    		6
#define HomeNegEdge_PositiveLimit_FallEdge_Index		7
#define HomeNegEdge_PositiveLimit_RisEdge_Index		    8
#define HomePosiEdge_PositiveLimit_RisEdge_Index		9
#define HomePosiEdge_PositiveLimit_FallEdge_Index		10
#define HomePosiEdge_NegativeLimit_FallEdge_Index		11
#define HomePosiEdge_NegativeLimit_RisEdge_Index		12
#define HomeNegEdge_NegativeLimit_RisEdge_Index			13
#define HomeNegEdge_NegativeLimit_FallEdge_Index		14

#define NegativeLimit_FallEdge							17
#define PositiveLimit_FallEdge							18
#define HomeInPositive_FallEdge				    		19
#define HomeInPositive_RisEdge				    		20
#define HomeInNegative_FallEdge				    		21
#define HomeInNegative_RisEdge				    		22
#define HomeNegEdge_PositiveLimit_FallEdge				23
#define HomeNegEdge_PositiveLimit_RisEdge		   		24
#define HomePosiEdge_PositiveLimit_RisEdge				25
#define HomePosiEdge_PositiveLimit_FallEdge				26
#define HomePosiEdge_NegativeLimit_FallEdge				27
#define HomePosiEdge_NegativeLimit_RisEdge				28
#define HomeNegEdge_NegativeLimit_RisEdge				29
#define HomeNegEdge_NegativeLimit_FallEdge				30

#define Negative_Index									33
#define Positive_Index									34
#define HomeNow											35

/*--------------------------------------------------------------------------------------------------*/
/*		FunctionGenerator Method Identifications                        							*/
/*--------------------------------------------------------------------------------------------------*/
#define FG_LinearWave                   4
#define FG_SinWave			            3
#define FG_SquareWave			       	2
#define FG_StepWave				        1
/*--------------------------------------------------------------------------------------------------*/
/*		JOG Method Identifications                        							            */
/*--------------------------------------------------------------------------------------------------*/
#define JOGUP			        	1
#define JOGDOWN			       		2

/*--------------------------------------------------------------------------------------------------*/
/*		 Cia402 control word Struct 		    		            							    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{	// Cia402 Controlword
	UINT16  SwitchON:1;
	UINT16  EnVoltage:1;
	UINT16  QkStop:1;			
	UINT16  EnOper:1;			
	UINT16  OperSpecific_b4:1;	 
	UINT16  OperSpecific_b5:1;	
	UINT16  OperSpecific_b6:1;	 
	UINT16  RstAlm:1;			
	UINT16  Halt:1;				
	UINT16  OperSpecific_b9:1; 
	UINT16  Resv_b10:1;
	UINT16  Resv_b11:1;
    UINT16  ClrEncMultTurnAndErr:1;
    UINT16  ClrEnc2:1;
    	UINT16  RstEtherCAT:1;
	UINT16  Resv:1;			

}CIA402_CTRL_BIT;

typedef union
{
	UINT16           all;
	CIA402_CTRL_BIT  bit;
}CIA402_CTRL_WORD;

typedef struct
{	// Cia402 Controlword
	UINT16  FstopIn:1;
	UINT16  FnSvonReq:1;
	UINT16  DiServoOn:1;			
	UINT16  BeReqSeqoff:1;			
	UINT16  Resv2:12;			
}CIA402_EXTRA_BIT;

typedef union
{
	UINT16            all;
	CIA402_EXTRA_BIT  bit;
}CIA402_EXTRA_WORD;
/*--------------------------------------------------------------------------------------------------*/
/*		 Cia402 status word Struct 		    		            							        */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{	// Cia402 Statusword
	UINT16  ReadySwitchOn:1;		
	UINT16  SwitchednOn:1;			
	UINT16  OperEnabled:1;			
	UINT16  Fault:1;					
	UINT16  VoltageEnabled:1;		
	UINT16  QkStop:1;				
	UINT16  SwitchednOnDisabled:1;	
	UINT16  Warning:1;
	UINT16  Resv8:1;				
	UINT16  Remote:1;				
	UINT16  TargetReached:1;		
	UINT16  InLimitActive:1;			
	UINT16  OperSpecific_b12:1;		
	UINT16  OperSpecific_b13:1;		
	UINT16  FnCtrlMode:1;		
	UINT16  OperSpecific_b15:1;	

}CIA402_STATUS_BIT;


typedef union
{
	UINT16             all;
	CIA402_STATUS_BIT  bit;
}CIA402_STATUS_WORD;


typedef struct
{
	float        TorSlop;
	INT16        TargetTor;
	float        OutputTor;
    UINT16          FirstRun;   // 1:True 0:False
}PT_MODE_CTRL;


typedef struct
{
	INT16    	  VelProfileType;
	UINT32        VelWindowCnt;
	UINT32        VelThresholdCnt;
	UINT32        Acc;
	UINT32        Dec;
	UINT8         ProfileJerkUse;
	UINT32        JerkTime1;
	UINT32        JerkTime2;
	UINT32        JerkTime3;
	UINT32        JerkTime4;
	UINT32        JerkCnt;
	UINT32		  TempJerk;
	INT32		  DetaTime;
	INT32		  UpDownTime;
	UINT16        Mark;
	UINT16        SignChange;
	UINT16        OverTime;
	INT32         RmdAcc;
	INT32		  RmdVel;
	INT32         TargetVel;
	INT32         LastTargetVel;
	INT32         OutputVel;
	INT32         LastOutputVel;
	INT32         OutputAcc;
    UINT16          FirstRun;   // 1:True 0:False
}PV_MODE_CTRL;


typedef struct
{
	INT32           InitPos;
	INT32          	InitVel;
	UINT32          EndVel;
	INT32           TargetPos;
	UINT32			TargetVel;
	UINT32			Acc;
	UINT32			Dec;
	
	UINT16          PlanState;
	UINT16          NewPointMark;
	UINT32			PosWindowCnt;
	
	INT32           SectPos[5];           // sectional Postion
	INT32         	IntegralPos;
	INT32			RmdPos;
	INT32			RmdVel;
        UINT16          FirstRun;   // 1:True 0:False
	
	INT32         	OutputPos;
	INT32         	OutputVel;
}PP_MODE_CTRL;



typedef struct
{
	INT32           InitPos;
	INT32          	InitVel;
	UINT32          EndVel;
	INT32           TargetPos;
	UINT32			TargetVel;
	UINT32			Acc;
	UINT32			Dec;
	UINT16          PlanState;
	UINT16          NewPointMark;
	UINT32			PosWindowCnt;
	INT32           SectPos[5];           // sectional Postion
	INT32         	IntegralPos;
	INT32			RmdPos;
	INT32			RmdVel;
    UINT16          FirstRun;   // 1:True 0:False
	INT32         	OutputPos;
	INT32         	OutputVel;
}CLAMPINGJAW_MODE_CTRL;
typedef struct
{
	UINT32			Method;
	INT32           InitPos;
	INT32           PosOffset;
	INT32           HomeOffset;
	UINT32			FastSpeed;
	UINT32			SlowSpeed;
	UINT32			Acc;
	INT32			IndexMaxDistance;

	UINT16			HomeSwitch;
	UINT16			NegativeLimit;
	UINT16			PositiveLimit;
	UINT16			Index;

	UINT16          TouchSwitchMark;
	UINT16          TouchLimitMark;
	UINT16          TouchEdgeMark;
	UINT16          TouchIndexMark;
		UINT16          LastTouchIndexMark;
	UINT16          HomeMark;
	UINT32			PosWindowCnt;	
	INT32         	IntegralPos;
	INT32			RmdPos;
	INT32			RmdVel;
	INT32			SwitchPos;

	INT32         	OutputPos;
	INT32         	OutputVel;
	UINT16          FirstRun;   // 1:True 0:False
	
	UINT16          PosChangFlag;   // 1:True 0:False
}HOME_MODE_CTRL;

typedef struct
{
	struct
	{	
		UINT16		FgType;
		
		UINT32 		PeriodNum;
		UINT32		TotalNum;
		REAL32      FgAmp;
        UINT32		Slope;
	}P;

	struct
	{	
		BOOL		FgAct;
		UINT32		Cnt;
        INT32		Rmd;
		INT32       InitPos;
		INT32       OutputRef;
        UINT16      LinearDir;
	}V;

	
}FG_MODE_CTRL;

typedef struct
{
    struct
	{      	
        UINT32			TargetVel;
        UINT32			Acc;
    }P;
    struct
	{
        UINT16			UpDownAct;
        INT32			RmdVel;
        INT32         	OutputVel;
    }V;
}JOG_MODE_CTRL;


typedef struct
{
    UINT16          Pulse;
    INT32           LastPulse;
    INT32           DiffPulse;
}PULSE_MODE_CTRL;
/*--------------------------------------------------------------------------------------------------*/
/*		 Cia402 application Struct 		    		            								    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	INT32           InitPos;
	INT32           TargetPos;
    UINT16          FirstRun;   
    
    UINT16          Pulse;
    INT32           LastPulse;
    INT32           DiffPulse;
}PPOS_MODE_CTRL;
/*--------------------------------------------------------------------------------------------------*/
/*		 Cia402 application Struct 		    		            								    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
    UINT16    DeadVol;
    REAL32    FilterCoeff;
	  REAL32    Output;
}ANALOG_SPD_MODE_CTRL;
typedef struct
{
    UINT16    DeadVol;
    REAL32    FilterCoeff;
		REAL32    Output;
}ANALOG_TRQ_MODE_CTRL;



typedef struct
{
   UINT16 AxisEnableStep;
   UINT16 Mark;
   INT32  TargetPos;
   INT32  Pos1;
   INT32  Pos2;
   UINT32 StopTime;
   UINT32 StopCNT;
}PTP_MODE_CTRL;
/*--------------------------------------------------------------------------------------------------*/
/*		 Cia402 application Struct 		    		            								    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	BOOL            Cia402BeReq;
    UINT16          State; 
    UINT16          VirtualAxisEn;       
    UINT16          DcCmdCode;
	BOOL			AlmRstCmd;

	UINT16          LastCtrlWord;
	UINT16          LastCtrlWordForMode;
	UINT16			LastSysPrmInit;

	UINT16          u16PendingOptionCode;
	UINT16          StopCode;
	UINT16          StopMode;
    
    INT32           ZorePostion;

    CiA402_PRM      *Objects; 
    UINT32           MotorRev;
    UINT32           LoadShaftRev;

    PT_MODE_CTRL    PT_Ctrl;
    PV_MODE_CTRL    PV_Ctrl;
    PP_MODE_CTRL	PP_Ctrl;

    HOME_MODE_CTRL	HOME_Ctrl;

    FG_MODE_CTRL    FG_Ctrl;
    JOG_MODE_CTRL	JOG_Ctrl;

    PULSE_MODE_CTRL PULSE_Ctrl;

    PPOS_MODE_CTRL  PULSE_POS_Ctrl;
    ANALOG_SPD_MODE_CTRL    ANALOG_SPD_Ctrl;
    ANALOG_TRQ_MODE_CTRL    ANALOG_TRQ_Ctrl;
    CLAMPINGJAW_MODE_CTRL   CLAMPINGJAW_Ctrl;

    PTP_MODE_CTRL   PTP_Ctrl;
    CIA402_EXTRA_WORD   ExtraCtrl;

    
    UINT16  ChangControlMode;
    BOOL    TrqChangSpdFlag;  
    
    BOOL    QkStop;
    BOOL    DiServoOff;
    BOOL    DiServoOn;
    BOOL    DiServoOnMask;
}TCiA402Axis;



PUBLIC void Cia402_Init(TCiA402Axis *Cia402Axis, CiA402_PRM *Cia402Prm);
PUBLIC void Cia402_LocalError(TCiA402Axis *Cia402Axis, UINT16 ErrorCode, UINT16 StopMode);
PUBLIC INT32 Cia402_GearRatioCal(TCiA402Axis *Cia402Axis, INT32 In );
PUBLIC INT32 Cia402_GearRatioInvCal(TCiA402Axis *Cia402Axis, INT32 In );
PUBLIC void Cia402_PP_Mode(TCiA402Axis *Cia402Axis, BOOL BaseEnable );
PUBLIC void Cia402_PV_Mode(TCiA402Axis *Cia402Axis, BOOL BaseEnable );
PUBLIC void Cia402_PT_Mode(TCiA402Axis *Cia402Axis, BOOL BaseEnable );


#endif //_CIA402_APPL_H

