/****************************************************************************************************
 *
 * FILE NAME:  BaseLoops.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _BASE_LOOPS_H_
#define _BASE_LOOPS_H_

#include "Alarm.h"
//#include "Encoder.h"
#include "Bprm.h"
#include "GselHandler.h"
#include "Cia402Appl.h"
#include "PcmdFilter.h"
#include "ResVibCtrl.h"
#include "JatOffLine.h"
#include "ProgramJog.h"
#include "MfcHandler.h"
#include "MicroPrmCal.h"
#include "Mlib.h"



#define ABS_MAX_FRAME_LENGTH  12

									
/*--------------------------------------------------------------------------------------------------*/
/*		Mode Switch Select definition																*/
/*--------------------------------------------------------------------------------------------------*/
enum MODE_SW_TYPE {
	MODESW_TRQREF = 0x00,						
	MODESW_SPDREF = 0x01,					
	MODESW_MOTACC = 0x02,						
	MODESW_POSERR = 0x03,					
	MODESW_NOTUSE = 0x04,					
};

/*--------------------------------------------------------------------------------------------------*/
/*		Control Mode Change Select definition																*/
/*--------------------------------------------------------------------------------------------------*/
enum CONTROLMODE_CHANGE_TYPE {
	NONE_MODECHANGE = 0x00,						
	PPTPV_MODECHANGE = 0x01,					
	PVTPP_MODECHANGE = 0x02,						
	PPTPT_MODECHANGE = 0x03,					
	PTTPP_MODECHANGE = 0x04,	
        PVTPT_MODECHANGE = 0x05,					
	PTTPV_MODECHANGE = 0x06,	
};
/*--------------------------------------------------------------------------------------------------*/
/*		Mode switch Struct		    				        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{					
	struct
	{									
		INT32		ModeSWSel;				
		REAL32		MSWTrqLevel;					
		REAL32		MSWSpdLevel;					
		REAL32		MSWAccLevel;			
		REAL32		MSWErrLevel;		
	} conf;

	struct
	{							
		BOOL		ModeSWFlag;						
		REAL32		Acceleration;					
	} var;
} MODE_SW;



/*--------------------------------------------------------------------------------------------------*/
/*    control command bit union		    			        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef union
{
    UINT16 all;
    struct
    {
        UINT8 ClrAlm:1;
        UINT8 b1:1;
        UINT8 SpdPCtrl:1;
        UINT8 ClrPosErr:1;
        UINT8 IgnoreModeSW:1;
        UINT8 b5:1;
        UINT8 b6:1;
        UINT8 b7:1;
        UINT8 b8:1;
        UINT8 b9:1;
        UINT8 b10:1;
        UINT8 b11:1;
        UINT8 b12:1;
        UINT8 b13:1;
        UINT8 b14:1;
        UINT8 b15:1;
    } bits;
}CTRL_CMD_BIT;


/*--------------------------------------------------------------------------------------------------*/
/*    control command parameter struct	        	        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	struct	
	{/* Tuneless definition */
		REAL32			Klpf3;					// First-order lag filter gain (after torque compensation)	
		REAL32			SpdRefPhFil;			// LPF for phase advance								
		REAL32			SpdRefPhFilGn;			// Phase lead compensation gain						
	} TLPrm;

	REAL32	  PI_rate;			       		    // Speed control PI control ratio			
	REAL32    KVfdbfil;
	REAL32    SvonSpdLmtLevel;

	BOOL      IPSpdControl;
	BOOL      SpdIctrlKeep;

	REAL32    OverTrqLoopGain[3];				// Loop gain for speed limit							
	REAL32	  OverTrqIntegGain[3];				// Integral gain for speed limit							
	REAL32    OverTrqIntegBuf[2];				// Over Torque Integral Gain Calculation Buffer

	REAL32    OvrTrqSpdLim;                     // Over torque speed limit[2^24/OvrSpd]
	BOOL      LpassFil2;                        // Secondary delay filter selection
	BOOL	  LpassFil3;	                    // Select 1st delay filter after torque compensation

	REAL32    KVrfFil;   						// Speed command filter gain

	UINT16    SpdffCfg;
	REAL32    Kspdff;							// Position control speed FF gain					
	REAL32    KspdffFil;				    	// Position control speed FF filter gain

    UINT16    TrqffCfg;
	REAL32    Ktrqff;							// Position control speed FF gain					
	REAL32    KtrqffFil;				    	// Position control speed FF filter gain

	REAL32    SpdSfsAcc;						// Soft start acceleration						
	REAL32	  SpdSfsDec;						// Soft start deceleration	
        
    INT32     PosRefKlpf;	
	REAL32    Kposstiff;
	REAL32    Kspdstiff;

}CTRL_CMD_PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		 Control command manager Struct				        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
  	UINT16    AlgorithmMode;
	INT32     CtrlMode;
	INT32     LastCtrlMode;
    
	INT32     PosRef;                         // Position reference[pulse]
	INT32     PosRef_l;                       // Last cycle position reference[pulse]
	INT32     dPosRef;     
	INT32     dPosRef_l;   
	INT32     dPosRefFilo;
        INT32     dPosRefFilo1;    
	INT32     dPosRefFiloRem;    
	INT32     dPosRefo;
    INT32     PosRefFilo;
    INT32     LdStpPosRef;
    INT32     RmdPosRef;
	REAL32    SpdRef;                                                                // [2^24/OvrSpd]
	REAL32    SpdRef_l;                                                              // [2^24/OvrSpd]
	REAL32    SpdRefFilo;                                                            // [2^24/OvrSpd]
	REAL32    LdStpSpdRef;                                                           // [2^24/OvrSpd]
	REAL32    TrqRef;                                                                // [2^24/MaxTrq]
    REAL32    WeakRef;                           									 // [2^24/MaxTrq]
	REAL32    OvrTrq;                                                                // [2^24/MaxTrq]
	
	REAL32   	TrqPosComp;
	UINT32   	TrqPosCompTime;
	BOOL		TrqPosFlag;
	
	BOOL	  PerrClrReq;				// Position deviation clear request (dedicated to table operation/PJOG)

    REAL32    NetSpdFFC;                // Network speed feedforward compensation
	REAL32    NetTrqFFC;                // Network torque feedforward compensation
	REAL32    SpdFFC;
    REAL32    SpdFFCFilo;
	REAL32    SpdFBC;
	REAL32    TrqFFC;
    REAL32    TrqFFCFilo;
	REAL32    TrqFBC;

	REAL32    SpdffFilo;
	REAL32    TrqffFilo;

	REAL32	  SpdFbki;					// Speed FB input						[2^24/OvrSpd]
	REAL32	  SpdObsFbki;				// Speed FB input (after OBS)			[2^24/OvrSpd]	
	INT32	  dPosFbki;					// Position control: Position FB difference input[Pulse/Scan]		
	INT32	  PosFbki;					// Position control: Position FB input[Pulse/Scan]		
	INT32	  FbkPosFromCpos;			// Motor position from origin[+/-(PulseNo/2)],[LM:float]	
	INT32     LatchdPosRef;

	INT32     dPosRefFRem;
	INT32     PosTragetErr;   
	INT32     SpdTragetErr;
	INT32     TrqTragetErr;

	REAL32     IpdPosTargetRad;		       // ipd position target [shfit point rad ]
	REAL32     IpdPosTargetRadLast;  	   // ipd position target last cycle
	REAL32     IpdSpdTargetRads;		       // ipd speed target [shfit point  rad/s]
	REAL32     IpdSpdTargetRadsLast;  	   // ipd speed target last cycle
	REAL32     IpdTrqFeedforwardNm;       // ipd torque feedforward [shfit point Nm]
	REAL32     IpdPosRadErr;               // ipd position error [shfit point rad ]
	REAL32     IpdSpdRadsErr;              // ipd speed error [shfit point rad/s]
}CTRL_CMD_MNG;

/*--------------------------------------------------------------------------------------------------*/
/*		New Tuningless parameter variable definition												*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	INT32		KrlpfResSup;		// Machine resonance suppression filter gain
	INT32		KrlpfDisObs1[5];	// Disturbance observer 2nd order filter gain (for bandpass filter)
	INT32		KrlpfDisObs2[5];	// Disturbance observer 2nd order filter gain (for high pass filter)
	INT32		KrCurfreq;			// Current loop approximation model frequency
	INT32		KrCurZeta1;			// Current loop approximation model damping coefficient 1
	INT32		KrCurZeta2;			// Current loop approximation model damping coefficient 2
	INT32		KrlpfCur[5];		// Current loop approximation model 2nd order filter gain

} TL2PRM;


/*--------------------------------------------------------------------------------------------------*/
/*		Tuningless parameter variable definition													*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	UINT16		kv;					// Speed loop gain									[0.1Hz]			
	UINT16		kv2;				// Speed loop gain after correction					[0.1Hz]			
	UINT16		jrate;				// Load moment of inertia/mass ratio				[%]				
	UINT16		Ti;					// Speed loop integration time constant				[0.01ms]		
	UINT16		kp;					// Position loop gain								[0.1/s]			
	UINT16		klpf;				// Time constant of torque command filter after torque compensation[0.01ms]
	UINT16		f1;					// Disturbance observer low-pass filter 1 frequency	[0.1Hz]			
	UINT16		f1Comp;				// After disturbance disturbance observer low pass filter 1 frequency[0.1Hz]			
	UINT16		f2;					// Disturbance observer low-pass filter 2 frequencies  [0.1Hz]			
	UINT16		kj_dist;			// External disturbance observer gain				[%]				
	UINT16		kj_dist2;			// External disturbance observer gain after correction  [%]				
	UINT16		kd_dist;			// Disturbance observer coefficient					[%]				
	UINT16		ks_vobs;			// Phase compensation velocity observer gain		[Hz]			
	UINT16		kj_vobs;			// Phase compensation Velocity observer gain		[%]			
	UINT16		kj_vobs2;			// Phase compensation Velocity observer gain after correction [%]				
	UINT16		vobsLpf;			// Phase compensation speed observer LPF time constant  [0.01ms]		
	UINT16		Stiff;				// Robustness reduction rate						[%]				
	UINT16		F1rate;				// Disturbance observer filter frequency 1 reduction rate  [%]				
	INT32		vffPhCmpFrq1;		// Phase compensation filter frequency 1			[0.1Hz]			
	INT32		vffPhCmpFrq2;		// Phase compensation filter frequency 2			[0.1Hz]			

	TL2PRM		Tl2Prm;													

} TUNELESS_PRM;

/*--------------------------------------------------------------------------------------------------*/
/*		Variable definition for robust compensator calculation with tuningless						*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{				
	struct
	{								

		INT32		KVfbFil;					// Speed FB filter gain for tune-less	
		INT32		KCmpOutlpf;					// Robust compensator Torque compensation Low-pass filter gain
		INT32		Krlpf1;						// Robust compensator Low-pass filter gain 1
		INT32		Krlpf2;						// Robust compensator Low-pass filter gain 2	
		INT32		Krd;						// Robust compensation torque coefficient		
		INT32		Krsj;						// Robust compensator torque correction gain			


		TL2PRM	    *pTl2;						// Tuningless Parameter 								

		void	    (*RobustCompensator)		// Tuneingless Robust Compensator 
				    ( void *pdrcmp, 		
				     INT32 motspd, 			
				     INT32 trqref );		

	} conf;

	struct
	{							
		INT32		DisTrq;						// Disturbance torque estimate value								
		INT32		DisTrqCmp;					// Disturbance torque compensation value [2^24/MaxTrq]			
		INT32		TrqLpfo;					// Robust compensator Low-pass filter output value				
		INT32		TrqLpfo_old;				// Robust compensator Previous low pass filter output value			
		INT32		VelLpfo1;					// Robust compensator differential velocity low-pass filter output value 1	
		INT32		VelLpfo2;					// Robust compensator differential velocity low-pass filter output value 2	
		BOOL		TrqFilClrReq;				// Torque command filter integration initialization request				
		BOOL		CurrentStatus;				// Current status flag for valid/invalid switching judgment
		BOOL		LastStatus;					// Previous status flag for valid/invalid switching judgment	

		INT32		Krlpftmp1[4];				// 2nd-order LPF filter variable for internal calculation				
		INT32		Krlpftmp2[4];				// 2nd-order LPF filter variable for internal calculation				
		INT32		Krlpftmp3[4];				// 2nd-order LPF filter variable for internal calculation				

	} var;

} TUNELESS_DRCOMP;

/*--------------------------------------------------------------------------------------------------*/
/*		Tuningless phase compensation speed observer variable definition							*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{	
	struct 
	{							
		REAL32		Kstf1;						// Phase compensation VOBS gain 1
		REAL32		Kstf2;						// Phase compensation VOBS gain 2
		REAL32		Kstf3;						// Phase compensation VOBS gain 3
		REAL32		Kj;							// Phase compensation VOBS Phase compensation gain
		REAL32		Lpf;						// Phase compensation VOBS Low pass filter gain (LPF + Pn218�j
		REAL32		Tf;							// Phase compensation VOBS Low pass filter time constant

	} conf;

	struct 
	{						
		REAL32		Ivar64[2];					// For integral calculation		
		REAL32		EhSpdObs;					// Speed estimate value	
		REAL32		Wkbf1;						// Low pass filter output 
		REAL32		Wkbf2;						// Work variable for phase compensation VOBS calculation
	} var;

} TUNELESS_EHVOBS;

/*--------------------------------------------------------------------------------------------------*/
/*		Tuningless control structure definition														*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct 
{
	TUNELESS_PRM		TuneLessPrm;			// Tuneless gain parameter structure			
	TUNELESS_DRCOMP		Drcomp;					// Robust compensation calculation variable for tuning-less
	TUNELESS_EHVOBS		EhVobs;					// Phase compensation speed OBS calculation variable for tuning-less

	struct
	{								
		UINT16			DatGnLevel;				// DAT(Dynamic Auto Tuning) gain level				
		UINT16			DatJratLevel;			// DAT load level				
	} OpeReg;

	struct {							
		BOOL			TuningLessUse;			// Tuningless use/non-use setting			
		BOOL			TuningLessEx;			// New Tuningless setting									
		BOOL			TuningLessVComp;		// Tuningless phase lead compensation setting							
		INT32			TuningLessType;			// Tuningless processing type								
		INT32			TuningLessMode;			// Tuningless mode								
	} conf;

	struct {								
		INT32			SpdFbFilo;				// Speed FB filter output						
		BOOL			TuneLessAct;			// TuningLess request					
		BOOL			TuneLessVCompReq;		// TuningLess phase lead compensation request							
		BOOL			TuneLessInvldReq;		// TuningLess forced invalidation request							
		BOOL			TuningFuncRun;			// Running flag						
	} var;

} TUNELESS_CTRL;

/*--------------------------------------------------------------------------------------------------*/
/*		Variable definition for disturbance observer								                */
/*--------------------------------------------------------------------------------------------------*/
typedef GSELDOBS DOBS_PRM;
//
typedef	struct 
{		
	DOBS_PRM			*pDobsPrm;				// Parameters for disturbance observer 	
//
	struct
	{							
		BOOL			DobsAct;				// Disturbance observer execution request					
		REAL32			Ivar64[2];				// For integral calculation								
		REAL32			SpdObs;					// Speed estimate									
		REAL32			DisTrq;					// Disturbance torque estimate					
		REAL32			Hpftmp;					// For high-pass filter operation			
		REAL32			DTrqLpfo;				// Low-pass filter output disturbance torque estimate	
		REAL32			DisTrqCmp;				// Disturbance torque compensation value[2^24/MaxTrq]	
		UINT8			CurrentStatus;			// Current state flag for valid / invalid switching judgment
		UINT8			LastStatus;				// Previous status flag for valid / invalid switching judgment
		UINT8			ExchangeSts;			// Function enable / disable switching state (for shock reduction)
		UINT8			Dummy;					// For Alignment					
	} V;
//
} DOBS_CTRL;


/*--------------------------------------------------------------------------------------------------*/
/*		Phase compensation speed observer (VOBS) variable definition								*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct
{					
	REAL32				Kstf1;					// Phase compensation VOBS gain 1	
	REAL32				Kstf2;					// Phase compensation VOBS gain 2	
	REAL32				Kstf3;					// Phase compensation VOBS gain 3
	REAL32				Kj;						// Phase compensation VOBS phase compensation gain 
	REAL32				Lpf;					// Phase compensation VOBS low pass filter gain	
	REAL32				Tf;						// Phase compensation VOBS low-pass filter time constant	
	REAL32				KTrqReflpf;				// VOBS torque command low pass filter gain			
} EHVOBS_PRM;

typedef	struct 
{				
	EHVOBS_PRM			EhVobsPrm;				// Speed observer parameter		

	struct
	{							
		BOOL			EhVobsUse;				// Phase compensation speed observer valid / invalid selection				
	} P;

	struct 
	{								
		REAL32			Ivar64[2];				// For integral calculation										
		REAL32			AccSum;					// Acceleration integrated value (speed estimation value)					
		REAL32			EhSpdObs;				// Speed estimate value							
		REAL32			Wkbf1;					// Low pass filter output 							
		REAL32			Wkbf2;					// Work variable for phase compensation VOBS calculation						
		REAL32			TrqRefLpfo;				// VOBS torque command filter estimated value		
        REAL32          MotSpdLpf;
	} V;
} EHVOBS_CTRL;


/*--------------------------------------------------------------------------------------------------*/
/*		Torque limit struct						        									        */
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
	struct
	{						
		float			EstopTrqLmt;		// Emergency stop torque[2^24/MaxTrq]	
		float			FwIntTrqLmt;		// Forward rotation internal torque limit[2^24/MaxTrq]		
		float			RvIntTrqLmt;		// Reverse internal torque limit[2^24/MaxTrq]		
		float			FwExtTrqLmt;		// Forward rotation external torque limit[2^24/MaxTrq]		
		float			RvExtTrqLmt;		// Reverse external torque limit[2^24/MaxTrq]		

	} P;
	
	struct
	{						
		BOOL			PclSignal;			// PCL Signal					
		BOOL			NclSignal;			// NCL Signal									
	
		float			PosTrqLmtOut;		    // Positive Torque Limit(output)[2^24/MaxTrq]		
		float			NegTrqLmtOut;		    // Negative Torque Limit(output)[2^24/MaxTrq]		
		float			IpdPosTrqLmtOut;		// Positive Torque Limit(output)[2^24/MaxTrq]		
		float			IpdNegTrqLmtOut;		// Negative Torque Limit(output)[2^24/MaxTrq]		
	} V;
		
} TRQ_LMT_DATA;

/*--------------------------------------------------------------------------------------------------*/
/*		Position management parameter definition													*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{					
	struct	
	{								
		EGEAR		InvalidEgear;					// Gear ratio when electronic gear function is disabled(1:1)
		EGEAR		Egear;							// Electronic gear ratio information							
		UINT8		CoinOutSel;						// /COIN output timing selection setting					
		INT32		CoinLevel;						// Positioning completion width [command unit]
		INT32		NearLevel;						// Near signal width [command unit]
		INT32		PosErrDelayShift;				// Number of correction time shifts for position deviation monitoring				
	} conf;

	struct	
	{								
//		PERRA		PosErrA;						// Position deviation (command unit)				
		INT32		dPcmdEgear;						// Position command electronic gear output						
		INT32		dPcmdEgrem;						// Position command electronic gear calculation remainder						
		INT32		dPcmda;							// Position command increment value input [command unit]	
//		INT32		PosErr;							// Position loop deviation(Pulse)				
		INT32		Per64[2];						// Position loop deviation calculation(Pulse)				

		BOOL		EgearDisable;					// Electronic gear invalid							
		BOOL		PcmdFilStop;					// Position command filter invalid						

	} var;
	
	EGEAR			*Egear;							// Electronic gear switching pointer			
	APOSRG			PgPos;							// FB position in machine coordinate system	
	APOSRG			MencPos;						// FB position of motor encoder								
	APOSRG			FencPos;						// FB position of full encoder					
	INT32			VirtualPositionError;			// Virtual position deviation (command unit)	

	INT32			dPosRefi;						// Position control: Position command difference input [Pulse / Scan]	
	INT32			CompdPosRefi;					// Position command difference input (ScanB-> ScanA) [Pulse / Scan]	
	INT32			CompdPcmda;						// Position command increment value input (ScanB-> ScanA) [command unit]
	UINT16			RefRenewCntA;					// BaseLoop counter for position command update processing		
	UINT16			RefRenewCntB;					// BaseCtrl counter for position command update processing				
	UINT16			RefRenewalChkCnt;				// For position command update check							

} POS_MNG_HNDL;

/*--------------------------------------------------------------------------------------------------*/
/*		TaskB node common variable definition														*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{
//	SV_OPTION_IF	CmdOptionIf;			// Pointer to SV-OP I/F handle				
	BOOL			CmdReady;				// Command acceptance flag 

//	CMD_CTRLBITS	CmdCtrl;				// Control command									
	INT32			CmdSeqBit;				// Sequence control command							
	INT32			CmdLtBit;				// Latch request									
//	RSP_CTRLBITS	RspCtrl;				// Control state 
	INT32			RspSigBit;				// Sequence signal								
	INT32			RspLtBit;				// Latch state										
	BOOL			LtReqCphs;
	BOOL			LtReqExt1;
	BOOL			LtReqExt2;
	BOOL			LtReqExt3;
	BOOL			ExtLtEnable[3];
//	SHAL_LATCH_STS	ExtLtSts[3];

	INT32			MonCphaLpos[2];			// C-phase latch position [command unit]			
	INT32			MonExt1Lpos[2];			// EXT1 latch position [command unit]			
	INT32			MonExt2Lpos[2];			// EXT2 latch position [command unit]	
	INT32			MonExt3Lpos[2];			// EXT3 latch position [command unit]		

	UINT32			NetSout;				// Communication I/F output signal command	
	INT32			NetCtrlMcmd;			// Control mode (command from communication)	
	INT32			NetTrqFFC;				// Torque FF command	[2^24/MaxTrq]				
	INT32			NetTrqRef;				// Torque command		[2^24/MaxTrq]					
	INT32			NetSpdLmt;				// Speed limit			[2^24/OvrSpd]				
	INT32			NetSpdRef;				// Speed command		[2^24/OvrSpd]				
	INT32			NetSpdFFC;				// Speed FF command		[2^24/OvrSpd]				
	INT32			NetPosRef;				// Position command		[command unit]			

	UINT32			NetIfMonInSignal1;		// SI port etc. Input signal status for Network Single axis amplifier
	UINT32			NetIfMonInSignal2;		// SI port etc. Input signal status for Network Multi-axis amplifier

	INT32			*MonRamTable[0x50];	    // Extended monitor variable storage table
	struct 
	{													
		INT32		MonSel1;				// Extended monitor selection 1								
		INT32		MonSel2;				// Extended monitor selection 2							
		INT32		MonSel3;				// Extended monitor selection 3						
		INT32		MonSel4;				// Extended monitor selection 4							
		INT32		MonSel5;				// Extended monitor selection 5							
		INT32		MonSel6;				// Extended monitor selection 6							
		INT32		MonSel7;				// Extended monitor selection 7							
		INT32		MonSel8;				// Extended monitor selection 8							
	} MonSel;
} SERVO_CONTROL_IF;


/*--------------------------------------------------------------------------------------------------*/
/*		Settling time related processing variable definition										*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct 
{		
	BOOL	StlgCalcReq;					/* Settling time calculation flag						*/
	BOOL	StlgLastCoinSts;				/* COIN signal previous value							*/
	BOOL	StlgCoinLatch;					/* COIN latch state										*/
	UINT32	ProcessTimer;					/* Settling time measurement process execution timing detection timer*/
	INT32	StlgLastdPcmd;					/* Differential position command previous value			*/
	INT32	StlgTimeCntr;					/* Counter for settling time measurement				*/
	INT32	TmpStlgTime;					/* Settling time counter buffer							*/
	INT32	RsltStlgTime;					/* Settling time calculation result						*/
	INT32	OverErrDir;						/* Sign of deviation to overshoot						*/
	INT32	OverShoot;						/* For overshoot amount measurement [command unit]		*/
	INT32	UnitChngGain;					/* Gain for unit conversion [cyc]->[1us]				*/
	INT32	UnMaxOverShoot;					/* Maximum overshoot amount [Unit of command]			*/
	INT32	UnCoinOffTimer;					/* Positioning completion failure time integration[ms]	*/
	INT32	OvsErrDetLvl;					/* Allowable overshoot amount [command unit]			*/
	INT32	LastdPcmda;						/* Previous position command increment value [command unit] */
} SETTLINGTIME;

typedef	struct 
{
	BOOL				RefZSignal;			/* Reference Zero Signal								*/
	BOOL				NearSignal;			/* Near Signal											*/
	BOOL				CoinSignal;			/* Coin Signal											*/
	BOOL				DenSignal;			/* Den Signal											*/
} POS_CTRL_STS;

typedef	struct 
{
	BOOL				MotCphPass;			/* Phase C pass flag(1scan)								*/
	INT32				MposFromCpos;		/* Motor position from CPOS	[+/-(PulseNo/2)],[LM:LONG]	*/
	INT32				MotSpd;				/* Motor speed							[2^24/OvrSpd]	*/
	INT32				FencSpd;			/* Motor speed (full scale)				[2^24/OvrSpd]	*/
	INT32				MotPos;				/* Motor position (TaskB integrated value) [32bitRingPulse ]*/
	INT32				dMotPos;			/* Delta Motor Position					[pulse/ScanB]	*/
	BOOL				FencCphPass;		/* Phase C pass flag (1scan) (full-scale)				*/
	INT32				FposFromCpos;		/* Motor position from CPOS (full scale)[+/-(PulseNo/2)],[LM:LONG]*/
	INT32				FencPos;			/* Motor position (TaskB integrated value) (full scale)[32bitRingPulse]*/
	INT32				dFencPos;			/* Delta Motor Position	(full scale)	[pulse/ScanB]	*/
	INT32				MotSpdSumB;			/* Motor Speed Sum(TaskB Cycle)							*/
	INT32				MotSpdCntB;			/* Motor Speed Cnt(TaskB Cycle)							*/
	INT32				MotSpdForSeq;		/* For Motor Speed sequence (without compensation)[2^24/OvrSpd]*/
	INT16				Dummy;				/* for Alignment */
} BASE_MOT_STS;

/*--------------------------------------------------------------------------------------------------*/
/*		Base Control Struct 						        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	CTRL_CMD_BIT      CtrlCmdBit;
	CTRL_CMD_PRM      CtrlCmdPrm;
	CTRL_CMD_MNG      CtrlCmdMngr;
	BASE_MOT_STS	  MotSts;				/* Motor status acquired/created by TaskB*/
	CTRLMD_SET		  CtrlModeSet;		
	POS_MNG_HNDL	  PosManager;			
	POS_CTRL_STS	  PosCtrlSts;			
	GAIN_CHNG_HNDL    GainChange;
	MFCTRL			  MFControl;	
//
	TRQ_LMT_DATA	  TrqLimitData;		
	SETTLINGTIME	  SettlingTime;		
//
	MODE_SW           ModeSw;
//
	TUNELESS_CTRL	  TuneLessCtrl;		
	DOBS_CTRL		  DobsCtrl;			
	EHVOBS_CTRL		  EhVobsCtrl;

	PCMDFIL			  PcmdFil;		
	MAFIL			  VFFcmdFil;			
	MAFIL			  TFFcmdFil;			
	RESVIB			  ResVib;			
	VIBSUPFIL		  VibSupFil;			
	FRICTION		  FrictionM;	
	REAL32			  TrqMotFricComp;

	JATHNDL			  JatHdl;				// 	Off-line moment of inertia estimation	
	PJOGHNDL		  PJogHdl;
	WEAKENFIELD		  WeakenField;		
//
	TCiA402Axis 	  Cia402Axis;
	BOOL              DcSyncSig;            // ethercat dc Sync signal
	BOOL              BaseEnable;
	BOOL              CmdEnable;
    BOOL              LastCmdEnable;

	INT32			  GselNum;				// Gain number for manual gain switching
	REAL32    PosDisturCompKx; // [0.1%]
	BOOL			  PosDisCDisable;
        UINT16          MotorControlAlgorithm;
	REAL32          TrqRefDamp;
	REAL32          TrqRefSpring;
  
}BASE_CTRL;


/*--------------------------------------------------------------------------------------------------*/
/*		motor speed moving average filter 						        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct MOTSPDMAFIL {
	INT32	mabuf[64];			// buffer	[2^24/OvrSpd]					
	INT64	mabufSumx;			// sum of buffer				
	INT32	idx;				// buffer index					
	INT32	manumBit;			// average number of move							
	INT32	defaultNumBit;		// default average number of move						
	INT32	ksub;				// Subtraction calculation gain (0 / 1)
} MOTSPDMAFIL;


/*--------------------------------------------------------------------------------------------------*/
/*		Weaken Field Control Struct																	*/
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{

    #define  WF_MAXID   -0.9f
	REAL32	WfKp;					// Voltage FB proportional gain			
	REAL32	WfKi;					// Voltage FB integrated gain
	REAL32	WfV1Max;				// Voltage command limit level							
	REAL32	WfIdRefLim;				// d-axis current command limit	
    REAL32	WfIqRefLim;				// q-axis current command limit		
    REAL32	WfIdRefLimAct;			// d-axis current command limit	
    REAL32	WfIqRefLimAct;		    // q-axis current command limit		
   			
	REAL32	Vel;					// Velocity (omega)				
																		
	REAL32	WfVqMax;				// q-axis voltage command limit	
	REAL32  WfKpPart;
	REAL32  WfKiPart;
	REAL32  WfOutPreSat;
	REAL32	WfVdRef;				// d-axis voltage command								
	REAL32	WfVqRef;				// q-axis voltage command	

	REAL32	IdOut;					// Id reference					
	REAL32	IqOut;					// Iq reference		
}WEAKFV;


/*--------------------------------------------------------------------------------------------------*/
/*		 Current Loop Struct						        									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	struct
	{
		REAL32	KpD;                  // current loop proportion gain
	    REAL32	KiD;                  // current loop intigration gain
		REAL32	KpQ;                  // current loop proportion gain
	    REAL32	KiQ;                  // current loop intigration gain

		/* SVPWM Module */
		INT16	Tonmin;              // PWM on min value (timer counter)
		INT16	Tonmax;              // PWM on max value (timer counter)
		INT16	PWMPRD;              // PWM period value (timer counter)
		REAL32	VDCinvTSQRT;         // for svpwm
		REAL32	VDCinvTCon0;         // for svpwm

		/* Notch filter */
		UINT32  NotFilCfg;           // notch filter config
		REAL32  NotchKn1[4];         // notch filter 1 coefficient
		REAL32  NotchKn2[4];         // notch filter 2 coefficient
		REAL32  NotchKn3[4];         // notch filter 3 coefficient
		REAL32  NotchKn4[4];         // notch filter 4 coefficient

		BOOL    WfSw;                   // Weaken Field Control Switch
	    REAL32  KwfId;                  // d-axis current command coeficiency	
	    REAL32  WfV1Rat;                // d-axis current command based velocity
        
        REAL32  CurSplKx;               // current sample kx  
        UINT32  DeadTime;                // dead time for timer [150Mhz]        
	    
	}P;

	struct
	{
		REAL32  Ia;                  // phase a actual current
		REAL32	Ib;                  // phase b actual current
		REAL32	Ic;                  // phase c actual current
		REAL32	Ialfa;               // Ialfa actual current
		REAL32	Ibeta;               // Ibeta actual current

		REAL32  IaRef;               // phase a current reference
		REAL32	IbRef;               // phase b current reference
		REAL32	IcRef;               // phase c current reference
		REAL32	IalfaRef;            // Ialfa current reference
		REAL32	IbetaRef;            // Ibeta current reference

		REAL32	IdRef;               // Id Reference
                REAL32	IdRefLit;            // Iq Reference after limit
                REAL32	IqRef;               // Iq Reference
                REAL32	IqRefLit;            // Iq Reference after limit
                REAL32	IqRefNotFil;         // Iq Reference after limit
                REAL32	IqRef_l;             // Last cycle Iq Reference
                REAL32	IqRef_ll;            // Last Last cycle Iq Reference
                REAL32	IqRef_lll;           // Last Last Last cycle Iq Reference

		REAL32	IqRefLmtP;           // Positive Iq Reference Limit
		REAL32	IqRefLmtN;           // Negative Iq Reference Limit

	    REAL32	IdFdbFil;            // Id Feedback [A]
	    REAL32	IqFdbFil;            // Iq Feedback [A]

	    REAL32	IdFdb;               // Id Feedback [A]
	    REAL32	IqFdb;               // Iq Feedback [A]
	    REAL32	IdErr;               // Id following error [A]
	    REAL32	IqErr;               // Iq following error [A]

		REAL32	IdKpPart;			 // pid_Id proportion part
		REAL32	IqKpPart;			 // pid_Iq proportion part
		REAL32	IdKiPart;			 // pid_Id intigration part
	    REAL32	IqKiPart;            // pid_Iq intigration part

	    REAL32	IdOutPreSat;         // pid_Id output value (not limitted by IdqPidOutMax)
	    REAL32	IqOutPreSat;         // pid_Iq output value (not limitted by IdqPidOutMax)
	    REAL32	VdRef;				 // pid_Id output value is VdRef (limitted by IdqPidOutMax)
	    REAL32	VqRef;               // pid_Iq output value is VqRef (limitted by IdqPidOutMax)

		REAL32  VdRefVF;             //for voltage&frequency control 
		REAL32  VqRefVF;             //for voltage&frequency control

		UINT16  ElecAngle;           // electric angle          [0-65536]/360deg
		REAL32	SinTheta;            // sin(ElecAngle) for park
		REAL32	CosTheta;            // cos(ElecAngle) for park
		REAL32	Valfa;               // alfa-axis voltage
		REAL32	Vbeta;               // beta-axis voltage
		
		/* SVPWM Module */
		INT16	TaNumber;            // SVPWM output for timer compare value
		INT16	TbNumber;            // SVPWM output for timer compare value
		INT16	TcNumber;            // SVPWM output for timer compare value

		REAL32  NotIn1[3];           // notch filter 1 input
		REAL32  NotOut1[3];          // notch filter 1 output
		REAL32  NotIn2[3];           // notch filter 2 input
		REAL32  NotOut2[3];          // notch filter 2 output
		REAL32  NotIn3[3];           // notch filter 3 input
		REAL32  NotOut3[3];          // notch filter 3 output
		REAL32  NotIn4[3];           // notch filter 4 input
		REAL32  NotOut4[3];          // notch filter 4 output
        
		REAL32  ActualVdc;
        REAL32  ActualVdcFilo;
		BOOL	MPIFlag;

		BOOL    VoltLmtFlg;
                BOOL    WeakFieldFlag;
	}V;

}CUR_LOOP;


/*--------------------------------------------------------------------------------------------------*/
/*		 Speed Loop Struct 						            									    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	struct
	{
		REAL32  KiFil;
	}P;

	struct
	{
		REAL32	SpdRef;             // Input: Reference input
		REAL32	SpdRefSum;          // Speed Reference before pid control
		REAL32	SpdFdbFilo; 		// Speed Feedback filter output
		REAL32	SpdErr;             // Variable: Error
		REAL32	SpdKpPart;		    // Variable: Proportional output
		REAL32	SpdKiPart;		    // Variable: Integral output
		REAL32  Ivar;
		REAL32	OutPreSat;		    // Variable: Pre-saturated output
		REAL32  TrqRef;             //speed loop output
		
		REAL32	SpdRefPhLpfo;		// LPF output for phase advance (for speed command)	
		REAL32	SpdRefPhHpfo;		// HPF output for phase advance (for speed command)			
		REAL32	SpdRefPhHpfTmp[2];		// For phase advance HPF calculation (for speed command)		
		REAL32	SpdFFPhLpfo;		// LPF output for phase advance (for speed FF)		
		REAL32	SpdFFPhHpfo;		// HPF output for phase advance (for speed FF)	
		REAL32	SpdFFPhHpfTmp[2];		// For phase advance HPF calculation (for speed FF)

		BOOL	SpdRefFilClrCmd;	// Speed command phase lead compensation filter initialization command
		BOOL	SpdFFFilClrCmd; 	// Speed FF phase lead compensation filter initialization command	
		BOOL    LastSvonFlag;       //Last servo on state
		BOOL    SvonSpdLmtFlag;     //speed limit flag when servo on
	}V;

}SPD_LOOP;


/*--------------------------------------------------------------------------------------------------*/
/*		 Position Loop Struct 						            								    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	INT32	PosErra; 			// position following error
	INT32	Per64a[2];			// position error
	INT32 	PosErr;				// position following error
	INT32	Per64[2];           // position error
	REAL32 	SpdRef;				// position loop output. rpm unit

}POS_LOOP;


/*--------------------------------------------------------------------------------------------------*/
/*		Variable definition for torque filter calculation											*/
/*--------------------------------------------------------------------------------------------------*/
/*		Parameter: Separate for taskA and taskB														*/
/*		Variable for calculation: shared by ScanA and ScanB (to ensure torque command continuity when switching control modes)*/
/*--------------------------------------------------------------------------------------------------*/
typedef	struct// Constant & variable definition for torque filter
{								
	struct    // taskA : Constant definition for torque filter
	{								                

		REAL32	Klpf;								// 1st delay filter gain					
		REAL32	Knxf[3];							// Notch filter gain					
		REAL32	Klpf2[4];							// Second order lag filter gain				
		REAL32	Ktrflpf;							// T-REF filter						

	} A;

	struct    // taskB : Constant definition for torque filter
	{												

		REAL32	Knxf[3];							// Notch filter gain						
		REAL32	Klpf2[4];							// Second order lag filter gain				
		REAL32	Ktrflpf;							// T-REF filter
		REAL32	Klpf3;								// First-order lag filter gain (after torque compensation)

	} P;

	struct    // Variable data definition for torque filter
	{											

		REAL32	FilOut;								// Torque filter output						
		REAL32	FilOut3;							// Torque filter output (after torque compensation)			
		REAL32	NxFil[3];							// For notch filter calculation
		REAL32	LpFil2[4];							// For secondary delay filter calculation
		UINT8	TrqFilClrCmd;						// Torque command filter integral initialization command
		UINT8	TrqFilDisable;						// Torque command filter invalid
	} V;
} TRQFIL;


/*--------------------------------------------------------------------------------------------------*/
/*		 Encoder interface Struct 		    		            								    */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{
	struct
	{
            INT32        EncType;                // encoder type
            INT32        EncPPR;                 // encoder pulses per revolution
            INT32		 MechAngleOffset;        // mechanical angle offset(0--PulseMax)
		    UINT16		 PhaseOffset;            // electrical angle offset(0--65536)/360 deg
            UINT16       AbzEncLine;
            UINT16       EncBitS;                // encoder single-turn bit number
            UINT16       EncBitM;                // encoder multi-turn bit number
            UINT16       EncSftBitNum;           // encoder shift bit number
            UINT16       PolePairs;	             // pole pairs number
            REAL32       EncPolePairs;           // encoder pluse per PolePairs
            UINT16       DataLen;
            UINT16       AlignLen;
            INT32        EncZeroOffset;   
            
            UINT16       EncExeNum;
            UINT16       EncUsersftBitS;                // User encoder single-turn bit number

			#define 	 ENCAMXINCRATIO	   50           // max encoder pulse increment ratio
            UINT16       EncUserBitSwNum;				// 
			UINT32       EncMaxPluseInc;                //  Enc Max Pluse Increment [cnt/PS_CYCLENS]
			BOOL		 EncIacEnable			        //  Enc Inc Abnormal compensate Enable
	}P;

	struct
	{
            UINT32	 MechAngle;		     // mechanical angle (0--PulseMax)
            UINT16	 ElecAngle;		         // electrical angle (0--SinUnit)
            INT32        MotPos;

            BOOL         PhaseReady;             // motor phase ready(phase found finish)
            BOOL         EncConnect;
            BOOL         EncConnectLast;
            BOOL         AbsPosEncConnect;  
            BOOL         bDataLost;
            BOOL         EncReset;         
            
            INT32        SingleTurn;
            INT32	     LastSingleTurn; 
            INT32        LastSingleTurnReal;
            INT32        DeltaSingleTurn;
            INT32        MultiTurn;
            UINT16       AbsAlmCode;
            UINT16       AbzPulse;
            INT32        LastAbzPulse;
            UINT8        AbsRecvBuf[ABS_MAX_FRAME_LENGTH];
            UINT32       AbsRecvRdy;
            UINT16       ComErrCnt;

            BOOL         ExCmdEn;               // Extended encoder command enable
            UINT16	 ExCmd;	            // Extended encoder command 
            BOOL	 MultClearBit;
            UINT16       EncCombine;
            UINT8        CrcErr;
            UINT8        RxCCErr;
            UINT32       RxStatus;
            
            UINT16       EncExeCNT;
            UINT32       LostLink;
            UINT32       CrcErrCnt;			            
	    INT32        SfSingleTurn;  
		  INT32        EncZeroOffFlag;	
		  UINT16		   EncIncAbnormFlag;
		  UINT16		   EncIncAbnormCnt;	
                  INT32     DeltaAccTurn;
	}V;
    
	struct
	{
            INT32        MotPos;
            UINT16       AbzPulse;
            INT32        LastAbzPulse;
            BOOL         ExCmdEn;           // Extended encoder command enable
            UINT16	     ExCmd;	            // Extended encoder command   
	}V1;            
            
}ENCODER;

/*--------------------------------------------------------------------------------------------------*/
/*		 Base Loops Struct 		    		            								            */
/*--------------------------------------------------------------------------------------------------*/
typedef struct
{       
    BPRMDAT           *Bprm;                            // base parameter
    BASE_CTRL         *BaseCtrls;                       // base control
	GSELGAINS         *GseGains;                        // gain select
	MOTSPDMAFIL       *MotSpdMaFil;                     // motor speed moving average filter
	MOTSPDMAFIL       *MotSpdMaFil1ms;                     // motor speed moving average filter         
	ENCODER           *Enc;                             // encoder module
        ENCODER           *Enc2;                             // encoder module
    ALARM             *AlmMngr;                         // alarm manager
        WEAKFV         	  WeakFV;                           // Weaken Field Control
	CUR_LOOP          CurLoop;                          // current loop
	SPD_LOOP          SpdLoop;                          // speed loop
	POS_LOOP          PosLoop;                          // position loop
	TRQFIL            TrqFil;                           // torque filter
	
	UINT16            CtrlMode;                         // control mode
	UINT16            LastCtrlMode;                     // last control mode
	UINT16            ZCtrlMode;                        // Zero speed control mode
    UINT16            FnCmnCtrlSvon;                    // 1:FnCmnCtrl Serve on
        UINT16            UseEncoder2;                      // control mode
	
	INT32             dMotPos;                          // delta motor position(pulse unit)
	INT32             MotPos;                           // motor position(pulse unit)
        INT32             Enc1MotPos;                       // motor position(pulse unit)
	INT32 			  SigleTurn;       					// encoder single turn(pulse unit)    
	REAL32            MotSpd;                           // motor speed[2^24/OvrSpd]	
	REAL32            MotSpdCnts;                       // motor speed[cnt/s]
	
	INT32             PosRef;                           // position reference
	INT32             dPcmda;
	INT32             dPosRef;                          // position reference
	INT32             PosFdb;                           // position feedback
	INT32             dPosFdb;                          // delta position feedback
	REAL32            SpdRefP;							// speed reference form position loop[2^24/OvrSpd]
	
	REAL32			  SpdRef;                           // speed reference[2^24/OvrSpd]
	REAL32			  SpdRefSum;                        // speed reference�iRef+FF�j[2^24/OvrSpd]
	REAL32            SpdFdb;                           // speed feedback[2^24/OvrSpd]
	REAL32            SpdObsFdb;                        // speed feedback observation value
	REAL32            TrqRefV;                          // torque reference from speed loop
	
	BOOL			  SpdPctrlFlag;

	REAL32            SpdFFC;                           // speed feedforward compensation
	REAL32            SpdFBC;                           // speed feedback compensation
	REAL32            TrqFFC;                           // torque feedforward compensation
	REAL32            TrqFBC;                           // torque feedback compensation
	REAL32            SpdFFCLatch;                       // speed feedforward compensation latch
	REAL32            TrqFFCLatch;                       // speed feedforward compensation latch
	REAL32			  TrqPosComp;

	REAL32            WeakRef;                          // weak field reference
	REAL32            TrqRef;                           // torque reference
	REAL32            TrqRefo;                          // torque reference after torque filter
	REAL32            TrqRefoLmt;                       // torque limit for TrqRefo
	REAL32            TrqRefoComp;                      // torque reference after compensation
	BOOL              TrqLitFlag;


	REAL32			  TrqFdbANm;					    // Motor torque feedback	Nm
	REAL32			  TrqFdbBNm;					    // load torque feedback	Nm
	REAL32            IpdSpringTrqNm;					// spring torque reference	Nm	
	REAL32            IpdDampTrqNm;						// damp torque reference	Nm
	REAL32            IpdTrqFeedforwardNm;				// feedforward torque reference	Nm	
        REAL32            IpdTrqRef;                                          //  torque reference	Nm	
        
	INT32             IpdPosFdb;						// load position feedback  cnt
	INT32             IpdSpdFdb;						// load speed feedback     cnt/s 		
	REAL32            IpdPosFdbRad;						// load position feedback  rad
	REAL32            IpdSpdFdbRads;				    // load speed feedback     rad/s 	
	REAL32            IpdPosErrRad;						// load position target rad
	REAL32            IpdSpdErrRads;				    // load speed target rad/s
	REAL32            IpdTrqFdbNm;						// load torque feedback Nm

	REAL32            IpdPosSfNLimit;					// load position soft limit	 rad
	REAL32            IpdPosSfPLimit;					// load position soft limit	 rad
	REAL32            IpdSpdSfLimit;					// load speed soft limit	 rad/s
	REAL32            IpdTrqSfLimit;					// load torque soft limit	 Nm
    REAL32            IpdSfLimitKp;				        // load position soft limit	 rad
    REAL32            IpdSfLimitKv;			         	// load speed soft limit	 rad/s


	REAL32            IqRef;                            // Iq reference form torque control
	REAL32            IdRef;                            // Id reference form torque control

	REAL32            VdRefVF;                          // Vd reference for VF control
	REAL32            VqRefVF;                          // Vq reference for VF control
    UINT16            ElecAngleVF;                      // ElecAngle reference for VF control [0-65536]/360deg

	REAL32            Idc;                              // DC current
    REAL32            Vdc;                              // DC voltate
    REAL32            VdcFilo;                           // DC voltate    
	REAL32            DebugVar1;
    REAL32            DebugVar2;
    REAL32            DebugVar3;
        REAL32            DebugVar4;
    BOOL              DymcPid ;                          // 0: do not use  1:use 
    BOOL              FunTqrCurType;                     // 0: D Axis Current 1:Q Axis Currenr
    BOOL              DcSyncMask;                        // 0: use dc  sync   1:do not user     
    BOOL              CurTiType;           
   BOOL              FunVoltFF;  
                            
     
    INT32           MotSpd1ms;                    // motspd 1ms cal  
    INT32           MotSpd1msFil;                    // motspd 1ms cal      
    INT32           dMotPos1msSum;
    UINT16          MotSpd1msCnt;
    BOOL            MotSpdHr;  
    BOOL            FuncCurLimit;
    BOOL            CurrentTest;
}BASE_LOOP;

PUBLIC void BaseLoopsInit(BASE_LOOP *BaseLoops);
PUBLIC void BaseLoopsExec( BASE_LOOP *BaseLoops);
PUBLIC void VC_BaseLoopsExec( BASE_LOOP *BaseLoops);
PUBLIC REAL32 GearRatioCal(BASE_LOOP *BaseLoopss, REAL32 In );
PUBLIC REAL32 GearRatioInvCal(BASE_LOOP *BaseLoops, REAL32 In );
#endif /* _BASE_LOOPS_H_ */
