/****************************************************************************************************
 *
 * FILE NAME:  HardApi.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.10
 *
 * AUTHOR:      luoly
 *
 * History:
 ****************************************************************************************************
	10-05-2019 Version 1.00 : Created by luoly
****************************************************************************************************/
#include "hal_data.h"
#include "bsp_enc.h"
#include "bsp_uart.h"
#include "bsp_timer.h"

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 配置绝对式编码器用SCI3(编码器轴1)和SCI2(编码器轴2)
 * 1. 2.5M波特率，8数据位+无校验+1停止位，开启FIFO，无任何中断使能
 * 2. 启用了硬件DE功能
 *
 * @param axis_num - 编码器轴号， 1-1号编码器轴外设(SCI3)，2-2号编码器轴外设(SCI2)
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci23_init_encoder(uint32_t buadrate, uint8_t axis_num)
{
    /* SCIx外设基地址 */
    R_SCI0_Type * regBase = ((R_SCI0_Type *) ( (axis_num == 0) ? R_SCI3_BASE : R_SCI2_BASE ) );

    /* SCIx通道 */
    uint32_t channel = (axis_num == 0) ? 3UL : 2UL;

    /* Cancel SCI0 module stop state */
    R_BSP_RegisterProtectDisable(BSP_REG_PROTECT_LPC_RESET);
    R_BSP_MODULE_START(FSP_IP_SCI, channel);
    R_BSP_RegisterProtectEnable(BSP_REG_PROTECT_LPC_RESET);

    /* 禁止中断 */
//    R_BSP_IrqDisable( ((channel == 3) ? VECTOR_NUMBER_SCI3_ERI : VECTOR_NUMBER_SCI2_ERI) );
//    R_BSP_IrqDisable( ((channel == 3) ? VECTOR_NUMBER_SCI3_RXI : VECTOR_NUMBER_SCI2_RXI) );
//    R_BSP_IrqDisable( ((channel == 3) ? VECTOR_NUMBER_SCI3_TXI : VECTOR_NUMBER_SCI2_TXI) );
//    R_BSP_IrqDisable( ((channel == 3) ? VECTOR_NUMBER_SCI3_TEI : VECTOR_NUMBER_SCI2_TEI) );

    /* Clear transmit/receive enable bits */
    regBase->CCR0_b.TE = 0U;
    regBase->CCR0_b.RE = 0U;

    /* CCR0默认值 */
    regBase->CCR0 = 0x00000000U;

    /* Reset transmit/receive FIFO data register operation */
    regBase->FCR_b.TFRST = 1U;
    regBase->FCR_b.RFRST = 1U;

    /* Read and clear status flags */
    regBase->CFCLR = 0UL;
    regBase->FFCLR = 0UL;

    /* Set transmission/reception format */

    regBase->CCR1 = 0x00000000;
    /* TXD引脚在TE为0时的电平，0-低电平，1-高电平(CCR1.bit12是取反意思) */
    regBase->CCR1_b.SPB2DT = 1;
    /* TXD引脚在TE为0时的输出是否受SPB2DT控制，1-使能控制 */
    regBase->CCR1_b.SPB2IO = 1;
    /* TXD输出引脚电平取反 */
    regBase->CCR1_b.TINV = 0;
    /* RXD输入引脚电平取反 */
    regBase->CCR1_b.RINV = 0;
    /* RXD输入数字滤波时钟选择 */
    regBase->CCR1_b.NFCS = 0;
    /* RXD输入数字滤波使能，1-使能 */
    regBase->CCR1_b.NFEN = 0;

    /* 复位默认值 */
    regBase->CCR2 = 0xFF00FF04;

    /* 位速率调制功能/波特率小数分频功能使能，1-使能 */
    regBase->CCR2_b.BRME = 1;
    /* 0-禁止外部时钟 */
    regBase->CCR2_b.ABCSE = 0;
    /* 波特率寄存器设置, 输入时钟为96MHz
     * PCLKSCIx = 96MHz
     * BBR = ( (PCLKSCIx * 10000000) / (64 * 2^(2*n-1) * B) ) - 1
     * BBR  : 寄存器设定值
     * B    : 波特率值
     * n    : 时钟源分频设定值
     * 计算公式基于一个bit采样时钟16个，单速率模式下
     */
    /* 波特率生成双速率模式使能，1-使能 */
    regBase->CCR2_b.BGDM = 1;
    /* 0-16个时钟发送一个bit，1-8个时钟发送一个bit */
    regBase->CCR2_b.ABCS = 1;
    /* 波特率计数器 */
    regBase->CCR2_b.BRR = 2;
    /* 输入时钟源分频选择，0-不分频，1-4分频，2-16分频，3-64分频 */
    regBase->CCR2_b.CKS = 0;
    /* 调制占空比设置 */
    regBase->CCR2_b.MDDR = 160;

    /* 复位默认值 */
    regBase->CCR3 = 0x00001203;
    /* 字符长度，0/1-9bit，2-8bit，3-7bit */
    regBase->CCR3_b.CHR = 2;
    /* 0-MSB先发送，1-LSB先发送 */
    regBase->CCR3_b.LSBF = 1;
    /* 1-接收/发送数据反相使能 */
    regBase->CCR3_b.SINV = 0;
    /* 0-低电平作为起始位检测，1-下降沿作为起始位检测 */
    regBase->CCR3_b.RXDESEL = 1;
    /* 0-串口通讯模式 */
    regBase->CCR3_b.MOD = 0;
    /* 1-FIFO功能使能 */
    regBase->CCR3_b.FM = 1;


    /* 1-RS485控制引脚驱动使能 */

    /* 测试发现起始位延时设定值和手册描述不符 */
    /* 停止位延时和手册一致，在目前的波特率设定下，1-50ns，5-250ns延迟，每个bit需要8个时钟周期，每个时钟周期是50ns的，延迟时间按照50ns为单位 */
    /* 起始位设定测试 1-1940ns，2-之后的数值也是按照50ns单位计算，但是会有很大的抖动，测试设定值2时，最短的延迟是100ns，最长延迟有450ns
     * 测试设定值3时，最短的延迟是150ns，最长延迟有500ns */
    regBase->DCR_b.DEPOL = 0;   // 极性选择 0-发送数据时低电平
    regBase->DCR_b.DEAST = 16;   // DE输出有效到数据起始位之间的延迟时间
    regBase->DCR_b.DENGT = 6;   // 数据停止位到DE无效之间的延迟时间
    regBase->CCR3_b.DEN = 1;    // DE端口控制功能使能

    /* 奇偶校验使能，1-使能*/
    regBase->CCR1_b.PE = 0;
    /* 0-偶校验(enen)，1-奇校验(odd) */
    regBase->CCR1_b.PM = 0;
    /* 0-1位停止位，1-2位停止位 */
    regBase->CCR3_b.STP = 0;

    /* 复位默认值 */
    regBase->CCR4 = 0x00000000;

    /* Wait for at least 1-bit interval */
    R_BSP_SoftwareDelay(10, BSP_DELAY_UNITS_MICROSECONDS);

    /* Set FIFO trigger conditions */

    /* FIFO控制寄存器复位默认值 */
    regBase->FCR = 0x00000000;
    /* FIFO发送缓存触发中断设定值 */
    regBase->FCR_b.TTRG = 0;
    /* FIFO接收缓存触发中断设定值 */
    regBase->FCR_b.RTRG = 0;

    /* Disable transmit/receive FIFO data register reset operation */
    regBase->FCR_b.TFRST = 1U;
    regBase->FCR_b.RFRST = 1U;

    /* 清除标志位 */
    regBase->CFCLR = 0xBD070010;
    regBase->FFCLR = 0x00000001;

    /* 清除挂起中断 */
//    R_BSP_IrqClearPending( ((channel == 3) ? VECTOR_NUMBER_SCI3_ERI : VECTOR_NUMBER_SCI2_ERI) );
//    R_BSP_IrqClearPending( ((channel == 3) ? VECTOR_NUMBER_SCI3_RXI : VECTOR_NUMBER_SCI2_RXI) );
//    R_BSP_IrqClearPending( ((channel == 3) ? VECTOR_NUMBER_SCI3_TXI : VECTOR_NUMBER_SCI2_TXI) );
//    R_BSP_IrqClearPending( ((channel == 3) ? VECTOR_NUMBER_SCI3_TEI : VECTOR_NUMBER_SCI2_TEI) );

    /* 仅配置中断优先级 */
//    R_BSP_IrqCfg( ((channel == 3) ? VECTOR_NUMBER_SCI3_ERI : VECTOR_NUMBER_SCI2_ERI), 12UL, (NULL) );
//    R_BSP_IrqCfg( ((channel == 3) ? VECTOR_NUMBER_SCI3_RXI : VECTOR_NUMBER_SCI2_RXI), 12UL, (NULL) );
//    R_BSP_IrqCfg( ((channel == 3) ? VECTOR_NUMBER_SCI3_TXI : VECTOR_NUMBER_SCI2_TXI), 12UL, (NULL) );
//    R_BSP_IrqCfg( ((channel == 3) ? VECTOR_NUMBER_SCI3_TEI : VECTOR_NUMBER_SCI2_TEI), 12UL, (NULL) );

    /* 使能GIC级中断 */
//    R_BSP_IrqEnable( ((channel == 3) ? VECTOR_NUMBER_SCI3_ERI : VECTOR_NUMBER_SCI2_ERI) );
//    R_BSP_IrqEnable( ((channel == 3) ? VECTOR_NUMBER_SCI3_RXI : VECTOR_NUMBER_SCI2_RXI) );
//    R_BSP_IrqEnable( ((channel == 3) ? VECTOR_NUMBER_SCI3_TXI : VECTOR_NUMBER_SCI2_TXI) );
//    R_BSP_IrqEnable( ((channel == 3) ? VECTOR_NUMBER_SCI3_TEI : VECTOR_NUMBER_SCI2_TEI) );

    /* 使能外设级中断 */
//    regBase->CCR0_b.RIE = 1U;    /* 接收中断 */
//    regBase->CCR0_b.TIE = 1U;    /* 发送空中断 */
//    regBase->CCR0_b.TEIE = 1U;   /* 发送结束中断 */

    /* 使能接收及使能发送 */
    regBase->CCR0_b.TE = 1U;
    regBase->CCR0_b.RE = 1U;
    while(1U != regBase->CCR0_b.TE)
    {
        ;
    }

}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 编码器用SCI3(编码器轴1)和SCI2(编码器轴2) 发送指令
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci2_3_transmit(uint8_t axis_num ,uint16_t tx_num, uint8_t *pdata)
{
    uint16_t count = 0;
    
    R_SCI0_Type *regBase = ((R_SCI0_Type *) ( (axis_num == 0) ? R_SCI3_BASE : R_SCI2_BASE ) );  
    
    // 清除错误标志位
    regBase->CFCLR |= SCI_UART_CFCLR_ALL_FLAG_CLEAR;
    
    // 复位接收fifo寄存器
    regBase->FCR_b.TFRST = 1;
    // 写fifo数据
    while(tx_num > 0 && count < _SCIF_FIFO_MAX_SIZE)
    {
      regBase->TDR_b.TDAT = *pdata;
      pdata++;
      count++;
      tx_num--;
    }
    
    
    regBase->CCR0_b.TE = 1U;    /* 使能发送 */
//    regBase->CCR0_b.RE = 1U;    /* 使能发送 */
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * 编码器用SCI3(编码器轴1)和SCI2(编码器轴2) 读取fifo数据
 * RETURNS:
 *
****************************************************************************************************/
uint8_t bsp_sci2_3_receive(uint8_t axis_num, uint16_t rx_num, uint8_t *pdata)
{   
    uint8_t state = 0;
    
    R_SCI0_Type *regBase = ((R_SCI0_Type *) ( (axis_num == 0) ? R_SCI3_BASE : R_SCI2_BASE ) );  
    
    if(regBase->FRSR_b.R  != rx_num )
    {
      state = 1;
    }
  
    // 清除错误标志位
    regBase->CFCLR |= SCI_UART_CFCLR_ALL_FLAG_CLEAR;
    
    uint16_t dummy_fdr = regBase->FRSR_b.R;
  
    while(dummy_fdr > 0)
    {
      *pdata = regBase->RDR_b.RDAT;
      pdata++;
      dummy_fdr= dummy_fdr -1;
    }  
     
    /* 重置接收fifo */
    regBase->FCR_b.RFRST = 1;     
    return state; 
}

/****************************************************************************************************
 * DESCRIPTION: 
 * @ timeout  [us]
 * 编码器用SCI3(编码器轴1)和SCI2(编码器轴2) 读取fifo数据
 * RETURNS:
 *
****************************************************************************************************/
void bsp_sci2_3_transmit_timeout(uint16_t axis_num ,uint16_t tx_num, uint8_t *pdata, uint16_t timeout)
{
    uint16_t count = 0;
    uint32_t starttimer = 0;
    uint32_t timecnt = (uint32_t)(timeout*CMTW1_PPR_US);
    uint32_t ilaspTime = 0;
    
    R_SCI0_Type *regBase = ((R_SCI0_Type *) ( (axis_num == 0) ? R_SCI3_BASE : R_SCI2_BASE ) );  
    
    // 清除错误标志位
    regBase->CFCLR |= SCI_UART_CFCLR_ALL_FLAG_CLEAR;
    
    // 复位接收fifo寄存器
    regBase->CCR0_b.TE = 0U;    /* 禁能发送 */
    regBase->CCR0_b.RE = 0U;    /* 禁能发送 */    
    regBase->FCR_b.TFRST = 1;
    regBase->FCR_b.RFRST = 1;   
    regBase->CCR0_b.RE = 1U;    /* 禁能发送 */    
    
    // 写fifo数据
    while(tx_num > 0 && count < _SCIF_FIFO_MAX_SIZE)
    {
      regBase->TDR_b.TDAT = *pdata;
      pdata++;
      count++;
      tx_num--;
    }
    
    
    regBase->CCR0_b.TE = 1U;    /* 使能发送 */
    
    starttimer = bsp_gettimercnt();
    
    while(regBase->CSR_b.TEND != 1)
    {
      // do nothing
      ilaspTime = bsp_gettimercnt() - starttimer;
      if(ilaspTime > timecnt)
      {
        break;
      }
    } 
}


/****************************************************************************************************
 * DESCRIPTION: 
 * @ timeout  [us]
 * 编码器用SCI3(编码器轴1)和SCI2(编码器轴2) 读取fifo数据
 * RETURNS:
 *
****************************************************************************************************/
uint8_t bsp_sci2_3_receive_timeout(uint16_t axis_num ,uint16_t rx_num, uint8_t *pdata, uint16_t timeout)
{
    uint8_t state = 0;
    uint16_t count = 0;
    uint32_t starttimer = 0;
    uint32_t timecnt = (uint32_t)(timeout*CMTW1_PPR_US);
    uint32_t ilaspTime = 0;
    R_SCI0_Type *regBase = ((R_SCI0_Type *) ( (axis_num == 0) ? R_SCI3_BASE : R_SCI2_BASE ) );  
    
    starttimer = bsp_gettimercnt();
    
    while(regBase->FRSR_b.R != 4)
    {
      ilaspTime = bsp_gettimercnt() - starttimer;
      
      if(ilaspTime > timecnt)
      {
        state = 1; 
        break;
      }
    }
    
    // 清除错误标志位
    regBase->CFCLR |= SCI_UART_CFCLR_ALL_FLAG_CLEAR;
    
    uint16_t dummy_fdr = regBase->FRSR_b.R;
    uint16_t dummy = 0;
    if(state == 0)
    {
      while(rx_num > 0)
      {
        *pdata = regBase->RDR_b.RDAT;
        pdata++;
        rx_num= rx_num -1;
      }  
    }
    else
    {
      while(dummy_fdr > 0)
      {
        dummy = regBase->RDR_b.RDAT;
        dummy_fdr= dummy_fdr -1;
      }        
    }
     
    /* 重置接收fifo */
    regBase->CCR0_b.TE = 0U;    /* 禁能发送 */
    regBase->CCR0_b.RE = 0U;    /* 禁能发送 */    
    regBase->FCR_b.RFRST = 1;   
    regBase->CCR0_b.RE = 1U;    /* 使能发送 */  
    return state; 
    
}
/****************************************************************************************************
 * DESCRIPTION: 
 *
 * @param mode      - 计数模式 0-正交计数，1-脉冲+方向(上升沿)，2-脉冲+方向(下降沿)，3-双脉冲(上升沿计数)，4-双脉冲(下降沿计数)
 * @param period    - 计数的最大值
 *
 *                 默认是通道1作为正交脉冲输入,  默认是通道2作为增量编码器 
****************************************************************************************************/
void bsp_mtu1_init(uint8_t mode, uint32_t period)
{
    /* 停止定时器MTU1 */
    R_MTU->TSTRA_b.CST1 = 0;

    /* MTU0定时器计数时钟为 PCLKH/1, 计数边沿为时钟的上升沿, 定时器计数器TCNT清零在TGRA比较匹配时 */
    R_MTU1->TCR_b.TPSC = 0x00;      /* 计数模式时，设定值无效 */
    R_MTU1->TCR_b.CKEG = 0x00;      /* 计数模式时，设定值无效 */
    R_MTU1->TCR_b.CCLR = 0x00;      /* 0-禁止清零， 1-TGRA比较清零，2-TRGB比较清零 */
    R_MTU1->TCR2 = 0x00;

    /* 定时器计数器设置为0 */
    R_MTU1->TCNT = 0x0000;

    /* 定时器通用寄存器(用于设定通道比较值) */

    /* 周期设定值 */
    R_MTU1->TGRA = (uint16_t)(period - 1);

    /* MTU1/MTU2级联功能禁止 */
    R_MTU1->TMDR3_b.LWA = 0;
    /* MTU1使用MTCLKA和MTCLKB两个引脚作为计数引脚 */
    R_MTU1->TMDR3_b.PHCKSEL = 0;

    /* 计数模式设置 */

    if (mode == 1)
    {
        /* 脉冲+方向(上升沿)：计数模式2 */
        R_MTU1->TMDR1_b.MD = 0x5;
        /* 计数模式2下，0-下降沿计数，1-上升沿计数，2/3-上升及下降沿均计数 */
        R_MTU1->TCR2_b.PCB = 1;
    }
    else if (mode == 2)
    {
        /* 脉冲+方向(下降沿)：计数模式2 */
        R_MTU1->TMDR1_b.MD = 0x5;
        /* 计数模式2下，0-下降沿计数，1-上升沿计数，2/3-上升及下降沿均计数 */
        R_MTU1->TCR2_b.PCB = 0;
    }
    else if (mode == 3)
    {
        /* 双脉冲(上升沿计数)：计数模式3 */
        R_MTU1->TMDR1_b.MD = 0x6;
        /* 计数模式2下，0-下降沿计数，1-上升沿计数，2/3-上升及下降沿均计数 */
        R_MTU1->TCR2_b.PCB = 1;
    }
    else if (mode == 4)
    {
        /* 双脉冲(下降沿计数)：计数模式3 */
        R_MTU1->TMDR1_b.MD = 0x6;
        /* 计数模式2下，0-下降沿计数，1-上升沿计数，2/3-上升及下降沿均计数 */
        R_MTU1->TCR2_b.PCB = 0;
    }
    else
    {
        /* 用于增量式编码器 正交计数模式：计数模式1 */ 
        R_MTU1->TMDR1_b.MD = 0x4;
    }

    /* 启动计数器 */
    R_MTU->TSTRA_b.CST1 = 1;
}

/****************************************************************************************************
 * DESCRIPTION: 
 *
 * @param mode      - 计数模式 0-正交计数，1-脉冲+方向(上升沿)，2-脉冲+方向(下降沿)，3-双脉冲(上升沿计数)，4-双脉冲(下降沿计数)
 * @param period    - 计数的最大值
 *
 *                  默认是通道1作为正交脉冲输入,  默认是通道2作为增量编码器 
****************************************************************************************************/
void bsp_mtu2_init(uint8_t mode, uint32_t period)
{

    /* 停止定时器MTU2 */
    R_MTU->TSTRA_b.CST2 = 0;

    /* MTU0定时器计数时钟为 PCLKH/1, 计数边沿为时钟的上升沿, 定时器计数器TCNT清零在TGRA比较匹配时 */
    R_MTU2->TCR_b.TPSC = 0x00;      /* 计数模式时，设定值无效 */
    R_MTU2->TCR_b.CKEG = 0x00;      /* 计数模式时，设定值无效 */
    R_MTU2->TCR_b.CCLR = 0x00;      /* 0-禁止清零， 1-TGRA比较清零，2-TRGB比较清零 */
    R_MTU2->TCR2 = 0x00;

    /* 定时器计数器设置为0 */
    R_MTU2->TCNT = 0x0000;

    /* 定时器通用寄存器(用于设定通道比较值) */

    /* 周期设定值 */
    R_MTU2->TGRA = (uint16_t)(period - 1);

    /* MTU1/MTU2级联功能禁止 */
    R_MTU1->TMDR3_b.LWA = 0;
    /* MTU2使用MTCLKC和MTCLKD两个引脚作为计数引脚 */
    R_MTU1->TMDR3_b.PHCKSEL = 1;

    /* 计数模式设置 */

    if (mode == 1)
    {
        /* 脉冲+方向(上升沿)：计数模式2 */
        R_MTU2->TMDR1_b.MD = 0x5;
        /* 计数模式2下，0-下降沿计数，1-上升沿计数，2/3-上升及下降沿均计数 */
        R_MTU2->TCR2_b.PCB = 1;
    }
    else if (mode == 2)
    {
        /* 脉冲+方向(下降沿)：计数模式2 */
        R_MTU2->TMDR1_b.MD = 0x5;
        /* 计数模式2下，0-下降沿计数，1-上升沿计数，2/3-上升及下降沿均计数 */
        R_MTU2->TCR2_b.PCB = 0;
    }
    else if (mode == 3)
    {
        /* 双脉冲(上升沿计数)：计数模式3 */
        R_MTU2->TMDR1_b.MD = 0x6;
        /* 计数模式2下，0-下降沿计数，1-上升沿计数，2/3-上升及下降沿均计数 */
        R_MTU2->TCR2_b.PCB = 1;
    }
    else if (mode == 4)
    {
        /* 双脉冲(下降沿计数)：计数模式3 */
        R_MTU2->TMDR1_b.MD = 0x6;
        /* 计数模式2下，0-下降沿计数，1-上升沿计数，2/3-上升及下降沿均计数 */
        R_MTU2->TCR2_b.PCB = 0;
    }
    else
    {
        /* 用于增量式编码器 正交计数模式：计数模式1 */
        R_MTU2->TMDR1_b.MD = 0x4;
    }

    /* 启动计数器 */
    R_MTU->TSTRA_b.CST2 = 1;
}

/****************************************************************************************************
 * DESCRIPTION: 
 * 
 * @return       
 *   
 *                 
****************************************************************************************************/
uint16_t bsp_opmtu1_cnt(uint8_t operation)
{

  if(operation == 1)
  {
    // 清除多圈值计数值
     R_MTU1->TCNT = 0; 
  }
  return  R_MTU1->TCNT;
}
/****************************************************************************************************
 * DESCRIPTION:  
 * @ operation   0: get cnt   1: clear cnt 
 * @ return       
 *   
 *                 
****************************************************************************************************/
uint16_t bsp_opmtu2_cnt(uint8_t operation)
{
  if(operation == 1)
  {
    // 清除多圈值计数值
     R_MTU2->TCNT = 0; 
  }
  return  R_MTU2->TCNT;
}