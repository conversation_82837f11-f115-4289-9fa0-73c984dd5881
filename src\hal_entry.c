#include "hal_data.h"
#include "bsp.h"
#include "Global.h"
#include "ecat_def.h"
#include "ecatappl.h"
#include "applInterface.h"
#include "app.h"
#include "ComUart.h"
#include "flash_if.h"
//#include "ecat_eeprom.h"
#include "modbus.h"
#include "cmd_otp.h"
#include "canfestival.h"
#include "canfd_protocol.h"
#include "mm.h"
#include "fundefine.h"
#include "bsp_spi.h"
#include "bsp_can.h"
#include "common.h"


FSP_CPP_HEADER
void R_BSP_WarmStart(bsp_warm_start_event_t event) BSP_PLACE_IN_SECTION(".warm_start");
FSP_CPP_FOOTER


PUBLIC volatile BOOL  gSystemInitDone = FALSE;
PUBLIC void RpiRoundMain( AXIS_HANDLE *AxisRscR );
PUBLIC void Bus_Com_init( AXIS_HANDLE *AxisRscR );
PUBLIC void Uid_Check(void);
void user_app_init(void);

static void EeppromVersion_Get(void);
char MemoryMalloc[2048]={0};

UINT16   gEepromVersion;
UINT32  Uid_Check_Result = 0;

extern CO_Data Object_Data;


void spi_callback (spi_callback_args_t * p_args);
static volatile bool g_spi_master_transfer_complete = false;

volatile uint32_t cpu_registers_LR;
volatile uint32_t cpu_registers_PC;
volatile uint32_t cpu_registers_CPSR;  
volatile uint32_t cpu_registers_SPSR; 
volatile uint32_t cpu_registers_DFSR;
volatile uint32_t cpu_registers_IFSR;
volatile uint32_t cpu_registers_DFAR;
volatile uint32_t cpu_registers_IFAR;
volatile uint32_t cpu_registers_ISR;
volatile uint32_t hard_fault_cnt = 0;

void exception_handler(void);
/*******************************************************************************************************************//**
 * main() is generated by the FSP Configuration editor and is used to generate threads if an RTOS is used.  This function
 * is called by main() when no RTOS is used.
 **********************************************************************************************************************/
void hal_entry(void)
{
  AXIS_HANDLE *AxisRscR;
  UINT8 SlaveID   = 0;
  
  /* TODO: add your own code here */

  user_bsp_init();
  
  
  
 /* memory pool initialization , for function MyMalloc */
  InitMm( (unsigned long)&MemoryMalloc[0], (unsigned long)&MemoryMalloc[2047]);     
  
  user_app_init();
   
  AxisRscR = (AXIS_HANDLE*)GetAxisHandle(0);
 
  while(1)
  {
    
      SlaveID =  AxisRscR->Prm->PnCfgPrm.DevID;
     #if NO_ETHERCAT 
      if(AxisRscR->Prm->PnCfgPrm.BusType & 0x01)
      {
        if(bRunApplication == TRUE)
        {
            MainLoop();
        }  
      }
      #endif
      #if NO_MODBUS
      if(AxisRscR->Prm->PnCfgPrm.BusType & 0x02)
      {
        ModBusTransmit(&ModbusStruct,SlaveID);
      }
      else if(AxisRscR->Prm->PnCfgPrm.BusType & 0x04)
      {
        // todo 
      }
      #endif
        
      RpiRoundMain( AxisRscR );   
      
      DataCollectSendLoop(AxisRscR,&gHmiHandle);
     
      UartSendData();
      

      firmware_process();
  }
}

/***********************************************************************************************************************
* Function Name: user_app_init
* Description  : This function adds user code before implementing main function.
* Arguments    : None
* Return Value : None
***********************************************************************************************************************/
void user_app_init(void)
{
  AXIS_HANDLE *AxisN;
  INT16       ax_id;
  
  AxisN = (AXIS_HANDLE*)GetAxisHandle(0);

  EeppromVersion_Get();

  hApi_TimerCounterInit();

  AxisGlobalsInitMain();
  
    /* Init Bus Com */
  Bus_Com_init(AxisN);
  
  Uid_Check();
  
  bsp_poe3_enable();
  
  hApi_delay_ms(10);
  
  bsp_pwm_start();
  
    
  while(gSystemInitDone == FALSE) ; 
    
}
/*******************************************************************************
* Function Name :Bus communication initialization 
* Description   : 
* Arguments     :             
* Return Value  : none
*******************************************************************************/
void Bus_Com_init(AXIS_HANDLE *AxisN)
{
  fsp_err_t err;
  UINT8  format = 0;
  UINT8  BuadRateLvl = 0;
  BOOL   Endian;
  
  #if NO_ETHERCAT
  bRunApplication = 0; 
  if(AxisN->Prm->PnCfgPrm.BusType & 0x01)
  {
    /* check ecat eeprom is empty */  
    Ecat_Eeprom_InitWrite();
    __disable_interrupt();
    __asm volatile ("isb");
    if(AxisN->Prm->PnAuxPrm.EscWrite == 4)
    {
        RM_ETHERCAT_SSC_PORT_Close(gp_ethercat_ssc_port->p_ctrl);
        hApi_delay_ms(1000);
    }
    /* Initialize EtherCAT SSC Port */
    err = RM_ETHERCAT_SSC_PORT_Open(gp_ethercat_ssc_port->p_ctrl, gp_ethercat_ssc_port->p_cfg);
    if(FSP_SUCCESS != err)
    {
        __BKPT(0);	/* Can't continue the stack */
        ALMSetGlobalAlarm(ALM_ECAT_INIT);
    }    
    __enable_interrupt();
    __asm volatile ("isb");

//    if(0 !=EcatWritePhyLedCtrlReg())
//    {
//       // todo alarm 
//       ALMSetGlobalAlarm(ALM_ECAT_INIT);
//    }
        /* Initilize the stack */
    MainInit();  
    /* create basic mapping */
    APPL_GenerateMapping(&nPdInputSize,&nPdOutputSize);
     
    /* set stack run flag */
    bRunApplication = TRUE;       
  }
  #endif

  #if NO_MODBUS
  if(AxisN->Prm->PnCfgPrm.BusType & 0x02)
  {
    /* Initialize modbus */
    BuadRateLvl = (AxisN->Prm->PnCfgPrm.BusFrame & 0x00FF);
    format      = (AxisN->Prm->PnCfgPrm.BusFrame & 0xFF00) >> 8;
    Endian      = (AxisN->Prm->PnCfgPrm.BusType  & (1<< 8)) >> 8;
    
    ModbusInit(&ModbusStruct,BuadRateLvl,format,Endian);   
  }  
  else 
  #endif
//  if(AxisN->Prm->PnCfgPrm.BusType & 0x04)
//  {
    CanInit(AxisN->Prm->PnCfgPrm.BusFrame); 
    // setNodeId(&Object_Data, AxisN->Prm->PnCfgPrm.DevID);
    // setState(&Object_Data, Initialisation); // Init the state
    Canfd_Slave_Init();
//  }
}


/*******************************************************************************
* Function Name :get  eerpom verson in software version 
* Description   : 
* Arguments     :             
* Return Value  : none
*******************************************************************************/
static void EeppromVersion_Get(void)
{
    UINT8 gEepromVStr[4];
    UINT16 wk = 0;
    UINT16  wk1 = 0;
    
    for(UINT8 i = 0;i < EEP_PV_LEN;i++)
    {
        gEepromVStr[i] = MCU_SOFTWARE_VERSION[EEP_PV_START + i];
    }
    
    for(UINT8 i = 0;i < EEP_PV_LEN;i++)
    {
        wk = (gEepromVStr[EEP_PV_LEN- i -1] - '0');
        wk1 = 1;
        if(i > 1)
        {
            for(UINT8 j = 0;j < i; j++)
                wk1 = wk1 * 16;
        }
        gEepromVersion += (wk*wk1);
    }
}
/*******************************************************************************
* Function Name :get  Uid_Check
* Description   : 
* Arguments     :             
* Return Value  : none
*******************************************************************************/
void Uid_Check(void)
{
    uint8_t uid[8] = {0};
    uint8_t uidInFlash[8] = {0};
    uint32_t  Uidflashaddr =  0x68008080;
    
    /* read uid flash */
    cmd_get_unique_id(&uid[0]);   
    
    /* read uid  flash */
    *(uint32_t *)&uidInFlash[0] = *(uint32_t *)Uidflashaddr;
    *(uint32_t *)&uidInFlash[4] = *(uint32_t *)(Uidflashaddr+4);
    
    for(uint8_t i = 0;i < 8;i++)
    {
      if(uid[i] != uidInFlash[i])
      {
         Uid_Check_Result = 1;
      }
    } 
}

/*******************************************************************************
* Function Name :get  Uid_Check
* Description   : 
* Arguments     :             
* Return Value  : none
*******************************************************************************/
void spi_callback (spi_callback_args_t * p_args)
{
//    if (SPI_EVENT_TRANSFER_COMPLETE == p_args->event)
//    {
//        g_spi_master_transfer_complete = true;
//    }
}


/*******************************************************************************************************************//**
 * This function is called at various points during the startup process.  This implementation uses the event that is
 * called right before main() to set up the pins.
 *
 * @param[in]  event    Where at in the start up process the code is currently at
 **********************************************************************************************************************/
void R_BSP_WarmStart (bsp_warm_start_event_t event)
{
    if (BSP_WARM_START_RESET == event)
    {
    	/* Pre clock initialization */
    }

    if (BSP_WARM_START_POST_C == event)
    {
        /* C runtime environment and system clocks are setup. */

        /* Configure pins. */
        R_IOPORT_Open(&g_ioport_ctrl, &g_bsp_pin_cfg);
    }
}

/*******************************************************************************************************************//**
 *  
 **********************************************************************************************************************/
void exception_handler(void)
{
    // initialize uart and timer 
    
    bsp_sci4_init_zx(115200,0); 
    bsp_timerCounter_init();
    uint8_t StringArray[11] = 0;

    asm volatile("cpsid I");     // Disabling interrupt
    asm volatile("isb");         // This ensure that above-mentioned setting be reflected to ARM 
    

    while (1)
    {
        Serial_PutString((uint8_t *)"\n\r ---------------Now is hard fault output messages----------------");

        Serial_PutString((uint8_t *)"\n\r LR   Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_LR);
        Serial_PutString(StringArray);

        Serial_PutString((uint8_t *)"\n\r PC   Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_PC);
        Serial_PutString(StringArray);

        Serial_PutString((uint8_t *)"\n\r CPSR Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_CPSR);
        Serial_PutString(StringArray);

        Serial_PutString((uint8_t *)"\n\r SPSR Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_SPSR);
        Serial_PutString(StringArray);

        Serial_PutString((uint8_t *)"\n\r DFSR Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_DFSR);
        Serial_PutString(StringArray);

        Serial_PutString((uint8_t *)"\n\r IFSR Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_IFSR);
        Serial_PutString(StringArray);

        Serial_PutString((uint8_t *)"\n\r DFAR Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_DFAR);
        Serial_PutString(StringArray);

        Serial_PutString((uint8_t *)"\n\r IFAR Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_IFAR);
        Serial_PutString(StringArray);

        Serial_PutString((uint8_t *)"\n\r ISR  Register Value:");
        snprintf(StringArray, 11, "0x%X", cpu_registers_ISR);
        Serial_PutString(StringArray);  
        hard_fault_cnt++;
    } 
}
