@echo off
REM Verifies a CRC32 checksum that has been appended to a binary file.
REM Leverages srec_cat.exe and srec_cmp.exe for CRC32 verification.

SETLOCAL EnableDelayedExpansion

REM Get the script directory and parent directory
SET "SCRIPT_DIR=%~dp0"
SET "PARENT_DIR=%SCRIPT_DIR%..\.."
SET "BINARY_DIR=%PARENT_DIR%\Binary"

REM Get current date in yyyy_mm_dd format using WMIC (more reliable across locales)
FOR /F "tokens=2 delims==" %%a IN ('wmic os get LocalDateTime /VALUE') DO SET "DATETIME=%%a"
SET "YYYY=%DATETIME:~0,4%"
SET "MM=%DATETIME:~4,2%"
SET "DD=%DATETIME:~6,2%"
SET "DATE_STR=%YYYY%%MM%%DD%"

REM Parse command line arguments
SET "INPUT_FILE="

IF "%~1" NEQ "" (
    SET "INPUT_FILE=%~1"
) ELSE (
    SET "INPUT_FILE=%BINARY_DIR%\MeteoriteX01_%DATE_STR%.bin"
    IF NOT EXIST "!INPUT_FILE!" (
        ECHO ERROR: No input file specified and default file not found.
        ECHO Looking for: !INPUT_FILE!
        ECHO Usage: crc_verify.bat [file_with_crc]
        EXIT /B 1
    )
)

ECHO -------------------------------------
ECHO Verifying CRC32 checksum in %INPUT_FILE%
ECHO -------------------------------------

REM **1. Configuration & Error Handling:**

SET "SREC_PATH=%SCRIPT_DIR%"
SET "SREC_CAT_EXE=%SREC_PATH%srec_cat.exe"
SET "SREC_CMP_EXE=%SREC_PATH%srec_cmp.exe"

ECHO Looking for tools at: %SREC_PATH%

IF NOT EXIST "%SREC_CAT_EXE%" (
    ECHO ERROR: srec_cat.exe not found at "%SREC_CAT_EXE%"
    EXIT /B 1
)

IF NOT EXIST "%SREC_CMP_EXE%" (
    ECHO ERROR: srec_cmp.exe not found at "%SREC_CMP_EXE%"
    EXIT /B 1
)

REM **2. Robust File Existence Check:**

IF NOT EXIST "%INPUT_FILE%" (
    ECHO ERROR: Input file "%INPUT_FILE%" not found.
    EXIT /B 2
)

REM **3. Determine File Size:**

FOR %%i IN ("%INPUT_FILE%") DO (
  SET "FILE_SIZE=%%~zi"
)

ECHO File size of "%INPUT_FILE%": %FILE_SIZE% bytes

REM Calculate the size of the file without the CRC32 (4 bytes less)
SET /A DATA_SIZE=%FILE_SIZE%-4

IF %DATA_SIZE% LEQ 0 (
    ECHO ERROR: File is too small to contain a valid CRC32 checksum.
    EXIT /B 3
)

ECHO Data size (excluding CRC32): %DATA_SIZE% bytes

REM **4. Extract the appended CRC32 value:**
SET "TEMP_CRC_FILE=%TEMP%\extracted_crc.bin"
SET "TEMP_DATA_FILE=%TEMP%\data_without_crc.bin"
SET "TEMP_CALC_CRC_FILE=%TEMP%\calculated_crc.bin"

ECHO Extracting appended CRC32...
"%SREC_CAT_EXE%" "%INPUT_FILE%" -Binary -crop %DATA_SIZE% %FILE_SIZE% -o "%TEMP_CRC_FILE%" -Binary

ECHO Extracting data portion...
"%SREC_CAT_EXE%" "%INPUT_FILE%" -Binary -crop 0 %DATA_SIZE% -o "%TEMP_DATA_FILE%" -Binary

ECHO Calculating CRC32 of data portion...
"%SREC_CAT_EXE%" "%TEMP_DATA_FILE%" -Binary -crop 0 %DATA_SIZE% -crc32-l-e %DATA_SIZE% -crop %DATA_SIZE% %FILE_SIZE% -o "%TEMP_CALC_CRC_FILE%" -Binary

ECHO Comparing CRC32 values...
"%SREC_CMP_EXE%" "%TEMP_CRC_FILE%" -Binary "%TEMP_CALC_CRC_FILE%" -Binary > nul 2>&1

IF %ERRORLEVEL% EQU 0 (
    ECHO CRC32 verification SUCCESSFUL: The file integrity is intact.
    SET "RESULT=0"
) ELSE (
    ECHO CRC32 verification FAILED: The file may be corrupted.
    SET "RESULT=4"
)

REM Clean up temporary files
DEL "%TEMP_CRC_FILE%" 2>nul
DEL "%TEMP_DATA_FILE%" 2>nul
DEL "%TEMP_CALC_CRC_FILE%" 2>nul

ENDLOCAL & EXIT /B %RESULT% 