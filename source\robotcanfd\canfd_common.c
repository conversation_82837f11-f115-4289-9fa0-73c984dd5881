
/**
 * @file canfd_common.c
 * @brief CANFD公共功能模块实现
 */

#include "canfd_common.h"

float uint_to_float(int x_int, float x_min, float x_max, int bits) {
     /// converts unsigned int to float, given range and number of bits /// 
     float span = x_max - x_min; float offset = x_min; 
     return ((float)x_int)*span/((float)((1<<bits)-1)) + offset; 
} 

//float转uint
unsigned int float_to_uint(float x, float x_min, float x_max, int bits){
    /// Converts a float to an unsigned int, given range and number of bits ///
    float span = x_max - x_min;
    float offset = x_min;
    return ((x-offset)*(float)((unsigned int)(1<<bits)-1)/span);
}
