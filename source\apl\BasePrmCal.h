/****************************************************************************************************
 *
 * FILE NAME:  BasePrmCal.h
 *
 * DESCRIPTION:
 *
 * CREATED ON:  2019.05.16
 *
 * AUTHOR:      Denny
 *
 * History:
 ****************************************************************************************************
	16-05-2019 Version 1.00 : Created by Denny
****************************************************************************************************/
#ifndef _BASE_PRM_CAL_H
#define	_BASE_PRM_CAL_H

#include "Global.h"

typedef struct 
{
	UINT16	jrate;                  // Pn205 : Inertia ratio
	UINT16	loophz;					// Pn203 : Speed loop gain									
	UINT16	pitime;					// Pn204 : Speed loop integration time constant									
	UINT16	loophz2;				// Pn206 : Speed loop gain 2								
	UINT16	pitime2;				// Pn207 : Speed loop integration time constant 2							
//	DBYTEX	ipg_rwt;				// Pnxxx : Lower: I-P control ratio, Upper: Runaway detection torque			
} BASE_SPDLOOP_PRM;

	

PUBLIC void BprmMotEncParamCal(BPRMDAT *Bprm, ENCODER *EncV, ENCODER *EncV2,MOTOR_IDENTIFY *MotIdt, PRMDATA *PnPrm);
PUBLIC void BprmMotIdentifyPrmCal(BPRMDAT *Bprm, MOTOR_IDENTIFY *MotIdt, PRMDATA *PnPrm);
PUBLIC void PcalBaseCurCtrl( BASE_LOOP *BaseLoops ,UINT16 *Curgn, UINT16 *Ti);
PUBLIC void PcalBaseWfCtrl( BASE_LOOP *BaseLoops ,PRMDATA *PnPrm);
PUBLIC void PcalBaseSpdCtrl( BASE_LOOP *BaseLoops, PRMDATA *PnPrm, UINT32 GselNo );
PUBLIC void PcalSpeedLimitGain( BASE_CTRL *BaseCtrls, BPRMDAT *Bprm, PRMDATA *PnPrm, INT32 GselNo );
PUBLIC void PcalBasePosPCtrll( BASE_LOOP  *BaseLoops, UINT32 Posgn, UINT32 GselNo );
PUBLIC REAL32 PcalSpdFFGain( REAL32 Kpx, INT32 ffgn, INT32 ScanTime );
PUBLIC REAL32 PcalTrqFFGain(REAL32 Kvx,PRMDATA *PnPrm, INT32 ScanTime );
PUBLIC void PcalBaseTrqLpassFilter( TRQFIL *TrqFilData, BASE_CTRL *BaseCtrls, 
													  INT32 trqfil11, INT32 trqfil12, INT32 GselNo );
PUBLIC void PcalBaseTrqLpassFilter2( TRQFIL *TrqFilData, UINT16 secfil, UINT16 secq );
PUBLIC REAL32 PcalBaseTrqLpassFilter3( INT32 Trqfil, INT32 ScanTime );

PUBLIC REAL32 f_BpxNorObsGainCal( REAL32 ObsGain , REAL32 scantime);
PUBLIC REAL32 f_BpxNorObsJGainCal( REAL32 Kvx, REAL32 jrate, REAL32 ObsJGain , REAL32 scantime );
PUBLIC INT32 BpxNorObsJGainCal( INT32 Kvx, INT32 jrate, INT32 ObsJGain , INT32 scantime );
PUBLIC void PcalBasePosRefLpassFilter( BASE_CTRL *BaseCtrls, INT32 PosRefFil);

#endif

