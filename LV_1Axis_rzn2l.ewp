<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>4</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>37</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>Debug\BrowseInfo</state>
                </option>
                <option>
                    <name>OGAarch64Abi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OG_32_64Device</name>
                    <state>0</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>Debug\</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Debug\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Debug\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Debug\List</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>A compact configuration of the C/C++14 runtime library. No locale interface, C locale, no file descriptor support, no multibytes in printf and scanf, and no hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>9.20.4.47112</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>9.60.4.11196</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>R9A07G084M04	Renesas R9A07G084M04</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Normal.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>34</version>
                    <state>49</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>34</version>
                    <state>49</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>R9A07G084M04	Renesas R9A07G084M04</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>8</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>34</version>
                    <state>49</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>PointerAuthentication</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FPU64</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OG_32_64DeviceCoreSlave</name>
                    <version>34</version>
                    <state>49</state>
                </option>
                <option>
                    <name>GOutputSo</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>39</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CCDefines</name>
                    <state>ETHERCAT_SSC_PORT_GMAC_MDIO_SUPPORT=1</state>
                    <state>_RENESAS_RZN_</state>
                    <state>_RZN_CORE=CR52_0</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>11111110</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc/api</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc/instances</state>
                    <state>$PROJ_DIR$/rzn_cfg/fsp_cfg</state>
                    <state>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</state>
                    <state>$PROJ_DIR$/rzn_gen</state>
                    <state>$PROJ_DIR$/src</state>
                    <state>$PROJ_DIR$\source\bsp</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</state>
                    <state>$PROJ_DIR$\source\apl</state>
                    <state>$PROJ_DIR$\source\Canopen</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</state>
                    <state>$PROJ_DIR$/rzn_cfg/driver</state>
                    <state>$PROJ_DIR$\source\IAP</state>
                    <state>$PROJ_DIR$\source\bsp\flash</state>
                    <state>$PROJ_DIR$\source\ecat</state>
                    <state>$PROJ_DIR$\source\modbus</state>
                    <state>$PROJ_DIR$\source\apl\ecat_eeprom</state>
                    <state>$PROJ_DIR$\..\lv-1axis-rzn2l-dev\source\Canopen</state>
                    <state>$PROJ_DIR$\source\UserLib</state>
                    <state>$PROJ_DIR$\source\robotcanfd</state>
                    <state>$PROJ_DIR$\source\EncDriver\Ktm59</state>
                    <state>$PROJ_DIR$\source\EncDriver\Bissc</state>
                    <state>$PROJ_DIR$\source\EncDriver\Ma600</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>3</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>3</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>2</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPointerAutentiction</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCBranchTargetIdentification</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosRadRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosSharedSlave</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>12</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state>_RENESAS_RZN_</state>
                    <state>_RZN_CORE=CR52_0</state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc/api</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc/instances</state>
                    <state>$PROJ_DIR$/rzn_cfg/fsp_cfg</state>
                    <state>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</state>
                    <state>$PROJ_DIR$/rzn_gen</state>
                    <state>$PROJ_DIR$/src</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</state>
                    <state>$PROJ_DIR$/rzn_cfg/driver</state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>A_32_64Device</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>3</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>$PROJ_DIR$/RF200.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>28</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>HV_1Axis_rzn2l.out</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$/script/fsp_xspi1_boot_SRAM.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state>--config_search "$PROJ_DIR$"</state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>system_init</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>HV_1Axis_rzn2l_import_lib.o</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkSharedSlave</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <buildActions>
                    <buildAction>
                        <cmdline>cmd /c "$PROJ_DIR$\script\user_script\crc_add.bat"</cmdline>
                        <workingDirectory>$PROJ_DIR$</workingDirectory>
                        <buildSequence>postLink</buildSequence>
                    </buildAction>
                </buildActions>
            </data>
        </settings>
        <settings>
            <name>Coder</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>37</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>BrowseInfoPath</name>
                    <state>Release\BrowseInfo</state>
                </option>
                <option>
                    <name>OGAarch64Abi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OG_32_64Device</name>
                    <state>0</state>
                </option>
                <option>
                    <name>BuildFilesPath</name>
                    <state>Release\</state>
                </option>
                <option>
                    <name>ExePath</name>
                    <state>Release\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Release\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Release\List</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state></state>
                </option>
                <option>
                    <name>Output description</name>
                    <state></state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state></state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>9.20.4.47112</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>9.40.1.63870</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>R9A07G084M04	Renesas R9A07G084M04</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state></state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>34</version>
                    <state>38</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>34</version>
                    <state>38</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>-</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>34</version>
                    <state>38</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>PointerAuthentication</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FPU64</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OG_32_64DeviceCoreSlave</name>
                    <version>34</version>
                    <state>38</state>
                </option>
                <option>
                    <name>GOutputSo</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>39</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CCDefines</name>
                    <state>NDEBUG</state>
                    <state>_RENESAS_RZN_</state>
                    <state>_RZN_CORE=CR52_0</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>11111110</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc/api</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc/instances</state>
                    <state>$PROJ_DIR$/rzn_cfg/fsp_cfg</state>
                    <state>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</state>
                    <state>$PROJ_DIR$/rzn_gen</state>
                    <state>$PROJ_DIR$/src</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</state>
                    <state>$PROJ_DIR$/rzn_cfg/driver</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>3</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPointerAutentiction</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCBranchTargetIdentification</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosRadRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosSharedSlave</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>12</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state>_RENESAS_RZN_</state>
                    <state>_RZN_CORE=CR52_0</state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc/api</state>
                    <state>$PROJ_DIR$/rzn/fsp/inc/instances</state>
                    <state>$PROJ_DIR$/rzn_cfg/fsp_cfg</state>
                    <state>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</state>
                    <state>$PROJ_DIR$/rzn_gen</state>
                    <state>$PROJ_DIR$/src</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</state>
                    <state>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</state>
                    <state>$PROJ_DIR$/rzn_cfg/driver</state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>A_32_64Device</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state></state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>4</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <buildSequence>inputOutputBased</buildSequence>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>28</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLogCrtRoutineSelection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogFragmentInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInlining</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogMerging</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkDemangle</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFileEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkWrapperFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>###Unitialized###</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$/script/fsp_xspi1_boot_SRAM.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state>--config_search "$PROJ_DIR$"</state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>system_init</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>###Unitialized###</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkSharedSlave</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <buildActions>
                    <buildAction>
                        <cmdline>cmd /c ""$RASC_EXE_PATH$" -nosplash --launcher.suppressErrors --gensmartbundle --compiler IAR --devicefamily rzn "$PROJ_DIR$\configuration.xml" "$TARGET_PATH$" 2&gt; "%TEMP%\rasc_stderr.out"" &amp;&amp; echo &gt; "$BUILD_FILES_DIR$/.postbuild"</cmdline>
                        <workingDirectory>$PROJ_DIR$</workingDirectory>
                        <buildSequence>postLink</buildSequence>
                        <outputs>
                            <file>
                                <name>$BUILD_FILES_DIR$/.postbuild</name>
                            </file>
                        </outputs>
                    </buildAction>
                </buildActions>
            </data>
        </settings>
        <settings>
            <name>Coder</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <group>
        <name>Flex Software</name>
        <group>
            <name>Build Configuration</name>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\bsp\board_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\bsp\bsp_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\bsp\bsp_mcu_device_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\bsp\bsp_mcu_device_pn_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\bsp\bsp_mcu_family_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\bsp\bsp_pin_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\r_cmt_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\r_dmac_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\r_dsmif_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\r_ether_phy_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\r_ether_selector_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\r_ioport_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\driver\r_xspi_qspi_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_cfg\fsp_cfg\rm_ethercat_ssc_port_cfg.h</name>
            </file>
        </group>
        <group>
            <name>Components</name>
            <file>
                <name>$PROJ_DIR$\rzn\board\custom\board.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\bsp_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\rzn2l\bsp_cache.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\rzn2l\bsp_cache.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_clocks.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_clocks.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_common.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_common.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_compiler_support.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_delay.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_delay.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\rzn2l\bsp_elc.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\rzn2l\bsp_feature.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_io.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_io.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_irq.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_irq.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\rzn2l\bsp_loader_param.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_mcu_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\rzn2l\bsp_mcu_info.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_module_stop.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_register_protection.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_register_protection.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_reset.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_reset.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_sbrk.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\mcu\all\bsp_tfu.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\arm\CMSIS_5\CMSIS\Core_R\Include\cmsis_compiler.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\arm\CMSIS_5\CMSIS\Core_R\Include\cmsis_cp15.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\arm\CMSIS_5\CMSIS\Core_R\Include\cmsis_gcc.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\arm\CMSIS_5\CMSIS\Core_R\Include\cmsis_iccarm.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\arm\CMSIS_5\CMSIS\Core_R\Include\cmsis_version.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\arm\CMSIS_5\CMSIS\Core_R\Include\core_cr52.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\fsp_common_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\fsp_features.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\fsp_version.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\arm\CMSIS_5\LICENSE.txt</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\cmsis\Device\RENESAS\Include\R9A07G084.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\r_adc_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_canfd\r_canfd.c</name>
                <configuration>
                    <name>Debug</name>
                    <settings>
                        <name>ICCARM</name>
                        <data>
                            <version>39</version>
                            <wantNonLocal>0</wantNonLocal>
                            <debug>1</debug>
                            <option>
                                <name>CCDefines</name>
                                <state>ETHERCAT_SSC_PORT_GMAC_MDIO_SUPPORT=1</state>
                                <state>_RENESAS_RZN_</state>
                                <state>_RZN_CORE=CR52_0</state>
                            </option>
                            <option>
                                <name>CCPreprocFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPreprocComments</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPreprocLine</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCListCFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListCMnemonics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListCMessages</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListAssFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListAssSource</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEnableRemarks</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCDiagSuppress</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagRemark</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagWarning</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagError</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCObjPrefix</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCAllowList</name>
                                <version>1</version>
                                <state>00000000</state>
                            </option>
                            <option>
                                <name>CCDebugInfo</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IEndianMode</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IProcessor</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IExtraOptionsCheck</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IExtraOptions</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCLangConformance</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCSignedPlainChar</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCRequirePrototypes</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCDiagWarnAreErr</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCCompilerRuntimeInfo</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IFpuProcessor</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>OutputFile</name>
                                <state>$FILE_BNAME$.o</state>
                            </option>
                            <option>
                                <name>CCLibConfigHeader</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>PreInclude</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCIncludePath2</name>
                                <state>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc/api</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc/instances</state>
                                <state>$PROJ_DIR$/rzn_cfg/fsp_cfg</state>
                                <state>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</state>
                                <state>$PROJ_DIR$/rzn_gen</state>
                                <state>$PROJ_DIR$/src</state>
                                <state>$PROJ_DIR$\source\bsp</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</state>
                                <state>$PROJ_DIR$\source\apl</state>
                                <state>$PROJ_DIR$\source\Canopen</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</state>
                                <state>$PROJ_DIR$/rzn_cfg/driver</state>
                                <state>$PROJ_DIR$\source\IAP</state>
                                <state>$PROJ_DIR$\source\bsp\flash</state>
                                <state>$PROJ_DIR$\source\ecat</state>
                                <state>$PROJ_DIR$\source\modbus</state>
                                <state>$PROJ_DIR$\source\apl\ecat_eeprom</state>
                                <state>$PROJ_DIR$\..\lv-1axis-rzn2l-dev\source\Canopen</state>
                                <state>$PROJ_DIR$\source\UserLib</state>
                                <state>$PROJ_DIR$\source\robotcanfd</state>
                                <state>$PROJ_DIR$\source\driver\Ktm59</state>
                            </option>
                            <option>
                                <name>CCStdIncCheck</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCCodeSection</name>
                                <state>.text</state>
                            </option>
                            <option>
                                <name>IProcessorMode2</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCOptLevel</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptStrategy</name>
                                <version>0</version>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptLevelSlave</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndRopi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndRwpi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndNoDynInit</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccLang</name>
                                <state>2</state>
                            </option>
                            <option>
                                <name>IccCDialect</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccAllowVLA</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccStaticDestr</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccCppInlineSemantics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccCmsis</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccFloatSemantics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptimizationNoSizeConstraints</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCNoLiteralPool</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptStrategySlave</name>
                                <version>0</version>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncSource</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncOutput</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncOutputBom</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCEncInput</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccExceptions2</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccRTTI2</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCStackProtection</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPointerAutentiction</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCBranchTargetIdentification</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosRadRwpi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosSharedSlave</name>
                                <state>0</state>
                            </option>
                        </data>
                    </settings>
                </configuration>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_cmt\r_cmt.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\instances\r_cmt.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_dmac\r_dmac.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\instances\r_dmac.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_dsmif\r_dsmif.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\instances\r_dsmif.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\r_elc_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_ether_phy\r_ether_phy.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\instances\r_ether_phy.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\r_ether_phy_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_ether_selector\r_ether_selector.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\instances\r_ether_selector.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\r_ether_selector_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_ioport\r_ioport.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\instances\r_ioport.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\r_ioport_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_spi\r_spi.c</name>
                <configuration>
                    <name>Debug</name>
                    <settings>
                        <name>ICCARM</name>
                        <data>
                            <version>39</version>
                            <wantNonLocal>0</wantNonLocal>
                            <debug>1</debug>
                            <option>
                                <name>CCDefines</name>
                                <state>ETHERCAT_SSC_PORT_GMAC_MDIO_SUPPORT=1</state>
                                <state>_RENESAS_RZN_</state>
                                <state>_RZN_CORE=CR52_0</state>
                            </option>
                            <option>
                                <name>CCPreprocFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPreprocComments</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPreprocLine</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCListCFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListCMnemonics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListCMessages</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListAssFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListAssSource</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEnableRemarks</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCDiagSuppress</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagRemark</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagWarning</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagError</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCObjPrefix</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCAllowList</name>
                                <version>1</version>
                                <state>00000000</state>
                            </option>
                            <option>
                                <name>CCDebugInfo</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IEndianMode</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IProcessor</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IExtraOptionsCheck</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IExtraOptions</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCLangConformance</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCSignedPlainChar</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCRequirePrototypes</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCDiagWarnAreErr</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCCompilerRuntimeInfo</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IFpuProcessor</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>OutputFile</name>
                                <state>$FILE_BNAME$.o</state>
                            </option>
                            <option>
                                <name>CCLibConfigHeader</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>PreInclude</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCIncludePath2</name>
                                <state>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc/api</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc/instances</state>
                                <state>$PROJ_DIR$/rzn_cfg/fsp_cfg</state>
                                <state>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</state>
                                <state>$PROJ_DIR$/rzn_gen</state>
                                <state>$PROJ_DIR$/src</state>
                                <state>$PROJ_DIR$\source\bsp</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</state>
                                <state>$PROJ_DIR$\source\apl</state>
                                <state>$PROJ_DIR$\source\Canopen</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</state>
                                <state>$PROJ_DIR$/rzn_cfg/driver</state>
                                <state>$PROJ_DIR$\source\IAP</state>
                                <state>$PROJ_DIR$\source\bsp\flash</state>
                                <state>$PROJ_DIR$\source\ecat</state>
                                <state>$PROJ_DIR$\source\modbus</state>
                                <state>$PROJ_DIR$\source\apl\ecat_eeprom</state>
                                <state>$PROJ_DIR$\..\lv-1axis-rzn2l-dev\source\Canopen</state>
                                <state>$PROJ_DIR$\source\UserLib</state>
                                <state>$PROJ_DIR$\source\robotcanfd</state>
                                <state>$PROJ_DIR$\source\EncDriver\Ktm59</state>
                                <state>$PROJ_DIR$\source\EncDriver\Bissc</state>
                                <state>$PROJ_DIR$\source\EncDriver\Ma600</state>
                            </option>
                            <option>
                                <name>CCStdIncCheck</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCCodeSection</name>
                                <state>.text</state>
                            </option>
                            <option>
                                <name>IProcessorMode2</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCOptLevel</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptStrategy</name>
                                <version>0</version>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptLevelSlave</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndRopi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndRwpi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndNoDynInit</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccLang</name>
                                <state>2</state>
                            </option>
                            <option>
                                <name>IccCDialect</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccAllowVLA</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccStaticDestr</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccCppInlineSemantics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccCmsis</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccFloatSemantics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptimizationNoSizeConstraints</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCNoLiteralPool</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptStrategySlave</name>
                                <version>0</version>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncSource</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncOutput</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncOutputBom</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCEncInput</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccExceptions2</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccRTTI2</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCStackProtection</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPointerAutentiction</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCBranchTargetIdentification</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosRadRwpi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosSharedSlave</name>
                                <state>0</state>
                            </option>
                        </data>
                    </settings>
                </configuration>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\r_spi_flash_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\r_timer_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\r_transfer_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\r_xspi_qspi\r_xspi_qspi.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\instances\r_xspi_qspi.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\cmsis\Device\RENESAS\SVD\RA.svd</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\cmsis\Device\RENESAS\Include\renesas.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\rm_ethercat_ssc_port\renesashw.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\rm_ethercat_ssc_port\rm_ethercat_ssc_port.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\instances\rm_ethercat_ssc_port.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\inc\api\rm_ethercat_ssc_port_api.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\cmsis\Device\RENESAS\Source\startup.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\cmsis\Device\RENESAS\Source\system.c</name>
                <configuration>
                    <name>Debug</name>
                    <settings>
                        <name>ICCARM</name>
                        <data>
                            <version>39</version>
                            <wantNonLocal>0</wantNonLocal>
                            <debug>1</debug>
                            <option>
                                <name>CCDefines</name>
                                <state>ETHERCAT_SSC_PORT_GMAC_MDIO_SUPPORT=1</state>
                                <state>_RENESAS_RZN_</state>
                                <state>_RZN_CORE=CR52_0</state>
                            </option>
                            <option>
                                <name>CCPreprocFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPreprocComments</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPreprocLine</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCListCFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListCMnemonics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListCMessages</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListAssFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListAssSource</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEnableRemarks</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCDiagSuppress</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagRemark</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagWarning</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagError</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCObjPrefix</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCAllowList</name>
                                <version>1</version>
                                <state>00000000</state>
                            </option>
                            <option>
                                <name>CCDebugInfo</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IEndianMode</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IProcessor</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IExtraOptionsCheck</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IExtraOptions</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCLangConformance</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCSignedPlainChar</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCRequirePrototypes</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCDiagWarnAreErr</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCCompilerRuntimeInfo</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IFpuProcessor</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>OutputFile</name>
                                <state>$FILE_BNAME$.o</state>
                            </option>
                            <option>
                                <name>CCLibConfigHeader</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>PreInclude</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCIncludePath2</name>
                                <state>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc/api</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc/instances</state>
                                <state>$PROJ_DIR$/rzn_cfg/fsp_cfg</state>
                                <state>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</state>
                                <state>$PROJ_DIR$/rzn_gen</state>
                                <state>$PROJ_DIR$/src</state>
                                <state>$PROJ_DIR$\source\bsp</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</state>
                                <state>$PROJ_DIR$\source\apl</state>
                                <state>$PROJ_DIR$\source\Canopen</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</state>
                                <state>$PROJ_DIR$/rzn_cfg/driver</state>
                                <state>$PROJ_DIR$\source\IAP</state>
                                <state>$PROJ_DIR$\source\bsp\flash</state>
                                <state>$PROJ_DIR$\source\ecat</state>
                                <state>$PROJ_DIR$\source\modbus</state>
                                <state>$PROJ_DIR$\source\apl\ecat_eeprom</state>
                            </option>
                            <option>
                                <name>CCStdIncCheck</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCCodeSection</name>
                                <state>.text</state>
                            </option>
                            <option>
                                <name>IProcessorMode2</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCOptLevel</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCOptStrategy</name>
                                <version>0</version>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptLevelSlave</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCPosIndRopi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndRwpi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndNoDynInit</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccLang</name>
                                <state>2</state>
                            </option>
                            <option>
                                <name>IccCDialect</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccAllowVLA</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccStaticDestr</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccCppInlineSemantics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccCmsis</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccFloatSemantics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptimizationNoSizeConstraints</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCNoLiteralPool</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptStrategySlave</name>
                                <version>0</version>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncSource</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncOutput</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncOutputBom</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCEncInput</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccExceptions2</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccRTTI2</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCStackProtection</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPointerAutentiction</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCBranchTargetIdentification</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosRadRwpi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosSharedSlave</name>
                                <state>0</state>
                            </option>
                        </data>
                    </settings>
                </configuration>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn\fsp\src\bsp\cmsis\Device\RENESAS\Include\system.h</name>
            </file>
        </group>
        <group>
            <name>Generated Data</name>
            <file>
                <name>$PROJ_DIR$\rzn_gen\bsp_clock_cfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_gen\common_data.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_gen\common_data.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_gen\hal_data.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_gen\hal_data.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_gen\main.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_gen\pin_data.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_gen\vector_data.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\rzn_gen\vector_data.h</name>
            </file>
        </group>
        <group>
            <name>Program Entry</name>
            <file>
                <name>$PROJ_DIR$\src\hal_entry.c</name>
            </file>
        </group>
    </group>
    <group>
        <name>Source</name>
        <group>
            <name>bsp</name>
            <group>
                <name>flash</name>
                <file>
                    <name>$PROJ_DIR$\source\bsp\flash\cmd_flash.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\source\bsp\flash\cmd_otp.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\source\bsp\flash\flash.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\source\bsp\flash\otp.c</name>
                </file>
            </group>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_adc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_can.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_dsmif.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_eeprom.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_enc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_pwm.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_spi.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_timer.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\bsp\bsp_uart.c</name>
            </file>
        </group>
        <group>
            <name>canopen</name>
            <excluded>
                <configuration>Debug</configuration>
            </excluded>
            <file>
                <name>$PROJ_DIR$\source\Canopen\applicfg.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\can.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\can_driver.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\canfestival.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\canobject.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\canobject.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\config.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\data.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\dcf.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\dcf.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\def.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\emcy.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\emcy.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\lifegrd.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\lifegrd.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\lss.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\lss.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\nmtMaster.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\nmtMaster.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\nmtSlave.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\nmtSlave.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\objacces.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\objacces.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\ObjDict.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\objdictdef.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\pdo.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\pdo.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\sdo.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\sdo.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\states.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\states.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\stm32_canfestival.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\sync.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\sync.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\sysdep.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\TestMaster.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\TestMaster.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\timer.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\timer.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\timers_driver.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\Canopen\timerscfg.h</name>
            </file>
        </group>
        <group>
            <name>ecat</name>
            <excluded>
                <configuration>Debug</configuration>
            </excluded>
            <file>
                <name>$PROJ_DIR$\source\ecat\app.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\coeappl.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\ecatappl.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\ecatcoe.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\ecatfoe.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\ecatslv.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\foeappl.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\mailbox.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\objdef.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\ecat\sdoserv.c</name>
            </file>
        </group>
        <group>
            <name>EncDriver</name>
            <group>
                <name>Bissc</name>
                <file>
                    <name>$PROJ_DIR$\source\EncDriver\Bissc\BisscEncIF.c</name>
                </file>
            </group>
            <group>
                <name>ktm59</name>
                <file>
                    <name>$PROJ_DIR$\source\EncDriver\Ktm59\common_ktm.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\source\EncDriver\Ktm59\ktm59xxIF.c</name>
                </file>
            </group>
            <group>
                <name>Ma600</name>
                <file>
                    <name>$PROJ_DIR$\source\EncDriver\Ma600\Ma600IF.c</name>
                </file>
            </group>
        </group>
        <group>
            <name>iap</name>
            <file>
                <name>$PROJ_DIR$\source\IAP\common.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\IAP\flash_if.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\IAP\menu.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\IAP\ymodem.c</name>
            </file>
        </group>
        <group>
            <name>modbus</name>
            <excluded>
                <configuration>Debug</configuration>
            </excluded>
            <file>
                <name>$PROJ_DIR$\source\modbus\modbus.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\modbus\modbus.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\modbus\modbusdrv.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\modbus\modbusdrv.h</name>
            </file>
        </group>
        <group>
            <name>motctrl</name>
            <group>
                <name>RobCan</name>
                <file>
                    <name>$PROJ_DIR$\source\robotcanfd\canfd_common.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\source\robotcanfd\canfd_protocol.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\source\robotcanfd\canfd_slave.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\source\robotcanfd\canfd_slave.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\source\robotcanfd\canfd_slave_main.c</name>
                </file>
                <configuration>
                    <name>Debug</name>
                    <settings>
                        <name>ICCARM</name>
                        <data>
                            <version>39</version>
                            <wantNonLocal>0</wantNonLocal>
                            <debug>1</debug>
                            <option>
                                <name>CCDefines</name>
                                <state>ETHERCAT_SSC_PORT_GMAC_MDIO_SUPPORT=1</state>
                                <state>_RENESAS_RZN_</state>
                                <state>_RZN_CORE=CR52_0</state>
                            </option>
                            <option>
                                <name>CCPreprocFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPreprocComments</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPreprocLine</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCListCFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListCMnemonics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListCMessages</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListAssFile</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCListAssSource</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEnableRemarks</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCDiagSuppress</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagRemark</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagWarning</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCDiagError</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCObjPrefix</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCAllowList</name>
                                <version>1</version>
                                <state>00000000</state>
                            </option>
                            <option>
                                <name>CCDebugInfo</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IEndianMode</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IProcessor</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IExtraOptionsCheck</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IExtraOptions</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCLangConformance</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCSignedPlainChar</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCRequirePrototypes</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCDiagWarnAreErr</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCCompilerRuntimeInfo</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IFpuProcessor</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>OutputFile</name>
                                <state>$FILE_BNAME$.o</state>
                            </option>
                            <option>
                                <name>CCLibConfigHeader</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>PreInclude</name>
                                <state></state>
                            </option>
                            <option>
                                <name>CCIncludePath2</name>
                                <state>$PROJ_DIR$/rzn/arm/CMSIS_5/CMSIS/Core_R/Include</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc/api</state>
                                <state>$PROJ_DIR$/rzn/fsp/inc/instances</state>
                                <state>$PROJ_DIR$/rzn_cfg/fsp_cfg</state>
                                <state>$PROJ_DIR$/rzn_cfg/fsp_cfg/bsp</state>
                                <state>$PROJ_DIR$/rzn_gen</state>
                                <state>$PROJ_DIR$/src</state>
                                <state>$PROJ_DIR$\source\bsp</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/driver/inc</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/r_usb_basic/src/hw/inc</state>
                                <state>$PROJ_DIR$\source\apl</state>
                                <state>$PROJ_DIR$\source\Canopen</state>
                                <state>$PROJ_DIR$/rzn/fsp/src/rm_ethercat_ssc_port</state>
                                <state>$PROJ_DIR$/rzn_cfg/driver</state>
                                <state>$PROJ_DIR$\source\IAP</state>
                                <state>$PROJ_DIR$\source\bsp\flash</state>
                                <state>$PROJ_DIR$\source\ecat</state>
                                <state>$PROJ_DIR$\source\modbus</state>
                                <state>$PROJ_DIR$\source\apl\ecat_eeprom</state>
                                <state>$PROJ_DIR$\..\lv-1axis-rzn2l-dev\source\Canopen</state>
                                <state>$PROJ_DIR$\source\UserLib</state>
                                <state>$PROJ_DIR$\source\robotcanfd</state>
                                <state>$PROJ_DIR$\source\driver\Ktm59</state>
                            </option>
                            <option>
                                <name>CCStdIncCheck</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCCodeSection</name>
                                <state>.text</state>
                            </option>
                            <option>
                                <name>IProcessorMode2</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCOptLevel</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptStrategy</name>
                                <version>0</version>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptLevelSlave</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndRopi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndRwpi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosIndNoDynInit</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccLang</name>
                                <state>2</state>
                            </option>
                            <option>
                                <name>IccCDialect</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccAllowVLA</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccStaticDestr</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccCppInlineSemantics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccCmsis</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>IccFloatSemantics</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptimizationNoSizeConstraints</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCNoLiteralPool</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCOptStrategySlave</name>
                                <version>0</version>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncSource</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncOutput</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCEncOutputBom</name>
                                <state>1</state>
                            </option>
                            <option>
                                <name>CCEncInput</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccExceptions2</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>IccRTTI2</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCStackProtection</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPointerAutentiction</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCBranchTargetIdentification</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosRadRwpi</name>
                                <state>0</state>
                            </option>
                            <option>
                                <name>CCPosSharedSlave</name>
                                <state>0</state>
                            </option>
                        </data>
                    </settings>
                </configuration>
            </group>
            <file>
                <name>$PROJ_DIR$\source\apl\Alarm.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\AlarmTable.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\AutoGainChange.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\BaseControls.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\BaseLoops.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\BasePrmCal.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\BaseSequence.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\CheckAlarm.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\CheckMotSts.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\Cia402Appl.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\ComRegMngr.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\ComUart.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\CurrentLoop.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\DataCache.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\DataCacheManager.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\DataTraceCal.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\DataTraceDef.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\DataTraceManager.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\EepromIF.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\Encoder.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\FieldWeakening.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\FnJog.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\FnPrgJog.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\FunGenerator.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\FunListDef.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\FunManager.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\GainChange.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\Global.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\HardApi.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\Home.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\InertiaIdent.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\IniPrmCal.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\IntTaskA.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\IntTaskB.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\IntTaskC.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\JOG.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\LostPhaseCheck.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\MLib.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\MotorIdentification.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\OpeRegCalc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\OpeRegTable.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\PcmdFilter.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\PnPrmCal.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\PosManager.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\PowerManager.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\PrmTable.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\ProgramJog.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\RegAccessIF.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\Round.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\SinTable.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\TamagawaIF.c</name>
                <excluded>
                    <configuration>Debug</configuration>
                </excluded>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\TaskCMain.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\apl\VersionDef.h</name>
            </file>
        </group>
        <group>
            <name>UserLib</name>
            <file>
                <name>$PROJ_DIR$\source\UserLib\cal_crc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\source\UserLib\dm.c</name>
            </file>
        </group>
    </group>
    <file>
        <name>$PROJ_DIR$\buildinfo.ipcf</name>
        <tag>IAR.ControlFile</tag>
    </file>
    <file>
        <name>$PROJ_DIR$\script\fsp_xspi1_boot.icf</name>
    </file>
    <file>
        <name>$PROJ_DIR$\memory_regions.icf</name>
    </file>
</project>
